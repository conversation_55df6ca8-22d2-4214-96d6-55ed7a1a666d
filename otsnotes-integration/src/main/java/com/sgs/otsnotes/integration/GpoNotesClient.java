package com.sgs.otsnotes.integration;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.beust.jcommander.internal.Lists;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.model.common.productsample.ProductSampleBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementBO;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.order.v2.ParcelBO;
import com.sgs.otsnotes.core.config.InterfaceConfig;
import com.sgs.otsnotes.core.config.StarLimsConfig;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.util.HttpClientUtil;
import com.sgs.otsnotes.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO;
import com.sgs.otsnotes.facade.model.common.BaseResponse;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.enums.GPOUpdateTLStatusEnums;
import com.sgs.otsnotes.facade.model.enums.LanguageType;
import com.sgs.otsnotes.facade.model.enums.StarLimsReportType;
import com.sgs.otsnotes.facade.model.req.gpo.*;
import com.sgs.otsnotes.facade.model.req.sample.ProductSampleReq;
import com.sgs.otsnotes.facade.model.req.starlims.ReceiveStarLimsReportDocReq;
import com.sgs.otsnotes.facade.model.req.starlims.receive.ReceiveStarLimsReportDocBodyReq;
import com.sgs.otsnotes.facade.model.req.subcontract.SubContractMatrixTestLineReq;
import com.sgs.otsnotes.facade.model.req.subcontract.SubcontractReportMatrixQueryReq;
import com.sgs.otsnotes.facade.model.req.tag.OrderTagQueryReq;
import com.sgs.otsnotes.facade.model.rsp.starlims.QuotationTestLineInfoRsp;
import com.sgs.otsnotes.facade.model.rsp.starlims.ReceiveStarLimsReportDocRsp;
import com.sgs.otsnotes.facade.model.rsp.starlims.TagToStarlimsRsp;
import com.sgs.otsnotes.facade.model.rsp.subcontract.SubContractMatrixTestLineRsp;
import com.sgs.otsnotes.facade.model.rsp.subcontract.SubcontractReportMatrixQueryRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.LabSectionListReq;
import com.sgs.otsnotes.facade.model.rsp.testLine.TestLineEditDetailRsp;
import com.sgs.otsnotes.integration.dto.DefaultAccreditationReq;
import com.sgs.otsnotes.integration.dto.DefaultAccreditationRsp;
import com.sgs.otsnotes.integration.dto.SubReportCancelReq;
import com.sgs.otsnotes.integration.dto.SubcontractCompleteReq;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class GpoNotesClient {
    private static final Logger logger = LoggerFactory.getLogger(GpoNotesClient.class);
    @Autowired
    private InterfaceConfig interfaceConfig;
    @Autowired
    private StarLimsConfig starLimsConfig;

    /**
     *
     * @param reqObject
     * @return
     */
    public BaseResponse returnSubContractReport(ReturnSubContractReportReq reqObject) {
        try {
            String gpoNotesApiUrl = String.format("%s/report/returnSubContractReport", interfaceConfig.getGpnNotesApi());

            logger.info("Start_returnSubContractReport_SubContractNo({}，gpoNotesApiUrl：{}.参数：{}", reqObject.getSubContractNo(), gpoNotesApiUrl, JSON.toJSONString(reqObject));
            BaseResponse rspObject = HttpClientUtil.post(gpoNotesApiUrl, reqObject, BaseResponse.class);
            logger.info("End_returnSubContractReport_SubContractNo({}，rspObject：{}.", reqObject.getSubContractNo(), JSON.toJSONString(rspObject));

            return rspObject;
        } catch (Exception ex) {
            logger.error("returnSubContractReport 信息异常：{}.", ex);
        }
        return null;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public BaseResponse generateMatrixNo(GenerateMatrixNoReq reqObject) {
        try {
            String gpoNotesApiUrl = String.format("%s/api/testMatrix/generateMatrixNo", interfaceConfig.getGpnNotesApi());

            logger.info("Start_generateMatrixNo({}，gpoNotesApiUrl：{}.", reqObject.getOrderNo(), gpoNotesApiUrl);
            BaseResponse rspObject = HttpClientUtil.post(gpoNotesApiUrl, reqObject, BaseResponse.class);
            logger.info("End_generateMatrixNo({}，rspObject：{}.", reqObject.getOrderNo(), JSON.toJSONString(rspObject));

            return rspObject;
        } catch (Exception ex) {
            logger.error("returnSubContractReport 信息异常：{}.", ex);
        }
        return null;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public BaseResponse reportTemplateCheck(ReportTemplateCheckReq reqObject) {
        try {
            String gpoNotesApiUrl = String.format("%s/api/report/reportTemplateCheck", interfaceConfig.getGpnNotesApi());

            logger.info("Start_reportTemplateCheck({}，gpoNotesApiUrl：{}.", reqObject.getOrderNo(), gpoNotesApiUrl);
            BaseResponse rspObject = HttpClientUtil.post(gpoNotesApiUrl, reqObject, BaseResponse.class);
            logger.info("End_reportTemplateCheck({}，rspObject：{}.", reqObject.getOrderNo(), JSON.toJSONString(rspObject));

            return rspObject;
        } catch (Exception ex) {
            logger.error("returnSubContractReport 信息异常：{}.", ex);
        }
        return null;
    }

    public List<TestLineEditDetailRsp> getLabSectionList(LabSectionListReq reqObject) {
        try {
            String gpoNotesApiUrl = String.format("%s/api/testLineQuery/getLabSectionList", interfaceConfig.getGpnNotesApi());

            logger.info("Start_getLabSectionList({}，gpoNotesApiUrl：{}.", reqObject.getOrderNo(), gpoNotesApiUrl);
            BaseResponse rspObject = HttpClientUtil.post(gpoNotesApiUrl, reqObject, BaseResponse.class);
            logger.info("End_getLabSectionList({}，rspObject：{}.", reqObject.getOrderNo(), JSON.toJSONString(rspObject));
            if (rspObject.getStatus() == 200) {
                List<TestLineEditDetailRsp> testLineEditDetailRsps = JSONObject.parseArray(rspObject.getData().toString(), TestLineEditDetailRsp.class);
                return testLineEditDetailRsps;
            }
            return Lists.newArrayList();
        } catch (Exception ex) {
            logger.error("returnSubContractReport 信息异常：{}.", ex);
        }
        return Lists.newArrayList();
    }

    /**
     *
     * @param orderNo
     * @param productLineCode
     * @return
     */
    public BaseResponse testLineGenerateSeq(String orderNo, String productLineCode) {
        try {
            String gpoNotesApiUrl = String.format("%s/api/testLine/generateSeq", interfaceConfig.getGpnNotesApi());

            GenerateTestLineSeqReq reqObject = new GenerateTestLineSeqReq();
            reqObject.setOrderNo(orderNo);
            reqObject.setProductLineCode(productLineCode);

            logger.info("Start_testLine_generateSeq({}，gpnNotesApiUrl：{}.参数：{}", reqObject.getOrderNo(), gpoNotesApiUrl, JSON.toJSONString(reqObject));
            BaseResponse rspObject = HttpClientUtil.post(gpoNotesApiUrl, reqObject, BaseResponse.class);
            logger.info("End_testLine_generateSeq({}，rspObject：{}.", reqObject.getOrderNo(), JSON.toJSONString(rspObject));

            return rspObject;
        } catch (Exception ex) {
            logger.error("returnSubContractReport 信息异常：{}.", ex);
        }
        return null;
    }

    /**
     *
     * @param reportDoc
     * @return
     */
    public CustomResult<List<ReceiveStarLimsReportDocRsp>> receiveStarLimsReportDoc(ReceiveStarLimsReportDocBodyReq reportDoc) {
        CustomResult rspReuslt = new CustomResult();
        try {
            ReceiveStarLimsReportDocReq reqObject = new ReceiveStarLimsReportDocReq();
            //
            reqObject.setAuthId(starLimsConfig.getStarLimsAuthId());
            reqObject.setCaller(reportDoc.getCaller());
            reqObject.setApproveDate(reportDoc.getApproveDate());
            reqObject.setConclusion(reportDoc.getConclusion());
            reqObject.setIsDraft(StarLimsReportType.getType(reportDoc.getReportType()));
            LanguageType languageType = LanguageType.findStarLimsCode(reportDoc.getLanguageId());
            if (languageType != null){
                reqObject.setLanguageId(String.valueOf(languageType.getLanguageId()));
            }else{
                // starLims 返回001 需要转换为3
                if(StringUtils.equals(reportDoc.getLanguageId(), Constants.STAR_LIMES_EN_AND_CN_LANGUAGEID)){
                    reqObject.setLanguageId(ReportLanguage.MultilingualReport.getCode());
                }
            }
            reqObject.setObjectNo(reportDoc.getObjectNo());
            String objectType = reportDoc.getObjectType();
            if (StringUtils.isNotBlank(objectType)){
                reqObject.setObjectType(objectType.toLowerCase());
            }
            reqObject.setOrderNo(reportDoc.getOrderNo());
            reqObject.setProductLineCode(reportDoc.getProductLineCode());
            reqObject.setReportNo(reportDoc.getExternalObjectNo());
            // GPO2-13625 starlims 增加参数 ExternalReportNo
            reqObject.setExternalReportNo(reportDoc.getReportNo());
            reqObject.setSubContractNo(reportDoc.getSubContractNo());
            reqObject.setRequestId(reportDoc.getRequestId());
            // 报告文件
            reqObject.setFiles(reportDoc.getFiles());

            String gpoNotesUrlApi = String.format("%s/api/subcontract/starlims/receiveReportDoc", interfaceConfig.getGpnNotesApi());
            logger.info("Start_receiveReportDoc(orderNo= {})", reqObject.getOrderNo());
            BaseResponse rspObject = HttpClientUtil.post(gpoNotesUrlApi, reqObject, BaseResponse.class);
            logger.info("End_receiveReportDoc({}，rspObject：{}.", reqObject.getOrderNo(), JSON.toJSONString(rspObject));
            if (rspObject.getStatus() != 200 || rspObject.getData() == null){
                rspReuslt.setMsg(rspObject.getMessage());
                return rspReuslt;
            }
            List<ReceiveStarLimsReportDocRsp> reports = JSONArray.parseArray(JSONObject.toJSONString(rspObject.getData()), ReceiveStarLimsReportDocRsp.class);
            rspReuslt.setData(reports);
            rspReuslt.setSuccess(reports != null && !reports.isEmpty());
            rspReuslt.setMsg(rspObject.getMessage());
        } catch (Exception ex) {
            logger.error("receiveStarLimsReportDoc 信息异常：{}.", ex);
            rspReuslt.setMsg(String.format("GPO ReceiveReportDoc 接口异常，Error：%s", ex.getMessage()));
        }/*finally {
            return rspReuslt;
        }*/

        // DIG-8555  Remove this return statement from this finally block.
        return rspReuslt;
    }

    public CustomResult cancelSubReport(SubReportCancelReq cancelReq) {
        CustomResult rspReuslt = new CustomResult();
        try {
            String gpoNotesUrlApi = String.format("%s/sub-report/cancel", interfaceConfig.getGpnNotesApi());
            logger.info("Start_cancelSubReport({}，reqObject：{}", cancelReq.getSubReportNo(), JSON.toJSONString(cancelReq));
            BaseResponse rspObject = HttpClientUtil.post(gpoNotesUrlApi, cancelReq, BaseResponse.class);
            logger.info("End_cancelSubReport({}，rspObject：{}.", cancelReq.getSubReportNo(), JSON.toJSONString(rspObject));
            if (rspObject.getStatus() != 200 ){
                rspReuslt.setMsg(rspObject.getMessage());
                return rspReuslt;
            }
            rspReuslt.setSuccess(true);
            rspReuslt.setMsg(rspObject.getMessage());
        } catch (Exception ex) {
            logger.error("gpn cancelSubReport 信息异常：{}.", ex);
            rspReuslt.setMsg(String.format("GPO cancelSubReport 接口异常，Error：%s", ex.getMessage()));
        }
        return rspReuslt;
    }

    public CustomResult completeSubcontract(SubcontractCompleteReq completeReq) {
        CustomResult rspReuslt = new CustomResult();
        try {
            String gpoNotesUrlApi = String.format("%s/subContract/complete", interfaceConfig.getGpnNotesApi());
            logger.info("Start_completeSubcontract({}，reqObject：{}", completeReq.getSubContractNo(), JSON.toJSONString(completeReq));
            BaseResponse rspObject = HttpClientUtil.post(gpoNotesUrlApi, completeReq, BaseResponse.class);
            logger.info("End_completeSubcontract({}，rspObject：{}.",completeReq.getSubContractNo(), JSON.toJSONString(rspObject));
            if (rspObject.getStatus() != 200){
                rspReuslt.setMsg(rspObject.getMessage());
                return rspReuslt;
            }
            rspReuslt.setSuccess(true);
            rspReuslt.setMsg(rspObject.getMessage());
        } catch (Exception ex) {
            logger.error("gpn completeSubcontract 信息异常：{}.", ex);
            rspReuslt.setMsg(String.format("GPO completeSubcontract 接口异常，Error：%s", ex.getMessage()));
        }
        return rspReuslt;
    }
    /**
     *
     * @param testLineList
     * @param orderNo
     * @param statusEnums
     * @return
     */
    public BaseResponse updateTestLineStatus(List<TestLineInstancePO> testLineList,String orderNo, GPOUpdateTLStatusEnums statusEnums) {
        try {
            String gpoNotesApiUrl = String.format("%s/api/testLine/updateStatus", interfaceConfig.getGpnNotesApi());

            List<String> tlIds = testLineList.stream().map(tl -> tl.getID()).distinct().collect(Collectors.toList());
            GPOUpdateTestLineStatusReq req = new GPOUpdateTestLineStatusReq();
            req.setTestLineInstanceIds(tlIds);
            req.setOrderNo(orderNo);
            req.setSynMatrixStatus(true);
            req.setOperationType(statusEnums.getStatus());
            req.setProductLineCode(ProductLineContextHolder.getProductLineCode());

            logger.info("Start_testLine_generateSeq({}，gpnNotesApiUrl：{}.参数：{}", req);
            BaseResponse rspObject = HttpClientUtil.post(gpoNotesApiUrl, req, BaseResponse.class);
            logger.info("End_testLine_generateSeq({}，rspObject：{}.",req,rspObject);

            return rspObject;
        } catch (Exception ex) {
            logger.error("returnSubContractReport 信息异常：{}.", ex);
        }
        return null;
    }

    /**
     * 注意 接口会返回null
     * @param subcontractId
     * @return
     */
    public SubcontractReportMatrixQueryRsp getReportMatrixBySubcontract(String subcontractId ,String labCode) {
        SubcontractReportMatrixQueryRsp result = null;
        try{
            SubcontractReportMatrixQueryReq subcontractReportMatrixQueryReq = new SubcontractReportMatrixQueryReq();
            subcontractReportMatrixQueryReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            subcontractReportMatrixQueryReq.setSubcontractId(subcontractId);
            subcontractReportMatrixQueryReq.setLabCode(labCode);

            logger.info("GPO client:::subcontractId :{} 请求reprotMatrix Req：{}",subcontractId,JSONObject.toJSONString(subcontractReportMatrixQueryReq));

            String gpoApi = String.format("%s/gpo-micro-api/subcontract/getReportMatrixBySubcontract",interfaceConfig.getAzureDomainUrl());
            BaseResponse<SubcontractReportMatrixQueryRsp> responseString = HttpClientUtil.sendPost(gpoApi, subcontractReportMatrixQueryReq, new TypeReference<BaseResponse<SubcontractReportMatrixQueryRsp>>(){});

            logger.info("GPO client:::subcontractId :{} 获取到ReportMatrix Resp：{}",subcontractId,responseString);

            if(responseString.getStatus() != ResponseCode.SUCCESS.getCode()){
                return null;
            }
            result = responseString.getData();

        }catch (Exception e){
            logger.error("Gpo client::: subcontractId:{} getReportMatrixBySubcontract err",subcontractId,e);
        }
        return result;

    }

    /**
     *
     * @param oldLabCode
     * @param labCode
     * @param token
     * @return
     */
    public DefaultAccreditationRsp getDefaultAccreditation(String oldLabCode, String labCode, String subContractNo, String token) {
        DefaultAccreditationRsp result = null;
        try{
            DefaultAccreditationReq defaultAccreditationReq =  new DefaultAccreditationReq();
            defaultAccreditationReq.setSubcontractNo(subContractNo);
            defaultAccreditationReq.setSourceLabCode(oldLabCode);
            defaultAccreditationReq.setTargetLabCode(labCode);
            defaultAccreditationReq.setSgsToken(token);

            logger.info("GPO client:::subContractNo :{} 请求getDefaultAccreditation Req：{}",subContractNo,JSONObject.toJSONString(defaultAccreditationReq));

            String gpoApi = String.format("%s/gpo-micro-api/report/default-accreditation",interfaceConfig.getAzureDomainUrl());
            BaseResponse responseString = HttpClientUtil.sendPost(gpoApi, defaultAccreditationReq, new TypeReference<BaseResponse>(){});

            logger.info("GPO client:::subContractNo :{} 请求getDefaultAccreditation Resp：{}",subContractNo,responseString);


            if(responseString.getStatus() != ResponseCode.SUCCESS.getCode()){
                return null;
            }
            List<DefaultAccreditationRsp> defaultAccreditationRsps = JSONObject.parseArray(JSONObject.toJSONString(responseString.getData()), DefaultAccreditationRsp.class);
            if (CollectionUtils.isEmpty(defaultAccreditationRsps)) {
                return null;
            }
            result = defaultAccreditationRsps.stream().findFirst().orElse(null);
        }catch (Exception e){
            logger.error("Gpo client::: subContractNo:{} 请求getDefaultAccreditation err",subContractNo,e);
        }
        return result;

    }

    /**
     * @param orderId
     * @return
     */
    public CustomResult listToStarlimsAttachments(String orderId) {
        CustomResult rspResult = new CustomResult();
        try {

            logger.info("Start_listToStarlimsAttachments_orderId {}.", orderId);
            // TODO 提交生成环境时，注意地址更换
            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/order/attachment/list-tostarlims-attachments", interfaceConfig.getAzureDomainUrl());
//            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/order/attachment/list-tostarlims-attachments", "http://localhost:8087");
            BaseResponse responseString = HttpClientUtil.sendPost(gpoNotesUrl, orderId, new TypeReference<BaseResponse>() {});

            if (responseString.getStatus() != ResponseCode.SUCCESS.getCode()) {
                rspResult.setMsg(responseString.getMessage());
                rspResult.setSuccess(false);
                rspResult.setData(null);
                return rspResult;
            }
            rspResult.setSuccess(true);
            rspResult.setData(responseString.getData());
            rspResult.setMsg("");
            return rspResult;
        } catch (Exception ex) {
            rspResult.setMsg(ex.getMessage());
        }
        return rspResult;
    }

    /**
     * @param orderId
     * @return
     */
    public CustomResult getServiceRequirementByOrderId(String orderId) {
        CustomResult rspResult = new CustomResult();
        try {

            logger.info("Start_getServiceRequirementByOrderId_orderId {}.", orderId);
            // TODO 提交生成环境时，注意地址更换
            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/testrequest/get-service-requirement-by-order-id", interfaceConfig.getAzureDomainUrl());
//            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/testrequest/get-service-requirement-by-order-id", "http://localhost:8087");
            BaseResponse responseString = HttpClientUtil.sendPost(gpoNotesUrl, orderId, new TypeReference<BaseResponse>() {});

            if (responseString.getStatus() != ResponseCode.SUCCESS.getCode()) {
                rspResult.setMsg(responseString.getMessage());
                rspResult.setSuccess(false);
                rspResult.setData(null);
                return rspResult;
            }
            if (responseString.getData() != null) {
                JSONObject jsonObject = JSON.parseObject(responseString.getData().toString());
                ServiceRequirementBO bo = jsonObject.toJavaObject(ServiceRequirementBO.class);
                rspResult.setSuccess(true);
                rspResult.setData(bo);
                rspResult.setMsg("");
            }
            return rspResult;
        } catch (Exception ex) {
            rspResult.setMsg(ex.getMessage());
        }
        return rspResult;
    }

    /**
     * @param orderId
     * @return
     */
    public CustomResult getMatrixTestLineExtDataByOrderId(String orderId) {
        CustomResult rspResult = new CustomResult();
        try {

            logger.info("Start_getMatrixTestLineExtDataByOrderId_orderId {}.", orderId);
            // TODO 提交生成环境时，注意地址更换
            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/subcontract-matrix-testline/get-matrix-testline", interfaceConfig.getAzureDomainUrl());
//            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/subcontract-matrix-testline/get-matrix-testline", "http://localhost:8087");
            SubContractMatrixTestLineReq subContractMatrixTestLineReq = new SubContractMatrixTestLineReq();
            subContractMatrixTestLineReq.setOrderId(orderId);
            BaseResponse responseString = HttpClientUtil.sendPost(gpoNotesUrl, subContractMatrixTestLineReq, new TypeReference<BaseResponse>() {});

            if (responseString.getStatus() != ResponseCode.SUCCESS.getCode()) {
                rspResult.setMsg(responseString.getMessage());
                rspResult.setSuccess(false);
                rspResult.setData(null);
                return rspResult;
            }
            if (responseString.getData() != null) {
                JSONObject jsonObject = JSON.parseObject(responseString.getData().toString());
                SubContractMatrixTestLineRsp result = jsonObject.toJavaObject(SubContractMatrixTestLineRsp.class);
                rspResult.setSuccess(true);
                rspResult.setData(result);
                rspResult.setMsg("");
            }
            return rspResult;
        } catch (Exception ex) {
            rspResult.setMsg(ex.getMessage());
        }
        return rspResult;
    }

    /**
     * @param orderId
     * @return
     */
    public CustomResult getProductSampleByOrderId(String orderId) {
        CustomResult rspResult = new CustomResult();
        try {

            logger.info("Start_getProductSampleByOrderId_orderId {}.", orderId);
            // TODO 提交生成环境时，注意地址更换
            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/product-sample/get-product-sample", interfaceConfig.getAzureDomainUrl());
//            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/product-sample/get-product-sample", "http://localhost:8087");
            ProductSampleReq productSampleReq = new ProductSampleReq();
            productSampleReq.setOrderId(orderId);
            BaseResponse responseString = HttpClientUtil.sendPost(gpoNotesUrl, productSampleReq, new TypeReference<BaseResponse>() {});

            if (responseString.getStatus() != ResponseCode.SUCCESS.getCode()) {
                rspResult.setMsg(responseString.getMessage());
                rspResult.setSuccess(false);
                rspResult.setData(null);
                return rspResult;
            }
            if (responseString.getData() != null) {
                List<ProductSampleBO> productSampleBO = JSON.parseArray(responseString.getData().toString(), ProductSampleBO.class);
                rspResult.setSuccess(true);
                rspResult.setData(productSampleBO);
                rspResult.setMsg("");
            }
            return rspResult;
        } catch (Exception ex) {
            rspResult.setMsg(ex.getMessage());
        }
        return rspResult;
    }
    /**
     * @param orderId
     * @return
     */
    public CustomResult getTagByOrderId(String orderId) {
        CustomResult rspResult = new CustomResult();
        try {

            logger.info("Start_getTagByOrderId_orderId {}.", orderId);
            // TODO 提交生成环境时，注意地址更换
            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/tag/get-tag", interfaceConfig.getAzureDomainUrl());
//            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/tag/get-tag", "http://localhost:8087");
            OrderTagQueryReq orderTagQueryReq = new OrderTagQueryReq();
            orderTagQueryReq.setOrderId(orderId);
            orderTagQueryReq.setProductLineCode(ProductLineType.AFL.getProductLineAbbr());
            BaseResponse responseString = HttpClientUtil.sendPost(gpoNotesUrl, orderTagQueryReq, new TypeReference<BaseResponse>() {});

            if (responseString.getStatus() != ResponseCode.SUCCESS.getCode()) {
                rspResult.setMsg(responseString.getMessage());
                rspResult.setSuccess(false);
                rspResult.setData(null);
                return rspResult;
            }
            if (responseString.getData() != null) {
                List<TagToStarlimsRsp> tagBO = JSON.parseArray(responseString.getData().toString(), TagToStarlimsRsp.class);
                rspResult.setSuccess(true);
                rspResult.setData(tagBO);
                rspResult.setMsg("");
            }
            return rspResult;
        } catch (Exception ex) {
            rspResult.setMsg(ex.getMessage());
        }
        return rspResult;
    }
    /**
     * @param orderId
     * @return
     */
    public CustomResult getAnalyteByOrderId(String orderId) {
        CustomResult rspResult = new CustomResult();
        try {

            logger.info("Start_getTagByOrderId_orderId {}.", orderId);
            // TODO 提交生成环境时，注意地址更换
            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/test-line/get-quotation-testline-list", interfaceConfig.getAzureDomainUrl());
//            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/test-line/get-quotation-testline-list", "http://localhost:8087");
            OrderTagQueryReq orderTagQueryReq = new OrderTagQueryReq();
            orderTagQueryReq.setOrderId(orderId);
            orderTagQueryReq.setProductLineCode(ProductLineType.AFL.getProductLineAbbr());
            BaseResponse responseString = HttpClientUtil.sendPost(gpoNotesUrl, orderTagQueryReq, new TypeReference<BaseResponse>() {});

            if (responseString.getStatus() != ResponseCode.SUCCESS.getCode()) {
                rspResult.setMsg(responseString.getMessage());
                rspResult.setSuccess(false);
                rspResult.setData(null);
                return rspResult;
            }
            if (responseString.getData() != null) {
                List<QuotationTestLineInfoRsp> list = JSON.parseArray(responseString.getData().toString(), QuotationTestLineInfoRsp.class);
                rspResult.setSuccess(true);
                rspResult.setData(list);
                rspResult.setMsg("");
            }
            return rspResult;
        } catch (Exception ex) {
            rspResult.setMsg(ex.getMessage());
        }
        return rspResult;
    }
    /**
     * @param orderId
     * @return
     */
    public CustomResult getParcelByOrderId(String orderId) {
        CustomResult rspResult = new CustomResult();
        try {

            logger.info("Start_getParcelByOrderId_orderId {}.", orderId);
            // TODO 提交生成环境时，注意地址更换
            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/parcel/get-parcel-by-order-id", interfaceConfig.getAzureDomainUrl());
//            String gpoNotesUrl = String.format("%s/af/gpo-micro-api/parcel/get-parcel-by-order-id", "http://localhost:8087");
            BaseResponse responseString = HttpClientUtil.sendPost(gpoNotesUrl, orderId, new TypeReference<BaseResponse>() {});

            if (responseString.getStatus() != ResponseCode.SUCCESS.getCode()) {
                rspResult.setMsg(responseString.getMessage());
                rspResult.setSuccess(false);
                rspResult.setData(null);
                return rspResult;
            }
            if (responseString.getData() != null) {
                List<ParcelBO> bo = JSON.parseArray(responseString.getData().toString(), ParcelBO.class);
                rspResult.setSuccess(true);
                rspResult.setData(bo);
                rspResult.setMsg("");
            }
            return rspResult;
        } catch (Exception ex) {
            rspResult.setMsg(ex.getMessage());
        }
        return rspResult;
    }
}
