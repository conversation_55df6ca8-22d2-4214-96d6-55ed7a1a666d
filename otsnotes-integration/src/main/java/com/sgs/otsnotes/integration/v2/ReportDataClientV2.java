package com.sgs.otsnotes.integration.v2;

import com.dtflys.forest.annotation.*;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.otsnotes.facade.model.req.subcontract.QuerySubcontractDataReq;
import com.sgs.otsnotes.facade.model.req.subcontract.InvalidateSubcontractDataReq;
import com.sgs.otsnotes.facade.model.req.subcontract.ReportDataReplaceReq;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;

import java.util.List;

/**
 * 分包数据服务客户端 - Forest V2版本
 * 用于调用分包相关的数据查询和失效操作
 * 
 * <AUTHOR> Generated
 */
@BaseRequest(baseURL = "#{current.environment.url}")
@Success(condition = SgsSuccessWhen.class)
public interface ReportDataClientV2 {

    /**
     * build4StarLims
     *
     *
     * @param rawDataJson
     * @return
     */
    @Post(url = "/testdatabiz/api/starlimsReport/build4StarLims")
//    @Retry(maxRetryCount = "3", maxRetryInterval = "1000")
    BaseResponse<ReportTestDataInfo> build4StarLims(@JSONBody String rawDataJson);


    /**
     * 保存Report Data接口
     *
     *
     * @param request 查询请求参数
     * @return 分包数据响应
     */
    @Post(url = "/testdatabiz/api/test/subTestData/importData")
    @Retry(maxRetryCount = "3", maxRetryInterval = "1000")
    BaseResponse<Void> importData(@JSONBody ReportTestDataInfo request);

    /**
     * 查询分包数据接口
     * 用于获取指定ReportNo下的已保存数据
     * 
     * @param request 查询请求参数
     * @return 分包数据响应
     */
    @Post(url = "/testdatabiz/api/subContract/querySubcontractData")
    @Retry(maxRetryCount = "3", maxRetryInterval = "1000")
    BaseResponse<List<ReportTestDataInfo>> queryReportTestData(@JSONBody QuerySubcontractDataReq request);

    /**
     * 使分包数据失效接口
     * 用于将指定ReportNo下的数据置为无效状态
     * 
     * @param request 失效请求参数
     * @return 操作结果
     */
    @Post(url = "/testdatabiz/api/subContract/invalidateSubcontractData")
    @Retry(maxRetryCount = "3", maxRetryInterval = "1000")
    BaseResponse<Void> invalidateSubcontractData(@JSONBody InvalidateSubcontractDataReq request);

    /**
     * 更新ReportNo接口
     * 用于修改数据中的ReportNo关联关系
     * 
     * @param request 请求参数
     * @return 操作结果
     */
    @Post(url = "/testdatabiz/api/subContract/replaceSubcontractReportNo")
    @Retry(maxRetryCount = "3", maxRetryInterval = "1000")
    BaseResponse<Void> updateReportNo(@JSONBody ReportDataReplaceReq request);
}