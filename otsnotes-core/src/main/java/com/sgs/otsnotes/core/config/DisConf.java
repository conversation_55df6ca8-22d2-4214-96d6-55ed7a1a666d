package com.sgs.otsnotes.core.config;

import com.google.common.collect.Lists;
import com.sgs.otsnotes.core.util.NumberUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@RefreshScope
@Configuration
public class DisConf {
    /**
     *
     */
    @Value("${fast.testLineIds}")
    private String fastTestLineIds;
    /**
     *
     */
    @Value("${rtf.jobHostIp}")
    private String rtfJobHostIp;
    /**
     *
     */
    @Value("${isEnableDBRule}")
    private boolean isEnableDBRule;


    @Value("${tLWorkingInstructionApi.Enable}")
    private boolean tLWorkingInstructionApi;

    @Value("${trims.queryAnalyteFlag}")
    private boolean queryAnalyteFlag;

    @Value("${trims.updateEventSync}")
    private boolean updateEventSync;

    @Value("${trims.oldSyncEvent}")
    private boolean oldSyncEvent;

    @Value("${trims.securityCode}")
    private String securityCode;

    @Value("${resource.location}")
    private String location;

    @Value("${resource.patterns}")
    private String patterns;
    /**
     *
     */
    @Value("${email.contacts}")
    private String email;

    @Value("${breakCheck.approve}")
    private boolean breakCheckApprove;

    @Value("${worksheet.timeout}")
    private int workSheetTimeout;

    @Value("${switch.newStarlimsReceiveReport}")
    private boolean switchNewStarlimsReceiveReport;
    @Value("${switch.newUpdateBreakDown}")
    private Boolean switchNewUpdateBreakDown;
    /**
     *
     * @return
     */
    public List<Integer> getFastTestLineIds() {
        List<Integer> testLineIds = Lists.newArrayList();
        if (StringUtils.isBlank(fastTestLineIds)){
            return testLineIds;
        }
        for (String fastTestLineId: fastTestLineIds.split(",")){
            int testLineId = NumberUtil.toInt(fastTestLineId);
            if (testLineId <= 0){
                continue;
            }
            testLineIds.add(testLineId);
        }
        return testLineIds;
    }

    public String getRtfJobHostIp() {
        return rtfJobHostIp;
    }

    public boolean isEnableDBRule() {
        return isEnableDBRule;
    }

    public boolean istLWorkingInstructionApi() {
        return tLWorkingInstructionApi;
    }

    public boolean isQueryAnalyteFlag() {
        return queryAnalyteFlag;
    }

    public String getLocation() {
        return location;
    }

    public String getPatterns() {
        return patterns;
    }

    public boolean isUpdateEventSync() {
        return updateEventSync;
    }

    public String getSecurityCode() {
        return securityCode;
    }

    public boolean isOldSyncEvent() {
        return oldSyncEvent;
    }

    public List<String> getEmail() {
        if (StringUtils.isBlank(email)){
            return Lists.newArrayList();
        }
        return Lists.newArrayList(email.split(","));
    }

    public boolean isBreakCheckApprove() {
        return breakCheckApprove;
    }

    public int getWorkSheetTimeout() {
        return workSheetTimeout;
    }

    public boolean isSwitchNewStarlimsReceiveReport() {
        return switchNewStarlimsReceiveReport;
    }

    public boolean isSwitchNewUpdateBreakDown() {
        return switchNewUpdateBreakDown;
    }

    public void setSwitchNewUpdateBreakDown(boolean switchNewUpdateBreakDown) {
        this.switchNewUpdateBreakDown = switchNewUpdateBreakDown;
    }
}
