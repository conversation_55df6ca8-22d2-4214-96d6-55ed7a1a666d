<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sgs.otsnotes</groupId>
    <artifactId>sgs-otsnotes</artifactId>
    <packaging>pom</packaging>
    <version>1.1.119</version>
    <modules>
        <module>otsnotes-web</module>
        <module>otsnotes-core</module>
        <module>otsnotes-facade-model</module>
        <module>otsnotes-dbstorages</module>
        <module>otsnotes-infra</module>
        <module>uni-otsnotes</module>
        <module>otsnotes-domain</module>
        <module>otsnotes-integration</module>
        <module>otsnotes-test</module>
        <module>otsnotes-facade</module>
        <module>otsnotes-facade-impl</module>
        <module>otsnotes-mybatis-generator</module>
        <module>otsnotes-subcontract</module>
    </modules>

    <properties>

        <sgs-framework.version>1.1.002-beta</sgs-framework.version>
        <user.version>1.0.16</user.version>
        <grus-core.version>1.1.2</grus-core.version>
        <grus-async.version>1.2.11</grus-async.version>
        <sgs.frameWork.version>1.0.13</sgs.frameWork.version>
        <preorder.version>3.0.64</preorder.version>
        <trimslocal.version>2.25.11.1</trimslocal.version>
        <bizlog-client.version>1.0.1</bizlog-client.version>
        <extsystem.facade.version>1.1.53</extsystem.facade.version>
        <fileservice.version>1.0.24</fileservice.version>
        <dataEntry.version>1.0.20</dataEntry.version>

        <!-- self version -->
        <otsnotes.version>1.1.119</otsnotes.version>

        <testdatabiz.version>1.3.50-SNAPSHOT</testdatabiz.version>

        <ec.facade.version>1.0.0</ec.facade.version>
        <notes-common.version>1.0.1</notes-common.version>
        <sgs.infrastructure.version>0.0.16-SNAPSHOT</sgs.infrastructure.version>
        <sgs.framework.unpack.version>1.1.41-SNAPSHOT</sgs.framework.unpack.version>


        <java.version>1.8</java.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <swagger.bootstrapui.version>1.9.6</swagger.bootstrapui.version>

        <!-- 文件拷贝时的编码 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- 编译时的编码 -->
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.test.skip>false</maven.test.skip>

        <spring.boot.version>2.4.2</spring.boot.version>
        <redisson.version>3.13.6</redisson.version>
        <jedis.version>3.3.0</jedis.version>
        <jetcache.version>2.6.7</jetcache.version>

        <!--数据库配置-->
        <mybatis.version>3.4.1</mybatis.version>
        <mybatis-spring.version>1.3.0</mybatis-spring.version>
        <mysql.version>8.0.26</mysql.version>
        <druid.version>1.2.24</druid.version>

        <mybatis.generator-version>1.3.5</mybatis.generator-version>
        <mybatis.spring.boot.version>1.2.0</mybatis.spring.boot.version>
        <pagehelper.version>5.1.8</pagehelper.version>

        <httpclient.version>4.5.2</httpclient.version>
        <httpcore.version>4.4.4</httpcore.version>

        <dozer.version>5.5.1</dozer.version>
        <guava.version>22.0</guava.version>

        <validation-api.version>2.0.1.Final</validation-api.version>
        <hibernate-validator.version>6.0.15.Final</hibernate-validator.version>

        <commons-lang3.version>3.4</commons-lang3.version>
        <commons-io.version>2.4</commons-io.version>
        <commons-net.version>3.4</commons-net.version>
        <commons-fileupload-version>1.3.3</commons-fileupload-version>
        <lombok.version>1.16.18</lombok.version>
        <joda-time.verson>2.9.9</joda-time.verson>

        <log4j-over-slf4j.version>1.7.25</log4j-over-slf4j.version>
        <fastjson.version>1.2.49</fastjson.version>
        <poi.version>3.17</poi.version>
        <jboss-jaxrs-api.version>1.0.0.Final</jboss-jaxrs-api.version>

        <dubbo.version>2.8.4</dubbo.version>
        <junit.version>4.13.1</junit.version>
        <archunit.version>1.4.1</archunit.version>
        <jmockit.version>1.27</jmockit.version>
        <dom4j.version>1.6.1</dom4j.version>


        <zkclient.version>0.1</zkclient.version>
        <javassist.version>3.12.1.GA</javassist.version>
        <org.javassist.version>3.25.0-GA</org.javassist.version>
        <fasterxml.jackson.version>2.11.2</fasterxml.jackson.version>

        <axis.version>1.4</axis.version>
        <axis-wsdl4j.version>1.5.1</axis-wsdl4j.version>
        <javacsv.version>2.0</javacsv.version>
        <xstream.version>1.4.4</xstream.version>
        <xmlbeans.version>2.6.0</xmlbeans.version>
        <commons-discovery.version>0.5</commons-discovery.version>
        <elastic-job.version>2.1.5</elastic-job.version>

        <dom4j.version>1.6.1</dom4j.version>
        <joda-time.verson>2.10</joda-time.verson>
        <cxf.version>3.1.6</cxf.version>

        <barcode.version>1.1.1</barcode.version>
        <springfox.version>3.0.0</springfox.version>
        <hystrix-version>1.5.9</hystrix-version>
        <pageoffice.version>4.6.0.4</pageoffice.version>

        <bizlog.version>1.2.19</bizlog.version>
        <customer.version>1.0.3</customer.version>
        <aviator.version>5.2.4</aviator.version>

        <javaxmail.version>1.4.6</javaxmail.version>
        <aspose.verion>18.7</aspose.verion>
        <liteflow.version>2.13.2</liteflow.version>
        <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
<!--        <snailjob.version>1.7.1-jdk8-hotfix</snailjob.version>-->
        <xxl.job.version>2.4.0</xxl.job.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--snailJob -->
<!--            <dependency>-->
<!--                <groupId>com.aizuda</groupId>-->
<!--                <artifactId>snail-job-client-starter</artifactId>-->
<!--                <version>${snailjob.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.aizuda</groupId>-->
<!--                <artifactId>snail-job-client-retry-core</artifactId>-->
<!--                <version>${snailjob.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.aizuda</groupId>-->
<!--                <artifactId>snail-job-client-job-core</artifactId>-->
<!--                <version>${snailjob.version}</version>-->
<!--            </dependency>-->
            <!-- spring-boot配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jdbc</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-logging</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-thymeleaf</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.jmockit</groupId>
                <artifactId>jmockit</artifactId>
                <version>${jmockit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.tngtech.archunit</groupId>
                <artifactId>archunit-junit4</artifactId>
                <version>${archunit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.tngtech.archunit</groupId>
                <artifactId>archunit</artifactId>
                <version>${archunit.version}</version>
                <scope>test</scope>
            </dependency>

            <!--数据库配置-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- 第三方Mybatis分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <!--dozer-->
            <dependency>
                <groupId>net.sf.dozer</groupId>
                <artifactId>dozer</artifactId>
                <version>${dozer.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sf.dozer</groupId>
                <artifactId>dozer-spring</artifactId>
                <version>${dozer.version}</version>
            </dependency>
            <!--validator-->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aspose</groupId>
                <artifactId>aspose-words</artifactId>
                <version>${aspose.verion}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>3.4.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.jboss.spec.javax.ws.rs</groupId>
                <artifactId>jboss-jaxrs-api_2.0_spec</artifactId>
                <version>${jboss-jaxrs-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.sgroschupf</groupId>
                <artifactId>zkclient</artifactId>
                <version>${zkclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${org.javassist.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${log4j-over-slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-rt-frontend-jaxws</artifactId>
                <version>${cxf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-rt-transports-http</artifactId>
                <version>${cxf.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${springfox.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>${swagger.bootstrapui.version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-core</artifactId>
                <version>${hystrix-version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-metrics-event-stream</artifactId>
                <version>${hystrix-version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-javanica</artifactId>
                <version>${hystrix-version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-servo-metrics-publisher</artifactId>
                <version>${hystrix-version}</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.verson}</version>
            </dependency>


            <dependency>
                <groupId>com.zhuozhengsoft</groupId>
                <artifactId>pageoffice</artifactId>
                <version>5.4.0.3</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-bizlog</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.kafka</groupId>
                        <artifactId>spring-kafka</artifactId>
                    </exclusion>
                </exclusions>
                <version>${bizlog.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-async</artifactId>
                <version>${grus-async.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.bizlog</groupId>
                <artifactId>bizlog-facade</artifactId>
                <version>${bizlog-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.preorder</groupId>
                <artifactId>preorder-facade</artifactId>
                <version>${preorder.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.preorder</groupId>
                <artifactId>preorder-facade-model</artifactId>
                <version>${preorder.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-facade</artifactId>
                <version>${testdatabiz.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sgs.otsnotes</groupId>
                        <artifactId>otsnotes-facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sgs.otsnotes</groupId>
                        <artifactId>otsnotes-facade-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sgs.ec</groupId>
                <artifactId>NotificationFacadeService</artifactId>
                <version>${ec.facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.extsystem</groupId>
                <artifactId>extsystem-facade</artifactId>
                <version>${extsystem.facade.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sgs.otsnotes</groupId>
                        <artifactId>otsnotes-facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sgs.otsnotes</groupId>
                        <artifactId>otsnotes-facade-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sgs.preorder</groupId>
                        <artifactId>preorder-facade-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sgs.extsystem</groupId>
                <artifactId>extsystem-facade-model</artifactId>
                <version>${extsystem.facade.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sgs.preorder</groupId>
                        <artifactId>preorder-facade-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sgs.file</groupId>
                <artifactId>file-facade</artifactId>
                <version>${fileservice.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sgs.otsnotes</groupId>
                        <artifactId>otsnotes-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sgs.otsnotes</groupId>
                        <artifactId>otsnotes-facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sgs.file</groupId>
                        <artifactId>file-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sgs.trimslocal</groupId>
                <artifactId>trimslocal-facade</artifactId>
                <version>${trimslocal.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- self -->
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-domain</artifactId>
                <version>${otsnotes.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-web</artifactId>
                <version>${otsnotes.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-facade</artifactId>
                <version>${otsnotes.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-facade-model</artifactId>
                <version>${otsnotes.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-facade-impl</artifactId>
                <version>${otsnotes.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-core</artifactId>
                <version>${otsnotes.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-integration</artifactId>
                <version>${otsnotes.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-test</artifactId>
                <version>${otsnotes.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-dbstorages</artifactId>
                <version>${otsnotes.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-infra</artifactId>
                <version>${otsnotes.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-mybatis-generator</artifactId>
                <version>${otsnotes.version}</version>
            </dependency>

            <!-- elastic-job -->
            <dependency>
                <groupId>com.dangdang</groupId>
                <artifactId>elastic-job-common-core</artifactId>
                <version>${elastic-job.version}</version>
            </dependency>
            <dependency>
                <artifactId>elastic-job-cloud-executor</artifactId>
                <groupId>com.dangdang</groupId>
                <version>${elastic-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dangdang</groupId>
                <artifactId>elastic-job-lite-core</artifactId>
                <version>${elastic-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dangdang</groupId>
                <artifactId>elastic-job-lite-spring</artifactId>
                <version>${elastic-job.version}</version>
            </dependency>
            <!-- elastic-job  end -->

            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-barcode</artifactId>
                <version>${barcode.version}</version>
            </dependency>

            <!--  -->
            <dependency>
                <groupId>net.sourceforge.javacsv</groupId>
                <artifactId>javacsv</artifactId>
                <version>2.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.axis</groupId>
                <artifactId>axis</artifactId>
                <version>${axis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.axis</groupId>
                <artifactId>axis-jaxrpc</artifactId>
                <version>${axis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.axis</groupId>
                <artifactId>axis-saaj</artifactId>
                <version>${axis.version}</version>
            </dependency>
            <dependency>
                <groupId>axis</groupId>
                <artifactId>axis-wsdl4j</artifactId>
                <version>${axis-wsdl4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.xmlbeans</groupId>
                <artifactId>xmlbeans</artifactId>
                <version>${xmlbeans.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-discovery</groupId>
                <artifactId>commons-discovery</artifactId>
                <version>${commons-discovery.version}</version>
            </dependency>

            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons-net.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/commons-fileupload/commons-fileupload -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons-fileupload-version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.user</groupId>
                <artifactId>UserManagementFacadeService</artifactId>
                <version>${user.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.customer</groupId>
                <artifactId>CustomerFacadeService</artifactId>
                <version>${customer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-core</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-corebase</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-model</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-context</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-context-extension</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-log</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-config</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-tinyid</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-commons</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>com.sgs.framework</groupId>
                        <artifactId>sgs-framework-swagger</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sgs.framework</groupId>
                        <artifactId>sgs-framework-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                </exclusions>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-kafka</artifactId>
                <version>${sgs-framework.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sgs.framework</groupId>
                        <artifactId>sgs-framework-tool</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-traceability</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-dictscan</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-token</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-core</artifactId>
                <version>${grus-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>FrameWorkFacadeService</artifactId>
                <version>${sgs.frameWork.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>${javaxmail.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.notes-common</groupId>
                <artifactId>notes-common-facade</artifactId>
                <version>${notes-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sgs.dataentry</groupId>
                <artifactId>dataentry-facade</artifactId>
                <version>${dataEntry.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl.job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-autoconfigure</artifactId>
                <version>${jetcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-anno</artifactId>
                <version>${jetcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-anno-api</artifactId>
                <version>${jetcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis-springdata</artifactId>
                <version>${jetcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-spring-boot-starter</artifactId>
                <version>${liteflow.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>my-deploy-release</id>
            <url>https://cnmaven.sgs.net/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>my-deploy-snapshot</id>
            <url>https://cnmaven.sgs.net/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>