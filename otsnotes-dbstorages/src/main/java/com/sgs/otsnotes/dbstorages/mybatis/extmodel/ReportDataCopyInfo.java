package com.sgs.otsnotes.dbstorages.mybatis.extmodel;

import com.sgs.framework.model.enums.OrderCopyType;
import com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.ReportTemplateInfoPO;
import com.sgs.otsnotes.facade.model.common.PrintFriendliness;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import lombok.Data;

import java.util.List;

@Data
public class ReportDataCopyInfo extends PrintFriendliness {

    private List<ReportTestDataInfo> reportTestDataInfos;

    private String orderNo;
    private String oldReportNo;
    private String newReportNo;
    private String labCode;

}
