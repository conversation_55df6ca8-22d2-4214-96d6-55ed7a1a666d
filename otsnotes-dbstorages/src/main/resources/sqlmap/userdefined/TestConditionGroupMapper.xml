<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestConditionGroupMapper" >
    <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionGroupInfoPO" >
        <id column="ID" property="ID" jdbcType="VARCHAR" />
        <result column="GeneralOrderInstanceID" property="generalOrderInstanceID" jdbcType="VARCHAR" />
        <result column="TestLineInstanceID" property="testLineInstanceID" jdbcType="VARCHAR" />
        <result column="CombinedConditionDescription" property="combinedConditionDescription" jdbcType="VARCHAR" />
        <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
        <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
        <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
        <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
        <result column="LastModifiedTimestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionGroupInfoPO" extends="BaseResultMap" >
        <result column="GroupFootNotes" property="groupFootNotes" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID, GeneralOrderInstanceID, TestLineInstanceID, CombinedConditionDescription, ActiveIndicator,
    CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
    </sql>

    <sql id="Blob_Column_List" >
        GroupFootNotes
    </sql>

    <select id="getTestConditionGroupListByOrderId" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
        SELECT
        <include refid="Base_Column_List" />,
        <include refid="Blob_Column_List" />, LastModifiedTimestamp
        FROM tb_test_condition_group
        WHERE GeneralOrderInstanceID = #{orderId}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO tb_test_condition_group (
        <include refid="Base_Column_List" />,
        <include refid="Blob_Column_List" />
        )
        VALUES
        <foreach collection="conditionGroups" item="conditionGroup" separator=",">
            (
            #{conditionGroup.ID}
            ,#{conditionGroup.generalOrderInstanceID}
            ,#{conditionGroup.testLineInstanceID}
            ,#{conditionGroup.combinedConditionDescription}
            ,#{conditionGroup.activeIndicator}
            ,#{conditionGroup.createdDate}
            ,#{conditionGroup.createdBy}
            ,#{conditionGroup.modifiedDate}
            ,#{conditionGroup.modifiedBy}
            ,#{conditionGroup.groupFootNotes}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        TestLineInstanceID = VALUES(testLineInstanceID),
        CombinedConditionDescription = VALUES(combinedConditionDescription),
        GroupFootNotes = VALUES(groupFootNotes),
        ModifiedDate = VALUES(modifiedDate),
        ModifiedBy = VALUES(modifiedBy)
    </insert>

    <update id="updateTestConditionGroupInfo" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionGroupInfoPO">
        UPDATE tb_test_condition_group
        SET CombinedConditionDescription = #{combinedConditionDescription},
            TestLineInstanceID = #{testLineInstanceID},
            ModifiedDate = #{modifiedDate},
            ModifiedBy = #{modifiedBy}
        WHERE Id = #{ID}
    </update>

    <delete id="batchDelete" parameterType="java.util.List">
        DELETE tcg,ml FROM tb_test_condition_group tcg
        LEFT JOIN tb_test_matrix tm ON tcg.Id = tm.TestConditionGroupID
        LEFT JOIN tb_test_condition_group_multiplelanguage ml ON tcg.Id = ml.TestConditionGroupID
        WHERE tcg.Id IN
        <foreach collection="delConditionGroupIds" item="delConditionGroupId" open="(" close=")" separator=",">
            #{delConditionGroupId}
        </foreach>
        AND tm.TestConditionGroupID NOT IN
        <foreach collection="delConditionGroupIds" item="delConditionGroupId" open="(" close=")" separator=",">
            #{delConditionGroupId}
        </foreach>
    </delete>
    
    <delete id="deleteByIds" parameterType="list">
        DELETE FROM tb_test_condition_group
        WHERE id IN
        <foreach item="id" index="index" collection="list" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteConditionGroupWithMultipleLangByIds" parameterType="list">
        DELETE test_cond_group_mult FROM tb_test_condition_group_multiplelanguage AS test_cond_group_mult
        JOIN tb_test_condition_group AS test_cond_group ON test_cond_group_mult.TestConditionGroupID = test_cond_group.ID
        WHERE test_cond_group.id IN
        <foreach item="id" index="index" collection="list" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
        ;

        DELETE FROM tb_test_condition_group WHERE id IN
        <foreach item="id" index="index" collection="list" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
        ;
    </delete>


    <select id="getTestConditionGroupListByTestLineId" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
        SELECT
        <include refid="Base_Column_List" />,
        <include refid="Blob_Column_List" />
        FROM tb_test_condition_group
        WHERE TestLineInstanceID = #{testLineInstanceID}
    </select>

    <select id="getTestConditionGroupListByMatrixId">
        SELECT
        <include refid="Base_Column_List" />,
        <include refid="Blob_Column_List" />
        FROM tb_test_condition_group
        WHERE TestLineInstanceID = #{testLineInstanceID}
    </select>

    <select id="getGroupByTestLineInstanceId"
            parameterType="java.lang.String"
            resultType="com.sgs.otsnotes.dbstorages.mybatis.extmodel.worksheet.WsTestConditionGroupInfo">
        SELECT DISTINCT
			g.*,tm.MatrixGroupId
		FROM
			tb_test_condition_group g
		JOIN tb_test_matrix tm ON g.ID = tm.TestConditionGroupID
        join tb_test_sample tts on tts.id = tm.TestSampleID
        WHERE
			g.TestLineInstanceID = #{testLineInstanceId}
		order by tm.MatrixGroupId, tts.SampleSeq
    </select>

    <select id="getGroupInfoById"
            parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
            *
		FROM
			tb_test_condition_group
		WHERE
			ID = #{id}
    </select>
    <select id="getTestConditionGroupListByTestLineIds"
            resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionGroupInfoPO">
        select id,TestLineInstanceID from tb_test_condition_group where TestLineInstanceID in
        <foreach collection="tlIds" item="tlId" separator="," open="(" close=")">
            #{tlId}
        </foreach>
    </select>

    <select id="getTestConditionGroupListByIds"
            resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionGroupInfoPO">
        select
            <include refid="Base_Column_List" />,
            <include refid="Blob_Column_List" />
        from tb_test_condition_group
        where id in
        <foreach collection="conditionGroupIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <update id="updateTestConditionGroupInfoDesc" parameterType="java.util.List">
        <foreach collection="conditionInfos" item="conditionInfo" separator=";">
            UPDATE tb_test_condition_group
            <set>
                GroupFootNotes = #{conditionInfo.footNotes},
                ModifiedDate=#{conditionInfo.modifiedDate},
                ModifiedBy=#{conditionInfo.modifiedBy}
            </set>
            WHERE ID = #{conditionInfo.conditionGroupID}
        </foreach>
    </update>

    <update id="batchUpdateFootNotes" parameterType="list">
        <foreach collection="list" item="item" separator=";" open="" close="">
            update tb_test_condition_group
            set GroupFootNotes = #{item.groupFootNotes,jdbcType=LONGVARCHAR}
            where id = #{item.ID}
        </foreach>
    </update>

</mapper>