package com.sgs.soda.otsnotes.app.testline.converter;

import com.sgs.soda.otsnotes.app.config.MapStructConfig;
import com.sgs.soda.otsnotes.client.dto.testline.data.*;
import com.sgs.soda.otsnotes.domain.common.valueobject.citation.Citation;
import com.sgs.soda.otsnotes.domain.common.valueobject.citation.CitationLanguage;
import com.sgs.soda.otsnotes.domain.testline.model.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

/**
 * TestlineInstance Domain到DTO的转换器 - 使用MapStruct自动生成
 */
@Mapper(componentModel = "spring", config = MapStructConfig.class)
public interface TestlineInstanceConverter {

    /**
     * 将TestlineInstance domain对象转换为DTO
     */
    @Mapping(target = "id", source = "id", qualifiedByName = "testlineInstanceIdToDTO")
    @Mapping(target = "header", source = "header")
    @Mapping(target = "citation", source = "citation")
    @Mapping(target = "ppTestlines", source = "ppTestlines")
    @Mapping(target = "analytes", source = "analytes")
    @Mapping(target = "labSections", source = "labSections")
    TestlineInstanceDTO toDTO(TestlineInstance testlineInstance);

    /**
     * 将TestlineInstance domain对象转换为DTO
     */
    @Mapping(target = "id", source = "id", qualifiedByName = "testlineInstanceIdToDTO")
    @Mapping(target = "header", source = "header")
    @Mapping(target = "citation", source = "citation")
    @Mapping(target = "ppTestlines", source = "ppTestlines")
    @Mapping(target = "analytes", source = "analytes")
    @Mapping(target = "labSections", source = "labSections")
    List<TestlineInstanceDTO> toDTOS(List<TestlineInstance> testlineInstances);


    /**
     * 转换TestlineHeader
     */
    @Mapping(target = "languages", source = "languages")
    TestlineHeaderDTO toHeaderDTO(TestlineHeader header);

    /**
     * 转换TestlineLanguage
     */
    TestlineLanguageDTO toTestlineLanguageDTO(TestlineLanguage language);

    /**
     * 转换PpTestline
     */
    @Mapping(target = "id", source = "id", qualifiedByName = "ppTestlineIdToDTO")
    @Mapping(target = "header", source = "header")
    PpTestlineDTO toPpTestlineDTO(PpTestline ppTestline);

    /**
     * 转换PpTestlineHeader
     */
    @Mapping(target = "languages", source = "languages")
    PpTestlineHeaderDTO toPpTestlineHeaderDTO(PpTestlineHeader header);

    /**
     * 转换PpTestlineLanguage
     */
    PpTestlineLanguageDTO toPpTestlineLanguageDTO(PpTestlineLanguage language);

    /**
     * 转换AnalyteInstance
     */
    @Mapping(target = "languages", source = "languages")
    AnalyteInstanceDTO toAnalyteInstanceDTO(AnalyteInstance analyte);

    /**
     * 转换AnalyteInstanceLanguage
     */
    AnalyteInstanceLanguageDTO toAnalyteInstanceLanguageDTO(AnalyteInstanceLanguage language);

    /**
     * 转换LabSection
     */
    LabSectionDTO toLabSectionDTO(LabSection labSection);

    /**
     * 转换Citation
     */
    @Mapping(target = "languages", source = "languages")
    CitationDTO toCitationDTO(Citation citation);

    /**
     * 转换CitationLanguage
     */
    CitationLanguageDTO toCitationLanguageDTO(CitationLanguage citationLanguage);

    /**
     * 转换列表 - TestlineLanguage
     */
    List<TestlineLanguageDTO> toTestlineLanguageDTOList(List<TestlineLanguage> languageList);

    /**
     * 转换列表 - PpTestlineLanguage
     */
    List<PpTestlineLanguageDTO> toPpTestlineLanguageDTOList(List<PpTestlineLanguage> languageList);

    /**
     * 转换列表 - AnalyteInstanceLanguage
     */
    List<AnalyteInstanceLanguageDTO> toAnalyteInstanceLanguageDTOList(List<AnalyteInstanceLanguage> languageList);

    /**
     * 转换列表 - PpTestline
     */
    List<PpTestlineDTO> toPpTestlineDTOList(List<PpTestline> ppTestlineList);

    /**
     * 转换列表 - AnalyteInstance
     */
    List<AnalyteInstanceDTO> toAnalyteInstanceDTOList(List<AnalyteInstance> analyteList);

    /**
     * 转换列表 - LabSection
     */
    List<LabSectionDTO> toLabSectionDTOList(List<LabSection> labSectionList);

    /**
     * 转换列表 - CitationLanguage
     */
    List<CitationLanguageDTO> toCitationLanguageDTOList(List<CitationLanguage> citationLanguageList);

    /**
     * TestlineInstanceId转换为TestlineInstanceIdDTO
     */
    @Named("testlineInstanceIdToDTO")
    default TestlineInstanceIdDTO testlineInstanceIdToDTO(TestlineInstanceId id) {
        if (id == null) {
            return null;
        }
        return TestlineInstanceIdDTO.of(id.getValue());
    }

    /**
     * PpTestlineId转换为PpTestlineIdDTO
     */
    @Named("ppTestlineIdToDTO")
    default PpTestlineIdDTO ppTestlineIdToDTO(PpTestlineId id) {
        if (id == null) {
            return null;
        }
        return PpTestlineIdDTO.of(id.getValue());
    }
} 