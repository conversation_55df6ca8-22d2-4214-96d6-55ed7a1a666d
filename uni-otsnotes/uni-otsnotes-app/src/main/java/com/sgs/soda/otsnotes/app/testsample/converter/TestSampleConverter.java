package com.sgs.soda.otsnotes.app.testsample.converter;

import com.sgs.soda.otsnotes.app.config.MapStructConfig;
import com.sgs.soda.otsnotes.client.dto.common.data.AttachmentDTO;
import com.sgs.soda.otsnotes.client.dto.testsample.data.*;
import com.sgs.soda.otsnotes.domain.common.valueobject.Attachment;
import com.sgs.soda.otsnotes.domain.testsample.model.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.Map;

/**
 * TestSample Domain到DTO的转换器 - 使用MapStruct自动生成
 */
@Mapper(componentModel = "spring", config = MapStructConfig.class)
public interface TestSampleConverter {

    /**
     * 将TestSample domain对象转换为DTO
     */
    @Mapping(target = "id", source = "id", qualifiedByName = "testSampleIdToDTO")
    @Mapping(target = "header", source = "header")
    @Mapping(target = "material", source = "material")
    @Mapping(target = "groupItems", source = "groupItems")
    @Mapping(target = "attachments", source = "attachments")
    @Mapping(target = "extFields", source = "extFields")
    TestSampleDTO toDTO(TestSample testSample);

    /**
     * 将TestSample domain对象列表转换为DTO列表
     */
    List<TestSampleDTO> toDTOs(List<TestSample> testSamples);

    /**
     * 转换TestSampleHeader
     */
    TestSampleHeaderDTO toHeaderDTO(TestSampleHeader header);

    /**
     * 转换TestSampleMaterial
     */
    TestSampleMaterialDTO toMaterialDTO(TestSampleMaterial material);

    /**
     * 转换TestSampleMaterialLanguage
     */
    TestSampleMaterialLanguageDTO toMaterialLanguageDTO(TestSampleMeterialLanguage language);

    /**
     * 转换TestSampleGroup
     */
    TestSampleGroupDTO toGroupDTO(TestSampleGroup group);

    /**
     * 转换Attachment
     */
    AttachmentDTO toAttachmentDTO(Attachment attachment);

    /**
     * 转换列表 - TestSampleMaterialLanguage
     */
    List<TestSampleMaterialLanguageDTO> toMaterialLanguageDTOList(List<TestSampleMeterialLanguage> languageList);

    /**
     * 转换列表 - TestSampleGroup
     */
    List<TestSampleGroupDTO> toGroupDTOList(List<TestSampleGroup> groupList);

    /**
     * 转换列表 - Attachment
     */
    List<AttachmentDTO> toAttachmentDTOList(List<Attachment> attachmentList);

    /**
     * 转换扩展字段
     */
    Map<String, String> toExtFieldsMap(Map<String, String> extFields);

    /**
     * TestSampleId转换为TestSampleIdDTO
     */
    @Named("testSampleIdToDTO")
    default TestSampleIdDTO testSampleIdToDTO(TestSampleId id) {
        if (id == null) {
            return null;
        }
        return TestSampleIdDTO.of(id.getValue());
    }
}