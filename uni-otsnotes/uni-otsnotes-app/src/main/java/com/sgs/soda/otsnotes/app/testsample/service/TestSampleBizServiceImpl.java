package com.sgs.soda.otsnotes.app.testsample.service;

import com.sgs.soda.otsnotes.app.testsample.converter.TestSampleConverter;
import com.sgs.soda.otsnotes.client.api.TestSampleBizService;
import com.sgs.soda.otsnotes.client.dto.testsample.data.TestSampleDTO;
import com.sgs.soda.otsnotes.client.dto.testsample.qry.TestSampleGetQry;
import com.sgs.soda.otsnotes.client.dto.testsample.qry.TestSampleListGetQry;
import com.sgs.soda.otsnotes.client.dto.testsample.qry.TestSampleGetByOrderNoQry;
import com.sgs.soda.otsnotes.domain.testsample.gateway.TestSampleGateway;
import com.sgs.soda.otsnotes.domain.testsample.model.TestSample;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class TestSampleBizServiceImpl implements TestSampleBizService {

    private final TestSampleGateway testSampleGateway;
    private final TestSampleConverter testSampleConverter;

    @Override
    public TestSampleDTO getTestSampleById(TestSampleGetQry qry) {
        log.info("开始查询TestSample，ID: {}", qry.getTestSampleId());

        // 1. 构建子对象配置
        TestSampleGateway.TestSampleSubObjectOptions subObjectConfig = buildSubObjectConfig(qry);

        // 2. 调用domain层获取数据
        TestSample testSample = testSampleGateway.findById(qry.getTestSampleId(), subObjectConfig);

        if (testSample == null) {
            log.warn("未找到TestSample，ID: {}", qry.getTestSampleId());
            return null;
        }

        // 3. 转换为DTO
        TestSampleDTO testSampleDTO = testSampleConverter.toDTO(testSample);

        log.info("成功查询TestSample，ID: {}", qry.getTestSampleId());
        return testSampleDTO;
    }

    @Override
    public List<TestSampleDTO> getTestSampleByIds(TestSampleListGetQry qry) {
        log.info("开始批量查询TestSample，IDs: {}", qry.getTestSampleIds());

        // 1. 构建子对象配置
        TestSampleGateway.TestSampleSubObjectOptions subObjectConfig = buildSubObjectConfig(qry);

        // 2. 调用domain层获取数据
        List<TestSample> testSamples = testSampleGateway.findByIds(qry.getTestSampleIds(), subObjectConfig);

        if (CollectionUtils.isEmpty(testSamples)) {
            log.warn("未找到TestSample，IDs: {}", qry.getTestSampleIds());
            return Collections.emptyList();
        }

        // 3. 转换为DTO
        List<TestSampleDTO> testSampleDTOs = testSamples.stream()
                .map(testSampleConverter::toDTO)
                .collect(Collectors.toList());

        log.info("成功批量查询TestSample，数量: {}", testSampleDTOs.size());
        return testSampleDTOs;
    }

    @Override
    public List<TestSampleDTO> getTestSampleByOrderNo(TestSampleGetByOrderNoQry qry) {
        log.info("开始根据订单号查询TestSample，OrderNo: {}", qry.getOrderNo());

        // 1. 构建子对象配置
        TestSampleGateway.TestSampleSubObjectOptions subObjectConfig = buildSubObjectConfig(qry);

        // 2. 调用domain层获取数据
        List<TestSample> testSamples = testSampleGateway.findByOrderNo(qry.getOrderNo(), subObjectConfig);

        if (CollectionUtils.isEmpty(testSamples)) {
            log.warn("未找到TestSample，OrderNo: {}", qry.getOrderNo());
            return Collections.emptyList();
        }

        // 3. 转换为DTO
        List<TestSampleDTO> testSampleDTOs = testSamples.stream()
                .map(testSampleConverter::toDTO)
                .collect(Collectors.toList());

        log.info("成功根据订单号查询TestSample，OrderNo: {}，数量: {}", qry.getOrderNo(), testSampleDTOs.size());
        return testSampleDTOs;
    }

    /**
     * 构建子对象配置
     */
    private TestSampleGateway.TestSampleSubObjectOptions buildSubObjectConfig(TestSampleGetQry qry) {
        TestSampleGateway.TestSampleSubObjectOptions config = new TestSampleGateway.TestSampleSubObjectOptions();
        config.setIncludeMaterial(qry.getIncludeMaterial() != null ? qry.getIncludeMaterial() : false); 
        config.setIncludeGroupItems(qry.getIncludeGroupItems() != null ? qry.getIncludeGroupItems() : false);
        config.setIncludeAttachments(qry.getIncludeAttachments() != null ? qry.getIncludeAttachments() : false);
        config.setIncludeExtFields(qry.getIncludeExtFields() != null ? qry.getIncludeExtFields() : false);
        config.setIncludeLanguage(qry.getIncludeLanguage() != null ? qry.getIncludeLanguage() : false);
        return config;
    }

    /**
     * 构建子对象配置 - 重载方法适配不同查询类型
     */
    private TestSampleGateway.TestSampleSubObjectOptions buildSubObjectConfig(TestSampleListGetQry qry) {
        TestSampleGateway.TestSampleSubObjectOptions config = new TestSampleGateway.TestSampleSubObjectOptions();
        config.setIncludeMaterial(qry.getIncludeMaterial() != null ? qry.getIncludeMaterial() : false);
        config.setIncludeGroupItems(qry.getIncludeGroupItems() != null ? qry.getIncludeGroupItems() : false);
        config.setIncludeAttachments(qry.getIncludeAttachments() != null ? qry.getIncludeAttachments() : false);
        config.setIncludeExtFields(qry.getIncludeExtFields() != null ? qry.getIncludeExtFields() : false);
        config.setIncludeLanguage(qry.getIncludeLanguage() != null ? qry.getIncludeLanguage() : false);
        return config;
    }

    /**
     * 构建子对象配置 - 重载方法适配按订单号查询
     */
    private TestSampleGateway.TestSampleSubObjectOptions buildSubObjectConfig(TestSampleGetByOrderNoQry qry) {
        TestSampleGateway.TestSampleSubObjectOptions config = new TestSampleGateway.TestSampleSubObjectOptions();
        config.setIncludeMaterial(qry.getIncludeMaterial() != null ? qry.getIncludeMaterial() : false);
        config.setIncludeGroupItems(qry.getIncludeGroupItems() != null ? qry.getIncludeGroupItems() : false);
        config.setIncludeAttachments(qry.getIncludeAttachments() != null ? qry.getIncludeAttachments() : false);
        config.setIncludeExtFields(qry.getIncludeExtFields() != null ? qry.getIncludeExtFields() : false);
        config.setIncludeLanguage(qry.getIncludeLanguage() != null ? qry.getIncludeLanguage() : false);

        return config;
    }
}