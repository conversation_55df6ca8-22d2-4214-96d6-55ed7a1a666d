package com.sgs.soda.otsnotes.app.testline.service;

import com.alibaba.cola.dto.SingleResponse;
import com.sgs.soda.otsnotes.app.testline.converter.TestlineInstanceConverter;
import com.sgs.soda.otsnotes.client.api.TestlineInstanceBizService;
import com.sgs.soda.otsnotes.client.dto.testline.data.TestlineInstanceDTO;
import com.sgs.soda.otsnotes.client.dto.testline.qry.TestlineInstanceGetByOrderNoQry;
import com.sgs.soda.otsnotes.client.dto.testline.qry.TestlineInstanceGetQry;
import com.sgs.soda.otsnotes.client.dto.testline.qry.TestlineInstanceListGetQry;
import com.sgs.soda.otsnotes.domain.testline.gateway.TestlineInstanceGateway;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstance;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class TestlineInstanceBizServiceImpl implements TestlineInstanceBizService {

    private final TestlineInstanceGateway testlineInstanceGateway;

    private final TestlineInstanceConverter testlineInstanceConverter;

    @Override
    public TestlineInstanceDTO getTestlineInstanceById(TestlineInstanceGetQry qry) {
        log.info("开始查询TestlineInstance，ID: {}", qry.getTestlineInstanceId());

        // 1. 构建子对象配置
        TestlineInstanceGateway.TestlineInstanceSubObjectOptions subObjectConfig =
                buildSubObjectConfig(qry);

        // 2. 调用domain层获取数据
        TestlineInstance testlineInstance = testlineInstanceGateway.findById(
                qry.getTestlineInstanceId(), subObjectConfig);

        if (testlineInstance == null) {
            log.warn("未找到TestlineInstance，ID: {}", qry.getTestlineInstanceId());
            return null;
        }

        // 3. 转换为DTO
        TestlineInstanceDTO testlineInstanceDTO = testlineInstanceConverter.toDTO(testlineInstance);

        log.info("成功查询TestlineInstance，ID: {}", qry.getTestlineInstanceId());
        return testlineInstanceDTO;
    }

    /**
     * 构建子对象配置
     */
    private TestlineInstanceGateway.TestlineInstanceSubObjectOptions buildSubObjectConfig(TestlineInstanceGetQry qry) {
        TestlineInstanceGateway.TestlineInstanceSubObjectOptions config =
                new TestlineInstanceGateway.TestlineInstanceSubObjectOptions();

        config.setIncludeAnalyte(qry.getIncludeAnalyte() != null ? qry.getIncludeAnalyte() : false);
        config.setIncludeLabSection(qry.getIncludeLabSection() != null ? qry.getIncludeLabSection() : false);
        config.setIncludeTestConditionGroup(false); // 暂时不包含测试条件组
        config.setIncludePpTestline(qry.getIncludePpTestLine() != null ? qry.getIncludePpTestLine() : false);
        config.setIncludeCitation(qry.getIncludeCitation() != null ? qry.getIncludeCitation() : false);

        return config;
    }

    /**
     * 构建子对象配置
     */
    private TestlineInstanceGateway.TestlineInstanceSubObjectOptions buildSubObjectConfig(TestlineInstanceListGetQry qry) {
        TestlineInstanceGateway.TestlineInstanceSubObjectOptions config =
                new TestlineInstanceGateway.TestlineInstanceSubObjectOptions();

        config.setIncludeAnalyte(qry.getIncludeAnalyte() != null ? qry.getIncludeAnalyte() : false);
        config.setIncludeLabSection(qry.getIncludeLabSection() != null ? qry.getIncludeLabSection() : false);
        config.setIncludeTestConditionGroup(false); // 暂时不包含测试条件组
        config.setIncludePpTestline(qry.getIncludePpTestLine() != null ? qry.getIncludePpTestLine() : false);
        config.setIncludeCitation(qry.getIncludeCitation() != null ? qry.getIncludeCitation() : false);

        return config;
    }


    /**
     * 构建OrderNo查询的子对象配置
     */
    private TestlineInstanceGateway.TestlineInstanceSubObjectOptions buildSubObjectConfig(TestlineInstanceGetByOrderNoQry qry) {
        TestlineInstanceGateway.TestlineInstanceSubObjectOptions config =
                new TestlineInstanceGateway.TestlineInstanceSubObjectOptions();

        config.setIncludeAnalyte(qry.getIncludeAnalyte() != null ? qry.getIncludeAnalyte() : false);
        config.setIncludeLabSection(qry.getIncludeLabSection() != null ? qry.getIncludeLabSection() : false);
        config.setIncludeTestConditionGroup(false); // 暂时不包含测试条件组
        config.setIncludePpTestline(qry.getIncludePpTestLine() != null ? qry.getIncludePpTestLine() : false);
        config.setIncludeCitation(qry.getIncludeCitation() != null ? qry.getIncludeCitation() : false);

        return config;
    }

    @Override
    public List<TestlineInstanceDTO> getTestlineInstanceByOrderNo(TestlineInstanceGetByOrderNoQry qry) {
        log.info("开始查询TestlineInstance List，OrderNo: {}", qry.getOrderNo());

        // 1. 构建子对象配置
        TestlineInstanceGateway.TestlineInstanceSubObjectOptions subObjectConfig =
                buildSubObjectConfig(qry);

        // 2. 调用domain层获取数据
        List<TestlineInstance> testlineInstanceList = testlineInstanceGateway.findByOrderNo(
                qry.getOrderNo(), subObjectConfig);

        if (testlineInstanceList == null || testlineInstanceList.isEmpty()) {
            log.warn("未找到TestlineInstance，OrderNo: {}", qry.getOrderNo());
            return Collections.emptyList();
        }

        // 3. 转换为DTO
        List<TestlineInstanceDTO> testlineInstanceDTOS = testlineInstanceConverter.toDTOS(testlineInstanceList);

        log.info("成功查询TestlineInstance，OrderNo: {}, count: {}", qry.getOrderNo(), testlineInstanceDTOS.size());
        return testlineInstanceDTOS;
    }

    @Override
    public List<TestlineInstanceDTO> getTestlineInstanceByIds(TestlineInstanceListGetQry qry) {

        // 1. 构建子对象配置
        TestlineInstanceGateway.TestlineInstanceSubObjectOptions subObjectConfig =
                buildSubObjectConfig(qry);

        // 2. 调用domain层获取数据
        List<TestlineInstance> testlineInstanceList = testlineInstanceGateway.findByIds(
                qry.getTestlineInstanceIds(), subObjectConfig);

        if (testlineInstanceList == null) {
            log.warn("未找到TestlineInstance，ID: {}", qry.getTestlineInstanceIds());
            return null;
        }

        // 3. 转换为DTO
        List<TestlineInstanceDTO> testlineInstanceDTOS = testlineInstanceConverter.toDTOS(testlineInstanceList);

        log.info("成功查询TestlineInstance，ID: {}", qry.getTestlineInstanceIds());
        return testlineInstanceDTOS;
    }
}
