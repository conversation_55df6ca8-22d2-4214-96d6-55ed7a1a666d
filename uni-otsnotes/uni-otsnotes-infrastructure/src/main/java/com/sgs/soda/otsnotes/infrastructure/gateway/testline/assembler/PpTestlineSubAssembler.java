package com.sgs.soda.otsnotes.infrastructure.gateway.testline.assembler;

import com.sgs.soda.otsnotes.domain.testline.model.PpTestline;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstance;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstanceId;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.PpTestLineRelationshipDO;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.PpTestLineRelationshipDOExample;
import com.sgs.soda.otsnotes.infrastructure.database.testline.mapper.generated.PpTestLineRelationshipDOMapper;
import com.sgs.soda.otsnotes.infrastructure.gateway.testline.converter.PpTestlineConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * PpTestline子对象组装器
 * 实现批量处理以提升性能
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PpTestlineSubAssembler implements TestlineSubAssembler {

    private final PpTestLineRelationshipDOMapper ppTestLineRelationshipMapper;

    private final PpTestlineConverter mapper;

    @Override
    public void assemble(TestlineInstance testlineInstance,
                         TestlineAssembleContext context) {
        if (!context.getSubObjectOptions().isIncludePpTestline()) {
            return;
        }
        TestlineInstanceId testlineInstanceId = testlineInstance.getId();
        // 使用Example查询PpTestLineRelationship表
        PpTestLineRelationshipDOExample example = new PpTestLineRelationshipDOExample();
        example.createCriteria()
                .andTestLineInstanceIDEqualTo(testlineInstanceId.getValue());

        List<PpTestLineRelationshipDO> ppTestLineRelationshipDOs =
                ppTestLineRelationshipMapper.selectByExample(example);

        List<PpTestline> ppTestlines =
                mapper.toPpTestlineDomains(ppTestLineRelationshipDOs);

        testlineInstance.setPpTestlines(ppTestlines);
    }

    @Override
    public void batchAssemble(List<TestlineInstance> testlineInstanceList, TestlineBatchAssembleContext batchContext) {
        if (!batchContext.getSubObjectOptions().isIncludePpTestline()) {
            return;
        }

        if (CollectionUtils.isEmpty(testlineInstanceList)) {
            return;
        }

        // 提取所有TestlineInstance ID
        Set<String> testlineInstanceIds = testlineInstanceList.stream()
                .map(testlineInstance -> testlineInstance.getId().getValue())
                .collect(Collectors.toSet());

        // 批量查询所有PpTestline
        PpTestLineRelationshipDOExample example = new PpTestLineRelationshipDOExample();
        example.createCriteria()
                .andTestLineInstanceIDIn(new ArrayList<>(testlineInstanceIds));

        List<PpTestLineRelationshipDO> allPpTestLineRelationshipDOs = ppTestLineRelationshipMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(allPpTestLineRelationshipDOs)) {
            // 如果没有PpTestline，给每个TestlineInstance设置空列表
            testlineInstanceList.forEach(testlineInstance -> testlineInstance.setPpTestlines(Collections.emptyList()));
            return;
        }

        // 按testlineInstanceId分组
        Map<String, List<PpTestLineRelationshipDO>> ppTestlinesByTestlineInstanceId = allPpTestLineRelationshipDOs.stream()
                .filter(Objects::nonNull)
                .filter(ppTestline -> ppTestline.getTestLineInstanceID() != null)
                .collect(Collectors.groupingBy(PpTestLineRelationshipDO::getTestLineInstanceID));

        // 为每个TestlineInstance组装PpTestline
        for (TestlineInstance testlineInstance : testlineInstanceList) {
            String testlineInstanceId = testlineInstance.getId().getValue();
            List<PpTestLineRelationshipDO> ppTestLineRelationshipDOs = ppTestlinesByTestlineInstanceId.get(testlineInstanceId);

            if (CollectionUtils.isEmpty(ppTestLineRelationshipDOs)) {
                testlineInstance.setPpTestlines(Collections.emptyList());
                continue;
            }

            // 转换为PpTestline领域对象
            List<PpTestline> ppTestlines = mapper.toPpTestlineDomains(ppTestLineRelationshipDOs);
            testlineInstance.setPpTestlines(ppTestlines);
        }

        log.debug("批量组装了{}个TestlineInstance的PpTestline", testlineInstanceList.size());
    }

    @Override
    public boolean supportsBatchOptimization() {
        return true; // 支持批量优化
    }

    @Override
    public String getName() {
        return "PpTestlineSubAssembler";
    }
} 
