package com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto;

import java.util.Date;

public class TestSampleFieldDO {
    /**
     * Id BIGINT(19) 必填<br>
     * 主键Id
     */
    private Long id;

    /**
     * SampleId VARCHAR(36) 必填<br>
     * 外键样品Id
     */
    private String sampleId;

    /**
     * FieldCode VARCHAR(64) 必填<br>
     * 字段代码
     */
    private String fieldCode;

    /**
     * FieldName VARCHAR(64)<br>
     * 字段名称
     */
    private String fieldName;

    /**
     * FieldValue VARCHAR(1000)<br>
     * 字段值
     */
    private String fieldValue;

    /**
     * FieldText VARCHAR(1000)<br>
     * 字段文本值（该值用于select场景）
     */
    private String fieldText;

    /**
     * Status INTEGER(10) 默认值[0] 必填<br>
     * 状态（0：禁用、1：启用 ）
     */
    private Integer status;

    /**
     * CreatedBy VARCHAR(50)<br>
     * 创建用户
     */
    private String createdBy;

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 修改用户
     */
    private String modifiedBy;

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * LastModifiedTimestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 
     */
    private Date lastModifiedTimestamp;

    /**
     * Id BIGINT(19) 必填<br>
     * 获得 主键Id
     */
    public Long getId() {
        return id;
    }

    /**
     * Id BIGINT(19) 必填<br>
     * 设置 主键Id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * SampleId VARCHAR(36) 必填<br>
     * 获得 外键样品Id
     */
    public String getSampleId() {
        return sampleId;
    }

    /**
     * SampleId VARCHAR(36) 必填<br>
     * 设置 外键样品Id
     */
    public void setSampleId(String sampleId) {
        this.sampleId = sampleId == null ? null : sampleId.trim();
    }

    /**
     * FieldCode VARCHAR(64) 必填<br>
     * 获得 字段代码
     */
    public String getFieldCode() {
        return fieldCode;
    }

    /**
     * FieldCode VARCHAR(64) 必填<br>
     * 设置 字段代码
     */
    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode == null ? null : fieldCode.trim();
    }

    /**
     * FieldName VARCHAR(64)<br>
     * 获得 字段名称
     */
    public String getFieldName() {
        return fieldName;
    }

    /**
     * FieldName VARCHAR(64)<br>
     * 设置 字段名称
     */
    public void setFieldName(String fieldName) {
        this.fieldName = fieldName == null ? null : fieldName.trim();
    }

    /**
     * FieldValue VARCHAR(1000)<br>
     * 获得 字段值
     */
    public String getFieldValue() {
        return fieldValue;
    }

    /**
     * FieldValue VARCHAR(1000)<br>
     * 设置 字段值
     */
    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue == null ? null : fieldValue.trim();
    }

    /**
     * FieldText VARCHAR(1000)<br>
     * 获得 字段文本值（该值用于select场景）
     */
    public String getFieldText() {
        return fieldText;
    }

    /**
     * FieldText VARCHAR(1000)<br>
     * 设置 字段文本值（该值用于select场景）
     */
    public void setFieldText(String fieldText) {
        this.fieldText = fieldText == null ? null : fieldText.trim();
    }

    /**
     * Status INTEGER(10) 默认值[0] 必填<br>
     * 获得 状态（0：禁用、1：启用 ）
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * Status INTEGER(10) 默认值[0] 必填<br>
     * 设置 状态（0：禁用、1：启用 ）
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 获得 创建用户
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 设置 创建用户
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 获得 修改用户
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 设置 修改用户
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * LastModifiedTimestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 获得 
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * LastModifiedTimestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 设置 
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sampleId=").append(sampleId);
        sb.append(", fieldCode=").append(fieldCode);
        sb.append(", fieldName=").append(fieldName);
        sb.append(", fieldValue=").append(fieldValue);
        sb.append(", fieldText=").append(fieldText);
        sb.append(", status=").append(status);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append("]");
        return sb.toString();
    }
}