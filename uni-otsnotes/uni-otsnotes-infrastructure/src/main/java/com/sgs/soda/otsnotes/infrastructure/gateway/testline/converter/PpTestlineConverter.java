package com.sgs.soda.otsnotes.infrastructure.gateway.testline.converter;

import com.sgs.soda.otsnotes.domain.testline.model.AnalyteInstance;
import com.sgs.soda.otsnotes.domain.testline.model.PpTestline;
import com.sgs.soda.otsnotes.domain.testline.model.PpTestlineHeader;
import com.sgs.soda.otsnotes.domain.testline.model.PpTestlineId;
import com.sgs.soda.otsnotes.infrastructure.config.MapStructConfig;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.AnalyteInstanceDO;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.PpTestLineRelationshipDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

/**
 * PpTestline转换器 - 从DO转换为Domain对象
 */
@Mapper(componentModel = "spring", config = MapStructConfig.class)
public interface PpTestlineConverter {
    
    /**
     * 将AnalyteInstanceDO列表转换为AnalyteInstance列表
     */
    List<AnalyteInstance> toDomains(List<AnalyteInstanceDO> dos);
    
    /**
     * 将PpTestLineRelationshipDO列表转换为PpTestline列表
     */
    @Mapping(target = "id", source = "id", qualifiedByName = "stringToPpTestlineId")
    @Mapping(target = "header", source = ".", qualifiedByName = "toPpTestlineHeader")
    List<PpTestline> toPpTestlineDomains(List<PpTestLineRelationshipDO> dos);
    
    /**
     * 将PpTestLineRelationshipDO转换为PpTestline
     */
    @Mapping(target = "id", source = "id", qualifiedByName = "stringToPpTestlineId")
    @Mapping(target = "header", source = ".", qualifiedByName = "toPpTestlineHeader")
    PpTestline toPpTestlineDomain(PpTestLineRelationshipDO relationshipDO);
    
    /**
     * 字符串ID转换为PpTestlineId
     */
    @Named("stringToPpTestlineId")
    default PpTestlineId stringToPpTestlineId(String id) {
        if (id == null) {
            return null;
        }
        return PpTestlineId.of(id);
    }
    /**
     * PpTestLineRelationshipDO转换为PpTestlineHeader
     */
    @Named("toPpTestlineHeader")
    @Mapping(target = "ppArtifactRelId", source = "ppArtifactRelId")
    @Mapping(target = "rootPPBaseId", source = "rootPpBaseId")
    @Mapping(target = "ppBaseId", source = "ppBaseId")
    @Mapping(target = "aid", source = "aid")
    @Mapping(target = "constructionId", source = "constructionId")
    @Mapping(target = "ppNotes", source = "PPNotes")
    @Mapping(target = "sectionId", source = "sectionID")
    @Mapping(target = "sectionLevel", source = "sectionLevel")
    @Mapping(target = "sectionName", source = "sectionName")
    @Mapping(target = "seq", source = "seq")
    @Mapping(target = "createdDate", source = "createdDate")
    @Mapping(target = "createdBy", source = "createdBy")
    @Mapping(target = "modifiedDate", source = "modifiedDate")
    @Mapping(target = "modifiedBy", source = "modifiedBy")
    @Mapping(target = "languages", ignore = true) // 多语言信息需要额外查询
    @Mapping(target = "ppVersionId", ignore = true) // 需要从PP Base信息获取
    @Mapping(target = "rootPPNo", ignore = true) // 需要从PP Base信息获取
    @Mapping(target = "ppNo", ignore = true) // 需要从PP Base信息获取
    @Mapping(target = "ppName", ignore = true) // 需要从PP Base信息获取
    PpTestlineHeader toPpTestlineHeader(PpTestLineRelationshipDO relationshipDO);
} 