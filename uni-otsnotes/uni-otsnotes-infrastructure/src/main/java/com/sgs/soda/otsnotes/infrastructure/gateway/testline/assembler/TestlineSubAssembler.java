package com.sgs.soda.otsnotes.infrastructure.gateway.testline.assembler;

import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstance;
import com.sgs.soda.otsnotes.domain.testline.gateway.TestlineInstanceGateway;

import java.util.List;

/**
 * TestlineInstance子对象组装器接口
 * 符合单一职责原则，每个实现类只负责组装一种子对象
 * 参考TestSampleSubAssembler架构，支持批量优化
 */
public interface TestlineSubAssembler {

    /**
     * 组装子对象到TestlineInstance（单个对象模式）
     *
     * @param testlineInstance 目标TestlineInstance对象
     * @param context           子对象配置
     */
    void assemble(TestlineInstance testlineInstance,
                  TestlineAssembleContext context);

    /**
     * 批量组装子对象到TestlineInstance列表（批量模式，用于性能优化）
     * 默认实现：逐个调用单个组装方法
     *
     * @param testlineInstanceList 目标TestlineInstance对象列表
     * @param batchContext         批量组装上下文
     */
    default void batchAssemble(List<TestlineInstance> testlineInstanceList, TestlineBatchAssembleContext batchContext) {
        // 默认实现：逐个调用单个组装方法（保持向后兼容）
        for (TestlineInstance testlineInstance : testlineInstanceList) {
            String testlineInstanceId = testlineInstance.getId().getValue();
            TestlineAssembleContext singleContext = TestlineAssembleContext.create(
                    batchContext.getSubObjectOptions(),
                    batchContext.getTestlineInstanceDOById(testlineInstanceId)
            );
            assemble(testlineInstance, singleContext);
        }
    }

    /**
     * 获取组装器名称（用于日志和调试）
     */
    String getName();

    /**
     * 是否支持批量优化
     * 返回true表示该组装器重写了batchAssemble方法，提供了批量优化实现
     */
    default boolean supportsBatchOptimization() {
        return false;
    }
}