package com.sgs.soda.otsnotes.infrastructure.gateway.testsample.assembler;

import com.sgs.soda.otsnotes.domain.testsample.model.TestSample;
import com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleFieldDO;
import com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleFieldDOExample;
import com.sgs.soda.otsnotes.infrastructure.database.testsample.mapper.generated.TestSampleFieldDOMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * TestSample扩展字段子对象组装器
 * 实现extFields的组装：key等于TestSampleFieldDO.fieldCode，value等于TestSampleFieldDO.fieldValue
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TestSampleExtFieldsSubAssembler implements TestSampleSubAssembler {

    private final TestSampleFieldDOMapper testSampleFieldMapper;

    @Override
    public void assemble(TestSample testSample, TestSampleAssembleContext context) {
        if (!context.getSubObjectOptions().isIncludeExtFields()) {
            return;
        }

        String testSampleId = testSample.getId().getValue();
        
        // 查询扩展字段
        TestSampleFieldDOExample example = new TestSampleFieldDOExample();
        example.createCriteria()
                .andSampleIdEqualTo(testSampleId)
                .andStatusEqualTo(1); // 只查询状态为启用的字段
        
        List<TestSampleFieldDO> fieldDOList = testSampleFieldMapper.selectByExample(example);
        
        if (CollectionUtils.isEmpty(fieldDOList)) {
            testSample.setExtFields(new HashMap<>());
            return;
        }
        
        // 转换为Map：key=fieldCode, value=fieldValue
        Map<String, String> extFields = fieldDOList.stream()
                .filter(Objects::nonNull)
                .filter(field -> field.getFieldCode() != null)
                .collect(Collectors.toMap(
                        TestSampleFieldDO::getFieldCode,
                        field -> field.getFieldValue() != null ? field.getFieldValue() : "",
                        (existing, replacement) -> replacement // 如果有重复的fieldCode，使用最新的值
                ));
        
        testSample.setExtFields(extFields);
        
        log.debug("为TestSample[{}]组装了{}个扩展字段", testSampleId, extFields.size());
    }

    @Override
    public void batchAssemble(List<TestSample> testSampleList, TestSampleBatchAssembleContext batchContext) {
        if (!batchContext.getSubObjectOptions().isIncludeExtFields()) {
            return;
        }

        if (CollectionUtils.isEmpty(testSampleList)) {
            return;
        }

        // 提取所有TestSample ID
        Set<String> testSampleIds = testSampleList.stream()
                .map(testSample -> testSample.getId().getValue())
                .collect(Collectors.toSet());

        // 批量查询所有扩展字段
        TestSampleFieldDOExample example = new TestSampleFieldDOExample();
        example.createCriteria()
                .andSampleIdIn(new ArrayList<>(testSampleIds))
                .andStatusEqualTo(1); // 只查询状态为启用的字段

        List<TestSampleFieldDO> allFieldDOList = testSampleFieldMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(allFieldDOList)) {
            // 如果没有扩展字段，给每个TestSample设置空Map
            testSampleList.forEach(testSample -> testSample.setExtFields(new HashMap<>()));
            return;
        }

        // 按sampleId分组
        Map<String, List<TestSampleFieldDO>> fieldsByTestSampleId = allFieldDOList.stream()
                .filter(Objects::nonNull)
                .filter(field -> field.getSampleId() != null)
                .collect(Collectors.groupingBy(TestSampleFieldDO::getSampleId));

        // 为每个TestSample组装扩展字段
        for (TestSample testSample : testSampleList) {
            String testSampleId = testSample.getId().getValue();
            List<TestSampleFieldDO> fieldDOList = fieldsByTestSampleId.get(testSampleId);

            if (CollectionUtils.isEmpty(fieldDOList)) {
                testSample.setExtFields(new HashMap<>());
                continue;
            }

            // 转换为Map：key=fieldCode, value=fieldValue
            Map<String, String> extFields = fieldDOList.stream()
                    .filter(Objects::nonNull)
                    .filter(field -> field.getFieldCode() != null)
                    .collect(Collectors.toMap(
                            TestSampleFieldDO::getFieldCode,
                            field -> field.getFieldValue() != null ? field.getFieldValue() : "",
                            (existing, replacement) -> replacement // 如果有重复的fieldCode，使用最新的值
                    ));

            testSample.setExtFields(extFields);
        }

        log.debug("批量组装了{}个TestSample的扩展字段", testSampleList.size());
    }

    @Override
    public boolean supportsBatchOptimization() {
        return true; // 支持批量优化
    }

    @Override
    public String getName() {
        return "TestSampleExtFieldsSubAssembler";
    }
}