package com.sgs.soda.otsnotes.infrastructure.gateway.testline.assembler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sgs.soda.otsnotes.domain.common.valueobject.citation.Citation;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstance;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.TestlineInstanceDO;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Citation实例子对象组装器
 * 实现批量处理以提升性能
 */
@Component
@Slf4j
public class CitationSubAssembler implements TestlineSubAssembler {

    @Override
    public void assemble(TestlineInstance testlineInstance,
                         TestlineAssembleContext context) {
        // 检查是否需要包含Citation
        if (!context.getSubObjectOptions().isIncludeCitation()) {
            return;
        }
        
        // 只处理Citation相关
        TestlineInstanceDO testlineInstanceDO = context.getTestlineInstanceDO();
        Citation citation = new Citation();
        // Citation相关字段
        citation.setCitationBaseId(testlineInstanceDO.getCitationBaseId());
        citation.setCitationId(testlineInstanceDO.getCitationId());
        citation.setCitationVersionId(testlineInstanceDO.getCitationVersionId());
        citation.setCitationType(testlineInstanceDO.getCitationTypeID());
        citation.setCitationSectionId(testlineInstanceDO.getStandardSectionID());
        citation.setCitationSectionName(testlineInstanceDO.getStandardSectionName());
        citation.setCitationName(testlineInstanceDO.getCitationName());
        // 拼接字段、方法描述等可根据实际需求补充
        // citation.setCitationFullName(...);
        // citation.setMethodDesc(...);
        // 多语言暂不处理
        testlineInstance.setCitation(citation);
    }

    @Override
    public void batchAssemble(List<TestlineInstance> testlineInstanceList, TestlineBatchAssembleContext batchContext) {
        if (!batchContext.getSubObjectOptions().isIncludeCitation()) {
            return;
        }

        if (CollectionUtils.isEmpty(testlineInstanceList)) {
            return;
        }

        // 获取TestlineInstanceDO映射
        Map<String, TestlineInstanceDO> testlineInstanceDOMap = batchContext.getTestlineInstanceDOList().stream()
                .collect(Collectors.toMap(TestlineInstanceDO::getId, testlineInstanceDO -> testlineInstanceDO));

        // 为每个TestlineInstance组装Citation
        for (TestlineInstance testlineInstance : testlineInstanceList) {
            String testlineInstanceId = testlineInstance.getId().getValue();
            TestlineInstanceDO testlineInstanceDO = testlineInstanceDOMap.get(testlineInstanceId);

            if (testlineInstanceDO == null) {
                continue;
            }

            // 构建Citation对象
            Citation citation = new Citation();
            citation.setCitationBaseId(testlineInstanceDO.getCitationBaseId());
            citation.setCitationId(testlineInstanceDO.getCitationId());
            citation.setCitationVersionId(testlineInstanceDO.getCitationVersionId());
            citation.setCitationType(testlineInstanceDO.getCitationTypeID());
            citation.setCitationSectionId(testlineInstanceDO.getStandardSectionID());
            citation.setCitationSectionName(testlineInstanceDO.getStandardSectionName());
            citation.setCitationName(testlineInstanceDO.getCitationName());
            
            testlineInstance.setCitation(citation);
        }

        log.debug("批量组装了{}个TestlineInstance的Citation", testlineInstanceList.size());
    }

    @Override
    public boolean supportsBatchOptimization() {
        return true; // 支持批量优化
    }

    @Override
    public String getName() {
        return "CitationSubAssembler";
    }
} 
