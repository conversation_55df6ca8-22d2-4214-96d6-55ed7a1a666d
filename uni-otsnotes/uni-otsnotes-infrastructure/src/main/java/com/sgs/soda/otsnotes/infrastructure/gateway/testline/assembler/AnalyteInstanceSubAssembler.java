package com.sgs.soda.otsnotes.infrastructure.gateway.testline.assembler;

import cn.hutool.core.collection.CollUtil;
import com.sgs.soda.otsnotes.domain.testline.model.AnalyteInstance;
import com.sgs.soda.otsnotes.domain.testline.model.AnalyteInstanceLanguage;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstance;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstanceId;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.AnalyteInstanceDO;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.AnalyteInstanceDOExample;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.AnalyteInstanceLanguageDO;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.AnalyteInstanceLanguageDOExample;
import com.sgs.soda.otsnotes.infrastructure.database.testline.mapper.generated.AnalyteInstanceDOMapper;
import com.sgs.soda.otsnotes.infrastructure.database.testline.mapper.generated.AnalyteInstanceLanguageDOMapper;
import com.sgs.soda.otsnotes.infrastructure.gateway.testline.converter.AnalyteInstanceConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分析物实例子对象组装器
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AnalyteInstanceSubAssembler implements TestlineSubAssembler {

    private final AnalyteInstanceDOMapper analyteInstanceMapper;

    private final AnalyteInstanceLanguageDOMapper analyteInstanceLanguageDOMapper;

    private final AnalyteInstanceConverter converter;

    @Override
    public void assemble(TestlineInstance testlineInstance,
                         TestlineAssembleContext context) {
        if (!context.getSubObjectOptions().isIncludeAnalyte()) {
            return;
        }

        //构建analyte
        TestlineInstanceId testlineInstanceId = testlineInstance.getId();
        List<AnalyteInstance> analyteInstances = selectAnalyteByTlInstanceId(testlineInstanceId);
        testlineInstance.setAnalytes(analyteInstances);

        if (CollUtil.isEmpty(analyteInstances)) {
            return;
        }
        //构建analyte language
        List<String> analyteInstanceIds = analyteInstances.stream().map(AnalyteInstance::getId).collect(Collectors.toList());
        AnalyteInstanceLanguageDOExample analyteInstanceLanguageDOExample = new AnalyteInstanceLanguageDOExample();
        analyteInstanceLanguageDOExample.createCriteria().andAnalyteInstanceIDIn(analyteInstanceIds);
        List<AnalyteInstanceLanguageDO> analyteInstanceLanguageDOS = analyteInstanceLanguageDOMapper
                .selectByExample(analyteInstanceLanguageDOExample);

        // 按analyteInstanceId分组多语言信息
        Map<String, List<AnalyteInstanceLanguageDO>> languageMap = analyteInstanceLanguageDOS.stream()
                .collect(Collectors.groupingBy(AnalyteInstanceLanguageDO::getAnalyteInstanceID));

        // 为每个AnalyteInstance设置多语言信息
        for (AnalyteInstance analyteInstance : analyteInstances) {
            List<AnalyteInstanceLanguageDO> languageDOs = languageMap.get(analyteInstance.getId());
            if (CollUtil.isEmpty(languageDOs)) {
                continue;
            }
            List<AnalyteInstanceLanguage> languages = languageDOs.stream()
                    .map(converter::toAnalyteLanguageDomain)
                    .collect(Collectors.toList());
            analyteInstance.setLanguages(languages);
        }
    }

    private List<AnalyteInstance> selectAnalyteByTlInstanceId(TestlineInstanceId testlineInstanceId) {
        // 使用Example查询
        AnalyteInstanceDOExample example = new AnalyteInstanceDOExample();
        example.createCriteria()
                .andTestLineInstanceIDEqualTo(testlineInstanceId.getValue())
                .andActiveIndicatorEqualTo(true);
        example.setOrderByClause("TestAnalyteSeq ASC");

        List<AnalyteInstanceDO> analyteInstanceDOs =
                analyteInstanceMapper.selectByExample(example);

        return converter.toAnalyteDomains(analyteInstanceDOs);
    }

    @Override
    public void batchAssemble(List<TestlineInstance> testlineInstanceList, TestlineBatchAssembleContext batchContext) {
        if (!batchContext.getSubObjectOptions().isIncludeAnalyte()) {
            return;
        }

        if (CollectionUtils.isEmpty(testlineInstanceList)) {
            return;
        }

        // 提取所有TestlineInstance ID
        Set<String> testlineInstanceIds = testlineInstanceList.stream()
                .map(testlineInstance -> testlineInstance.getId().getValue())
                .collect(Collectors.toSet());

        // 批量查询所有AnalyteInstance
        AnalyteInstanceDOExample example = new AnalyteInstanceDOExample();
        example.createCriteria()
                .andTestLineInstanceIDIn(new ArrayList<>(testlineInstanceIds))
                .andActiveIndicatorEqualTo(true);
        example.setOrderByClause("TestAnalyteSeq ASC");

        List<AnalyteInstanceDO> allAnalyteInstanceDOs = analyteInstanceMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(allAnalyteInstanceDOs)) {
            // 如果没有AnalyteInstance，给每个TestlineInstance设置空列表
            testlineInstanceList.forEach(testlineInstance -> testlineInstance.setAnalytes(Collections.emptyList()));
            return;
        }

        // 按testlineInstanceId分组
        Map<String, List<AnalyteInstanceDO>> analytesByTestlineInstanceId = allAnalyteInstanceDOs.stream()
                .filter(Objects::nonNull)
                .filter(analyte -> analyte.getTestLineInstanceID() != null)
                .collect(Collectors.groupingBy(AnalyteInstanceDO::getTestLineInstanceID));

        // 批量查询所有AnalyteInstanceLanguage
        List<String> allAnalyteInstanceIds = allAnalyteInstanceDOs.stream()
                .map(AnalyteInstanceDO::getId)
                .collect(Collectors.toList());

        AnalyteInstanceLanguageDOExample languageExample = new AnalyteInstanceLanguageDOExample();
        languageExample.createCriteria().andAnalyteInstanceIDIn(allAnalyteInstanceIds);
        List<AnalyteInstanceLanguageDO> allAnalyteInstanceLanguageDOs = analyteInstanceLanguageDOMapper
                .selectByExample(languageExample);

        // 按analyteInstanceId分组多语言信息
        Map<String, List<AnalyteInstanceLanguageDO>> languageMap = allAnalyteInstanceLanguageDOs.stream()
                .filter(Objects::nonNull)
                .filter(lang -> lang.getAnalyteInstanceID() != null)
                .collect(Collectors.groupingBy(AnalyteInstanceLanguageDO::getAnalyteInstanceID));

        // 为每个TestlineInstance组装AnalyteInstance
        for (TestlineInstance testlineInstance : testlineInstanceList) {
            String testlineInstanceId = testlineInstance.getId().getValue();
            List<AnalyteInstanceDO> analyteInstanceDOs = analytesByTestlineInstanceId.get(testlineInstanceId);

            if (CollectionUtils.isEmpty(analyteInstanceDOs)) {
                testlineInstance.setAnalytes(Collections.emptyList());
                continue;
            }

            // 转换为AnalyteInstance领域对象
            List<AnalyteInstance> analyteInstances = converter.toAnalyteDomains(analyteInstanceDOs);

            // 为每个AnalyteInstance设置多语言信息
            for (AnalyteInstance analyteInstance : analyteInstances) {
                List<AnalyteInstanceLanguageDO> languageDOs = languageMap.get(analyteInstance.getId());
                if (CollectionUtils.isEmpty(languageDOs)) {
                    continue;
                }
                List<AnalyteInstanceLanguage> languages = languageDOs.stream()
                        .map(converter::toAnalyteLanguageDomain)
                        .collect(Collectors.toList());
                analyteInstance.setLanguages(languages);
            }

            testlineInstance.setAnalytes(analyteInstances);
        }

        log.debug("批量组装了{}个TestlineInstance的AnalyteInstance", testlineInstanceList.size());
    }

    @Override
    public boolean supportsBatchOptimization() {
        return true; // 支持批量优化
    }

    @Override
    public String getName() {
        return "AnalyteInstanceSubAssembler";
    }
} 
