package com.sgs.soda.otsnotes.infrastructure.gateway.testline.assembler;

import com.sgs.soda.otsnotes.domain.testline.model.TestlineHeader;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstance;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstanceId;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineLanguage;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.TestlineInstanceMultipleLanguageDO;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.TestlineInstanceMultipleLanguageDOExample;
import com.sgs.soda.otsnotes.infrastructure.database.testline.mapper.generated.TestlineInstanceMultipleLanguageDOMapper;
import com.sgs.soda.otsnotes.infrastructure.gateway.testline.converter.TestlineLanguageConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * TestlineInstance多语言子对象组装器
 * 实现批量处理以提升性能
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TestlineInstanceLanguageSubAssembler implements TestlineSubAssembler {

    private final TestlineInstanceMultipleLanguageDOMapper multipleLanguageMapper;

    private final TestlineLanguageConverter mapper ;

    @Override
    public void assemble(TestlineInstance testlineInstance,
                         TestlineAssembleContext context) {
        if (!context.getSubObjectOptions().isIncludeAnalyte()) {
            return;
        }
        TestlineInstanceId testlineInstanceId = testlineInstance.getId();
        // 使用Example查询
        TestlineInstanceMultipleLanguageDOExample example = new TestlineInstanceMultipleLanguageDOExample();
        example.createCriteria()
                .andTestLineInstanceIDEqualTo(testlineInstanceId.getValue());

        List<TestlineInstanceMultipleLanguageDO> multipleLanguageDOs =
                multipleLanguageMapper.selectByExample(example);

        List<TestlineLanguage> multipleLanguages =
                mapper.toDomains(multipleLanguageDOs);

        TestlineHeader header = testlineInstance.getHeader();
        header.setLanguages(multipleLanguages);
    }

    @Override
    public void batchAssemble(List<TestlineInstance> testlineInstanceList, TestlineBatchAssembleContext batchContext) {
        if (!batchContext.getSubObjectOptions().isIncludeAnalyte()) {
            return;
        }

        if (CollectionUtils.isEmpty(testlineInstanceList)) {
            return;
        }

        // 提取所有TestlineInstance ID
        Set<String> testlineInstanceIds = testlineInstanceList.stream()
                .map(testlineInstance -> testlineInstance.getId().getValue())
                .collect(Collectors.toSet());

        // 批量查询所有多语言记录
        TestlineInstanceMultipleLanguageDOExample example = new TestlineInstanceMultipleLanguageDOExample();
        example.createCriteria()
                .andTestLineInstanceIDIn(new ArrayList<>(testlineInstanceIds));

        List<TestlineInstanceMultipleLanguageDO> allMultipleLanguageDOs = multipleLanguageMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(allMultipleLanguageDOs)) {
            // 如果没有多语言记录，给每个TestlineInstance设置空列表
            testlineInstanceList.forEach(testlineInstance -> {
                if (testlineInstance.getHeader() != null) {
                    testlineInstance.getHeader().setLanguages(Collections.emptyList());
                }
            });
            return;
        }

        // 按testlineInstanceId分组
        Map<String, List<TestlineInstanceMultipleLanguageDO>> languagesByTestlineInstanceId = allMultipleLanguageDOs.stream()
                .filter(Objects::nonNull)
                .filter(language -> language.getTestLineInstanceID() != null)
                .collect(Collectors.groupingBy(TestlineInstanceMultipleLanguageDO::getTestLineInstanceID));

        // 为每个TestlineInstance组装多语言
        for (TestlineInstance testlineInstance : testlineInstanceList) {
            String testlineInstanceId = testlineInstance.getId().getValue();
            List<TestlineInstanceMultipleLanguageDO> multipleLanguageDOs = languagesByTestlineInstanceId.get(testlineInstanceId);

            TestlineHeader header = testlineInstance.getHeader();
            if (header == null) {
                continue;
            }

            if (CollectionUtils.isEmpty(multipleLanguageDOs)) {
                header.setLanguages(Collections.emptyList());
                continue;
            }

            // 转换为TestlineLanguage领域对象
            List<TestlineLanguage> multipleLanguages = mapper.toDomains(multipleLanguageDOs);
            header.setLanguages(multipleLanguages);
        }

        log.debug("批量组装了{}个TestlineInstance的多语言", testlineInstanceList.size());
    }

    @Override
    public boolean supportsBatchOptimization() {
        return true; // 支持批量优化
    }

    @Override
    public String getName() {
        return "TestlineInstanceMultipleLanguageSubAssembler";
    }
} 
