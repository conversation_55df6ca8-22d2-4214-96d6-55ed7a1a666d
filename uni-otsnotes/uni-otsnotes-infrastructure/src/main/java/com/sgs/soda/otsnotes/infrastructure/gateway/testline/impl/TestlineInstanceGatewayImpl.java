package com.sgs.soda.otsnotes.infrastructure.gateway.testline.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstance;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineHeader;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstanceId;
import com.sgs.soda.otsnotes.domain.testline.gateway.TestlineInstanceGateway;
import com.sgs.soda.otsnotes.infrastructure.database.preorder.dataobject.auto.GeneralOrderDO;
import com.sgs.soda.otsnotes.infrastructure.database.preorder.dataobject.auto.GeneralOrderDOExample;
import com.sgs.soda.otsnotes.infrastructure.database.preorder.mapper.generated.GeneralOrderDOMapper;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.TestlineInstanceDO;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.TestlineInstanceDOExample;
import com.sgs.soda.otsnotes.infrastructure.database.testline.mapper.generated.TestlineInstanceDOMapper;

import com.sgs.soda.otsnotes.infrastructure.gateway.testline.assembler.*;
import com.sgs.soda.otsnotes.infrastructure.gateway.testline.converter.TestlineInstanceHeaderConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * TestlineInstance Gateway Implementation - 采用Assembler链构�?
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TestlineInstanceGatewayImpl implements TestlineInstanceGateway {

    private final TestlineInstanceDOMapper testlineInstanceMapper;

    private final GeneralOrderDOMapper generalOrderDOMapper;

    // 子对象组装器列表
    private List<TestlineSubAssembler> subAssemblers;

    // 注入各个子对象组装器
    private final TestlineInstanceLanguageSubAssembler multipleLanguageSubAssembler;

    private final AnalyteInstanceSubAssembler analyteInstanceSubAssembler;

    private final LabSectionInstanceSubAssembler labSectionInstanceSubAssembler;

    private final PpTestlineSubAssembler ppTestlineSubAssembler;

    private final CitationSubAssembler citationSubAssembler;

    private final TestlineInstanceHeaderConverter testlineInstanceHeaderConverter = Mappers.getMapper(TestlineInstanceHeaderConverter.class);

    /**
     * 初始化时显式注册所有子对象组装�?
     */
    @PostConstruct
    public void initSubAssemblers() {
        //显式注册 subAssember，易于阅读，不要使用Autowired List<TestlineSubAssembler> subAssemblers
        subAssemblers = new ArrayList<>();
        // 按照业务逻辑顺序注册组装
        subAssemblers.add(multipleLanguageSubAssembler);
        subAssemblers.add(analyteInstanceSubAssembler);
        subAssemblers.add(labSectionInstanceSubAssembler);
        subAssemblers.add(ppTestlineSubAssembler);
        subAssemblers.add(citationSubAssembler);

        log.info("已注册" + subAssemblers.size() + " 个子对象组装器");
    }

    @Override
    public TestlineInstance findById(String testlineInstanceId,
                                     TestlineInstanceSubObjectOptions includeSubObjects) {
        Assert.notEmpty(testlineInstanceId,"testlineInstanceId is required");

        // 转换为批量查询，统一使用批量算法
        List<TestlineInstance> results = findByIds(Collections.singleton(testlineInstanceId), includeSubObjects);
        return CollectionUtils.isEmpty(results) ? null : results.get(0);
    }

    @Override
    public List<TestlineInstance> findByIds(Set<String> testlineInstanceIds,
                                            TestlineInstanceSubObjectOptions includeSubObjects) {
        Assert.notEmpty(testlineInstanceIds,"testlineInstanceIds is required");

        // 使用Example构建ID IN查询
        TestlineInstanceDOExample example = new TestlineInstanceDOExample();
        example.createCriteria()
                .andIdIn(new ArrayList<>(testlineInstanceIds))
                .andActiveIndicatorEqualTo(true);
        example.setOrderByClause("CreatedDate DESC");
        
        List<TestlineInstanceDO> testlineInstanceList = testlineInstanceMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(testlineInstanceList)) {
            return Collections.emptyList();
        }

        // 使用批量算法组装TestlineInstance及其子对象
        return batchAssembleTestlineInstances(testlineInstanceList, includeSubObjects);
    }

    @Override
    public List<TestlineInstance> findByOrderNo(String orderNo, TestlineInstanceSubObjectOptions includeSubObjects) {
        Assert.notEmpty(orderNo, "orderNo is required");

        GeneralOrderDO order = getByOrderNo(orderNo);

        if(Objects.isNull(order)){
            return Collections.emptyList();
        }

        // 使用Example构建OrderNo查询
        TestlineInstanceDOExample example = new TestlineInstanceDOExample();
        example.createCriteria()
                .andGeneralOrderInstanceIDEqualTo(order.getID())
                .andActiveIndicatorEqualTo(true);
        example.setOrderByClause("TestLineSeq ASC, CreatedDate ASC");
        
        List<TestlineInstanceDO> testlineInstanceList = testlineInstanceMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(testlineInstanceList)) {
            return Collections.emptyList();
        }

        // 使用批量算法组装TestlineInstance及其子对象
        return batchAssembleTestlineInstances(testlineInstanceList, includeSubObjects);
    }

    /**
     * 构建TestlineInstance的核心部分（ID和Header�?
     */
    private TestlineInstance buildTestlineInstanceCore(TestlineInstanceDO testlineInstanceDO) {
        TestlineInstance testlineInstance = new TestlineInstance();

        // 设置ID
        testlineInstance.setId(TestlineInstanceId.of(testlineInstanceDO.getId()));

        // 设置Header
        TestlineHeader header = testlineInstanceHeaderConverter.toHeader(testlineInstanceDO);
        testlineInstance.setHeader(header);

        return testlineInstance;
    }

    /**
     * 链式调用子对象组装器
     */
    private void assembleSubObjects(TestlineInstance testlineInstance,
                                    TestlineAssembleContext context) {

        for (TestlineSubAssembler subAssembler : subAssemblers) {
            subAssembler.assemble(testlineInstance, context);
        }
    }

    /**
     * 批量组装TestlineInstance及其子对象（核心批量算法）
     *
     * @param testlineInstanceDOList 主表数据列表
     * @param includeSubObjects 子对象加载选项
     * @return 组装完成的TestlineInstance列表
     */
    private List<TestlineInstance> batchAssembleTestlineInstances(List<TestlineInstanceDO> testlineInstanceDOList, 
                                                                 TestlineInstanceSubObjectOptions includeSubObjects) {
        if (CollectionUtils.isEmpty(testlineInstanceDOList)) {
            return Collections.emptyList();
        }

        // 1. 构建TestlineInstance核心对象列表
        List<TestlineInstance> testlineInstanceList = testlineInstanceDOList.stream()
                .filter(Objects::nonNull)
                .map(this::buildTestlineInstanceCore)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(testlineInstanceList)) {
            return Collections.emptyList();
        }

        // 2. 提取所有TestlineInstance ID用于批量查询子对象
        Set<String> testlineInstanceIds = testlineInstanceDOList.stream()
                .filter(Objects::nonNull)
                .map(TestlineInstanceDO::getId)
                .collect(Collectors.toSet());

        // 3. 创建批量组装上下文
        TestlineBatchAssembleContext batchContext = TestlineBatchAssembleContext.create(
                testlineInstanceIds, includeSubObjects, testlineInstanceDOList);

        // 4. 批量组装子对象
        batchAssembleSubObjects(testlineInstanceList, batchContext);

        return testlineInstanceList;
    }

    /**
     * 批量组装子对象
     *
     * @param testlineInstanceList TestlineInstance列表
     * @param batchContext   批量组装上下文
     */
    private void batchAssembleSubObjects(List<TestlineInstance> testlineInstanceList, TestlineBatchAssembleContext batchContext) {
        for (TestlineSubAssembler subAssembler : subAssemblers) {
            try {
                if (subAssembler.supportsBatchOptimization()) {
                    // 使用批量优化实现
                    log.debug("使用批量优化组装器: {}", subAssembler.getName());
                    subAssembler.batchAssemble(testlineInstanceList, batchContext);
                } else {
                    // 使用默认的逐个组装实现
                    log.debug("使用默认组装器: {}", subAssembler.getName());
                    subAssembler.batchAssemble(testlineInstanceList, batchContext);
                }
            } catch (Exception e) {
                log.error("批量子对象组装失败: {}", subAssembler.getName(), e);
                // 异常隔离，继续执行下一个组装器
            }
        }
    }

    private GeneralOrderDO getByOrderNo(String orderNo) {
        GeneralOrderDOExample generalOrderDOExample = new GeneralOrderDOExample();
        generalOrderDOExample.createCriteria().andOrderNoEqualTo(orderNo);
        List<GeneralOrderDO> generalOrderDOS = generalOrderDOMapper.selectByExample(generalOrderDOExample);
        return CollUtil.get(generalOrderDOS, 0);
    }
} 
