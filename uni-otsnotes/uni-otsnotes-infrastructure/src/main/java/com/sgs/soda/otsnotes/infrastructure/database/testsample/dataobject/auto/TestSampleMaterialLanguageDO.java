package com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto;

import java.util.Date;

public class TestSampleMaterialLanguageDO {
    /**
     * Id VARCHAR(36) 必填<br>
     * 
     */
    private String id;

    /**
     * SampleId VARCHAR(36) 必填<br>
     * 
     */
    private String sampleId;

    /**
     * GroupId VARCHAR(36)<br>
     * 
     */
    private String groupId;

    /**
     * Material VARCHAR(255)<br>
     * 
     */
    private String material;

    /**
     * LanguageId INTEGER(10) 必填<br>
     * 
     */
    private Integer languageId;

    /**
     * Status INTEGER(10) 必填<br>
     * 0：禁用(默认)、1：启用
     */
    private Integer status;

    /**
     * CreatedDate TIMESTAMP(19) 必填<br>
     * 
     */
    private Date createdDate;

    /**
     * CreatedBy VARCHAR(255) 必填<br>
     * 
     */
    private String createdBy;

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 
     */
    private Date modifiedDate;

    /**
     * ModifiedBy VARCHAR(255)<br>
     * 
     */
    private String modifiedBy;

    /**
     * LastModifiedTimestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 
     */
    private Date lastModifiedTimestamp;

    /**
     * Composition VARCHAR(500)<br>
     * 
     */
    private String composition;

    /**
     * Color VARCHAR(500)<br>
     * 
     */
    private String color;

    /**
     * SampleDescforReport VARCHAR(4000)<br>
     * 
     */
    private String sampleDescforReport;

    /**
     * SampleRemark VARCHAR(4000)<br>
     * 
     */
    private String sampleRemark;

    /**
     * EndUse VARCHAR(500)<br>
     * 
     */
    private String endUse;

    /**
     * Description LONGVARCHAR(65535)<br>
     * 
     */
    private String description;

    /**
     * OtherSampleInfo LONGVARCHAR(65535)<br>
     * 
     */
    private String otherSampleInfo;

    /**
     * Id VARCHAR(36) 必填<br>
     * 获得 
     */
    public String getId() {
        return id;
    }

    /**
     * Id VARCHAR(36) 必填<br>
     * 设置 
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * SampleId VARCHAR(36) 必填<br>
     * 获得 
     */
    public String getSampleId() {
        return sampleId;
    }

    /**
     * SampleId VARCHAR(36) 必填<br>
     * 设置 
     */
    public void setSampleId(String sampleId) {
        this.sampleId = sampleId == null ? null : sampleId.trim();
    }

    /**
     * GroupId VARCHAR(36)<br>
     * 获得 
     */
    public String getGroupId() {
        return groupId;
    }

    /**
     * GroupId VARCHAR(36)<br>
     * 设置 
     */
    public void setGroupId(String groupId) {
        this.groupId = groupId == null ? null : groupId.trim();
    }

    /**
     * Material VARCHAR(255)<br>
     * 获得 
     */
    public String getMaterial() {
        return material;
    }

    /**
     * Material VARCHAR(255)<br>
     * 设置 
     */
    public void setMaterial(String material) {
        this.material = material == null ? null : material.trim();
    }

    /**
     * LanguageId INTEGER(10) 必填<br>
     * 获得 
     */
    public Integer getLanguageId() {
        return languageId;
    }

    /**
     * LanguageId INTEGER(10) 必填<br>
     * 设置 
     */
    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    /**
     * Status INTEGER(10) 必填<br>
     * 获得 0：禁用(默认)、1：启用
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * Status INTEGER(10) 必填<br>
     * 设置 0：禁用(默认)、1：启用
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * CreatedDate TIMESTAMP(19) 必填<br>
     * 获得 
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * CreatedDate TIMESTAMP(19) 必填<br>
     * 设置 
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * CreatedBy VARCHAR(255) 必填<br>
     * 获得 
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * CreatedBy VARCHAR(255) 必填<br>
     * 设置 
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 设置 
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * ModifiedBy VARCHAR(255)<br>
     * 获得 
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * ModifiedBy VARCHAR(255)<br>
     * 设置 
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * LastModifiedTimestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 获得 
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * LastModifiedTimestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 设置 
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    /**
     * Composition VARCHAR(500)<br>
     * 获得 
     */
    public String getComposition() {
        return composition;
    }

    /**
     * Composition VARCHAR(500)<br>
     * 设置 
     */
    public void setComposition(String composition) {
        this.composition = composition == null ? null : composition.trim();
    }

    /**
     * Color VARCHAR(500)<br>
     * 获得 
     */
    public String getColor() {
        return color;
    }

    /**
     * Color VARCHAR(500)<br>
     * 设置 
     */
    public void setColor(String color) {
        this.color = color == null ? null : color.trim();
    }

    /**
     * SampleDescforReport VARCHAR(4000)<br>
     * 获得 
     */
    public String getSampleDescforReport() {
        return sampleDescforReport;
    }

    /**
     * SampleDescforReport VARCHAR(4000)<br>
     * 设置 
     */
    public void setSampleDescforReport(String sampleDescforReport) {
        this.sampleDescforReport = sampleDescforReport == null ? null : sampleDescforReport.trim();
    }

    /**
     * SampleRemark VARCHAR(4000)<br>
     * 获得 
     */
    public String getSampleRemark() {
        return sampleRemark;
    }

    /**
     * SampleRemark VARCHAR(4000)<br>
     * 设置 
     */
    public void setSampleRemark(String sampleRemark) {
        this.sampleRemark = sampleRemark == null ? null : sampleRemark.trim();
    }

    /**
     * EndUse VARCHAR(500)<br>
     * 获得 
     */
    public String getEndUse() {
        return endUse;
    }

    /**
     * EndUse VARCHAR(500)<br>
     * 设置 
     */
    public void setEndUse(String endUse) {
        this.endUse = endUse == null ? null : endUse.trim();
    }

    /**
     * Description LONGVARCHAR(65535)<br>
     * 获得 
     */
    public String getDescription() {
        return description;
    }

    /**
     * Description LONGVARCHAR(65535)<br>
     * 设置 
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * OtherSampleInfo LONGVARCHAR(65535)<br>
     * 获得 
     */
    public String getOtherSampleInfo() {
        return otherSampleInfo;
    }

    /**
     * OtherSampleInfo LONGVARCHAR(65535)<br>
     * 设置 
     */
    public void setOtherSampleInfo(String otherSampleInfo) {
        this.otherSampleInfo = otherSampleInfo == null ? null : otherSampleInfo.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sampleId=").append(sampleId);
        sb.append(", groupId=").append(groupId);
        sb.append(", material=").append(material);
        sb.append(", languageId=").append(languageId);
        sb.append(", status=").append(status);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append(", composition=").append(composition);
        sb.append(", color=").append(color);
        sb.append(", sampleDescforReport=").append(sampleDescforReport);
        sb.append(", sampleRemark=").append(sampleRemark);
        sb.append(", endUse=").append(endUse);
        sb.append(", description=").append(description);
        sb.append(", otherSampleInfo=").append(otherSampleInfo);
        sb.append("]");
        return sb.toString();
    }
}