package com.sgs.soda.otsnotes.infrastructure.gateway.testline.assembler;

import com.sgs.soda.otsnotes.domain.testline.model.LabSection;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstance;
import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstanceId;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.LabSectionInstanceDO;
import com.sgs.soda.otsnotes.infrastructure.database.testline.dataobject.auto.LabSectionInstanceDOExample;
import com.sgs.soda.otsnotes.infrastructure.database.testline.mapper.generated.LabSectionInstanceDOMapper;
import com.sgs.soda.otsnotes.infrastructure.gateway.testline.converter.LabSectionConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 实验室部门实例子对象组装器
 * 实现批量处理以提升性能
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class LabSectionInstanceSubAssembler implements TestlineSubAssembler {

    private final LabSectionInstanceDOMapper labSectionInstanceMapper;

    private final LabSectionConverter mapper;

    @Override
    public void assemble(TestlineInstance testlineInstance,
                         TestlineAssembleContext context) {
        if (!context.getSubObjectOptions().isIncludeLabSection()) {
            return;
        }
        TestlineInstanceId testlineInstanceId = testlineInstance.getId();
        // 使用Example查询
        LabSectionInstanceDOExample example = new LabSectionInstanceDOExample();
        example.createCriteria()
                .andTestLineInstanceIDEqualTo(testlineInstanceId.getValue())
                .andActiveIndicatorEqualTo(true);

        List<LabSectionInstanceDO> labSectionInstanceDOs =
                labSectionInstanceMapper.selectByExample(example);

        List<LabSection> labSections =
                mapper.toDomains(labSectionInstanceDOs);

        testlineInstance.setLabSections(labSections);
    }

    @Override
    public void batchAssemble(List<TestlineInstance> testlineInstanceList, TestlineBatchAssembleContext batchContext) {
        if (!batchContext.getSubObjectOptions().isIncludeLabSection()) {
            return;
        }

        if (CollectionUtils.isEmpty(testlineInstanceList)) {
            return;
        }

        // 提取所有TestlineInstance ID
        Set<String> testlineInstanceIds = testlineInstanceList.stream()
                .map(testlineInstance -> testlineInstance.getId().getValue())
                .collect(Collectors.toSet());

        // 批量查询所有LabSection
        LabSectionInstanceDOExample example = new LabSectionInstanceDOExample();
        example.createCriteria()
                .andTestLineInstanceIDIn(new ArrayList<>(testlineInstanceIds))
                .andActiveIndicatorEqualTo(true);

        List<LabSectionInstanceDO> allLabSectionInstanceDOs = labSectionInstanceMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(allLabSectionInstanceDOs)) {
            // 如果没有LabSection，给每个TestlineInstance设置空列表
            testlineInstanceList.forEach(testlineInstance -> testlineInstance.setLabSections(Collections.emptyList()));
            return;
        }

        // 按testlineInstanceId分组
        Map<String, List<LabSectionInstanceDO>> labSectionsByTestlineInstanceId = allLabSectionInstanceDOs.stream()
                .filter(Objects::nonNull)
                .filter(labSection -> labSection.getTestLineInstanceID() != null)
                .collect(Collectors.groupingBy(LabSectionInstanceDO::getTestLineInstanceID));

        // 为每个TestlineInstance组装LabSection
        for (TestlineInstance testlineInstance : testlineInstanceList) {
            String testlineInstanceId = testlineInstance.getId().getValue();
            List<LabSectionInstanceDO> labSectionInstanceDOs = labSectionsByTestlineInstanceId.get(testlineInstanceId);

            if (CollectionUtils.isEmpty(labSectionInstanceDOs)) {
                testlineInstance.setLabSections(Collections.emptyList());
                continue;
            }

            // 转换为LabSection领域对象
            List<LabSection> labSections = mapper.toDomains(labSectionInstanceDOs);
            testlineInstance.setLabSections(labSections);
        }

        log.debug("批量组装了{}个TestlineInstance的LabSection", testlineInstanceList.size());
    }

    @Override
    public boolean supportsBatchOptimization() {
        return true; // 支持批量优化
    }

    @Override
    public String getName() {
        return "LabSectionInstanceSubAssembler";
    }
} 
