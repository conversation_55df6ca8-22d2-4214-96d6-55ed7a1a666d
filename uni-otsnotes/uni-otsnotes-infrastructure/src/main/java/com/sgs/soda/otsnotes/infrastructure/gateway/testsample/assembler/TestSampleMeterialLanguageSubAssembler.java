package com.sgs.soda.otsnotes.infrastructure.gateway.testsample.assembler;

import com.sgs.soda.otsnotes.domain.testsample.model.TestSample;
import com.sgs.soda.otsnotes.domain.testsample.model.TestSampleMeterialLanguage;
import com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDO;
import com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDOExample;
import com.sgs.soda.otsnotes.infrastructure.database.testsample.mapper.generated.TestSampleMaterialLanguageDOMapper;
import com.sgs.soda.otsnotes.infrastructure.gateway.testsample.converter.TestSampleMaterialLanguageConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * TestSample物料语言子对象组装器
 */
@Component
@RequiredArgsConstructor
public class TestSampleMeterialLanguageSubAssembler implements TestSampleSubAssembler {

    private final TestSampleMaterialLanguageDOMapper materialLanguageMapper;
    private final TestSampleMaterialLanguageConverter materialLanguageConverter;

    @Override
    public void assemble(TestSample testSample, TestSampleAssembleContext context) {
        if (!context.getSubObjectOptions().isIncludeLanguage()) {
            return;
        }
        
        // 确保material已存在
        if (testSample.getMaterial() == null) {
            return;
        }

        String testSampleId = context.getTestSampleDO().getID();
        
        // 查询物料语言信息
        TestSampleMaterialLanguageDOExample example = new TestSampleMaterialLanguageDOExample();
        example.createCriteria()
                .andSampleIdEqualTo(testSampleId)
                .andStatusEqualTo(1); // 1表示启用
        example.setOrderByClause("LanguageId ASC, CreatedDate ASC");
        
        List<TestSampleMaterialLanguageDO> languageDOList = materialLanguageMapper.selectByExampleWithBLOBs(example);
        
        if (CollectionUtils.isEmpty(languageDOList)) {
            testSample.getMaterial().setLanguages(Collections.emptyList());
            return;
        }
        
        // 转换为领域对象
        List<TestSampleMeterialLanguage> languageList = languageDOList.stream()
                .map(materialLanguageConverter::toDomain)
                .collect(Collectors.toList());
        
        testSample.getMaterial().setLanguages(languageList);
        
        // 设置扩展字段
        if (context.getSubObjectOptions().isIncludeExtFields()) {
            testSample.setExtFields(new HashMap<>());
        }
    }

    @Override
    public void batchAssemble(List<TestSample> testSampleList, TestSampleBatchAssembleContext batchContext) {
        if (!batchContext.getSubObjectOptions().isIncludeLanguage()) {
            return;
        }
        
        // 过滤出有material的TestSample
        List<TestSample> samplesWithMaterial = testSampleList.stream()
                .filter(testSample -> testSample.getMaterial() != null)
                .collect(Collectors.toList());
        
        if (samplesWithMaterial.isEmpty()) {
            return;
        }
        
        // 提取这些TestSample的ID
        List<String> sampleIdsWithMaterial = samplesWithMaterial.stream()
                .map(testSample -> testSample.getId().getValue())
                .collect(Collectors.toList());

        // 批量查询所有TestSample的MaterialLanguage信息
        TestSampleMaterialLanguageDOExample example = new TestSampleMaterialLanguageDOExample();
        example.createCriteria()
                .andSampleIdIn(sampleIdsWithMaterial)
                .andStatusEqualTo(1); // 1表示启用
        example.setOrderByClause("SampleId ASC, LanguageId ASC, CreatedDate ASC");
        
        List<TestSampleMaterialLanguageDO> allLanguageDOList = materialLanguageMapper.selectByExampleWithBLOBs(example);
        
        if (CollectionUtils.isEmpty(allLanguageDOList)) {
            // 如果没有MaterialLanguage数据，设置空列表
            samplesWithMaterial.forEach(testSample -> testSample.getMaterial().setLanguages(Collections.emptyList()));
        } else {
            // 按TestSample ID分组
            Map<String, List<TestSampleMaterialLanguageDO>> languagesByTestSampleId = allLanguageDOList.stream()
                    .collect(Collectors.groupingBy(TestSampleMaterialLanguageDO::getSampleId));
            
            // 为每个TestSample设置对应的MaterialLanguage列表
            for (TestSample testSample : samplesWithMaterial) {
                String testSampleId = testSample.getId().getValue();
                List<TestSampleMaterialLanguageDO> languageDOList = languagesByTestSampleId.getOrDefault(testSampleId, Collections.emptyList());
                
                List<TestSampleMeterialLanguage> languageList = languageDOList.stream()
                        .map(materialLanguageConverter::toDomain)
                        .collect(Collectors.toList());
                
                testSample.getMaterial().setLanguages(languageList);
            }
        }
        
        // 设置扩展字段
        if (batchContext.getSubObjectOptions().isIncludeExtFields()) {
            testSampleList.forEach(testSample -> testSample.setExtFields(new HashMap<>()));
        }
    }

    @Override
    public boolean supportsBatchOptimization() {
        return true;
    }

    @Override
    public String getName() {
        return "TestSampleMeterialLanguageSubAssembler";
    }
}