package com.sgs.soda.otsnotes.infrastructure.database.testsample.mapper.generated;

import com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDO;
import com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TestSampleMaterialLanguageDOMapper {
    int countByExample(TestSampleMaterialLanguageDOExample example);

    int deleteByExample(TestSampleMaterialLanguageDOExample example);

    int deleteByPrimaryKey(String id);

    int insert(TestSampleMaterialLanguageDO record);

    int insertSelective(TestSampleMaterialLanguageDO record);

    List<TestSampleMaterialLanguageDO> selectByExampleWithBLOBs(TestSampleMaterialLanguageDOExample example);

    List<TestSampleMaterialLanguageDO> selectByExample(TestSampleMaterialLanguageDOExample example);

    TestSampleMaterialLanguageDO selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") TestSampleMaterialLanguageDO record, @Param("example") TestSampleMaterialLanguageDOExample example);

    int updateByExampleWithBLOBs(@Param("record") TestSampleMaterialLanguageDO record, @Param("example") TestSampleMaterialLanguageDOExample example);

    int updateByExample(@Param("record") TestSampleMaterialLanguageDO record, @Param("example") TestSampleMaterialLanguageDOExample example);

    int updateByPrimaryKeySelective(TestSampleMaterialLanguageDO record);

    int updateByPrimaryKeyWithBLOBs(TestSampleMaterialLanguageDO record);

    int updateByPrimaryKey(TestSampleMaterialLanguageDO record);

    int batchInsert(List<TestSampleMaterialLanguageDO> list);

    int batchUpdate(List<TestSampleMaterialLanguageDO> list);
}