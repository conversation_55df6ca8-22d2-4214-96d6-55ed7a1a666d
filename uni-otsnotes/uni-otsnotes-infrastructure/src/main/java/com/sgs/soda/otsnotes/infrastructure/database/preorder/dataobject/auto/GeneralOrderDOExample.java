package com.sgs.soda.otsnotes.infrastructure.database.preorder.dataobject.auto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GeneralOrderDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public GeneralOrderDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIDIsNull() {
            addCriterion("ID is null");
            return (Criteria) this;
        }

        public Criteria andIDIsNotNull() {
            addCriterion("ID is not null");
            return (Criteria) this;
        }

        public Criteria andIDEqualTo(String value) {
            addCriterion("ID =", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDNotEqualTo(String value) {
            addCriterion("ID <>", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDGreaterThan(String value) {
            addCriterion("ID >", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDGreaterThanOrEqualTo(String value) {
            addCriterion("ID >=", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDLessThan(String value) {
            addCriterion("ID <", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDLessThanOrEqualTo(String value) {
            addCriterion("ID <=", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDLike(String value) {
            addCriterion("ID like", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDNotLike(String value) {
            addCriterion("ID not like", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDIn(List<String> values) {
            addCriterion("ID in", values, "ID");
            return (Criteria) this;
        }

        public Criteria andIDNotIn(List<String> values) {
            addCriterion("ID not in", values, "ID");
            return (Criteria) this;
        }

        public Criteria andIDBetween(String value1, String value2) {
            addCriterion("ID between", value1, value2, "ID");
            return (Criteria) this;
        }

        public Criteria andIDNotBetween(String value1, String value2) {
            addCriterion("ID not between", value1, value2, "ID");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("OrderNo is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("OrderNo is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("OrderNo =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("OrderNo <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("OrderNo >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("OrderNo >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("OrderNo <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("OrderNo <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("OrderNo like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("OrderNo not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("OrderNo in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("OrderNo not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("OrderNo between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("OrderNo not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("OrderStatus is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("OrderStatus is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(Integer value) {
            addCriterion("OrderStatus =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(Integer value) {
            addCriterion("OrderStatus <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(Integer value) {
            addCriterion("OrderStatus >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("OrderStatus >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(Integer value) {
            addCriterion("OrderStatus <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(Integer value) {
            addCriterion("OrderStatus <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<Integer> values) {
            addCriterion("OrderStatus in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<Integer> values) {
            addCriterion("OrderStatus not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(Integer value1, Integer value2) {
            addCriterion("OrderStatus between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("OrderStatus not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNull() {
            addCriterion("CustomerCode is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNotNull() {
            addCriterion("CustomerCode is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeEqualTo(String value) {
            addCriterion("CustomerCode =", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotEqualTo(String value) {
            addCriterion("CustomerCode <>", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThan(String value) {
            addCriterion("CustomerCode >", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("CustomerCode >=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThan(String value) {
            addCriterion("CustomerCode <", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThanOrEqualTo(String value) {
            addCriterion("CustomerCode <=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLike(String value) {
            addCriterion("CustomerCode like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotLike(String value) {
            addCriterion("CustomerCode not like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIn(List<String> values) {
            addCriterion("CustomerCode in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotIn(List<String> values) {
            addCriterion("CustomerCode not in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBetween(String value1, String value2) {
            addCriterion("CustomerCode between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotBetween(String value1, String value2) {
            addCriterion("CustomerCode not between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("CustomerName is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("CustomerName is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("CustomerName =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("CustomerName <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("CustomerName >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("CustomerName >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("CustomerName <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("CustomerName <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("CustomerName like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("CustomerName not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("CustomerName in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("CustomerName not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("CustomerName between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("CustomerName not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeIsNull() {
            addCriterion("CustomerGroupCode is null");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeIsNotNull() {
            addCriterion("CustomerGroupCode is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeEqualTo(String value) {
            addCriterion("CustomerGroupCode =", value, "customerGroupCode");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeNotEqualTo(String value) {
            addCriterion("CustomerGroupCode <>", value, "customerGroupCode");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeGreaterThan(String value) {
            addCriterion("CustomerGroupCode >", value, "customerGroupCode");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeGreaterThanOrEqualTo(String value) {
            addCriterion("CustomerGroupCode >=", value, "customerGroupCode");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeLessThan(String value) {
            addCriterion("CustomerGroupCode <", value, "customerGroupCode");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeLessThanOrEqualTo(String value) {
            addCriterion("CustomerGroupCode <=", value, "customerGroupCode");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeLike(String value) {
            addCriterion("CustomerGroupCode like", value, "customerGroupCode");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeNotLike(String value) {
            addCriterion("CustomerGroupCode not like", value, "customerGroupCode");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeIn(List<String> values) {
            addCriterion("CustomerGroupCode in", values, "customerGroupCode");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeNotIn(List<String> values) {
            addCriterion("CustomerGroupCode not in", values, "customerGroupCode");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeBetween(String value1, String value2) {
            addCriterion("CustomerGroupCode between", value1, value2, "customerGroupCode");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupCodeNotBetween(String value1, String value2) {
            addCriterion("CustomerGroupCode not between", value1, value2, "customerGroupCode");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameIsNull() {
            addCriterion("CustomerGroupName is null");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameIsNotNull() {
            addCriterion("CustomerGroupName is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameEqualTo(String value) {
            addCriterion("CustomerGroupName =", value, "customerGroupName");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameNotEqualTo(String value) {
            addCriterion("CustomerGroupName <>", value, "customerGroupName");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameGreaterThan(String value) {
            addCriterion("CustomerGroupName >", value, "customerGroupName");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameGreaterThanOrEqualTo(String value) {
            addCriterion("CustomerGroupName >=", value, "customerGroupName");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameLessThan(String value) {
            addCriterion("CustomerGroupName <", value, "customerGroupName");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameLessThanOrEqualTo(String value) {
            addCriterion("CustomerGroupName <=", value, "customerGroupName");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameLike(String value) {
            addCriterion("CustomerGroupName like", value, "customerGroupName");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameNotLike(String value) {
            addCriterion("CustomerGroupName not like", value, "customerGroupName");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameIn(List<String> values) {
            addCriterion("CustomerGroupName in", values, "customerGroupName");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameNotIn(List<String> values) {
            addCriterion("CustomerGroupName not in", values, "customerGroupName");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameBetween(String value1, String value2) {
            addCriterion("CustomerGroupName between", value1, value2, "customerGroupName");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupNameNotBetween(String value1, String value2) {
            addCriterion("CustomerGroupName not between", value1, value2, "customerGroupName");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNull() {
            addCriterion("LabId is null");
            return (Criteria) this;
        }

        public Criteria andLabIdIsNotNull() {
            addCriterion("LabId is not null");
            return (Criteria) this;
        }

        public Criteria andLabIdEqualTo(Integer value) {
            addCriterion("LabId =", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotEqualTo(Integer value) {
            addCriterion("LabId <>", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThan(Integer value) {
            addCriterion("LabId >", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("LabId >=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThan(Integer value) {
            addCriterion("LabId <", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdLessThanOrEqualTo(Integer value) {
            addCriterion("LabId <=", value, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdIn(List<Integer> values) {
            addCriterion("LabId in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotIn(List<Integer> values) {
            addCriterion("LabId not in", values, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdBetween(Integer value1, Integer value2) {
            addCriterion("LabId between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andLabIdNotBetween(Integer value1, Integer value2) {
            addCriterion("LabId not between", value1, value2, "labId");
            return (Criteria) this;
        }

        public Criteria andLabCodeIsNull() {
            addCriterion("LabCode is null");
            return (Criteria) this;
        }

        public Criteria andLabCodeIsNotNull() {
            addCriterion("LabCode is not null");
            return (Criteria) this;
        }

        public Criteria andLabCodeEqualTo(String value) {
            addCriterion("LabCode =", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotEqualTo(String value) {
            addCriterion("LabCode <>", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeGreaterThan(String value) {
            addCriterion("LabCode >", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeGreaterThanOrEqualTo(String value) {
            addCriterion("LabCode >=", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLessThan(String value) {
            addCriterion("LabCode <", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLessThanOrEqualTo(String value) {
            addCriterion("LabCode <=", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLike(String value) {
            addCriterion("LabCode like", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotLike(String value) {
            addCriterion("LabCode not like", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeIn(List<String> values) {
            addCriterion("LabCode in", values, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotIn(List<String> values) {
            addCriterion("LabCode not in", values, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeBetween(String value1, String value2) {
            addCriterion("LabCode between", value1, value2, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotBetween(String value1, String value2) {
            addCriterion("LabCode not between", value1, value2, "labCode");
            return (Criteria) this;
        }

        public Criteria andOrderLaboratoryIDIsNull() {
            addCriterion("OrderLaboratoryID is null");
            return (Criteria) this;
        }

        public Criteria andOrderLaboratoryIDIsNotNull() {
            addCriterion("OrderLaboratoryID is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLaboratoryIDEqualTo(Integer value) {
            addCriterion("OrderLaboratoryID =", value, "orderLaboratoryID");
            return (Criteria) this;
        }

        public Criteria andOrderLaboratoryIDNotEqualTo(Integer value) {
            addCriterion("OrderLaboratoryID <>", value, "orderLaboratoryID");
            return (Criteria) this;
        }

        public Criteria andOrderLaboratoryIDGreaterThan(Integer value) {
            addCriterion("OrderLaboratoryID >", value, "orderLaboratoryID");
            return (Criteria) this;
        }

        public Criteria andOrderLaboratoryIDGreaterThanOrEqualTo(Integer value) {
            addCriterion("OrderLaboratoryID >=", value, "orderLaboratoryID");
            return (Criteria) this;
        }

        public Criteria andOrderLaboratoryIDLessThan(Integer value) {
            addCriterion("OrderLaboratoryID <", value, "orderLaboratoryID");
            return (Criteria) this;
        }

        public Criteria andOrderLaboratoryIDLessThanOrEqualTo(Integer value) {
            addCriterion("OrderLaboratoryID <=", value, "orderLaboratoryID");
            return (Criteria) this;
        }

        public Criteria andOrderLaboratoryIDIn(List<Integer> values) {
            addCriterion("OrderLaboratoryID in", values, "orderLaboratoryID");
            return (Criteria) this;
        }

        public Criteria andOrderLaboratoryIDNotIn(List<Integer> values) {
            addCriterion("OrderLaboratoryID not in", values, "orderLaboratoryID");
            return (Criteria) this;
        }

        public Criteria andOrderLaboratoryIDBetween(Integer value1, Integer value2) {
            addCriterion("OrderLaboratoryID between", value1, value2, "orderLaboratoryID");
            return (Criteria) this;
        }

        public Criteria andOrderLaboratoryIDNotBetween(Integer value1, Integer value2) {
            addCriterion("OrderLaboratoryID not between", value1, value2, "orderLaboratoryID");
            return (Criteria) this;
        }

        public Criteria andConfirmMatrixDateIsNull() {
            addCriterion("ConfirmMatrixDate is null");
            return (Criteria) this;
        }

        public Criteria andConfirmMatrixDateIsNotNull() {
            addCriterion("ConfirmMatrixDate is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmMatrixDateEqualTo(Date value) {
            addCriterion("ConfirmMatrixDate =", value, "confirmMatrixDate");
            return (Criteria) this;
        }

        public Criteria andConfirmMatrixDateNotEqualTo(Date value) {
            addCriterion("ConfirmMatrixDate <>", value, "confirmMatrixDate");
            return (Criteria) this;
        }

        public Criteria andConfirmMatrixDateGreaterThan(Date value) {
            addCriterion("ConfirmMatrixDate >", value, "confirmMatrixDate");
            return (Criteria) this;
        }

        public Criteria andConfirmMatrixDateGreaterThanOrEqualTo(Date value) {
            addCriterion("ConfirmMatrixDate >=", value, "confirmMatrixDate");
            return (Criteria) this;
        }

        public Criteria andConfirmMatrixDateLessThan(Date value) {
            addCriterion("ConfirmMatrixDate <", value, "confirmMatrixDate");
            return (Criteria) this;
        }

        public Criteria andConfirmMatrixDateLessThanOrEqualTo(Date value) {
            addCriterion("ConfirmMatrixDate <=", value, "confirmMatrixDate");
            return (Criteria) this;
        }

        public Criteria andConfirmMatrixDateIn(List<Date> values) {
            addCriterion("ConfirmMatrixDate in", values, "confirmMatrixDate");
            return (Criteria) this;
        }

        public Criteria andConfirmMatrixDateNotIn(List<Date> values) {
            addCriterion("ConfirmMatrixDate not in", values, "confirmMatrixDate");
            return (Criteria) this;
        }

        public Criteria andConfirmMatrixDateBetween(Date value1, Date value2) {
            addCriterion("ConfirmMatrixDate between", value1, value2, "confirmMatrixDate");
            return (Criteria) this;
        }

        public Criteria andConfirmMatrixDateNotBetween(Date value1, Date value2) {
            addCriterion("ConfirmMatrixDate not between", value1, value2, "confirmMatrixDate");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnIsNull() {
            addCriterion("CustomerNameCn is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnIsNotNull() {
            addCriterion("CustomerNameCn is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnEqualTo(String value) {
            addCriterion("CustomerNameCn =", value, "customerNameCn");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnNotEqualTo(String value) {
            addCriterion("CustomerNameCn <>", value, "customerNameCn");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnGreaterThan(String value) {
            addCriterion("CustomerNameCn >", value, "customerNameCn");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnGreaterThanOrEqualTo(String value) {
            addCriterion("CustomerNameCn >=", value, "customerNameCn");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnLessThan(String value) {
            addCriterion("CustomerNameCn <", value, "customerNameCn");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnLessThanOrEqualTo(String value) {
            addCriterion("CustomerNameCn <=", value, "customerNameCn");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnLike(String value) {
            addCriterion("CustomerNameCn like", value, "customerNameCn");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnNotLike(String value) {
            addCriterion("CustomerNameCn not like", value, "customerNameCn");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnIn(List<String> values) {
            addCriterion("CustomerNameCn in", values, "customerNameCn");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnNotIn(List<String> values) {
            addCriterion("CustomerNameCn not in", values, "customerNameCn");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnBetween(String value1, String value2) {
            addCriterion("CustomerNameCn between", value1, value2, "customerNameCn");
            return (Criteria) this;
        }

        public Criteria andCustomerNameCnNotBetween(String value1, String value2) {
            addCriterion("CustomerNameCn not between", value1, value2, "customerNameCn");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("ActiveIndicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("ActiveIndicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Boolean value) {
            addCriterion("ActiveIndicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Boolean value) {
            addCriterion("ActiveIndicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Boolean value) {
            addCriterion("ActiveIndicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Boolean value) {
            addCriterion("ActiveIndicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Boolean value) {
            addCriterion("ActiveIndicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Boolean value) {
            addCriterion("ActiveIndicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Boolean> values) {
            addCriterion("ActiveIndicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Boolean> values) {
            addCriterion("ActiveIndicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Boolean value1, Boolean value2) {
            addCriterion("ActiveIndicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Boolean value1, Boolean value2) {
            addCriterion("ActiveIndicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("CreatedDate is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("CreatedDate is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("CreatedDate =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("CreatedDate <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("CreatedDate >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("CreatedDate >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("CreatedDate <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("CreatedDate <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("CreatedDate in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("CreatedDate not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("CreatedDate between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("CreatedDate not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("CreatedBy is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("CreatedBy is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("CreatedBy =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("CreatedBy <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("CreatedBy >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("CreatedBy >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("CreatedBy <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("CreatedBy <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("CreatedBy like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("CreatedBy not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("CreatedBy in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("CreatedBy not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("CreatedBy between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("CreatedBy not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("ModifiedDate is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("ModifiedDate is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("ModifiedDate =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("ModifiedDate <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("ModifiedDate >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("ModifiedDate <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("ModifiedDate in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("ModifiedDate not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("ModifiedBy is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("ModifiedBy is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("ModifiedBy =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("ModifiedBy <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("ModifiedBy >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("ModifiedBy >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("ModifiedBy <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("ModifiedBy <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("ModifiedBy like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("ModifiedBy not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("ModifiedBy in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("ModifiedBy not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("ModifiedBy between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("ModifiedBy not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNull() {
            addCriterion("LastModifiedTimestamp is null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNotNull() {
            addCriterion("LastModifiedTimestamp is not null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampEqualTo(Date value) {
            addCriterion("LastModifiedTimestamp =", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotEqualTo(Date value) {
            addCriterion("LastModifiedTimestamp <>", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThan(Date value) {
            addCriterion("LastModifiedTimestamp >", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThanOrEqualTo(Date value) {
            addCriterion("LastModifiedTimestamp >=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThan(Date value) {
            addCriterion("LastModifiedTimestamp <", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThanOrEqualTo(Date value) {
            addCriterion("LastModifiedTimestamp <=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIn(List<Date> values) {
            addCriterion("LastModifiedTimestamp in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotIn(List<Date> values) {
            addCriterion("LastModifiedTimestamp not in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampBetween(Date value1, Date value2) {
            addCriterion("LastModifiedTimestamp between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotBetween(Date value1, Date value2) {
            addCriterion("LastModifiedTimestamp not between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andConclusionModeIsNull() {
            addCriterion("ConclusionMode is null");
            return (Criteria) this;
        }

        public Criteria andConclusionModeIsNotNull() {
            addCriterion("ConclusionMode is not null");
            return (Criteria) this;
        }

        public Criteria andConclusionModeEqualTo(Integer value) {
            addCriterion("ConclusionMode =", value, "conclusionMode");
            return (Criteria) this;
        }

        public Criteria andConclusionModeNotEqualTo(Integer value) {
            addCriterion("ConclusionMode <>", value, "conclusionMode");
            return (Criteria) this;
        }

        public Criteria andConclusionModeGreaterThan(Integer value) {
            addCriterion("ConclusionMode >", value, "conclusionMode");
            return (Criteria) this;
        }

        public Criteria andConclusionModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("ConclusionMode >=", value, "conclusionMode");
            return (Criteria) this;
        }

        public Criteria andConclusionModeLessThan(Integer value) {
            addCriterion("ConclusionMode <", value, "conclusionMode");
            return (Criteria) this;
        }

        public Criteria andConclusionModeLessThanOrEqualTo(Integer value) {
            addCriterion("ConclusionMode <=", value, "conclusionMode");
            return (Criteria) this;
        }

        public Criteria andConclusionModeIn(List<Integer> values) {
            addCriterion("ConclusionMode in", values, "conclusionMode");
            return (Criteria) this;
        }

        public Criteria andConclusionModeNotIn(List<Integer> values) {
            addCriterion("ConclusionMode not in", values, "conclusionMode");
            return (Criteria) this;
        }

        public Criteria andConclusionModeBetween(Integer value1, Integer value2) {
            addCriterion("ConclusionMode between", value1, value2, "conclusionMode");
            return (Criteria) this;
        }

        public Criteria andConclusionModeNotBetween(Integer value1, Integer value2) {
            addCriterion("ConclusionMode not between", value1, value2, "conclusionMode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeIsNull() {
            addCriterion("ApplicantCustomerCode is null");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeIsNotNull() {
            addCriterion("ApplicantCustomerCode is not null");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeEqualTo(String value) {
            addCriterion("ApplicantCustomerCode =", value, "applicantCustomerCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeNotEqualTo(String value) {
            addCriterion("ApplicantCustomerCode <>", value, "applicantCustomerCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeGreaterThan(String value) {
            addCriterion("ApplicantCustomerCode >", value, "applicantCustomerCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ApplicantCustomerCode >=", value, "applicantCustomerCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeLessThan(String value) {
            addCriterion("ApplicantCustomerCode <", value, "applicantCustomerCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeLessThanOrEqualTo(String value) {
            addCriterion("ApplicantCustomerCode <=", value, "applicantCustomerCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeLike(String value) {
            addCriterion("ApplicantCustomerCode like", value, "applicantCustomerCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeNotLike(String value) {
            addCriterion("ApplicantCustomerCode not like", value, "applicantCustomerCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeIn(List<String> values) {
            addCriterion("ApplicantCustomerCode in", values, "applicantCustomerCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeNotIn(List<String> values) {
            addCriterion("ApplicantCustomerCode not in", values, "applicantCustomerCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeBetween(String value1, String value2) {
            addCriterion("ApplicantCustomerCode between", value1, value2, "applicantCustomerCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerCodeNotBetween(String value1, String value2) {
            addCriterion("ApplicantCustomerCode not between", value1, value2, "applicantCustomerCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnIsNull() {
            addCriterion("ApplicantCustomerNameEn is null");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnIsNotNull() {
            addCriterion("ApplicantCustomerNameEn is not null");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnEqualTo(String value) {
            addCriterion("ApplicantCustomerNameEn =", value, "applicantCustomerNameEn");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnNotEqualTo(String value) {
            addCriterion("ApplicantCustomerNameEn <>", value, "applicantCustomerNameEn");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnGreaterThan(String value) {
            addCriterion("ApplicantCustomerNameEn >", value, "applicantCustomerNameEn");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnGreaterThanOrEqualTo(String value) {
            addCriterion("ApplicantCustomerNameEn >=", value, "applicantCustomerNameEn");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnLessThan(String value) {
            addCriterion("ApplicantCustomerNameEn <", value, "applicantCustomerNameEn");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnLessThanOrEqualTo(String value) {
            addCriterion("ApplicantCustomerNameEn <=", value, "applicantCustomerNameEn");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnLike(String value) {
            addCriterion("ApplicantCustomerNameEn like", value, "applicantCustomerNameEn");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnNotLike(String value) {
            addCriterion("ApplicantCustomerNameEn not like", value, "applicantCustomerNameEn");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnIn(List<String> values) {
            addCriterion("ApplicantCustomerNameEn in", values, "applicantCustomerNameEn");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnNotIn(List<String> values) {
            addCriterion("ApplicantCustomerNameEn not in", values, "applicantCustomerNameEn");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnBetween(String value1, String value2) {
            addCriterion("ApplicantCustomerNameEn between", value1, value2, "applicantCustomerNameEn");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameEnNotBetween(String value1, String value2) {
            addCriterion("ApplicantCustomerNameEn not between", value1, value2, "applicantCustomerNameEn");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNIsNull() {
            addCriterion("ApplicantCustomerNameCN is null");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNIsNotNull() {
            addCriterion("ApplicantCustomerNameCN is not null");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNEqualTo(String value) {
            addCriterion("ApplicantCustomerNameCN =", value, "applicantCustomerNameCN");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNNotEqualTo(String value) {
            addCriterion("ApplicantCustomerNameCN <>", value, "applicantCustomerNameCN");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNGreaterThan(String value) {
            addCriterion("ApplicantCustomerNameCN >", value, "applicantCustomerNameCN");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNGreaterThanOrEqualTo(String value) {
            addCriterion("ApplicantCustomerNameCN >=", value, "applicantCustomerNameCN");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNLessThan(String value) {
            addCriterion("ApplicantCustomerNameCN <", value, "applicantCustomerNameCN");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNLessThanOrEqualTo(String value) {
            addCriterion("ApplicantCustomerNameCN <=", value, "applicantCustomerNameCN");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNLike(String value) {
            addCriterion("ApplicantCustomerNameCN like", value, "applicantCustomerNameCN");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNNotLike(String value) {
            addCriterion("ApplicantCustomerNameCN not like", value, "applicantCustomerNameCN");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNIn(List<String> values) {
            addCriterion("ApplicantCustomerNameCN in", values, "applicantCustomerNameCN");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNNotIn(List<String> values) {
            addCriterion("ApplicantCustomerNameCN not in", values, "applicantCustomerNameCN");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNBetween(String value1, String value2) {
            addCriterion("ApplicantCustomerNameCN between", value1, value2, "applicantCustomerNameCN");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerNameCNNotBetween(String value1, String value2) {
            addCriterion("ApplicantCustomerNameCN not between", value1, value2, "applicantCustomerNameCN");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeIsNull() {
            addCriterion("ApplicantCustomerGroupCode is null");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeIsNotNull() {
            addCriterion("ApplicantCustomerGroupCode is not null");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeEqualTo(String value) {
            addCriterion("ApplicantCustomerGroupCode =", value, "applicantCustomerGroupCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeNotEqualTo(String value) {
            addCriterion("ApplicantCustomerGroupCode <>", value, "applicantCustomerGroupCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeGreaterThan(String value) {
            addCriterion("ApplicantCustomerGroupCode >", value, "applicantCustomerGroupCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ApplicantCustomerGroupCode >=", value, "applicantCustomerGroupCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeLessThan(String value) {
            addCriterion("ApplicantCustomerGroupCode <", value, "applicantCustomerGroupCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeLessThanOrEqualTo(String value) {
            addCriterion("ApplicantCustomerGroupCode <=", value, "applicantCustomerGroupCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeLike(String value) {
            addCriterion("ApplicantCustomerGroupCode like", value, "applicantCustomerGroupCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeNotLike(String value) {
            addCriterion("ApplicantCustomerGroupCode not like", value, "applicantCustomerGroupCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeIn(List<String> values) {
            addCriterion("ApplicantCustomerGroupCode in", values, "applicantCustomerGroupCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeNotIn(List<String> values) {
            addCriterion("ApplicantCustomerGroupCode not in", values, "applicantCustomerGroupCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeBetween(String value1, String value2) {
            addCriterion("ApplicantCustomerGroupCode between", value1, value2, "applicantCustomerGroupCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupCodeNotBetween(String value1, String value2) {
            addCriterion("ApplicantCustomerGroupCode not between", value1, value2, "applicantCustomerGroupCode");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameIsNull() {
            addCriterion("ApplicantCustomerGroupName is null");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameIsNotNull() {
            addCriterion("ApplicantCustomerGroupName is not null");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameEqualTo(String value) {
            addCriterion("ApplicantCustomerGroupName =", value, "applicantCustomerGroupName");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameNotEqualTo(String value) {
            addCriterion("ApplicantCustomerGroupName <>", value, "applicantCustomerGroupName");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameGreaterThan(String value) {
            addCriterion("ApplicantCustomerGroupName >", value, "applicantCustomerGroupName");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameGreaterThanOrEqualTo(String value) {
            addCriterion("ApplicantCustomerGroupName >=", value, "applicantCustomerGroupName");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameLessThan(String value) {
            addCriterion("ApplicantCustomerGroupName <", value, "applicantCustomerGroupName");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameLessThanOrEqualTo(String value) {
            addCriterion("ApplicantCustomerGroupName <=", value, "applicantCustomerGroupName");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameLike(String value) {
            addCriterion("ApplicantCustomerGroupName like", value, "applicantCustomerGroupName");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameNotLike(String value) {
            addCriterion("ApplicantCustomerGroupName not like", value, "applicantCustomerGroupName");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameIn(List<String> values) {
            addCriterion("ApplicantCustomerGroupName in", values, "applicantCustomerGroupName");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameNotIn(List<String> values) {
            addCriterion("ApplicantCustomerGroupName not in", values, "applicantCustomerGroupName");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameBetween(String value1, String value2) {
            addCriterion("ApplicantCustomerGroupName between", value1, value2, "applicantCustomerGroupName");
            return (Criteria) this;
        }

        public Criteria andApplicantCustomerGroupNameNotBetween(String value1, String value2) {
            addCriterion("ApplicantCustomerGroupName not between", value1, value2, "applicantCustomerGroupName");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeIsNull() {
            addCriterion("ResponsibleTeamCode is null");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeIsNotNull() {
            addCriterion("ResponsibleTeamCode is not null");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeEqualTo(String value) {
            addCriterion("ResponsibleTeamCode =", value, "responsibleTeamCode");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeNotEqualTo(String value) {
            addCriterion("ResponsibleTeamCode <>", value, "responsibleTeamCode");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeGreaterThan(String value) {
            addCriterion("ResponsibleTeamCode >", value, "responsibleTeamCode");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ResponsibleTeamCode >=", value, "responsibleTeamCode");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeLessThan(String value) {
            addCriterion("ResponsibleTeamCode <", value, "responsibleTeamCode");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeLessThanOrEqualTo(String value) {
            addCriterion("ResponsibleTeamCode <=", value, "responsibleTeamCode");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeLike(String value) {
            addCriterion("ResponsibleTeamCode like", value, "responsibleTeamCode");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeNotLike(String value) {
            addCriterion("ResponsibleTeamCode not like", value, "responsibleTeamCode");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeIn(List<String> values) {
            addCriterion("ResponsibleTeamCode in", values, "responsibleTeamCode");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeNotIn(List<String> values) {
            addCriterion("ResponsibleTeamCode not in", values, "responsibleTeamCode");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeBetween(String value1, String value2) {
            addCriterion("ResponsibleTeamCode between", value1, value2, "responsibleTeamCode");
            return (Criteria) this;
        }

        public Criteria andResponsibleTeamCodeNotBetween(String value1, String value2) {
            addCriterion("ResponsibleTeamCode not between", value1, value2, "responsibleTeamCode");
            return (Criteria) this;
        }

        public Criteria andCSNameIsNull() {
            addCriterion("CSName is null");
            return (Criteria) this;
        }

        public Criteria andCSNameIsNotNull() {
            addCriterion("CSName is not null");
            return (Criteria) this;
        }

        public Criteria andCSNameEqualTo(String value) {
            addCriterion("CSName =", value, "CSName");
            return (Criteria) this;
        }

        public Criteria andCSNameNotEqualTo(String value) {
            addCriterion("CSName <>", value, "CSName");
            return (Criteria) this;
        }

        public Criteria andCSNameGreaterThan(String value) {
            addCriterion("CSName >", value, "CSName");
            return (Criteria) this;
        }

        public Criteria andCSNameGreaterThanOrEqualTo(String value) {
            addCriterion("CSName >=", value, "CSName");
            return (Criteria) this;
        }

        public Criteria andCSNameLessThan(String value) {
            addCriterion("CSName <", value, "CSName");
            return (Criteria) this;
        }

        public Criteria andCSNameLessThanOrEqualTo(String value) {
            addCriterion("CSName <=", value, "CSName");
            return (Criteria) this;
        }

        public Criteria andCSNameLike(String value) {
            addCriterion("CSName like", value, "CSName");
            return (Criteria) this;
        }

        public Criteria andCSNameNotLike(String value) {
            addCriterion("CSName not like", value, "CSName");
            return (Criteria) this;
        }

        public Criteria andCSNameIn(List<String> values) {
            addCriterion("CSName in", values, "CSName");
            return (Criteria) this;
        }

        public Criteria andCSNameNotIn(List<String> values) {
            addCriterion("CSName not in", values, "CSName");
            return (Criteria) this;
        }

        public Criteria andCSNameBetween(String value1, String value2) {
            addCriterion("CSName between", value1, value2, "CSName");
            return (Criteria) this;
        }

        public Criteria andCSNameNotBetween(String value1, String value2) {
            addCriterion("CSName not between", value1, value2, "CSName");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionIsNull() {
            addCriterion("SampleDescription is null");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionIsNotNull() {
            addCriterion("SampleDescription is not null");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionEqualTo(String value) {
            addCriterion("SampleDescription =", value, "sampleDescription");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionNotEqualTo(String value) {
            addCriterion("SampleDescription <>", value, "sampleDescription");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionGreaterThan(String value) {
            addCriterion("SampleDescription >", value, "sampleDescription");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("SampleDescription >=", value, "sampleDescription");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionLessThan(String value) {
            addCriterion("SampleDescription <", value, "sampleDescription");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionLessThanOrEqualTo(String value) {
            addCriterion("SampleDescription <=", value, "sampleDescription");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionLike(String value) {
            addCriterion("SampleDescription like", value, "sampleDescription");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionNotLike(String value) {
            addCriterion("SampleDescription not like", value, "sampleDescription");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionIn(List<String> values) {
            addCriterion("SampleDescription in", values, "sampleDescription");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionNotIn(List<String> values) {
            addCriterion("SampleDescription not in", values, "sampleDescription");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionBetween(String value1, String value2) {
            addCriterion("SampleDescription between", value1, value2, "sampleDescription");
            return (Criteria) this;
        }

        public Criteria andSampleDescriptionNotBetween(String value1, String value2) {
            addCriterion("SampleDescription not between", value1, value2, "sampleDescription");
            return (Criteria) this;
        }

        public Criteria andReturnSampleIsNull() {
            addCriterion("ReturnSample is null");
            return (Criteria) this;
        }

        public Criteria andReturnSampleIsNotNull() {
            addCriterion("ReturnSample is not null");
            return (Criteria) this;
        }

        public Criteria andReturnSampleEqualTo(Short value) {
            addCriterion("ReturnSample =", value, "returnSample");
            return (Criteria) this;
        }

        public Criteria andReturnSampleNotEqualTo(Short value) {
            addCriterion("ReturnSample <>", value, "returnSample");
            return (Criteria) this;
        }

        public Criteria andReturnSampleGreaterThan(Short value) {
            addCriterion("ReturnSample >", value, "returnSample");
            return (Criteria) this;
        }

        public Criteria andReturnSampleGreaterThanOrEqualTo(Short value) {
            addCriterion("ReturnSample >=", value, "returnSample");
            return (Criteria) this;
        }

        public Criteria andReturnSampleLessThan(Short value) {
            addCriterion("ReturnSample <", value, "returnSample");
            return (Criteria) this;
        }

        public Criteria andReturnSampleLessThanOrEqualTo(Short value) {
            addCriterion("ReturnSample <=", value, "returnSample");
            return (Criteria) this;
        }

        public Criteria andReturnSampleIn(List<Short> values) {
            addCriterion("ReturnSample in", values, "returnSample");
            return (Criteria) this;
        }

        public Criteria andReturnSampleNotIn(List<Short> values) {
            addCriterion("ReturnSample not in", values, "returnSample");
            return (Criteria) this;
        }

        public Criteria andReturnSampleBetween(Short value1, Short value2) {
            addCriterion("ReturnSample between", value1, value2, "returnSample");
            return (Criteria) this;
        }

        public Criteria andReturnSampleNotBetween(Short value1, Short value2) {
            addCriterion("ReturnSample not between", value1, value2, "returnSample");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterIsNull() {
            addCriterion("TechnicalSupporter is null");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterIsNotNull() {
            addCriterion("TechnicalSupporter is not null");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterEqualTo(String value) {
            addCriterion("TechnicalSupporter =", value, "technicalSupporter");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterNotEqualTo(String value) {
            addCriterion("TechnicalSupporter <>", value, "technicalSupporter");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterGreaterThan(String value) {
            addCriterion("TechnicalSupporter >", value, "technicalSupporter");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterGreaterThanOrEqualTo(String value) {
            addCriterion("TechnicalSupporter >=", value, "technicalSupporter");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterLessThan(String value) {
            addCriterion("TechnicalSupporter <", value, "technicalSupporter");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterLessThanOrEqualTo(String value) {
            addCriterion("TechnicalSupporter <=", value, "technicalSupporter");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterLike(String value) {
            addCriterion("TechnicalSupporter like", value, "technicalSupporter");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterNotLike(String value) {
            addCriterion("TechnicalSupporter not like", value, "technicalSupporter");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterIn(List<String> values) {
            addCriterion("TechnicalSupporter in", values, "technicalSupporter");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterNotIn(List<String> values) {
            addCriterion("TechnicalSupporter not in", values, "technicalSupporter");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterBetween(String value1, String value2) {
            addCriterion("TechnicalSupporter between", value1, value2, "technicalSupporter");
            return (Criteria) this;
        }

        public Criteria andTechnicalSupporterNotBetween(String value1, String value2) {
            addCriterion("TechnicalSupporter not between", value1, value2, "technicalSupporter");
            return (Criteria) this;
        }

        public Criteria andSuffixNumIsNull() {
            addCriterion("SuffixNum is null");
            return (Criteria) this;
        }

        public Criteria andSuffixNumIsNotNull() {
            addCriterion("SuffixNum is not null");
            return (Criteria) this;
        }

        public Criteria andSuffixNumEqualTo(String value) {
            addCriterion("SuffixNum =", value, "suffixNum");
            return (Criteria) this;
        }

        public Criteria andSuffixNumNotEqualTo(String value) {
            addCriterion("SuffixNum <>", value, "suffixNum");
            return (Criteria) this;
        }

        public Criteria andSuffixNumGreaterThan(String value) {
            addCriterion("SuffixNum >", value, "suffixNum");
            return (Criteria) this;
        }

        public Criteria andSuffixNumGreaterThanOrEqualTo(String value) {
            addCriterion("SuffixNum >=", value, "suffixNum");
            return (Criteria) this;
        }

        public Criteria andSuffixNumLessThan(String value) {
            addCriterion("SuffixNum <", value, "suffixNum");
            return (Criteria) this;
        }

        public Criteria andSuffixNumLessThanOrEqualTo(String value) {
            addCriterion("SuffixNum <=", value, "suffixNum");
            return (Criteria) this;
        }

        public Criteria andSuffixNumLike(String value) {
            addCriterion("SuffixNum like", value, "suffixNum");
            return (Criteria) this;
        }

        public Criteria andSuffixNumNotLike(String value) {
            addCriterion("SuffixNum not like", value, "suffixNum");
            return (Criteria) this;
        }

        public Criteria andSuffixNumIn(List<String> values) {
            addCriterion("SuffixNum in", values, "suffixNum");
            return (Criteria) this;
        }

        public Criteria andSuffixNumNotIn(List<String> values) {
            addCriterion("SuffixNum not in", values, "suffixNum");
            return (Criteria) this;
        }

        public Criteria andSuffixNumBetween(String value1, String value2) {
            addCriterion("SuffixNum between", value1, value2, "suffixNum");
            return (Criteria) this;
        }

        public Criteria andSuffixNumNotBetween(String value1, String value2) {
            addCriterion("SuffixNum not between", value1, value2, "suffixNum");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}