package com.sgs.soda.otsnotes.infrastructure.gateway.testsample.assembler;

import com.sgs.soda.otsnotes.domain.testsample.model.TestSample;
import com.sgs.soda.otsnotes.domain.testsample.model.TestSampleGroup;
import com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleGroupDO;
import com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleGroupDOExample;
import com.sgs.soda.otsnotes.infrastructure.database.testsample.mapper.generated.TestSampleGroupDOMapper;
import com.sgs.soda.otsnotes.infrastructure.gateway.testsample.converter.TestSampleGroupConverter;
import lombok.RequiredArgsConstructor;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * TestSample分组子对象组装器
 */
@Component
@RequiredArgsConstructor
public class TestSampleGroupSubAssembler implements TestSampleSubAssembler {

    private final TestSampleGroupDOMapper testSampleGroupMapper;
    private final TestSampleGroupConverter testSampleGroupConverter;

    @Override
    public void assemble(TestSample testSample, TestSampleAssembleContext context) {
        if (!context.getSubObjectOptions().isIncludeGroupItems()) {
            return;
        }

        String testSampleId = context.getTestSampleDO().getID();
        
        // 查询分组信息
        TestSampleGroupDOExample example = new TestSampleGroupDOExample();
        example.createCriteria()
                .andSampleIDEqualTo(testSampleId)
                .andActiveIndicatorEqualTo(true);
        example.setOrderByClause("Sequence ASC, CreatedDate ASC");
        
        List<TestSampleGroupDO> groupDOList = testSampleGroupMapper.selectByExample(example);
        
        if (CollectionUtils.isEmpty(groupDOList)) {
            testSample.setGroupItems(Collections.emptyList());
            return;
        }
        
        // 转换为领域对象
        List<TestSampleGroup> groupList = groupDOList.stream()
                .map(testSampleGroupConverter::toDomain)
                .collect(Collectors.toList());
        
        testSample.setGroupItems(groupList);
    }

    @Override
    public void batchAssemble(List<TestSample> testSampleList, TestSampleBatchAssembleContext batchContext) {
        if (!batchContext.getSubObjectOptions().isIncludeGroupItems()) {
            return;
        }

        // 批量查询所有TestSample的Group信息
        TestSampleGroupDOExample example = new TestSampleGroupDOExample();
        example.createCriteria()
                .andSampleIDIn(new ArrayList<>(batchContext.getTestSampleIds()))
                .andActiveIndicatorEqualTo(true);
        example.setOrderByClause("SampleID ASC, Sequence ASC, CreatedDate ASC");
        
        List<TestSampleGroupDO> allGroupDOList = testSampleGroupMapper.selectByExample(example);
        
        if (CollectionUtils.isEmpty(allGroupDOList)) {
            // 如果没有Group数据，设置空列表
            testSampleList.forEach(testSample -> testSample.setGroupItems(Collections.emptyList()));
            return;
        }
        
        // 按TestSample ID分组
        Map<String, List<TestSampleGroupDO>> groupsByTestSampleId = allGroupDOList.stream()
                .collect(Collectors.groupingBy(TestSampleGroupDO::getSampleID));
        
        // 为每个TestSample设置对应的Group列表
        for (TestSample testSample : testSampleList) {
            String testSampleId = testSample.getId().getValue();
            List<TestSampleGroupDO> groupDOList = groupsByTestSampleId.getOrDefault(testSampleId, Collections.emptyList());
            
            List<TestSampleGroup> groupList = groupDOList.stream()
                    .map(testSampleGroupConverter::toDomain)
                    .collect(Collectors.toList());
            
            testSample.setGroupItems(groupList);
        }
    }

    @Override
    public boolean supportsBatchOptimization() {
        return true;
    }

    @Override
    public String getName() {
        return "TestSampleGroupSubAssembler";
    }
}