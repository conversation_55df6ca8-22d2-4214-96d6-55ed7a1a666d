package com.sgs.soda.otsnotes.infrastructure.database.testsample.mapper.generated;

import com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleGroupDO;
import com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleGroupDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TestSampleGroupDOMapper {
    int countByExample(TestSampleGroupDOExample example);

    int deleteByExample(TestSampleGroupDOExample example);

    int deleteByPrimaryKey(String ID);

    int insert(TestSampleGroupDO record);

    int insertSelective(TestSampleGroupDO record);

    List<TestSampleGroupDO> selectByExample(TestSampleGroupDOExample example);

    TestSampleGroupDO selectByPrimaryKey(String ID);

    int updateByExampleSelective(@Param("record") TestSampleGroupDO record, @Param("example") TestSampleGroupDOExample example);

    int updateByExample(@Param("record") TestSampleGroupDO record, @Param("example") TestSampleGroupDOExample example);

    int updateByPrimaryKeySelective(TestSampleGroupDO record);

    int updateByPrimaryKey(TestSampleGroupDO record);

    int batchInsert(List<TestSampleGroupDO> list);

    int batchUpdate(List<TestSampleGroupDO> list);
}