package com.sgs.soda.otsnotes.infrastructure.gateway.testsample.assembler;

import com.sgs.soda.otsnotes.domain.testsample.model.TestSample;

import java.util.List;

/**
 * TestSample子对象组装器接口
 * 符合单一职责原则，每个实现类只负责组装一种子对象
 */
public interface TestSampleSubAssembler {

    /**
     * 组装子对象到TestSample（单个对象模式）
     *
     * @param testSample 目标TestSample对象
     * @param context    子对象配置
     */
    void assemble(TestSample testSample, TestSampleAssembleContext context);

    /**
     * 批量组装子对象到TestSample列表（批量模式，用于性能优化）
     * 默认实现：逐个调用单个组装方法
     *
     * @param testSampleList 目标TestSample对象列表
     * @param batchContext   批量组装上下文
     */
    default void batchAssemble(List<TestSample> testSampleList, TestSampleBatchAssembleContext batchContext) {
        // 默认实现：逐个调用单个组装方法（保持向后兼容）
        for (TestSample testSample : testSampleList) {
            String testSampleId = testSample.getId().getValue();
            TestSampleAssembleContext singleContext = TestSampleAssembleContext.create(
                    batchContext.getSubObjectOptions(),
                    batchContext.getTestSampleDOById(testSampleId)
            );
            assemble(testSample, singleContext);
        }
    }

    /**
     * 获取组装器名称（用于日志和调试）
     */
    String getName();

    /**
     * 是否支持批量优化
     * 返回true表示该组装器重写了batchAssemble方法，提供了批量优化实现
     */
    default boolean supportsBatchOptimization() {
        return false;
    }
}