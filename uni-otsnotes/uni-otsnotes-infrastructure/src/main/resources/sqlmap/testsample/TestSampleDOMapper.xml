<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.soda.otsnotes.infrastructure.database.testsample.mapper.generated.TestSampleDOMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleDO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="SampleParentID" property="sampleParentID" jdbcType="VARCHAR" />
    <result column="SampleNo" property="sampleNo" jdbcType="VARCHAR" />
    <result column="SampleType" property="sampleType" jdbcType="INTEGER" />
    <result column="GroupType" property="groupType" jdbcType="VARCHAR" />
    <result column="SampleSeq" property="sampleSeq" jdbcType="INTEGER" />
    <result column="OrderNo" property="orderNo" jdbcType="VARCHAR" />
    <result column="Category" property="category" jdbcType="VARCHAR" />
    <result column="Description" property="description" jdbcType="VARCHAR" />
    <result column="Composition" property="composition" jdbcType="VARCHAR" />
    <result column="Color" property="color" jdbcType="VARCHAR" />
    <result column="SampleDescforReport" property="sampleDescforReport" jdbcType="VARCHAR" />
    <result column="SampleRemark" property="sampleRemark" jdbcType="VARCHAR" />
    <result column="EndUse" property="endUse" jdbcType="VARCHAR" />
    <result column="Material" property="material" jdbcType="VARCHAR" />
    <result column="Applicable" property="applicable" jdbcType="BIT" />
    <result column="ReferDataType" property="referDataType" jdbcType="VARCHAR" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="LastModifiedTimestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
    <result column="Version" property="version" jdbcType="INTEGER" />
    <result column="SampleSort" property="sampleSort" jdbcType="INTEGER" />
    <result column="NoOfSample" property="noOfSample" jdbcType="INTEGER" />
    <result column="ExternalSampleId" property="externalSampleId" jdbcType="VARCHAR" />
    <result column="SourceType" property="sourceType" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleDO" extends="BaseResultMap" >
    <result column="OtherSampleInfo" property="otherSampleInfo" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, SampleParentID, SampleNo, SampleType, GroupType, SampleSeq, OrderNo, Category, 
    Description, Composition, Color, SampleDescforReport, SampleRemark, EndUse, Material, 
    Applicable, ReferDataType, ActiveIndicator, CreatedDate, CreatedBy, ModifiedDate, 
    ModifiedBy, LastModifiedTimestamp, Version, SampleSort, NoOfSample, ExternalSampleId, 
    SourceType
  </sql>
  <sql id="Blob_Column_List" >
    OtherSampleInfo
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleDOExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_test_sample
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleDOExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_sample
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_test_sample
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_test_sample
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleDOExample" >
    delete from tb_test_sample
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleDO" >
    insert into tb_test_sample (ID, SampleParentID, SampleNo, 
      SampleType, GroupType, SampleSeq, 
      OrderNo, Category, Description, 
      Composition, Color, SampleDescforReport, 
      SampleRemark, EndUse, Material, 
      Applicable, ReferDataType, ActiveIndicator, 
      CreatedDate, CreatedBy, ModifiedDate, 
      ModifiedBy, LastModifiedTimestamp, Version, 
      SampleSort, NoOfSample, ExternalSampleId, 
      SourceType, OtherSampleInfo)
    values (#{ID,jdbcType=VARCHAR}, #{sampleParentID,jdbcType=VARCHAR}, #{sampleNo,jdbcType=VARCHAR}, 
      #{sampleType,jdbcType=INTEGER}, #{groupType,jdbcType=VARCHAR}, #{sampleSeq,jdbcType=INTEGER}, 
      #{orderNo,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{composition,jdbcType=VARCHAR}, #{color,jdbcType=VARCHAR}, #{sampleDescforReport,jdbcType=VARCHAR}, 
      #{sampleRemark,jdbcType=VARCHAR}, #{endUse,jdbcType=VARCHAR}, #{material,jdbcType=VARCHAR}, 
      #{applicable,jdbcType=BIT}, #{referDataType,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=BIT}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{lastModifiedTimestamp,jdbcType=TIMESTAMP}, #{version,jdbcType=INTEGER}, 
      #{sampleSort,jdbcType=INTEGER}, #{noOfSample,jdbcType=INTEGER}, #{externalSampleId,jdbcType=VARCHAR}, 
      #{sourceType,jdbcType=VARCHAR}, #{otherSampleInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleDO" >
    insert into tb_test_sample
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="sampleParentID != null" >
        SampleParentID,
      </if>
      <if test="sampleNo != null" >
        SampleNo,
      </if>
      <if test="sampleType != null" >
        SampleType,
      </if>
      <if test="groupType != null" >
        GroupType,
      </if>
      <if test="sampleSeq != null" >
        SampleSeq,
      </if>
      <if test="orderNo != null" >
        OrderNo,
      </if>
      <if test="category != null" >
        Category,
      </if>
      <if test="description != null" >
        Description,
      </if>
      <if test="composition != null" >
        Composition,
      </if>
      <if test="color != null" >
        Color,
      </if>
      <if test="sampleDescforReport != null" >
        SampleDescforReport,
      </if>
      <if test="sampleRemark != null" >
        SampleRemark,
      </if>
      <if test="endUse != null" >
        EndUse,
      </if>
      <if test="material != null" >
        Material,
      </if>
      <if test="applicable != null" >
        Applicable,
      </if>
      <if test="referDataType != null" >
        ReferDataType,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp,
      </if>
      <if test="version != null" >
        Version,
      </if>
      <if test="sampleSort != null" >
        SampleSort,
      </if>
      <if test="noOfSample != null" >
        NoOfSample,
      </if>
      <if test="externalSampleId != null" >
        ExternalSampleId,
      </if>
      <if test="sourceType != null" >
        SourceType,
      </if>
      <if test="otherSampleInfo != null" >
        OtherSampleInfo,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="sampleParentID != null" >
        #{sampleParentID,jdbcType=VARCHAR},
      </if>
      <if test="sampleNo != null" >
        #{sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="sampleType != null" >
        #{sampleType,jdbcType=INTEGER},
      </if>
      <if test="groupType != null" >
        #{groupType,jdbcType=VARCHAR},
      </if>
      <if test="sampleSeq != null" >
        #{sampleSeq,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="category != null" >
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="composition != null" >
        #{composition,jdbcType=VARCHAR},
      </if>
      <if test="color != null" >
        #{color,jdbcType=VARCHAR},
      </if>
      <if test="sampleDescforReport != null" >
        #{sampleDescforReport,jdbcType=VARCHAR},
      </if>
      <if test="sampleRemark != null" >
        #{sampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="endUse != null" >
        #{endUse,jdbcType=VARCHAR},
      </if>
      <if test="material != null" >
        #{material,jdbcType=VARCHAR},
      </if>
      <if test="applicable != null" >
        #{applicable,jdbcType=BIT},
      </if>
      <if test="referDataType != null" >
        #{referDataType,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null" >
        #{version,jdbcType=INTEGER},
      </if>
      <if test="sampleSort != null" >
        #{sampleSort,jdbcType=INTEGER},
      </if>
      <if test="noOfSample != null" >
        #{noOfSample,jdbcType=INTEGER},
      </if>
      <if test="externalSampleId != null" >
        #{externalSampleId,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null" >
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="otherSampleInfo != null" >
        #{otherSampleInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleDOExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_sample
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_sample
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleParentID != null" >
        SampleParentID = #{record.sampleParentID,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleNo != null" >
        SampleNo = #{record.sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleType != null" >
        SampleType = #{record.sampleType,jdbcType=INTEGER},
      </if>
      <if test="record.groupType != null" >
        GroupType = #{record.groupType,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleSeq != null" >
        SampleSeq = #{record.sampleSeq,jdbcType=INTEGER},
      </if>
      <if test="record.orderNo != null" >
        OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null" >
        Category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null" >
        Description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.composition != null" >
        Composition = #{record.composition,jdbcType=VARCHAR},
      </if>
      <if test="record.color != null" >
        Color = #{record.color,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleDescforReport != null" >
        SampleDescforReport = #{record.sampleDescforReport,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleRemark != null" >
        SampleRemark = #{record.sampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.endUse != null" >
        EndUse = #{record.endUse,jdbcType=VARCHAR},
      </if>
      <if test="record.material != null" >
        Material = #{record.material,jdbcType=VARCHAR},
      </if>
      <if test="record.applicable != null" >
        Applicable = #{record.applicable,jdbcType=BIT},
      </if>
      <if test="record.referDataType != null" >
        ReferDataType = #{record.referDataType,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="record.version != null" >
        Version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.sampleSort != null" >
        SampleSort = #{record.sampleSort,jdbcType=INTEGER},
      </if>
      <if test="record.noOfSample != null" >
        NoOfSample = #{record.noOfSample,jdbcType=INTEGER},
      </if>
      <if test="record.externalSampleId != null" >
        ExternalSampleId = #{record.externalSampleId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceType != null" >
        SourceType = #{record.sourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.otherSampleInfo != null" >
        OtherSampleInfo = #{record.otherSampleInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tb_test_sample
    set ID = #{record.ID,jdbcType=VARCHAR},
      SampleParentID = #{record.sampleParentID,jdbcType=VARCHAR},
      SampleNo = #{record.sampleNo,jdbcType=VARCHAR},
      SampleType = #{record.sampleType,jdbcType=INTEGER},
      GroupType = #{record.groupType,jdbcType=VARCHAR},
      SampleSeq = #{record.sampleSeq,jdbcType=INTEGER},
      OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      Category = #{record.category,jdbcType=VARCHAR},
      Description = #{record.description,jdbcType=VARCHAR},
      Composition = #{record.composition,jdbcType=VARCHAR},
      Color = #{record.color,jdbcType=VARCHAR},
      SampleDescforReport = #{record.sampleDescforReport,jdbcType=VARCHAR},
      SampleRemark = #{record.sampleRemark,jdbcType=VARCHAR},
      EndUse = #{record.endUse,jdbcType=VARCHAR},
      Material = #{record.material,jdbcType=VARCHAR},
      Applicable = #{record.applicable,jdbcType=BIT},
      ReferDataType = #{record.referDataType,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      Version = #{record.version,jdbcType=INTEGER},
      SampleSort = #{record.sampleSort,jdbcType=INTEGER},
      NoOfSample = #{record.noOfSample,jdbcType=INTEGER},
      ExternalSampleId = #{record.externalSampleId,jdbcType=VARCHAR},
      SourceType = #{record.sourceType,jdbcType=VARCHAR},
      OtherSampleInfo = #{record.otherSampleInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_sample
    set ID = #{record.ID,jdbcType=VARCHAR},
      SampleParentID = #{record.sampleParentID,jdbcType=VARCHAR},
      SampleNo = #{record.sampleNo,jdbcType=VARCHAR},
      SampleType = #{record.sampleType,jdbcType=INTEGER},
      GroupType = #{record.groupType,jdbcType=VARCHAR},
      SampleSeq = #{record.sampleSeq,jdbcType=INTEGER},
      OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      Category = #{record.category,jdbcType=VARCHAR},
      Description = #{record.description,jdbcType=VARCHAR},
      Composition = #{record.composition,jdbcType=VARCHAR},
      Color = #{record.color,jdbcType=VARCHAR},
      SampleDescforReport = #{record.sampleDescforReport,jdbcType=VARCHAR},
      SampleRemark = #{record.sampleRemark,jdbcType=VARCHAR},
      EndUse = #{record.endUse,jdbcType=VARCHAR},
      Material = #{record.material,jdbcType=VARCHAR},
      Applicable = #{record.applicable,jdbcType=BIT},
      ReferDataType = #{record.referDataType,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      Version = #{record.version,jdbcType=INTEGER},
      SampleSort = #{record.sampleSort,jdbcType=INTEGER},
      NoOfSample = #{record.noOfSample,jdbcType=INTEGER},
      ExternalSampleId = #{record.externalSampleId,jdbcType=VARCHAR},
      SourceType = #{record.sourceType,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleDO" >
    update tb_test_sample
    <set >
      <if test="sampleParentID != null" >
        SampleParentID = #{sampleParentID,jdbcType=VARCHAR},
      </if>
      <if test="sampleNo != null" >
        SampleNo = #{sampleNo,jdbcType=VARCHAR},
      </if>
      <if test="sampleType != null" >
        SampleType = #{sampleType,jdbcType=INTEGER},
      </if>
      <if test="groupType != null" >
        GroupType = #{groupType,jdbcType=VARCHAR},
      </if>
      <if test="sampleSeq != null" >
        SampleSeq = #{sampleSeq,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null" >
        OrderNo = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="category != null" >
        Category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        Description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="composition != null" >
        Composition = #{composition,jdbcType=VARCHAR},
      </if>
      <if test="color != null" >
        Color = #{color,jdbcType=VARCHAR},
      </if>
      <if test="sampleDescforReport != null" >
        SampleDescforReport = #{sampleDescforReport,jdbcType=VARCHAR},
      </if>
      <if test="sampleRemark != null" >
        SampleRemark = #{sampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="endUse != null" >
        EndUse = #{endUse,jdbcType=VARCHAR},
      </if>
      <if test="material != null" >
        Material = #{material,jdbcType=VARCHAR},
      </if>
      <if test="applicable != null" >
        Applicable = #{applicable,jdbcType=BIT},
      </if>
      <if test="referDataType != null" >
        ReferDataType = #{referDataType,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null" >
        Version = #{version,jdbcType=INTEGER},
      </if>
      <if test="sampleSort != null" >
        SampleSort = #{sampleSort,jdbcType=INTEGER},
      </if>
      <if test="noOfSample != null" >
        NoOfSample = #{noOfSample,jdbcType=INTEGER},
      </if>
      <if test="externalSampleId != null" >
        ExternalSampleId = #{externalSampleId,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null" >
        SourceType = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="otherSampleInfo != null" >
        OtherSampleInfo = #{otherSampleInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleDO" >
    update tb_test_sample
    set SampleParentID = #{sampleParentID,jdbcType=VARCHAR},
      SampleNo = #{sampleNo,jdbcType=VARCHAR},
      SampleType = #{sampleType,jdbcType=INTEGER},
      GroupType = #{groupType,jdbcType=VARCHAR},
      SampleSeq = #{sampleSeq,jdbcType=INTEGER},
      OrderNo = #{orderNo,jdbcType=VARCHAR},
      Category = #{category,jdbcType=VARCHAR},
      Description = #{description,jdbcType=VARCHAR},
      Composition = #{composition,jdbcType=VARCHAR},
      Color = #{color,jdbcType=VARCHAR},
      SampleDescforReport = #{sampleDescforReport,jdbcType=VARCHAR},
      SampleRemark = #{sampleRemark,jdbcType=VARCHAR},
      EndUse = #{endUse,jdbcType=VARCHAR},
      Material = #{material,jdbcType=VARCHAR},
      Applicable = #{applicable,jdbcType=BIT},
      ReferDataType = #{referDataType,jdbcType=VARCHAR},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      Version = #{version,jdbcType=INTEGER},
      SampleSort = #{sampleSort,jdbcType=INTEGER},
      NoOfSample = #{noOfSample,jdbcType=INTEGER},
      ExternalSampleId = #{externalSampleId,jdbcType=VARCHAR},
      SourceType = #{sourceType,jdbcType=VARCHAR},
      OtherSampleInfo = #{otherSampleInfo,jdbcType=LONGVARCHAR}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleDO" >
    update tb_test_sample
    set SampleParentID = #{sampleParentID,jdbcType=VARCHAR},
      SampleNo = #{sampleNo,jdbcType=VARCHAR},
      SampleType = #{sampleType,jdbcType=INTEGER},
      GroupType = #{groupType,jdbcType=VARCHAR},
      SampleSeq = #{sampleSeq,jdbcType=INTEGER},
      OrderNo = #{orderNo,jdbcType=VARCHAR},
      Category = #{category,jdbcType=VARCHAR},
      Description = #{description,jdbcType=VARCHAR},
      Composition = #{composition,jdbcType=VARCHAR},
      Color = #{color,jdbcType=VARCHAR},
      SampleDescforReport = #{sampleDescforReport,jdbcType=VARCHAR},
      SampleRemark = #{sampleRemark,jdbcType=VARCHAR},
      EndUse = #{endUse,jdbcType=VARCHAR},
      Material = #{material,jdbcType=VARCHAR},
      Applicable = #{applicable,jdbcType=BIT},
      ReferDataType = #{referDataType,jdbcType=VARCHAR},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      Version = #{version,jdbcType=INTEGER},
      SampleSort = #{sampleSort,jdbcType=INTEGER},
      NoOfSample = #{noOfSample,jdbcType=INTEGER},
      ExternalSampleId = #{externalSampleId,jdbcType=VARCHAR},
      SourceType = #{sourceType,jdbcType=VARCHAR}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_test_sample
      (`ID`,`SampleParentID`,`SampleNo`,
      `SampleType`,`GroupType`,`SampleSeq`,
      `OrderNo`,`Category`,`Description`,
      `Composition`,`Color`,`SampleDescforReport`,
      `SampleRemark`,`EndUse`,`Material`,
      `Applicable`,`ReferDataType`,`ActiveIndicator`,
      `CreatedDate`,`CreatedBy`,`ModifiedDate`,
      `ModifiedBy`,`LastModifiedTimestamp`,`Version`,
      `SampleSort`,`NoOfSample`,`ExternalSampleId`,
      `SourceType`,`OtherSampleInfo`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.ID, jdbcType=VARCHAR},#{ item.sampleParentID, jdbcType=VARCHAR},#{ item.sampleNo, jdbcType=VARCHAR},
      #{ item.sampleType, jdbcType=INTEGER},#{ item.groupType, jdbcType=VARCHAR},#{ item.sampleSeq, jdbcType=INTEGER},
      #{ item.orderNo, jdbcType=VARCHAR},#{ item.category, jdbcType=VARCHAR},#{ item.description, jdbcType=VARCHAR},
      #{ item.composition, jdbcType=VARCHAR},#{ item.color, jdbcType=VARCHAR},#{ item.sampleDescforReport, jdbcType=VARCHAR},
      #{ item.sampleRemark, jdbcType=VARCHAR},#{ item.endUse, jdbcType=VARCHAR},#{ item.material, jdbcType=VARCHAR},
      #{ item.applicable, jdbcType=BIT},#{ item.referDataType, jdbcType=VARCHAR},#{ item.activeIndicator, jdbcType=BIT},
      #{ item.createdDate, jdbcType=TIMESTAMP},#{ item.createdBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},
      #{ item.modifiedBy, jdbcType=VARCHAR},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP},#{ item.version, jdbcType=INTEGER},
      #{ item.sampleSort, jdbcType=INTEGER},#{ item.noOfSample, jdbcType=INTEGER},#{ item.externalSampleId, jdbcType=VARCHAR},
      #{ item.sourceType, jdbcType=VARCHAR},#{ item.otherSampleInfo, jdbcType=LONGVARCHAR}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_test_sample 
      <set>
        <if test="item.sampleParentID != null"> 
          `SampleParentID` = #{item.sampleParentID, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleNo != null"> 
          `SampleNo` = #{item.sampleNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleType != null"> 
          `SampleType` = #{item.sampleType, jdbcType = INTEGER},
        </if> 
        <if test="item.groupType != null"> 
          `GroupType` = #{item.groupType, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleSeq != null"> 
          `SampleSeq` = #{item.sampleSeq, jdbcType = INTEGER},
        </if> 
        <if test="item.orderNo != null"> 
          `OrderNo` = #{item.orderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.category != null"> 
          `Category` = #{item.category, jdbcType = VARCHAR},
        </if> 
        <if test="item.description != null"> 
          `Description` = #{item.description, jdbcType = VARCHAR},
        </if> 
        <if test="item.composition != null"> 
          `Composition` = #{item.composition, jdbcType = VARCHAR},
        </if> 
        <if test="item.color != null"> 
          `Color` = #{item.color, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleDescforReport != null"> 
          `SampleDescforReport` = #{item.sampleDescforReport, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleRemark != null"> 
          `SampleRemark` = #{item.sampleRemark, jdbcType = VARCHAR},
        </if> 
        <if test="item.endUse != null"> 
          `EndUse` = #{item.endUse, jdbcType = VARCHAR},
        </if> 
        <if test="item.material != null"> 
          `Material` = #{item.material, jdbcType = VARCHAR},
        </if> 
        <if test="item.applicable != null"> 
          `Applicable` = #{item.applicable, jdbcType = BIT},
        </if> 
        <if test="item.referDataType != null"> 
          `ReferDataType` = #{item.referDataType, jdbcType = VARCHAR},
        </if> 
        <if test="item.activeIndicator != null"> 
          `ActiveIndicator` = #{item.activeIndicator, jdbcType = BIT},
        </if> 
        <if test="item.createdDate != null"> 
          `CreatedDate` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.createdBy != null"> 
          `CreatedBy` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `ModifiedDate` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `ModifiedBy` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `LastModifiedTimestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.version != null"> 
          `Version` = #{item.version, jdbcType = INTEGER},
        </if> 
        <if test="item.sampleSort != null"> 
          `SampleSort` = #{item.sampleSort, jdbcType = INTEGER},
        </if> 
        <if test="item.noOfSample != null"> 
          `NoOfSample` = #{item.noOfSample, jdbcType = INTEGER},
        </if> 
        <if test="item.externalSampleId != null"> 
          `ExternalSampleId` = #{item.externalSampleId, jdbcType = VARCHAR},
        </if> 
        <if test="item.sourceType != null"> 
          `SourceType` = #{item.sourceType, jdbcType = VARCHAR},
        </if> 
        <if test="item.otherSampleInfo != null"> 
          `OtherSampleInfo` = #{item.otherSampleInfo, jdbcType = LONGVARCHAR},
        </if> 
      </set>
      <where>
        <if test="item.ID != null">
           and `ID` = #{item.ID,jdbcType = VARCHAR}
        </if>
      </where>
    </foreach>
  </update>
</mapper>