<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.soda.otsnotes.infrastructure.database.testsample.mapper.generated.TestSampleMaterialLanguageDOMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDO" >
    <id column="Id" property="id" jdbcType="VARCHAR" />
    <result column="SampleId" property="sampleId" jdbcType="VARCHAR" />
    <result column="GroupId" property="groupId" jdbcType="VARCHAR" />
    <result column="Material" property="material" jdbcType="VARCHAR" />
    <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
    <result column="Status" property="status" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="LastModifiedTimestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
    <result column="Composition" property="composition" jdbcType="VARCHAR" />
    <result column="Color" property="color" jdbcType="VARCHAR" />
    <result column="SampleDescforReport" property="sampleDescforReport" jdbcType="VARCHAR" />
    <result column="SampleRemark" property="sampleRemark" jdbcType="VARCHAR" />
    <result column="EndUse" property="endUse" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDO" extends="BaseResultMap" >
    <result column="Description" property="description" jdbcType="LONGVARCHAR" />
    <result column="OtherSampleInfo" property="otherSampleInfo" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, SampleId, GroupId, Material, LanguageId, `Status`, CreatedDate, CreatedBy, ModifiedDate, 
    ModifiedBy, LastModifiedTimestamp, Composition, Color, SampleDescforReport, SampleRemark, 
    EndUse
  </sql>
  <sql id="Blob_Column_List" >
    Description, OtherSampleInfo
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDOExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_test_sample_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDOExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_sample_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_test_sample_language
    where Id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_test_sample_language
    where Id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDOExample" >
    delete from tb_test_sample_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDO" >
    insert into tb_test_sample_language (Id, SampleId, GroupId, 
      Material, LanguageId, `Status`, 
      CreatedDate, CreatedBy, ModifiedDate, 
      ModifiedBy, LastModifiedTimestamp, Composition, 
      Color, SampleDescforReport, SampleRemark, 
      EndUse, Description, OtherSampleInfo
      )
    values (#{id,jdbcType=VARCHAR}, #{sampleId,jdbcType=VARCHAR}, #{groupId,jdbcType=VARCHAR}, 
      #{material,jdbcType=VARCHAR}, #{languageId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{lastModifiedTimestamp,jdbcType=TIMESTAMP}, #{composition,jdbcType=VARCHAR}, 
      #{color,jdbcType=VARCHAR}, #{sampleDescforReport,jdbcType=VARCHAR}, #{sampleRemark,jdbcType=VARCHAR}, 
      #{endUse,jdbcType=VARCHAR}, #{description,jdbcType=LONGVARCHAR}, #{otherSampleInfo,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDO" >
    insert into tb_test_sample_language
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="sampleId != null" >
        SampleId,
      </if>
      <if test="groupId != null" >
        GroupId,
      </if>
      <if test="material != null" >
        Material,
      </if>
      <if test="languageId != null" >
        LanguageId,
      </if>
      <if test="status != null" >
        `Status`,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp,
      </if>
      <if test="composition != null" >
        Composition,
      </if>
      <if test="color != null" >
        Color,
      </if>
      <if test="sampleDescforReport != null" >
        SampleDescforReport,
      </if>
      <if test="sampleRemark != null" >
        SampleRemark,
      </if>
      <if test="endUse != null" >
        EndUse,
      </if>
      <if test="description != null" >
        Description,
      </if>
      <if test="otherSampleInfo != null" >
        OtherSampleInfo,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="sampleId != null" >
        #{sampleId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null" >
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="material != null" >
        #{material,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        #{languageId,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="composition != null" >
        #{composition,jdbcType=VARCHAR},
      </if>
      <if test="color != null" >
        #{color,jdbcType=VARCHAR},
      </if>
      <if test="sampleDescforReport != null" >
        #{sampleDescforReport,jdbcType=VARCHAR},
      </if>
      <if test="sampleRemark != null" >
        #{sampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="endUse != null" >
        #{endUse,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="otherSampleInfo != null" >
        #{otherSampleInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDOExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_sample_language
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_sample_language
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleId != null" >
        SampleId = #{record.sampleId,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null" >
        GroupId = #{record.groupId,jdbcType=VARCHAR},
      </if>
      <if test="record.material != null" >
        Material = #{record.material,jdbcType=VARCHAR},
      </if>
      <if test="record.languageId != null" >
        LanguageId = #{record.languageId,jdbcType=INTEGER},
      </if>
      <if test="record.status != null" >
        `Status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="record.composition != null" >
        Composition = #{record.composition,jdbcType=VARCHAR},
      </if>
      <if test="record.color != null" >
        Color = #{record.color,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleDescforReport != null" >
        SampleDescforReport = #{record.sampleDescforReport,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleRemark != null" >
        SampleRemark = #{record.sampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.endUse != null" >
        EndUse = #{record.endUse,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null" >
        Description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.otherSampleInfo != null" >
        OtherSampleInfo = #{record.otherSampleInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tb_test_sample_language
    set Id = #{record.id,jdbcType=VARCHAR},
      SampleId = #{record.sampleId,jdbcType=VARCHAR},
      GroupId = #{record.groupId,jdbcType=VARCHAR},
      Material = #{record.material,jdbcType=VARCHAR},
      LanguageId = #{record.languageId,jdbcType=INTEGER},
      `Status` = #{record.status,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      Composition = #{record.composition,jdbcType=VARCHAR},
      Color = #{record.color,jdbcType=VARCHAR},
      SampleDescforReport = #{record.sampleDescforReport,jdbcType=VARCHAR},
      SampleRemark = #{record.sampleRemark,jdbcType=VARCHAR},
      EndUse = #{record.endUse,jdbcType=VARCHAR},
      Description = #{record.description,jdbcType=LONGVARCHAR},
      OtherSampleInfo = #{record.otherSampleInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_sample_language
    set Id = #{record.id,jdbcType=VARCHAR},
      SampleId = #{record.sampleId,jdbcType=VARCHAR},
      GroupId = #{record.groupId,jdbcType=VARCHAR},
      Material = #{record.material,jdbcType=VARCHAR},
      LanguageId = #{record.languageId,jdbcType=INTEGER},
      `Status` = #{record.status,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      Composition = #{record.composition,jdbcType=VARCHAR},
      Color = #{record.color,jdbcType=VARCHAR},
      SampleDescforReport = #{record.sampleDescforReport,jdbcType=VARCHAR},
      SampleRemark = #{record.sampleRemark,jdbcType=VARCHAR},
      EndUse = #{record.endUse,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDO" >
    update tb_test_sample_language
    <set >
      <if test="sampleId != null" >
        SampleId = #{sampleId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null" >
        GroupId = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="material != null" >
        Material = #{material,jdbcType=VARCHAR},
      </if>
      <if test="languageId != null" >
        LanguageId = #{languageId,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        `Status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="composition != null" >
        Composition = #{composition,jdbcType=VARCHAR},
      </if>
      <if test="color != null" >
        Color = #{color,jdbcType=VARCHAR},
      </if>
      <if test="sampleDescforReport != null" >
        SampleDescforReport = #{sampleDescforReport,jdbcType=VARCHAR},
      </if>
      <if test="sampleRemark != null" >
        SampleRemark = #{sampleRemark,jdbcType=VARCHAR},
      </if>
      <if test="endUse != null" >
        EndUse = #{endUse,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        Description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="otherSampleInfo != null" >
        OtherSampleInfo = #{otherSampleInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where Id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDO" >
    update tb_test_sample_language
    set SampleId = #{sampleId,jdbcType=VARCHAR},
      GroupId = #{groupId,jdbcType=VARCHAR},
      Material = #{material,jdbcType=VARCHAR},
      LanguageId = #{languageId,jdbcType=INTEGER},
      `Status` = #{status,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      Composition = #{composition,jdbcType=VARCHAR},
      Color = #{color,jdbcType=VARCHAR},
      SampleDescforReport = #{sampleDescforReport,jdbcType=VARCHAR},
      SampleRemark = #{sampleRemark,jdbcType=VARCHAR},
      EndUse = #{endUse,jdbcType=VARCHAR},
      Description = #{description,jdbcType=LONGVARCHAR},
      OtherSampleInfo = #{otherSampleInfo,jdbcType=LONGVARCHAR}
    where Id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleMaterialLanguageDO" >
    update tb_test_sample_language
    set SampleId = #{sampleId,jdbcType=VARCHAR},
      GroupId = #{groupId,jdbcType=VARCHAR},
      Material = #{material,jdbcType=VARCHAR},
      LanguageId = #{languageId,jdbcType=INTEGER},
      `Status` = #{status,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      Composition = #{composition,jdbcType=VARCHAR},
      Color = #{color,jdbcType=VARCHAR},
      SampleDescforReport = #{sampleDescforReport,jdbcType=VARCHAR},
      SampleRemark = #{sampleRemark,jdbcType=VARCHAR},
      EndUse = #{endUse,jdbcType=VARCHAR}
    where Id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_test_sample_language
      (`Id`,`SampleId`,`GroupId`,
      `Material`,`LanguageId`,`Status`,
      `CreatedDate`,`CreatedBy`,`ModifiedDate`,
      `ModifiedBy`,`LastModifiedTimestamp`,`Composition`,
      `Color`,`SampleDescforReport`,`SampleRemark`,
      `EndUse`,`Description`,`OtherSampleInfo`
      )
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=VARCHAR},#{ item.sampleId, jdbcType=VARCHAR},#{ item.groupId, jdbcType=VARCHAR},
      #{ item.material, jdbcType=VARCHAR},#{ item.languageId, jdbcType=INTEGER},#{ item.status, jdbcType=INTEGER},
      #{ item.createdDate, jdbcType=TIMESTAMP},#{ item.createdBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},
      #{ item.modifiedBy, jdbcType=VARCHAR},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP},#{ item.composition, jdbcType=VARCHAR},
      #{ item.color, jdbcType=VARCHAR},#{ item.sampleDescforReport, jdbcType=VARCHAR},#{ item.sampleRemark, jdbcType=VARCHAR},
      #{ item.endUse, jdbcType=VARCHAR},#{ item.description, jdbcType=LONGVARCHAR},#{ item.otherSampleInfo, jdbcType=LONGVARCHAR}
      ) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_test_sample_language 
      <set>
        <if test="item.sampleId != null"> 
          `SampleId` = #{item.sampleId, jdbcType = VARCHAR},
        </if> 
        <if test="item.groupId != null"> 
          `GroupId` = #{item.groupId, jdbcType = VARCHAR},
        </if> 
        <if test="item.material != null"> 
          `Material` = #{item.material, jdbcType = VARCHAR},
        </if> 
        <if test="item.languageId != null"> 
          `LanguageId` = #{item.languageId, jdbcType = INTEGER},
        </if> 
        <if test="item.status != null"> 
          `Status` = #{item.status, jdbcType = INTEGER},
        </if> 
        <if test="item.createdDate != null"> 
          `CreatedDate` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.createdBy != null"> 
          `CreatedBy` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `ModifiedDate` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `ModifiedBy` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `LastModifiedTimestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.composition != null"> 
          `Composition` = #{item.composition, jdbcType = VARCHAR},
        </if> 
        <if test="item.color != null"> 
          `Color` = #{item.color, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleDescforReport != null"> 
          `SampleDescforReport` = #{item.sampleDescforReport, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleRemark != null"> 
          `SampleRemark` = #{item.sampleRemark, jdbcType = VARCHAR},
        </if> 
        <if test="item.endUse != null"> 
          `EndUse` = #{item.endUse, jdbcType = VARCHAR},
        </if> 
        <if test="item.description != null"> 
          `Description` = #{item.description, jdbcType = LONGVARCHAR},
        </if> 
        <if test="item.otherSampleInfo != null"> 
          `OtherSampleInfo` = #{item.otherSampleInfo, jdbcType = LONGVARCHAR},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `Id` = #{item.id,jdbcType = VARCHAR}
        </if>
      </where>
    </foreach>
  </update>
</mapper>