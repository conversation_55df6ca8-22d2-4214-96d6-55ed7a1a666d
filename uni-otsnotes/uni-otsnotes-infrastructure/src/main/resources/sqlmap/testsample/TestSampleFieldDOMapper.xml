<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.soda.otsnotes.infrastructure.database.testsample.mapper.generated.TestSampleFieldDOMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleFieldDO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="SampleId" property="sampleId" jdbcType="VARCHAR" />
    <result column="FieldCode" property="fieldCode" jdbcType="VARCHAR" />
    <result column="FieldName" property="fieldName" jdbcType="VARCHAR" />
    <result column="FieldValue" property="fieldValue" jdbcType="VARCHAR" />
    <result column="FieldText" property="fieldText" jdbcType="VARCHAR" />
    <result column="Status" property="status" jdbcType="INTEGER" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="LastModifiedTimestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, SampleId, FieldCode, FieldName, FieldValue, FieldText, `Status`, CreatedBy, CreatedDate, 
    ModifiedBy, ModifiedDate, LastModifiedTimestamp
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleFieldDOExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_sample_ext
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_test_sample_ext
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_test_sample_ext
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleFieldDOExample" >
    delete from tb_test_sample_ext
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleFieldDO" >
    insert into tb_test_sample_ext (Id, SampleId, FieldCode, 
      FieldName, FieldValue, FieldText, 
      `Status`, CreatedBy, CreatedDate, 
      ModifiedBy, ModifiedDate, LastModifiedTimestamp
      )
    values (#{id,jdbcType=BIGINT}, #{sampleId,jdbcType=VARCHAR}, #{fieldCode,jdbcType=VARCHAR}, 
      #{fieldName,jdbcType=VARCHAR}, #{fieldValue,jdbcType=VARCHAR}, #{fieldText,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleFieldDO" >
    insert into tb_test_sample_ext
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="sampleId != null" >
        SampleId,
      </if>
      <if test="fieldCode != null" >
        FieldCode,
      </if>
      <if test="fieldName != null" >
        FieldName,
      </if>
      <if test="fieldValue != null" >
        FieldValue,
      </if>
      <if test="fieldText != null" >
        FieldText,
      </if>
      <if test="status != null" >
        `Status`,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sampleId != null" >
        #{sampleId,jdbcType=VARCHAR},
      </if>
      <if test="fieldCode != null" >
        #{fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null" >
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldValue != null" >
        #{fieldValue,jdbcType=VARCHAR},
      </if>
      <if test="fieldText != null" >
        #{fieldText,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleFieldDOExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_sample_ext
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_sample_ext
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sampleId != null" >
        SampleId = #{record.sampleId,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldCode != null" >
        FieldCode = #{record.fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldName != null" >
        FieldName = #{record.fieldName,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldValue != null" >
        FieldValue = #{record.fieldValue,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldText != null" >
        FieldText = #{record.fieldText,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `Status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_sample_ext
    set Id = #{record.id,jdbcType=BIGINT},
      SampleId = #{record.sampleId,jdbcType=VARCHAR},
      FieldCode = #{record.fieldCode,jdbcType=VARCHAR},
      FieldName = #{record.fieldName,jdbcType=VARCHAR},
      FieldValue = #{record.fieldValue,jdbcType=VARCHAR},
      FieldText = #{record.fieldText,jdbcType=VARCHAR},
      `Status` = #{record.status,jdbcType=INTEGER},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleFieldDO" >
    update tb_test_sample_ext
    <set >
      <if test="sampleId != null" >
        SampleId = #{sampleId,jdbcType=VARCHAR},
      </if>
      <if test="fieldCode != null" >
        FieldCode = #{fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null" >
        FieldName = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldValue != null" >
        FieldValue = #{fieldValue,jdbcType=VARCHAR},
      </if>
      <if test="fieldText != null" >
        FieldText = #{fieldText,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `Status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleFieldDO" >
    update tb_test_sample_ext
    set SampleId = #{sampleId,jdbcType=VARCHAR},
      FieldCode = #{fieldCode,jdbcType=VARCHAR},
      FieldName = #{fieldName,jdbcType=VARCHAR},
      FieldValue = #{fieldValue,jdbcType=VARCHAR},
      FieldText = #{fieldText,jdbcType=VARCHAR},
      `Status` = #{status,jdbcType=INTEGER},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_test_sample_ext
      (`Id`,`SampleId`,`FieldCode`,
      `FieldName`,`FieldValue`,`FieldText`,
      `Status`,`CreatedBy`,`CreatedDate`,
      `ModifiedBy`,`ModifiedDate`,`LastModifiedTimestamp`
      )
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.sampleId, jdbcType=VARCHAR},#{ item.fieldCode, jdbcType=VARCHAR},
      #{ item.fieldName, jdbcType=VARCHAR},#{ item.fieldValue, jdbcType=VARCHAR},#{ item.fieldText, jdbcType=VARCHAR},
      #{ item.status, jdbcType=INTEGER},#{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},
      #{ item.modifiedBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP}
      ) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_test_sample_ext 
      <set>
        <if test="item.sampleId != null"> 
          `SampleId` = #{item.sampleId, jdbcType = VARCHAR},
        </if> 
        <if test="item.fieldCode != null"> 
          `FieldCode` = #{item.fieldCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.fieldName != null"> 
          `FieldName` = #{item.fieldName, jdbcType = VARCHAR},
        </if> 
        <if test="item.fieldValue != null"> 
          `FieldValue` = #{item.fieldValue, jdbcType = VARCHAR},
        </if> 
        <if test="item.fieldText != null"> 
          `FieldText` = #{item.fieldText, jdbcType = VARCHAR},
        </if> 
        <if test="item.status != null"> 
          `Status` = #{item.status, jdbcType = INTEGER},
        </if> 
        <if test="item.createdBy != null"> 
          `CreatedBy` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `CreatedDate` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `ModifiedBy` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `ModifiedDate` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `LastModifiedTimestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `Id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>