<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.soda.otsnotes.infrastructure.database.testsample.mapper.generated.TestSampleGroupDOMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleGroupDO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="SampleGroupID" property="sampleGroupID" jdbcType="VARCHAR" />
    <result column="SampleID" property="sampleID" jdbcType="VARCHAR" />
    <result column="MainMaterialFlag" property="mainMaterialFlag" jdbcType="BIT" />
    <result column="Sequence" property="sequence" jdbcType="INTEGER" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="LastModifiedTimestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, SampleGroupID, SampleID, MainMaterialFlag, `Sequence`, ActiveIndicator, CreatedDate, 
    CreatedBy, ModifiedDate, ModifiedBy, LastModifiedTimestamp
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleGroupDOExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_test_sample_group
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_test_sample_group
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_test_sample_group
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleGroupDOExample" >
    delete from tb_test_sample_group
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleGroupDO" >
    insert into tb_test_sample_group (ID, SampleGroupID, SampleID, 
      MainMaterialFlag, `Sequence`, ActiveIndicator, 
      CreatedDate, CreatedBy, ModifiedDate, 
      ModifiedBy, LastModifiedTimestamp)
    values (#{ID,jdbcType=VARCHAR}, #{sampleGroupID,jdbcType=VARCHAR}, #{sampleID,jdbcType=VARCHAR}, 
      #{mainMaterialFlag,jdbcType=BIT}, #{sequence,jdbcType=INTEGER}, #{activeIndicator,jdbcType=BIT}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{lastModifiedTimestamp,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleGroupDO" >
    insert into tb_test_sample_group
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="sampleGroupID != null" >
        SampleGroupID,
      </if>
      <if test="sampleID != null" >
        SampleID,
      </if>
      <if test="mainMaterialFlag != null" >
        MainMaterialFlag,
      </if>
      <if test="sequence != null" >
        `Sequence`,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="sampleGroupID != null" >
        #{sampleGroupID,jdbcType=VARCHAR},
      </if>
      <if test="sampleID != null" >
        #{sampleID,jdbcType=VARCHAR},
      </if>
      <if test="mainMaterialFlag != null" >
        #{mainMaterialFlag,jdbcType=BIT},
      </if>
      <if test="sequence != null" >
        #{sequence,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleGroupDOExample" resultType="java.lang.Integer" >
    select count(*) from tb_test_sample_group
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_test_sample_group
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleGroupID != null" >
        SampleGroupID = #{record.sampleGroupID,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleID != null" >
        SampleID = #{record.sampleID,jdbcType=VARCHAR},
      </if>
      <if test="record.mainMaterialFlag != null" >
        MainMaterialFlag = #{record.mainMaterialFlag,jdbcType=BIT},
      </if>
      <if test="record.sequence != null" >
        `Sequence` = #{record.sequence,jdbcType=INTEGER},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_test_sample_group
    set ID = #{record.ID,jdbcType=VARCHAR},
      SampleGroupID = #{record.sampleGroupID,jdbcType=VARCHAR},
      SampleID = #{record.sampleID,jdbcType=VARCHAR},
      MainMaterialFlag = #{record.mainMaterialFlag,jdbcType=BIT},
      `Sequence` = #{record.sequence,jdbcType=INTEGER},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleGroupDO" >
    update tb_test_sample_group
    <set >
      <if test="sampleGroupID != null" >
        SampleGroupID = #{sampleGroupID,jdbcType=VARCHAR},
      </if>
      <if test="sampleID != null" >
        SampleID = #{sampleID,jdbcType=VARCHAR},
      </if>
      <if test="mainMaterialFlag != null" >
        MainMaterialFlag = #{mainMaterialFlag,jdbcType=BIT},
      </if>
      <if test="sequence != null" >
        `Sequence` = #{sequence,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto.TestSampleGroupDO" >
    update tb_test_sample_group
    set SampleGroupID = #{sampleGroupID,jdbcType=VARCHAR},
      SampleID = #{sampleID,jdbcType=VARCHAR},
      MainMaterialFlag = #{mainMaterialFlag,jdbcType=BIT},
      `Sequence` = #{sequence,jdbcType=INTEGER},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_test_sample_group
      (`ID`,`SampleGroupID`,`SampleID`,
      `MainMaterialFlag`,`Sequence`,`ActiveIndicator`,
      `CreatedDate`,`CreatedBy`,`ModifiedDate`,
      `ModifiedBy`,`LastModifiedTimestamp`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.ID, jdbcType=VARCHAR},#{ item.sampleGroupID, jdbcType=VARCHAR},#{ item.sampleID, jdbcType=VARCHAR},
      #{ item.mainMaterialFlag, jdbcType=BIT},#{ item.sequence, jdbcType=INTEGER},#{ item.activeIndicator, jdbcType=BIT},
      #{ item.createdDate, jdbcType=TIMESTAMP},#{ item.createdBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP},
      #{ item.modifiedBy, jdbcType=VARCHAR},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_test_sample_group 
      <set>
        <if test="item.sampleGroupID != null"> 
          `SampleGroupID` = #{item.sampleGroupID, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleID != null"> 
          `SampleID` = #{item.sampleID, jdbcType = VARCHAR},
        </if> 
        <if test="item.mainMaterialFlag != null"> 
          `MainMaterialFlag` = #{item.mainMaterialFlag, jdbcType = BIT},
        </if> 
        <if test="item.sequence != null"> 
          `Sequence` = #{item.sequence, jdbcType = INTEGER},
        </if> 
        <if test="item.activeIndicator != null"> 
          `ActiveIndicator` = #{item.activeIndicator, jdbcType = BIT},
        </if> 
        <if test="item.createdDate != null"> 
          `CreatedDate` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.createdBy != null"> 
          `CreatedBy` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `ModifiedDate` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `ModifiedBy` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `LastModifiedTimestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.ID != null">
           and `ID` = #{item.ID,jdbcType = VARCHAR}
        </if>
      </where>
    </foreach>
  </update>
</mapper>