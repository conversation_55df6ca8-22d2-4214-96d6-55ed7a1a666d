<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.soda.otsnotes.infrastructure.database.preorder.mapper.generated.GeneralOrderDOMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.soda.otsnotes.infrastructure.database.preorder.dataobject.auto.GeneralOrderDO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="OrderNo" property="orderNo" jdbcType="VARCHAR" />
    <result column="OrderStatus" property="orderStatus" jdbcType="INTEGER" />
    <result column="CustomerCode" property="customerCode" jdbcType="VARCHAR" />
    <result column="CustomerName" property="customerName" jdbcType="VARCHAR" />
    <result column="CustomerGroupCode" property="customerGroupCode" jdbcType="VARCHAR" />
    <result column="CustomerGroupName" property="customerGroupName" jdbcType="VARCHAR" />
    <result column="LabId" property="labId" jdbcType="INTEGER" />
    <result column="LabCode" property="labCode" jdbcType="VARCHAR" />
    <result column="OrderLaboratoryID" property="orderLaboratoryID" jdbcType="INTEGER" />
    <result column="ConfirmMatrixDate" property="confirmMatrixDate" jdbcType="TIMESTAMP" />
    <result column="CustomerNameCn" property="customerNameCn" jdbcType="VARCHAR" />
    <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="LastModifiedTimestamp" property="lastModifiedTimestamp" jdbcType="TIMESTAMP" />
    <result column="ConclusionMode" property="conclusionMode" jdbcType="INTEGER" />
    <result column="ApplicantCustomerCode" property="applicantCustomerCode" jdbcType="VARCHAR" />
    <result column="ApplicantCustomerNameEn" property="applicantCustomerNameEn" jdbcType="VARCHAR" />
    <result column="ApplicantCustomerNameCN" property="applicantCustomerNameCN" jdbcType="VARCHAR" />
    <result column="ApplicantCustomerGroupCode" property="applicantCustomerGroupCode" jdbcType="VARCHAR" />
    <result column="ApplicantCustomerGroupName" property="applicantCustomerGroupName" jdbcType="VARCHAR" />
    <result column="ResponsibleTeamCode" property="responsibleTeamCode" jdbcType="VARCHAR" />
    <result column="CSName" property="CSName" jdbcType="VARCHAR" />
    <result column="SampleDescription" property="sampleDescription" jdbcType="VARCHAR" />
    <result column="ReturnSample" property="returnSample" jdbcType="SMALLINT" />
    <result column="TechnicalSupporter" property="technicalSupporter" jdbcType="VARCHAR" />
    <result column="SuffixNum" property="suffixNum" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, OrderNo, OrderStatus, CustomerCode, CustomerName, CustomerGroupCode, CustomerGroupName, 
    LabId, LabCode, OrderLaboratoryID, ConfirmMatrixDate, CustomerNameCn, ActiveIndicator, 
    CreatedDate, CreatedBy, ModifiedDate, ModifiedBy, LastModifiedTimestamp, ConclusionMode, 
    ApplicantCustomerCode, ApplicantCustomerNameEn, ApplicantCustomerNameCN, ApplicantCustomerGroupCode, 
    ApplicantCustomerGroupName, ResponsibleTeamCode, CSName, SampleDescription, ReturnSample, 
    TechnicalSupporter, SuffixNum
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.soda.otsnotes.infrastructure.database.preorder.dataobject.auto.GeneralOrderDOExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_general_order_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_general_order_instance
    where ID = #{ID,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_general_order_instance
    where ID = #{ID,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.soda.otsnotes.infrastructure.database.preorder.dataobject.auto.GeneralOrderDOExample" >
    delete from tb_general_order_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.soda.otsnotes.infrastructure.database.preorder.dataobject.auto.GeneralOrderDO" >
    insert into tb_general_order_instance (ID, OrderNo, OrderStatus, 
      CustomerCode, CustomerName, CustomerGroupCode, 
      CustomerGroupName, LabId, LabCode, 
      OrderLaboratoryID, ConfirmMatrixDate, 
      CustomerNameCn, ActiveIndicator, CreatedDate, 
      CreatedBy, ModifiedDate, ModifiedBy, 
      LastModifiedTimestamp, ConclusionMode, 
      ApplicantCustomerCode, ApplicantCustomerNameEn, 
      ApplicantCustomerNameCN, ApplicantCustomerGroupCode, 
      ApplicantCustomerGroupName, ResponsibleTeamCode, 
      CSName, SampleDescription, ReturnSample, 
      TechnicalSupporter, SuffixNum)
    values (#{ID,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{orderStatus,jdbcType=INTEGER}, 
      #{customerCode,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{customerGroupCode,jdbcType=VARCHAR}, 
      #{customerGroupName,jdbcType=VARCHAR}, #{labId,jdbcType=INTEGER}, #{labCode,jdbcType=VARCHAR}, 
      #{orderLaboratoryID,jdbcType=INTEGER}, #{confirmMatrixDate,jdbcType=TIMESTAMP}, 
      #{customerNameCn,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=BIT}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTimestamp,jdbcType=TIMESTAMP}, #{conclusionMode,jdbcType=INTEGER}, 
      #{applicantCustomerCode,jdbcType=VARCHAR}, #{applicantCustomerNameEn,jdbcType=VARCHAR}, 
      #{applicantCustomerNameCN,jdbcType=VARCHAR}, #{applicantCustomerGroupCode,jdbcType=VARCHAR}, 
      #{applicantCustomerGroupName,jdbcType=VARCHAR}, #{responsibleTeamCode,jdbcType=VARCHAR}, 
      #{CSName,jdbcType=VARCHAR}, #{sampleDescription,jdbcType=VARCHAR}, #{returnSample,jdbcType=SMALLINT}, 
      #{technicalSupporter,jdbcType=VARCHAR}, #{suffixNum,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.soda.otsnotes.infrastructure.database.preorder.dataobject.auto.GeneralOrderDO" >
    insert into tb_general_order_instance
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        ID,
      </if>
      <if test="orderNo != null" >
        OrderNo,
      </if>
      <if test="orderStatus != null" >
        OrderStatus,
      </if>
      <if test="customerCode != null" >
        CustomerCode,
      </if>
      <if test="customerName != null" >
        CustomerName,
      </if>
      <if test="customerGroupCode != null" >
        CustomerGroupCode,
      </if>
      <if test="customerGroupName != null" >
        CustomerGroupName,
      </if>
      <if test="labId != null" >
        LabId,
      </if>
      <if test="labCode != null" >
        LabCode,
      </if>
      <if test="orderLaboratoryID != null" >
        OrderLaboratoryID,
      </if>
      <if test="confirmMatrixDate != null" >
        ConfirmMatrixDate,
      </if>
      <if test="customerNameCn != null" >
        CustomerNameCn,
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="createdBy != null" >
        CreatedBy,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy,
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp,
      </if>
      <if test="conclusionMode != null" >
        ConclusionMode,
      </if>
      <if test="applicantCustomerCode != null" >
        ApplicantCustomerCode,
      </if>
      <if test="applicantCustomerNameEn != null" >
        ApplicantCustomerNameEn,
      </if>
      <if test="applicantCustomerNameCN != null" >
        ApplicantCustomerNameCN,
      </if>
      <if test="applicantCustomerGroupCode != null" >
        ApplicantCustomerGroupCode,
      </if>
      <if test="applicantCustomerGroupName != null" >
        ApplicantCustomerGroupName,
      </if>
      <if test="responsibleTeamCode != null" >
        ResponsibleTeamCode,
      </if>
      <if test="CSName != null" >
        CSName,
      </if>
      <if test="sampleDescription != null" >
        SampleDescription,
      </if>
      <if test="returnSample != null" >
        ReturnSample,
      </if>
      <if test="technicalSupporter != null" >
        TechnicalSupporter,
      </if>
      <if test="suffixNum != null" >
        SuffixNum,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ID != null" >
        #{ID,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null" >
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null" >
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupCode != null" >
        #{customerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupName != null" >
        #{customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=INTEGER},
      </if>
      <if test="labCode != null" >
        #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="orderLaboratoryID != null" >
        #{orderLaboratoryID,jdbcType=INTEGER},
      </if>
      <if test="confirmMatrixDate != null" >
        #{confirmMatrixDate,jdbcType=TIMESTAMP},
      </if>
      <if test="customerNameCn != null" >
        #{customerNameCn,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="conclusionMode != null" >
        #{conclusionMode,jdbcType=INTEGER},
      </if>
      <if test="applicantCustomerCode != null" >
        #{applicantCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="applicantCustomerNameEn != null" >
        #{applicantCustomerNameEn,jdbcType=VARCHAR},
      </if>
      <if test="applicantCustomerNameCN != null" >
        #{applicantCustomerNameCN,jdbcType=VARCHAR},
      </if>
      <if test="applicantCustomerGroupCode != null" >
        #{applicantCustomerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="applicantCustomerGroupName != null" >
        #{applicantCustomerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="responsibleTeamCode != null" >
        #{responsibleTeamCode,jdbcType=VARCHAR},
      </if>
      <if test="CSName != null" >
        #{CSName,jdbcType=VARCHAR},
      </if>
      <if test="sampleDescription != null" >
        #{sampleDescription,jdbcType=VARCHAR},
      </if>
      <if test="returnSample != null" >
        #{returnSample,jdbcType=SMALLINT},
      </if>
      <if test="technicalSupporter != null" >
        #{technicalSupporter,jdbcType=VARCHAR},
      </if>
      <if test="suffixNum != null" >
        #{suffixNum,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.soda.otsnotes.infrastructure.database.preorder.dataobject.auto.GeneralOrderDOExample" resultType="java.lang.Integer" >
    select count(*) from tb_general_order_instance
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_general_order_instance
    <set >
      <if test="record.ID != null" >
        ID = #{record.ID,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null" >
        OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderStatus != null" >
        OrderStatus = #{record.orderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.customerCode != null" >
        CustomerCode = #{record.customerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.customerName != null" >
        CustomerName = #{record.customerName,jdbcType=VARCHAR},
      </if>
      <if test="record.customerGroupCode != null" >
        CustomerGroupCode = #{record.customerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.customerGroupName != null" >
        CustomerGroupName = #{record.customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.labId != null" >
        LabId = #{record.labId,jdbcType=INTEGER},
      </if>
      <if test="record.labCode != null" >
        LabCode = #{record.labCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orderLaboratoryID != null" >
        OrderLaboratoryID = #{record.orderLaboratoryID,jdbcType=INTEGER},
      </if>
      <if test="record.confirmMatrixDate != null" >
        ConfirmMatrixDate = #{record.confirmMatrixDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.customerNameCn != null" >
        CustomerNameCn = #{record.customerNameCn,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="record.conclusionMode != null" >
        ConclusionMode = #{record.conclusionMode,jdbcType=INTEGER},
      </if>
      <if test="record.applicantCustomerCode != null" >
        ApplicantCustomerCode = #{record.applicantCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.applicantCustomerNameEn != null" >
        ApplicantCustomerNameEn = #{record.applicantCustomerNameEn,jdbcType=VARCHAR},
      </if>
      <if test="record.applicantCustomerNameCN != null" >
        ApplicantCustomerNameCN = #{record.applicantCustomerNameCN,jdbcType=VARCHAR},
      </if>
      <if test="record.applicantCustomerGroupCode != null" >
        ApplicantCustomerGroupCode = #{record.applicantCustomerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.applicantCustomerGroupName != null" >
        ApplicantCustomerGroupName = #{record.applicantCustomerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.responsibleTeamCode != null" >
        ResponsibleTeamCode = #{record.responsibleTeamCode,jdbcType=VARCHAR},
      </if>
      <if test="record.CSName != null" >
        CSName = #{record.CSName,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleDescription != null" >
        SampleDescription = #{record.sampleDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.returnSample != null" >
        ReturnSample = #{record.returnSample,jdbcType=SMALLINT},
      </if>
      <if test="record.technicalSupporter != null" >
        TechnicalSupporter = #{record.technicalSupporter,jdbcType=VARCHAR},
      </if>
      <if test="record.suffixNum != null" >
        SuffixNum = #{record.suffixNum,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_general_order_instance
    set ID = #{record.ID,jdbcType=VARCHAR},
      OrderNo = #{record.orderNo,jdbcType=VARCHAR},
      OrderStatus = #{record.orderStatus,jdbcType=INTEGER},
      CustomerCode = #{record.customerCode,jdbcType=VARCHAR},
      CustomerName = #{record.customerName,jdbcType=VARCHAR},
      CustomerGroupCode = #{record.customerGroupCode,jdbcType=VARCHAR},
      CustomerGroupName = #{record.customerGroupName,jdbcType=VARCHAR},
      LabId = #{record.labId,jdbcType=INTEGER},
      LabCode = #{record.labCode,jdbcType=VARCHAR},
      OrderLaboratoryID = #{record.orderLaboratoryID,jdbcType=INTEGER},
      ConfirmMatrixDate = #{record.confirmMatrixDate,jdbcType=TIMESTAMP},
      CustomerNameCn = #{record.customerNameCn,jdbcType=VARCHAR},
      ActiveIndicator = #{record.activeIndicator,jdbcType=BIT},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{record.createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{record.modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{record.lastModifiedTimestamp,jdbcType=TIMESTAMP},
      ConclusionMode = #{record.conclusionMode,jdbcType=INTEGER},
      ApplicantCustomerCode = #{record.applicantCustomerCode,jdbcType=VARCHAR},
      ApplicantCustomerNameEn = #{record.applicantCustomerNameEn,jdbcType=VARCHAR},
      ApplicantCustomerNameCN = #{record.applicantCustomerNameCN,jdbcType=VARCHAR},
      ApplicantCustomerGroupCode = #{record.applicantCustomerGroupCode,jdbcType=VARCHAR},
      ApplicantCustomerGroupName = #{record.applicantCustomerGroupName,jdbcType=VARCHAR},
      ResponsibleTeamCode = #{record.responsibleTeamCode,jdbcType=VARCHAR},
      CSName = #{record.CSName,jdbcType=VARCHAR},
      SampleDescription = #{record.sampleDescription,jdbcType=VARCHAR},
      ReturnSample = #{record.returnSample,jdbcType=SMALLINT},
      TechnicalSupporter = #{record.technicalSupporter,jdbcType=VARCHAR},
      SuffixNum = #{record.suffixNum,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.soda.otsnotes.infrastructure.database.preorder.dataobject.auto.GeneralOrderDO" >
    update tb_general_order_instance
    <set >
      <if test="orderNo != null" >
        OrderNo = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null" >
        OrderStatus = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null" >
        CustomerCode = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null" >
        CustomerName = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupCode != null" >
        CustomerGroupCode = #{customerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupName != null" >
        CustomerGroupName = #{customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="labId != null" >
        LabId = #{labId,jdbcType=INTEGER},
      </if>
      <if test="labCode != null" >
        LabCode = #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="orderLaboratoryID != null" >
        OrderLaboratoryID = #{orderLaboratoryID,jdbcType=INTEGER},
      </if>
      <if test="confirmMatrixDate != null" >
        ConfirmMatrixDate = #{confirmMatrixDate,jdbcType=TIMESTAMP},
      </if>
      <if test="customerNameCn != null" >
        CustomerNameCn = #{customerNameCn,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CreatedBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTimestamp != null" >
        LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="conclusionMode != null" >
        ConclusionMode = #{conclusionMode,jdbcType=INTEGER},
      </if>
      <if test="applicantCustomerCode != null" >
        ApplicantCustomerCode = #{applicantCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="applicantCustomerNameEn != null" >
        ApplicantCustomerNameEn = #{applicantCustomerNameEn,jdbcType=VARCHAR},
      </if>
      <if test="applicantCustomerNameCN != null" >
        ApplicantCustomerNameCN = #{applicantCustomerNameCN,jdbcType=VARCHAR},
      </if>
      <if test="applicantCustomerGroupCode != null" >
        ApplicantCustomerGroupCode = #{applicantCustomerGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="applicantCustomerGroupName != null" >
        ApplicantCustomerGroupName = #{applicantCustomerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="responsibleTeamCode != null" >
        ResponsibleTeamCode = #{responsibleTeamCode,jdbcType=VARCHAR},
      </if>
      <if test="CSName != null" >
        CSName = #{CSName,jdbcType=VARCHAR},
      </if>
      <if test="sampleDescription != null" >
        SampleDescription = #{sampleDescription,jdbcType=VARCHAR},
      </if>
      <if test="returnSample != null" >
        ReturnSample = #{returnSample,jdbcType=SMALLINT},
      </if>
      <if test="technicalSupporter != null" >
        TechnicalSupporter = #{technicalSupporter,jdbcType=VARCHAR},
      </if>
      <if test="suffixNum != null" >
        SuffixNum = #{suffixNum,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.soda.otsnotes.infrastructure.database.preorder.dataobject.auto.GeneralOrderDO" >
    update tb_general_order_instance
    set OrderNo = #{orderNo,jdbcType=VARCHAR},
      OrderStatus = #{orderStatus,jdbcType=INTEGER},
      CustomerCode = #{customerCode,jdbcType=VARCHAR},
      CustomerName = #{customerName,jdbcType=VARCHAR},
      CustomerGroupCode = #{customerGroupCode,jdbcType=VARCHAR},
      CustomerGroupName = #{customerGroupName,jdbcType=VARCHAR},
      LabId = #{labId,jdbcType=INTEGER},
      LabCode = #{labCode,jdbcType=VARCHAR},
      OrderLaboratoryID = #{orderLaboratoryID,jdbcType=INTEGER},
      ConfirmMatrixDate = #{confirmMatrixDate,jdbcType=TIMESTAMP},
      CustomerNameCn = #{customerNameCn,jdbcType=VARCHAR},
      ActiveIndicator = #{activeIndicator,jdbcType=BIT},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      CreatedBy = #{createdBy,jdbcType=VARCHAR},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      ModifiedBy = #{modifiedBy,jdbcType=VARCHAR},
      LastModifiedTimestamp = #{lastModifiedTimestamp,jdbcType=TIMESTAMP},
      ConclusionMode = #{conclusionMode,jdbcType=INTEGER},
      ApplicantCustomerCode = #{applicantCustomerCode,jdbcType=VARCHAR},
      ApplicantCustomerNameEn = #{applicantCustomerNameEn,jdbcType=VARCHAR},
      ApplicantCustomerNameCN = #{applicantCustomerNameCN,jdbcType=VARCHAR},
      ApplicantCustomerGroupCode = #{applicantCustomerGroupCode,jdbcType=VARCHAR},
      ApplicantCustomerGroupName = #{applicantCustomerGroupName,jdbcType=VARCHAR},
      ResponsibleTeamCode = #{responsibleTeamCode,jdbcType=VARCHAR},
      CSName = #{CSName,jdbcType=VARCHAR},
      SampleDescription = #{sampleDescription,jdbcType=VARCHAR},
      ReturnSample = #{returnSample,jdbcType=SMALLINT},
      TechnicalSupporter = #{technicalSupporter,jdbcType=VARCHAR},
      SuffixNum = #{suffixNum,jdbcType=VARCHAR}
    where ID = #{ID,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_general_order_instance
      (`ID`,`OrderNo`,`OrderStatus`,
      `CustomerCode`,`CustomerName`,`CustomerGroupCode`,
      `CustomerGroupName`,`LabId`,`LabCode`,
      `OrderLaboratoryID`,`ConfirmMatrixDate`,`CustomerNameCn`,
      `ActiveIndicator`,`CreatedDate`,`CreatedBy`,
      `ModifiedDate`,`ModifiedBy`,`LastModifiedTimestamp`,
      `ConclusionMode`,`ApplicantCustomerCode`,`ApplicantCustomerNameEn`,
      `ApplicantCustomerNameCN`,`ApplicantCustomerGroupCode`,`ApplicantCustomerGroupName`,
      `ResponsibleTeamCode`,`CSName`,`SampleDescription`,
      `ReturnSample`,`TechnicalSupporter`,`SuffixNum`
      )
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.ID, jdbcType=VARCHAR},#{ item.orderNo, jdbcType=VARCHAR},#{ item.orderStatus, jdbcType=INTEGER},
      #{ item.customerCode, jdbcType=VARCHAR},#{ item.customerName, jdbcType=VARCHAR},#{ item.customerGroupCode, jdbcType=VARCHAR},
      #{ item.customerGroupName, jdbcType=VARCHAR},#{ item.labId, jdbcType=INTEGER},#{ item.labCode, jdbcType=VARCHAR},
      #{ item.orderLaboratoryID, jdbcType=INTEGER},#{ item.confirmMatrixDate, jdbcType=TIMESTAMP},#{ item.customerNameCn, jdbcType=VARCHAR},
      #{ item.activeIndicator, jdbcType=BIT},#{ item.createdDate, jdbcType=TIMESTAMP},#{ item.createdBy, jdbcType=VARCHAR},
      #{ item.modifiedDate, jdbcType=TIMESTAMP},#{ item.modifiedBy, jdbcType=VARCHAR},#{ item.lastModifiedTimestamp, jdbcType=TIMESTAMP},
      #{ item.conclusionMode, jdbcType=INTEGER},#{ item.applicantCustomerCode, jdbcType=VARCHAR},#{ item.applicantCustomerNameEn, jdbcType=VARCHAR},
      #{ item.applicantCustomerNameCN, jdbcType=VARCHAR},#{ item.applicantCustomerGroupCode, jdbcType=VARCHAR},#{ item.applicantCustomerGroupName, jdbcType=VARCHAR},
      #{ item.responsibleTeamCode, jdbcType=VARCHAR},#{ item.CSName, jdbcType=VARCHAR},#{ item.sampleDescription, jdbcType=VARCHAR},
      #{ item.returnSample, jdbcType=SMALLINT},#{ item.technicalSupporter, jdbcType=VARCHAR},#{ item.suffixNum, jdbcType=VARCHAR}
      ) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_general_order_instance 
      <set>
        <if test="item.orderNo != null"> 
          `OrderNo` = #{item.orderNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.orderStatus != null"> 
          `OrderStatus` = #{item.orderStatus, jdbcType = INTEGER},
        </if> 
        <if test="item.customerCode != null"> 
          `CustomerCode` = #{item.customerCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.customerName != null"> 
          `CustomerName` = #{item.customerName, jdbcType = VARCHAR},
        </if> 
        <if test="item.customerGroupCode != null"> 
          `CustomerGroupCode` = #{item.customerGroupCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.customerGroupName != null"> 
          `CustomerGroupName` = #{item.customerGroupName, jdbcType = VARCHAR},
        </if> 
        <if test="item.labId != null"> 
          `LabId` = #{item.labId, jdbcType = INTEGER},
        </if> 
        <if test="item.labCode != null"> 
          `LabCode` = #{item.labCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.orderLaboratoryID != null"> 
          `OrderLaboratoryID` = #{item.orderLaboratoryID, jdbcType = INTEGER},
        </if> 
        <if test="item.confirmMatrixDate != null"> 
          `ConfirmMatrixDate` = #{item.confirmMatrixDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.customerNameCn != null"> 
          `CustomerNameCn` = #{item.customerNameCn, jdbcType = VARCHAR},
        </if> 
        <if test="item.activeIndicator != null"> 
          `ActiveIndicator` = #{item.activeIndicator, jdbcType = BIT},
        </if> 
        <if test="item.createdDate != null"> 
          `CreatedDate` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.createdBy != null"> 
          `CreatedBy` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `ModifiedDate` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `ModifiedBy` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.lastModifiedTimestamp != null"> 
          `LastModifiedTimestamp` = #{item.lastModifiedTimestamp, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.conclusionMode != null"> 
          `ConclusionMode` = #{item.conclusionMode, jdbcType = INTEGER},
        </if> 
        <if test="item.applicantCustomerCode != null"> 
          `ApplicantCustomerCode` = #{item.applicantCustomerCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.applicantCustomerNameEn != null"> 
          `ApplicantCustomerNameEn` = #{item.applicantCustomerNameEn, jdbcType = VARCHAR},
        </if> 
        <if test="item.applicantCustomerNameCN != null"> 
          `ApplicantCustomerNameCN` = #{item.applicantCustomerNameCN, jdbcType = VARCHAR},
        </if> 
        <if test="item.applicantCustomerGroupCode != null"> 
          `ApplicantCustomerGroupCode` = #{item.applicantCustomerGroupCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.applicantCustomerGroupName != null"> 
          `ApplicantCustomerGroupName` = #{item.applicantCustomerGroupName, jdbcType = VARCHAR},
        </if> 
        <if test="item.responsibleTeamCode != null"> 
          `ResponsibleTeamCode` = #{item.responsibleTeamCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.CSName != null"> 
          `CSName` = #{item.CSName, jdbcType = VARCHAR},
        </if> 
        <if test="item.sampleDescription != null"> 
          `SampleDescription` = #{item.sampleDescription, jdbcType = VARCHAR},
        </if> 
        <if test="item.returnSample != null"> 
          `ReturnSample` = #{item.returnSample, jdbcType = SMALLINT},
        </if> 
        <if test="item.technicalSupporter != null"> 
          `TechnicalSupporter` = #{item.technicalSupporter, jdbcType = VARCHAR},
        </if> 
        <if test="item.suffixNum != null"> 
          `SuffixNum` = #{item.suffixNum, jdbcType = VARCHAR},
        </if> 
      </set>
      <where>
        <if test="item.ID != null">
           and `ID` = #{item.ID,jdbcType = VARCHAR}
        </if>
      </where>
    </foreach>
  </update>
</mapper>