package com.sgs.soda.otsnotes.infrastructure.gateway.testsample.impl;

import com.sgs.soda.otsnotes.domain.testsample.gateway.TestSampleGateway;
import com.sgs.soda.otsnotes.domain.testsample.model.TestSample;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * TestSampleGatewayImpl批量算法测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class TestSampleGatewayImplBatchTest {

    @Resource
    private TestSampleGateway testSampleGateway;

    /**
     * 测试批量查询性能
     */
    @Test
    public void testBatchPerformance() {
        // 模拟批量查询ID
        Set<String> testSampleIds = Set.of("test1", "test2", "test3");
        
        TestSampleGateway.TestSampleSubObjectOptions options = 
                TestSampleGateway.TestSampleSubObjectOptions.builder()
                        .includeGroupItems(true)
                        .includeLanguage(true)
                        .includeMaterial(true)
                        .includeAttachments(true)
                        .includeExtFields(true)
                        .build();

        long startTime = System.currentTimeMillis();
        
        // 执行批量查询
        List<TestSample> results = testSampleGateway.findByIds(testSampleIds, options);
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("批量查询耗时: " + (endTime - startTime) + "ms");
        System.out.println("查询结果数量: " + results.size());
        
        // 验证结果
        for (TestSample testSample : results) {
            System.out.println("TestSample ID: " + testSample.getId().getValue());
            if (testSample.getMaterial() != null) {
                System.out.println("  Material: " + testSample.getMaterial().getMaterialName());
                if (testSample.getMaterial().getLanguages() != null) {
                    System.out.println("  Languages count: " + testSample.getMaterial().getLanguages().size());
                }
            }
            if (testSample.getGroupItems() != null) {
                System.out.println("  Groups count: " + testSample.getGroupItems().size());
            }
        }
    }

    /**
     * 测试单个查询也使用批量算法
     */
    @Test
    public void testSingleQueryUsesBatchAlgorithm() {
        TestSampleGateway.TestSampleSubObjectOptions options = 
                TestSampleGateway.TestSampleSubObjectOptions.builder()
                        .includeGroupItems(true)
                        .includeLanguage(true)
                        .includeMaterial(true)
                        .build();

        // 单个查询应该内部转换为批量查询
        TestSample result = testSampleGateway.findById("test1", options);
        
        if (result != null) {
            System.out.println("单个查询结果: " + result.getId().getValue());
        } else {
            System.out.println("未找到测试数据");
        }
    }

    /**
     * 测试orderNo查询使用批量算法
     */
    @Test
    public void testOrderNoQueryUsesBatchAlgorithm() {
        TestSampleGateway.TestSampleSubObjectOptions options = 
                TestSampleGateway.TestSampleSubObjectOptions.builder()
                        .includeGroupItems(true)
                        .includeLanguage(true)
                        .includeMaterial(true)
                        .build();

        // orderNo查询应该使用批量组装算法
        List<TestSample> results = testSampleGateway.findByOrderNo("ORDER001", options);
        
        System.out.println("OrderNo查询结果数量: " + results.size());
        for (TestSample testSample : results) {
            System.out.println("TestSample ID: " + testSample.getId().getValue());
        }
    }
}