package com.sgs.soda.otsnotes.domain.testline.gateway;

import com.sgs.soda.otsnotes.domain.testline.model.TestlineInstance;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * TestlineInstance Domain Gateway
 */
public interface TestlineInstanceGateway {
    /**
     * 根据ID查找TestlineInstance
     *
     * @param testlineInstanceId TestlineInstance ID
     * @param includeSubObjects  控制是否包含子对象的配置
     * @return TestlineInstance领域对象
     */
    TestlineInstance findById(String testlineInstanceId, TestlineInstanceSubObjectOptions includeSubObjects);

    /**
     * 根据ID list查找TestlineInstance
     *
     * @param testlineInstanceIds TestlineInstance ID
     * @param includeSubObjects  控制是否包含子对象的配置
     * @return TestlineInstance领域对象
     */
    List<TestlineInstance> findByIds(Set<String> testlineInstanceIds, TestlineInstanceSubObjectOptions includeSubObjects);

    /**
     * 根据订单号查找TestlineInstance
     *
     * @param orderNo 订单号
     * @param includeSubObjects 控制是否包含子对象的配置
     * @return TestlineInstance领域对象列表
     */
    List<TestlineInstance> findByOrderNo(String orderNo, TestlineInstanceSubObjectOptions includeSubObjects);
    /**
     * 子对象查询选项类
     */
    @Data
    class TestlineInstanceSubObjectOptions {
        private boolean includeAnalyte = true;
        private boolean includeLabSection = true;
        private boolean includeTestConditionGroup = true;
        private boolean includePpTestline = true;
        private boolean includeCitation = true;
    }


} 