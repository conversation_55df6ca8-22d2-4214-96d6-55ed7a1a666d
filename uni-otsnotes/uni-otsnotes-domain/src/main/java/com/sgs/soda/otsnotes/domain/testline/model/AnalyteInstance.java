package com.sgs.soda.otsnotes.domain.testline.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 分析物实例领域实体
 */
@Data
public class AnalyteInstance {
    private String id;
    private Long analyteBaseId;
    private Integer analyteId;
    private String testAnalyteName;
    private Long unitBaseId;
    private String reportUnit;
    private String casNo;
    private Integer testAnalyteSeq;
    private Boolean activeIndicator;


    private LocalDateTime createdDate;
    private String createdBy;
    private LocalDateTime modifiedDate;
    private String modifiedBy;

    private List<AnalyteInstanceLanguage> languages;
}