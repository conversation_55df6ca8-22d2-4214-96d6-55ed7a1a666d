package com.sgs.soda.otsnotes.domain.common.valueobject.citation;

import lombok.Data;

import java.util.List;

@Data
public class Citation {

    /**
     * LocalTrims 测试标准标识
     */
    private Long citationBaseId;
    /**
     * Trims 测试标准标识
     */
    private Integer citationId;
    /**
     * Trims 测试标准版本标识
     */
    private Integer citationVersionId;
    /**
     * Trims 测试标准分类
     */
    private Integer citationType;
    private Integer citationSectionId;
    private String citationSectionName;
    /**
     * Trims 测试标准名称
     */
    private String citationName;
    /**
     * Trims 测试标准名称，拼接字段
     */
    private String citationFullName;
    private String methodDesc;
    /**
     * 语言信息
     */
    private List<CitationLanguage> languages;

}
