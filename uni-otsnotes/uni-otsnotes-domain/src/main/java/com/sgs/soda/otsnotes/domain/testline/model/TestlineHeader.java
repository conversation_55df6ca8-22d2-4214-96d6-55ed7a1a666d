package com.sgs.soda.otsnotes.domain.testline.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Testline Header - 封装TestlineInstance的基础属性
 */
@Data
public class TestlineHeader {

    private String orderNo;
    
    /**
     * 测试项类型
     */
    private Integer testLineType;
    /**
     * 测试项名称
     */
    private String evaluationAlias;
    private String evaluationName;
    /**
     * 测试项状态
     */
    private Integer testLineStatus;
    /**
     * 测试项顺序
     */
    private Integer testLineSeq;
    /**
     * 实验室团队
     */
    private String labTeam;
    /**
     * 业务线编码
     */
    private String productLineAbbr;
    private String testRequest;
    /**
     * 测试项备注
     */
    private String testLineRemark;
    /**
     * 切割要求
     **/
    private String cuttingRequest;
    /**
     * pending Flag
     */
    private Boolean pendingFlag;
    /**
     * 测试方法
     */
    private String testMethod;
    private String reportReferenceNotes;
    private String noOfReplication;
    private String engineer;
    private Date testDataCreateDate;
    private String sampleSegegrationWIText;
    private String sampleSegegrationWIID;
    /**
     * 扩展数据
     */
    private String extData;

    private Date createdDate;
    private String createdBy;
    private Date modifiedDate;
    private String modifiedBy;

    private List<TestlineLanguage> languages;


} 