package com.sgs.soda.otsnotes.domain.common.valueobject;

import java.util.Date;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class Attachment {
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件相对路径
     */
    private String cloudId;
    /**
     * 文件绝对路径
     */
    private String filePath;

    private String description;

    // 区分附件来源
    private String source;

    
    private Date createdDate;
    private String createdBy;
    private Date modifiedDate;
    private String modifiedBy;
}
