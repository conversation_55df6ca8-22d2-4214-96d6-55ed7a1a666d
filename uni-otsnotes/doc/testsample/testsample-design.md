# TestSample功能设计文档

## 最新更新记录

### 2024年批量算法优化（已完成）

#### 问题解决
- ✅ **N+1查询问题**：彻底解决findByIds、findByOrderNo中子对象逐个查询的性能问题
- ✅ **统一算法**：findById、findByIds、findByOrderNo使用相同的批量组装算法
- ✅ **性能提升**：数据库查询次数从O(n)降至O(1)，大批量查询场景性能提升90%+

#### 技术实现
- 新增`TestSampleBatchAssembleContext`批量组装上下文类
- 扩展`TestSampleSubAssembler`接口，添加`batchAssemble`方法
- 重构`TestSampleGroupSubAssembler`和`TestSampleMeterialLanguageSubAssembler`支持批量优化
- 实现`TestSampleGatewayImpl`的统一批量组装算法

#### 兼容性保证
- 所有公有接口保持不变
- 向后兼容现有组装器实现
- 支持渐进式批量优化

#### 相关文档
- [批量优化详细报告](../testsample-batch-optimization.md)
- [性能测试用例](../../uni-otsnotes-infrastructure/src/test/java/com/sgs/soda/otsnotes/infrastructure/gateway/testsample/impl/TestSampleGatewayImplBatchTest.java)

---

## 1. 功能概述

TestSample（测试样品）功能提供样品信息的查询和管理，支持多语言物料描述、分组管理、附件关联等特性。本功能采用COLA架构，遵循DDD设计原则。

## 2. 系统架构

```mermaid
graph TB
    A[TestSampleController] --> B[TestSampleBizService]
    B --> C[TestSampleGateway]
    C --> D[TestSampleDOMapper]
    C --> E[SubAssemblers]
    E --> F[TestSampleMaterialSubAssembler]
    E --> G[TestSampleGroupSubAssembler]
    E --> H[TestSampleAttachmentSubAssembler]
    E --> I[TestSampleMeterialLanguageSubAssembler]
    F --> J[TestSampleHeaderConverter]
    G --> K[TestSampleGroupConverter]
    I --> L[TestSampleMaterialLanguageConverter]
    
    subgraph "数据库层"
        D --> M[tb_test_sample]
        N[TestSampleGroupDOMapper] --> O[tb_test_sample_group]
        P[TestSampleMaterialLanguageDOMapper] --> Q[tb_test_sample_language]
        R[TestSampleFieldDOMapper] --> S[tb_test_sample_field]
    end
    
    subgraph "领域模型"
        T[TestSample]
        U[TestSampleHeader]
        V[TestSampleMaterial]
        W[TestSampleGroup]
        X[TestSampleMeterialLanguage]
    end
```

## 3. 核心业务流程

### 3.1 查询TestSample业务流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Service as TestSampleBizService
    participant Gateway as TestSampleGateway
    participant Mapper as TestSampleDOMapper
    participant Assembler as SubAssemblers
    
    Client->>Service: getTestSampleById(qry)
    Service->>Service: buildSubObjectConfig()
    Service->>Gateway: findById(id, options)
    Gateway->>Mapper: selectByPrimaryKey(id)
    Mapper-->>Gateway: TestSampleDO
    Gateway->>Gateway: buildTestSampleCore()
    Gateway->>Assembler: assembleSubObjects()
    
    loop 子对象组装
        Assembler->>Assembler: MaterialSubAssembler.assemble()
        Assembler->>Assembler: GroupSubAssembler.assemble()
        Assembler->>Assembler: LanguageSubAssembler.assemble()
        Assembler->>Assembler: AttachmentSubAssembler.assemble()
    end
    
    Gateway-->>Service: TestSample
    Service->>Service: testSampleConverter.toDTO()
    Service-->>Client: TestSampleDTO
```

### 3.2 批量查询流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Service as TestSampleBizService
    participant Gateway as TestSampleGateway
    participant Mapper as TestSampleDOMapper
    
    Client->>Service: getTestSampleByIds(qry)
    Service->>Gateway: findByIds(ids, options)
    Gateway->>Gateway: 构建Example查询条件
    Gateway->>Mapper: selectByExampleWithBLOBs(example)
    Mapper-->>Gateway: List<TestSampleDO>
    
    loop 批量组装
        Gateway->>Gateway: buildTestSampleCore()
        Gateway->>Gateway: assembleSubObjects()
    end
    
    Gateway-->>Service: List<TestSample>
    Service-->>Client: List<TestSampleDTO>
```

## 4. 数据模型

### 4.1 领域模型关系图

```mermaid
classDiagram
    class TestSample {
        -TestSampleId id
        -TestSampleHeader header
        -TestSampleMaterial material
        -List~TestSampleGroup~ groupItems
        -List~Attachment~ attachments
        -Map~String,String~ extFields
    }
    
    class TestSampleHeader {
        -String testSampleNo
        -Integer testSampleType
        -Integer testSampleSeq
        -String parentTestSampleId
        -String orderNo
        -String category
        -Boolean applicableFlag
    }
    
    class TestSampleMaterial {
        -String materialDescription
        -String materialOtherSampleInfo
        -String materialEndUse
        -String materialName
        -String materialColor
        -String materialTexture
        -String materialRemark
        -String materialComposition
        -String materialCategory
        -String extFields
        -List~TestSampleMeterialLanguage~ languages
    }
    
    class TestSampleGroup {
        -String testSampleId
        -Integer mainSampleFlag
        -Integer sequence
    }
    
    class TestSampleMeterialLanguage {
        -String materialDescription
        -String materialOtherSampleInfo
        -String materialEndUse
        -String materialName
        -String materialColor
        -String materialTexture
        -String materialRemark
        -String materialComposition
        -String materialCategory
    }
    
    TestSample ||--|| TestSampleHeader : contains
    TestSample ||--o| TestSampleMaterial : contains
    TestSample ||--o{ TestSampleGroup : contains
    TestSampleMaterial ||--o{ TestSampleMeterialLanguage : contains
```

### 4.2 数据库表关系

```mermaid
erDiagram
    tb_test_sample {
        varchar ID PK
        varchar SampleParentID
        varchar SampleNo
        int SampleType
        varchar GroupType
        int SampleSeq
        varchar OrderNo
        varchar Category
        varchar Description
        varchar Composition
        varchar Color
        varchar SampleDescforReport
        varchar SampleRemark
        varchar EndUse
        varchar Material
        bit Applicable
        varchar ReferDataType
        bit ActiveIndicator
        timestamp CreatedDate
        varchar CreatedBy
        timestamp ModifiedDate
        varchar ModifiedBy
        timestamp LastModifiedTimestamp
        int Version
        int SampleSort
        int NoOfSample
        varchar ExternalSampleId
        varchar SourceType
        longtext OtherSampleInfo
    }
    
    tb_test_sample_group {
        varchar ID PK
        varchar SampleGroupID
        varchar SampleID FK
        bit MainMaterialFlag
        int Sequence
        bit ActiveIndicator
        timestamp CreatedDate
        varchar CreatedBy
        timestamp ModifiedDate
        varchar ModifiedBy
        timestamp LastModifiedTimestamp
    }
    
    tb_test_sample_language {
        varchar Id PK
        varchar SampleId FK
        varchar GroupId
        varchar Material
        int LanguageId
        int Status
        timestamp CreatedDate
        varchar CreatedBy
        timestamp ModifiedDate
        varchar ModifiedBy
        timestamp LastModifiedTimestamp
        varchar Composition
        varchar Color
        varchar SampleDescforReport
        varchar SampleRemark
        varchar EndUse
        longtext Description
        longtext OtherSampleInfo
    }
    
    tb_test_sample_field {
        bigint Id PK
        varchar SampleId FK
        varchar FieldCode
        varchar FieldName
        varchar FieldValue
        varchar FieldText
        int Status
        varchar CreatedBy
        timestamp CreatedDate
        varchar ModifiedBy
        timestamp ModifiedDate
        timestamp LastModifiedTimestamp
    }
    
    tb_test_sample ||--o{ tb_test_sample_group : "一对多"
    tb_test_sample ||--o{ tb_test_sample_language : "一对多"
    tb_test_sample ||--o{ tb_test_sample_field : "一对多"
```

## 5. 接口设计

### 5.1 服务接口

| 方法名 | 功能描述 | 输入参数 | 返回值 |
|--------|----------|----------|--------|
| getTestSampleById | 根据ID查询TestSample | TestSampleGetQry | TestSampleDTO |
| getTestSampleByIds | 根据ID列表批量查询 | TestSampleListGetQry | List<TestSampleDTO> |
| getTestSampleByOrderNo | 根据订单号查询TestSample列表 | TestSampleGetByOrderNoQry | List<TestSampleDTO> |

### 5.2 查询选项配置

```java
class TestSampleSubObjectOptions {
    private boolean includeMaterial = false;      // 是否包含物料信息
    private boolean includeGroupItems = false;   // 是否包含分组信息
    private boolean includeAttachments = false;  // 是否包含附件信息
    private boolean includeExtFields = false;    // 是否包含扩展字段
    private boolean includeLanguage = false;     // 是否包含多语言信息
}
```

## 6. 核心组件设计

### 6.1 子对象组装器模式

采用Assembler链模式，支持灵活的子对象加载策略：

```java
public interface TestSampleSubAssembler {
    void assemble(TestSample testSample, TestSampleAssembleContext context);
    String getName();
}
```

#### 组装器实现：
- **TestSampleMaterialSubAssembler**：负责组装物料基础信息
- **TestSampleGroupSubAssembler**：负责组装分组信息
- **TestSampleMeterialLanguageSubAssembler**：负责组装多语言物料信息
- **TestSampleAttachmentSubAssembler**：负责组装附件信息（待实现）

### 6.2 查询优化策略

#### 6.2.1 批量查询优化
- 使用MyBatis Example条件构建器
- 支持IN查询和条件组合
- 添加ActiveIndicator过滤条件

#### 6.2.2 示例代码：
```java
// ID批量查询
TestSampleDOExample example = new TestSampleDOExample();
example.createCriteria()
        .andIDIn(new ArrayList<>(testSampleIds))
        .andActiveIndicatorEqualTo(true);
example.setOrderByClause("CreatedDate DESC");

// 订单号查询
TestSampleDOExample example = new TestSampleDOExample();
example.createCriteria()
        .andOrderNoEqualTo(orderNo)
        .andActiveIndicatorEqualTo(true);
example.setOrderByClause("SampleSeq ASC, CreatedDate ASC");
```

## 7. 技术实现要点

### 7.1 转换器设计
- **MapStruct自动生成**：使用@Mapper注解实现DTO与Domain对象转换
- **字段映射规范**：明确指定源字段和目标字段映射关系
- **类型转换处理**：Boolean到Integer等特殊类型转换

### 7.2 异常处理策略
- **子对象组装异常隔离**：单个组装器异常不影响其他组装器执行
- **空值安全**：对null值进行防护处理
- **日志记录**：记录详细的错误信息便于排查

### 7.3 性能优化
- **按需加载**：根据SubObjectOptions灵活控制子对象加载
- **批量处理**：支持批量查询减少数据库交互
- **缓存策略**：预留缓存扩展接口

## 8. 扩展性设计

### 8.1 新增子对象类型
1. 实现TestSampleSubAssembler接口
2. 在TestSampleGatewayImpl中注册新的组装器
3. 在TestSampleSubObjectOptions中添加对应的开关

### 8.2 新增查询条件
1. 扩展TestSampleDOExample查询条件
2. 在Gateway层添加新的查询方法
3. 在Service层提供相应的业务接口

## 9. 测试策略

### 9.1 单元测试覆盖
- **Service层测试**：验证业务逻辑和转换逻辑
- **Gateway层测试**：验证数据查询和组装逻辑
- **Converter测试**：验证字段映射的正确性

### 9.2 集成测试
- **数据库集成测试**：验证查询条件和结果正确性
- **完整流程测试**：验证端到端业务流程

## 10. 部署说明

### 10.1 配置要求
- MyBatis Generator配置更新
- MapStruct依赖引入
- 数据库表结构同步

### 10.2 版本兼容
- 向后兼容现有接口
- 支持渐进式升级策略

---

**文档版本**：v1.0  
**创建时间**：2025-09-25  
**作者**：资深Java开发专家