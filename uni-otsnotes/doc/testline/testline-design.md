# TestLine批量查询优化设计文档

## 1. 项目概述

### 1.1 改造背景
- **问题描述**: TestlineInstance原有的查询实现存在N+1查询问题，严重影响系统性能
- **解决方案**: 参考TestSample的批量处理架构，实现TestlineInstance的批量查询优化
- **技术目标**: 
  - 解决N+1查询问题，提升查询性能
  - 新增基于orderNo的查询功能
  - 保持架构一致性和代码可维护性

### 1.2 技术选型
- **架构模式**: Assembler模式 + 批量优化
- **核心技术**: MyBatis Example查询 + Stream批量处理
- **设计原则**: 单一职责、开闭原则、批量优化

## 2. 系统架构设计

### 2.1 核心架构图

```mermaid
graph TB
    A[TestlineInstanceGateway] --> B[TestlineInstanceGatewayImpl]
    B --> C[批量组装算法]
    C --> D[TestlineBatchAssembleContext]
    C --> E[TestlineSubAssembler接口]
    E --> F[AnalyteInstanceSubAssembler]
    E --> G[LabSectionInstanceSubAssembler]
    E --> H[PpTestlineSubAssembler]
    E --> I[CitationSubAssembler]
    E --> J[TestlineInstanceLanguageSubAssembler]
    
    F --> K[批量查询AnalyteInstance]
    G --> L[批量查询LabSection]
    H --> M[批量查询PpTestline]
    I --> N[批量查询Citation]
    J --> O[批量查询Language]
    
    K --> P[内存分组装配]
    L --> P
    M --> P
    N --> P
    O --> P
```

### 2.2 批量查询流程时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as TestlineInstanceGateway
    participant Impl as TestlineInstanceGatewayImpl
    participant Context as BatchAssembleContext
    participant Assembler as SubAssembler
    participant Mapper as MyBatis Mapper

    Client->>Gateway: findByOrderNo(orderNo)
    Gateway->>Impl: 调用实现类
    
    Note over Impl: 1. 主表批量查询
    Impl->>Mapper: 使用Example批量查询TestlineInstanceDO
    Mapper-->>Impl: 返回DO列表
    
    Note over Impl: 2. 核心对象构建
    Impl->>Impl: 构建TestlineInstance核心对象列表
    
    Note over Impl: 3. 批量上下文创建
    Impl->>Context: 创建BatchAssembleContext
    Context-->>Impl: 返回批量上下文
    
    Note over Impl: 4. 子对象批量组装
    loop 每个SubAssembler
        Impl->>Assembler: batchAssemble()
        
        Note over Assembler: 4.1 批量查询子对象
        Assembler->>Mapper: 批量查询所有子对象
        Mapper-->>Assembler: 返回子对象DO列表
        
        Note over Assembler: 4.2 内存分组
        Assembler->>Assembler: 按主对象ID分组
        
        Note over Assembler: 4.3 批量装配
        Assembler->>Assembler: 为每个主对象装配子对象
        
        Assembler-->>Impl: 组装完成
    end
    
    Impl-->>Gateway: 返回完整对象列表
    Gateway-->>Client: 返回结果
```

### 2.3 类关系图

```mermaid
classDiagram
    class TestlineInstanceGateway {
        <<interface>>
        +findById(String, SubObjectOptions) TestlineInstance
        +findByIds(Set~String~, SubObjectOptions) List~TestlineInstance~
        +findByOrderNo(String, SubObjectOptions) List~TestlineInstance~
    }
    
    class TestlineInstanceGatewayImpl {
        -testlineInstanceMapper: TestlineInstanceDOMapper
        -subAssemblers: List~TestlineSubAssembler~
        +findById() TestlineInstance
        +findByIds() List~TestlineInstance~
        +findByOrderNo() List~TestlineInstance~
        -batchAssembleTestlineInstances()
        -batchAssembleSubObjects()
    }
    
    class TestlineSubAssembler {
        <<interface>>
        +assemble(TestlineInstance, TestlineAssembleContext)
        +batchAssemble(List~TestlineInstance~, TestlineBatchAssembleContext)
        +supportsBatchOptimization() boolean
        +getName() String
    }
    
    class TestlineBatchAssembleContext {
        -testlineInstanceIds: Set~String~
        -subObjectOptions: SubObjectOptions
        -testlineInstanceDOList: List~TestlineInstanceDO~
        -testlineInstanceDOMap: Map~String,TestlineInstanceDO~
        +create() TestlineBatchAssembleContext
        +getTestlineInstanceDOById() TestlineInstanceDO
    }
    
    class AnalyteInstanceSubAssembler {
        +assemble()
        +batchAssemble()
        +supportsBatchOptimization() true
        +getName() String
    }
    
    TestlineInstanceGateway <|-- TestlineInstanceGatewayImpl
    TestlineInstanceGatewayImpl --> TestlineSubAssembler
    TestlineInstanceGatewayImpl --> TestlineBatchAssembleContext
    TestlineSubAssembler <|-- AnalyteInstanceSubAssembler
```

## 3. 核心组件设计

### 3.1 TestlineSubAssembler接口扩展

**职责**: 定义子对象组装器的标准接口，支持单个和批量组装模式

**关键方法**:
- `assemble()`: 单个对象组装（向后兼容）
- `batchAssemble()`: 批量对象组装（性能优化）
- `supportsBatchOptimization()`: 标识是否支持批量优化

**设计优势**:
- 保持向后兼容性
- 支持渐进式批量优化
- 遵循开闭原则

### 3.2 TestlineBatchAssembleContext批量上下文

**职责**: 承载批量组装所需的上下文信息和缓存

**核心属性**:
- `testlineInstanceIds`: 所有ID集合（用于批量查询）
- `subObjectOptions`: 子对象加载选项
- `testlineInstanceDOMap`: ID到DO的快速映射

**性能优化**:
- 避免重复查询主表数据
- 提供快速ID查找能力
- 支持子对象按需加载

### 3.3 批量组装算法

**核心流程**:
1. **主表批量查询**: 使用Example一次性查询所有主表数据
2. **核心对象构建**: Stream并行构建领域对象列表
3. **批量上下文创建**: 构建包含所有必要信息的上下文对象
4. **子对象批量组装**: 遍历组装器，执行批量优化组装

**性能关键点**:
- 减少数据库查询次数（N+1 → 1+子对象数量）
- 内存中分组装配，避免重复查询
- 异常隔离，单个组装器失败不影响整体流程

## 4. 数据模型设计

### 4.1 查询条件映射

| 查询方法 | Example构建 | 排序规则 | 过滤条件 |
|---------|-------------|----------|----------|
| findById | ID等值查询 | CreatedDate DESC | ActiveIndicator=true |
| findByIds | ID IN查询 | CreatedDate DESC | ActiveIndicator=true |
| findByOrderNo | OrderNo等值查询 | TestLineSeq ASC, CreatedDate ASC | ActiveIndicator=true |

### 4.2 子对象关联关系

```mermaid
erDiagram
    TestlineInstance ||--o{ AnalyteInstance : "hasMany"
    TestlineInstance ||--o{ LabSection : "hasMany"
    TestlineInstance ||--o{ PpTestline : "hasMany"
    TestlineInstance ||--o| Citation : "hasOne"
    TestlineInstance ||--o{ TestlineLanguage : "hasMany"
    
    AnalyteInstance ||--o{ AnalyteInstanceLanguage : "hasMany"
```

## 5. 批量优化策略

### 5.1 查询优化

**优化前**:
```java
// N+1查询问题
for (TestlineInstance instance : instances) {
    List<AnalyteInstance> analytes = queryAnalytesByTestlineId(instance.getId());
    for (AnalyteInstance analyte : analytes) {
        List<Language> languages = queryLanguagesByAnalyteId(analyte.getId());
    }
}
```

**优化后**:
```java
// 批量查询
Set<String> testlineIds = instances.stream().map(TestlineInstance::getId).collect(toSet());
List<AnalyteInstance> allAnalytes = queryAnalytesByTestlineIds(testlineIds);
Set<String> analyteIds = allAnalytes.stream().map(AnalyteInstance::getId).collect(toSet());
List<Language> allLanguages = queryLanguagesByAnalyteIds(analyteIds);

// 内存分组装配
Map<String, List<AnalyteInstance>> analytesByTestlineId = groupBy(allAnalytes, AnalyteInstance::getTestlineId);
Map<String, List<Language>> languagesByAnalyteId = groupBy(allLanguages, Language::getAnalyteId);
```

### 5.2 性能对比

| 场景 | 优化前 | 优化后 | 性能提升 |
|------|--------|--------|----------|
| 10个TestlineInstance + 50个AnalyteInstance | 61次查询 | 3次查询 | 95% ↓ |
| 100个TestlineInstance + 500个AnalyteInstance | 601次查询 | 3次查询 | 99% ↓ |

## 6. 代码实现关键点

### 6.1 Example查询构建

```java
// OrderNo查询示例
TestlineInstanceDOExample example = new TestlineInstanceDOExample();
example.createCriteria()
        .andOrderNoEqualTo(orderNo)
        .andActiveIndicatorEqualTo(true);
example.setOrderByClause("TestLineSeq ASC, CreatedDate ASC");
```

### 6.2 批量分组装配

```java
// 按testlineInstanceId分组
Map<String, List<AnalyteInstanceDO>> analytesByTestlineInstanceId = 
    allAnalyteInstanceDOs.stream()
        .filter(Objects::nonNull)
        .filter(analyte -> analyte.getTestLineInstanceID() != null)
        .collect(Collectors.groupingBy(AnalyteInstanceDO::getTestLineInstanceID));
```

### 6.3 异常处理

```java
for (TestlineSubAssembler subAssembler : subAssemblers) {
    try {
        if (subAssembler.supportsBatchOptimization()) {
            subAssembler.batchAssemble(testlineInstanceList, batchContext);
        } else {
            subAssembler.batchAssemble(testlineInstanceList, batchContext);
        }
    } catch (Exception e) {
        log.error("批量子对象组装失败: {}", subAssembler.getName(), e);
        // 异常隔离，继续执行下一个组装器
    }
}
```

## 7. 扩展性设计

### 7.1 新增子对象组装器

1. 实现`TestlineSubAssembler`接口
2. 重写`batchAssemble()`方法实现批量优化
3. 设置`supportsBatchOptimization()`返回true
4. 在`TestlineInstanceGatewayImpl`中注册

### 7.2 新增查询方法

1. 在`TestlineInstanceGateway`接口中定义方法
2. 在`TestlineInstanceGatewayImpl`中实现
3. 使用Example构建查询条件
4. 复用`batchAssembleTestlineInstances()`方法

## 8. 测试策略

### 8.1 单元测试

- **组装器测试**: 验证单个和批量组装逻辑
- **Gateway测试**: 验证查询方法和批量算法
- **性能测试**: 对比优化前后的查询次数

### 8.2 集成测试

- **数据一致性**: 验证批量查询与单个查询结果一致
- **异常处理**: 验证组装器异常不影响整体流程
- **边界条件**: 空数据、大数据量场景测试

## 9. 运维监控

### 9.1 性能监控

- **查询耗时**: 监控各查询方法的响应时间
- **数据库查询**: 监控SQL执行次数和耗时
- **内存使用**: 监控批量处理的内存消耗

### 9.2 日志策略

- **DEBUG级别**: 记录批量组装的详细过程
- **ERROR级别**: 记录组装器异常信息
- **WARN级别**: 记录性能异常或数据异常

## 10. getTestlineInstanceByOrderNo 实现设计

### 10.1 业务需求
- **功能描述**: 根据订单号查询TestlineInstance列表，支持子对象按需加载
- **技术要求**: 复用批量优化算法，避免N+1查询问题
- **接口层次**: 从BizService到Controller的完整调用链

### 10.2 实现架构

```mermaid
graph TB
    A[TestlineInstanceController] --> B[TestlineInstanceBizService]
    B --> C[TestlineInstanceGateway]
    C --> D[批量组装算法复用]
    D --> E[TestlineSubAssemblers]
    
    F[TestlineInstanceGetByOrderNoQry] --> A
    G[子对象选项配置] --> B
    H[findByOrderNo方法] --> C
```

### 10.3 关键组件

#### 10.3.1 查询DTO设计
**TestlineInstanceGetByOrderNoQry**:
- 继承BaseRequest
- orderNo: 订单号（必填，@NotBlank验证）
- 子对象加载选项（与其他查询方法保持一致）
  - includeTestlineLanguage: 多语言信息
  - includeAnalyte: 分析物信息  
  - includeLabSection: 实验室部门信息
  - includePpTestLine: PP测试线信息
  - includeCitation: 标准信息

#### 10.3.2 业务服务实现
**TestlineInstanceBizServiceImpl.getTestlineInstanceByOrderNo()**:
```java
@Override
public List<TestlineInstanceDTO> getTestlineInstanceByOrderNo(TestlineInstanceGetByOrderNoQry qry) {
    // 1. 构建子对象配置
    TestlineInstanceGateway.TestlineInstanceSubObjectOptions subObjectConfig = buildSubObjectConfig(qry);
    
    // 2. 调用domain层获取数据（复用批量算法）
    List<TestlineInstance> testlineInstanceList = testlineInstanceGateway.findByOrderNo(qry.getOrderNo(), subObjectConfig);
    
    // 3. 转换为DTO
    return testlineInstanceConverter.toDTOS(testlineInstanceList);
}
```

#### 10.3.3 Web控制器实现
**TestlineInstanceController**:
- **端点**: POST /uni/api/testline/instanceList/getByOrderNo
- **请求体**: TestlineInstanceGetByOrderNoQry
- **响应**: BaseResponse<List<TestlineInstanceDTO>>
- **验证**: @Valid注解确保参数校验

### 10.4 核心优势

#### 10.4.1 架构一致性
- **复用批量算法**: 与findById、findByIds共享同一套批量组装逻辑
- **统一接口设计**: 查询DTO结构与其他查询方法保持一致
- **标准异常处理**: 使用统一的日志和异常处理机制

#### 10.4.2 性能优化
- **批量查询**: 基于orderNo的Example查询，一次性获取所有相关TestlineInstance
- **子对象优化**: 自动应用所有子对象assembler的批量优化
- **内存效率**: 使用Stream和Map分组，避免重复查询

#### 10.4.3 业务价值
- **订单维度查询**: 支持按订单号获取所有相关测试线实例
- **灵活配置**: 支持子对象按需加载，减少不必要的数据传输
- **接口标准化**: 遵循RESTful设计和统一的响应格式

### 10.5 使用示例

#### 10.5.1 请求示例
```json
{
  "orderNo": "ORDER-2024-001",
  "includeTestlineLanguage": true,
  "includeAnalyte": true,
  "includeLabSection": false,
  "includePpTestLine": true,
  "includeCitation": true
}
```

#### 10.5.2 响应示例
```json
{
  "success": true,
  "data": [
    {
      "id": "testline-001",
      "header": {
        "orderNo": "ORDER-2024-001",
        "testLineName": "化学分析测试线",
        "languages": [...]
      },
      "analytes": [...],
      "ppTestlines": [...],
      "citation": {...}
    }
  ],
  "code": "200",
  "message": "success"
}
```

### 10.6 扩展说明
- **向后兼容**: 新增方法不影响现有接口
- **批量优化**: 自动享受所有子对象assembler的性能优化
- **监控支持**: 继承现有的性能监控和日志体系
- **测试覆盖**: 可复用现有的批量算法测试用例

## 11. 总结

### 10.1 技术成果

✅ **性能提升**: 解决N+1查询问题，查询次数减少95%以上
✅ **功能扩展**: 新增findByOrderNo查询方法和getTestlineInstanceByOrderNo业务接口
✅ **架构优化**: 统一使用批量算法，提升代码复用性
✅ **可维护性**: 保持接口兼容性，支持渐进式优化
✅ **接口完整性**: 从Gateway到Controller的完整调用链实现

### 11.2 最佳实践

- **统一批量算法**: findById、findByIds、findByOrderNo、getTestlineInstanceByOrderNo共享同一套批量组装逻辑
- **异常隔离**: 单个组装器失败不影响整体查询结果
- **渐进优化**: 支持组装器按需升级为批量模式
- **性能监控**: 完善的日志和监控体系
- **接口标准化**: 遵循统一的查询DTO设计模式和响应格式

### 11.3 后续规划

- 继续优化其他组装器的批量实现
- 基于缓存进一步优化高频查询场景
- 扩展更多基于不同条件的查询方法
- 建立性能基准和自动化性能测试

---

**文档版本**: v1.0  
**创建时间**: 2025-09-26  
**作者**: Xuehua_Zhang  
**技术栈**: Spring Boot + MyBatis + COLA架构