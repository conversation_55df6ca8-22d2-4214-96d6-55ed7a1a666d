-- otsnotes.tb_test_line_instance definition

CREATE TABLE `tb_test_line_instance` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `LabId` int(11) DEFAULT NULL COMMENT '实验室编码',
  `LabCode` varchar(50) DEFAULT NULL,
  `GeneralOrderInstanceID` varchar(36) DEFAULT NULL COMMENT 'tb_GeneralOrder ',
  `OrderNo` varchar(50) DEFAULT NULL,
  `TestLineVersionID` int(11) DEFAULT NULL COMMENT 'Version ID of test line',
  `CitationVersionId` int(11) DEFAULT '0',
  `TestLineID` int(11) DEFAULT NULL COMMENT 'Test line ID',
  `TestLineEvaluation` varchar(500) DEFAULT NULL,
  `CitationTypeID` int(11) DEFAULT NULL,
  `EvaluationAlias` varchar(500) DEFAULT NULL,
  `StandardID` int(11) DEFAULT NULL,
  `StandardVersionID` int(11) DEFAULT NULL,
  `StandardName` varchar(500) DEFAULT NULL,
  `StandardSectionID` int(11) DEFAULT NULL,
  `StandardSectionName` varchar(1000) DEFAULT NULL,
  `AutoConclusion` bit(1) DEFAULT NULL,
  `TestLineStatus` int(11) DEFAULT NULL,
  `ConditionStatus` int(11) DEFAULT NULL,
  `RecommendedFibreLabel` varchar(4000) DEFAULT NULL,
  `TestLineSeq` int(11) DEFAULT NULL,
  `CuttingRequest` varchar(250) DEFAULT NULL,
  `IsPretreatment` bit(1) DEFAULT NULL,
  `OrdertestLineRemark` varchar(4000) DEFAULT NULL,
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  `modified` tinyint(1) DEFAULT '0',
  `FileID` varchar(36) DEFAULT NULL,
  `DataEntryCloudID` varchar(250) DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `ValidateDate` datetime DEFAULT NULL,
  `ValidateBy` varchar(50) DEFAULT NULL,
  `ReportSeq` int(11) DEFAULT NULL,
  `TestLineAlias` varchar(500) DEFAULT NULL,
  `CalculateConclusionFlag` int(11) DEFAULT '0',
  `ProductLineAbbr` varchar(100) DEFAULT NULL,
  `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `TestLineType` int(11) DEFAULT '0' COMMENT '0：普通【默认值】、1：Pretreatment、2：SubContract(Order)、4：CSPP、8：Claim、16：IngredientTL、32：CloneTestLine、64：Job、128：OOB_TEST 256：READ_ONLY 512：SUB_PP 1024：VIRTAUL_TL 2048：FIBRE_CONTENT_TL',
  `TestLineBaseId` bigint(20) DEFAULT '0',
  `CitationBaseId` bigint(20) DEFAULT '0',
  `LabSectionBaseId` bigint(20) DEFAULT '0',
  `CitationId` int(11) DEFAULT '0',
  `SampleSegegrationWIID` bigint(20) DEFAULT NULL,
  `SampleSegegrationWIText` text,
  `LabTeamCode` varchar(100) DEFAULT NULL COMMENT 'LabTeamCode，CS 提供。length follow CS',
  `PendingFlag` bit(1) DEFAULT b'0' COMMENT '0 unpending,1pending',
  `Engineer` varchar(50) DEFAULT NULL,
  `TestStartDate` datetime DEFAULT NULL,
  `TestEndDate` datetime DEFAULT NULL,
  `OrderSeq` int(11) DEFAULT NULL,
  `TestDueDate` datetime DEFAULT NULL,
  `TestItemNo` varchar(50) DEFAULT NULL,
  `MainTestLineId` int(11) DEFAULT NULL COMMENT '配置的mainTestLineId',
  `StyleVersionId` int(11) NOT NULL DEFAULT '0' COMMENT 'ef tb_test_line_data_entry_style.ID，第一次SaveDataEntry时保存，用于保留样式的历史',
  `CustomerTestLineName` varchar(500) DEFAULT NULL,
  `CustomerTestLineNameCN` varchar(500) DEFAULT NULL COMMENT 'OOB TestLine 名称',
  `ClientStandard` varchar(500) DEFAULT NULL COMMENT 'ClientStandard',
  `ExternalTestlineInstanceId` varchar(150) DEFAULT NULL COMMENT '外部系统testline instanceId',
  `CitationName` varchar(500) DEFAULT NULL COMMENT 'CitationName',
  `DocumentReviewFlag` int(11) DEFAULT NULL COMMENT ' 0-None,1-Conclusion,2-Testing & Conclusion',
  PRIMARY KEY (`ID`),
  KEY `FK_Reference_41` (`GeneralOrderInstanceID`),
  KEY `idx_test_line_instance_LastModifiedTimestamp` (`LastModifiedTimestamp`),
  KEY `idx_testLineBaseId` (`TestLineBaseId`),
  KEY `idx_citationBaseId` (`CitationBaseId`),
  KEY `idx_labSectionBaseId` (`LabSectionBaseId`),
  KEY `idx_testLineId` (`TestLineID`) USING BTREE,
  KEY `idx_order_no` (`OrderNo`),
  CONSTRAINT `FK_Reference_41` FOREIGN KEY (`GeneralOrderInstanceID`) REFERENCES `tb_general_order_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- otsnotes.tb_test_line_instance_multiplelanguage definition

CREATE TABLE `tb_test_line_instance_multiplelanguage` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `TestLineInstanceID` varchar(36) DEFAULT NULL COMMENT 'Test line ID',
  `LanguageId` int(10) NOT NULL,
  `EvaluationAlias` varchar(500) DEFAULT NULL,
  `StandardName` varchar(500) DEFAULT NULL,
  `TestLineEvaluation` varchar(500) DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `StandardSectionName` varchar(1000) DEFAULT NULL,
  `CitationName` varchar(500) DEFAULT NULL COMMENT 'CitationName',
  PRIMARY KEY (`ID`),
  KEY `idx_TestLineInstanceID` (`TestLineInstanceID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- otsnotes.tb_pp_instance definition

CREATE TABLE `tb_pp_instance` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `GeneralOrderInstanceID` varchar(36) DEFAULT NULL COMMENT 'FK tb_GeneralOrderInstance',
  `PPVersionID` int(11) DEFAULT NULL,
  `PPNo` int(11) DEFAULT NULL,
  `PPClientRefNo` varchar(500) DEFAULT NULL,
  `SubPPVersionID` int(11) DEFAULT NULL,
  `SubPPNo` int(11) DEFAULT NULL,
  `SubPPClientRefNo` varchar(500) DEFAULT NULL,
  `RootPPVersionID` int(11) DEFAULT NULL,
  `RootPPNo` int(11) DEFAULT NULL,
  `RootClientRefNo` varchar(500) DEFAULT NULL,
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `PPStandardVersionId` int(11) DEFAULT NULL,
  `PPRegulationVersionId` int(11) DEFAULT NULL,
  `PPStandardName` varchar(512) DEFAULT NULL,
  `PPRegulationName` varchar(512) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `FK_Reference_43` (`GeneralOrderInstanceID`),
  CONSTRAINT `FK_Reference_43` FOREIGN KEY (`GeneralOrderInstanceID`) REFERENCES `tb_general_order_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- otsnotes.tb_pp_instance_multiplelanguage definition

CREATE TABLE `tb_pp_instance_multiplelanguage` (
  `ID` varchar(36) NOT NULL,
  `PPInstanceId` varchar(36) NOT NULL,
  `PPClientRefNo` varchar(500) DEFAULT NULL,
  `LanguageId` int(11) DEFAULT NULL,
  `CreatedDate` datetime NOT NULL,
  `CreatedBy` varchar(50) NOT NULL,
  `ModifiedDate` datetime DEFAULT NULL,
  `ModifiedBy` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `idx_PPInstanceId` (`PPInstanceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- otsnotes.tb_analyte_instance definition

CREATE TABLE `tb_analyte_instance` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `GeneralOrderInstanceID` varchar(36) DEFAULT NULL COMMENT 'FK tb_GeneralOrderInstance',
  `TestLineInstanceID` varchar(36) DEFAULT NULL COMMENT 'FK tb_TestLineInstance',
  `AnalyteBaseId` bigint(20) DEFAULT '0',
  `AnalyteID` int(11) NOT NULL,
  `TestAnalyteName` varchar(500) DEFAULT NULL,
  `UnitBaseId` bigint(20) DEFAULT '0',
  `ReportUnit` varchar(4000) DEFAULT NULL,
  `CasNo` varchar(100) DEFAULT NULL,
  `TestAnalyteSeq` int(11) DEFAULT NULL,
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`ID`,`AnalyteID`),
  KEY `FK_Reference_3` (`TestLineInstanceID`),
  KEY `FK_Order_Analyte` (`GeneralOrderInstanceID`),
  KEY `idx_analyteBaseId` (`AnalyteBaseId`),
  KEY `index_LastModifiedTimestamp` (`LastModifiedTimestamp`) USING BTREE,
  KEY `idx_tb_analyte_instance_LastModifiedTimestamp` (`LastModifiedTimestamp`),
  CONSTRAINT `FK_Order_Analyte` FOREIGN KEY (`GeneralOrderInstanceID`) REFERENCES `tb_general_order_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_Reference_3` FOREIGN KEY (`TestLineInstanceID`) REFERENCES `tb_test_line_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- otsnotes.tb_lab_section_instance definition

CREATE TABLE `tb_lab_section_instance` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `GeneralOrderInstanceID` varchar(36) DEFAULT NULL COMMENT 'FK tb_GeneralOrderInstance',
  `TestLineInstanceID` varchar(36) DEFAULT NULL COMMENT 'FK tb_TestLineInstance',
  `LabSectionCode` varchar(500) DEFAULT NULL,
  `LabSectionName` varchar(500) DEFAULT NULL,
  `SectionSequence` int(11) DEFAULT NULL,
  `LabSectionID` int(11) DEFAULT NULL,
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  PRIMARY KEY (`ID`),
  KEY `FK_Order_LabSection` (`GeneralOrderInstanceID`),
  KEY `FK_Reference_8` (`TestLineInstanceID`),
  KEY `idx_tb_lab_section_instance_LabSectionID` (`LabSectionID`),
  CONSTRAINT `FK_Order_LabSection` FOREIGN KEY (`GeneralOrderInstanceID`) REFERENCES `tb_general_order_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_Reference_8` FOREIGN KEY (`TestLineInstanceID`) REFERENCES `tb_test_line_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- otsnotes.tb_test_condition_group definition

CREATE TABLE `tb_test_condition_group` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `GeneralOrderInstanceID` varchar(36) DEFAULT NULL COMMENT 'FK tb_GeneralOrderInstance',
  `TestLineInstanceID` varchar(36) DEFAULT NULL COMMENT 'FK tb_TestLineInstance',
  `GroupFootNotes` text,
  `CombinedConditionDescription` varchar(4000) DEFAULT NULL,
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`ID`),
  KEY `FK_Reference_25` (`TestLineInstanceID`),
  KEY `FK_Reference_39` (`GeneralOrderInstanceID`),
  KEY `index_LastModifiedTimestamp` (`LastModifiedTimestamp`) USING BTREE,
  CONSTRAINT `FK_Reference_25` FOREIGN KEY (`TestLineInstanceID`) REFERENCES `tb_test_line_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_Reference_39` FOREIGN KEY (`GeneralOrderInstanceID`) REFERENCES `tb_general_order_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- otsnotes.tb_test_pp_condition_group definition

CREATE TABLE `tb_test_pp_condition_group` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `ConditionGroupID` varchar(36) DEFAULT NULL COMMENT 'tb_test_condition_group',
  `PpTestLineRelId` varchar(36) DEFAULT NULL COMMENT 'tre_pp_test_line_relationship.ID',
  `TestLineInstanceId` varchar(36) DEFAULT NULL COMMENT 'tb_test_line_instance',
  `LanguageType` int(10) NOT NULL COMMENT 'default en',
  `GroupFootNotes` text COMMENT 'FootNotes',
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  PRIMARY KEY (`ID`) USING BTREE,
  KEY `idx_ConditionGroupID` (`ConditionGroupID`),
  KEY `idx_TestLineInstanceId` (`TestLineInstanceId`),
  KEY `idx_PpTestLineRelId` (`PpTestLineRelId`),
  KEY `index_LastModifiedTimestamp` (`LastModifiedTimestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- otsnotes.tre_pp_condition_relationship_multiplelanguage definition

CREATE TABLE `tre_pp_condition_relationship_multiplelanguage` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `PpConditionRelationshipID` varchar(36) NOT NULL,
  `LanguageId` int(10) NOT NULL,
  `TestConditionDesc` varchar(500) DEFAULT NULL,
  `TestConditionTypeName` varchar(500) DEFAULT NULL,
  `TestConditionName` varchar(500) DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  PRIMARY KEY (`ID`),
  KEY `idx_PpConditionRelationshipID` (`PpConditionRelationshipID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- otsnotes.tre_pp_condition_relationship definition

CREATE TABLE `tre_pp_condition_relationship` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `PPTestLineRelID` varchar(36) DEFAULT NULL,
  `TestConditionTypeID` int(11) DEFAULT NULL,
  `TestConditionID` int(11) DEFAULT NULL,
  `TestConditionName` varchar(500) DEFAULT NULL,
  `TestConditionDesc` varchar(500) DEFAULT NULL,
  `TestConditionSeq` int(11) DEFAULT NULL,
  `IsConditionTypeBlock` bit(1) DEFAULT NULL,
  `TestConditionTypeName` varchar(500) DEFAULT NULL,
  `ConditionTypeBlockLevel` int(11) DEFAULT NULL,
  `IsProcedureCondition` bit(1) DEFAULT NULL,
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  PRIMARY KEY (`ID`),
  KEY `FK_Reference_36` (`PPTestLineRelID`),
  CONSTRAINT `FK_Reference_36` FOREIGN KEY (`PPTestLineRelID`) REFERENCES `tre_pp_test_line_relationship` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- otsnotes.tre_pp_test_line_relationship definition

CREATE TABLE `tre_pp_test_line_relationship` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `GeneralOrderInstanceID` varchar(36) DEFAULT NULL,
  `PpArtifactRelId` bigint(20) NOT NULL DEFAULT '0',
  `TestLineInstanceID` varchar(36) DEFAULT NULL,
  `PPInstanceID` varchar(36) DEFAULT NULL,
  `SectionID` int(11) DEFAULT NULL,
  `SectionName` varchar(200) DEFAULT NULL,
  `SectionLevel` varchar(200) DEFAULT NULL,
  `PPNotes` varchar(4000) DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `aid` bigint(20) DEFAULT '0',
  `ConstructionId` varchar(50) DEFAULT NULL COMMENT 'PP Construction跨版本唯一ID',
  `QuotationTestlineInstanceID` varchar(50) DEFAULT NULL,
  `SubPpRelSeq` int(11) DEFAULT '0' COMMENT '对应tre_trims_pp_artifact_relationship.TestLineSeq,当Add PP时如果是SubPP TL时，需要将SubPP的Seq保存进去',
  `TrimsPPTestLineRelId` bigint(20) DEFAULT '0',
  `PpBaseId` bigint(20) DEFAULT '0',
  `RootPpBaseId` bigint(20) DEFAULT '0',
  `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `Seq` bigint(20) DEFAULT NULL,
  `ExtFields` text COMMENT 'ExtFields',
  PRIMARY KEY (`ID`),
  KEY `FK_PPInstance_PPTestLineRelationship` (`PPInstanceID`),
  KEY `FK_Reference_42` (`GeneralOrderInstanceID`),
  KEY `FK_TestLineInstance_PPTestLineRelationship` (`TestLineInstanceID`),
  KEY `idx_trimsPPTestLineRelId` (`TrimsPPTestLineRelId`),
  KEY `idx_ppBaseId` (`PpBaseId`),
  KEY `idx_ppArtifactRelId` (`PpArtifactRelId`),
  KEY `index_LastModifiedTimestamp` (`LastModifiedTimestamp`) USING BTREE,
  KEY `idx_tre_pp_test_line_relationship_LastModifiedTimestamp` (`LastModifiedTimestamp`),
  CONSTRAINT `FK_PPInstance_PPTestLineRelationship` FOREIGN KEY (`PPInstanceID`) REFERENCES `tb_pp_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_Reference_42` FOREIGN KEY (`GeneralOrderInstanceID`) REFERENCES `tb_general_order_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_TestLineInstance_PPTestLineRelationship` FOREIGN KEY (`TestLineInstanceID`) REFERENCES `tb_test_line_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;



-- otsnotes.tb_test_sample definition

CREATE TABLE `tb_test_sample` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `SampleParentID` varchar(36) DEFAULT NULL,
  `SampleNo` varchar(60) DEFAULT NULL,
  `SampleType` int(11) DEFAULT NULL,
  `GroupType` varchar(10) DEFAULT NULL COMMENT 'group类别区分 With、Mix 样品',
  `SampleSeq` int(11) DEFAULT NULL,
  `OrderNo` varchar(50) DEFAULT NULL,
  `Category` varchar(10) DEFAULT NULL,
  `Description` varchar(1024) DEFAULT NULL,
  `Composition` varchar(500) DEFAULT NULL,
  `Color` varchar(500) DEFAULT NULL,
  `SampleDescforReport` varchar(4000) DEFAULT NULL,
  `SampleRemark` varchar(4000) DEFAULT NULL,
  `EndUse` varchar(500) DEFAULT NULL,
  `Material` varchar(1500) DEFAULT NULL,
  `OtherSampleInfo` text,
  `Applicable` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'NC标记：1是，0否',
  `ReferDataType` varchar(5) DEFAULT NULL,
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `Version` int(11) DEFAULT '0',
  `SampleSort` int(11) DEFAULT NULL COMMENT 'sample sort 排序',
  `NoOfSample` int(11) DEFAULT NULL COMMENT 'Sample Qty',
  `ExternalSampleId` varchar(150) DEFAULT NULL COMMENT '外部sample instance id',
  `SourceType` varchar(100) DEFAULT 'sgs' COMMENT 'Sample 来源：sgs/customer',
  PRIMARY KEY (`ID`),
  KEY `idx_tb_test_sample_SampleType` (`SampleType`),
  KEY `idx_tb_test_sample_LastModifiedTimestamp` (`LastModifiedTimestamp`),
  KEY `idx_OrderNo_SampleNo` (`OrderNo`,`SampleNo`),
  KEY `ix_tb_test_sample_SampleParentID` (`SampleParentID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;



-- otsnotes.tb_product_attribute_instance definition

CREATE TABLE `tb_product_attribute_instance` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `TestSampleID` varchar(36) DEFAULT NULL COMMENT 'FK tb_TestSample',
  `TestMatrixID` varchar(36) DEFAULT NULL COMMENT '添加TestMatrixID字段。',
  `TestLineVersionID` int(11) DEFAULT NULL,
  `ProductAttributeID` int(11) DEFAULT NULL,
  `ProductAttributeValue` varchar(500) DEFAULT NULL,
  `OrderNo` varchar(50) DEFAULT NULL,
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `LimitGroupId` varchar(36) DEFAULT NULL,
  `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`ID`),
  KEY `FK_Reference_23` (`TestSampleID`),
  KEY `idx_LimitGroupId` (`LimitGroupId`),
  KEY `idx_orderNo` (`OrderNo`),
  KEY `idx_TestMatrixID` (`TestMatrixID`),
  KEY `index_LastModifiedTimestamp` (`LastModifiedTimestamp`) USING BTREE,
  KEY `idx_tb_product_attribute_instance_LastModifiedTimestamp` (`LastModifiedTimestamp`),
  CONSTRAINT `FK_Reference_23` FOREIGN KEY (`TestSampleID`) REFERENCES `tb_test_sample` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- otsnotes.tb_test_matrix definition

CREATE TABLE `tb_test_matrix` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `GeneralOrderInstanceID` varchar(36) DEFAULT NULL,
  `TestLineInstanceID` varchar(36) DEFAULT NULL COMMENT 'From tb_TestLineInstance',
  `TestSampleID` varchar(36) DEFAULT NULL,
  `TestConditionGroupID` varchar(36) DEFAULT NULL,
  `ConditionID` int(11) DEFAULT NULL,
  `ConditionName` varchar(100) DEFAULT NULL,
  `MatrixGroupId` int(2) DEFAULT '0',
  `MatrixStatus` int(11) DEFAULT NULL COMMENT 'Matrix Status',
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `ExternalMatrixInstanceId` varchar(150) DEFAULT NULL COMMENT '外部系统matrix instance id',
  `MatrixNo` varchar(50) DEFAULT NULL COMMENT 'matrixNo',
  `MatrixConfirmDate` datetime DEFAULT NULL COMMENT 'matrix confirm date',
  `Remark` text COMMENT 'matrix的备注信息',
  PRIMARY KEY (`ID`),
  KEY `FK_Reference_27` (`TestSampleID`),
  KEY `FK_Reference_44` (`GeneralOrderInstanceID`),
  KEY `FK_Reference_7` (`TestLineInstanceID`),
  KEY `FK_Reference_98` (`TestConditionGroupID`),
  KEY `index_LastModifiedTimestamp` (`LastModifiedTimestamp`) USING BTREE,
  CONSTRAINT `FK_Reference_27` FOREIGN KEY (`TestSampleID`) REFERENCES `tb_test_sample` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_Reference_44` FOREIGN KEY (`GeneralOrderInstanceID`) REFERENCES `tb_general_order_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_Reference_7` FOREIGN KEY (`TestLineInstanceID`) REFERENCES `tb_test_line_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_Reference_98` FOREIGN KEY (`TestConditionGroupID`) REFERENCES `tb_test_condition_group` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- otsnotes.tb_test_specimen definition

CREATE TABLE `tb_test_specimen` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `TestLineInstanceID` varchar(36) DEFAULT NULL COMMENT 'From tb_TestLineInstance',
  `TestSampleID` varchar(36) DEFAULT NULL COMMENT 'FK tb_TestSample',
  `TestMatrixId` varchar(36) DEFAULT NULL,
  `SpecimenNo` int(11) DEFAULT NULL,
  `SpecimenDescription` varchar(120) DEFAULT NULL,
  `ConditionGroupId` int(11) DEFAULT NULL,
  `SpecimenType` tinyint(1) DEFAULT NULL COMMENT ' 1:No of Specimen  2:specimenRel',
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`ID`),
  KEY `FK_Reference_28` (`TestSampleID`),
  KEY `FK_Reference_29` (`TestLineInstanceID`),
  KEY `index_LastModifiedTimestamp` (`LastModifiedTimestamp`) USING BTREE,
  CONSTRAINT `FK_Reference_28` FOREIGN KEY (`TestSampleID`) REFERENCES `tb_test_sample` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_Reference_29` FOREIGN KEY (`TestLineInstanceID`) REFERENCES `tb_test_line_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- otsnotes.tb_test_position definition

CREATE TABLE `tb_test_position` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `TestMatrixID` varchar(36) DEFAULT NULL,
  `UsageTypeID` int(11) DEFAULT NULL,
  `UsageTypeName` varchar(500) DEFAULT NULL,
  `TestingSeq` int(11) DEFAULT '0' COMMENT '用户配置seq',
  `TestPositionSeq` int(11) DEFAULT NULL,
  `TestPositionName` varchar(256) DEFAULT NULL,
  `UsageTypePositionRelId` bigint(20) DEFAULT '0',
  `Client` tinyint(1) DEFAULT '0' COMMENT '0: Trims默认, 1:用户手动输入',
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`ID`),
  KEY `FK_Reference_5` (`TestMatrixID`),
  KEY `index_LastModifiedTimestamp` (`LastModifiedTimestamp`) USING BTREE,
  KEY `idx_tb_test_position_CreatedDate` (`CreatedDate`),
  CONSTRAINT `FK_Reference_5` FOREIGN KEY (`TestMatrixID`) REFERENCES `tb_test_matrix` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- otsnotes.tb_limit_instance definition

CREATE TABLE `tb_limit_instance` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `TestLineInstanceID` varchar(36) DEFAULT NULL COMMENT 'FK tb_TestLineInstance',
  `AnalyteID` int(11) DEFAULT NULL COMMENT 'From tb_AnalyteInstance',
  `TestSampleID` varchar(36) DEFAULT NULL COMMENT 'FK tb_TestSample',
  `TestMatrixID` varchar(36) DEFAULT NULL,
  `TestConditionID` int(11) DEFAULT NULL,
  `TalBaseId` bigint(20) DEFAULT '0',
  `OperatorID` varchar(36) DEFAULT NULL,
  `AnalyteLimitVersionID` int(11) DEFAULT NULL,
  `OperatorName` varchar(500) DEFAULT NULL,
  `Value1` varchar(100) DEFAULT NULL,
  `Value2` varchar(100) DEFAULT NULL,
  `SpecimenID` varchar(36) DEFAULT NULL,
  `ManualRequirement` bit(1) DEFAULT NULL,
  `ReportUnit` varchar(4000) DEFAULT NULL,
  `ReportDescription` text,
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `PPId` varchar(36) DEFAULT NULL,
  `PpBaseId` bigint(20) DEFAULT '0',
  `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `AnalyteInstanceId` varchar(36) DEFAULT NULL,
  `ParentCondtionId` int(11) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `FK_Reference_19` (`TestSampleID`),
  KEY `FK_Reference_20` (`TestLineInstanceID`),
  KEY `FK_Reference_40` (`TestConditionID`),
  KEY `idx_ppBaseId` (`PpBaseId`),
  KEY `idx_AnalyteLimitVersionID` (`AnalyteLimitVersionID`),
  KEY `idx_talBaseId` (`TalBaseId`),
  KEY `index_LastModifiedTimestamp` (`LastModifiedTimestamp`) USING BTREE,
  KEY `idx_TestMatrixID` (`TestMatrixID`),
  KEY `idx_tb_limit_instance_LastModifiedTimestamp` (`LastModifiedTimestamp`),
  CONSTRAINT `FK_Reference_19` FOREIGN KEY (`TestSampleID`) REFERENCES `tb_test_sample` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_Reference_20` FOREIGN KEY (`TestLineInstanceID`) REFERENCES `tb_test_line_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- otsnotes.tb_test_condition_instance definition

CREATE TABLE `tb_test_condition_instance` (
  `ID` varchar(36) NOT NULL COMMENT 'ID,Primary key',
  `GeneralOrderInstanceID` varchar(36) DEFAULT NULL COMMENT 'FK tb_GeneralOrderInstance',
  `TestLineInstanceID` varchar(36) DEFAULT NULL COMMENT 'FK tb_TestLineInstance',
  `TestSampleID` varchar(36) DEFAULT NULL COMMENT 'FK tb_TestSample',
  `TestMatrixID` varchar(36) DEFAULT NULL,
  `TestConditionTypeID` int(11) DEFAULT NULL,
  `TestConditionID` int(11) DEFAULT NULL,
  `TestConditionName` varchar(500) DEFAULT NULL,
  `TestConditionDesc` varchar(500) DEFAULT NULL,
  `TestConditionSeq` int(11) DEFAULT NULL,
  `IsConditionTypeBlock` bit(1) DEFAULT NULL,
  `TestConditionTypeName` varchar(500) DEFAULT NULL,
  `ConditionTypeBlockLevel` int(11) DEFAULT NULL,
  `IsProcedureCondition` bit(1) DEFAULT NULL,
  `ActiveIndicator` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
  `CreatedDate` datetime DEFAULT NULL COMMENT 'CreatedDate',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
  `ModifiedDate` datetime DEFAULT NULL COMMENT 'ModifiedDate',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
  `TestLineConditionRelId` bigint(20) DEFAULT '0',
  `ConditionBaseId` bigint(20) DEFAULT '0',
  `ClientSpecified` int(11) DEFAULT '0',
  `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`ID`),
  KEY `FK_Reference_11` (`TestSampleID`),
  KEY `FK_Reference_26` (`TestLineInstanceID`),
  KEY `FK_Reference_38` (`GeneralOrderInstanceID`),
  KEY `idx_testMatrixId` (`TestMatrixID`),
  KEY `idx_testLineConditionRelId` (`TestLineConditionRelId`),
  KEY `idx_conditionBaseId` (`ConditionBaseId`),
  KEY `index_LastModifiedTimestamp` (`LastModifiedTimestamp`) USING BTREE,
  CONSTRAINT `FK_Reference_11` FOREIGN KEY (`TestSampleID`) REFERENCES `tb_test_sample` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_Reference_26` FOREIGN KEY (`TestLineInstanceID`) REFERENCES `tb_test_line_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_Reference_38` FOREIGN KEY (`GeneralOrderInstanceID`) REFERENCES `tb_general_order_instance` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;