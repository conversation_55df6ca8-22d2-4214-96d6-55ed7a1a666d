-- otsnotes.tb_test_sample definition

CREATE TABLE `tb_test_sample`
(
    `ID`                    varchar(36) NOT NULL COMMENT 'ID,Primary key',
    `SampleParentID`        varchar(36)   DEFAULT NULL,
    `SampleNo`              varchar(60)   DEFAULT NULL,
    `SampleType`            int(11) DEFAULT NULL,
    `GroupType`             varchar(10)   DEFAULT NULL COMMENT 'group类别区分 With、Mix 样品',
    `SampleSeq`             int(11) DEFAULT NULL,
    `OrderNo`               varchar(50)   DEFAULT NULL,
    `Category`              varchar(10)   DEFAULT NULL,
    `Description`           varchar(1024) DEFAULT NULL,
    `Composition`           varchar(500)  DEFAULT NULL,
    `Color`                 varchar(500)  DEFAULT NULL,
    `SampleDescforReport`   varchar(4000) DEFAULT NULL,
    `SampleRemark`          varchar(4000) DEFAULT NULL,
    `EndUse`                varchar(500)  DEFAULT NULL,
    `Material`              varchar(1500) DEFAULT NULL,
    `OtherSampleInfo`       text,
    `Applicable`            tinyint(1) NOT NULL DEFAULT '0' COMMENT 'NC标记：1是，0否',
    `ReferDataType`         varchar(5)    DEFAULT NULL,
    `ActiveIndicator`       tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
    `CreatedDate`           datetime      DEFAULT NULL COMMENT 'CreatedDate',
    `CreatedBy`             varchar(50)   DEFAULT NULL COMMENT 'CreatedBy',
    `ModifiedDate`          datetime      DEFAULT NULL COMMENT 'ModifiedDate',
    `ModifiedBy`            varchar(50)   DEFAULT NULL COMMENT 'ModifiedBy',
    `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    `Version`               int(11) DEFAULT '0',
    `SampleSort`            int(11) DEFAULT NULL COMMENT 'sample sort 排序',
    `NoOfSample`            int(11) DEFAULT NULL COMMENT 'Sample Qty',
    `ExternalSampleId`      varchar(150)  DEFAULT NULL COMMENT '外部sample instance id',
    `SourceType`            varchar(100)  DEFAULT 'sgs' COMMENT 'Sample 来源：sgs/customer',
    PRIMARY KEY (`ID`),
    KEY                     `idx_tb_test_sample_SampleType` (`SampleType`),
    KEY                     `idx_tb_test_sample_LastModifiedTimestamp` (`LastModifiedTimestamp`),
    KEY                     `idx_OrderNo_SampleNo` (`OrderNo`,`SampleNo`),
    KEY                     `ix_tb_test_sample_SampleParentID` (`SampleParentID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
-- otsnotes.tb_test_sample_ext definition

CREATE TABLE `tb_test_sample_ext`
(
    `Id`                    bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `SampleId`              varchar(36) NOT NULL COMMENT '外键样品Id',
    `FieldCode`             varchar(64) NOT NULL COMMENT '字段代码',
    `FieldName`             varchar(64)   DEFAULT NULL COMMENT '字段名称',
    `FieldValue`            varchar(1000) DEFAULT NULL COMMENT '字段值',
    `FieldText`             varchar(1000) DEFAULT NULL COMMENT '字段文本值（该值用于select场景）',
    `Status`                int(11) NOT NULL DEFAULT '0' COMMENT '状态（0：禁用、1：启用 ）',
    `CreatedBy`             varchar(50)   DEFAULT NULL COMMENT '创建用户',
    `CreatedDate`           datetime      DEFAULT NULL COMMENT '创建时间',
    `ModifiedBy`            varchar(50)   DEFAULT NULL COMMENT '修改用户',
    `ModifiedDate`          datetime      DEFAULT NULL COMMENT '修改时间',
    `LastModifiedTimestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`),
    UNIQUE KEY `idx_SampleId_FieldCode` (`SampleId`,`FieldCode`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10157227 DEFAULT CHARSET=utf8;

-- otsnotes.tb_test_sample_group definition

CREATE TABLE `tb_test_sample_group`
(
    `ID`                    varchar(36) NOT NULL COMMENT 'ID,Primary key',
    `SampleGroupID`         varchar(36) DEFAULT NULL,
    `SampleID`              varchar(36) DEFAULT NULL COMMENT 'FK tb_TestSample',
    `MainMaterialFlag`      tinyint(1) DEFAULT '0' COMMENT '标记是否为主测试样，默认为0 （0 否 1是）',
    `Sequence`              int(10) DEFAULT NULL COMMENT '记录Group样品的添加顺序',
    `ActiveIndicator`       tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: inactive, 1: active',
    `CreatedDate`           datetime    DEFAULT NULL COMMENT 'CreatedDate',
    `CreatedBy`             varchar(50) DEFAULT NULL COMMENT 'CreatedBy',
    `ModifiedDate`          datetime    DEFAULT NULL COMMENT 'ModifiedDate',
    `ModifiedBy`            varchar(50) DEFAULT NULL COMMENT 'ModifiedBy',
    `LastModifiedTimestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    PRIMARY KEY (`ID`),
    KEY                     `FK_Reference_12` (`SampleGroupID`),
    KEY                     `FK_Reference_13` (`SampleID`),
    KEY                     `index_LastModifiedTimestamp` (`LastModifiedTimestamp`) USING BTREE,
    CONSTRAINT `FK_Reference_12` FOREIGN KEY (`SampleGroupID`) REFERENCES `tb_test_sample` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION,
    CONSTRAINT `FK_Reference_13` FOREIGN KEY (`SampleID`) REFERENCES `tb_test_sample` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- otsnotes.tb_test_sample_language definition

CREATE TABLE `tb_test_sample_language`
(
    `Id`                    varchar(36)  NOT NULL,
    `SampleId`              varchar(36)  NOT NULL,
    `GroupId`               varchar(36)   DEFAULT NULL,
    `Material`              varchar(255)  DEFAULT NULL,
    `LanguageId`            int(11) NOT NULL,
    `Status`                int(11) NOT NULL COMMENT '0：禁用(默认)、1：启用',
    `CreatedDate`           datetime     NOT NULL,
    `CreatedBy`             varchar(255) NOT NULL,
    `ModifiedDate`          datetime      DEFAULT NULL,
    `ModifiedBy`            varchar(255)  DEFAULT NULL,
    `LastModifiedTimestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `Description`           text,
    `Composition`           varchar(500)  DEFAULT NULL,
    `Color`                 varchar(500)  DEFAULT NULL,
    `SampleDescforReport`   varchar(4000) DEFAULT NULL,
    `SampleRemark`          varchar(4000) DEFAULT NULL,
    `EndUse`                varchar(500)  DEFAULT NULL,
    `OtherSampleInfo`       text,
    PRIMARY KEY (`Id`),
    KEY                     `idx_SampleId` (`SampleId`),
    KEY                     `idx_tb_test_sample_language_LastModifiedTimestamp` (`LastModifiedTimestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
