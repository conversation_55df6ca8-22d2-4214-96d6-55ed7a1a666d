package com.sgs.soda.otsnotes.adapter.web.testline;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.soda.otsnotes.client.api.TestlineInstanceBizService;
import com.sgs.soda.otsnotes.client.dto.testline.data.TestlineInstanceDTO;
import com.sgs.soda.otsnotes.client.dto.testline.qry.TestlineInstanceGetByOrderNoQry;
import com.sgs.soda.otsnotes.client.dto.testline.qry.TestlineInstanceGetQry;
import com.sgs.soda.otsnotes.client.dto.testline.qry.TestlineInstanceListGetQry;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * TestlineInstance Controller
 */
@Api(tags = "TestlineInstance管理")
@RestController
@RequestMapping("/uni/api/testline")
@Slf4j
public class TestlineInstanceController {

    @Autowired
    private TestlineInstanceBizService testlineInstanceBizService;

    @ApiOperation("根据ID获取TestlineInstance信息(GET)")
    @GetMapping("/instance/{testlineInstanceId}")
    public BaseResponse<TestlineInstanceDTO> getTestlineInstanceById(
            @ApiParam(value = "TestlineInstance ID", required = true, example = "testline-001") 
            @PathVariable String testlineInstanceId,
            @ApiParam(value = "是否包含多语言信息", example = "true") 
            @RequestParam(defaultValue = "true") Boolean includeTestlineLanguage,
            @ApiParam(value = "是否包含分析物信息", example = "true") 
            @RequestParam(defaultValue = "true") Boolean includeAnalyte,
            @ApiParam(value = "是否包含实验室部门信息", example = "true") 
            @RequestParam(defaultValue = "true") Boolean includeLabSection,
            @ApiParam(value = "是否包含PP测试线信息", example = "true") 
            @RequestParam(defaultValue = "true") Boolean includePpTestLine,
            @ApiParam(value = "是否包含标准信息", example = "true") 
            @RequestParam(defaultValue = "true") Boolean includeCitation) {

        log.info("开始查询TestlineInstance，ID: {}, 参数: includeTestlineLanguage={}, includeAnalyte={}, includeLabSection={}, includePpTestLine={}, includeCitation={}", 
                testlineInstanceId, includeTestlineLanguage, includeAnalyte, includeLabSection, includePpTestLine, includeCitation);

        TestlineInstanceGetQry qry = new TestlineInstanceGetQry();
        qry.setTestlineInstanceId(testlineInstanceId);
        qry.setIncludeTestlineLanguage(includeTestlineLanguage);
        qry.setIncludeAnalyte(includeAnalyte);
        qry.setIncludeLabSection(includeLabSection);
        qry.setIncludePpTestLine(includePpTestLine);
        qry.setIncludeCitation(includeCitation);

        TestlineInstanceDTO testlineInstanceDTO = testlineInstanceBizService.getTestlineInstanceById(qry);
        log.info("查询TestlineInstance完成，ID: {}, 结果: {}", testlineInstanceId, testlineInstanceDTO != null);

        return BaseResponse.newInstance(testlineInstanceDTO);
    }

    @ApiOperation("根据ID获取TestlineInstance信息(POST)")
    @PostMapping("/instance/get")
    public BaseResponse<TestlineInstanceDTO> getTestlineInstance(@RequestBody TestlineInstanceGetQry qry) {
        log.info("开始查询TestlineInstance，请求参数: {}", qry);
        
        TestlineInstanceDTO testlineInstanceDTO = testlineInstanceBizService.getTestlineInstanceById(qry);
        log.info("查询TestlineInstance完成，ID: {}, 结果: {}", qry.getTestlineInstanceId(), testlineInstanceDTO != null);
        
        return BaseResponse.newInstance(testlineInstanceDTO);
    }

    @ApiOperation("根据ID List获取TestlineInstance信息(POST)")
    @PostMapping("/instanceList/get")
    public BaseResponse<List<TestlineInstanceDTO>> getTestlineInstanceList(@RequestBody TestlineInstanceListGetQry qry) {
        log.info("开始查询TestlineInstance，请求参数: {}", qry);

        List<TestlineInstanceDTO> testlineInstanceList = testlineInstanceBizService.getTestlineInstanceByIds(qry);
        log.info("查询TestlineInstance完成，ID: {}, 结果: {}", qry.getTestlineInstanceIds(), testlineInstanceList != null);

        return BaseResponse.newInstance(testlineInstanceList);
    }

    @ApiOperation("根据订单号获取TestlineInstance信息(POST)")
    @PostMapping("/instanceList/getByOrderNo")
    public BaseResponse<List<TestlineInstanceDTO>> getTestlineInstanceByOrderNo(@Valid @RequestBody TestlineInstanceGetByOrderNoQry qry) {
        log.info("开始根据订单号查询TestlineInstance，请求参数: {}", qry);

        List<TestlineInstanceDTO> testlineInstanceList = testlineInstanceBizService.getTestlineInstanceByOrderNo(qry);
        log.info("根据订单号查询TestlineInstance完成，OrderNo: {}, 结果数量: {}", 
                qry.getOrderNo(), testlineInstanceList != null ? testlineInstanceList.size() : 0);

        return BaseResponse.newInstance(testlineInstanceList);
    }

} 