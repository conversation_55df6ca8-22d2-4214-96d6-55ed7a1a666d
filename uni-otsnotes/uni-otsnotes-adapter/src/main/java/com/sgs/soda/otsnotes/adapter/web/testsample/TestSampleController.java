package com.sgs.soda.otsnotes.adapter.web.testsample;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.soda.otsnotes.client.api.TestSampleBizService;
import com.sgs.soda.otsnotes.client.dto.testsample.data.TestSampleDTO;
import com.sgs.soda.otsnotes.client.dto.testsample.qry.TestSampleGetQry;
import com.sgs.soda.otsnotes.client.dto.testsample.qry.TestSampleListGetQry;
import com.sgs.soda.otsnotes.client.dto.testsample.qry.TestSampleGetByOrderNoQry;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "TestSample管理")
@RestController
@RequestMapping("/uni/api/testsample")
@Slf4j
public class TestSampleController {

    @Autowired
    private TestSampleBizService testSampleBizService;

    @ApiOperation("根据ID获取TestSample信息(GET)")
    @GetMapping("/sample/{testSampleId}")
    public BaseResponse<TestSampleDTO> getTestSampleById(
            @ApiParam(value = "TestSample ID", required = true, example = "testsample-001") 
            @PathVariable String testSampleId,
            @ApiParam(value = "是否包含材质信息", example = "true") 
            @RequestParam(defaultValue = "true") Boolean includeMaterial,
            @ApiParam(value = "是否包含分组信息", example = "true") 
            @RequestParam(defaultValue = "true") Boolean includeGroupItems,
            @ApiParam(value = "是否包含附件信息", example = "true") 
            @RequestParam(defaultValue = "true") Boolean includeAttachments,
            @ApiParam(value = "是否包含扩展字段", example = "true") 
            @RequestParam(defaultValue = "true") Boolean includeExtFields) {

        log.info("开始查询TestSample，ID: {}, 参数: includeMaterial={}, includeGroupItems={}, includeAttachments={}, includeExtFields={}", 
                testSampleId, includeMaterial, includeGroupItems, includeAttachments, includeExtFields);

        TestSampleGetQry qry = new TestSampleGetQry();
        qry.setTestSampleId(testSampleId);
        qry.setIncludeMaterial(includeMaterial);
        qry.setIncludeGroupItems(includeGroupItems);
        qry.setIncludeAttachments(includeAttachments);
        qry.setIncludeExtFields(includeExtFields);

        TestSampleDTO testSampleDTO = testSampleBizService.getTestSampleById(qry);
        log.info("查询TestSample完成，ID: {}, 结果: {}", testSampleId, testSampleDTO != null);

        return BaseResponse.newInstance(testSampleDTO);
    }

    @ApiOperation("根据ID获取TestSample信息(POST)")
    @PostMapping("/sample/get")
    public BaseResponse<TestSampleDTO> getTestSample(@RequestBody TestSampleGetQry qry) {
        log.info("开始查询TestSample，请求参数: {}", qry);
        
        TestSampleDTO testSampleDTO = testSampleBizService.getTestSampleById(qry);
        log.info("查询TestSample完成，ID: {}, 结果: {}", qry.getTestSampleId(), testSampleDTO != null);
        
        return BaseResponse.newInstance(testSampleDTO);
    }

    @ApiOperation("根据ID List获取TestSample信息(POST)")
    @PostMapping("/sampleList/get")
    public BaseResponse<List<TestSampleDTO>> getTestSampleList(@RequestBody TestSampleListGetQry qry) {
        log.info("开始批量查询TestSample，请求参数: {}", qry);

        List<TestSampleDTO> testSampleList = testSampleBizService.getTestSampleByIds(qry);
        log.info("批量查询TestSample完成，IDs: {}, 结果数量: {}", qry.getTestSampleIds(), 
                testSampleList != null ? testSampleList.size() : 0);

        return BaseResponse.newInstance(testSampleList);
    }

    @ApiOperation("根据订单号获取TestSample信息(POST)")
    @PostMapping("/sampleByOrderNo/get")
    public BaseResponse<List<TestSampleDTO>> getTestSampleByOrderNo(@RequestBody TestSampleGetByOrderNoQry qry) {
        log.info("开始根据订单号查询TestSample，请求参数: {}", qry);

        List<TestSampleDTO> testSampleList = testSampleBizService.getTestSampleByOrderNo(qry);
        log.info("根据订单号查询TestSample完成，OrderNo: {}, 结果数量: {}", qry.getOrderNo(), 
                testSampleList != null ? testSampleList.size() : 0);

        return BaseResponse.newInstance(testSampleList);
    }

    @ApiOperation("根据订单号获取TestSample信息(GET)")
    @GetMapping("/sampleByOrderNo/{orderNo}")
    public BaseResponse<List<TestSampleDTO>> getTestSampleByOrderNo(
            @ApiParam(value = "订单号", required = true, example = "ORDER-2024-001") 
            @PathVariable String orderNo,
            @ApiParam(value = "是否包含材质信息", example = "true") 
            @RequestParam(defaultValue = "true") Boolean includeMaterial,
            @ApiParam(value = "是否包含分组信息", example = "true") 
            @RequestParam(defaultValue = "true") Boolean includeGroupItems,
            @ApiParam(value = "是否包含附件信息", example = "true") 
            @RequestParam(defaultValue = "true") Boolean includeAttachments,
            @ApiParam(value = "是否包含扩展字段", example = "true") 
            @RequestParam(defaultValue = "true") Boolean includeExtFields) {

        log.info("开始根据订单号查询TestSample，OrderNo: {}, 参数: includeMaterial={}, includeGroupItems={}, includeAttachments={}, includeExtFields={}", 
                orderNo, includeMaterial, includeGroupItems, includeAttachments, includeExtFields);

        TestSampleGetByOrderNoQry qry = new TestSampleGetByOrderNoQry();
        qry.setOrderNo(orderNo);
        qry.setIncludeMaterial(includeMaterial);
        qry.setIncludeGroupItems(includeGroupItems);
        qry.setIncludeAttachments(includeAttachments);
        qry.setIncludeExtFields(includeExtFields);

        List<TestSampleDTO> testSampleList = testSampleBizService.getTestSampleByOrderNo(qry);
        log.info("根据订单号查询TestSample完成，OrderNo: {}, 结果数量: {}", orderNo, 
                testSampleList != null ? testSampleList.size() : 0);

        return BaseResponse.newInstance(testSampleList);
    }
}