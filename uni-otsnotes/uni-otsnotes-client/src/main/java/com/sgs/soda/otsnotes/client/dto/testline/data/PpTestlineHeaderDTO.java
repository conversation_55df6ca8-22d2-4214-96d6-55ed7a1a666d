package com.sgs.soda.otsnotes.client.dto.testline.data;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PpTestlineHeaderDTO {

    private Long ppArtifactRelId;
    private Long rootPPBaseId;
    private Long ppBaseId;
    private Integer ppVersionId;
    private Integer rootPPNo;
    private Integer ppNo;
    private Integer aid;
    private String constructionId;

    private String ppName;
    private String ppNotes;
    private Integer sectionId;
    private Integer sectionLevel;
    private String sectionName;

    private Integer seq;

    private Date createdDate;
    private String createdBy;
    private Date modifiedDate;
    private String modifiedBy;

    private List<PpTestlineLanguageDTO> languages;
} 