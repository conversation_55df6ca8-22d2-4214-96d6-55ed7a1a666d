package com.sgs.soda.otsnotes.client.api;

import com.alibaba.cola.dto.SingleResponse;
import com.sgs.soda.otsnotes.client.dto.testline.data.TestlineInstanceDTO;
import com.sgs.soda.otsnotes.client.dto.testline.qry.TestlineInstanceGetByOrderNoQry;
import com.sgs.soda.otsnotes.client.dto.testline.qry.TestlineInstanceGetQry;
import com.sgs.soda.otsnotes.client.dto.testline.qry.TestlineInstanceListGetQry;

import java.util.List;

/**
 * TestlineInstance Business Service Interface
 */
public interface TestlineInstanceBizService {
    
    /**
     * 根据ID获取TestlineInstance信息
     * 
     * @param qry 查询条件，包含testlineInstanceId和控制子对象的boolean属性
     * @return TestlineInstance完整信息
     */
    TestlineInstanceDTO getTestlineInstanceById(TestlineInstanceGetQry qry);


    /**
     * 根据IdList获取TestlineInstance信息
     *
     * @param qry 查询条件，包含testlineInstanceId和控制子对象的boolean属性
     * @return TestlineInstance完整信息
     */
    List<TestlineInstanceDTO> getTestlineInstanceByIds(TestlineInstanceListGetQry qry);


    /**
     * 根据订单号获取TestlineInstance信息
     *
     * @param qry 查询条件，包含orderNo和控制子对象的boolean属性
     * @return TestlineInstance列表
     */
    List<TestlineInstanceDTO> getTestlineInstanceByOrderNo(TestlineInstanceGetByOrderNoQry qry);
} 