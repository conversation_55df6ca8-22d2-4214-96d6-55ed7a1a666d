# Soda OTS Notes

基于COLA架构的笔记管理系统

## 技术栈

- JDK 1.8
- Spring Boot 2.4.2
- ArchUnit 1.4.1
- MyBatis 3.4.6
- MyBatis Spring 1.3.2
- Druid 1.2.24 (数据库连接池)
- LiteFlow 2.13.2.1
- Lettuce (Redis客户端)
- MySQL Driver 8.0.33
- JetCache 2.6.7
- MapStruct 1.5.5.Final
- Hutool 5.8.38
- Knife4j 2.0.9
- COLA 4.3.2

## 技术栈变更记录

### 2025-07-02 技术栈升级
- Spring Boot: 2.7.18 → 2.4.2
- MyBatis Plus: 3.5.12 → MyBatis 3.4.6 + MyBatis Spring 1.3.2
- 数据库连接池: HikariCP → Druid 1.2.24
- JetCache: 2.7.3 → 2.6.7
- Knife4j: 3.0.3 → 2.0.9

### 变更说明
- 从MyBatis Plus迁移到原生MyBatis，提供更灵活的SQL控制
- 引入Druid连接池，提供更好的监控和性能
- 降级部分组件版本以确保兼容性

## 项目结构

```
uni-otsnotes/
├── uni-otsnotes-client/          # 客户端层 - 对外接口和DTO
├── uni-otsnotes-adapter/         # 适配器层 - Web控制器等
├── uni-otsnotes-app/             # 应用层 - 应用服务
├── uni-otsnotes-domain/          # 领域层 - 领域模型和业务逻辑
├── uni-otsnotes-infrastructure/  # 基础设施层 - 数据访问等
└── uni-otsnotes-start/           # 启动模块 - 应用启动和配置
```

## COLA架构说明

### 分层架构
- **Client层**: 对外接口定义，包含API接口和DTO对象
- **Adapter层**: 适配器层，包含Web控制器等
- **App层**: 应用服务层，协调领域对象完成业务用例
- **Domain层**: 领域层，包含领域模型和业务逻辑
- **Infrastructure层**: 基础设施层，包含数据访问、外部服务等

### 依赖规则
- Client层不依赖任何其他层
- Adapter层只能依赖App层
- App层只能依赖Domain层
- Domain层不依赖任何其他层
- Infrastructure层只能被App层和Adapter层依赖

## 快速开始

### 环境要求
- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 数据库初始化
1. 创建数据库：`otsnotes`
2. 执行SQL脚本：`doc/db/testline_init.sql`

### 配置修改
修改 `uni-otsnotes-start/src/main/resources/application.yml` 中的数据库和Redis配置

### 启动应用
```bash
mvn clean install
cd uni-otsnotes-start
mvn spring-boot:run
```

### 访问接口文档
启动后访问：http://localhost:8080/doc.html

### 访问Druid监控
启动后访问：http://localhost:8080/druid

## API接口

### 笔记管理
- `POST /api/notes` - 添加笔记
- `PUT /api/notes` - 更新笔记
- `GET /api/notes/{id}` - 根据ID查询笔记

## 架构验证

项目集成了ArchUnit来验证COLA架构规范，运行测试：

```bash
mvn test -Dtest=ColaArchitectureTest
```

## 特性

- ✅ 严格遵循COLA架构规范
- ✅ 完整的依赖管理
- ✅ 集成MyBatis进行数据访问
- ✅ 集成Druid连接池提供监控
- ✅ 集成JetCache进行缓存管理
- ✅ 集成LiteFlow进行流程编排
- ✅ 集成Knife4j提供API文档
- ✅ 使用MapStruct进行对象映射
- ✅ 使用Hutool提供工具类支持
- ✅ 使用ArchUnit进行架构验证
- ✅ 集成MyBatis Generator自动生成代码

## 开发规范

1. 严格遵循COLA架构的分层依赖规则
2. 使用MapStruct进行对象转换
3. 使用Hutool提供的工具类
4. 所有API接口都要有Swagger注解
5. 重要业务逻辑要有单元测试
6. 定期运行ArchUnit测试验证架构规范
7. 使用MyBatis Generator生成基础CRUD代码 