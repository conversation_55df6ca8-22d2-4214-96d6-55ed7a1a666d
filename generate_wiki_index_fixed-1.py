#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wiki处理器 - 增强版
复制content目录到wiki目录，处理文件路径，并生成Wiki导航索引
"""

import os
import json
import re
import shutil
from pathlib import Path
from datetime import datetime

class WikiProcessor:
    def __init__(self, source_content_dir: str, target_wiki_dir: str, metadata_file: str | None = None):
        self.source_content_dir = Path(source_content_dir)
        self.target_wiki_dir = Path(target_wiki_dir)
        self.target_content_dir = self.target_wiki_dir / "content"
        self.metadata_file = Path(metadata_file) if metadata_file else None

    def _process_file_content(self, content: str) -> str:
        """
        处理文件内容，转换file://路径为GitLab格式链接
        同时处理.qoder/repowiki路径为wiki/content路径
        """
        # 1. 处理 file://路径
        file_path_pattern = r'file://([^)\s\]]+)'

        def replace_file_path(match):
            file_path = match.group(1)
            # 去掉file://前缀，将反斜杠转换为正斜杠
            clean_path = file_path.replace('\\', '/')
            
            # 判断是否已经包含GitLab路径前缀
            if clean_path.startswith('-/tree/master/') or clean_path.startswith('-/blob/master/'):
                # 如果已经是GitLab格式，直接返回
                return clean_path
            else:
                # 根据文件类型区分：代码文件使用tree，文档文件使用blob
                # 但在GitLab Wiki中，我们不需要完整的GitLab前缀，只需要简单的路径
                # 文档文件（.md等）使用相对路径或直接路径
                if any(clean_path.lower().endswith(ext) for ext in ['.md', '.txt', '.rst', '.adoc']):
                    # 文档文件使用相对路径或简单路径
                    gitlab_url = clean_path
                else:
                    # 代码文件使用简单路径，让GitLab自动处理
                    gitlab_url = clean_path
                return gitlab_url

        # 替换所有file://路径
        processed_content = re.sub(file_path_pattern, replace_file_path, content)
        
        # 2. 处理.qoder/repowiki路径，将其替换为wiki/content路径
        # 匹配形如 [.qoder/repowiki/zh/content/API接口参考/报告API.md](/-/blob/master/.qoder/repowiki/zh/content/...) 的模式
        qoder_path_pattern = r'\[\.qoder/repowiki/zh/content/([^\]]+)\]\((/-/blob/master/)\.qoder/repowiki/zh/content/([^)]+)\)'
        
        def replace_qoder_path(match):
            original_text = match.group(1)  # 原始文本（去掉.qoder/repowiki/zh/content/前缀）
            url_prefix = match.group(2)  # /-/blob/master/
            relative_path = match.group(3)  # 相对路径
            # 对于.md文档，使用相对路径或者保留/-/blob/master/格式
            if relative_path.endswith('.md'):
                # 对于md文档之间的引用，使用相对路径
                # 计算从当前文档到目标文档的相对路径
                new_url = f"../{relative_path}"
            else:
                # 非.md文档使用简单路径
                new_url = f"wiki/content/{relative_path}"
            return f"[{original_text}]({new_url})"
        
        processed_content = re.sub(qoder_path_pattern, replace_qoder_path, processed_content)
        
        # 3. 处理其他可能的.qoder/repowiki路径格式
        # 匹配形如 [.qoder/repowiki/zh/content/API接口参考/报告API.md](/-/tree/master/.qoder/repowiki/zh/content/...) 的模式
        qoder_tree_pattern = r'\[\.qoder/repowiki/zh/content/([^\]]+)\]\((/-/tree/master/)\.qoder/repowiki/zh/content/([^)]+)\)'
        
        def replace_qoder_tree_path(match):
            original_text = match.group(1)  # 原始文本（去掉.qoder/repowiki/zh/content/前缀）
            url_prefix = match.group(2)  # /-/tree/master/
            relative_path = match.group(3)  # 相对路径
            # 对于.md文档，使用相对路径或者保留/-/tree/master/格式
            if relative_path.endswith('.md'):
                # 对于md文档之间的引用，使用相对路径
                new_url = f"../{relative_path}"
            else:
                # 非.md文档使用简单路径
                new_url = f"wiki/content/{relative_path}"
            return f"[{original_text}]({new_url})"
        
        processed_content = re.sub(qoder_tree_pattern, replace_qoder_tree_path, processed_content)
        
        # 4. 处理其他可能的.qoder/repowiki路径格式，包括那些只有文件名但URL中有.qoder/repowiki的
        # 匹配形如 [文件名.md](/-/blob/master/.qoder/repowiki/zh/content/...) 的模式
        simple_qoder_pattern = r'\[([^\]]+\.md)\]\((/-/blob/master/)\.qoder/repowiki/zh/content/([^)]+)\)'
        
        def replace_simple_qoder_path(match):
            file_name = match.group(1)  # 文件名
            url_prefix = match.group(2)  # /-/blob/master/
            relative_path = match.group(3)  # 相对路径
            # 对于.md文档，使用相对路径
            if relative_path.endswith('.md'):
                new_url = f"../{relative_path}"
            else:
                new_url = f"wiki/content/{relative_path}"
            return f"[{file_name}]({new_url})"
        
        processed_content = re.sub(simple_qoder_pattern, replace_simple_qoder_path, processed_content)
        
        # 5. 处理最简单的.qoder/repowiki路径格式（没有GitLab前缀）
        # 匹配形如 [text](.qoder/repowiki/zh/content/...) 的模式
        direct_qoder_pattern = r'\[([^\]]+)\]\(\.qoder/repowiki/zh/content/([^)]+)\)'
        
        def replace_direct_qoder_path(match):
            link_text = match.group(1)
            relative_path = match.group(2)  # 相对路径
            # 使用简单路径，去掉.qoder/repowiki/zh/content/前缀
            if link_text.startswith('.qoder/repowiki/zh/content/'):
                # 如果链接文本包含完整路径，只保留最后部分
                clean_text = link_text.replace('.qoder/repowiki/zh/content/', '')
            else:
                clean_text = link_text
            
            # 对于.md文档，使用相对路径
            if relative_path.endswith('.md'):
                new_url = f"../{relative_path}"
            else:
                new_url = f"wiki/content/{relative_path}"
            return f"[{clean_text}]({new_url})"
        
        processed_content = re.sub(direct_qoder_pattern, replace_direct_qoder_path, processed_content)
        
        return processed_content

    def _copy_and_process_content(self):
        """
        复制source content目录到target wiki目录，并处理文件中的路径
        """
        print(f"开始复制内容目录: {self.source_content_dir} -> {self.target_content_dir}")

        # 确保目标目录存在
        self.target_wiki_dir.mkdir(parents=True, exist_ok=True)

        # 如果目标content目录已存在，先删除
        if self.target_content_dir.exists():
            shutil.rmtree(self.target_content_dir)

        # 复制整个目录结构
        shutil.copytree(self.source_content_dir, self.target_content_dir)

        # 处理所有Markdown文件中的路径
        processed_count = 0
        for md_file in self.target_content_dir.rglob('*.md'):
            try:
                with open(md_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 处理文件内容中的路径
                processed_content = self._process_file_content(content)

                # 如果内容有变化，写回文件
                if processed_content != content:
                    with open(md_file, 'w', encoding='utf-8') as f:
                        f.write(processed_content)
                    processed_count += 1

            except Exception as e:
                print(f"处理文件 {md_file} 时出错: {e}")

        print(f"内容复制完成，处理了 {processed_count} 个文件中的路径")

    def _get_file_title(self, file_path: Path) -> str:
        """从Markdown文件中提取标题"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 查找第一个一级标题
                match = re.search(r'^#\s+(.+)$', content, re.MULTILINE)
                if match:
                    return match.group(1).strip()
                # 如果没有找到标题，使用文件名
                return file_path.stem.replace('_', ' ').replace('-', ' ')
        except Exception:
            return file_path.stem.replace('_', ' ').replace('-', ' ')

    def _scan_directory(self, dir_path: Path, relative_path: str = "") -> dict:
        """递归扫描目录结构"""
        structure = {
            'type': 'directory',
            'name': dir_path.name,
            'path': relative_path,
            'children': [],
            'files': []
        }

        if not dir_path.exists():
            return structure

        try:
            items = list(dir_path.iterdir())
        except PermissionError:
            print(f"Warning: 无法访问目录 {dir_path}")
            return structure

        # 分离文件和目录
        directories = [item for item in items if item.is_dir()]
        files = [item for item in items if item.is_file() and item.suffix == '.md']

        # 排序
        directories.sort(key=lambda x: x.name)
        files.sort(key=lambda x: x.name)

        # 处理子目录
        for subdir in directories:
            sub_relative_path = f"{relative_path}/{subdir.name}" if relative_path else subdir.name
            child_structure = self._scan_directory(subdir, sub_relative_path)
            structure['children'].append(child_structure)

        # 处理Markdown文件
        for file in files:
            file_relative_path = f"{relative_path}/{file.name}" if relative_path else file.name
            title = self._get_file_title(file)

            file_info = {
                'type': 'file',
                'name': file.name,
                'title': title,
                'path': file_relative_path,
                'size': file.stat().st_size if file.exists() else 0
            }
            structure['files'].append(file_info)

        return structure

    def _generate_markdown_index(self, structure: dict, level: int = 0) -> str:
        """生成Markdown格式的目录"""
        lines = []
        indent = "  " * level

        # 处理当前目录的文件
        for file_info in structure.get('files', []):
            title = file_info['title']
            path = file_info['path'].replace('\\', '/')
            lines.append(f"{indent}- [{title}](./content/{path})")

        # 处理子目录
        for child in structure.get('children', []):
            if child['files'] or child['children']:  # 只显示非空目录
                dir_name = child['name']
                lines.append(f"{indent}- **{dir_name}**")
                child_content = self._generate_markdown_index(child, level + 1)
                if child_content:
                    lines.append(child_content)

        return '\n'.join(lines)

    def _generate_sidebar(self, structure: dict) -> str:
        """生成GitLab Wiki侧边栏格式"""
        lines = []

        def process_level(items, level=0):
            for item in items:
                if item['type'] == 'directory' and (item['files'] or item['children']):
                    # 目录标题
                    lines.append(f"{'  ' * level}- **{item['name']}**")
                    # 处理目录下的文件
                    for file_info in item['files']:
                        title = file_info['title']
                        path = file_info['path'].replace('\\', '/')
                        lines.append(f"{'  ' * (level + 1)}- [{title}](./content/{path})")
                    # 递归处理子目录
                    if item['children']:
                        process_level(item['children'], level + 1)

        # 首先处理根目录的文件
        for file_info in structure.get('files', []):
            title = file_info['title']
            path = file_info['path'].replace('\\', '/')
            lines.append(f"- [{title}](./content/{path})")

        # 然后处理子目录
        if structure.get('children'):
            process_level(structure['children'])

        return '\n'.join(lines)

    def _count_files(self, structure: dict) -> int:
        """统计文件数量"""
        count = len(structure.get('files', []))
        for child in structure.get('children', []):
            count += self._count_files(child)
        return count

    def _calculate_total_size(self, structure: dict) -> float:
        """计算总文件大小（KB）"""
        total_bytes = 0

        def collect_files_recursive(struct):
            nonlocal total_bytes
            # 统计当前层级的文件
            for file_info in struct.get('files', []):
                total_bytes += file_info['size']
            # 递归处理子目录
            for child in struct.get('children', []):
                collect_files_recursive(child)

        collect_files_recursive(structure)
        return total_bytes / 1024  # 转换为KB

    def _generate_html_tree(self, structure: dict, level: int = 0) -> str:
        """生成HTML树结构"""
        html = ""
        indent = "  " * level

        if level == 0:
            html += "<ul>\n"

        # 处理文件
        for file_info in structure.get('files', []):
            title = file_info['title']
            path = file_info['path'].replace('\\', '/')
            size_kb = round(file_info['size'] / 1024, 1)
            html += f'{indent}  <li>📄 <a href="./content/{path}">{title}</a> <small>({size_kb}KB)</small></li>\n'

        # 处理目录
        for child in structure.get('children', []):
            if child['files'] or child['children']:
                html += f'{indent}  <li class="folder">📁 {child["name"]}\n'
                html += f'{indent}    <ul>\n'
                html += self._generate_html_tree(child, level + 2)
                html += f'{indent}    </ul>\n'
                html += f'{indent}  </li>\n'

        if level == 0:
            html += "</ul>\n"

        return html

    def process_wiki(self):
        """处理Wiki：复制内容并生成索引"""
        # 第一步：复制并处理内容
        self._copy_and_process_content()

        # 第二步：生成索引
        print("开始扫描Wiki内容目录...")
        structure = self._scan_directory(self.target_content_dir)

        print(f"扫描完成，找到 {len(structure.get('children', []))} 个子目录")

        # 生成统计信息
        total_files = self._count_files(structure)
        total_size = self._calculate_total_size(structure)

        print(f"总计 {total_files} 个Markdown文件，总大小 {total_size:.1f}KB")

        # 生成各种格式的索引
        markdown_index = self._generate_markdown_index(structure)
        sidebar = self._generate_sidebar(structure)

        # 使用wiki目录作为输出目录
        output_dir = self.target_wiki_dir

        # 保存文件
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 保存主索引文件
        with open(output_dir / "Wiki-Index.md", 'w', encoding='utf-8') as f:
            f.write("# OTSNotes服务 Wiki 目录索引\n\n")
            f.write(f"本Wiki包含 **{total_files}** 个文档文件，总大小 **{total_size:.1f}KB**\n\n")
            f.write("## 📚 文档目录\n\n")
            f.write(markdown_index)
            f.write(f"\n\n---\n*📅 生成时间: {current_time}*\n")

        # 保存侧边栏文件
        with open(output_dir / "_Sidebar.md", 'w', encoding='utf-8') as f:
            f.write("# 📖 Wiki 导航\n\n")
            f.write(sidebar)
            f.write(f"\n\n---\n*更新: {current_time}*\n")

        # 保存结构数据
        with open(output_dir / "wiki-structure.json", 'w', encoding='utf-8') as f:
            json.dump({
                'structure': structure,
                'stats': {
                    'total_files': total_files,
                    'total_size_kb': total_size,
                    'generated_at': current_time
                }
            }, f, ensure_ascii=False, indent=2)

        # 生成HTML索引
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>OTSNotes Wiki Index</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
        .stats {{ color: #666; margin: 10px 0; }}
        ul {{ line-height: 1.8; }}
        a {{ color: #0366d6; text-decoration: none; }}
        a:hover {{ text-decoration: underline; }}
        .folder {{ font-weight: bold; color: #333; margin: 8px 0; }}
        small {{ color: #888; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🏢 OTSNotes服务 Wiki 目录索引</h1>
        <div class="stats">
            📊 统计: {total_files} 个文档文件 | 📦 总大小: {total_size:.1f}KB | 🕒 生成时间: {current_time}
        </div>
    </div>
    <div id="content">
        {self._generate_html_tree(structure)}
    </div>
</body>
</html>"""

        with open(output_dir / "Wiki-Index.html", 'w', encoding='utf-8') as f:
            f.write(html_content)

        print("\n✅ 索引文件生成完成:")
        print(f"  📄 {output_dir}/Wiki-Index.md - Markdown格式主索引")
        print(f"  📋 {output_dir}/_Sidebar.md - GitLab Wiki侧边栏")
        print(f"  🌐 {output_dir}/Wiki-Index.html - HTML格式索引")
        print(f"  📊 {output_dir}/wiki-structure.json - 结构化数据")
        print(f"  📂 {self.target_content_dir}/ - 处理后的Wiki内容")

        return {
            'total_files': total_files,
            'total_size_kb': total_size,
            'structure': structure
        }

def main():
    """主函数"""
    source_content_dir = ".qoder/repowiki/zh/content"
    target_wiki_dir = "wiki"
    metadata_file = ".qoder/repowiki/zh/meta/repowiki-metadata.json"

    print("🚀 启动Wiki处理器...")
    print(f"📁 源目录: {source_content_dir}")
    print(f"📁 目标目录: {target_wiki_dir}")
    print("=" * 60)

    try:
        processor = WikiProcessor(source_content_dir, target_wiki_dir, metadata_file)
        result = processor.process_wiki()

        print("=" * 60)
        print("🎉 Wiki处理成功!")
        print(f"📈 共处理 {result['total_files']} 个文档，总大小 {result['total_size_kb']:.1f}KB")
        print(f"📂 Wiki文件已生成到: {target_wiki_dir}")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()