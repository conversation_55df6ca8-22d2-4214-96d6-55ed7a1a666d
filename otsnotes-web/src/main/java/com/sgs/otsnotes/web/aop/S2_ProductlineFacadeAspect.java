package com.sgs.otsnotes.web.aop;

import cn.hutool.core.util.StrUtil;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.core.util.LabUtil;
import com.sgs.otsnotes.core.common.HeaderHelper;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.otsnotes.facade.model.common.BaseResponse;
import com.sgs.otsnotes.facade.model.common.BizException;
import com.sgs.otsnotes.facade.model.common.ResponseCode;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.PriorityOrdered;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.UUID;

/**
 * Facade实现方法的AOP.
 * <p/>
 * 实现与业务无关的通用操作。
 * <p/>
 * 1，日志
 * <p/>
 * 2，异常处理等
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class S2_ProductlineFacadeAspect implements PriorityOrdered {
    private static final Logger logger = LoggerFactory.getLogger(S2_ProductlineFacadeAspect.class);

    /**
     *
     * @param reqObject
     */
    @Pointcut("execution(* com.sgs.otsnotes.facade.*Facade.*(*)) && args(reqObject)")
    public void aspect(BaseRequest reqObject) {

    }


    /**
     *
     * @param reqObject
     */
    @Pointcut("execution(* com.sgs.otsnotes.biz.*BizService.*(*)) && args(reqObject)")
    public void bizLayer(BaseRequest reqObject) {

    }


    /**
     *
     * @param joinPoint
     * @param reqObject
     * @return
     */
    @Around("aspect(reqObject)||bizLayer(reqObject)")
    public Object around(ProceedingJoinPoint joinPoint, BaseRequest reqObject){
        if (reqObject == null) {
            logger.error("{} Req: null", joinPoint.getSignature());
            return buildErrorResponse(ResponseCode.ILLEGAL_ARGUMENT, "request is null");
        }

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        if (StringUtils.isBlank(reqObject.getRequestId())) {
            reqObject.setRequestId(UUID.randomUUID().toString());
        }
        if (StringUtils.isBlank(reqObject.getProductLineCode())) {
            reqObject.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        }
        // log日志配有"logPrefix"占位符
        MDC.put(Constants.LOG_PREFIX, reqObject.getRequestId());
        Object resp = null;
        try {
            reqObject.validate();

            // 设置当前BU数据源
            this.setProductLineCode(reqObject);

            resp = joinPoint.proceed(new Object[]{reqObject});
        } catch (IllegalArgumentException ex) {
            resp = buildErrorResponse(ResponseCode.ILLEGAL_ARGUMENT, ex.getLocalizedMessage());
            logger.error(StrUtil.format("IllegalArgumentException req: {}", reqObject), ex);
        } catch (BizException e) {
            // 前端可能将错误msg直接抛给用户
            resp = buildErrorResponse(e.getErrorCode(), e.getLocalizedMessage());
            logger.info("BizException req: {}, Error:{}", reqObject.getRequestId(), e.getMessage());
        } catch (Throwable e) {
            resp = buildErrorResponse(ResponseCode.UNKNOWN, e.getMessage());
            logger.error(StrUtil.format("Throwable req. error:{}. req:{}", e.getMessage(), reqObject), e);
        } finally {
            stopWatch.stop();
            //logger.info("Resp:" + resp);
            MDC.clear();
        }
        /*if (stopWatch.getTime() > 100) {
            logger.info("Finished {},req={} Consumed:{}ms", reqObject.getRequestId(), reqObject, stopWatch.getTime());
        }*/
        return resp;
    }

    /**
     *
     * @param reqObject
     */
    private void setProductLineCode(BaseRequest reqObject){

        // 目标数据源
        String targetProductLineCode = reqObject.getProductLineCode();
        if (StringUtils.isBlank(targetProductLineCode)){
            targetProductLineCode = HeaderHelper.getParamValue(Constants.PRODUCTLINECODE);
        }
        String sourceProductLineCode = reqObject.getSourceProductLineCode();
        if (StringUtils.isBlank(sourceProductLineCode)){
            sourceProductLineCode = targetProductLineCode;
        }
        if(StrUtil.isBlank(sourceProductLineCode)&& Objects.nonNull(UserHelper.getLocalUser())){
            UserInfo localUser = UserHelper.getLocalUser();
            Lab lab = LabUtil.resolveLab(localUser.getCurrentLabCode());
            sourceProductLineCode = lab.getBuCode();
        }
        ProductLineContextHolder.setProductLineCode(sourceProductLineCode, targetProductLineCode);
    }

    /**
     *
     * @param point
     * @param reqObject
     */
    @After("aspect(reqObject)")
    public void after(JoinPoint point, BaseRequest reqObject) {
        ProductLineContextHolder.clear();
    }

    /**
     *
     * @param errorCode
     * @param errorMsg
     * @return
     */
    private BaseResponse buildErrorResponse(ResponseCode errorCode, String errorMsg){
        if (StringUtils.isBlank(errorMsg)){
            errorMsg = "服务器出了点小差错，请稍后再试.";
        }
        return new BaseResponse(errorCode.getCode(), errorMsg);
    }

    @Override
    public int getOrder() {
        return Integer.MAX_VALUE - 9000;

    }
}
