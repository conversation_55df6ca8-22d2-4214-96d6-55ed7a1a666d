package com.sgs.otsnotes.web.aop;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.kafka.client.MessageReq;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.domain.utils.TokenResolver;
import com.sgs.otsnotes.facade.model.common.OtsNotesRequest;
import com.sgs.otsnotes.integration.TokenClient;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.core.PriorityOrdered;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 严格排序aspect，修改时注意先后依赖关系
 */
@Aspect
@Component
@Slf4j
public class S1_UserContextAspect implements PriorityOrdered {
    @Resource
    private TokenClient tokenClient;

    /**
     * controller
     */
    @Pointcut("@within(org.springframework.web.bind.annotation.RestController) || @within(org.springframework.stereotype.Controller)")
    public void controllerJoinPoint() {
    }

    /**
     * web service
     */

    @Pointcut("@within(javax.jws.WebService)")
    public void webServiceJoinPoint() {
    }

    /**
     * dubbo service
     * <p>
     * com.sgs.otsnotes.facade包与子包任意类和方法执行时
     */
    @Pointcut("execution(* com.sgs.otsnotes.facade..*.*(..))")
    public void dubboService() {
    }

    /**
     * mq消费者
     */
    @Pointcut("target(com.sgs.framework.kafka.client.KafkaFacade)")
    public void kafkaConsumer() {

    }

    @Around(value = "controllerJoinPoint()||webServiceJoinPoint()||dubboService()||kafkaConsumer()")
    public Object aroundMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        String sgsToken = resolveSgsToken(joinPoint.getArgs());
        if (StrUtil.isEmpty(sgsToken)) {
            return joinPoint.proceed();
        }
        UserInfo user = tokenClient.getUser(sgsToken);
        Optional.ofNullable(user).ifPresent(UserHelper::setLocalUser);
        return joinPoint.proceed();
    }

    private String resolveSgsToken(Object[] methodArgs) {
        String sgsToken = TokenResolver.resolveToken();
        if (StrUtil.isNotEmpty(sgsToken)) {
            return sgsToken;
        }
        if (ArrayUtil.isEmpty(methodArgs)) {
            return sgsToken;
        }
        for (Object methodArg : methodArgs) {
            if (methodArg instanceof OtsNotesRequest && StrUtil.isNotBlank(((OtsNotesRequest) methodArg).getToken())) {
                sgsToken = ((OtsNotesRequest) methodArg).getToken();
                break;
            } else if (methodArg instanceof BaseRequest && StrUtil.isNotBlank(((BaseRequest) methodArg).getToken())) {
                sgsToken = ((BaseRequest) methodArg).getToken();
                break;
            } else if (methodArg instanceof MessageReq && StrUtil.isNotBlank(((MessageReq) methodArg).getSgsToken())) {
                sgsToken = ((MessageReq) methodArg).getSgsToken();
                break;
            } else if (methodArg instanceof com.sgs.grus.kafka.client.MessageReq && StrUtil.isNotBlank(((com.sgs.grus.kafka.client.MessageReq) methodArg).getSgsToken())) {
                sgsToken = ((com.sgs.grus.kafka.client.MessageReq) methodArg).getSgsToken();
                break;
            }
        }
        return sgsToken;
    }

    @After(value = "controllerJoinPoint()||webServiceJoinPoint()||dubboService()||kafkaConsumer()")
    public void after() {
        UserHelper.clear();
    }

    /**
     * 越小优先级越高
     *
     * @return
     */
    @Override
    public int getOrder() {
        return Integer.MAX_VALUE - 10000;
    }
}
