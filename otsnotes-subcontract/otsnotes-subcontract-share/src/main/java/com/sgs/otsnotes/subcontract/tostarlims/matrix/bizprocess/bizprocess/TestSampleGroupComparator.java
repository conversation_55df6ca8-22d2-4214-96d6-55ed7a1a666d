package com.sgs.otsnotes.subcontract.tostarlims.matrix.bizprocess.bizprocess;

import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleGroupInfoPO;

import java.util.Comparator;

public class TestSampleGroupComparator implements Comparator<TestSampleGroupInfoPO> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public TestSampleGroupComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(TestSampleGroupInfoPO o1, TestSampleGroupInfoPO o2) {

        if (o1.getSequence() == null){
            o1.setSequence(0);
        }
        if (o2.getSequence() == null){
            o2.setSequence(0);
        }
        int index = Integer.compare(o1.getSequence(), o2.getSequence());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        return isAsc ? 1 : -1;
    }
}
