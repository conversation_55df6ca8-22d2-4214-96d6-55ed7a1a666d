package com.sgs.otsnotes.domain.service.reportdata.copy;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.otsnotes.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.otsnotes.domain.service.reportdata.ReportDataDealService;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.ordercopy.SysCopyInfo;
import com.sgs.otsnotes.facade.model.req.subcontract.InvalidateSubcontractDataReq;
import com.sgs.otsnotes.integration.FrameWorkClient;
import com.sgs.otsnotes.integration.v2.ReportDataClientV2;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ReportDataSplitCopyService extends ReportDataDealService {
    private static final Logger logger = LoggerFactory.getLogger(ReportDataSplitCopyService.class);



    public ReportDataSplitCopyService(ReportDataClientV2 subContractDataClient, FrameWorkClient frameWorkClient) {
        super(subContractDataClient, frameWorkClient);
    }


    /**
     * 将老的ReportNo数据Copy到新reportNo 并将oldReportNo设置为无效
     */
    public CustomResult<List<ReportTestDataInfo>> handleCopyReportDataAndInValidaOldReportNoScenario(SysCopyInfo reqParams) {
        CustomResult<List<ReportTestDataInfo>> rspResult = new CustomResult<>();

        String oldReportNo = reqParams.getOldReportNo();
        String newReportNo = reqParams.getOldReportNoUpdate();

        logger.info("【ReportDataCopyService】orderNo:{}, 开始处理Rework场景, oldReportNo: {}, newReportNo: {}", oldReportNo, newReportNo);

        // 替换ReportData 中的数据
        replaceOldReportData(reqParams);


        return handleAddResultData(reqParams, reqParams.getOldReportNoUpdate());
    }

    private void replaceOldReportData(SysCopyInfo reqParams) {
        String orderNo = reqParams.getRootOrderNo();
        String oldReportNo = reqParams.getOldReportNo();
        String newReportNo = reqParams.getOldReportNoUpdate();
        String labCode = reqParams.getLabBu().getLabCode();

        logger.info("【ReportDataCopyService】orderNo:{}, 开始处理Rework场景, oldReportNo: {}, newReportNo: {}", oldReportNo, newReportNo);

        if (StringUtils.isEmpty(oldReportNo) || StringUtils.isEmpty(newReportNo) || StringUtils.equalsIgnoreCase(oldReportNo, newReportNo)) {
            logger.info("【ReportDataCopyService】orderNo:{}, oldReportNo：{} newReportNo：{} 未查询到已保存的测试数据,", orderNo, oldReportNo, newReportNo);
            return; // 无数据时直接返回成功
        }

        List<ReportTestDataInfo> queryResult = queryExistingData(orderNo, oldReportNo, labCode);
        if (CollectionUtils.isEmpty(queryResult)) {
            logger.info("【ReportDataCopyService】orderNo:{}, 未查询到已保存的测试数据, reportNo: {}", orderNo, oldReportNo);
            return; // 无数据时直接返回成功
        }

        try {
            for (ReportTestDataInfo dataInfo : queryResult) {
                dataInfo.setReportNo(newReportNo);
                BaseResponse<Void> updateResult = subContractDataClient.importData(dataInfo);
                if (!updateResult.isSuccess()) {
                    logger.info("【ReportDataCopyService】orderNo:{}, oldReportNo:{}, newReportNo:{}, 更新ReportNo失败: {}",
                            orderNo, oldReportNo, newReportNo, updateResult.getMessage());
                }

                InvalidateSubcontractDataReq invalidateReq = new InvalidateSubcontractDataReq();
                invalidateReq.setReportNo(oldReportNo);
                invalidateReq.setLabCode(labCode);
                invalidateReq.setOrderNo(orderNo);
                invalidateReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                invalidateReq.setActiveIndicator(Boolean.FALSE);
                BaseResponse<Void> invalidateResult = subContractDataClient.invalidateSubcontractData(invalidateReq);

                logger.info("【ReportDataCopyService】orderNo:{}, oldReportNo:{}, newReportNo:{}, invalidateSubcontractData: {}",
                        orderNo, oldReportNo, newReportNo, updateResult.getMessage());
            }

        } catch (Exception e) {
            logger.info("【ReportDataCopyService】orderNo:{}, 处理Rework场景失败", e);
        }
    }


}
