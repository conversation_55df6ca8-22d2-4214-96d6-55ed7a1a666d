package com.sgs.otsnotes.domain.service.reportdata.copy;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.otsnotes.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.otsnotes.domain.service.reportdata.ReportDataDealService;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.req.subcontract.ReportDataReplaceReq;
import com.sgs.otsnotes.integration.FrameWorkClient;
import com.sgs.otsnotes.integration.v2.ReportDataClientV2;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ReportDataReWorkCopyService extends ReportDataDealService {
    private static final Logger logger = LoggerFactory.getLogger(ReportDataReWorkCopyService.class);



    public ReportDataReWorkCopyService(ReportDataClientV2 subContractDataClient, FrameWorkClient frameWorkClient) {
        super(subContractDataClient, frameWorkClient);
    }



    /**
     * 处理Rework场景 - 更新ReportNo关联关系
     */
    public CustomResult<Void> handleReWorkReportNoScenario(String orderNo, String oldReportNo, String newReportNo, String labCode) {
        CustomResult<Void> rspResult = new CustomResult<>();

        logger.info("【ReportDataCopyService】orderNo:{}, 开始处理Rework场景, oldReportNo: {}, newReportNo: {}", oldReportNo, newReportNo);

        List<ReportTestDataInfo> queryResult = queryExistingData(orderNo, oldReportNo, labCode);
        if (CollectionUtils.isEmpty(queryResult)) {
            logger.warn("【ReportDataCopyService】orderNo:{}, 未查询到已保存的测试数据, reportNo: {}", orderNo, oldReportNo);
            return rspResult.success(); // 无数据时直接返回成功
        }

        ReportDataReplaceReq reportDataReplaceReq = new ReportDataReplaceReq();
        reportDataReplaceReq.setOrderNo(orderNo);
//        reportDataReplaceReq.setObjectNo("");
        reportDataReplaceReq.setLabCode(labCode);
        reportDataReplaceReq.setReportNo(oldReportNo);
        reportDataReplaceReq.setNewReportNo(newReportNo);
        reportDataReplaceReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());

        try {
            BaseResponse<Void> updateResult = subContractDataClient.updateReportNo(reportDataReplaceReq);
            if (!updateResult.isSuccess()) {
                logger.error("【ReportDataCopyService】orderNo:{}, oldReportNo:{}, newReportNo:{}, 更新ReportNo失败: {}",
                        orderNo, oldReportNo, newReportNo, updateResult.getMessage());
                return rspResult.fail("更新ReportNo失败: " + updateResult.getMessage());
            }

            logger.info("Rework场景处理成功");
            return rspResult.success();

        } catch (Exception e) {
            logger.error("【ReportDataCopyService】orderNo:{}, 处理Rework场景失败", e);
            return rspResult.fail("处理Rework场景失败: " + e.getMessage());
        }
    }



}
