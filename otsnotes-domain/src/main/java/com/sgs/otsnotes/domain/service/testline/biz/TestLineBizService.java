package com.sgs.otsnotes.domain.service.testline.biz;

import java.util.List;

/**
 * TestLine业务处理服务
 * 
 * <AUTHOR> @since 2025-06-16
 */
public interface TestLineBizService {

    /**
     * 判断测试线是否存在矩阵信息
     * 
     * @param testlineInstance 测试线实例
     * @return 是否存在矩阵信息
     */
    public boolean hasMatrixInfoByTestLineInstanceId(String testlineInstanceId);

    public boolean hasMatrixInfoByTestLineInstanceIds(List<String> testLineInstanceIds);
    
    /**
     * 判断测试线是否存在
     * 
     * @param orderId 订单ID
     * @return 是否存在矩阵信息
     */
    public boolean hasMatrixInfoByOrderId(String orderId);

    /**
     * 判断测试线是否存在确认信息
     * 
     * @param testlineInstance 测试线实例
     * @return 是否存在确认信息
     */
    public boolean hasTestLineConfirmed(String testlineInstanceId);   
}
