package com.sgs.otsnotes.domain.service.testline;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.OrderMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.SubContractExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestLineMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestMatrixMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.GeneralOrderInstanceInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.SubContractPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.SubContractTestLineMappingPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.enums.TestLineModuleType;
import com.sgs.otsnotes.facade.model.enums.TestLineStatus;
import com.sgs.otsnotes.facade.model.enums.TestLineType;
import com.sgs.otsnotes.facade.model.info.matrix.MatrixStatusInfo;
import com.sgs.otsnotes.facade.model.info.testline.TestLineStatusInfo;
import com.sgs.otsnotes.facade.model.req.testLine.UpdateTestLineStatusReq;
import com.sgs.otsnotes.integration.StatusClient;
import com.sgs.preorder.facade.model.req.SysStatusReq;
import com.sgs.soda.otsnotes.client.api.TestlineInstanceBizService;
import com.sgs.soda.otsnotes.client.dto.testline.data.TestlineInstanceDTO;
import com.sgs.soda.otsnotes.client.dto.testline.qry.TestlineInstanceListGetQry;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class TestLineStatusService {
    private static final Logger logger = LoggerFactory.getLogger(TestLineStatusService.class);
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private TestMatrixMapper testMatrixMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private StatusClient statusClient;
    @Autowired
    private SubContractExtMapper subContractMapper;

    @Autowired
    private TestlineInstanceBizService testlineInstanceBizService;

    /**
     *
     * @param moduleType
     * @param testLine
     */
    public CustomResult updateTestLineStatus(TestLineModuleType moduleType, TestLineInstancePO testLine){
        return this.batchUpdateTestLineStatus(moduleType, Lists.newArrayList(testLine));
    }

    /**
     * Add TL/Add PP TL
     * reTest
     * Change
     * SyncSubContract_Completed                                     -》Completed
     * Data Entry Save/Sumbit
     * returnTestLine
     * Validate
     * unlock
     *
     * NC TestLine                                     -》OrdertestLineRemark
     * Cancel TestLine                                 -》TestLineSeq、ActiveIndicator
     * SubContract、SubContractSync、NewSubContract  -》oldTestLineStatus
     * Cancel Order                                    -》ActiveIndicator
     *
     * Copy Report                                     -》FileID
     * UploadTestLineReport                            -》FileID
     *
     * OrdertestLineRemark
     * TestLineSeq
     * ActiveIndicator
     * FileID
     *
     */
    public CustomResult batchUpdateTestLineStatus(TestLineModuleType moduleType, List<TestLineInstancePO> testLines){
        CustomResult rspResult = new CustomResult();
        if (testLines == null || testLines.isEmpty()){
            return rspResult.fail("请求的Test Line 列表为空.");
        }
        List<TestLineStatusInfo> tls = Lists.newArrayList();
        TestLineStatusInfo tl;

        for (TestLineInstancePO testLine: testLines) {
            tl = new TestLineStatusInfo();
            tl.setTestLineInstanceId(testLine.getID());
            tl.setTestLineType(NumberUtil.toInt(testLine.getTestLineType()));

            TestLineStatus testLineStatus = TestLineStatus.findStatus(testLine.getTestLineStatus());
            if (testLineStatus == null){
                return rspResult.fail("请求的TestLine Status 无效.");
            }
            tl.setTestLineStatus(testLineStatus.getStatus());
            tl.setTestLineSeq(NumberUtil.toInt(testLine.getTestLineSeq()));
            tl.setFileId(testLine.getFileID());

            Boolean activeIndicator = testLine.getActiveIndicator();
            if (activeIndicator != null){
                tl.setActiveIndicator(activeIndicator);
            }
            tl.setTestLineRemark(testLine.getOrdertestLineRemark());
            tl.setRegionAccount(testLine.getModifiedBy());
            tl.setUpdateTime(testLine.getModifiedDate());

            tls.add(tl);
        }
        return this.updateTestLineStatus(moduleType, tls);
    }

    /**
     * 更新
     * @param moduleType
     * @param testLineInstanceId
     * @param testMatrixIds
     * @return
     */
    public CustomResult updateMatrixStatus(TestLineModuleType moduleType, String testLineInstanceId, Set<String> testMatrixIds){
        CustomResult rspResult = new CustomResult();
        if (StringUtils.isBlank(testLineInstanceId)){
            return rspResult.fail("请求TestLineInstanceId无效.");
        }
        if (testMatrixIds == null || testMatrixIds.isEmpty()){
            return rspResult.fail(String.format("对应的TestLine(%s) TestMatrixIds为空.", testLineInstanceId));
        }
        TestLineStatusInfo testLine = new TestLineStatusInfo();
        TestLineStatus testLineStatus = null;
        switch (moduleType){
            case ChangeCancelMatrix:
                testLineStatus = TestLineStatus.findStatus(testMatrixMapper.getTestLineMatrixStatus(testLineInstanceId, testMatrixIds.iterator().next()));
                break;
            case Change:
            case ChangeUpdateCondition:
            case ChangeUpdateStandard:
            case ChangeAddMatrix:
                // TestLine状态为Entered
                testLineStatus = TestLineStatus.Entered;
                break;
            case ReTest:
            case DataEntrySave:
            case PPSummaryDataEntry:
                // TestLine状态为Entered
                testLineStatus = TestLineStatus.Entered;
                break;
            case DataEntrySumbit:
                testLineStatus = TestLineStatus.Submit;
                break;
            case Validate:
                testLineStatus = TestLineStatus.Completed;
                break;
        }
        if (testLineStatus == null){
            return rspResult.fail("请求TestLineStatus无效.");
        }
        testLine.setTestLineInstanceId(testLineInstanceId);
        testLine.setTestLineStatus(testLineStatus.getStatus());
        testLine.setTestMatrixIds(testMatrixIds);

        return this.updateTestLineStatus(moduleType, Lists.newArrayList(testLine));
    }

    /**
     * 以Sample维度Cancel 对应的Matrix
     * 必需传的参数为：
     * 1、TestSampleId
     * 2、RegionAccount
     * @param reqObject
     * @return
     */
    public CustomResult updateMatrixStatus(MatrixStatusInfo reqObject){
        CustomResult rspResult = new CustomResult();
        if (StringUtils.isBlank(reqObject.getTestSampleId())){
            return rspResult.fail("请求TestSampleId不能为空.");
        }
        if (StringUtils.isBlank(reqObject.getRegionAccount())){
            return rspResult.fail("请求RegionAccount不能为空.");
        }
        reqObject.setMatrixStatus(TestLineStatus.Cancelled.getStatus());
        reqObject.setActiveIndicator(false);
        reqObject.setUpdateTime(DateUtils.getNow());

        testMatrixMapper.updateMatrixStatusByTestSampleId(reqObject);

        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param moduleType
     * @param testLine
     * @return
     */
    public CustomResult updateTestLineStatus(TestLineModuleType moduleType, TestLineStatusInfo testLine){
        return this.updateTestLineStatus(moduleType, Lists.newArrayList(testLine));
    }

    /**
     *
     * @param moduleType
     * @param testLines
     * @return
     */
    public CustomResult updateTestLineStatus(TestLineModuleType moduleType, List<TestLineStatusInfo> testLines){
        CustomResult rspResult = new CustomResult();
        if (moduleType == null){
            return rspResult.fail("请求的功能模块类型无效.");
        }
        if (testLines == null || testLines.isEmpty()){
            return rspResult.fail("请求的更新TestLine 不能为空.");
        }
        // 这里获取不到用户信息，会导致refer整个testData失败！！！！！！！！！
        UserInfo localUser = UserHelper.getLocalUser();
        String regionAccount = "System";
        if (localUser != null){
            regionAccount = localUser.getRegionAccount();
        }
        Map<String, Integer> testLineMaps = Maps.newHashMap();

        TestlineInstanceListGetQry testlineInstanceListGetQry = new TestlineInstanceListGetQry();
        testlineInstanceListGetQry.setIncludeTestlineLanguage(false);
        testlineInstanceListGetQry.setTestlineInstanceIds(testLines.stream().map(TestLineStatusInfo::getTestLineInstanceId).collect(Collectors.toSet()));
        List<TestlineInstanceDTO> testlineInstanceList = testlineInstanceBizService.getTestlineInstanceByIds(testlineInstanceListGetQry);
        for (TestlineInstanceDTO tlInstance: testlineInstanceList) {
            testLineMaps.put(tlInstance.getId().getValue(), tlInstance.getHeader().getTestLineStatus());
        }

        Set<String> testLineInstanceIds = Sets.newHashSet();
        List<MatrixStatusInfo> testMatrixs = Lists.newArrayList();
        MatrixStatusInfo testMatrix;

        // 只在 ChangeUpdateStandard  ChangeUpdateCondition ChangeAddMatrix 时 发送/check SubContract，且数据只会有一条
        SysStatusReq reqStatus = new SysStatusReq();
        SubContractPO subContract = new SubContractPO();

        List<TestLineStatusInfo> updateTestLines = Lists.newArrayList();
        for (TestLineStatusInfo testLine: testLines) {
            String testLineInstanceId = testLine.getTestLineInstanceId();
            if (StringUtils.isBlank(testLineInstanceId)){
                return rspResult.fail("请求的TestLineInstanceId为空.");
            }
            if (testLineInstanceIds.contains(testLineInstanceId)){
                return rspResult.fail(String.format("请求的TestLineInstanceId(%s)重复.", testLineInstanceId));
            }

            if (!testLineMaps.containsKey(testLineInstanceId)){
                return rspResult.fail(String.format("该条TestLine(%s)不存在.", testLineInstanceId));
            }
            int oldTestLineStatus = testLineMaps.get(testLineInstanceId);

            testMatrix = new MatrixStatusInfo();
            testMatrix.setTestLineInstanceId(testLineInstanceId);
            testMatrix.setRegionAccount(testLine.getRegionAccount());
            testMatrix.setUpdateTime(testLine.getUpdateTime());
            testMatrix.setTestMatrixIds(testLine.getTestMatrixIds());

            if (StringUtils.isBlank(testLine.getRegionAccount())){
                testLine.setRegionAccount(regionAccount);
            }
            // DIG-8911 设置默认值
            if (testLine.getUpdateTime() == null){
                testLine.setUpdateTime(DateUtils.getNow());
            }

            TestLineStatus testLineStatus = TestLineStatus.findStatus(testLine.getTestLineStatus());
            boolean isUpdateTestLineStatus = true;

            switch (moduleType){
                case ChangeCancelMatrix:
                    // 这里的TestLineStatus与MatrixStatus 是同等的
                    TestLineStatus matrixStatus = testLineStatus;
                    if (matrixStatus == null){
                        return rspResult.fail(String.format("对应的MatrixStatus(%s)无效.", testLineInstanceId));
                    }
                    // 如果是Cancelled，则重置为TestLineStatus的状态，否则重置为Cancelled
                    testLineStatus = matrixStatus == TestLineStatus.Cancelled ? TestLineStatus.findStatus(oldTestLineStatus) : TestLineStatus.Cancelled;

                    testLine.setActiveIndicator(matrixStatus == TestLineStatus.Cancelled ? true : false);
                    isUpdateTestLineStatus = false;
                    break;
                case ChangeUpdateStandard:
                case ChangeUpdateCondition:
                case ChangeAddMatrix:
                case ReviseSubcontract: //DIG-8536 增加
                    // DIG-6914  Update Condition，Update Standard修改为 Entered
                    testLineStatus = TestLineStatus.Entered;
                    testLine.setOldTestLineStatus(oldTestLineStatus);
                    if (!TestLineStatus.check(oldTestLineStatus, TestLineStatus.Submit, TestLineStatus.Completed)) {
                        break;
                    }
                    GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderByTestLineInstanceId(testLineInstanceId);
                    if (orderInfo == null) {
                        break;
                    }
                    // 发送 状态修改
                    reqStatus.setObjectNo(orderInfo.getOrderNo());
                    reqStatus.setOldStatus(com.sgs.preorder.facade.model.enums.OrderStatus.Reporting.getStatus());
                    reqStatus.setNewStatus(com.sgs.preorder.facade.model.enums.OrderStatus.Testing.getStatus());
                    reqStatus.setUserName(testLine.getRegionAccount());

                    // change 时check subcontract
                    TestLineInstancePO testLineInstanceById = testLineMapper.getTestLineInstanceById(testLineInstanceId);
                    if (testLineInstanceById == null || !TestLineType.check(testLineInstanceById.getTestLineType(), TestLineType.SubContractOrder)) {
                        break;
                    }
                    SubContractTestLineMappingPO subContractInfo = testLineMapper.getSubContractInfoByTestLineId(testLineInstanceId);
                    if (subContractInfo == null) {
                        break;
                    }
                    subContract.setID(subContractInfo.getSubContractID());
                    subContract.setStatus(1);
                    subContract.setModifiedBy(testLine.getRegionAccount());
                    subContract.setModifiedDate(DateUtils.getNow());
                    break;
                case Change:
                case ReTest:
                case DataEntrySave:
                case DataEntrySumbit:
                case Validate:
                case PPSummaryDataEntry:
                    testMatrix.setOldMatrixStatus(oldTestLineStatus);
                    if (StringUtils.isNotBlank(testLine.getTestItemNo())) {
                        testMatrix.setTestItemNo(testLine.getTestItemNo());
                    }
                    break;
            }
            if (testLineStatus == null){
                return rspResult.fail(String.format("请求的TestLineInstanceId(%s)，TestLineStatus(%s)无效.", testLineInstanceId, testLine.getTestLineStatus()));
            }
            testLine.setTestLineStatus(testLineStatus.getStatus());
            testMatrix.setMatrixStatus(testLine.getTestLineStatus());
            testMatrix.setActiveIndicator(testLine.isActiveIndicator());

            if (!TestLineModuleType.check(moduleType.getModuleType(), TestLineModuleType.Change)) {
                testMatrixs.add(testMatrix);
            }
            testLineInstanceIds.add(testLineInstanceId);

            if (isUpdateTestLineStatus){
                updateTestLines.add(testLine);
            }
        }

        boolean isSuccess =transactionTemplate.execute((trans) -> {
            if (StringUtils.isNotBlank(reqStatus.getObjectNo())) {
                statusClient.insertStatusInfo(reqStatus);
            }
            if (StringUtils.isNotBlank(subContract.getID())) {
                subContractMapper.updateSubContractStatus(subContract);
            }

            if (!testMatrixs.isEmpty()){
                testMatrixMapper.batchUpdateMatrixStatus(moduleType.getModuleType(), testMatrixs);
            }
            if (updateTestLines.isEmpty()){
                return true;
            }
            testLineMapper.batchUpdateTestLineStatus(moduleType.getModuleType(), updateTestLines);
            return true;
        });
        rspResult.setSuccess(isSuccess);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult batchUpdateTestLineStatus(UpdateTestLineStatusReq reqObject){
        return this.updateTestLineStatus(reqObject.getModuleType(), reqObject.getTestLines());
    }
}
