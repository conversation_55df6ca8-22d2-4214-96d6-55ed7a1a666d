package com.sgs.otsnotes.domain.service.subcontract.starlims.handler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sgs.otsnotes.domain.service.subcontract.starlims.context.StarLimsReportContext;

/**
 * @Description StarLims报告处理器抽象基类
 * 提供通用的处理逻辑和异常处理
 * <AUTHOR>
 * @Date 2024-03-21
 */
public abstract class AbstractStarLimsReportHandler implements StarLimsReportHandler {
    
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    @Override
    public final boolean handle(StarLimsReportContext context) {
        if (!supports(context)) {
            return true;
        }
        
        try {
            beforeHandle(context);
            boolean result = doHandle(context);
            afterHandle(context, result);
            return result;
        } catch (Exception e) {
            logger.error("处理器执行异常", e);
            context.getResult().fail(e.getMessage());
            return false;
        }
    }
    
    /**
     * 处理器的具体处理逻辑
     */
    protected abstract boolean doHandle(StarLimsReportContext context);
    
    /**
     * 处理前的准备工作
     */
    protected void beforeHandle(StarLimsReportContext context) {
        // 默认空实现，子类可以根据需要重写
    }
    
    /**
     * 处理后的清理工作
     */
    protected void afterHandle(StarLimsReportContext context, boolean result) {
        // 默认空实现，子类可以根据需要重写
    }
} 