package com.sgs.otsnotes.domain.service.sample;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sgs.otsnotes.domain.service.sample.context.SampleBreakDownContext;
import com.sgs.otsnotes.domain.service.sample.context.SampleBreakDownResult;
import com.sgs.otsnotes.domain.service.sample.processor.SampleBreakDownProcessor;
import com.sgs.otsnotes.domain.service.sample.validator.SampleBreakDownValidator;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.req.SampleBreakDownReq;

/**
 * 样品分解门面服务
 * 
 * 重构后的样品分解处理统一入口，负责协调各个组件完成样品分解业务
 * 
 * 主要功能：
 * - 样品分解数据验证和处理
 * - 样品关系管理（原样、子样、混样、共享样）
 * - 测试矩阵维护
 * - 分包数据同步
 * - 事务性数据操作
 * 
 * 业务流程：
 * 1. 参数验证和预处理
 * 2. 加载历史数据和构建上下文
 * 3. 业务规则验证
 * 4. 样品数据处理和转换
 * 5. 测试矩阵处理
 * 6. SLIM分包处理
 * 7. 数据持久化和后置处理
 * 
 * <AUTHOR>
 * @since 2024
 */
@Service
public class SampleBreakDownFacadeService {
    
    private static final Logger logger = LoggerFactory.getLogger(SampleBreakDownFacadeService.class);
    
    // 依赖注入各个组件
    @Autowired 
    private SampleBreakDownValidator validator;
    
    @Autowired
    private SampleBreakDownProcessor processor;
    
    /**
     * 执行重构后的样品分解处理
     * 
     * 执行流程：
     * 1. 创建上下文 - 封装请求数据
     * 2. 验证阶段 - 执行所有业务规则验证
     * 3. 处理阶段 - 执行核心业务逻辑处理
     * 4. 返回结果 - 统一封装处理结果
     * 
     * @param reqObject 样品分解请求
     * @return 处理结果
     */
    public CustomResult executeRefactoredBreakDown(SampleBreakDownReq reqObject) {
        logger.info("【样品分解门面服务】开始执行重构后的样品分解，订单号: {}", reqObject.getOrderNo());
        
        try {
            // 1. 创建处理上下文
            SampleBreakDownContext context = new SampleBreakDownContext();
            context.setReqObject(reqObject);
            
            // 2. 验证阶段
            logger.info("【样品分解门面服务】开始验证阶段，订单号: {}", reqObject.getOrderNo());
            SampleBreakDownResult validationResult = validator.validate(context);
            if (!validationResult.isSuccess()) {
                logger.error("【样品分解门面服务】验证失败，订单号: {}, 错误: {}", reqObject.getOrderNo(), validationResult.getMessage());
                CustomResult result = new CustomResult();
                result.setSuccess(false);
                result.setMsg(validationResult.getMessage());
                return result;
            }
            
            // 3. 处理阶段
            logger.info("【样品分解门面服务】开始处理阶段，订单号: {}", reqObject.getOrderNo());
            SampleBreakDownResult processingResult = processor.process(validationResult.getContext());
            if (!processingResult.isSuccess()) {
                logger.error("【样品分解门面服务】处理失败，订单号: {}, 错误: {}", reqObject.getOrderNo(), processingResult.getMessage());
                CustomResult result = new CustomResult();
                result.setSuccess(false);
                result.setMsg(processingResult.getMessage());
                return result;
            }
            
            // 4. 返回成功结果
            logger.info("【样品分解门面服务】样品分解处理成功，订单号: {}", reqObject.getOrderNo());
            CustomResult result = new CustomResult();
            result.setSuccess(true);
            return result;
            
        } catch (Exception e) {
            logger.error("【样品分解门面服务】执行异常，订单号: {}", reqObject.getOrderNo(), e);
            CustomResult result = new CustomResult();
            result.setSuccess(false);
            result.setMsg("系统异常: " + e.getMessage());
            return result;
        }
    }
} 