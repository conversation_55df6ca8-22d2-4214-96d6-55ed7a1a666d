package com.sgs.otsnotes.domain.service.subcontract.starlims.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.OrderMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.GeneralOrderInstanceInfoPO;
import com.sgs.otsnotes.domain.service.subcontract.starlims.StarLimsReportReceiveService;
import com.sgs.otsnotes.domain.service.subcontract.starlims.context.StarLimsReportContext;
import com.sgs.otsnotes.domain.service.subcontract.starlims.context.StarLimsReportResult;
import com.sgs.otsnotes.domain.service.subcontract.starlims.exception.StarLimsReportException;
import com.sgs.otsnotes.domain.service.subcontract.starlims.factory.StarLimsReportHandlerFactory;
import com.sgs.otsnotes.domain.service.subcontract.starlims.handler.StarLimsReportHandler;
import com.sgs.otsnotes.facade.model.req.starlims.receive.ReceiveStarLimsReportDocBodyReq;

import lombok.extern.slf4j.Slf4j;

/*
 * StarLims报告接收服务实现类
 */
@Service
@Slf4j
public class StarLimsReportReceiveServiceImpl implements StarLimsReportReceiveService {
    
    @Autowired
    private StarLimsReportHandlerFactory handlerFactory;
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomResult<Void> receiveReportDoc(ReceiveStarLimsReportDocBodyReq request) {
        log.info("[receiveReportDoc new] 接收到请求参数：orderNo:{},reportNo:{},subContractNo:{},objectNo:{},externalObjectNo:{}",request.getOrderNo(),request.getReportNo(),request.getSubContractNo(),request.getObjectNo(),request.getExternalObjectNo());
        
        try {
            // 1. 创建上下文
            StarLimsReportContext context = createContext(request);
            
            // 2. 获取处理器链
            List<StarLimsReportHandler> handlers = handlerFactory.getHandlers(context.getProductLineType());
            
            if (handlers.isEmpty()) {
                log.warn("未找到产品线[{}]的处理器", context.getProductLineType());
                CustomResult<Void> result = new CustomResult<>();
                return result.fail("未找到对应产品线的处理器");
            }
            context.setHandlers(handlers);
            
            log.info("获取到产品线[{}]的处理器链，共{}个处理器", context.getProductLineType(), handlers.size());
            
            // 3. 执行处理器链
            for (StarLimsReportHandler handler : handlers) {
                log.info("执行处理器: {}", handler.getHandlerName());
                if (!handler.handle(context)) {
                    log.warn("处理器{}执行失败: {}", 
                        handler.getHandlerName(),
                        context.getResult().getMessage());
                    CustomResult<Void> result = new CustomResult<>();
                    return result.fail(context.getResult().getMessage());
                }
            }
            
            // 4. 处理成功
            log.info("StarLims报告接收处理成功, OrderNo: {}, SubContractNo: {}, ReportNo: {}", 
                request.getOrderNo(), request.getSubContractNo(), context.getResult().getReportNo());
            
            CustomResult<Void> result = new CustomResult<>();
            result.setSuccess(context.getResult().isSuccess());
            result.setMsg(context.getResult().getMessage());
            return result;
            
        } catch (StarLimsReportException e) {
            log.error("StarLims报告接收处理业务异常", e);
            CustomResult<Void> result = new CustomResult<>();
            return result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("StarLims报告接收处理系统异常", e);
            CustomResult<Void> result = new CustomResult<>();
            return result.fail("系统异常: " + e.getMessage());
        }
    }
    
    /**
     * 创建上下文
     * @param request 请求
     * @return 上下文
     */
    private StarLimsReportContext createContext(ReceiveStarLimsReportDocBodyReq request) {
        log.info("创建上下文, 请求: {}", request);
        if (request == null) {
            throw new StarLimsReportException("请求不能为空");
        }
        // 1. 检查产品线
        if (StringUtils.isBlank(request.getProductLineCode())) {
            throw new StarLimsReportException("产品线不能为空");
        }
        
        ProductLineType productLineType = ProductLineType.findProductLineAbbr(request.getProductLineCode());
        if (productLineType == null) {
            throw new StarLimsReportException("无效的产品线: " + request.getProductLineCode());
        }
        if(StringUtils.isBlank(request.getOrderNo())){
            throw new StarLimsReportException("订单号不能为空");
        }
        // 2. 检查订单信息
        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfo(request.getOrderNo());
        if (orderInfo == null) {
            throw new StarLimsReportException("未找到订单信息: " + request.getOrderNo());
        }
        
        // 3. 创建上下文
        return StarLimsReportContext.builder()
            .request(request)
            .orderInfo(orderInfo)
            .productLineType(productLineType)
            .result(StarLimsReportResult.builder().build())
            .build();
    }

} 