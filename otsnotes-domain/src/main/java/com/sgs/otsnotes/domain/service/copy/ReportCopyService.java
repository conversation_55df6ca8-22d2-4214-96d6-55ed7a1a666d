package com.sgs.otsnotes.domain.service.copy;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.ReportTestMatrixMergeMode;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.core.util.StringUtil;
import com.sgs.otsnotes.dbstorages.mybatis.config.DatabaseContextHolder;
import com.sgs.otsnotes.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.OrderMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportTemplateExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.SubContractExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.ReportCopyInfo;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.utils.ProductLineUtil;
import com.sgs.otsnotes.facade.model.annotation.CopyEventType;
import com.sgs.otsnotes.facade.model.annotation.CopyServiceType;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.framework.model.enums.OrderCopyType;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractReportTemplate;
import com.sgs.otsnotes.facade.model.info.user.UserLabBuInfo;
import com.sgs.otsnotes.facade.model.ordercopy.SysCopyInfo;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.req.copy.SyncCustomerInfo;
import com.sgs.otsnotes.integration.FrameWorkClient;
import com.sgs.otsnotes.integration.GpoNotesClient;
import com.sgs.otsnotes.integration.OrderClient;
import com.sgs.otsnotes.integration.dto.DefaultAccreditationRsp;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.enums.ReportLanguage;
import com.sgs.preorder.facade.model.req.BuParamReq;
import com.sgs.preorder.facade.model.rsp.TestRequestRsp;
import groovy.cli.internal.OptionAccessor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;

import static com.sgs.framework.model.enums.OrderCopyType.*;

@Service
@CopyServiceType(copyType = {
    NewCopy,
    CCOTS,
    CCOS,
    Supplement,
    TranslationReport,
    Extract,
    Replace,
    Sample,
    Conclusion,
    TestLine,
    HostSubContract,
    LightSubContract,
    ExecSubContract
}, bizType = OrderBizType.Report)
public class ReportCopyService extends BaseCopyService<String, ReportCopyInfo> {
    private static final Logger logger = LoggerFactory.getLogger(ReportCopyService.class);
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private ReportTemplateExtMapper reportTemplateExtMapper;
    @Autowired
    private SubContractExtMapper subContractExtMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private FrameWorkClient frameWorkClient;
    @Autowired
    private GpoNotesClient gpoNotesClient;

    /**
     *
     * @param reqObject1
     * @param reqParams
     * @return
     */
    @Override
    protected CustomResult<ReportCopyInfo> doInvoke(String reqObject1, SysCopyInfo reqParams) {
        CustomResult rspResult = new CustomResult(false);
        UserInfo localUser = UserHelper.getLocalUser();
        OrderCopyType copyType = reqParams.getCopyType();
        ReportInfoPO oldReport = reqParams.getAmendByReport() != null && reqParams.getAmendByReport() ? reportMapper.getReportInfoByReportId(reqParams.getOldReportId()) : reportMapper.getReportByOrderNo(reqParams.getOldOrderNo());
        if (oldReport == null){
            return rspResult.fail(String.format("未找到该订单(%s)ReportNo信息.", reqParams.getOldOrderNo()));
        }
        // 设置老的Report信息
        reqParams.setOldReportId(oldReport.getID());
        reqParams.setOldReportNo(oldReport.getReportNo());

        String oldOrderNo = reqParams.getOldOrderNo();
        TestRequestRsp oldTestRequest = orderClient.getTestRequestByOrderNo(oldOrderNo);
        OrderInfoDto orderInfoPre = orderClient.getOrderInfoByOrderNo(oldOrderNo);
        GeneralOrderInstanceInfoPO oldOrderInfo = orderMapper.getOrderInfo(oldOrderNo);
        if (oldTestRequest == null || orderInfoPre == null || oldOrderInfo == null) {
            return rspResult.fail(String.format("未找到该订单(%s)信息.", oldOrderNo));
        }


        ReportInfoPO report = null; //reportMapper.getReportInfoByReportNo(reqParams.getReportNo());
        report = new ReportInfoPO();
        report.setID(UUID.randomUUID().toString());
        if (StringUtils.isNotBlank(reqParams.getReportId())){
            report.setID(reqParams.getReportId());
        }
        report.setReportNo(reqParams.getReportNo());
        report.setOrderNo(reqParams.getOrderNo());
        report.setReportStatus(ReportStatus.New.getCode());

        // 查询Original对应的type类型id
        DatabaseContextHolder.setTargetDataSource();
        ReportTypePO reportType = this.reportMapper.getReportTypeByDesc(AmendType.Original.toString());
        if (reportType != null) {
            report.setReportTypeID(reportType.getID());
        }
        //设置固定reportTypeId
        if (copyType.check(Extract,Replace,Supplement,TranslationReport)){
            ReportLanguage oldReportLang = ReportLanguage.findName(String.valueOf(oldTestRequest.getReportLanguage()));
            Integer reportLanguage = ReportLanguage.compareLanguage(oldReportLang, ReportLanguage.ChineseReportOnly) ? ReportLanguage.ChineseReportOnly.getLanguageId():ReportLanguage.EnglishReportOnly.getLanguageId();
            AmendType amendType = null;
            switch (copyType){
                case Extract:
                    amendType = AmendType.Extract;
                    break;
                case Replace:
                    amendType = AmendType.Replaced;
                    break;
                case Supplement:
                    amendType = AmendType.Supplement;
                    break;
                case TranslationReport:
                    reportLanguage = ReportLanguage.compareLanguage(oldReportLang, ReportLanguage.EnglishReportOnly) ? ReportLanguage.ChineseReportOnly.getLanguageId():ReportLanguage.EnglishReportOnly.getLanguageId();
                    amendType = AmendType.TranslationReport;
                    break;
            }
            if (amendType == null){
                return rspResult.fail(String.format("订单(%s)copyType（%s），amendType类型为空.", reqParams.getOldOrderNo(), copyType));
            }
            ReportTypePO reportTypePO = this.reportMapper.getReportTypeByDescAndLanguage(amendType.getCode(),reportLanguage);
            if (reportTypePO != null) {
                report.setReportTypeID(reportTypePO.getID());
            }
            // add by vincent 2022年7月8日 start
            report.setParentReportNo(oldReport.getReportNo());
        }
        UserLabBuInfo labBu = reqParams.getLabBu();

        // 设置Accreditation推荐值
        this.setReportAccreditation(report, copyType, oldOrderInfo.getLabCode(), labBu.getLabCode(), reqParams.getSubContractNo(),  ProductLineContextHolder.getTargetProductLineCode(), reqParams.getSgsToken());

        report.setLabId(Optional.ofNullable(labBu.getLabId()).orElse(new Long(0L)).intValue());
        report.setLabCode(labBu.getLabCode());

        report.setCoverPageTemplateNewMappingFlag(false);
        report.setRecalculationFlag(0);
        report.setActiveIndicator(true);
        report.setCreatedBy(localUser.getRegionAccount());
        report.setCreatedDate(DateUtils.getNow());
        // add by vincent 2019年6月17日 DIG-2877
        report.setReportDueDate(reqParams.getReportDueDate());

        SyncCustomerInfo customer = reqParams.getCustomer();
        if (customer != null) {
            report.setCustomerCode(customer.getCustomerCode());
            report.setCustomerGroupCode(customer.getCustomerGroupCode());
        }
        report.setModifiedBy(localUser.getRegionAccount());
        report.setModifiedDate(DateUtils.getNow());
        // POSL-3353 WatermarkCode、LogoAliyunID设为初始值（Null）
        report.setWatermarkCode(null);
        report.setLogoAliyunID(null);

        // GPO2-8667 report 增加 actualReportNo字段
        report.setActualReportNo(reqParams.getExternalReportNo());
        report.setReportVersion(1);
        report.setTestMatrixMergeMode(ReportTestMatrixMergeMode.Host.getCode());
        report.setRootReportNo(report.getReportNo());

        ReportCopyInfo rspObject = new ReportCopyInfo();
        rspObject.setReport(report);
        rspObject.setOldReport(oldReport);
        rspObject.setCopyType(copyType);
        rspObject.setBuCode(orderInfoPre.getBUCode());
        rspObject.setLabCode(oldOrderInfo.getLabCode());

        // 如果是分包单创建的order，需要设置分包单选择的report template.
        if(copyType.check(HostSubContract, ExecSubContract, LightSubContract) &&
                !StringUtil.judgeProductLineCode(ProductLineContextHolder.getProductLineCode(), ProductLineType.SL)){
            rspObject.setReportTemplateInfoPOS(this.buildReportTemplate(reqParams, report));
        }

        // 设置新的Report信息
        reqParams.setReportId(report.getID());
        reqParams.setReportNo(report.getReportNo());

        rspResult.setData(rspObject);
        rspResult.setSuccess(true);
        if (copyType.check(HostSubContractSync)){
            rspResult.setSuccess(false);
            rspResult.setIgnore(true);
        }
        return rspResult;
    }

    /**
     * AmendReport Replace
     * @param reqObject
     * @param rspObject
     */
    @CopyEventType(copyType = { Replace, AmendReport}, execSeq = 1)
    public void doAmendReportReplace(SysCopyInfo reqObject, ReportCopyInfo rspObject) {
        if (rspObject == null) {
            return;
        }
        UserInfo localUser = UserHelper.getLocalUser();

        ReportInfoPO oldReport = new ReportInfoPO();
        oldReport.setID(reqObject.getOldReportId());
        oldReport.setReportStatus(ReportStatus.Replaced.getCode());
        oldReport.setReportNo(reqObject.getOldReportNo());
        oldReport.setModifiedBy(localUser.getRegionAccount());
        oldReport.setModifiedDate(DateUtils.getNow());

        rspObject.setOldReport(oldReport);
    }

    /**
     * 重置订单状态 订单号
     *
     * @param reqObject
     * @param rspObject
     */
    @CopyEventType(copyType = { Sample, Conclusion, TestLine }, execSeq = 2)
    public void doSplitReport(SysCopyInfo reqObject, ReportCopyInfo rspObject) {
        if (rspObject == null) {
            return;
        }
        // 获取用户信息
        UserInfo localUser = UserHelper.getLocalUser();
        String oldReportNoUpdate = new StringBuffer(reqObject.getOldOrderNo()).insert(14, "00").toString();
        ReportInfoPO oldReport = new ReportInfoPO();
        oldReport.setID(reqObject.getOldReportId());
        oldReport.setReportStatus(ReportStatus.Cancelled.getCode());
        oldReport.setReportNo(oldReportNoUpdate);
        oldReport.setModifiedBy(localUser.getRegionAccount());
        oldReport.setModifiedDate(DateUtils.getNow());

        reqObject.setOldReportNoUpdate(oldReportNoUpdate);

        rspObject.setOldReport(oldReport);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @Override
    public CustomResult doCopy(ReportCopyInfo reqObject) {
        CustomResult rspResult = new CustomResult(false);
        ReportInfoPO oldReport = reqObject.getOldReport();
        OrderCopyType copyType = reqObject.getCopyType();
        if (oldReport != null && copyType.check(Sample, Conclusion, TestLine, Replace)){
            // split拆分时，更新订单号
            reportMapper.updateReportStatusInfo(oldReport);
        }
        ReportInfoPO report = reqObject.getReport();
        if (report == null || StringUtils.isBlank(report.getReportNo())){
            // DIG-8555 A "NullPointerException" could be thrown; "report" is nullable here.
            rspResult.setMsg(String.format("当前订单(%s)Report无效.", Objects.nonNull(report) ? report.getOrderNo() : "null"));
            //rspResult.setMsg(String.format("当前订单(%s)Report无效.", report.getOrderNo()));
            return rspResult;
        }
        if (CollectionUtils.isNotEmpty(reqObject.getReportTemplateInfoPOS())) {
            reportTemplateExtMapper.batchSaveReportTemplate(reqObject.getReportTemplateInfoPOS());
        }
        rspResult.setSuccess(reportMapper.saveReportInfo(report) > 0);

        // 20220414 & miao 判断后  不记log
//        this.sendBizLog(reqObject);

        return rspResult;
    }

    /**
     * @param reqParams
     * @param report
     * @return
     */
    private List<ReportTemplateInfoPO> buildReportTemplate(SysCopyInfo reqParams, ReportInfoPO report) {
        UserInfo localUser = UserHelper.getLocalUser();
        List<ReportTemplateInfoPO> reportTemplateInfoPOS = Lists.newArrayList();
        SubContractInfo subContractInfo = subContractExtMapper.getSubContractRequireInfo(reqParams.getSubContractNo());
        if (!ObjectUtils.isEmpty(subContractInfo) && StringUtils.isNotEmpty(subContractInfo.getReportTemplate())) {
            SubContractReportTemplate subContractReportTemplate = JSON.parseObject(subContractInfo.getReportTemplate(), SubContractReportTemplate.class);
            report.setCoverPageTemplateName(subContractReportTemplate.getTemplateName());
            report.setCoverPageTemplatePath(subContractReportTemplate.getDigReportBuId().toString());

            ReportTemplateInfoPO reportTemplateInfoPOCommon = new ReportTemplateInfoPO();
            reportTemplateInfoPOCommon.setTemplateName(subContractReportTemplate.getTemplateName());
            reportTemplateInfoPOCommon.setReportId(report.getID());
            reportTemplateInfoPOCommon.setActiveIndicator(true);
            reportTemplateInfoPOCommon.setCreatedBy(localUser.getRegionAccount());
            reportTemplateInfoPOCommon.setCreatedDate(DateUtils.getNow());

            // 分包单的模板需要存入数据库
            if (!ObjectUtils.isEmpty(subContractReportTemplate.getTemplateSettingIdCN())) {
                ReportTemplateInfoPO reportTemplateInfoPO = new ReportTemplateInfoPO();
                BeanUtils.copyProperties(reportTemplateInfoPOCommon, reportTemplateInfoPO);
                reportTemplateInfoPO.setId(UUID.randomUUID().toString());
                reportTemplateInfoPO.setLanguageId(LanguageType.CHI.getLanguageId());
                reportTemplateInfoPO.setTemplateSettingId(subContractReportTemplate.getTemplateSettingIdCN());
                reportTemplateInfoPOS.add(reportTemplateInfoPO);
//                reportTemplateInfoMapper.insert(reportTemplateInfoPO);
            }
            if (!ObjectUtils.isEmpty(subContractReportTemplate.getTemplateSettingIdEN())) {
                ReportTemplateInfoPO reportTemplateInfoPO = new ReportTemplateInfoPO();
                BeanUtils.copyProperties(reportTemplateInfoPOCommon, reportTemplateInfoPO);
                reportTemplateInfoPO.setId(UUID.randomUUID().toString());
                reportTemplateInfoPO.setLanguageId(LanguageType.EN.getLanguageId());
                reportTemplateInfoPO.setTemplateSettingId(subContractReportTemplate.getTemplateSettingIdEN());
                reportTemplateInfoPOS.add(reportTemplateInfoPO);
//                reportTemplateInfoMapper.insert(reportTemplateInfoPO);
            }
            return reportTemplateInfoPOS;
        }



        return reportTemplateInfoPOS;
    }

    /**
     * bizlog 方法没有被使用，如果使用的话，reqObject里面的bu ,lab一定要用order的，
     * @param reqObject
     */
    private void sendBizLog(ReportCopyInfo reqObject) {
        if (reqObject == null) {
            return;
        }
        ReportInfoPO oldReport = reqObject.getOldReport();
        ReportInfoPO report = reqObject.getReport();
        if (!ObjectUtils.isEmpty(oldReport)) {
            //旧单保存BizLog，报告状态更新
            BizLogInfo bizLogInfo = new BizLogInfo();
            bizLogInfo.setBu(reqObject.getBuCode());
            bizLogInfo.setLab(ProductLineUtil.getProductLineCode(reqObject.getLabCode()).getLocationCode());//reqObject.getLabCode().split(" ")[0]
            bizLogInfo.setOpUser(oldReport.getModifiedBy());
            bizLogInfo.setBizId(oldReport.getReportNo());
            bizLogInfo.setOpType(ReportOperationTypeEnum.ORDER_COPY.getType());
            bizLogInfo.setBizOpType(BizLogConstant.REPORT_STATUS_CHANGE_HISTORY);
            bizLogInfo.setNewVal(oldReport.getReportStatus());
            bizLogInfo.setOriginalVal(oldReport.getReportStatus());
            logger.info("report copy old  bizLog param: {}", JSON.toJSONString(bizLogInfo));
            bizLogClient.doSend(bizLogInfo);
        }

        //保存BizLog，报告状态更新
        BizLogInfo bizLog = new BizLogInfo();
        bizLog.setBu(reqObject.getBuCode());
        bizLog.setLab(ProductLineUtil.getProductLineCode(reqObject.getLabCode()).getLocationCode());//reqObject.getLabCode().split(" ")[0]
        bizLog.setOpUser(report.getCreatedBy());
        bizLog.setBizId(report.getReportNo());
        bizLog.setOpType(ReportOperationTypeEnum.ORDER_COPY.getType());
        bizLog.setBizOpType(BizLogConstant.REPORT_STATUS_CHANGE_HISTORY);
        bizLog.setNewVal(report.getReportStatus());
        bizLog.setOriginalVal(null);
        logger.info("report copy bizLog param: {}", JSON.toJSONString(bizLog));
        bizLogClient.doSend(bizLog);
    }

    /**
     * POSL-6191
     * @param report
     * @param copyType
     * @param oldLabCode
     * @param labCode
     * @param subContractNo
     * @param targetProductLineCode
     * @param sgsToken
     */
    private void setReportAccreditation(ReportInfoPO report, OrderCopyType copyType, String oldLabCode, String labCode, String subContractNo, String targetProductLineCode, String sgsToken) {
        if (report == null) {
            return;
        }
        if (copyType == null || !copyType.check(HostSubContract, ExecSubContract, LightSubContract)) {
            return;
        }
        // 获取配置
        if (!queryReportAccreditation(targetProductLineCode)) {
            return;
        }

        DefaultAccreditationRsp defaultAccreditation = gpoNotesClient.getDefaultAccreditation(oldLabCode, labCode, subContractNo, sgsToken);
        if (defaultAccreditation == null) {
            return;
        }
        report.setCertificateId(defaultAccreditation.getCertificateId());
        report.setCertificateFileCloudKey(defaultAccreditation.getCertificateFileCloudKey());
        report.setCertificateName(defaultAccreditation.getCertificateName());
    }

    /**
     *
     * @param productLineCode
     * @return
     */
    private Boolean queryReportAccreditation(String productLineCode) {
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setGroupCode(Constants.BU_PARAM.SUBCONTRACT.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.SUBCONTRACT.CODE.Report_Accreditation);
        buParamReq.setProductLineCode(productLineCode);
        BuParamValueRsp buParam = frameWorkClient.getBuParam(buParamReq);
        if (buParam == null || StringUtils.isBlank(buParam.getParamValue())) {
            return Boolean.FALSE;
        }
        return StringUtils.equalsIgnoreCase(Constants.BU_PARAM.SUBCONTRACT.CODE.VALUES.TRUE, buParam.getParamValue());
    }

}
