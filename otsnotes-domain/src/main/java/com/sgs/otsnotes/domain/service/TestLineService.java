package com.sgs.otsnotes.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.extsystem.facade.model.customer.req.CheckTestLineMappingReq;
import com.sgs.extsystem.facade.model.customer.req.MappingTestLineReq;
import com.sgs.extsystem.facade.model.customer.rsp.CheckTestLineMappingRsp;
import com.sgs.file.facade.model.req.EmpInfoReq;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.util.LabUtil;
import com.sgs.framework.model.enums.ConclusionFlag;
import com.sgs.framework.model.enums.PriorityLevel;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.model.enums.*;
import com.sgs.framework.tool.utils.CollectionUtil;
import com.sgs.framework.core.util.LOStringUtil;
import com.sgs.grus.async.AsyncCall;
import com.sgs.grus.async.AsyncResult;
import com.sgs.grus.async.AsyncUtils;
import com.sgs.grus.bizlog.common.BizLog;
import com.sgs.grus.bizlog.common.BizLogHelper;
import com.sgs.notes.common.facade.model.dto.testLineStyle.TestLineStyleDto;
import com.sgs.notes.common.facade.model.req.testLineStyle.QueryTestLineStyleReq;
import com.sgs.otsnotes.core.annotation.AccessRule;
import com.sgs.otsnotes.core.annotation.BindOldMethod;
import com.sgs.otsnotes.core.common.RedisHelper;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.config.SysConstants;
import com.sgs.otsnotes.core.config.ThreadConfig;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.kafka.KafkaProducer;

import com.sgs.otsnotes.core.util.*;
import com.sgs.otsnotes.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.otsnotes.dbstorages.mybatis.enums.TagNameEnum;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.PPTestLineInfo;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.customervalidate.CheckDataForSCIReq;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.customervalidate.CheckDataForSCIRsp;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.reporttemplate.TestResultTemplateInfo;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.common.TestLineEvaluationAliasUtil;
import com.sgs.otsnotes.domain.service.customervalidate.ValidateDataService;
import com.sgs.otsnotes.domain.service.reportdata.ReportDataHandlerService;
import com.sgs.otsnotes.domain.service.testline.TestLineStatusService;
import com.sgs.otsnotes.domain.service.testlineremark.TestLineRemarkManager;
import com.sgs.otsnotes.domain.service.testlineremark.TestLineRemarkService;
import com.sgs.otsnotes.domain.service.trimslocal.TrimsLocalCitationService;
import com.sgs.otsnotes.domain.service.trimslocal.TrimsLocalTestLineService;
import com.sgs.otsnotes.domain.utils.ProductLineUtil;
import com.sgs.otsnotes.facade.model.common.BizException;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.comparator.StandardComparator;
import com.sgs.otsnotes.facade.model.comparator.TestLineComparator;
import com.sgs.otsnotes.facade.model.comparator.TestLineNoStyleComparator;
import com.sgs.otsnotes.facade.model.comparator.TestLineSubContractComparator;
import com.sgs.otsnotes.facade.model.dto.*;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractOrderInfoDTO;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubcontractPPSampleDTO;
import com.sgs.otsnotes.facade.model.enums.JobStatus;
import com.sgs.otsnotes.facade.model.enums.LanguageType;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.otsnotes.facade.model.enums.SampleType;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.enums.TestLineStatus;
import com.sgs.otsnotes.facade.model.info.*;
import com.sgs.otsnotes.facade.model.info.digitalreport.*;
import com.sgs.otsnotes.facade.model.info.job.JobInfo;
import com.sgs.otsnotes.facade.model.info.job.JobStatusInfo;
import com.sgs.otsnotes.facade.model.info.report.front.DigitalReportResp;
import com.sgs.otsnotes.facade.model.info.sample.TestLineSampleTypeInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.BUSettingSubContractEmailDTO;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractExtInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.print.SubcontractDigitalInfoBean;
import com.sgs.otsnotes.facade.model.info.subcontract.print.SubcontractDigitalPrintInfo;
import com.sgs.otsnotes.facade.model.info.testline.*;
import com.sgs.otsnotes.facade.model.info.trims.TestAnalyteLanguage;
import com.sgs.otsnotes.facade.model.kafka.GeneralMessage;
import com.sgs.otsnotes.facade.model.kafka.KafkaTopicConsts;
import com.sgs.otsnotes.facade.model.po.SubcontractRelInfoPO;
import com.sgs.otsnotes.facade.model.po.TestLineReportPo;
import com.sgs.otsnotes.facade.model.req.GetTestLinePPNameZHReq;
import com.sgs.otsnotes.facade.model.req.PhysicDeleteReq;
import com.sgs.otsnotes.facade.model.req.TestLineBreakDownReq;
import com.sgs.otsnotes.facade.model.req.TestLineReportParamReq;
import com.sgs.otsnotes.facade.model.req.condition.GetTestLineInfoForReTestReq;
import com.sgs.otsnotes.facade.model.req.condition.ReTestReq;
import com.sgs.otsnotes.facade.model.req.dataentry.ReturnTestLineReq;
import com.sgs.otsnotes.facade.model.req.fast.*;
import com.sgs.otsnotes.facade.model.req.subcontract.GetOrderInfoRsp;
import com.sgs.otsnotes.facade.model.req.testLine.*;
import com.sgs.otsnotes.facade.model.rsp.*;
import com.sgs.preorder.facade.model.req.BuParamReq;
import org.apache.commons.lang3.StringUtils;
import com.sgs.otsnotes.facade.model.rsp.empinfo.EmpInfoExtRsp;
import com.sgs.otsnotes.facade.model.rsp.master.PreOrder;
import com.sgs.otsnotes.facade.model.rsp.matrix.OrderSubcontractRelMatrixRsp;
import com.sgs.otsnotes.facade.model.rsp.matrix.TestMatrixWithTestLineRsp;
import com.sgs.otsnotes.facade.model.rsp.report.GetTestLineConclusionRsp;
import com.sgs.otsnotes.facade.model.rsp.report.ReportTestLineConclusionDTO;
import com.sgs.otsnotes.facade.model.rsp.subcontract.SampleRsp;
import com.sgs.otsnotes.facade.model.rsp.subcontract.SubContractPrintRsp;
import com.sgs.otsnotes.facade.model.rsp.subcontract.TestLineInstanceSubContractRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.QueryTestLineRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.*;
import com.sgs.otsnotes.facade.model.rsp.trims.ProductLineRsp;
import com.sgs.otsnotes.infra.repository.pp.PpRepository;
import com.sgs.otsnotes.infra.repository.pp.PpTestLineAssembler;
import com.sgs.otsnotes.infra.repository.testline.TestLineRepository;
import com.sgs.otsnotes.infra.service.TestSampleLangService;
import com.sgs.otsnotes.integration.*;
import com.sgs.otsnotes.integration.old.OldTestLineClient;
import com.sgs.otsnotes.integration.trimslocal.*;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.dto.order.OrderTrfRelationshipDTO;
import com.sgs.preorder.facade.model.enums.CustomerType;
import com.sgs.preorder.facade.model.enums.CustomerWeightType;
import com.sgs.preorder.facade.model.enums.OperationType;
import com.sgs.preorder.facade.model.enums.ReportLanguage;
import com.sgs.preorder.facade.model.info.customer.CustomerBuyerGroupInfo;
import com.sgs.preorder.facade.model.info.user.UserLabBuInfo;
import com.sgs.preorder.facade.model.req.JobInfoReq;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import com.sgs.preorder.facade.model.req.SysStatusReq;
import com.sgs.preorder.facade.model.req.job.JobReq;
import com.sgs.preorder.facade.model.req.report.ReportLangReq;
import com.sgs.preorder.facade.model.rsp.SlOrderInfoRep;
import com.sgs.preorder.facade.model.rsp.customer.CustomerSimplifyInfoRsp;
import com.sgs.preorder.facade.model.rsp.order.OrderSimplifyInfoRsp;
import com.sgs.testdatabiz.facade.model.info.TestDataInfo;
import com.sgs.testdatabiz.facade.model.req.TestDataQueryReq;
import com.sgs.testdatabiz.facade.model.req.fast.TestDataInfoReq;
import com.sgs.trimslocal.facade.model.analyte.req.QueryTestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.req.TestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.GetAnalyteInfoRsp;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteRsp;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteUnitRsp;
import com.sgs.trimslocal.facade.model.artifact.rsp.CsppTestLineInfoRsp;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactInfoRsp;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactRsp;
import com.sgs.trimslocal.facade.model.citation.rsp.CitationInfoRsp;
import com.sgs.trimslocal.facade.model.citation.rsp.CitationLangRsp;
import com.sgs.trimslocal.facade.model.citation.rsp.CitationListRsp;
import com.sgs.trimslocal.facade.model.citation.rsp.CitationNameRsp;
import com.sgs.trimslocal.facade.model.enums.PpStatusEnum;
import com.sgs.trimslocal.facade.model.labsection.rsp.GetLabSectionBaseInfoRsp;
import com.sgs.trimslocal.facade.model.language.rsp.GetArtifactCitationLanguageRsp;
import com.sgs.trimslocal.facade.model.language.rsp.GetTestLineLanguageRsp;
import com.sgs.trimslocal.facade.model.pp.req.SearchPpInfoReq;
import com.sgs.trimslocal.facade.model.pp.rsp.GetPpBaseInfoRsp;
import com.sgs.trimslocal.facade.model.pp.rsp.GetSubPPSectionInfoRsp;
import com.sgs.trimslocal.facade.model.pp.rsp.PpOrderingRsp;
import com.sgs.trimslocal.facade.model.pp.rsp.SearchPpInfoRsp;
import com.sgs.trimslocal.facade.model.testline.req.GetTestLineEvaluationAliasItemReq;
import com.sgs.trimslocal.facade.model.testline.req.QueryPpTestLineReq;
import com.sgs.trimslocal.facade.model.testline.req.TestLineCitationReq;
import com.sgs.trimslocal.facade.model.testline.rsp.*;
import com.sgs.trimslocal.facade.model.testline.rsp.testlinecitation.CitationBaseInfoRsp;
import com.sgs.trimslocal.facade.model.testline.rsp.testlinecitation.GetTestLineCitationInfoRsp;
import com.sgs.trimslocal.facade.model.testline.rsp.testlinecitation.TestLineCitationRsp;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.concurrent.ListenableFutureCallback;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.sgs.otsnotes.facade.model.enums.EnvironmentType.Dev;
import static com.sgs.otsnotes.facade.model.enums.EnvironmentType.Local;

// 添加CustomerClientV2相关的import语句
import com.sgs.otsnotes.integration.v2.CustomerClientV2;
import com.sgs.otsnotes.integration.dto.CustomerChangedListQueryRequest;
import com.sgs.otsnotes.integration.dto.CustomerChangedInfo;
import com.sgs.otsnotes.integration.dto.CustomerGroupQueryRequest;
import com.sgs.otsnotes.integration.dto.CustomerPageDto;
import com.sgs.otsnotes.integration.dto.CustomerGroupInfo;

/**
 *
 */
@Service
public class TestLineService {
    private static final Logger logger = LoggerFactory.getLogger(TestLineService.class);

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private PPBaseMapper ppBaseMapper;
    @Autowired
    private TestLineBaseMapper testLineBaseMapper;
    @Autowired
    private TestSampleMapper testSampleMapper;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private FileClient fileClient;

    @Autowired
    private SubContractTestLineMappingMapper subContractTestLineMappingMapper;
    @Autowired
    private JobTestLineRelationshipInfoMapper jobTestLineRelationshipInfoMapper;
    @Autowired
    private JobInfoMapper jobInfoMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private StatusClient statusClient;
    @Autowired
    private JobClient jobClient;
    @Autowired
    private TestMatrixInfoMapper testMatrixInfoMapper;
    @Autowired
    private SubContractMapper subContractMapper;
    @Autowired
    private SubContractExtMapper subContractExtMapper;
    @Autowired
    private PreOrderClient preOrderClient;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private CustomerClientV2 customerClientV2;
    @Autowired
    private PPTestLineRelMapper ppTestLineRelMapper;
    @Autowired
    private TestLineTatConfigMapper testLineTatConfigMapper;
    @Autowired
    private OrderLanguageRelMapper orderLanguageRelMapper;
    @Autowired
    private TestLineCitationLangMapper testLineCitationLangMapper;
    @Autowired
    private TestDataMapper testDataMapper;
    @Autowired
    private TestLineInstanceMapper testLineInstanceMapper;
    @Autowired
    private GeneralOrderInstanceInfoMapper generalOrderInstanceInfoMapper;
    @Autowired
    private  TestMatrixService testMatrixService;
    @Autowired
    private PPMapper ppMapper;
    @Autowired
    private PpRepository ppRepository;
    @Autowired
    private PpTestLineAssembler ppTestLineAssembler;
    @Autowired
    private TestMatrixMapper testMatrixMapper;
    @Autowired
    private ConclusionInfoExtMapper conclusionMapper;
    @Autowired
    private ReportInfoMapper reportInfoMapper;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private JobExtMapper jobExtMapper;
    @Autowired
    private ArtifactCitationRelInfoMapper testLineCitationRelInfoMapper;
    @Autowired
    private ArtifactCitationLangInfoMapper testLineCitationLanguageInfoMapper;
    @Autowired
    private AnalyteMapper analyteMapper;
    @Autowired
    private OrderReportClient orderReportClient;
    @Autowired
    private OrderLangRelService orderLangRelService;
    @Autowired
    private TestSampleInfoMapper testSampleInfoMapper;
    @Autowired
    private TrimsClient trimsClient;
    @Autowired
    private JobService jobService;
    @Autowired
    private OrderCitationRelService orderCitationRelService;
    @Resource
    private KafkaProducer kafkaProducer;
    @Autowired
    private ReferDataRelationshipMapper referDataRelationshipMapper;
    @Autowired
    private PPArtifactRelService ppArtifactRelService;
    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private TestLineInstanceExtMapper testLineInstanceExtMapper;
    @Autowired
    private TestLineLocalService testLineLocalService;
    @Autowired
    private ProtocolPackageTestLineRelationshipService protocolPackageTestLineRelationshipService;
    @Autowired
    private PPSampleRelMapper ppSampleRelMapper;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private SampleClaimRelExtMapper sampleClaimRelExtMapper;
    @Autowired
    private EnvUtil envUtil;
    @Autowired
    private TrimsLocalTestLineService trimsLocalTestLineService;
    @Autowired
    private TestLineStatusService testLineStatusService;
    @Autowired
    private OrderSubcontractRelService orderSubcontractRelService;
    @Autowired
    private RelatePretreatmentTestLineExtMapper relatePretreatmentTestLineExtMapper;
    @Autowired
    private CitationClient citationClient;
    @Autowired
    private TestLineClient testLineClient;
    @Autowired
    private PpClient ppClient;
    @Autowired
    private LabSectionClient labSectionClient;
    @Autowired
    private LanguageClient languageClient;
    @Autowired
    private TestConditionGroupMapper testConditionGroupMapper;
    @Autowired
    private TrimsLocalCitationService trimsLocalCitationService;
    @Autowired
    private CustomerConclusionClient customerConclusionClient;
    @Autowired
    private AnalyteClient analyteClient;
    @Autowired
    private SubContractOperateService subContractOperateService;
    @Autowired
    private OrderSubcontractRelMapper subcontractRelMapper;
    @Autowired
    private TestLineWorkInstructionRelationshipService testLineWorkInstructionRelationshipService;
    @Autowired
    private ExtCustomerConfigClient extCustomerConfigClient;
    @Autowired
    private FileServiceTestDataClient fileServiceTestDataClient;
    @Autowired
    private TestDataBizClient testDataBizClient;
    @Autowired
    private TestSampleLangMapper testSampleLangMapper;
    @Autowired
    private PpArtifactRelClient ppArtifactRelClient;

    @Autowired
    private ReportTemplateExtMapper reportTemplateExtMapper;

    @Autowired
    private TestSampleLangService testSampleLangService;

    @Autowired
    private TestLineStyleClient testLineStyleClient;

    @Autowired
    private CrossLabExtMapper crossLabExtMapper;
    @Autowired
    private UserManagementClient userManagementClient;

    @Autowired
    private ReportTemplateExtMapper templateExtMapper;
    @Autowired
    private JobTestLineRelExtMapper jobTestLineRelExtMapper;
    @Autowired
    private FrameWorkClient frameWorkClient;
    @Autowired
    private TestLineValidateService testLineValidateService;
    @Autowired
    private DigitalReportClient digitalReportClient;
    @Autowired
    private TestLineRepository testLineRepository;
    @Autowired
    private ValidateDataService validateDataService;
    @Autowired
    private ReportDataHandlerService reportDataHandlerService;
    @Autowired
    private TestLineRemarkService testLineRemarkService;
    @Autowired
    private TestLineInstanceExtInfoMapper testLineInstanceExtInfoMapper;

    /**
     * 判断客户组列表是否有效
     * 规则：所有item的activeIndicator必须等于"A"
     * @param groupInfoList 客户组信息列表
     * @return 如果所有item都有效返回true，否则返回false
     */
    private boolean isValidCustomerGroup(List<CustomerGroupInfo> groupInfoList) {
        if (CollectionUtils.isEmpty(groupInfoList)) {
            return false;
        }
        
        // 检查所有item的activeIndicator是否都等于"A"
        return groupInfoList.stream()
                .allMatch(groupInfo -> "A".equals(groupInfo.getActiveIndicator()));
    }

    /**
     * 验证customerGroupCode的有效性
     * @param customerGroupCode 客户组代码
     * @param buCode 业务单元代码
     * @throws BizException 如果存在多个有效customerGroup
     */
    private void validateCustomerGroupCode(String customerGroupCode, String buCode) {
        if (StringUtils.isBlank(customerGroupCode)) {
            return;
        }

        //order detail加签，对比 保存前后数据是否变更
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setProductLineCode(buCode);
        buParamReq.setGroupCode(Constants.BU_PARAM.TEST_LINE.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.TEST_LINE.CODE.CUSTOMER_GROUP_VALIDATE_SWITCH);
        buParamReq.setSystemId(SysConstants.SYSTEMID);
        boolean validateCustomerSwitch = frameWorkClient.getBuParamSwitch(buParamReq);
        if(!validateCustomerSwitch){
            return;
        }
        // 调用queryCustomerChangedList查询变更列表
        CustomerChangedListQueryRequest changedListQueryRequest = new CustomerChangedListQueryRequest();
        changedListQueryRequest.setCustomerGroupCode(customerGroupCode);
        changedListQueryRequest.setBu(Lists.newArrayList(buCode));
        
        BaseResponse<List<CustomerChangedInfo>> changedListResponse = customerClientV2.queryCustomerChangedList(changedListQueryRequest);
        if (changedListResponse == null || changedListResponse.getData() == null) {
            return;
        }
        
        // 提取并去重customerGroupCode
        Set<String> uniqueCustomerGroupCodes = changedListResponse.getData().stream()
                .map(CustomerChangedInfo::getCustomerGroupCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        
        // 检查每个customerGroupCode是否有效
        int validCustomerGroupCount = 0;
        for (String groupCode : uniqueCustomerGroupCodes) {
            CustomerGroupQueryRequest groupQueryRequest = new CustomerGroupQueryRequest();
            groupQueryRequest.setGroupCode(groupCode);
            
            // 修复：CustomerClientV2.queryCustomerGroupList返回的是CustomerPageDto<CustomerGroupInfo>，而不是BaseResponse
            CustomerPageDto<CustomerGroupInfo> customerPageDto = customerClientV2.queryCustomerGroupList(groupQueryRequest);
            // 优化：增加对customerPageDto.getRows()中item有效性判断
            if (customerPageDto != null && isValidCustomerGroup(customerPageDto.getRows())) {
                validCustomerGroupCount++;
            }
        }
        
        // 如果存在多个有效customerGroup，抛出错误
        if (validCustomerGroupCount > 1) {
            throw new BizException("Multiple valid customers found, operation not possible.");
        }
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getPPList(QueryPPReq reqObject){
        CustomResult rspResult = new CustomResult();
        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());
        if (order == null){
            rspResult.setMsg("Order is  null.");
            return rspResult;
        }
        if(!(StringUtils.isNotEmpty(reqObject.getClient())
                ||StringUtils.isNotEmpty(reqObject.getClientPPRef())
                ||(reqObject.getPpNo()!=null&&reqObject.getPpNo().intValue()>0))){
            rspResult.setMsg("At least one query parameter");
            return rspResult;
        }

        //如果是转单的，只能host lab 或者top lab操作
        //转单的话，用转单的labId
        String labCode = order.getLabCode();
        CustomResult<UserLabBuInfo> labRspResult = this.checkAndGetLabId(order.getOrderNo(),labCode);
        if(!labRspResult.isSuccess()){
            return rspResult.fail(labRspResult.getMsg());
        }

        String customerGroupCode = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(reqObject.getClient())) {
            customerGroupCode = order.getCustomerGroupCode();
            CustomResult<CustomerBuyerGroupInfo> customResult=customerClient.getCustomerWeightInfo(order.getOrderNo(), CustomerWeightType.TestPackage);
            if(customResult.isSuccess()&&customResult.getData()!=null){
                customerGroupCode = customResult.getData().getBuyerGroup();
            }
        }
        
        // 验证customerGroupCode
        try {
            validateCustomerGroupCode(customerGroupCode, LabUtil.resolveLab(labCode).getBuCode());
        } catch (BizException e) {
            rspResult.setMsg(e.getMessage());
            return rspResult;
        }
        
        return this.getPPListFromTrimsLocal(reqObject, customerGroupCode);
    }

    /**
     *
     * @param reqObject
     * @param customerGroupCode
     * @return
     */
    private CustomResult<List<QueryPPRsp>> getPPListFromTrimsLocal(QueryPPReq reqObject, String customerGroupCode) {
        List<QueryPPRsp> pps = Lists.newArrayList();
        // DIG-6900 对接本地化
        SearchPpInfoReq searchPpInfoReq = new SearchPpInfoReq();
        searchPpInfoReq.setPpNo(reqObject.getPpNo());
        searchPpInfoReq.setCustomerGroupCode(customerGroupCode);
        searchPpInfoReq.setPpName(reqObject.getClientPPRef());
        searchPpInfoReq.setPpTagBlackList(Lists.newArrayList(TagNameEnum.CHKchemicalPP.getName()));
        //DIG-8839 Customer Account PP
        searchPpInfoReq.setDeltaPpTypeList(Lists.newArrayList(TagNameEnum.GeneralPPUsedByCustomer.getName()));
        //2021年4月14日 当有ppNo作为查询条件时，不需要使用bu
        if(reqObject.getPpNo()==null || reqObject.getPpNo().compareTo(0)==0){
            String bu = tokenClient.getUserProductLineCodeByLab(tokenClient.getUserLabCode(tokenClient.getToken()));
            ProductLineType productLineType = ProductLineType.findProductLineAbbr(bu);
            searchPpInfoReq.setProductLineId(productLineType.getProductLineId());
        }
        CustomResult<List<SearchPpInfoRsp>> ppListResult = ppClient.getPpList(searchPpInfoReq);
        List<SearchPpInfoRsp> ppList =ppListResult.getData();
        if (CollectionUtils.isEmpty(ppList)) {
            CustomResult customResult = new CustomResult(ppListResult.isSuccess());
            customResult.setMessage(ppListResult.getMessage());
            customResult.setMsg(ppListResult.getMessage());
            return customResult;
        }
        ppList.forEach(pp -> {
            QueryPPRsp queryPPRsp = new QueryPPRsp();
            queryPPRsp.setPpId(pp.getPpBaseId());
            queryPPRsp.setPpName(pp.getPpName());
            queryPPRsp.setVersionId(pp.getPpVersionId());
            queryPPRsp.setPpNo(pp.getPpNo());
            pps.add(queryPPRsp);
        });

        return CustomResult.newSuccessInstance(pps);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getPPTestLineList(QueryPPTestLineReq reqObject){
        CustomResult rspResult = new CustomResult();
        Long ppId = reqObject.getPpId();
        Integer ppNo = reqObject.getPpNo();
        if (ppId == null || ppId.longValue() <= 0 || ppNo == null || ppNo.intValue() <= 0){
            rspResult.setMsg("PpId is not null.");
            return rspResult;
        }
        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());
        if (order == null){
            rspResult.setMsg("Order is not null.");
            return rspResult;
        }

        List<QueryTestLineRsp> ppTestlines= Lists.newArrayList();
        reqObject.setLabId(order.getOrderLaboratoryID());
        //转单的话，用转单的labId
        String orderLabCode = order.getLabCode();
        CustomResult<UserLabBuInfo> result = this.checkAndGetLabId(order.getOrderNo(), orderLabCode);
        if(!result.isSuccess()){
            return result;
        }
        UserLabBuInfo data = result.getData();
        if(data != null && NumberUtil.toInt(data.getLabId())>0){
            reqObject.setLabId(Math.toIntExact(data.getLabId()));
        }

        ppTestlines.addAll(this.getPpTestLineListFromTrimsLocal(reqObject, order));
        rspResult.setSuccess(true);
        rspResult.setData(ppTestlines);
        return rspResult;
    }

    /**
     *页面根据pp search TL功能
     * @param reqObject
     * @param order
     * @return
     */
    private List<QueryTestLineRsp> getPpTestLineListFromTrimsLocal(QueryPPTestLineReq reqObject, GeneralOrderInstanceInfoPO order) {
        List<QueryTestLineRsp> ppTestlines= Lists.newArrayList();

        boolean isChinese = isChinese(order);
        // DIG-6900 getPpTestLineList 对接本地化
        QueryPpTestLineReq queryPpTestLineReq = new QueryPpTestLineReq();
        queryPpTestLineReq.setLabId(reqObject.getLabId());
        queryPpTestLineReq.setPpBaseId(reqObject.getPpId());
        // 去掉PpName PpNo 参数
//        queryPpTestLineReq.setPpName(reqObject.getPpName());
//        queryPpTestLineReq.setPpNo(reqObject.getPpNo());
        queryPpTestLineReq.setPpVersionId(reqObject.getVersionId());
        // DIG-8478 SL只搜索出 Active的ppTl
        queryPpTestLineReq.setStatues(Lists.newArrayList(PpStatusEnum.Active.getStatus()));

        List<QueryPpTestLineRsp> ppTestLineList = ppClient.getPpTestLineList(queryPpTestLineReq);
        if (CollectionUtils.isEmpty(ppTestLineList)) {
            return ppTestlines;
        }
        ppTestLineList.forEach(ppTL -> {
            QueryTestLineRsp queryTestLine = new QueryTestLineRsp();
            BeanUtils.copyProperties(ppTL, queryTestLine);
            queryTestLine.setStandardId(ppTL.getCitationId());
            String defaultENPPNotes = queryTestLine.getPpNotes();

            //queryTestLine.setStandardName(StringUtils.isEmpty(ppTL.getCitationSectionName()) ? ppTL.getCitationName() : String.format("%s,%s", ppTL.getCitationName(), ppTL.getCitationSectionName()));
            queryTestLine.setStandardName(ppTL.getCitationFullName());
            if (isChinese && CollectionUtils.isNotEmpty(ppTL.getLanguages())) {
                PpTestLineLangRsp ppTestLineLangRsp = ppTL.getLanguages().stream().filter(lang -> LanguageType.check(lang.getLanguageId(), LanguageType.CHI)).findFirst().orElse(null);
                if (ppTestLineLangRsp != null) {
                    queryTestLine.setEvaluationAlias(ppTestLineLangRsp.getEvaluationAlias());
                    String languagePPNotes = ppTestLineLangRsp.getPpNotes();
                    queryTestLine.setPpNotes(StringUtils.defaultIfBlank(languagePPNotes,defaultENPPNotes));
                }
            }
            ppTestlines.add(queryTestLine);
        });
        return ppTestlines;

    }

    /**
     * 获取所有子PP所对应的RootPP的RootSection信息
     * @param ppVersionId
     * @return key=PpVersionId , value=RootSection
     */
    private Map<Integer, QueryPPSection> getSubPpSectionInfo(Integer ppVersionId,Map<Integer, PPSectionBaseInfoPO> rootSectionMap) {
        Map<Integer, QueryPPSection> subPPToRootSectionMap = Maps.newHashMap();

        // SubPPSection
//        List<QueryPPSection> subPP = ppSectionMapper.getSubPPSectionInfoList(ppVersionId);
        // DIG-6873 对接trimsLocal
        List<GetSubPPSectionInfoRsp> subPPSectionInfoListByPpVersionId = ppClient.getSubPPSectionInfoListByPpVersionId(ppVersionId);
        List<QueryPPSection> subPP = Lists.newArrayList();
        subPPSectionInfoListByPpVersionId.forEach(subPp -> {
            QueryPPSection ppSection = new QueryPPSection();
            BeanUtils.copyProperties(subPp, ppSection);
            subPP.add(ppSection);
        });

        // key=subPP <--> value=RootSection
        if (CollectionUtils.isNotEmpty(subPP)) {
            subPP.forEach(sub -> {
                PPSectionBaseInfoPO ppSectionPO = rootSectionMap.get(sub.getSubPpSectionLevel());
                sub.setRootPpSectionId(ppSectionPO.getSectionId());
                sub.setRootPpSectionLevel(ppSectionPO.getSectionLevel());
                sub.setRootPpSectionName(ppSectionPO.getSectionText());
                // subPP 的所有子PP
                Set<Integer> subPpVersionIds = ppArtifactRelService.getSubPpVersionIds(sub.getSubPpVersionId(), null);
                subPpVersionIds.forEach(subPPId -> subPPToRootSectionMap.put(subPPId, sub));
            });
        }
        return subPPToRootSectionMap;
    }

    private boolean isChinese(GeneralOrderInstanceInfoPO order) {
        ReportLangReq reqReport = new ReportLangReq();
        reqReport.setOrderNo(order.getOrderNo());
        List<LanguageType> languages = orderReportClient.getReportLanguageByOrderNo(reqReport);
        return languages.contains(LanguageType.CHI);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getTestLineList(OrderTestLineReq reqObject){
        return getTestLineListByOrderId(reqObject);
    }

    /**
     * testLine table的数据回显
     * @param reqObject
     * @return
     */
    private CustomResult<List<OrderTestLineRsp>> getTestLineListByOrderId(OrderTestLineReq reqObject){
        CustomResult rspResult = new CustomResult();
        if (StringUtils.isBlank(reqObject.getOrderId())){
            rspResult.setMsg("OrderID is not null.");
            return rspResult;
        }


        AsyncCall asyncCall = new AsyncCall();
        asyncCall.put(AsyncType.Order, ()-> orderMapper.getOrderInfoByOrderId(reqObject.getOrderId()));
        // DIG-9415 切换localTrims
//        asyncCall.put(AsyncType.TestLine, ()-> testLineMapper.getTestLineListByOrderId(reqObject.getOrderId()));
        asyncCall.put(AsyncType.TestLine, ()-> testLineRepository.getTestLineListByOrderId(reqObject.getOrderId()));
        asyncCall.put(AsyncType.CopyReport, ()-> orderSubcontractRelService.getOrderSubcontractRelMatrix(reqObject.getOrderId()));
        asyncCall.put(AsyncType.PpMatrixSampleRel, ()-> testSampleMapper.getMatrixSampleInfoByOrderId(reqObject.getOrderId()));
        asyncCall.put(AsyncType.CitationName, ()->  trimsLocalCitationService.getCitationNameByOrderId(reqObject.getOrderId()));

        GeneralOrderInstanceInfoPO order = null;
        List<OrderTestLineRsp> testLines = null;
        List<OrderSubcontractRelMatrixRsp> subcontractRelInfoPOS = null;
        List<PPMatrixSampleRelInfo> ppMatrixSampleRels = null;
        List<CitationNameRsp> localCitationName = Lists.newArrayList();
        List<AsyncResult> asyncResults = AsyncUtils.awaitResult(asyncCall);
        for (AsyncResult asyncResult: asyncResults) {
            AsyncType asyncType = asyncResult.getTaskKey();
            switch (asyncType){
                case Order:
                    order = asyncResult.getData();
                    break;
                case TestLine:
                    testLines = asyncResult.getData();
                    break;
                case CopyReport:
                    subcontractRelInfoPOS = asyncResult.getData();
                    break;
                case PpMatrixSampleRel:
                    ppMatrixSampleRels = asyncResult.getData();
                    break;
                case CitationName:
                    localCitationName = asyncResult.getData();
                    break;

            }
        }

        /*GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());*/
        if (order == null){
            rspResult.setMsg("Order is not null..");
            return rspResult;
        }

        /*List<OrderTestLineRsp> testLines = testLineMapper.getTestLineListByOrderId(reqObject.getOrderId());*/
        if (CollectionUtils.isEmpty(testLines)){
            rspResult.setData(Lists.newArrayList());
            rspResult.setSuccess(true);
            return rspResult;
        }

        Map<String, CitationNameRsp> fullNameMap = Maps.newHashMap();
        localCitationName.forEach(c->{
            String key = String.format("%s_%s", c.getCitationBaseId(), NumberUtil.toLong(c.getPpArtifactRelId()));
            fullNameMap.put(key,c);
        });

        //search sample&matrix info
        //List<PPMatrixSampleRelInfo> ppMatrixSampleRelInfos=testSampleMapper.getMatrixSampleInfoByOrderId(reqObject.getOrderId());
        List<PPMatrixSampleRelInfo> ppMatrixSampleRelInfos = ppMatrixSampleRels;
        //search matrix info
        TestMatrixInfoExample testMatrixInfoExample=new TestMatrixInfoExample();
        testMatrixInfoExample.createCriteria().andGeneralOrderInstanceIDEqualTo(reqObject.getOrderId());
        List<TestMatrixInfoPO> testMatrixInfoPOs=testMatrixInfoMapper.selectByExample(testMatrixInfoExample);

        //refer data，这里是到matrix级别的判断：取得source order对应的maxtrix,然后跟current order的matrix进行比对
        List<TestMatrixWithTestLineRsp> referSourceMatrixs = Lists.newArrayList();

        ReferDataRelationshipExample referDataRelationshipExample = new ReferDataRelationshipExample();
        referDataRelationshipExample.createCriteria().andCurrentOrderNoEqualTo(order.getOrderNo());
        List<ReferDataRelationshipPO> referDataRelationshipPOS = referDataRelationshipMapper.selectByExample(referDataRelationshipExample);
        if (CollectionUtils.isNotEmpty(referDataRelationshipPOS)){
            List<String> sourceOrderNos = referDataRelationshipPOS.stream().map(x -> x.getSourceOrderNo()).collect(Collectors.toList());
            List<String> sourceSampleIds = referDataRelationshipPOS.stream().map(x -> x.getSourceSampleId()).distinct().collect(Collectors.toList());
            referSourceMatrixs = testMatrixMapper.getReferMatrixByOrderAndSamples(sourceOrderNos, sourceSampleIds);
        }

        // copy report  DIG-6713
        // List<OrderSubcontractRelInfoPO> subcontractRelInfoPOS = orderSubcontractRelService.getSampleAndCopyReportList(reqObject.getOrderId());
        List<OrderSubcontractRelMatrixRsp> finalSubcontractRelInfoPOS = subcontractRelInfoPOS;

        List<TestMatrixWithTestLineRsp> finalReferSourceMatrixs = referSourceMatrixs;

        testLines.forEach(testLine->{
            //find
            List<PPMatrixSampleRelInfo> matchedPPMatrixSampleRelInfos=ppMatrixSampleRelInfos.stream()
                    .filter(ppMatrixSampleRelInfo -> StringUtil.isBlank(testLine.getId(),"").equals(ppMatrixSampleRelInfo.getPpTLRelId()) && StringUtils.isNotBlank(ppMatrixSampleRelInfo.getMatrixId()))
                    .collect(Collectors.toList());
            // build sampleNos
            StringBuilder sampleNos= new StringBuilder();
            List<TestMatrixInfoPO> matchedTestMatrixInfoPOs=testMatrixInfoPOs.stream()
                    .filter(testMatrixInfoPO -> testLine.getTestLineInstanceId().equals(testMatrixInfoPO.getTestLineInstanceID()))
                    .collect(Collectors.toList());
            for (PPMatrixSampleRelInfo ppSampleRelationshipVO : matchedPPMatrixSampleRelInfos) {
                AtomicReference<String> referMarker = new AtomicReference<>(StringUtils.EMPTY);
                if (finalReferSourceMatrixs != null) {
                    finalReferSourceMatrixs.stream().filter(f -> !TestLineStatus.check(f.getTestLineStatus(), TestLineStatus.Cancelled))
                            .filter(p -> StringUtils.equalsIgnoreCase(p.getCurrentSampleId(), ppSampleRelationshipVO.getTestSampleID())
                            && Objects.equals(p.getTestLineId(), ppSampleRelationshipVO.getTestLineID()))
                     .findFirst().ifPresent(c -> {
                        referMarker.set(Constants.REFER_STYLE_HIGHLIGHT);
                    });
                }

                if (StringUtils.isBlank(referMarker.get())) {
                    if (finalSubcontractRelInfoPOS != null) {
                        finalSubcontractRelInfoPOS.stream().filter(p -> StringUtils.equalsIgnoreCase(ppSampleRelationshipVO.getTestSampleID(), p.getCurrentSampleId())
                                && Objects.equals(p.getTestLineId(), ppSampleRelationshipVO.getTestLineID()))
                                .findFirst().ifPresent(c-> {
                            referMarker.set(Constants.REFER_STYLE_HIGHLIGHT);
                        });
                    }
                }

                if (ppSampleRelationshipVO.getIsCancelled() != null
                        && ppSampleRelationshipVO.getIsCancelled() == 0) {
                    sampleNos.append(",<font style='text-decoration:line-through;color:red;").append(referMarker.get()).append("'>").append(ppSampleRelationshipVO.getSampleNo())
                            .append("</font>");
                } else {
                    // matrix cancel
                    if (CollectionUtils.isNotEmpty(matchedTestMatrixInfoPOs)) {
                        String matrixId = ppSampleRelationshipVO.getMatrixId();
                        List<TestMatrixInfoPO> list = matchedTestMatrixInfoPOs.stream().filter(
                                vo2 -> vo2.getID().equals(matrixId) && !vo2.getActiveIndicator())
                                .collect(Collectors.toList());
                        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
                            //业务上，此段代码不会执行
                            sampleNos.append(",<label style='background-color:red;'>").append(ppSampleRelationshipVO.getSampleNo()).append("</label>");
                        } else {
                            sampleNos.append(",<font style='").append(referMarker.get()).append("'>").append(ppSampleRelationshipVO.getSampleNo()).append("</font>");
                        }
                    } else {
                        sampleNos.append(",<font style='").append(referMarker.get()).append("'>").append(ppSampleRelationshipVO.getSampleNo()).append("</font>");
                    }
                }
            }

            String sampleStr = sampleNos.toString();
            if (StringUtils.isNotBlank(sampleStr)) {
                sampleStr = StringUtils.substring(sampleStr,1);
            }
            testLine.setSampleNos(sampleStr);
            //设置fullName
            String key = String.format("%s_%s",testLine.getCitationBaseId(),testLine.getPpArtifactRelId());
            CitationNameRsp citationNameRsp = fullNameMap.get(key);
            if(citationNameRsp != null){
                testLine.setStandardName(citationNameRsp.getCitationFullName());
            }
        });

        // 获取rootPpName
        Map<Long, String> rootPpNameMap = Collections.emptyMap();
        List<Long> rootBaseIds = testLines.stream()
                .filter(pp -> NumberUtil.toLong(pp.getRootPpBaseId()) > 0 && !NumberUtil.equals(pp.getPpId(), pp.getRootPpBaseId()))
                .map(OrderTestLineRsp::getRootPpBaseId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rootBaseIds)){
//            List<PPBaseInfoWithBLOBs> rootPpBaseInfos = ppBaseMapper.getPpBaseInfoByIds(rootBaseIds);
            List<GetPpBaseInfoRsp> ppBaseInfoList = ppClient.getPpBaseInfoList(Sets.newHashSet(rootBaseIds));
            if (CollectionUtils.isNotEmpty(ppBaseInfoList)) {
                rootPpNameMap = ppBaseInfoList.stream().collect(Collectors.toMap(GetPpBaseInfoRsp::getPpBaseId, GetPpBaseInfoRsp::getPpName, (k1, k2) -> k1));
            }
        }

        List<OrderLanguageRelInfoPO> orderLanguageRelInfoPODBs = Lists.newArrayList();
        List<GetTestLineLanguageRsp> testLineLanguageBaseInfoReqPOS = Lists.newArrayList();
        List<GetArtifactCitationLanguageRsp> testLineCitationLanguageInfoPOS = Lists.newArrayList();
        boolean isChinese = isChinese(order);
        if (isChinese) {
            orderLanguageRelInfoPODBs=this.getOrderLanguageRelInfoPOS(reqObject.getOrderId());
            // DIG-9415 切换localTrims
//            testLineCitationLanguageInfoPOS = testLineCitationLangMapper.getTestLineCitationLangList(reqObject.getOrderId());
            testLineLanguageBaseInfoReqPOS = languageClient.getTestLineLanguage(testLines.stream().map(e->e.getTestLineBaseId()).collect(Collectors.toList()));
            testLineCitationLanguageInfoPOS = testLineRepository.getTestLineCitationLangList(reqObject.getOrderId(), LangTypeEnum.Citation.getType());
        }

        for(OrderTestLineRsp testLineInstanceMatrixDTO : testLines){
            if (isChinese) {
                if (CollectionUtils.isNotEmpty(testLineLanguageBaseInfoReqPOS)) {
                    List<GetTestLineLanguageRsp> finalTestLineLanguageBaseInfoReqPOS = testLineLanguageBaseInfoReqPOS;
                    orderLanguageRelInfoPODBs.stream().filter(e->e.getLangType()!=null
                            &&e.getLangType().intValue()==LangTypeEnum.TestLine.getType()
                            &&e.getObjectBaseId().longValue()==testLineInstanceMatrixDTO.getTestLineBaseId().intValue()
                            &&LanguageType.CHI.getLanguageId()==e.getLanguageId().intValue())
                            .findFirst().ifPresent(orderLanguageRelInfoPO-> {
                        finalTestLineLanguageBaseInfoReqPOS.stream().filter(e -> e.getLangId().equals(orderLanguageRelInfoPO.getLangBaseId())).
                                findFirst().ifPresent(objTestLineLanguageInfoPO -> {
                                    if(StringUtils.isNotBlank(objTestLineLanguageInfoPO.getMethodDesc())){
                                        testLineInstanceMatrixDTO.setPpNotes(objTestLineLanguageInfoPO.getMethodDesc());
                                    }
                        });
                    });
                }
                testLineCitationLanguageInfoPOS.stream()
                        .filter(e-> (testLineInstanceMatrixDTO.getPpArtifactRelId() == null || testLineInstanceMatrixDTO.getPpArtifactRelId() == 0L)
                                && NumberUtil.equals(e.getCitationBaseId(), testLineInstanceMatrixDTO.getCitationBaseId()) && StringUtils.isNotBlank(e.getEvaluationAlias()))
                        .findFirst().ifPresent(objTestLineCitationLanguageInfoPO->{
                    testLineInstanceMatrixDTO.setEvaluationAlias(objTestLineCitationLanguageInfoPO.getEvaluationAlias());
                });
                /*设置citationFullName 多语言 保持显示统一，testline table 暂时不显示standard的中文 2021年10月27日 如果后续需要，直接释放注释即可
                String key = String.format("%s_%s",testLineInstanceMatrixDTO.getCitationBaseId(),NumberUtil.toLong(testLineInstanceMatrixDTO.getPpArtifactRelId()));
                CitationNameRsp citationNameRsp = fullNameMap.get(key);
                if(citationNameRsp != null){
                    List<CitationNameLanItem> citationLanguages = citationNameRsp.getLanguages();
                    if(CollectionUtils.isNotEmpty(citationLanguages)){
                        CitationNameLanItem citationNameLanItem = citationLanguages.stream().filter(lang -> LanguageType.check(lang.getLanguageId(), LanguageType.CHI)).findFirst().orElse(null);
                        testLineInstanceMatrixDTO.setStandardName(citationNameLanItem == null ? null : citationNameLanItem.getCitationFullName());
                    }
                }*/
            }
            List<OrderTestLineRsp> existsDtoByTestLineID= testLines.stream().filter(t->{
                return StringUtils.equalsIgnoreCase(t.getTestLineInstanceId(), testLineInstanceMatrixDTO.getTestLineInstanceId())&&StringUtils.isNotEmpty(t.getSampleNos());
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existsDtoByTestLineID)) {
                testLineInstanceMatrixDTO.setShowNcRemark(true);
            }

            //set rootpp name
            String rootPpName = rootPpNameMap.get(testLineInstanceMatrixDTO.getRootPpBaseId());
            if(StringUtils.isNotEmpty(rootPpName)){
                testLineInstanceMatrixDTO.setPpName(rootPpName);
            }
        }
        //由于本来就是到PP的，这里无须取第一个PP的
        //DIG-4691
        if (isChinese) {
            TestLineEvaluationAliasUtil.buildEvaluationAliasMultiLanguage(testLines, LanguageType.CHI);
        }

//        testLineLocalService.buildReportLang(order.getOrderNo(),testLines);

        testLines.sort(Comparator.comparing(OrderTestLineRsp::getTestLineId));
        rspResult.setSuccess(true);
        rspResult.setData(testLines);
        return rspResult;
    }

    private List<PPBaseSectionPO> getRootPpList(List<OrderTestLineRsp> testLines) {
        PPBaseDTO objPPBaseDTO=new PPBaseDTO();
        List<Integer> ppVersionIds =testLines.stream()
                .filter(e->e.getPpVersionId()!=null&&e.getPpVersionId()>0).map(e1->e1.getPpVersionId())
                .distinct()
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ppVersionIds)){
            return Lists.newArrayList();
        }
        objPPBaseDTO.setArtifactVersionIds(ppVersionIds);
        List<PPBaseSectionPO> ppBaseSectionPOS=ppBaseMapper.getRootPPListByPpIds(objPPBaseDTO);
        if(ppBaseSectionPOS == null){
            ppBaseSectionPOS = Lists.newArrayList();
        }
        return ppBaseSectionPOS;
    }

    private List<ArtifactCitationLangInfoPO> getTestLineCitationLanguageInfoPOS(List<Long> citationBaseIds) {
        TestlineCitationLanguageDTO objTestlineCitationLanguageDTO=new TestlineCitationLanguageDTO();
        objTestlineCitationLanguageDTO.setCitationBaseIds(citationBaseIds);
        return testLineBaseMapper.getTlCitationLanguage(objTestlineCitationLanguageDTO);
    }

    private List<TrimsPPTestLineLanguageInfoPO> getPpTestLineLanguageInfoPOS(List<Long> ppArtifactRelIds) {
        TrimsPPTestLineLanguageDTO objTrimsPPTestLineLanguageDTO=new TrimsPPTestLineLanguageDTO();
        objTrimsPPTestLineLanguageDTO.setPpArtifactRelIds(ppArtifactRelIds);
        return testLineBaseMapper.getPpTlRelLanguage(objTrimsPPTestLineLanguageDTO);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult queryTestLineList(QueryTestLineReq reqObject){
        CustomResult rspResult = new CustomResult();
        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());
        if (order == null){
            rspResult.setMsg("Order not found.");
            return rspResult;
        }
        if(reqObject.getCitationVersionId() == null
                && StringUtils.isBlank(reqObject.getTestLineName())
                &&(reqObject.getTestLineId()==null||reqObject.getTestLineId().intValue()==0)
        ){
            rspResult.setMsg("At least one query parameter");
            return rspResult;
        }
        reqObject.setLabId(order.getOrderLaboratoryID());
        String orderLabCode = order.getLabCode();
        //如果是转单，刚好当前操作人所在lab也是转单的lab ，那么就用当前操作人的labId
        CustomResult<UserLabBuInfo> result = this.checkAndGetLabId(order.getOrderNo(), orderLabCode);
        if(!result.isSuccess()){
            return result;
        }
        UserLabBuInfo data = result.getData();
        if(data!=null && NumberUtil.toInt(data.getLabId())>0){
            reqObject.setLabId(Math.toIntExact(data.getLabId()));
        }
        return this.getTestLineListFromTrimsLocal(reqObject, order);
    }

    /**
     *
     * @param reqObject
     * @param order
     * @return
     */
    private CustomResult<List<QueryTestLineListRsp>> getTestLineListFromTrimsLocal(QueryTestLineReq reqObject, GeneralOrderInstanceInfoPO order) {
        List<QueryTestLineListRsp> testLines = Lists.newArrayList();
        com.sgs.trimslocal.facade.model.testline.req.QueryTestLineReq queryTestLineReq = new com.sgs.trimslocal.facade.model.testline.req.QueryTestLineReq();
        queryTestLineReq.setTestLineId(reqObject.getTestLineId());
        queryTestLineReq.setCitationVersionId(reqObject.getCitationVersionId());
        queryTestLineReq.setCitationType(reqObject.getCitationType());
        queryTestLineReq.setEvaluationName(reqObject.getTestLineName());
        queryTestLineReq.setLabId(reqObject.getLabId());
        boolean isChinese = isChinese(order);
        queryTestLineReq.setLanguageIds(isChinese ? Lists.newArrayList(LanguageType.EN.getLanguageId(), LanguageType.CHI.getLanguageId()) : Lists.newArrayList(LanguageType.EN.getLanguageId()));

        CustomResult<List<com.sgs.trimslocal.facade.model.testline.rsp.QueryTestLineRsp>> testlineResult = testLineClient.getTestLineList(queryTestLineReq);

        if (CollectionUtils.isEmpty(testlineResult.getData())) {
            CustomResult customResult = new CustomResult(testlineResult.isSuccess());
            customResult.setMessage(testlineResult.getMessage());
            customResult.setMsg(testlineResult.getMessage());
            customResult.setStatus(testlineResult.getStatus());
            return customResult;
        }
        List<com.sgs.trimslocal.facade.model.testline.rsp.QueryTestLineRsp> testLineList = testlineResult.getData();
        testLineList.forEach(testLine -> {
            QueryTestLineListRsp testLineRsp = new QueryTestLineListRsp();
            testLineRsp.setArtifactBaseId(testLine.getArtifactBaseId());
            testLineRsp.setArtifactType(testLine.getArtifactType());
            testLineRsp.setEvaluationName(testLine.getEvaluationName());

            testLineRsp.setLabSectionName(testLine.getLabSectionName());
            testLineRsp.setPpNotes(testLine.getMethodDesc());
            // 仅处理英文， trims暂时不维护中文
            testLineRsp.setMethodStandardSupplementary(testLine.getMethodStandardSupplementary());
            testLineRsp.setTestLineId(testLine.getTestLineId());
            List<StandardRsp> standards = Lists.newArrayList();
            for (ArtifactCitationRelInfoRsp citationRelInfoRsp : testLine.getCitations()) {
                // 入参中包含 citation时，仅显示指定的Citation
                if (reqObject.getCitationVersionId() != null && !NumberUtil.equals(reqObject.getCitationVersionId(), citationRelInfoRsp.getCitationVersionId())) {
                    continue;
                }
                StandardRsp standardRsp = new StandardRsp();
                standardRsp.setCitationBaseId(NumberUtil.toInt(citationRelInfoRsp.getCitationBaseId()));
                standardRsp.setCitationVersionId(citationRelInfoRsp.getCitationVersionId());
                // 暂时没用
                standardRsp.setiSDefault(false);
                standardRsp.setStandardId(citationRelInfoRsp.getCitationId());
                //standardRsp.setStandardName(StringUtils.isEmpty(citationRelInfoRsp.getCitationSectionName()) ? citationRelInfoRsp.getCitationName() : String.format("%s,%s", citationRelInfoRsp.getCitationName(), citationRelInfoRsp.getCitationSectionName()));
                standardRsp.setStandardName(citationRelInfoRsp.getCitationFullName());
                standardRsp.setStandardSectionId(citationRelInfoRsp.getCitationSectionId());
                standards.add(standardRsp);
            }
            standards.sort(new StandardComparator(true));
            testLineRsp.setStandards(standards);

            if (isChinese && CollectionUtils.isNotEmpty(testLine.getLanguages())) {
                QueryTestLineLanguagesRsp queryTestLineLanguagesRsp = testLine.getLanguages().stream().filter(lang -> NumberUtil.equals(LanguageType.CHI.getLanguageId(), lang.getLanguageId())).findFirst().orElse(null);
                if (queryTestLineLanguagesRsp != null && StringUtils.isNotEmpty(queryTestLineLanguagesRsp.getEvaluationName())) {
                    testLineRsp.setEvaluationName(queryTestLineLanguagesRsp.getEvaluationName());
                }
            }
            testLines.add(testLineRsp);
        });
        return CustomResult.newSuccessInstance(testLines);
    }
    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getCitationInfoList(GetCitationInfoListReq reqObject) {
        CustomResult rspResult = new CustomResult(false);

        if (reqObject == null || StringUtils.isEmpty(reqObject.getCitationName())) {
            rspResult.setMsg("PLEASE CHECK PARAMETER");
            return rspResult.fail("PLEASE CHECK PARAMETER");
        }
        if (reqObject.getCitationName().length() < 3){
            return rspResult.fail("Citation Name 不能小于3个字符.");
        }
        String citationName = reqObject.getCitationName();
        List<CitationInfoRsp> citationInfoList = citationClient.getCitationInfoList(citationName);

        if (CollectionUtils.isEmpty(citationInfoList)) {
            rspResult.setData(Lists.newArrayList());
            rspResult.setSuccess(true);
            return rspResult;
        }
        List<GetCitationInfoRsp> citationInfoListRsps = Lists.newArrayList();

        citationInfoList.forEach(citationInfoRsp -> {
            GetCitationInfoRsp citationInfo = new GetCitationInfoRsp();
            citationInfo.setCitationVersionId(citationInfoRsp.getCitationVersionId());
            citationInfo.setCitationType(citationInfoRsp.getCitationType());
            String citationNameInfo = citationInfoRsp.getCitationName();
            if (CollectionUtils.isNotEmpty(citationInfoRsp.getLanguages())) {
                for (CitationLangRsp citationLang : citationInfoRsp.getLanguages()) {
                    if (StringUtils.isEmpty(citationLang.getCitationName()) || StringUtils.equals(citationNameInfo, citationLang.getCitationName())) {
                        continue;
                    }
                    citationNameInfo = String.format("%s(%s)", citationNameInfo, citationLang.getCitationName());
                }
            }
            citationInfo.setCitationId(citationInfoRsp.getCitationId());
            citationInfo.setCitationName(citationNameInfo);
            citationInfoListRsps.add(citationInfo);
        });

        rspResult.setData(citationInfoListRsps);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @AccessRule(reportStatus = { ReportStatus.Approved, ReportStatus.Cancelled, ReportStatus.Replaced }
            , subContractType = SubContractOperationTypeEnums.DelTestLine
    )
    @BizLog(bizType= BizLogConstant.TEST_HISTORY,operType="Delete TL")
    public CustomResult delTestLine(DelTestLineReq reqObject){
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(false);

        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());
        if (order == null){
            rspResult.setMsg("Order is  null.");
            return rspResult;
        }
        List<String> ppTestLineRelIds = reqObject.getPpTestLineRelIds();

        if (CollectionUtils.isEmpty(ppTestLineRelIds)) {
            rspResult.setMsg("No data to delete!");
            return rspResult;
        }
        // 一旦TL已经建立job就仅允许CANCEL，不能删除
        if (checkHasJob(ppTestLineRelIds)) {
            rspResult.setMsg("Delete Testline failed, one or more job(s) have been created.");
            return rspResult;
        }
        // 查询是否有状态不为Typing的TL
        if (checkTestLineStatus(ppTestLineRelIds)) {
            rspResult.setMsg("Delete Testline failed, since you selected wrong Testline. Only testline with no data can be deleted.");
            return rspResult;
        }
        if (checkDeleteSubContract(ppTestLineRelIds)) {
            rspResult.setMsg("Delete Testline failed, please delete subcontract first.");
            return rspResult;
        }
        //all ppTlRel of order
        List<PPTestLineRelationshipInfoPO> pptlRelsOfOrder=ppTestLineRelMapper.getPPTestLineRelListByOrderId(reqObject.getOrderId());
        if (CollectionUtils.isEmpty(pptlRelsOfOrder)) {
            rspResult.setMsg("PPTestLineRelationship Data is not found!");
            return rspResult;
        }

        //all tls of order
        List<TestLineInstancePO> allTlsOfOrder = testLineRepository.getTestLineByOrderId(reqObject.getOrderId());

        if (CollectionUtils.isEmpty(allTlsOfOrder)) {
            rspResult.setMsg("TestlineInstance Data is not found!");
            return rspResult;
        }

        //待删除的TL ID
        List<String> tlIdsWaitForDelete = Lists.newArrayList();
        List<TestlineBatchDelDTO> testlineBatchDelDTOS = Lists.newArrayList();
        //把用户请求删除的TL按TL ID分组并统计数量
        Map<String,Long> possibleDeleteTestlineInstanceIdMaps = pptlRelsOfOrder.stream().filter(pptlRel->ppTestLineRelIds.contains(pptlRel.getID())).collect(Collectors.groupingBy(PPTestLineRelationshipInfoPO::getTestLineInstanceID,Collectors.counting()));
        //把当前订单的所有tl按TL ID分组并统计数量
        Map<String,Long> orderTestlineInstanceIdMaps = pptlRelsOfOrder.stream().collect(Collectors.groupingBy(PPTestLineRelationshipInfoPO::getTestLineInstanceID,Collectors.counting()));
        orderTestlineInstanceIdMaps.forEach((k,v)->{
            if(NumberUtil.equals(v, possibleDeleteTestlineInstanceIdMaps.get(k))) {
                tlIdsWaitForDelete.add(k);
                TestlineBatchDelDTO objTestlineBatchDelDTO=new TestlineBatchDelDTO();
                objTestlineBatchDelDTO.setId(k);
                objTestlineBatchDelDTO.setOrderID(reqObject.getOrderId());
                testlineBatchDelDTOS.add(objTestlineBatchDelDTO);
            }
        });

        if (CollectionUtils.isNotEmpty(tlIdsWaitForDelete) && !this.ableDeleteTestline(tlIdsWaitForDelete)) {
            rspResult.setMsg("Exist test line has test data");
            return rspResult;
        }


        List<TestLineSampleAssociationInfo> matrixListWaitForDelete = Lists.newArrayList();
        // DIG-4484 matrix从deleteTestLine的SQL中抽出来，单独删除，主要思路如下：
        // 1、根据ppTlRel从pp_sample_rel表中找到对应的sample，以及matrix；
        // 2、根据tl+sample=matrix判断除了自己外是否还有其他记录，如果有则不删除matrix，如果没有则删除matrix；
        List<PpInstanceSampleRelationshipWithTestLineInfo> ppInstanceSampleRelationshipWithTestLineInfos = ppSampleRelMapper.listPpSampleRelationWithTestLineListByOrderId(reqObject.getOrderId());

        //待删除的，即用户勾选的
        List<PpInstanceSampleRelationshipWithTestLineInfo> ppInstanceSampleRelationshipWithTestLineInfosWaitForDelete = ppInstanceSampleRelationshipWithTestLineInfos.stream()
                .filter(p -> ppTestLineRelIds.contains(p.getPpTLRelID())).collect(Collectors.toList());

        //排除待删除的
        List<PpInstanceSampleRelationshipWithTestLineInfo> ppInstanceSampleRelationshipWithTestLineInfosExcludeDelete = ppInstanceSampleRelationshipWithTestLineInfos.stream()
                .filter(p -> ! ppTestLineRelIds.contains(p.getPpTLRelID())).collect(Collectors.toList());

        for (String ppTestLineRelId : ppTestLineRelIds) {
            List<PpInstanceSampleRelationshipWithTestLineInfo> currentPpInstanceSampleRelationshipWithTestLineInfos = ppInstanceSampleRelationshipWithTestLineInfosWaitForDelete.stream().filter(p -> StringUtils.equalsIgnoreCase(ppTestLineRelId, p.getPpTLRelID())).collect(Collectors.toList());
            if (org.springframework.util.CollectionUtils.isEmpty(currentPpInstanceSampleRelationshipWithTestLineInfos)){
                continue;
            }

            //再判断matrix
            for (PpInstanceSampleRelationshipWithTestLineInfo currentPpInstanceSampleRelationshipWithTestLineInfo : currentPpInstanceSampleRelationshipWithTestLineInfos) {
                Optional<PpInstanceSampleRelationshipWithTestLineInfo> ppInstanceSampleRelationshipWithTestLineInfoOptional = ppInstanceSampleRelationshipWithTestLineInfosExcludeDelete.stream()
                        .filter(p -> StringUtils.equalsIgnoreCase(currentPpInstanceSampleRelationshipWithTestLineInfo.getMatrixID(), p.getMatrixID()))
                        .findFirst();

                if (! ppInstanceSampleRelationshipWithTestLineInfoOptional.isPresent()) {
                    TestLineSampleAssociationInfo testLineSampleAssociationInfo = new TestLineSampleAssociationInfo();
                    testLineSampleAssociationInfo.setMatrixID(currentPpInstanceSampleRelationshipWithTestLineInfo.getMatrixID());
                    matrixListWaitForDelete.add(testLineSampleAssociationInfo);
                }
            }
        }

        //检查condition group id是否还有被其他Matrix使用，没有则删除
        List<String> conditionGroupIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(matrixListWaitForDelete)){
            List<String> matrixIds = matrixListWaitForDelete.stream().map(x -> x.getMatrixID()).collect(Collectors.toList());
//            conditionGroupIds = testMatrixMapper.getConditionGroupIdByMatrixIds(matrixIds);

            //先取出待删除的ConditionGroupID
            List<String> conditionGroupIdOfMatrix = ppInstanceSampleRelationshipWithTestLineInfos.stream()
                    .filter(p -> matrixIds.contains(p.getMatrixID()) && StringUtils.isNotBlank(p.getTestConditionGroupId()))
                    .map(m -> m.getTestConditionGroupId()).collect(Collectors.toList());

            //取出剩余matrix的ConditionGroupID
            List<String> conditionGroupIdsOfLeft = ppInstanceSampleRelationshipWithTestLineInfos.stream()
                    .filter(p -> !matrixIds.contains(p.getMatrixID()) && StringUtils.isNotBlank(p.getTestConditionGroupId()))
                    .map(m -> m.getTestConditionGroupId()).collect(Collectors.toList());

            conditionGroupIds = conditionGroupIdOfMatrix.stream().filter(p -> ! conditionGroupIdsOfLeft.contains(p)).distinct().collect(Collectors.toList());
        }

        DelPPTestLineRelInfo delPPTestLineRel = new DelPPTestLineRelInfo();
        delPPTestLineRel.setOrderId(reqObject.getOrderId());
        delPPTestLineRel.setTestLineInstanceIds(tlIdsWaitForDelete);
        delPPTestLineRel.setPpTestLineRelIds(ppTestLineRelIds);

        ReportInfoPO reportInfoPO = reportMapper.getReportByOrderNo(order.getOrderNo());

        UserInfo userInfo = tokenClient.getUser();
        List<String> finalConditionGroupIds = conditionGroupIds;
        boolean isSuccess = transactionTemplate.execute((trans) -> {
            if (CollectionUtils.isNotEmpty(matrixListWaitForDelete)) {
                //testMatrixMapper.deleteMatrixWithForeign(matrixListWaitForDelete);

                testMatrixMapper.deleteMatrixAndForeign(matrixListWaitForDelete);
                if (CollectionUtils.isNotEmpty(finalConditionGroupIds)){
                    testConditionGroupMapper.deleteConditionGroupWithMultipleLangByIds(finalConditionGroupIds);
                }
            }


            if (!envUtil.isEnvType(Local, Dev)){
                ppTestLineRelMapper.delScrapPpTestLineRelTemp(delPPTestLineRel);
            }
            // delete ppTestLineRel list
            ppTestLineRelMapper.deletePPTestLineRelationship(delPPTestLineRel);
            // delete tl with slave table
            //testLineMapper.batchDeleteExt(delPPTestLineRel);
            testLineMapper.batchDeleteAndForeign(delPPTestLineRel);
            //delete language list
            deleteOrderLanguageList(reqObject, allTlsOfOrder, tlIdsWaitForDelete, pptlRelsOfOrder,order.getOrderLaboratoryID());
            //checkPreOrderStatus(order);

            //DIG-4507 All TL Complete后需要更改Order状态为Reporting
            orderStatusValidate(reqObject.getOrderId());

            //DIG-7875
            if(Objects.nonNull(reportInfoPO) && NumberUtil.toInt(reportInfoPO.getRecalculationFlag()) == 1){
                reportInfoPO.setRecalculationFlag(2);
                reportInfoPO.setModifiedBy(userInfo.getRegionAccount());
                reportInfoPO.setModifiedDate(DateUtils.getNow());
                reportMapper.updateReportRecalculationFlag(reportInfoPO);
            }
            return true;
        });

        List<String> testLineInstanceIds = delPPTestLineRel.getTestLineInstanceIds();
        Set<Integer> testLineIDList = allTlsOfOrder.stream().filter(testLine->{
            return  testLineInstanceIds.contains(testLine.getID());
        }).collect(Collectors.toSet()).stream().map(TestLineInstancePO::getTestLineID).collect(Collectors.toSet());
        BizLogHelper.setValue(order.getOrderNo(), StringUtils.join(testLineIDList, ","));


        //删除TL要通知CROSSDB
//        Set<String> realDeletedTestLineInstanceIds = allTlsOfOrder.stream().filter(testLine->{
//            return  testLineInstanceIds.contains(testLine.getID());
//        }).collect(Collectors.toSet()).stream().map(TestLineInstancePO::getID).collect(Collectors.toSet());

        //新的ODC通过binlog方式同步删除的记录，此方法废弃
        //doSendMsgOnDeleteTestLine(realDeletedTestLineInstanceIds);

        rspResult.setSuccess(isSuccess);
        return rspResult;
    }

    /**
     * 物理删除tl后，需要通知crossdb
     * @param testLineInstanceIds
     */
    @Deprecated
    private void doSendMsgOnDeleteTestLine(Set<String> testLineInstanceIds) {
        logger.info("方法doSendMsgOnDeleteTestLine，物理删除tl后，需要通知crossdb");
        if(testLineInstanceIds == null || testLineInstanceIds.size() ==0) {
            logger.warn("方法doSendMsgOnDeleteTestLine，物理删除tl后，但没有testlineinstanceId");
            return;
        }
        PhysicDeleteReq physicDeleteReq = new PhysicDeleteReq();
        physicDeleteReq.setTableName("tb_test_line_instance");
        physicDeleteReq.setKey(StringUtils.join(testLineInstanceIds, ","));
        logger.info("方法doSendMsgOnDeleteTestLine，物理删除tl后，KEY:"+physicDeleteReq.getKey());

        GeneralMessage<PhysicDeleteReq> msg = new GeneralMessage<>();
        msg.setActionType("delete");
        msg.setData(physicDeleteReq);
        msg.setProductLineCode(ProductLineType.SL.getProductLineAbbr());

        kafkaProducer.doSend(KafkaTopicConsts.TOPIC_PHYCAL_DELETE, msg);
    }

    public void checkPreOrderStatus(GeneralOrderInstanceInfoPO order) {
        // 验证是否所有的testLine都被validate
        TestLineInstanceExample objTestLineInstanceExample=new TestLineInstanceExample();
        objTestLineInstanceExample.createCriteria().andGeneralOrderInstanceIDEqualTo(order.getID()).andTestLineStatusNotEqualTo(708);
        List<TestLineInstancePO> testLineLists=testLineInstanceMapper.selectByExample(objTestLineInstanceExample);

        int count = 0;
        int validCount = 0;
        for (TestLineInstancePO testLine : testLineLists) {
            if (TestLineStatus.check(testLine.getTestLineStatus(), TestLineStatus.Completed, TestLineStatus.Cancelled, TestLineStatus.NC) ||
                    TestLineType.check(testLine.getTestLineType(), TestLineType.Pretreatment)) {
                count++;
            }
            if(!TestLineStatus.check(testLine.getTestLineStatus(), TestLineStatus.Cancelled, TestLineStatus.NC)){
                validCount ++;
            }
        }
        // 所有的testline都为Completed或Subcontracted或cancelled状态   且 至少有一条有效的TL
        if (validCount > 0 && count == testLineLists.size()) {
            GeneralOrderInstanceInfoPO updateOrderInfo=new GeneralOrderInstanceInfoPO();
            updateOrderInfo.setID(order.getID());
            updateOrderInfo.setOrderStatus(OrderStatus.Completed.getStatus());
            generalOrderInstanceInfoMapper.updateByPrimaryKeySelective(updateOrderInfo);
            SysStatusReq reqStatus = new SysStatusReq();
            reqStatus.setObjectNo(order.getOrderNo());
            reqStatus.setIgnoreOldStatus(true);
            reqStatus.setNewStatus(PreOrderStatus.Reporting.getStatus());
            String regionAccount = "System";
            UserInfo localUser = UserHelper.getLocalUser();
            if (localUser != null){
                regionAccount = localUser.getRegionAccount();
            }
            reqStatus.setUserName(regionAccount);
            statusClient.insertStatusInfo(reqStatus);
        }
    }

    private void deleteOrderLanguageList(DelTestLineReq reqObject, List<TestLineInstancePO> testLineInstanceDbList, List<String> testlineInstanceIdDeleteds,  List<PPTestLineRelationshipInfoPO> pptlRelDbList,int labId) {
        Set<Long> testTestlineDeletedLangs = Sets.newHashSet();
        Set<Long> testTestlineCitationDeletedLangs = Sets.newHashSet();
        List<TestLineInstancePO> testlineInstanceDeletedList=testLineInstanceDbList.stream().filter(testLineInstancePO -> testlineInstanceIdDeleteds.contains(testLineInstancePO.getID())).collect(Collectors.toList());

        Map<Long, Integer> ppBaseIds = Maps.newHashMap();
        ppMapper.getPpTestLineGroupList(reqObject.getOrderId()).forEach(pp->{
            ppBaseIds.put(pp.getPpBaseId(), pp.getPpCount());
        });
        Map<Long,Long> possibleDeletedTestlineBaseIdMaps=testlineInstanceDeletedList.stream().filter(e->e.getTestLineBaseId()!=null).collect(Collectors.groupingBy(TestLineInstancePO::getTestLineBaseId,Collectors.counting()));
        Map<Long,Long> possibleDeletedTestlineCitBaseIdMaps=testlineInstanceDeletedList.stream().filter(e->e.getCitationBaseId()!=null).collect(Collectors.groupingBy(TestLineInstancePO::getCitationBaseId,Collectors.counting()));

        testLineInstanceDbList.stream().filter(e->e.getTestLineBaseId()!=null).collect(Collectors.groupingBy(TestLineInstancePO::getTestLineBaseId,Collectors.counting())).forEach((k,v)->{if(v.equals(possibleDeletedTestlineBaseIdMaps.get(k))){
            testTestlineDeletedLangs.add(k);
        }});
        testLineInstanceDbList.stream().filter(e->e.getCitationBaseId()!=null).collect(Collectors.groupingBy(TestLineInstancePO::getCitationBaseId,Collectors.counting())).forEach((k,v)->{if(v.equals(possibleDeletedTestlineCitBaseIdMaps.get(k))){
            testTestlineCitationDeletedLangs.add(k);
        }});

        List<Long> trimsPPtlRelIdDeleteds = Lists.newArrayList();
        List<Long> possibleDeletedTrimsPPtlRelIds=pptlRelDbList.stream().filter(pptlRel->pptlRel.getPpArtifactRelId()!=null&&reqObject.getPpTestLineRelIds().
                contains(pptlRel.getID())).map(e->e.getPpArtifactRelId()).collect(Collectors.toList());
        pptlRelDbList.stream().filter(e->e.getPpArtifactRelId()!=null).collect(Collectors.groupingBy(PPTestLineRelationshipInfoPO::getPpArtifactRelId,Collectors.counting())).forEach((k,v)->{if(v==1&&possibleDeletedTrimsPPtlRelIds.contains(k)){
            trimsPPtlRelIdDeleteds.add(k);
        }});

        Set<Long> delLangPpBaseIds = Sets.newHashSet(),
        delLangPpArtifactRelIds = Sets.newHashSet(),
        delLangPpSectionBaseIds = Sets.newHashSet();

        if(CollectionUtils.isNotEmpty(trimsPPtlRelIdDeleteds)){
            PPTestLineDTO objPPTestLineDTO=new PPTestLineDTO();
            objPPTestLineDTO.setLabId(labId);
            objPPTestLineDTO.setPpArtifactRelId(trimsPPtlRelIdDeleteds);
            List<QueryTestLineRsp> ppTestLines = testLineBaseMapper.getPpTestLineList(objPPTestLineDTO);
            ppTestLines.forEach(ppTestLine->{
                long ppBaseId = NumberUtil.toLong(ppTestLine.getPpId());
                if (ppBaseId > 0 && !ppBaseIds.containsKey(ppBaseId)){
                    delLangPpBaseIds.add(ppBaseId);
                }
                long ppArtifactRelId = NumberUtil.toLong(ppTestLine.getArtifactBaseId());
                if (ppArtifactRelId > 0){
                    delLangPpArtifactRelIds.add(ppArtifactRelId);
                }
                long sectionBaseId = NumberUtil.toLong(ppTestLine.getSectionBaseId());
                if (sectionBaseId > 0){
                    delLangPpSectionBaseIds.add(sectionBaseId);
                }
            });
        }
        for (Map.Entry<Long, Integer> entry: ppBaseIds.entrySet()) {
            int ppCount = entry.getValue();
            if (ppCount > 0){
                continue;
            }
            delLangPpBaseIds.add(entry.getKey());
        }

        List<OrderLanguageDTO> orderLangs = Lists.newArrayList();
        OrderLanguageDTO orderLang;
        if(CollectionUtils.isNotEmpty(testTestlineDeletedLangs)){
            orderLang = new OrderLanguageDTO();
            orderLang.setOrderId(reqObject.getOrderId());
            orderLang.setLangType(LangTypeEnum.TestLine.getType());
            orderLang.setObjectBaseIds(testTestlineDeletedLangs);
            orderLangs.add(orderLang);
        }
        if(CollectionUtils.isNotEmpty(testTestlineCitationDeletedLangs)){
            orderLang = new OrderLanguageDTO();
            orderLang.setOrderId(reqObject.getOrderId());
            orderLang.setLangType(LangTypeEnum.Citation.getType());
            orderLang.setObjectBaseIds(testTestlineCitationDeletedLangs);
            orderLangs.add(orderLang);
        }
        if(CollectionUtils.isNotEmpty(delLangPpBaseIds)){
            orderLang = new OrderLanguageDTO();
            orderLang.setOrderId(reqObject.getOrderId());
            orderLang.setLangType(LangTypeEnum.PP.getType());
            orderLang.setObjectBaseIds(delLangPpBaseIds);
            orderLangs.add(orderLang);
        }
       if(CollectionUtils.isNotEmpty(delLangPpArtifactRelIds)){
           orderLang = new OrderLanguageDTO();
           orderLang.setOrderId(reqObject.getOrderId());
           orderLang.setLangType(LangTypeEnum.PpArtifactRel.getType());
           orderLang.setObjectBaseIds(delLangPpArtifactRelIds);
           orderLangs.add(orderLang);
        }
        if(CollectionUtils.isNotEmpty(delLangPpSectionBaseIds)){
            orderLang = new OrderLanguageDTO();
            orderLang.setOrderId(reqObject.getOrderId());
            orderLang.setLangType(LangTypeEnum.Section.getType());
            orderLang.setObjectBaseIds(delLangPpSectionBaseIds);
            orderLangs.add(orderLang);
        }
        if (orderLangs.isEmpty()){
            return;
        }
        orderLanguageRelMapper.batchDelLangRels(orderLangs);
    }

    private boolean ableDeleteTestline(List<String> testLineIDList) {
        /**
         * TODO Kevin
         * 1、return count <= 0;
         */
        Integer count = this.testDataMapper.getExistedTestDataCount(testLineIDList);
        if (count > 0) {
            return false;
        } else {
            return true;
        }
    }

    private boolean checkDeleteSubContract(List<String> relationIDs) {
        /**
         * TODO Kevin
         * 1、return list != null && list.size() > 0;
         */
        List<SubContractTestLineMappingPO> list = subContractExtMapper.queryMappingByPPTestLineRelationShipIDs(relationIDs);
        if (list != null && list.size() > 0) {
            return true;
        }
        return false;
    }

    private boolean checkTestLineStatus(List<String> relationIDs) {
        List<TestLineInstancePO> testLineInstancePOS = testLineMapper.queryTestLineNotTyping(relationIDs);
        for (TestLineInstancePO testline : testLineInstancePOS) {
            if (!TestLineStatus.check(testline.getTestLineStatus(),TestLineStatus.Typing,TestLineStatus.NC,TestLineStatus.DR,TestLineStatus.SubContracted)) {
               return  true;
            } else {
                if ("704".equals(testline.getTestLineStatus().toString()) && testline.getFileID() != null) {
                    return  true;
                }
            }
        }
        return false;
    }

    private boolean checkHasJob(List<String> relationIDs) {
        return testLineMapper.queryJobNumberByTestLineInstanceIds(relationIDs) > 0;
    }

    /**
     *
     * @param reqObj
     * @return
     */
    @AccessRule(reportStatus = { ReportStatus.Approved, ReportStatus.Cancelled, ReportStatus.Replaced },
            subContractType = SubContractOperationTypeEnums.CancelTestLine,
            testLinePendingType = TestLinePendingTypeEnums.TestLineInstanceId)
    @BizLog(bizType=BizLogConstant.TEST_HISTORY,operType="Cancel TL")
//    @TestLinePending(filedName = "testLineInstanceId",type=TestLinePendingTypeEnums.TL_ID)
    public CustomResult cancelTestLine(TestLineCancelReq reqObj){
        CustomResult rspResult =new CustomResult();
        SubContractTestLineMappingExample example = new SubContractTestLineMappingExample();
        example.createCriteria().andTestLineInstanceIDEqualTo(reqObj.getTestLineInstanceId());
        List<SubContractTestLineMappingPO> subContractTestLines = subContractTestLineMappingMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(subContractTestLines)){
            String subContractID = subContractTestLines.get(0).getSubContractID();
            SubContractPO subContract = subContractMapper.selectByPrimaryKey(subContractID);
            if(subContract != null){
                Integer subContractOrder = subContract.getSubContractOrder();
                Integer dataLock = subContract.getDataLock();
                if(subContractOrder != null && subContractOrder.compareTo(1)==0 && SubContractDataLockEnums.check(dataLock,SubContractDataLockEnums.lock)){
                    return rspResult.fail("This testLine has been subcontracted locked,can't cancel");
                }
            }
        }
        TestLineInstancePO testLineInstance = testLineRepository.getBaseTestLineById(reqObj.getTestLineInstanceId());
        if (TestLineStatus.check(testLineInstance.getTestLineStatus(),TestLineStatus.NC, TestLineStatus.Cancelled)){
            rspResult.setSuccess(false);
            rspResult.setMsg("This testLine has been nc/cancelled,can't cancel");
            return  rspResult;
        }
        String username=UserHelper.getLocalUser().getRegionAccount();
        Date modifiedDate=DateUtils.getNow();
        testLineInstance.setTestLineStatus(TestLineStatus.Cancelled.getStatus());
        testLineInstance.setModifiedBy(username);
        testLineInstance.setModifiedDate(modifiedDate);
        testLineInstance.setActiveIndicator(false);

        TestMatrixPO testMatrixPO = new TestMatrixPO();
        testMatrixPO.setTestLineInstanceID(reqObj.getTestLineInstanceId());
        testMatrixPO.setModifiedBy(username);
        testMatrixPO.setModifiedDate(modifiedDate);
        testMatrixPO.setActiveIndicator(false);

        ConclusionInfoPO conclusion=new ConclusionInfoPO();
        conclusion.setTestLineInstanceID(reqObj.getTestLineInstanceId());
        conclusion.setModifiedBy(username);
        conclusion.setModifiedDate(modifiedDate);
        conclusion.setID(reqObj.getTestLineInstanceId());

        String orderID=testLineInstance.getGeneralOrderInstanceID();
        GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO= orderMapper.getOrderInfoByOrderId(orderID);
        String orderNo = generalOrderInstanceInfoPO.getOrderNo();

        ReportInfoExample reportInfoExample=new ReportInfoExample();
        reportInfoExample.createCriteria().andOrderNoEqualTo(orderNo);
        List<ReportInfoPO> reportInfoPOS = reportInfoMapper.selectByExample(reportInfoExample);
        ReportInfoPO reportInfoPO=reportInfoPOS.get(0);

        // DIG-8827 cancel testLine 时 Job 的处理
        JobUpdateDTO jobUpdateDTO = this.buildJobUpdate(orderID, Lists.newArrayList(reqObj.getTestLineInstanceId()));
        List<String> jobNoList = jobUpdateDTO.getJobNoList();
        Boolean jobCancelFlag = jobUpdateDTO.getJobCancelFlag();

        /*JobTestLineRelationshipInfoExample jobTestLineRelationshipInfoExample=new JobTestLineRelationshipInfoExample();
        jobTestLineRelationshipInfoExample.createCriteria().andTestLineInstanceIDEqualTo(reqObj.getTestLineInstanceId());
        List<JobTestLineRelationshipInfoPO> jobTestLineRelationshipInfoPOS=jobTestLineRelationshipInfoMapper.selectByExample(jobTestLineRelationshipInfoExample);

        List<String>jobIDList=jobTestLineRelationshipInfoPOS.stream().map(JobTestLineRelationshipInfoPO::getJobID).collect(Collectors.toList());


        List<JobInfoPO> jobInfoPOS=Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(jobIDList)){
            //如果当前testLine对应的job有其他的testLine,不取消
            JobTestLineRelationshipInfoExample jobTestLineRelationshipInfoExampleExist=new JobTestLineRelationshipInfoExample();
            jobTestLineRelationshipInfoExampleExist.createCriteria().andTestLineInstanceIDNotEqualTo(reqObj.getTestLineInstanceId()).andJobIDIn(jobIDList);
            List<JobTestLineRelationshipInfoPO> jobTestLineRelationshipInfoPOSExist=jobTestLineRelationshipInfoMapper.selectByExample(jobTestLineRelationshipInfoExampleExist);
            if (CollectionUtils.isEmpty(jobTestLineRelationshipInfoPOSExist)){
                JobInfoExample jobInfoExample=new JobInfoExample();
                jobInfoExample.createCriteria().andIDIn(jobIDList);
                jobInfoPOS= jobInfoMapper.selectByExample(jobInfoExample);
            }

        }
        List<String>jobNoList=jobInfoPOS.stream().map(JobInfoPO::getJobNo).collect(Collectors.toList());*/

        // DIG-7128 判断当前TestLine 是否是所处的分包单的最后一个 若是，则不能Cancel
        SubContractPO subContract = subContractExtMapper.querySubContractByTestLineInstanceId(testLineInstance.getID());
        if (subContract != null && !SubContractStatusEnum.check(subContract.getStatus(), SubContractStatusEnum.Cancelled)) {
            List<TestLineInstancePO> subcontractTestLines = testLineMapper.getSubcontractTestLineInfo(subContract.getID());
            List<TestLineInstancePO> subTestLines = subcontractTestLines.stream().filter(tl -> tl.getActiveIndicator()
                    && !TestLineStatus.check(tl.getTestLineStatus(), TestLineStatus.Cancelled)
                    && !StringUtils.equals(tl.getID(), testLineInstance.getID())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subTestLines)) {
                return rspResult.fail("Fail to cancel! Because it was the only one TL of ongoing Subcontract!");
            }
        }

        List<String> finalJobNoList = jobNoList;
        Boolean finalJobCancelFlag = jobCancelFlag;
        boolean isSuccess = transactionTemplate.execute((trans) -> {
            testLineStatusService.updateTestLineStatus(TestLineModuleType.CancelTestLine, testLineInstance);
            testMatrixMapper.cancelMatrixByTestLineInstanceId(testMatrixPO);
            subContractExtMapper.deleteByTestLineInstanceId(testLineInstance.getID());
            int count=conclusionMapper.cancelConclusionByTestLineInstanceId(conclusion);
            if (count>0){
                if(reportInfoPO.getRecalculationFlag()==1){
                    reportInfoPO.setRecalculationFlag(2);
                    reportInfoPO.setModifiedBy(username);
                    reportInfoPO.setModifiedDate(modifiedDate);
                    reportMapper.updateRecalculationFlagByReportId(reportInfoPO);
                }
            }
            JobTestLineRelationshipInfoExample exampleDelete=new JobTestLineRelationshipInfoExample();
            exampleDelete.createCriteria().andTestLineInstanceIDEqualTo(reqObj.getTestLineInstanceId());
            jobTestLineRelationshipInfoMapper.deleteByExample(exampleDelete);
            if (CollectionUtils.isNotEmpty(finalJobNoList)){
                List<JobInfoReq> jobInfoReqs=Lists.newArrayList();
                List<JobInfo> jobInfoPOSUpdate=Lists.newArrayList();
                for(String jobNo: finalJobNoList){
                    JobInfoReq jobInfoReq=new JobInfoReq();
                    jobInfoReq.setJobNo(jobNo);
                    jobInfoReq.setJobStatus(finalJobCancelFlag ? JobStatus.Cancelled.getStatus() : JobStatus.Validated.getStatus());
                    jobInfoReq.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
                    jobInfoReqs.add(jobInfoReq);

                    JobInfo jobInfoPO=new JobInfo();
                    jobInfoPO.setJobNo(jobNo);
                    jobInfoPO.setModifiedDate(modifiedDate);
                    jobInfoPO.setModifiedBy(username);
                    jobInfoPO.setJobStatus(finalJobCancelFlag ? JobStatus.Cancelled.getStatus() : JobStatus.Validated.getStatus());
                    jobInfoPOSUpdate.add(jobInfoPO);
                }
                jobExtMapper.updateJobsBatch(jobInfoPOSUpdate);

                JobReq reqJob = new JobReq();
                reqJob.setJobs(jobInfoReqs);
                jobClient.updateJobBatch(reqJob);
            }
            orderStatusValidate(orderID);
            //删除pretreatment 绑定表数据
            relatePretreatmentTestLineExtMapper.batchDeleteByPretreatmentTestLineId(Lists.newArrayList(testLineInstance.getID()));
            return  true;
        });
        BizLogHelper.setValue(generalOrderInstanceInfoPO.getOrderNo(), testLineInstance.getTestLineID());
        rspResult.setSuccess(isSuccess);
        return rspResult;
    }

    /**
     *
     * @param orderId
     * @param testLineInstanceIds
     * @return
     */
    private JobUpdateDTO buildJobUpdate(String orderId, List<String> testLineInstanceIds) {
        JobUpdateDTO jobUpdateDTO = new JobUpdateDTO();

        JobTestLineRelationshipInfoExample jobTestLineRelationshipInfoExample=new JobTestLineRelationshipInfoExample();
        jobTestLineRelationshipInfoExample.createCriteria().andTestLineInstanceIDIn(testLineInstanceIds);
        List<JobTestLineRelationshipInfoPO> jobTestLineRelationshipInfoPOS = jobTestLineRelationshipInfoMapper.selectByExample(jobTestLineRelationshipInfoExample);

        List<String> jobIDList = jobTestLineRelationshipInfoPOS.stream().map(JobTestLineRelationshipInfoPO::getJobID).collect(Collectors.toList());

        List<String> jobNoList = Lists.newArrayList();
        Boolean jobCancelFlag = Boolean.TRUE;
        if (CollectionUtils.isNotEmpty(jobIDList)){
            //如果当前testLine对应的job有其他的testLine,不取消
            JobTestLineRelationshipInfoExample jobTestLineRelationshipInfoExampleExist=new JobTestLineRelationshipInfoExample();
            jobTestLineRelationshipInfoExampleExist.createCriteria().andTestLineInstanceIDNotIn(testLineInstanceIds).andJobIDIn(jobIDList);
            List<JobTestLineRelationshipInfoPO> jobTestLineRelationshipInfoPOSExist = jobTestLineRelationshipInfoMapper.selectByExample(jobTestLineRelationshipInfoExampleExist);
            // 判断 当前testLine 关联的Job 下是否还有别的testLine 来判断job 是否是删除
            jobCancelFlag = CollectionUtils.isEmpty(jobTestLineRelationshipInfoPOSExist);
            if (CollectionUtils.isEmpty(jobTestLineRelationshipInfoPOSExist)){
                JobInfoExample jobInfoExample=new JobInfoExample();
                jobInfoExample.createCriteria().andIDIn(jobIDList);
                List<JobInfoPO> jobInfoPOS= jobInfoMapper.selectByExample(jobInfoExample);
                jobNoList = jobInfoPOS.stream().map(JobInfoPO::getJobNo).collect(Collectors.toList());
            } else {
                List<String> jobTestLineIds = jobTestLineRelationshipInfoPOSExist.stream().map(JobTestLineRelationshipInfoPO::getTestLineInstanceID).collect(Collectors.toList());
                CustomResult<List<JobInfo>> jobInfoRsp = testLineValidateService.checkTlAllValidate(orderId, testLineInstanceIds, jobTestLineIds);
                if(jobInfoRsp.isSuccess()){
                    if(CollectionUtils.isNotEmpty(jobInfoRsp.getData())){
                        jobNoList = jobInfoRsp.getData().stream().map(JobInfo::getJobNo).collect(Collectors.toList());
                    }
                }
            }
        }
        jobUpdateDTO.setJobNoList(jobNoList);
        jobUpdateDTO.setJobCancelFlag(jobCancelFlag);
        return jobUpdateDTO;
    }


    /**
     *
     * @param reqObj
     * @return
     */
    public CustomResult updateModified(UpdateModifiedTestLineListReq reqObj){
        CustomResult rspResult = new CustomResult();
        if(StringUtils.isBlank(reqObj.getTestLineInstanceId()) || reqObj.getModified()==null){
            rspResult.setMsg("TestLineInstanceId is not null");
            return rspResult;
        }
        TestLineInstancePO testLineInstancePO = new TestLineInstancePO();

        testLineInstancePO.setID(reqObj.getTestLineInstanceId());
        testLineInstancePO.setModified(reqObj.getModified());
        testLineInstancePO.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
        testLineInstancePO.setModifiedDate(DateUtils.getNow());

        // 判断是否正常添加，否则回滚
        rspResult.setSuccess(testLineMapper.updateModifiedById(testLineInstancePO) > 0);
        return rspResult;
    }
    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getTestLineRemark(TestLineInstanceIdReq reqObject){
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(true);
        rspResult.setData(testLineMapper.getTestLineRemark(reqObject.getTestLineInstanceId()));
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
//    @TestLinePending(filedName = "testLineInstanceId",type=TestLinePendingTypeEnums.TL_ID)
    @AccessRule(testLinePendingType = TestLinePendingTypeEnums.TestLineInstanceId)
    public CustomResult updateRemark(TestLineRemarkReq reqObject){
        CustomResult rspResult = new CustomResult();
        TestLineInstancePO testLineInstancePO = testLineMapper.getTestLineInstanceById(reqObject.getTestLineInstanceId());
        if (TestLineStatus.check(testLineInstancePO.getTestLineStatus(),TestLineStatus.NC)){
            rspResult=checkAssignedSampleByTestLine(testLineInstancePO.getID());
            if (!rspResult.isSuccess()){
                return rspResult;
            }
        }
        TestLineInstancePO testLineInstancePOUpDate = new TestLineInstancePO();
        testLineInstancePOUpDate.setID(reqObject.getTestLineInstanceId());
        testLineInstancePOUpDate.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());

        testLineInstancePOUpDate.setModifiedDate(DateUtils.getNow());
        testLineInstancePOUpDate.setOrdertestLineRemark(reqObject.getTestLineRemark());
        testLineInstancePOUpDate.setTestLineStatus(testLineInstancePO.getTestLineStatus());
        testLineStatusService.updateTestLineStatus(TestLineModuleType.UpdateRemarkTestLine, testLineInstancePOUpDate);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getTestStandard(TestStandardReq reqObject){
        CustomResult rspResult = new CustomResult();
        if (reqObject==null|| reqObject.getTestLineInstanceId()==null){
            rspResult.setSuccess(false);
            rspResult.setMsg("Para is null!");
            return  rspResult;
        }
        List<String >testLineIDList=Lists.newArrayList();
        testLineIDList.add(reqObject.getTestLineInstanceId());
        List<TestLineInstancePO> testLineInstancePOS =testLineMapper.getBaseTestLineByIds(testLineIDList);
        if (CollectionUtils.isEmpty(testLineInstancePOS)){
            rspResult.setSuccess(false);
            rspResult.setMsg(" Get testLine error!");
            return  rspResult;
        }

        TestLineInstancePO testLineInstancePO = testLineInstancePOS.get(0);
        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfoByOrderId(testLineInstancePO.getGeneralOrderInstanceID());

        //替换buildQueryTestStandardRsp
        QueryTestStandardRsp queryTestStandardRsp = buildQueryTestStandardRspByTrimsLocal(testLineInstancePO);

        //TODO v-tl别名修改
        testLineLocalService.build(orderInfo.getOrderNo(),Lists.newArrayList(queryTestStandardRsp));

        rspResult.setData(queryTestStandardRsp);
        rspResult.setSuccess(true);
        return rspResult;
    }

    @Deprecated
    public QueryTestStandardRsp buildQueryTestStandardRsp(TestLineInstancePO testLineInstancePO) {
        ArtifactCitationRelInfoExample testLineCitationRelInfoExample=new ArtifactCitationRelInfoExample();
        testLineCitationRelInfoExample.createCriteria()
                .andArtifactVersionIdEqualTo(testLineInstancePO.getTestLineVersionID())
                .andArtifactTypeEqualTo(0)
                .andCitationStatusEqualTo(1)
                .andCitationTypeBetween(CitationType.Regulation.getType(), CitationType.Standard.getType());
        List<ArtifactCitationRelInfoPO> testLineCitationRelInfoPOS=testLineCitationRelInfoMapper.selectByExample(testLineCitationRelInfoExample);

        List<ArtifactCitationLangInfoPO> testLineCitationLanguageInfoPOS= Lists.newArrayList();
        List<Long>citationBaseIDList=testLineCitationRelInfoPOS.stream().map(ArtifactCitationRelInfoPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(citationBaseIDList)){
            ArtifactCitationLangInfoExample testLineCitationLanguageInfoExample=new ArtifactCitationLangInfoExample();
            testLineCitationLanguageInfoExample.createCriteria().andCitationBaseIdIn(citationBaseIDList).andLangStatusEqualTo(1);
            testLineCitationLanguageInfoPOS=testLineCitationLanguageInfoMapper.selectByExample(testLineCitationLanguageInfoExample);
        }

        int versionID = NumberUtil.toInt(testLineInstancePO.getStandardVersionID());
        int sectionID = NumberUtil.toInt(testLineInstancePO.getStandardSectionID());

        QueryTestStandardRsp queryTestStandardRsp=new QueryTestStandardRsp();
        queryTestStandardRsp.setTestLineInstanceId(testLineInstancePO.getID());
        queryTestStandardRsp.setEvaluationAlias(testLineInstancePO.getEvaluationAlias());
        queryTestStandardRsp.setTestLineId(testLineInstancePO.getTestLineID());
        queryTestStandardRsp.setStandardName(testLineInstancePO.getStandardName());
        List<TestStandardExtRsp> standardExtRspList=Lists.newArrayList();
        for(ArtifactCitationRelInfoPO testLineCitationRelInfoPO:testLineCitationRelInfoPOS){

            int citationId = NumberUtil.toInt(testLineCitationRelInfoPO.getCitationVersionId(),0);
            int citationSectionId =  NumberUtil.toInt(testLineCitationRelInfoPO.getCitationSectionId(),0);
            TestStandardExtRsp objStandardExtRsp=new TestStandardExtRsp();
            objStandardExtRsp.setSelected(versionID == citationId && sectionID == citationSectionId);
            objStandardExtRsp.setCitationBaseId(testLineCitationRelInfoPO.getId());
            objStandardExtRsp.setStandardName(testLineCitationRelInfoPO.getCitationName());
            if(StringUtils.isNotEmpty(testLineCitationRelInfoPO.getCitationSectionName())) {
                objStandardExtRsp.setStandardName(testLineCitationRelInfoPO.getCitationName()+","+testLineCitationRelInfoPO.getCitationSectionName());
            }
            objStandardExtRsp.setStandardSectionId(testLineCitationRelInfoPO.getCitationSectionId());
            objStandardExtRsp.setStandardVersionId(testLineCitationRelInfoPO.getCitationVersionId());
            List<ArtifactCitationLangInfoPO>testLineCitationLanguageInfoPOList= testLineCitationLanguageInfoPOS.stream().filter(testLineCitationLanguageInfoPO->testLineCitationRelInfoPO.getId().compareTo(testLineCitationLanguageInfoPO.getCitationBaseId())==0).collect(Collectors.toList());
            List<TestStandardOtherLanguageItemRsp> testStandardOtherLanguageItemRspList=Lists.newArrayList();
            testLineCitationLanguageInfoPOList.forEach(testLineCitationLanguageInfoPO->{
                TestStandardOtherLanguageItemRsp objTTestStandardOtherLanguageItemRsp=new TestStandardOtherLanguageItemRsp();
                objTTestStandardOtherLanguageItemRsp.setLanguageId(testLineCitationLanguageInfoPO.getLanguageId());
                objTTestStandardOtherLanguageItemRsp.setMultiEvaluationAlias(testLineCitationLanguageInfoPO.getEvaluationAlias());
                objTTestStandardOtherLanguageItemRsp.setMultiStandardName(testLineCitationLanguageInfoPO.getCitationName());
                testStandardOtherLanguageItemRspList.add(objTTestStandardOtherLanguageItemRsp);
            });
            standardExtRspList.add(objStandardExtRsp);
        }

        //这里增加一个语言的判断
        //CitationLocalizeHelper.build(standardExtRspList);
        queryTestStandardRsp.setStandardList(standardExtRspList);
        return queryTestStandardRsp;
    }

    /**
     * 构造StandardList
     * @param testLine
     * @return
     */
    public QueryTestStandardRsp buildQueryTestStandardRspByTrimsLocal(TestLineInstancePO testLine) {
        List<CitationListRsp> citations = citationClient.getCitationListByTestLineVersionId(testLine.getTestLineVersionID(),testLine.getTestLineBaseId());

        int standardVersionId = NumberUtil.toInt(testLine.getStandardVersionID());
        int standardSectionId = NumberUtil.toInt(testLine.getStandardSectionID());

        QueryTestStandardRsp queryTestStandardRsp=new QueryTestStandardRsp();
        queryTestStandardRsp.setTestLineInstanceId(testLine.getID());
        queryTestStandardRsp.setEvaluationAlias(testLine.getEvaluationAlias());
        queryTestStandardRsp.setTestLineId(testLine.getTestLineID());
        queryTestStandardRsp.setStandardName(testLine.getStandardName());
        List<TestStandardExtRsp> testStandards = Lists.newArrayList();

        for(CitationListRsp citation: citations){
            int citationVersionId = NumberUtil.toInt(citation.getCitationVersionId());
            int citationSectionId =  NumberUtil.toInt(citation.getCitationSectionId());
            boolean isSelected = standardVersionId == citationVersionId && standardSectionId == citationSectionId;
            TestStandardExtRsp testStandard = new TestStandardExtRsp();
            testStandard.setCitationBaseId(citation.getCitationBaseId());
            testStandard.setStandardName(citation.getCitationName());
            if(StringUtils.isNotEmpty(citation.getCitationSectionName())) {
                testStandard.setStandardName(citation.getCitationName()+","+citation.getCitationSectionName());
            }
            testStandard.setStandardSectionId(citationSectionId);
            testStandard.setStandardVersionId(citationVersionId);
            testStandard.setSelected(isSelected);

            List<TestStandardOtherLanguageItemRsp> languages = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(citation.getOtherLanguageItems())) {
                citation.getOtherLanguageItems().forEach(testLineCitationLanguageInfoPO -> {
                    TestStandardOtherLanguageItemRsp language = new TestStandardOtherLanguageItemRsp();
                    language.setLanguageId(testLineCitationLanguageInfoPO.getLanguageId());
                    language.setMultiEvaluationAlias(testLineCitationLanguageInfoPO.getEvaluationAlias());
                    language.setMultiStandardName(testLineCitationLanguageInfoPO.getCitationName());
                    languages.add(language);
                });
            }
            testStandard.setOtherLanguageItems(languages);
            testStandards.add(testStandard);
        }

        //这里增加一个语言的判断
        //CitationLocalizeHelper.build(standardExtRspList);
        queryTestStandardRsp.setStandardList(testStandards);
        return queryTestStandardRsp;
    }

    /**
     *
     * @param reqObjects
     * @return
     */
    @AccessRule(reportStatus = { ReportStatus.Approved, ReportStatus.Cancelled },
            subContractType = SubContractOperationTypeEnums.UpdateStandard,
    testLinePendingType = TestLinePendingTypeEnums.SaveStandTestLineId)
    @BindOldMethod(value = OldTestLineClient.class, routeId = 18, oldRouteId = 5, methodRouteId = 2,oldMethodRouteId = 0,isWrite = true)
    @BizLog(bizType=BizLogConstant.TEST_HISTORY,operType="Change Update Standard")
    public CustomResult updateStandard(SaveTestStandardReq reqObjects){
        CustomResult rspResult = new CustomResult();
        if (reqObjects == null || CollectionUtils.isEmpty(reqObjects.getStandards())) {
            return rspResult.fail("Standards is null!");
        }

        List<StandardReq> standards=reqObjects.getStandards();

        List<String> testLineInstanceIds = Lists.newArrayList();
        Boolean noStandardFlag=false;
        for (StandardReq saveTestStandardReq:standards){
            testLineInstanceIds.add(saveTestStandardReq.getTestLineInstanceId());
            if (saveTestStandardReq.getStandardVersionId()==null||saveTestStandardReq.getStandardVersionId() <= 0){
                noStandardFlag=true;
                break;
            }
        }
        if (noStandardFlag){
            return rspResult.fail("Invalidate standard!");
        }

        Set<Long> citationBaseIds = standards.stream().map(st -> st.getCitationBaseId()).distinct().collect(Collectors.toSet());

//        ArtifactCitationRelInfoExample example = new ArtifactCitationRelInfoExample();
//        example.createCriteria().andIdIn(citationBaseIds);
//        List<ArtifactCitationRelInfoPO> artifactCitationRelInfoPOS = testLineCitationRelInfoMapper.selectByExample(example);
        //接trimslocal接口，待稳定后上面三行代码可移除
        List<GetCitationBaseInfoRsp> artifactCitationRelInfoPOS = citationClient.getCitationBaseInfo(citationBaseIds,null, LanguageType.EN);

        Map<Long, GetCitationBaseInfoRsp> citationBaseIdPOMap = artifactCitationRelInfoPOS.stream().collect(Collectors.toMap(GetCitationBaseInfoRsp::getCitationBaseId, Function.identity(), (k1, k2) -> k1));

        Map<String, StandardReq> testLineStandardReqMap = standards.stream().collect(Collectors.toMap(StandardReq::getTestLineInstanceId,Function.identity(),(k1, k2) -> k2));

        Map<String, List<TestAnalyteLanguage>> analyteLanguageMaps=Maps.newHashMap();
        List<TestLineInstancePO> testLineInstancePOS=testLineMapper.getBaseTestLineByIds(testLineInstanceIds);
        String orderID = testLineInstancePOS.get(0).getGeneralOrderInstanceID();
        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfoByOrderId(orderID);
        //DIG-7831
        SaveTestLineListReq lineListReq = new SaveTestLineListReq();
        List<SaveTestLineReq> lineReqs = testLineInstanceExtMapper.queryPpTestLineByOrderNo(orderInfo.getOrderNo(),Sets.newHashSet(testLineInstanceIds),citationBaseIds);
        Map<String,StandardReq> standardMap = standards.stream().collect(Collectors.toMap(StandardReq::getTestLineInstanceId,Function.identity(),(o1,o2)->o1));
        lineReqs.forEach(x->{
            if(Objects.nonNull(standardMap.get(x.getTestLineInstanceId())) && standardMap.get(x.getTestLineInstanceId()).getCitationBaseId() != null ){
                x.setCitationBaseId(standardMap.get(x.getTestLineInstanceId()).getCitationBaseId());
            }
        });
        lineListReq.setTestLines(lineReqs);
        checkTestLineMappingExists(lineListReq,rspResult,orderInfo);
        if(!rspResult.isSuccess()){
            return rspResult;
        }

        logger.info("[OrderNo-UpdateStandard]OrderNo:{}, updateStandard:{}, ", orderInfo.getOrderNo(), JSONObject.toJSONString(standards));

        List<TestLineInstancePO> testLineInstancePOsUpdate = Lists.newArrayList();
        for (TestLineInstancePO testLineInstancePO : testLineInstancePOS) {
            StandardReq standard = testLineStandardReqMap.get(testLineInstancePO.getID());
            Long citationBaseId = standard.getCitationBaseId();
            GetCitationBaseInfoRsp artifactCitationRelInfoPO = citationBaseIdPOMap.get(citationBaseId);
            Integer citationId = artifactCitationRelInfoPO.getCitationId();
            Integer citationVersionId = artifactCitationRelInfoPO.getCitationVersionId();

            if (NumberUtil.equals(testLineInstancePO.getStandardVersionID(), standard.getStandardVersionId())
                    && NumberUtil.equals(testLineInstancePO.getStandardSectionID(), standard.getStandardSectionId())) {
                testLineStandardReqMap.remove(testLineInstancePO.getID());
                testLineInstanceIds.remove(testLineInstancePO.getID());
                continue;
            }
            testLineInstancePO.setCitationBaseId(standard.getCitationBaseId());
            testLineInstancePO.setCitationId(citationId);
            testLineInstancePO.setCitationVersionId(citationVersionId);
            testLineInstancePO.setStandardVersionID(standard.getStandardVersionId());
            testLineInstancePOsUpdate.add(testLineInstancePO);
        }

        if (CollectionUtils.isEmpty(testLineInstanceIds)){
            rspResult.setSuccess(true);
            return  rspResult;
        }

        //除了自己外，仍存在相同的TL，则不允许更新。判断条件：testLineStandard.getTestLineId(), citation.getCitationId(), citation.getCitationVersionId(), citation.getCitationSectionId())
        //{"standards":[{"testLineInstanceId":"65a14bf4-375d-4a87-9d1b-6b54bea5ba4a","standardVersionId":3852,"citationBaseId":6808192,"standardSectionId":0,"testLineCitationId":""}]}
        List<MergeTestLineRsp> mergeTestLineRsps = testLineInstanceExtMapper.selectTestLineMergeList(orderID);
        AtomicReference<Boolean> exists = new AtomicReference<>(false);
        AtomicReference<String> fmt = new AtomicReference<>("");
        for (TestLineInstancePO testLineInstancePO : testLineInstancePOsUpdate) {
            mergeTestLineRsps.stream().filter(p -> {
                return
                        (!StringUtils.equalsIgnoreCase(p.getTestLineInstanceId(), testLineInstancePO.getID()))
                                && NumberUtil.equals(testLineInstancePO.getTestLineID(), p.getTestLineId())
                                && NumberUtil.equals(testLineInstancePO.getCitationBaseId(), p.getCitationBaseId());
            }).findFirst().ifPresent(c -> {
                exists.set(true);
                GetCitationBaseInfoRsp artifactCitationRelInfoPO = citationBaseIdPOMap.get(c.getCitationBaseId());
                fmt.set(String.format("TL ID: %s Standard: %s is exists and can not be UPDATE.", c.getTestLineId(), (artifactCitationRelInfoPO == null ? "" : artifactCitationRelInfoPO.getCitationName())));
            });
            if (exists.get()) {
                break;
            }
        }
        if(exists.get()){
            return rspResult.fail(fmt.get());
        }

        List<PPTestLineInfo> ppTestLineInfos =ppRepository.getPpInstanceByTestLineList(testLineInstanceIds);
        //获取需要添加analyte的testLine
        List<PPTestLineInfo> ppSingleTestLineInfos=ppTestLineInfos.stream().filter(ppTestLineInfo -> {
            return ppTestLineInfo.getArtifactId()==null;
        }).collect(Collectors.toList());
        //获取pp testLine,且需要添加analyte的testLine,根据pp分组
        Map<Long,List<PPTestLineInfo>> ppTestLineInfosMap=ppTestLineInfos.stream().filter(ppTestLineInfo -> {
            return ppTestLineInfo.getArtifactId()!=null;
        }).collect(Collectors.toList()).stream().collect(Collectors.groupingBy(PPTestLineInfo::getArtifactId));
        if (MapUtils.isEmpty(ppTestLineInfosMap)){
            ppTestLineInfosMap=Maps.newHashMap();
        }
        //将单独添加的testLine放在一组
        if (CollectionUtils.isNotEmpty(ppSingleTestLineInfos)){
            ppTestLineInfosMap.put(null,ppSingleTestLineInfos);
        }
        Map<String,TestLineInstancePO>testLineInfoMap=testLineInstancePOsUpdate.stream().collect(Collectors.toMap(TestLineInstancePO::getID,lineInstancePO->lineInstancePO));
        ppTestLineInfos.forEach(ppTestLineInfo -> {
            TestLineInstancePO testLine = testLineInfoMap.get(ppTestLineInfo.getTestLineInstanceID());
            ppTestLineInfo.setTestLineVersionID(testLine.getTestLineVersionID());
            ppTestLineInfo.setStandardVersionID(testLine.getStandardVersionID());
            if (StringUtils.isNotBlank(ppTestLineInfo.getExtFields()) && TestLineType.check(ppTestLineInfo.getTestLineType(), TestLineType.CSPP_TESTLINE_TRIMS_DATA)) {
                PpTestLineExtFieldsDTO ppTestLineExtFieldsDTO = ppTestLineAssembler.convertPpTestLineExtDTO(ppTestLineInfo.getExtFields());
                if (ppTestLineExtFieldsDTO != null) {
                    ppTestLineInfo.setArtifactId(ppTestLineExtFieldsDTO.getTestLineAid());
                    ppTestLineInfo.setTestLineVersionID(ppTestLineExtFieldsDTO.getTestLineVersionId());
                }
            }
        });
//        List<AnalyteInfoPO> analytePOList = testMatrixService.getTestAnalyteInfoList(ppTestLineInfosMap, orderID,analyteLanguageMaps,false);
        //获取所有analyte
        List<AnalyteInfoPO> dbAnalytes = analyteMapper.getAnalyteByTestLineInstanceIds(testLineInstanceIds);
        Set<Integer> analyteIds = dbAnalytes.stream().map(dba -> dba.getAnalyteID()).collect(Collectors.toSet());
        Set<Long> unAnalyteAid = Sets.newHashSet();
        List<AnalyteInfoPO> analytePOList = testMatrixService.getTrimsLocalAnalyteInfoList(ppTestLineInfosMap, orderID,analyteLanguageMaps,false,analyteIds, unAnalyteAid);

        // 增加提示 获取不到Analyte 的testLine
        rspResult.setMsg(testMatrixService.buildUnAnalyteMessage(ppTestLineInfos, unAnalyteAid));

        List<OrderLanguageRelInfoPO> orderLanguageRelInfoDbList= getOrderLanguageRelInfoPOS(orderID);
        //append testline citation language
        List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOSSaved = Lists.newArrayList();
//        appendTestlineCitationLanguageRelInfoPOS(orderID, orderLanguageRelInfoDbList, orderLanguageRelInfoPOSSaved, Lists.newArrayList(citationBaseIds));
        if (CollectionUtils.isNotEmpty(citationBaseIds)) {
            List<GetArtifactCitationLanguageRsp> artifactCitationLanguage = languageClient.getArtifactCitationLanguage(Lists.newArrayList(citationBaseIds));
            for(GetArtifactCitationLanguageRsp citationLang : artifactCitationLanguage) {
                if (!orderLanguageRelInfoDbList.stream()
                        .anyMatch(item -> NumberUtil.equals(item.getLangBaseId(), citationLang.getLangId())
                                && LangTypeEnum.check(item.getLangType(), LangTypeEnum.Citation))) {
                    //build new OrderLanguageRelInfoPO
                    orderLanguageRelInfoPOSSaved.add(buildOrderLanguageRelIInfo(citationLang.getCitationBaseId(), citationLang.getLanguageId(), citationLang.getLangId(), orderID, LangTypeEnum.Citation.getType()));
                }
            }
        }

        Set<String> testLineIdSets = new HashSet<>(testLineInstanceIds);
        orderLangRelService.batchUpdateCitationLangInfo(orderID, testLineIdSets, orderLanguageRelInfoPOSSaved);

        ReportInfoPO reportInfoPO = reportMapper.getReportByOrderNo(orderInfo.getOrderNo());
        UserInfo userInfo = tokenClient.getUser();


        // DIG-9487 客户筛选Analyte
        List<String> checkNeedDelAnalyteIds = Lists.newArrayList();
        CheckDataForSCIReq checkDataForSCIReq = new CheckDataForSCIReq();
        checkDataForSCIReq.setOrderNo(orderInfo.getOrderNo());
        checkDataForSCIReq.setCustomerValidateOperation(CustomerValidateOperationEnum.ConfirmMatrix.getType());
        checkDataForSCIReq.setLocalTrimsAnalyteList(analytePOList);
        CustomResult<CheckDataForSCIRsp> checkValidate = validateDataService.checkValidateDataFormSCI(checkDataForSCIReq);
        if (!checkValidate.isSuccess()) {
            logger.info("[OrderNo-UpdateStandard]OrderNo:{}, updateStandard:{}, checkValidateDataFormSCI:{}", orderInfo.getOrderNo(), JSONObject.toJSONString(standards), Boolean.FALSE);
            // 判断结果中是否有数据
            if (checkValidate.getData() != null && CollectionUtils.isNotEmpty(checkValidate.getData().getLocalTrimsAnalyteList())) {
                // 更新 trimsAnalyteList
                analytePOList = checkValidate.getData().getLocalTrimsAnalyteList();
                checkNeedDelAnalyteIds = checkValidate.getData().getNeedDelAnalyteIds();
                logger.info("[OrderNo-UpdateStandard]OrderNo:{}, updateStandard:{}, checkNeedDelAnalyteIds:{}", orderInfo.getOrderNo(), JSONObject.toJSONString(standards), checkNeedDelAnalyteIds);
            }
        }

        List<AnalyteInfoPO> finalAnalytePOList = analytePOList;
        List<String> finalCheckNeedDelAnalyteIds = checkNeedDelAnalyteIds;
        rspResult.setSuccess(transactionTemplate.execute((tranStatus)->{
            analyteMapper.deleteAnalyteWithOutLimit(testLineInstanceIds);
            testMatrixService.saveAnalyte(orderID, finalAnalytePOList, analyteLanguageMaps, finalCheckNeedDelAnalyteIds, 1);
            testLineMapper.updateBatchStandard(testLineInstancePOsUpdate);
            // DIG-6577 Change 修改 Standard 时  同步修改 matrixStatus
            if (testLineInstancePOsUpdate.size() == 1 && !TestLineStatus.check(testLineInstancePOsUpdate.get(0).getTestLineStatus(), TestLineStatus.Typing, TestLineStatus.DR)) {
                String testLineIdForUpdateMatrix = testLineInstancePOsUpdate.get(0).getID();
                List<String> testMatrixFromTestLineId = testMatrixMapper.getTestMatrixByTestLineId(testLineIdForUpdateMatrix);
                // DIG-6914 change 更改 matrix时  同时修改 TL status
                testLineStatusService.updateMatrixStatus(TestLineModuleType.ChangeUpdateStandard, testLineIdForUpdateMatrix, Sets.newHashSet(testMatrixFromTestLineId));
                Integer testLineId = testLineInstancePOsUpdate.get(0).getTestLineID();
                BizLogHelper.setValue(orderInfo.getOrderNo(), testLineId);
                if(reportInfoPO != null && NumberUtil.toInt(reportInfoPO.getRecalculationFlag()) == 1){
                    reportInfoPO.setRecalculationFlag(2);
                    reportInfoPO.setModifiedBy(userInfo.getRegionAccount());
                    reportInfoPO.setModifiedDate(DateUtils.getNow());
                    reportMapper.updateReportRecalculationFlag(reportInfoPO);
                }
            }
            return true;
        }));
        logger.info("[OrderNo-UpdateStandard]OrderNo:{}, updateStandard:{}, Success", orderInfo.getOrderNo(), JSONObject.toJSONString(standards));
        return rspResult;
    }


    /**
     * description   :  getSubContractInfo
     *
     * @param reqObject
     * @return       : com.sgs.otsnotes.facade.model.common.CustomResult
     * @exception    :
     * @date         : 2020/6/18 10:20 PM
     * <AUTHOR> Killian.Sun  Sun Hengyuan
     */
    public CustomResult getSubContractInfo(SubContractInfoReq reqObject){
        CustomResult rspResult = new CustomResult();
        List<String> subcontractIds = reqObject.getSubcontractIds();
        List<SubContractPrintRsp> resultDataList = Lists.newArrayList();
        for (String subcontractId : subcontractIds) {
            // 获取各个系统的参数拼装
            SubContractPrintRsp vo = new SubContractPrintRsp();
            SubContractPO subContractPO = subContractMapper.selectByPrimaryKey(subcontractId);
            if(subContractPO==null){
                logger.info("subcontractID:{} 查询不到subcontract",subcontractId);
                rspResult.setSuccess(false);
                rspResult.setMsg("Query subcontract data is null by keyId");
                return rspResult;
            }

            String extData = subContractPO.getExtData();
            if(StringUtils.isNotBlank(extData)){
                SubContractExtInfo subContractExtInfo = JSONObject.parseObject(extData, SubContractExtInfo.class);
                vo.setAgeGroup(StringUtils.join(subContractExtInfo.getAgeGroup(),"|"));
                vo.setSampleType(StringUtils.join(subContractExtInfo.getSampleType(),"|"));
                //Email 的设置 ，迁移到这里
                vo.setEmail(subContractExtInfo.getSoftCopyDeliverTo());
            }

            //vn 需要分包联系人和开始时间
            vo.setSubcontractContract( subContractPO.getSubContractContract());
            vo.setSubcontractStartDate(subContractPO.getStartDate());
            //vn 需要设置or,cr对应的 email，并显示到from
            this.getAndCreateOrderInfo(subContractPO.getOrderNo(),vo);

            GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfo(subContractPO.getOrderNo());

            String subServiceType = orderClient.getOrderServiceTypeByOrderNo(subContractPO.getOrderNo());
            if(NumberUtil.toInt(subContractPO.getSubcontractTAT())!=0){
                subServiceType = String.format("%s+%s",subServiceType,subContractPO.getSubcontractTAT());
            }

            // DIG-8697 修改为配置
            List<BUSettingSubContractEmailDTO> subcontractAddress = frameWorkClient.getBUSettingSubcontractAddressEmail(
                    Constants.BU_PARAM.GENERAL.GROUP,
                    Constants.BU_PARAM.GENERAL.CODE.LabAddress,
                    SysConstants.SYSTEMID);
            Map<String, String> labAddressMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(subcontractAddress)) {
                labAddressMap = subcontractAddress.stream()
                        .filter(item -> StringUtils.isNotBlank(item.getLabCode()) && StringUtils.isNotBlank(item.getLabAddress()))
                        .collect(Collectors.toMap(BUSettingSubContractEmailDTO::getLabCode, BUSettingSubContractEmailDTO::getLabAddress, (k1, k2) -> k1));
            }
            String labCode = StringUtils.trim(orderInfo.getLabCode());
            if (labAddressMap.containsKey(labCode)) {
                vo.setAddress(labAddressMap.get(labCode));
            }
            String packageName = this.createSubcontractPackageName(subcontractId);
            vo.setPackageName(packageName);

            vo.setTo(subContractPO.getSubContractLabName()+" / "+subContractPO.getSubContractContract()+" / "+subContractPO.getSubContractContractTel()+" / "+subContractPO.getSubContractContractEmail());
            vo.setOrderNo(subContractPO.getOrderNo());
            vo.setLabCode(orderInfo.getLabCode());
            vo.setAdditionalInfo(subContractPO.getAdditionalInfo());
            vo.setServiceType(subServiceType);
            vo.setSubContractOrder(subContractPO.getSubContractOrder());


            //zhangtao DIG-3242: 设置分包单备注
            vo.setRemark(subContractPO.getSubContractRemark());

            //String expectedDueDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(subContractPO.getSubContractExpectDueDate());
            vo.setExpectedDueDate(DateUtils.format(subContractPO.getSubContractExpectDueDate()));
            vo.setSubContractNo(subContractPO.getSubContractNo());
            GetOrderInfoRsp params = new GetOrderInfoRsp();
            params.setOrderNo(subContractPO.getOrderNo());
            PreOrder preOrder = preOrderClient.getOrderInfo(params);

            vo.setOrderCreateDate(DateFormatUtil.formatDate(preOrder.getOrderCreateDate()));

            String cSContactTel = StringUtil.isBlank(preOrder.getcSContactTel(), StringUtils.EMPTY);
            String responsibleCS = StringUtil.isBlank(preOrder.getResponsibleCS(), StringUtils.EMPTY);
            String cSEmailAddress = StringUtil.isBlank(preOrder.getcSEmailAddress(), StringUtils.EMPTY);

            vo.setFm(String.format("%s / %s %s,%s",orderInfo.getLabCode(), responsibleCS, cSContactTel, cSEmailAddress));
            vo.setBuyer(nullToWhitespace(preOrder.getBuyerClientName()));
            vo.setResponsibleTeam(preOrder.getResponsibleTeam());

            ReportLanguage reportLanguage = ReportLanguage.findName(preOrder.getReportLanguage());
            if (reportLanguage!=null){
                vo.setReportLanguage(reportLanguage.getName());
            }
            vo.setCommentRequired(preOrder.getCommentRequired());
            String returnResidueSample = "";
            if(Constants.YES_OR_NO.YES.equalsIgnoreCase(preOrder.getReturnResidueSampleFlag())){
                /**
                 * TODO Hengyuan
                 * 1、改为：returnResidueSample=String.format("Residue Sample: %s", StringUtil.isBlank(preOrder.getReturnResidueSampleRemark(), ""));
                 */
                if(StringUtils.isNoneBlank(preOrder.getReturnResidueSampleRemark())){
                    returnResidueSample=String.format("Residue Sample: %s", StringUtil.isBlank(preOrder.getReturnResidueSampleRemark(), ""));
                }else {
                    returnResidueSample="Residue Sample: ";
                }
            }

            vo.setReturnResidueSample(returnResidueSample);
            String returnTestedSample = "";
            if(Constants.YES_OR_NO.YES.equalsIgnoreCase(preOrder.getReturnTestedSampleFlag())){
                /**
                 * TODO Hengyuan
                 * 1、改为：returnTestedSample=String.format("Tested Sample: %s", StringUtil.isBlank(preOrder.getReturnTestedSampleRemark(), ""));
                 */
                if(StringUtils.isNoneBlank(preOrder.getReturnTestedSampleRemark())){
                    //returnTestedSample="Tested Sample: "+preOrder.getReturnTestedSampleRemark();
                    returnTestedSample=String.format("Tested Sample:  %s", StringUtil.isBlank(preOrder.getReturnTestedSampleRemark(), ""));
                }else {
                    returnTestedSample="Tested Sample:  ";
                }
            }
            vo.setReturnTestedSample(returnTestedSample);

            List<TestLineInstanceSubContractDTO> testLineInstanceSubContractDTOS =
                    this.queryTestlineList(subcontractId,orderInfo.getID());

            //设置TestLine的Remark信息
            if(testLineInstanceSubContractDTOS.size()>0){
                for (int i = 0; i < testLineInstanceSubContractDTOS.size(); i++) {
                    TestLineInstanceDTO testLineInstanceDTO = testLineInstanceSubContractDTOS.get(i);
                    if(StringUtils.isNotEmpty(testLineInstanceDTO.getOrdertestLineRemark())){
                        vo.setOrdertestLineRemark(testLineInstanceDTO.getOrdertestLineRemark());
                    }
                }
            }


            List<SampleDTO> sampleDTOS = subContractExtMapper.querySampleList(subcontractId);
            List<SampleRsp> sampleRsps = new ArrayList<>();
            for (SampleDTO sampleDTO : sampleDTOS){
                SampleRsp newRes = new SampleRsp();
                BeanUtils.copyProperties(sampleDTO,newRes);
                sampleRsps.add(newRes);
            }
            List<TestLineInstanceSubContractRsp> testLineInstanceSubContractRsps = new ArrayList<>();
            for (TestLineInstanceSubContractDTO subContractDTO : testLineInstanceSubContractDTOS){
                TestLineInstanceSubContractRsp newRes = new TestLineInstanceSubContractRsp();
                BeanUtils.copyProperties(subContractDTO,newRes);
                testLineInstanceSubContractRsps.add(newRes);
            }
            testLineLocalService.build(subContractPO.getOrderNo(), testLineInstanceSubContractRsps);

            vo.setTestItemList(testLineInstanceSubContractRsps);
            //vo.setSampleList(sampleDTOS);
            vo.setSampleList(sampleRsps);

            if(StringUtils.isNoneBlank(preOrder.getPhotoRemark())){
                vo.setPhotoRequired(preOrder.getPhotoRequired()+" | "+preOrder.getPhotoRemark());
            }else{
                vo.setPhotoRequired(preOrder.getPhotoRequired());
            }
            resultDataList.add(vo);
        }

        //转成调用TS服务对象结构
        List<SubcontractDigitalPrintInfo> info = this.createDigitalInfo(resultDataList);
        if(info == null){
            return rspResult.fail("Query order info fail");
        }
        CustomResult callResult = this.callDigitalReportGenerateSubcontractReport(info);
        if(!callResult.isSuccess()){
            return callResult;
        }
        rspResult.setData(callResult.getData());
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param orderNo
     * @param vo
     */
    private void getAndCreateOrderInfo(String orderNo, SubContractPrintRsp vo) {
        CustomResult<OrderSimplifyInfoRsp> orderSimplifyInfo = orderClient.getOrderSimplifyInfo(orderNo);
        OrderSimplifyInfoRsp order = orderSimplifyInfo.getData();
        if(order == null){
            return ;
        }
        String buCode = order.getBuCode();
        vo.setBuCode(buCode);

        String or = order.getOr();
        String cr = order.getCr();

        EmpInfoReq empInfoReq = new EmpInfoReq();
        if(StringUtils.isNotBlank(cr)){
            empInfoReq.setRegionAccount(cr);
            com.sgs.framework.core.base.CustomResult<EmpInfoExtRsp> empInfo = userManagementClient.getEmpInfo(empInfoReq);
            if(empInfo.isSuccess()){
                EmpInfoExtRsp emp = empInfo.getData();
                String email = emp.getEmail();
                vo.setCrName(cr);
                vo.setCrTel(emp.getTelephone());
                vo.setCrEmail(email);

            }
        }

        if(StringUtils.isNotBlank(or)){
            empInfoReq.setRegionAccount(or);
            com.sgs.framework.core.base.CustomResult<EmpInfoExtRsp> empInfo = userManagementClient.getEmpInfo(empInfoReq);
            if(empInfo.isSuccess()){
                EmpInfoExtRsp emp = empInfo.getData();
                String email = emp.getEmail();
                vo.setOrName(or);
                vo.setOrTel(emp.getTelephone());
                vo.setOrEmail(email);
            }
        }
    }

    private CustomResult callDigitalReportGenerateSubcontractReport(List<SubcontractDigitalPrintInfo> subcontractDetailInfo) {
        CustomResult rspResult = new CustomResult();

        subcontractDetailInfo = processWithTimeZone(subcontractDetailInfo);

        CustomResult<DigitalReportResp> digitalReportRespCustomResult = digitalReportClient.generateSubcontractPrint(subcontractDetailInfo);
        if(!digitalReportRespCustomResult.isSuccess()){
            return rspResult.fail(digitalReportRespCustomResult.getMsg());
        }
        DigitalReportResp data = digitalReportRespCustomResult.getData();
        if(data == null){
            return rspResult.fail("Call digital report server fail");
        }
        String cloudId = data.getCloudId();
        String fileUrlByCloudId = fileClient.getFileUrlByCloudId(cloudId);
        rspResult.setData(fileUrlByCloudId);
        rspResult.setSuccess(true);
        return rspResult;
    }

    private static List<SubcontractDigitalPrintInfo> processWithTimeZone(List<SubcontractDigitalPrintInfo> subcontractDetailInfo) {
        try {
            ObjectMapper objectMapper = SpringUtil.getBean(ObjectMapper.class);
            String object = objectMapper.writeValueAsString(subcontractDetailInfo);
            subcontractDetailInfo = JSON.parseObject(object, new com.alibaba.fastjson.TypeReference<List<SubcontractDigitalPrintInfo>>(){});
            return subcontractDetailInfo;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取分包打印的packageName
     * @param subcontractId
     * @return
     */
    private String createSubcontractPackageName(String subcontractId) {
        List<SubcontractPPSampleDTO> list = subContractExtMapper.querySubcontractPPSample(subcontractId);
        if(CollectionUtils.isEmpty(list)){
            return Strings.EMPTY;
        }
        Set<Long> rootPPBaseIds = list.stream().map(s -> s.getRootPpBaseId()).distinct().collect(Collectors.toSet());
        List<GetPpBaseInfoRsp> ppInfoList = ppClient.getPpBaseInfoList(rootPPBaseIds);
        if(CollectionUtils.isEmpty(ppInfoList)){
            return Strings.EMPTY;
        }

        Map<Long, String> ppBaseIdPPNameMap = ppInfoList.stream().filter(p -> NumberUtil.toLong(p.getPpBaseId())>0).collect(Collectors.toMap(GetPpBaseInfoRsp::getPpBaseId, GetPpBaseInfoRsp::getPpName, (k1, k2) -> k1));
        Map<Long, List<SubcontractPPSampleDTO>> rootPPBaseSubList = list.stream().collect(Collectors.groupingBy(SubcontractPPSampleDTO::getRootPpBaseId));
        List<String> packageNames = Lists.newArrayList();
        rootPPBaseSubList.forEach((rootPPBaseId,subList)->{
            subList.sort(Comparator.comparing(SubcontractPPSampleDTO::getSampleSeq));
            List<String> sampleNos = subList.stream().map(sub -> sub.getSampleNo()).collect(Collectors.toList());
            String prefixSampleNo = StringUtils.join(sampleNos, "&");
            String ppName = ppBaseIdPPNameMap.get(rootPPBaseId);
            String packageName = String.format("%s:%s",prefixSampleNo,ppName);
            packageNames.add(packageName);
        });
        String resultPackageName = StringUtils.join(packageNames, "\n");
        return resultPackageName;
    }

    /**
     *
     * @param resultDataList
     * @return
     */
    private List<SubcontractDigitalPrintInfo> createDigitalInfo(List<SubContractPrintRsp> resultDataList) {
        if(CollectionUtils.isEmpty(resultDataList)){
            return null;
        }
        List<SubcontractDigitalPrintInfo> digitalPrintInfoList = Lists.newArrayList();
        for (SubContractPrintRsp sub : resultDataList) {
            SubcontractDigitalPrintInfo digitalPrintInfo = new SubcontractDigitalPrintInfo();
            String buCode = sub.getBuCode();
            //info 对象是固定的
            SubcontractDigitalInfoBean info = new SubcontractDigitalInfoBean();
            info.setAppId(DigitalAppIDEnums.SL.getAppId());
            info.setBuCode(buCode);
            info.setGroupCode(Constants.DIGITAL_REPORT_GENERAL_GROUP_CODE);
            info.setInstanceNumber(sub.getOrderNo());
            info.setReportType(DigitalReportReportTypeEnums.Subcontract.getReportType());
            info.setStorageType(Constants.DIGITAL_REPORT_STORAGETYPE);
            digitalPrintInfo.setInfo(info);

            //组装datasource
            DigitalReportDataSourceInfo dataSourceInfo = new DigitalReportDataSourceInfo();
            dataSourceInfo.setProductLineCode(buCode);

            VariableDataListInfo variableDataListInfo = new VariableDataListInfo();
            //variabeldata 的order
            DigitalReportOrderInfoInfo digitalReportOrderInfoInfo = new DigitalReportOrderInfoInfo();
            digitalReportOrderInfoInfo.setOrderNo(sub.getOrderNo());
            digitalReportOrderInfoInfo.setLabCode(sub.getLabCode());
            digitalReportOrderInfoInfo.setOrderCreatedDate(sub.getOrderCreateDate());
            digitalReportOrderInfoInfo.setOrderExpectDueDate(sub.getExpectedDueDate());
            digitalReportOrderInfoInfo.setServiceType(sub.getServiceType());
            digitalReportOrderInfoInfo.setBuyerEnName(sub.getBuyer());
            digitalReportOrderInfoInfo.setPhotoRequired(sub.getPhotoRequired());
            digitalReportOrderInfoInfo.setLabAddress(sub.getAddress());
            digitalReportOrderInfoInfo.setResponsibleTeam(sub.getResponsibleTeam());
            digitalReportOrderInfoInfo.setReportLanguage(sub.getReportLanguage());
            digitalReportOrderInfoInfo.setResponsibleCSEmail(sub.getEmail());
            digitalReportOrderInfoInfo.setChemicalReviewName(sub.getCrName());
            digitalReportOrderInfoInfo.setChemicalReviewTel(sub.getCrTel());
            digitalReportOrderInfoInfo.setChemicalReviewEmail(sub.getCrEmail());
            digitalReportOrderInfoInfo.setOrderReviewName(sub.getOrName());
            digitalReportOrderInfoInfo.setOrderReviewTel(sub.getOrTel());
            digitalReportOrderInfoInfo.setOrderReviewEmail(sub.getOrEmail());

            Integer subContractOrder = sub.getSubContractOrder();
            String subcontractOrderDesc = Strings.EMPTY;
            if(SubContractOrderEnum.check(subContractOrder,SubContractOrderEnum.TO_SLIM,SubContractOrderEnum.TO_STARLIMS)){
                subcontractOrderDesc = SubContractOrderEnum.findStatus(sub.getSubContractOrder()).getDesc().toUpperCase();
            }
            digitalReportOrderInfoInfo.setSubContractOrder(subcontractOrderDesc);
            digitalReportOrderInfoInfo.setCommentRequired(sub.getCommentRequired());
            digitalReportOrderInfoInfo.setPackageName(sub.getPackageName());
            variableDataListInfo.setOrder(digitalReportOrderInfoInfo);

            //variabeldata 的sampleList
            List<SampleRsp> subSampleList = sub.getSampleList();
            subSampleList = CollectionUtils.isEmpty(subSampleList) ? Lists.newArrayList() : subSampleList;
            List<DigitalReportSampleInfo> sampleList = subSampleList.stream().map(s->{
                DigitalReportSampleInfo digitalReportSampleInfo = new DigitalReportSampleInfo();
                digitalReportSampleInfo.setSampleNo(s.getSampleNo());
                digitalReportSampleInfo.setSampleDescription(s.getDescription());
                digitalReportSampleInfo.setComposition(s.getComposition());
                digitalReportSampleInfo.setColor(s.getColor());
                digitalReportSampleInfo.setEndUse(s.getEndUse());
                digitalReportSampleInfo.setOtherSampleInformation(s.getOtherSampleInfo());
                return digitalReportSampleInfo;
            }).collect(Collectors.toList());
            variableDataListInfo.setSampleList(sampleList);

            //variabeldata 的testline
            List<TestLineInstanceSubContractRsp> testItemList = sub.getTestItemList();
            testItemList = CollectionUtils.isEmpty(testItemList) ? Lists.newArrayList() : testItemList;
            List<DigitalReportTestLineInfoInfo> testLine = testItemList.stream().map(t->{
                DigitalReportTestLineInfoInfo tlInfo = new DigitalReportTestLineInfoInfo();
                tlInfo.setEvaluationAlias(t.getEvaluationAlias());
                String standardName = t.getStandardName();
                String standardSectionName = t.getStandardSectionName();
                if(StringUtils.isNotBlank(standardSectionName)){
                    standardName = standardName + "," + standardSectionName;
                }
                tlInfo.setCombinedTestStandard(standardName);
                tlInfo.setCombinedSampleNo(t.getSampleNo());
                tlInfo.setCombinedTestCondition(t.getTestConditionName());
                tlInfo.setCombinedRemark(t.getOrdertestLineRemark());
                return tlInfo;
            }).collect(Collectors.toList());
            variableDataListInfo.setTestLine(testLine);

            //variabeldata 的subcontract
            List<DigitalReportSubcontractInfo> subcontract = Lists.newArrayList();
            DigitalReportSubcontractInfo subInfo = new DigitalReportSubcontractInfo();
            subInfo.setSubcontractNo(sub.getSubContractNo());
            subInfo.setSubcontractFrom(sub.getFm());
            subInfo.setSubcontractTo(sub.getTo());
            subInfo.setSubcontractToContactName(sub.getSubcontractContract());
            subInfo.setSubcontractStartDate(DateUtils.format(sub.getSubcontractStartDate()));
            subInfo.setAdditionalInfo(sub.getAdditionalInfo());
            subInfo.setSubcontractRemark(sub.getRemark());
            subInfo.setAgeGroup(sub.getAgeGroup());
            subInfo.setSampleType(sub.getSampleType());

            String returnResidueSample = sub.getReturnResidueSample();
            String returnTestedSample = sub.getReturnTestedSample();
            List<String> returnSamples = Lists.newArrayList(returnResidueSample,returnTestedSample).stream().filter(s-> StringUtils.isNotBlank(s)).collect(Collectors.toList());
            String sampleReturn = Strings.EMPTY;
            if(CollectionUtils.isNotEmpty(returnSamples)){
                sampleReturn = StringUtils.join(returnSamples, "\n");
            }
            subInfo.setSampleReturn(sampleReturn);
            subcontract.add(subInfo);
            variableDataListInfo.setSubcontract(subcontract);

            dataSourceInfo.setVariableDataList(variableDataListInfo);
            digitalPrintInfo.setDatasource(dataSourceInfo);

            digitalPrintInfoList.add(digitalPrintInfo);

        }
        return digitalPrintInfoList;
    }

    public List<TestLineInstanceSubContractDTO> queryTestlineList(String id,String generalOrderInstanceID) {
        List<CitationNameRsp> citationNameRsps = trimsLocalCitationService.getCitationNameByOrderId(generalOrderInstanceID);
        Map<String,CitationNameRsp> fullNameMap = Maps.newHashMap();
        citationNameRsps.forEach(c->{
            String key = String.format("%s_%s", c.getCitationBaseId(), NumberUtil.toLong(c.getPpArtifactRelId()));
            fullNameMap.put(key,c);
        });
        List<TestLineInstanceSubContractDTO> testLineInstanceSubContractDTOs = subContractExtMapper.queryTestlineList(id);
        List<String> testLineInstanceIDs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(testLineInstanceSubContractDTOs)) {
            testLineInstanceSubContractDTOs.forEach(testLineInstanceSubContractDTO -> {
                testLineInstanceIDs.add(testLineInstanceSubContractDTO.getId());
            });
            if (CollectionUtils.isNotEmpty(testLineInstanceIDs)) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("testLineInstanceIDs", testLineInstanceIDs);
                List<TestLineInstanceSubContractDTO> testLineInstanceSubContractDTOs2 = subContractExtMapper
                        .getTestConditionByTestLineIdList(testLineInstanceIDs);
                Map<String, String> testConditionMap = new HashMap<>();
                for (TestLineInstanceSubContractDTO testLineInstanceSubContractDTO : testLineInstanceSubContractDTOs2) {
                    if (testLineInstanceSubContractDTO != null) {
                        testConditionMap.put(
                                testLineInstanceSubContractDTO.getId() + "-"
                                        + testLineInstanceSubContractDTO.getMatrixGroupId(),
                                testLineInstanceSubContractDTO.getTestConditionName());
                    }
                }
                for (TestLineInstanceSubContractDTO testLineInstanceSubContractDTO : testLineInstanceSubContractDTOs) {
                    Long ppArtifactRelId = null;
                    if(CollectionUtils.isNotEmpty(testLineInstanceSubContractDTO.getPpArtifactRelIds())){
                        Optional<Long> optional = testLineInstanceSubContractDTO.getPpArtifactRelIds().stream().filter(aLong -> !NumberUtil.equals(aLong,0L)).findFirst();
                        if(optional.isPresent()){
                            ppArtifactRelId = optional.get();
                        }
                    }
                    String key = String.format("%s_%s", testLineInstanceSubContractDTO.getCitationBaseId(), NumberUtil.toLong(ppArtifactRelId));
                    CitationNameRsp nameRsp = fullNameMap.get(key);
                    if(Objects.nonNull(nameRsp)){
                        testLineInstanceSubContractDTO
                                .setStandardName(nameRsp.getCitationFullName());
                    }
                    testLineInstanceSubContractDTO
                            .setTestConditionName(testConditionMap.get(testLineInstanceSubContractDTO.getId() + "-"
                                    + testLineInstanceSubContractDTO.getMatrixGroupId()));
                }

            }
        }
        return testLineInstanceSubContractDTOs;
    }
    public  CustomResult checkAssignedSampleByTestLine(String testLineID){
        CustomResult rspResult = new CustomResult();
        TestMatrixInfoExample testMatrixInfoExample=new TestMatrixInfoExample();
        testMatrixInfoExample.createCriteria().andTestLineInstanceIDEqualTo(testLineID);
        List<TestMatrixInfoPO> testMatrixInfoPOS= testMatrixInfoMapper.selectByExample(testMatrixInfoExample);
        if (CollectionUtils.isNotEmpty(testMatrixInfoPOS)){
            rspResult.setSuccess(false);
            rspResult.setMsg("TestLine has assigned sample ,please check!");
            return rspResult;
        }
        rspResult.setSuccess(true);
        return  rspResult;
    }

    public CustomResult checkNCTestline(List<String> testLineInstanceIds){
        CustomResult rspResult = new CustomResult();

        //1 check tl 是否assign了sample
        List<TestMatrixTestLineInfo> testMatrixInfos = testMatrixMapper.getMatrixInfoByTestLineIds(testLineInstanceIds);
        if(CollectionUtils.isNotEmpty(testMatrixInfos)){
            //组装提示信息
            List<String> msg = Lists.newArrayList();
            //按tl 分组，提示
            Map<Integer, List<TestMatrixTestLineInfo>> tlIdMatrixLIst = testMatrixInfos.stream().collect(Collectors.groupingBy(TestMatrixTestLineInfo::getTestLineId));
            tlIdMatrixLIst.forEach((tlid,list)->{
                List<String> sampleNos = list.stream().map(TestMatrixTestLineInfo::getSampleNo).collect(Collectors.toList());
                msg.add(String.format("TestLine %s has been assigned sample %s ",tlid, StringUtils.join(sampleNos,",")));
            });
            return rspResult.fail(StringUtils.join(msg,";"));
        }
        //2 check tl是否做了分包
        List<SubcontractDTO> subcontractDTOS = subContractExtMapper.querySubContractListByTLIds(testLineInstanceIds);
        if(CollectionUtils.isNotEmpty(subcontractDTOS)){
            //组装提示信息
            List<String> msg = Lists.newArrayList();
            subcontractDTOS.stream().forEach(t->{
                msg.add(String.format("TestLine %s has subContract %s , please check",t.getTestLineId(),t.getSubContractNo()));
            });
            return rspResult.fail(StringUtils.join(msg,";"));
        }

        //3 check tl status
        List<TestLineInstancePO> testLineInstancePOs = testLineMapper.getTestLineByIds(testLineInstanceIds);
        if(CollectionUtils.isEmpty(testLineInstancePOs)){
            return rspResult.fail("Check testline status fail");
        }

        CustomResult customResult = TestLineStatusManager.checkTestLineStatus(ActionTestLineStatusMatrixEnum.NC_TL,testLineInstancePOs);
        if(! customResult.isSuccess()){
            rspResult.setSuccess(false);
            rspResult.setMsg("TestLine状态为"+customResult.getMsg()+"才能NC TestLine!");
            return rspResult;
        }
        return rspResult.success();
    }

    /**
     *
     * @param reqObj
     * @return
     */
    @AccessRule(reportStatus = { ReportStatus.Approved, ReportStatus.Cancelled, ReportStatus.Replaced },
            subContractType = SubContractOperationTypeEnums.NCTestLine,
            testLinePendingType = TestLinePendingTypeEnums.TestLineInstanceId)
    public CustomResult ncTestLine(TestLineRemarkReq reqObj) {

        final List<String> testLineInstanceIds = reqObj.getTestLineInstanceIds();

        CustomResult rspResult = this.checkNCTestline(testLineInstanceIds);
        if(!rspResult.isSuccess()){
            return rspResult;
        }

        String testLineRemark = reqObj.getTestLineRemark();

        if(StringUtils.isBlank(testLineRemark)){
            return rspResult.fail("Please input remark");
        }

        UserInfo user = UserHelper.getLocalUser();
        if(user == null){
            return rspResult.fail("Get User Fail");
        }

        //4 如果tl create 了job ，且job下的tl是唯一的一个，需要将job进行cancel
        List<JobStatusInfo> jobInfoPOUpdateStatusList = Lists.newArrayList();
        List<JobInfoReq> jobInfoReqsForPreorderFacade = Lists.newArrayList();
        List<String> deleteJobRelList = Lists.newArrayList();

        JobTestLineRelationshipInfoExample jobTestLineRelationshipInfoExample = new JobTestLineRelationshipInfoExample();
        jobTestLineRelationshipInfoExample.createCriteria().andTestLineInstanceIDIn(testLineInstanceIds);
        List<JobTestLineRelationshipInfoPO> jobTestLineRelationshipInfoPOS = jobTestLineRelationshipInfoMapper.selectByExample(jobTestLineRelationshipInfoExample);

        if (CollectionUtils.isNotEmpty(jobTestLineRelationshipInfoPOS)) {
            //用jobid 分组
            Map<String, List<JobTestLineRelationshipInfoPO>> jobIdListMap = jobTestLineRelationshipInfoPOS.stream().collect(Collectors.groupingBy(JobTestLineRelationshipInfoPO::getJobID));
            //获取jobNo
            Set<String> jobId = jobIdListMap.keySet();
            JobInfoExample jobInfoExample = new JobInfoExample();
            jobInfoExample.createCriteria().andIDIn(Lists.newArrayList(jobId));

            List<JobInfoPO> jobInfoPOList = jobInfoMapper.selectByExample(jobInfoExample);
            Map<String, String> jobIdJobNoMap = jobInfoPOList.stream().collect(Collectors.toMap(JobInfoPO::getID, JobInfoPO::getJobNo, (k1, k2) -> k1));
            //check jobList 是否只有唯一的一个tl
            for (Map.Entry<String, List<JobTestLineRelationshipInfoPO>> stringListEntry : jobIdListMap.entrySet()) {
                List<JobTestLineRelationshipInfoPO> jobTestLineList = stringListEntry.getValue();
                if(jobTestLineList.size()>1){
                    continue;
                }
                //需要cancel当前job, 删除rel关系
                String jobID = stringListEntry.getKey();
                String jobNo = jobIdJobNoMap.get(jobID);
                deleteJobRelList.add(jobID);

                JobStatusInfo jobPO = new JobStatusInfo();
                jobPO.setModifiedBy(user.getRegionAccount());
                jobPO.setModifiedDate(DateUtils.getNow());
                jobPO.setJobStatus(JobStatus.Cancelled.getStatus());
                jobPO.setJobNo(jobNo);
                jobInfoPOUpdateStatusList.add(jobPO);

                //用来发送到preorder ，进行同步更新
                JobInfoReq jobInfoReq = new JobInfoReq();
                jobInfoReq.setJobNo(jobNo);
                jobInfoReq.setJobStatus(JobStatus.Cancelled.getStatus());
                jobInfoReq.setModifiedBy(user.getRegionAccount());
                jobInfoReq.setModifiedDate(DateUtils.getNow());
                jobInfoReqsForPreorderFacade.add(jobInfoReq);
            }
        }

        //5 更新TL状态和remark
        List<TestLineInstancePO> updateTLStatusToNCList = testLineInstanceIds.stream().map(tlid -> {
            TestLineInstancePO updateTLStatusToNC = new TestLineInstancePO();
            updateTLStatusToNC.setID(tlid);
            updateTLStatusToNC.setModifiedBy(user.getRegionAccount());
            updateTLStatusToNC.setModifiedDate(DateUtils.getNow());
            updateTLStatusToNC.setOrdertestLineRemark(testLineRemark);
            updateTLStatusToNC.setTestLineStatus(TestLineStatus.NC.getStatus());
            return updateTLStatusToNC;
        }).collect(Collectors.toList());

        List<TestLineInstanceExtInfoPO> testLineInstanceExtInfoPOS = testLineRemarkService.queryTestLineInstanceExtInfoPO(Lists.newArrayList(testLineInstanceIds));
        TestLineInstanceExtInfoPO testLineInstanceExtInfoPO = new TestLineInstanceExtInfoPO();
        testLineInstanceExtInfoPO.setModifiedBy(user.getRegionAccount());
        testLineInstanceExtInfoPO.setModifiedDate(DateUtils.getNow());
        testLineInstanceExtInfoPO.setActiveIndicator(ActiveType.Disable.getStatus());
        TestLineInstanceExtInfoExample testLineInstanceExtInfoExample = new TestLineInstanceExtInfoExample();
        testLineInstanceExtInfoExample.createCriteria().andTestlineInstanceIdIn(testLineInstanceIds);


        List<TestLineInstancePO> testLineInstancePOs = testLineMapper.getTestLineByIds(testLineInstanceIds);
        String orderId = testLineInstancePOs.get(0).getGeneralOrderInstanceID();


        boolean isSuccess = transactionTemplate.execute((trans) -> {
            CustomResult res=new CustomResult();
            res.setSuccess(true);

            //更新tl 状态为nc
            testLineStatusService.batchUpdateTestLineStatus(TestLineModuleType.NCTestLine, updateTLStatusToNCList);


            //删除job tl 关系
            if (CollectionUtils.isNotEmpty(deleteJobRelList)){
                jobTestLineRelExtMapper.batchDelJobTestLineRelByJobIds(deleteJobRelList);

            }
            // 更新job status 为cancel
            if (CollectionUtils.isNotEmpty(jobInfoPOUpdateStatusList)){
                jobExtMapper.batchUpdateJobStatus(jobInfoPOUpdateStatusList);

                JobReq reqJob = new JobReq();
                reqJob.setJobs(jobInfoReqsForPreorderFacade);
                res = jobClient.updateJobBatch(reqJob);
            }

            if (CollectionUtils.isNotEmpty(testLineInstanceExtInfoPOS)) {
                // 更新数据
                testLineInstanceExtInfoMapper.updateByExampleSelective(testLineInstanceExtInfoPO, testLineInstanceExtInfoExample);
            }

            if (res.isSuccess()) {
                this.orderStatusValidate(orderId);
            } else {
                trans.setRollbackOnly();
            }
            return res.isSuccess();
        });
        rspResult.setSuccess(isSuccess);
        return rspResult;
    }

    public void orderStatusValidate(String orderId) {
        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(orderId);
        int orderStatusByOrderNo = orderClient.getOrderStatusByOrderNo(order.getOrderNo());
        if (com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(orderStatusByOrderNo, com.sgs.preorder.facade.model.enums.OrderStatus.Completed)) {
            return;
        }

        List<TestLineInstancePO> testLineInstancePOS = testLineRepository.getTestLineByOrderId(orderId);

        //过滤出非Pretreatment TL
        testLineInstancePOS = testLineInstancePOS.stream().filter(tl->!TestLineType.check(tl.getTestLineType(), TestLineType.Pretreatment)).collect(Collectors.toList());
        //存在测试TL 非 NC,DR,Cancelled
        long count = testLineInstancePOS.stream()
                .filter(testline -> !TestLineStatus.check(testline.getTestLineStatus(),TestLineStatus.NC,TestLineStatus.DR,TestLineStatus.Cancelled))
                .count();
        //没有TL了 不做处理
        if(count==0){
            return;
        }
        //找出所有completed的TL
        long validCount = testLineInstancePOS.stream()
                .filter(testline ->TestLineStatus.check(testline.getTestLineStatus(),TestLineStatus.Completed))
                .count();
        //没有completed TL 不做处理
        if(validCount==0){
            return;
        }
        //所有tl 都是complete 可以修改状态了
        if (count == validCount) {
            UserInfo localUser = UserHelper.getLocalUser();
            String user = localUser == null ?"System":localUser.getRegionAccount();
            order.setOrderStatus(OrderStatus.Completed.getStatus());
            order.setModifiedBy(user);
            order.setModifiedDate(new Date());
            orderMapper.updateOrderStatus(order);
            SysStatusReq reqStatus = new SysStatusReq();
            reqStatus.setObjectNo(order.getOrderNo());
            reqStatus.setIgnoreOldStatus(true);
            reqStatus.setNewStatus(com.sgs.preorder.facade.model.enums.OrderStatus.Reporting.getStatus());
            reqStatus.setUserName(user);
            statusClient.insertStatusInfo(reqStatus);
        }

    }

    /**
     *
     * @param reqObject
     * @return
     */
    @AccessRule(reportStatus = { ReportStatus.Approved, ReportStatus.Cancelled })
    @BindOldMethod(value = OldTestLineClient.class, routeId = 18, oldRouteId = 5, methodRouteId = 2,oldMethodRouteId = 0, isWrite = true,isOld = false)
    public CustomResult saveTestLine(SaveTestLineListReq reqObject){
        CustomResult rspResult = new CustomResult();
        if (!this.saveTestLineList(reqObject, rspResult)){
            rspResult.setSuccess(false);
            return rspResult;
        };
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getTestLineClaimInfo(GetTestLineClaimInfoReq reqObject) {
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(false);
        if (reqObject.getTestLineInstanceId() == null) {
            rspResult.setMsg("PLEASE CHECK PARAMETER");
            return rspResult;
        }
        String testLineInstanceId = reqObject.getTestLineInstanceId();

        CustomResult customResult = testLineWorkInstructionRelationshipService.getClaimDataByTestLineInstanceId(testLineInstanceId);
        if (!customResult.isSuccess()) {
            return customResult;
        }

        /*List<TestLineClaimInfo> testLineClaimInfo = testLineMapper.getTestLineClaimInfo(Lists.newArrayList(testLineInstanceId));
        if (CollectionUtils.isEmpty(testLineClaimInfo)) {
            rspResult.setMsg("NO Claim Data");
            rspResult.setSuccess(true);
            return rspResult;
        }

        List<Integer> testLineVersionIds = testLineClaimInfo.stream().map(TestLineClaimInfo::getTestLineVersionID).collect(Collectors.toList());
        List<GetAnalyteInfoRsp> analyteInfos = analyteClient.getAnalyteInfoByTestLineVersionIds(testLineVersionIds);
        Map<Integer,GetAnalyteInfoRsp> analyteMap = analyteInfos.stream().collect(Collectors.toMap(GetAnalyteInfoRsp::getTestLineVersionId,Function.identity(),(o1,o2)->o1));
        testLineClaimInfo.forEach(x->{
            GetAnalyteInfoRsp analyteInfoRsp = analyteMap.get(x.getTestLineVersionID());
            if(Objects.nonNull(analyteInfoRsp)){
                x.setTestAnalyteDesc(analyteInfoRsp.getTestAnalyteDesc());
            }
        });
        testLineClaimInfo = testLineClaimInfo.stream().sorted(Comparator.comparing(TestLineClaimInfo::getTestAnalyteDesc).thenComparing(TestLineClaimInfo::getSampleSeq)).collect(Collectors.toList());*/

        // DIG-8764 提取 claim 方法
        List<TestLineClaimInfo> testLineClaimInfo = this.queryTestLineClaim(Lists.newArrayList(testLineInstanceId));

        TestLineClaimInfo claimInfo = testLineClaimInfo.get(0);
        if (claimInfo == null || claimInfo.getTestLineType() == null) {
            rspResult.setMsg("NO Claim Data");
            rspResult.setSuccess(true);
            return rspResult;
        }
        Integer testLineType = claimInfo.getTestLineType();
        if (!TestLineType.check(testLineType, TestLineType.Claim)) {
            rspResult.setMsg("NO Claim TestLine");
            rspResult.setSuccess(true);
            return rspResult;
        }

        Map<String, List<TestLineClaimInfo>> testLineClaimInfoMap = testLineClaimInfo.stream()
                .collect(Collectors.groupingBy(TestLineClaimInfo::getTestAnalyteDesc));
        List<TestLineAnalyteClaimInfo> collect = testLineClaimInfoMap.entrySet()
                .stream().map(e -> new TestLineAnalyteClaimInfo(e.getKey(), e.getValue()))
                .collect(Collectors.toList());
        for (TestLineAnalyteClaimInfo testLineAnalyteClaimInfo : collect) {
            if (CollectionUtils.isNotEmpty(testLineAnalyteClaimInfo.getSampleClaims())) {
                testLineAnalyteClaimInfo.setTestAnalyteDesc(LOStringUtil.delHTMLTag(testLineAnalyteClaimInfo.getSampleClaims().get(0).getTestAnalyteDesc()));
                continue;
            }
        }

        GetTestLineClaimInfoRsp getTestLineClaimInfoRsp = new GetTestLineClaimInfoRsp();
        getTestLineClaimInfoRsp.setClaimInfos(collect);

        getTestLineClaimInfoRsp.setSubOrderLock(this.checkSubTestLineLock(testLineInstanceId));

        // 设置AnalyteUnited
        getAnalyteUnitList(getTestLineClaimInfoRsp, testLineInstanceId, claimInfo.getTestLineVersionID());

        rspResult.setData(getTestLineClaimInfoRsp);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * 查询testLine 的 Claim
     * @param testLineInstanceIds
     * @return
     */
    public List<TestLineClaimInfo> queryTestLineClaim(List<String> testLineInstanceIds) {

        List<TestLineClaimInfo> testLineClaimInfoOrder = testLineMapper.getTestLineClaimInfo(testLineInstanceIds);
        if (CollectionUtils.isEmpty(testLineClaimInfoOrder)) {
            return Lists.newArrayList();
        }

        // 根据TestLineVersionId 获取 analyte
        List<Integer> testLineVersionIds = testLineClaimInfoOrder.stream().map(TestLineClaimInfo::getTestLineVersionID).collect(Collectors.toList());
        List<GetAnalyteInfoRsp> analyteInfos = analyteClient.getAnalyteInfoByTestLineVersionIds(testLineVersionIds);
        Map<Integer, List<GetAnalyteInfoRsp>> analyteMap = analyteInfos.stream().collect(Collectors.groupingBy(GetAnalyteInfoRsp::getTestLineVersionId));

        for (Integer testLineVersionId : analyteMap.keySet()) {
            List<GetAnalyteInfoRsp> analyteInfoRsps = analyteMap.get(testLineVersionId);
            testLineClaimInfoOrder.forEach(item -> {
                if (!NumberUtil.equals(item.getTestLineVersionID(), testLineVersionId) || CollectionUtils.isEmpty(analyteInfoRsps)) {
                    return;
                }
                List<String> testAnalyteDescs = Lists.newArrayList();
                analyteInfoRsps.forEach(analyte -> {
                    if (StringUtils.isBlank(analyte.getTestAnalyteDesc())) {
                        return;
                    }
                    String analyteDesc = LOStringUtil.delHTMLTag(analyte.getTestAnalyteDesc());
                    if (testAnalyteDescs.contains(analyteDesc)) {
                        return;
                    }
                    testAnalyteDescs.add(analyteDesc);
                });
                item.setTestAnalyteDescs(testAnalyteDescs);
            });
        }

        // 根据sampleId 获取 claim
        List<String> sampleIds = testLineClaimInfoOrder.stream().map(TestLineClaimInfo::getTestSampleID).collect(Collectors.toList());
        List<SampleClaimRelInfoPO> sampleClaimInfo = sampleClaimRelExtMapper.getSampleClaimInfoBySampleAndTestLine(sampleIds);
        Map<String, List<SampleClaimRelInfoPO>> sampleIdClaimMaps = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(sampleClaimInfo)) {
            sampleIdClaimMaps = sampleClaimInfo.stream().collect(Collectors.groupingBy(SampleClaimRelInfoPO::getSampleId));
        }

        List<TestLineClaimInfo> testLineClaimInfo = Lists.newArrayList();
        for (TestLineClaimInfo testLineClaimItem : testLineClaimInfoOrder) {
            List<TestLineClaimInfo> finalTestLineClaimInfo = testLineClaimInfo;
            List<SampleClaimRelInfoPO> sampleClaimRelInfoPOS = sampleIdClaimMaps.get(testLineClaimItem.getTestSampleID());
            Map<String, SampleClaimRelInfoPO> analyteClaimMaps = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(sampleClaimRelInfoPOS)) {
                analyteClaimMaps = sampleClaimRelInfoPOS.stream().collect(Collectors.toMap(SampleClaimRelInfoPO::getClaimType, Function.identity(), (k1, k2) -> k1));
            }
            Map<String, SampleClaimRelInfoPO> finalAnalyteClaimMaps = analyteClaimMaps;
            testLineClaimItem.getTestAnalyteDescs().forEach(item -> {

                TestLineClaimInfo testLineClaimItemNew = new TestLineClaimInfo();
                BeanUtils.copyProperties(testLineClaimItem, testLineClaimItemNew);
                testLineClaimItemNew.setTestAnalyteDesc(item);
                if (finalAnalyteClaimMaps.containsKey(item) && finalAnalyteClaimMaps.get(item) != null) {
                    SampleClaimRelInfoPO sampleClaimRelInfoPO = finalAnalyteClaimMaps.get(item);
                    testLineClaimItemNew.setSampleClaimId(NumberUtil.toInt(sampleClaimRelInfoPO.getId()));
                    testLineClaimItemNew.setClaimType(item);
                    testLineClaimItemNew.setClaimUnit(sampleClaimRelInfoPO.getClaimUnit());
                    testLineClaimItemNew.setClaimValue(sampleClaimRelInfoPO.getClaimValue());
                    testLineClaimItemNew.setUnitId(sampleClaimRelInfoPO.getUnitId());
                }
                finalTestLineClaimInfo.add(testLineClaimItemNew);
            });
        }
        testLineClaimInfo = testLineClaimInfo.stream().sorted(Comparator.comparing(TestLineClaimInfo::getTestAnalyteDesc).thenComparing(TestLineClaimInfo::getSampleSeq)).collect(Collectors.toList());

        return testLineClaimInfo;
    }

    /**
     *
     * @param testLineClaimInfo
     * @param testLineInstanceId
     * @param testLineVersionId
     * @return
     */
    public GetTestLineClaimInfoRsp getAnalyteUnitList(GetTestLineClaimInfoRsp testLineClaimInfo, String testLineInstanceId, Integer testLineVersionId){
        Map<String, List<ClaimAnalyteUnit>> analyteBaseIdMap = Maps.newHashMap();
        if (StringUtils.isBlank(testLineInstanceId)) {
            return testLineClaimInfo;
        }
        if (testLineClaimInfo == null || CollectionUtils.isEmpty(testLineClaimInfo.getClaimInfos())) {
            return testLineClaimInfo;
        }
        List<TestLineAnalyteClaimInfo> claimInfos = testLineClaimInfo.getClaimInfos();
        if (CollectionUtils.isEmpty(claimInfos)) {
            return testLineClaimInfo;
        }

        List<TestLineInstancePO> baseTestLineByIds = testLineMapper.getBaseTestLineByIdsForStyleUntil(Arrays.asList(testLineInstanceId));
        List<Long> testLineBaseIds = baseTestLineByIds.stream().map(TestLineInstancePO::getTestLineBaseId).collect(Collectors.toList());
        Set<Long> citationBaseIds = baseTestLineByIds.stream().map(TestLineInstancePO::getCitationBaseId).collect(Collectors.toSet());
        List<GetTestLineBaseInfoRsp> testLineBaseInfos = testLineClient.getTestLineBaseInfo(testLineBaseIds,LanguageType.EN,null);
        List<GetCitationBaseInfoRsp> citationBaseInfos = citationClient.getCitationBaseInfo(citationBaseIds,Lists.newArrayList(LanguageType.EN.getLanguageId(),LanguageType.CHI.getLanguageId()));
        baseTestLineByIds.forEach(x->{
            Optional<GetTestLineBaseInfoRsp> tlOp = testLineBaseInfos.stream().filter(testLineBaseInfo -> x.getTestLineBaseId().equals(testLineBaseInfo.getTestLineBaseId())).findFirst();
            Optional<GetCitationBaseInfoRsp> ciOp = citationBaseInfos.stream().filter(citationBaseInfo -> x.getCitationBaseId().equals(citationBaseInfo.getCitationBaseId())).findFirst();
            if(tlOp.isPresent()){
                GetTestLineBaseInfoRsp testLineBaseInfo = tlOp.get();
                x.setTestLineVersionID(testLineBaseInfo.getTestLineVersionId());
                x.setTestLineID(testLineBaseInfo.getTestLineId());
            }
            if(ciOp.isPresent()){
                GetCitationBaseInfoRsp citationBaseInfo = ciOp.get();
                x.setCitationVersionId(citationBaseInfo.getCitationVersionId());
                x.setStandardName(citationBaseInfo.getCitationName());
                x.setStandardID(citationBaseInfo.getCitationId());
                x.setStandardSectionID(citationBaseInfo.getCitationSectionId());
                x.setStandardSectionName(citationBaseInfo.getCitationSectionName());
                x.setEvaluationAlias(citationBaseInfo.getEvaluationAlias());
            }
        });


        //查询 analyte  add comment by vincent 2021年11月1
        // modify by vincent 2021年11月1日 后续的处理逻辑代码已删除，如需查看，需看历史版本

        // 获取trimslocal 数据
        QueryTestLineAnalyteReq queryTestLineAnalyteReq = new QueryTestLineAnalyteReq();
        queryTestLineAnalyteReq.setAnalytes(Lists.newArrayList());
        TestLineAnalyteReq testLineAnalyteReq = new TestLineAnalyteReq();
        testLineAnalyteReq.setTestLineVersionId(baseTestLineByIds.get(0).getTestLineVersionID());
        testLineAnalyteReq.setStandardVersionIds(Sets.newHashSet(0));
        queryTestLineAnalyteReq.getAnalytes().add(testLineAnalyteReq);
        List<TestLineAnalyteRsp> trimsAnalyteData = analyteClient.getTestLineAnalyteList(queryTestLineAnalyteReq);
        if(trimsAnalyteData.stream().filter(t->!NumberUtil.equals(testLineVersionId, t.getTestLineVersionId())).count()>0){
            return testLineClaimInfo;
        }
        ClaimAnalyteUnit claimAnalyteUnit;
        for (TestLineAnalyteRsp analyte : trimsAnalyteData) {

            Set<Integer> unitIdSet = Sets.newHashSet();
            List<ClaimAnalyteUnit> analyteUnits = Lists.newArrayList();

            List<TestLineAnalyteUnitRsp> units = analyte.getUnits();
            if (CollectionUtils.isEmpty(units)){
                continue;
            }
            if (!unitIdSet.contains(0)) {
                // 在units中默认显示一个 “-”
                analyteUnits.add(0, new ClaimAnalyteUnit("-", 0));
                unitIdSet.add(0);
            }
            for (TestLineAnalyteUnitRsp unit : units) {
                if (unit == null){
                    continue;
                }
                if (unitIdSet.contains(NumberUtil.toInt(unit.getUnitId()))) {
                    continue;
                }
                String reportUnit = LOStringUtil.delHTMLTag(unit.getUnitShortDepiction());
                claimAnalyteUnit = new ClaimAnalyteUnit();
                claimAnalyteUnit.setId(reportUnit);
                claimAnalyteUnit.setUnitId(NumberUtil.toInt(unit.getUnitId()));
                analyteUnits.add(claimAnalyteUnit);
                unitIdSet.add(NumberUtil.toInt(unit.getUnitId()));
            }
            if (CollectionUtils.isEmpty(analyteUnits)) {
                continue;
            }
            if (analyteBaseIdMap.containsKey(analyte.getTestAnalyteDesc())) {
                analyteUnits.remove(0);
                analyteBaseIdMap.get(analyte.getTestAnalyteDesc()).addAll(analyteUnits);
            } else {
                analyteBaseIdMap.put(analyte.getTestAnalyteDesc(), analyteUnits);
            }

        }
        if (analyteBaseIdMap.isEmpty()) {
            return testLineClaimInfo;
        }

        for (TestLineAnalyteClaimInfo testLineAnalyteClaimInfo : claimInfos) {
            if (CollectionUtils.isEmpty(testLineAnalyteClaimInfo.getSampleClaims())) {
                continue;
            }
            List<TestLineClaimInfo> sampleClaims = testLineAnalyteClaimInfo.getSampleClaims();
            for (TestLineClaimInfo claimInfo : sampleClaims) {
                if (!analyteBaseIdMap.containsKey(claimInfo.getTestAnalyteDesc())) {
                    continue;
                }
                claimInfo.setClaimAnalyteUnits(analyteBaseIdMap.get(claimInfo.getTestAnalyteDesc()));
            }
        }
        return testLineClaimInfo;
    }


    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult saveTestLineClaimInfo(SaveTestLineClaimInfoReq reqObject) {
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(false);
        if(StringUtils.isEmpty(reqObject.getTestLineInstanceId())) {
            rspResult.setMsg("PLEASE CHECK PARAMETER");
            return rspResult;
        }
        String testLineInstanceId = reqObject.getTestLineInstanceId();
        // 判断 是否包含 conclusion
        List<ConclusionInfoPO> conclusionFromTestLine = conclusionMapper.getConclusionFromTestLine(testLineInstanceId);
        if (CollectionUtils.isNotEmpty(conclusionFromTestLine)) {
            rspResult.setMsg("该TestLine 已设置 Conclusion，不能更改Claim！");
            return rspResult;
        }
        List<TestLineAnalyteClaimInfo> claimInfos = reqObject.getClaimInfos();
        if (CollectionUtils.isEmpty(claimInfos)) {
            rspResult.setMsg("PLEASE CHECK PARAMETER");
            return rspResult;
        }

        if (this.checkSubTestLineLock(testLineInstanceId)) {
            rspResult.setSuccess(false);
            rspResult.setMsg(" data locked,Please click unlock button first");
            return rspResult;
        }

        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderByTestLineInstanceId(testLineInstanceId);
        OrderInfoDto order = orderClient.getOrderInfoByOrderNo(orderInfo.getOrderNo());
        if(OperationType.check(order.getOperationType(), OperationType.SubContract)) {
            rspResult.setSuccess(false);
            rspResult.setMsg(" SubContract 单不能更改Claim！");
            return rspResult;
        }

        UserInfo localUser = UserHelper.getLocalUser();
        List<SampleClaimRelInfoPO> sampleClaimRelInfoPOS = Lists.newArrayList();
        for (TestLineAnalyteClaimInfo testLineAnalyteClaimInfo :  claimInfos) {
            if (testLineAnalyteClaimInfo == null ||
                    CollectionUtils.isEmpty(testLineAnalyteClaimInfo.getSampleClaims())) {
                continue;
            }
            List<TestLineClaimInfo> sampleClaims = testLineAnalyteClaimInfo.getSampleClaims();
            for (TestLineClaimInfo claimInfo: sampleClaims) {
                if (StringUtils.isEmpty(claimInfo.getClaimValue()) || claimInfo.getUnitId() == null) {
                    rspResult.setMsg("请填写所有的Claim值！");
                    return rspResult;
                }
                if (StringUtils.equalsIgnoreCase(claimInfo.getClaimValue(), "-") && !NumberUtil.equals(claimInfo.getUnitId(), 0) ||
                        (!StringUtils.equalsIgnoreCase(claimInfo.getClaimValue(), "-") && NumberUtil.equals(claimInfo.getUnitId(), 0))) {
                    rspResult.setMsg("请填写所有的Claim值！");
                    return rspResult;
                }
                SampleClaimRelInfoPO sampleClaimRelInfoPO = new SampleClaimRelInfoPO();
                sampleClaimRelInfoPO.setId(NumberUtil.toLong(claimInfo.getSampleClaimId()));
                sampleClaimRelInfoPO.setSampleId(claimInfo.getTestSampleID());
                sampleClaimRelInfoPO.setClaimType(claimInfo.getTestAnalyteDesc());
                sampleClaimRelInfoPO.setClaimValue(claimInfo.getClaimValue());
                sampleClaimRelInfoPO.setUnitId(claimInfo.getUnitId());
                List<ClaimAnalyteUnit> claimAnalyteUnits = claimInfo.getClaimAnalyteUnits();
                for (ClaimAnalyteUnit analyteUnit : claimAnalyteUnits) {
                    if (NumberUtil.equals(claimInfo.getUnitId(), analyteUnit.getUnitId())) {
                        sampleClaimRelInfoPO.setClaimUnit(analyteUnit.getId());
                        continue;
                    }
                }
                sampleClaimRelInfoPO.setCreatedBy(localUser.getRegionAccount());
                sampleClaimRelInfoPO.setCreatedDate(DateUtils.getNow());
                sampleClaimRelInfoPO.setModifiedBy(localUser.getRegionAccount());
                sampleClaimRelInfoPO.setModifiedDate(DateUtils.getNow());
                sampleClaimRelInfoPOS.add(sampleClaimRelInfoPO);
            }
        }

        rspResult.setSuccess(sampleClaimRelExtMapper.batchInsertAndUpdate(sampleClaimRelInfoPOS) > 0);

        return rspResult;
    }

    /**
     * 判断TestLine 是否是分包TestLine 并且订单是否锁定
     * @param testLineInstanceId
     * @return
     */
    private boolean checkSubTestLineLock(String testLineInstanceId) {
        //校验当前订单是否是内部分包被锁定订单
        List<SubcontractDTO> subcontractDTOS = subContractExtMapper.querySubContractListByTLIds(Lists.newArrayList(testLineInstanceId));
        if(CollectionUtils.isEmpty(subcontractDTOS)){
            return false;
        }
        //有做分包
        SubcontractDTO subcontractDTO = subcontractDTOS.get(0);
        String subContractNo = subcontractDTO.getSubContractNo();
        SubContractInfo subContractInfo = subContractExtMapper.getSubContractInfo(subContractNo);
        if(subContractInfo == null){
            return false;
        }
        Integer subContractOrder = subContractInfo.getSubContractOrder();
        //说明是内部分包，需要进行校验
        if (!SubContractType.check(subContractOrder, SubContractType.SubContract)) {
            return false;
        }
        Integer dataLock = subContractInfo.getDataLock();
        return SubContractDataLockEnums.check(dataLock, SubContractDataLockEnums.lock);
    }

    public boolean saveTestLineList(SaveTestLineListReq reqObject, CustomResult rspResult) {
        return this.saveTestLineList(reqObject, rspResult, Boolean.TRUE);
    }

    private List<Integer> needCheckTestLineStatus(Boolean isNeedCheckComplete) {
        List<Integer> testLineStatusList = Lists.newArrayList(TestLineStatus.Typing.getStatus(),
                TestLineStatus.Entered.getStatus(), TestLineStatus.Submit.getStatus(), TestLineStatus.NC.getStatus(), TestLineStatus.DR.getStatus());
        if (isNeedCheckComplete) {
            testLineStatusList.add(TestLineStatus.Completed.getStatus());
        }
        return testLineStatusList;
    }

    public void checkTestLineMappingExists(SaveTestLineListReq reqObject, CustomResult rspResult, GeneralOrderInstanceInfoPO order){
        // DIG-8555 把判断提上来做 不然order.getOrderNo() 可能会空指针
        if(Objects.isNull(order)){
            rspResult.setMsg("订单数据为空");
            return ;
        }

        OrderTrfRelationshipDTO shipDto = orderClient.getOrderTrfRelationshipByOrderNo(order.getOrderNo(), ProductLineContextHolder.getProductLineCode());
        if(Objects.isNull(shipDto) || !RefSystemIdEnum.check(shipDto.getReferenceId(),RefSystemIdEnum.Shein,RefSystemIdEnum.SheinSupplier,RefSystemIdEnum.UNIQLO)){
            rspResult.setSuccess(true);
            return ;
        }

        if(Objects.isNull(reqObject) || CollectionUtils.isEmpty(reqObject.getTestLines())){
            rspResult.fail("testLine参数为空");
            return ;
        }

        List<SaveTestLineReq> testLineReqs = reqObject.getTestLines();
        Set<Integer> ppVersionIds = testLineReqs.stream().filter(saveTestLineReq -> NumberUtil.toInt(saveTestLineReq.getPpVersionId()) > 0).map(SaveTestLineReq::getPpVersionId).collect(Collectors.toSet());
        Map<Integer,GetPpBaseInfoRsp> ppNoMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(ppVersionIds)){
            //查询ppNO
            List<GetPpBaseInfoRsp> baseInfoRsps = ppClient.getPpBaseInfoByPpVersionIds(ppVersionIds);
            if(CollectionUtils.isNotEmpty(baseInfoRsps)){
                ppNoMap = baseInfoRsps.stream().collect(Collectors.toMap(GetPpBaseInfoRsp::getPpVersionId,Function.identity(),(o1,o2)->o1));
            }
        }
        List<Long> testLineBaseIds = testLineReqs.stream().map(SaveTestLineReq::getTestLineBaseId).collect(Collectors.toList());
        Set<Long> pretreatmentTLBaseIds = Sets.newHashSet();
        if (CollectionUtil.isNotEmpty(testLineBaseIds)) {
            List<GetTestLineBaseInfoRsp> testLineBaseInfo = testLineClient.getTestLineBaseInfo(testLineBaseIds, LanguageType.EN, null);
            pretreatmentTLBaseIds = testLineBaseInfo.stream()
                    .filter(item -> NumberUtil.equals(item.getTestLineType(), 1))
                    .map(GetTestLineBaseInfoRsp::getTestLineBaseId).collect(Collectors.toSet());
        }
        Set<Long> finalPretreatmentTLBaseIds = pretreatmentTLBaseIds;
        testLineReqs = testLineReqs.stream().filter(item -> !finalPretreatmentTLBaseIds.contains(item.getTestLineBaseId())).collect(Collectors.toList());
        // 排除掉  Pretreatment TestLine 后，如果不存在 则不需要校验mapping 关系
        if (testLineReqs.isEmpty()) {
            rspResult.setSuccess(true);
            return;
        }

        Set<Long> citationBaseIds = testLineReqs.stream().map(saveTestLineReq -> Long.valueOf(saveTestLineReq.getCitationBaseId())).collect(Collectors.toSet());
        List<GetCitationBaseInfoRsp> citationList = citationClient.getCitationBaseInfo(citationBaseIds,null,null);
        Map<Long,GetCitationBaseInfoRsp> citationMap = citationList.stream().collect(Collectors.toMap(GetCitationBaseInfoRsp::getCitationBaseId,Function.identity(),(o1,o2)->o1));
        CheckTestLineMappingReq checkTestLineMappingReq = new CheckTestLineMappingReq();
        List<MappingTestLineReq> testLines = Lists.newArrayList();
        for (SaveTestLineReq x : testLineReqs){
            MappingTestLineReq mappingTestLineReq = new MappingTestLineReq();
            if(Objects.nonNull(ppNoMap.get(x.getPpVersionId()))){
                mappingTestLineReq.setPpNo(ppNoMap.get(x.getPpVersionId()).getPpNo());
            }
            mappingTestLineReq.setTestLineId(x.getTestLineId());
            GetCitationBaseInfoRsp baseInfoRsp = citationMap.get(NumberUtil.toLong(x.getCitationBaseId()));
            if(Objects.isNull(baseInfoRsp)){
                continue;
            }
            mappingTestLineReq.setCitationId(baseInfoRsp.getCitationId());
            mappingTestLineReq.setCitationType(baseInfoRsp.getCitationType());
            testLines.add(mappingTestLineReq);
        }
        String refName = RefSystemIdEnum.getName(shipDto.getReferenceId());

        checkTestLineMappingReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        checkTestLineMappingReq.setCustomerGroupCode(order.getCustomerGroupCode());
        checkTestLineMappingReq.setTestLines(testLines);
        checkTestLineMappingReq.setRefSystemId(shipDto.getReferenceId());
        checkTestLineMappingReq.setProductLineCode(StringUtils.defaultString(ProductLineContextHolder.getProductLineCode(), ProductLineType.SL.getProductLineAbbr()));
        List<CheckTestLineMappingRsp> mappingRsps = extCustomerConfigClient.checkTestLineMapping(checkTestLineMappingReq);

        if (CollectionUtils.isEmpty(mappingRsps)) {
            rspResult.fail(String.format("%s的Mapping关系,获取失败", refName));
            return;
        }
        List<CheckTestLineMappingRsp> collect = mappingRsps.stream().filter(item -> StringUtils.isBlank(item.getItemCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            rspResult.fail(String.format("提示PP(%s)下TL(%s)（CitationID: %s, CitationType: %s）没有建立%s的Mapping关系，无法保存！",
                    collect.get(0).getPpNo(),
                    collect.get(0).getTestLineId(),
                    collect.get(0).getCitationId(),
                    collect.get(0).getCitationType(),
                    refName));
            return;
        }

        rspResult.setSuccess(true);
    }

    /**
     *
     * @param reqObject
     * @param rspResult
     * @param isNeedCheckComplete  是否需要判断 testLineComplete
     * @return
     */
    public boolean saveTestLineList(SaveTestLineListReq reqObject, CustomResult rspResult, Boolean isNeedCheckComplete) {
        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());
        if (order == null){
            rspResult.setMsg("Order is  null.");
            return false;
        }
        List<SaveTestLineReq> testLines = reqObject.getTestLines();
        if(testLines == null || testLines.isEmpty()){
            rspResult.setMsg("SaveTestLineReqs is  Empty.");
            return false;
        }
        // ppTestLineType check
        if(CollectionUtils.isEmpty(testLines.stream().filter(testLine->ArtifactType.check(testLine.getArtifactType())).collect(Collectors.toList()))){
            rspResult.setMsg("ppTestLineType Illegal parameter");
            return false;
        }

        //如果是转单的，只能host lab 或者top lab操作
        //转单的话，用转单的labId
        String labCode = order.getLabCode();
        Integer laboratoryID = order.getOrderLaboratoryID();
        CustomResult<UserLabBuInfo> checkRspResult = this.checkAndGetLabId(order.getOrderNo(),labCode);
        if(!checkRspResult.isSuccess()){
            return false;
        }
        UserLabBuInfo data = checkRspResult.getData();
        if(data!=null && NumberUtil.toInt(data.getLabId())>0){
            laboratoryID = Math.toIntExact(data.getLabId());
        }

        SaveTestLineReq testLineReq = testLines.get(0);
        Boolean isAddPPTL = NumberUtil.toLong(testLineReq.getPpVersionId()) > 0 ? true :false;

        List<Integer> testLineStatusList = needCheckTestLineStatus(isNeedCheckComplete);
        // 验证添加的TestLine 是否已经在其他层级的Order中添加过
        SubContractOrderInfoDTO contractOrderInfo = new SubContractOrderInfoDTO();
        CustomResult<List<SaveTestLineReq>> checkResult = subContractOperateService.checkAddTestLineForContract(reqObject.getOrderId(), reqObject.getTestLines(), contractOrderInfo, testLineStatusList);
        rspResult.setMsg(checkResult.getMsg());
        if (!checkResult.isSuccess()) {
            return false;
        }
        if (CollectionUtils.isEmpty(checkResult.getData())) {
            return true;
        }
        reqObject.getTestLines().clear();
        reqObject.setTestLines(checkResult.getData());

        testLines = reqObject.getTestLines();

        // DIG-9415 切换localTrims接口
//        List<Long> invalidLangIdList =testLineBaseMapper.getInvalidLangList(order.getID());
        List<Long> invalidLangIdList = testLineRepository.getInvalidLangList(order.getID());


        // DIG-6677 getTestLineByOrderId接口开发
        List<TestLineInstancePO> oldTestLines = trimsLocalTestLineService.getTestLineByOrderId(reqObject.getOrderId());
        List<PPTestLineRelationshipInfoPO> ppTestLineRelList = ppTestLineRelMapper.getPPTestLineRelList(order.getOrderNo());

        // DIG-7618 对接 trimslocal 获取相关Citation数据信息
        GetTestLineCitationInfoRsp testLineCitationInfoRsp = this.buildTrimsCitationInfo(testLines, laboratoryID);
        if (testLineCitationInfoRsp == null || CollectionUtils.isEmpty(testLineCitationInfoRsp.getTestLines())) {
            rspResult.setMsg("获取TestLine相关信息失败，请检查数据");
            return false;
        }

        testLines.stream().filter(item -> NumberUtil.toInt(item.getPpVersionId()) == 0)
                .forEach(item -> {
                    item.setTestLineBaseId(item.getArtifactBaseId());
                });
        this.checkTestLineMappingExists(reqObject,rspResult,order);
        if(!rspResult.isSuccess()){
            // POSL-5045 修改希音保存时的mapping校验
            return false;
        }
        List<TestLineCitationRsp> testLienCitationInfos = testLineCitationInfoRsp.getTestLines();

        Map<String, TestLineInstancePO> testLineMaps = this.getSavedTestLineInstanceList(oldTestLines, testLienCitationInfos);
        List<OrderLanguageRelInfoPO> orderLanguageRelInfoDbList = this.getOrderLanguageRelInfoPOS(reqObject.getOrderId());

        List<TestLineInstancePO> testLineInstancePOListSaved= Lists.newArrayList();
        List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOListSaved= Lists.newArrayList();

        Set<Long> ppBaseIds = Sets.newHashSet();
        List<Long> citationBaseIds = Lists.newArrayList();
        UserInfo localUser = UserHelper.getLocalUser();
        // 获取 preOrder 中的订单信息
        Set<Long> testLineBaseIds = Sets.newHashSet();
        //DIG-6717 获取处理不能合并的tlID
        List<TestLineJobSubcontractDTO> canMergeTestLineIdList = this.canMergeTestLine(reqObject, isNeedCheckComplete);
        // DIG-8553 记录循环中已处理的数据key 判断是否需要合并
        List<String> mergeKeyList = Lists.newArrayList();
        for(SaveTestLineReq saveTestLineReq : reqObject.getTestLines()){
            // search testlinebase
            TestLineCitationRsp testLineStandard = this.getTestLineStandarBaseByArtifactBaseId(testLienCitationInfos, saveTestLineReq, isAddPPTL);
            if(testLineStandard == null){
                rspResult.setMsg("TestLine is not found.");
                return false;
            }
            //search testline citation base
            CitationBaseInfoRsp citation = null;
            if(CollectionUtils.isNotEmpty(testLineStandard.getCitations())){
                citation= testLineStandard.getCitations()
                        .stream()
                        .filter(c -> NumberUtil.equals(c.getCitationId(), saveTestLineReq.getStandardId()))
                        .filter(c->NumberUtil.equals(c.getCitationVersionId(),saveTestLineReq.getCitationVersionId()))
                        .findFirst()
                        .orElse(null);
            }
            if(citation==null||citation.getCitationId()==null){
                rspResult.setMsg("TestLine CitationBase is not find");
                logger.info("citationBase is not find,TestLineBaseId="+testLineStandard.getTestLineBaseId()+" TestLineId="+testLineStandard.getTestLineId());
                return false;
            }
            long testLineBaseId = NumberUtil.toLong(testLineStandard.getTestLineBaseId());
            if (testLineBaseId > 0){
                testLineBaseIds.add(testLineBaseId);
            }
            citationBaseIds.add(citation.getCitationBaseId());

            String key = String.format("%s_%s_%s_%s", testLineStandard.getTestLineId(), citation.getCitationId(), citation.getCitationVersionId(), citation.getCitationSectionId());
            TestLineInstancePO orderTestLine = testLineMaps.get(key);

            //判断是否可以mergeTestline
            // DIG-4947  PreOrderStatus = New（1）和Confirm（3）时合并
            //if(orderTestLine == null || !PreOrderStatus.checkStatus(orderStatus, PreOrderStatus.New, PreOrderStatus.Confirmed))
            //DIG-6717 不需要preorder status的判断了 orderTestLine==null 是需要新增的
            List<TestLineJobSubcontractDTO> cannotMergeTestLines = canMergeTestLineIdList.stream()
                    .filter(item -> NumberUtil.equals(item.getTestLineId(), saveTestLineReq.getTestLineId()) &&
                            NumberUtil.equals(item.getCitationBaseId(), saveTestLineReq.getCitationBaseId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(cannotMergeTestLines) || orderTestLine == null) {

                // DIG-8553 数据库里没有重复的TL 准备新增时，通过key判断循环中是否还有相同的TL，有的则跳过新增的逻辑
                String mergeKey = saveTestLineReq.getTestLineId().toString() + saveTestLineReq.getCitationBaseId().toString();
                if (!mergeKeyList.contains(mergeKey) || Objects.isNull(orderTestLine)) {

                    //build new TestlineInstance 这里是 不 进行merge的处理
                    orderTestLine = buildTestLineInstancePO(testLineStandard, citation,
                            testLineStandard.getHasWorkingInstruction() == null ? false : testLineStandard.getHasWorkingInstruction(), isNeedCheckComplete, order);
                    testLineMaps.put(key, orderTestLine);

                    mergeKeyList.add(mergeKey);
                }
            }
            orderTestLine.setModifiedDate(DateUtils.getNow());
            orderTestLine.setPendingFlag(false);
            orderTestLine.setModifiedBy(localUser.getRegionAccount());
            orderTestLine.setCalculateConclusionFlag(ConclusionFlag.Calculate.getFlag());
            orderTestLine.setStyleVersionId(0);
            testLineInstancePOListSaved.add(orderTestLine);
            //append ppTestlineRelationship

            Long ppBaseId = appendPpTestLineRelationshipInfoPOS(reqObject.getOrderId(),
                    ppTestLineRelationshipInfoPOListSaved,
                    orderTestLine,
                    testLineStandard,
                    saveTestLineReq.getRootPpBaseId());
            if (ppBaseId == null || ppBaseId.longValue() <= 0){
                continue;
            }
            ppBaseIds.add(ppBaseId);
        }

        // set dr
        CustomResult customResult = this.setDrFlag(testLienCitationInfos, testLineInstancePOListSaved, oldTestLines, ppTestLineRelList, order.getOrderNo());
        if (!customResult.isSuccess()) {
            rspResult.setMsg(customResult.getMsg());
            return false;
        }
        // 多语言处理
        Map<Long, Long> testLineBaseLangMap = Maps.newHashMap();
        Map<Long, Long> ppArtifactRelLangMap = Maps.newHashMap();
        Map<Long, Long> citationLangMap = Maps.newHashMap();
        Map<Long, Long> ppLangMap = Maps.newHashMap();
        Map<Long, Long> ppSectionLangMap = Maps.newHashMap();
        this.buildLangMaps(testLineCitationInfoRsp, testLineBaseLangMap, ppArtifactRelLangMap, citationLangMap, ppLangMap, ppSectionLangMap);

        //======append language
        List<OrderLanguageRelInfoPO> orderLangRels = Lists.newArrayList();
        //append pp testline specialized languageSL521052200093FW
        if(isAddPPTL){
            List<Long> ppArtifactRelIds = testLienCitationInfos.stream().map(testLineStandarBase->testLineStandarBase.getPpArtifactRelId()).collect(Collectors.toList());
            //append pptestline language
            appendPpTestlineLanguageRelInfoPOS(reqObject.getOrderId(), orderLanguageRelInfoDbList,orderLangRels, ppArtifactRelIds, ppArtifactRelLangMap);
            //append pp language
            appendPpLanguageRelInfoPOS(reqObject.getOrderId(), orderLanguageRelInfoDbList, orderLangRels, ppLangMap);
            //append pp section language
            appendPpSectionLanguageRelInfoPOS(reqObject.getOrderId(), orderLanguageRelInfoDbList, orderLangRels, ppSectionLangMap);

            protocolPackageTestLineRelationshipService.buildSubPpSeq(ppTestLineRelationshipInfoPOListSaved);

        }
        // append testline Language
        appendTestlineLanguageRelInfoPOS(reqObject.getOrderId(), orderLanguageRelInfoDbList,orderLangRels, testLineBaseLangMap);
        // append testline citation language
        appendTestlineCitationLanguageRelInfoPOS(reqObject.getOrderId(), orderLanguageRelInfoDbList, orderLangRels, citationLangMap);


        List<SubcontractRelInfoPO> subcontractRels = Lists.newArrayList();
        // 如果当前单是新版内部分包子单，则需要将记录写到 Relationship 中
        if (contractOrderInfo != null && StringUtils.isNotBlank(contractOrderInfo.getOriginalOrderId())) {
            this.appendSubTestLinRelInfoPOS(contractOrderInfo.getOriginalOrderId(), order.getID(), subcontractRels, testLineInstancePOListSaved, ppTestLineRelationshipInfoPOListSaved);
        }
        ReportInfoPO reportInfoPO = reportMapper.getReportByOrderNo(order.getOrderNo());

        boolean isSuccess = transactionTemplate.execute((trans) -> {
            // insert testlineinstance
            if(CollectionUtils.isNotEmpty(testLineInstancePOListSaved)){
                testLineMapper.batchInsertForAddTl(testLineInstancePOListSaved);
            }
            // insert ppTestLineRel
            if(CollectionUtils.isNotEmpty(ppTestLineRelationshipInfoPOListSaved)){
                ppTestLineRelMapper.batchInsert(ppTestLineRelationshipInfoPOListSaved);
            }

            // delete LangStatus=0
            if(CollectionUtils.isNotEmpty(invalidLangIdList)){
                testLineBaseMapper.deleteInvalidLangList(invalidLangIdList);
            }
            //
            orderCitationRelService.batchInsert(order.getID(), ppBaseIds, orderLangRels);

            // insert language
            if(CollectionUtils.isNotEmpty(orderLangRels)){
                orderLangRelService.batchInsertOrUpdate(order.getID(), orderLangRels);
            }

            // insert tre_order_subcontract_relationship_mapping
            if (CollectionUtils.isNotEmpty(subcontractRels)) {
                subcontractRelMapper.batchInsert(subcontractRels);
            }

            //DIG-7875
            if(Objects.nonNull(reportInfoPO)){
                ReportInfoPO report = new ReportInfoPO();
                report.setReportNo(reportInfoPO.getReportNo());
                report.setRecalculationFlag(2);
                report.setModifiedBy(localUser.getRegionAccount());
                report.setModifiedDate(DateUtils.getNow());
                reportMapper.updateReportRecalculationFlag(report);
            }

            SysStatusReq reqStatus = new SysStatusReq();
            reqStatus.setObjectNo(order.getOrderNo());
            reqStatus.setOldStatus(PreOrderStatus.Reporting.getStatus());
            reqStatus.setNewStatus(PreOrderStatus.Testing.getStatus());
            reqStatus.setUserName(localUser.getRegionAccount());
            statusClient.insertStatusInfo(reqStatus);
            return true;
        });

        return isSuccess;
    }

    /** 判断当前tl是否需要进行合并
     * TL所在Job/Subcontract 为New，添加不同Pp的相同TL，TL应该合并
     TL所在Job/Subcontract 为Testing，添加不同Pp的相同TL，TL应该不合并
     TL所在Job/Subcontract 为Completed，添加不同Pp的相同TL，TL应该不合并
     TL所在Job/Subcontract 为Cancel，添加不同Pp的相同TL，TL应该不合并
     TL所在Job/Subcontract 为New，TL不为Typing，添加不同Pp的相同TL，TL应该不合并
     @Return list 能进行merge的tl
     */
    private List<TestLineJobSubcontractDTO> canMergeTestLine(SaveTestLineListReq reqObject, Boolean isNeedCheckComplete){
        List<TestLineJobSubcontractDTO> result = Lists.newArrayList();
        List<SaveTestLineReq> testLines = reqObject.getTestLines();
        //再进一步判断job/subcontract 的状态，判断是否可以合并
        List<Integer> reqTLIds = testLines.stream().map(SaveTestLineReq::getTestLineId).distinct().collect(Collectors.toList());
        List<Long> citationBaseIds = testLines.stream().map(SaveTestLineReq::getCitationBaseId).distinct().collect(Collectors.toList());
        TestLineJobSubcontractReqDTO reqDTO = new TestLineJobSubcontractReqDTO();
        reqDTO.setOrderId(reqObject.getOrderId());
        reqDTO.setTestLineIds(reqTLIds);
        reqDTO.setCitationBaseIds(citationBaseIds);
//        reqDTO.setNeedCheckComplete(isNeedCheckComplete);
        List<TestLineJobSubcontractDTO> dtoList = testLineMapper.getTestLineJobSubcontractStatusByOrderID(reqDTO);

        if(CollectionUtils.isEmpty(dtoList)){
            return result;
        }

        //只有testLine 为typeing 且 job 、subcontractstatus 都为new 才能合并，其它清空不能合并
        //过滤出来看是否是非new状态 ，是不予许继续合并的
        List<TestLineJobSubcontractDTO> needMergeStatus = dtoList.stream().filter(dto ->{
            // DIG-7431 合并处理 添加  Document Review
            boolean isNotTyping = !TestLineStatus.check(dto.getTestLineStatus(), TestLineStatus.Typing, TestLineStatus.DR);
            if(isNotTyping) {
                return false;
            }
            boolean jobOrSubIsNew = true;
            if(NumberUtil.toInt(dto.getJobStatus())!=0){
                boolean jobIsNew = JobStatus.check(dto.getJobStatus(), JobStatus.New);
                jobOrSubIsNew =  jobOrSubIsNew && jobIsNew;
            }
            if(dto.getSubStatus()!=null){
                boolean subIsNew = SubContractStatusEnum.check(dto.getSubStatus(), SubContractStatusEnum.Created);
                jobOrSubIsNew =  jobOrSubIsNew || subIsNew;
            }
            return jobOrSubIsNew;
        })
        .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(needMergeStatus)){
//            return notNewStatus.stream().map(TestLineJobSubcontractDTO::getTestLineId).distinct().collect(Collectors.toList());
            return needMergeStatus;
        }

        return result;
    }


    /**
     * 请求trims Citation 相关数据
     * @param testLines
     */
    public GetTestLineCitationInfoRsp buildTrimsCitationInfo(List<SaveTestLineReq> testLines, Integer labId) {
        // 对接新的 getTestLineCitationInfoList
        List<TestLineCitationReq> testLineCitationReqs = Lists.newArrayList();
        List<Long> testLineArtifactBaseIds = Lists.newArrayList();
        List<Long> testLineCitationBaseIds = Lists.newArrayList();
        List<Long> ppTLArtifactBaseIds = Lists.newArrayList();
        List<Long> ppTLCitationBaseIds = Lists.newArrayList();

        testLines.stream().filter(item -> NumberUtil.toInt(item.getPpVersionId()) == 0)
                .forEach(item -> {
                    testLineArtifactBaseIds.add(item.getArtifactBaseId());
                    testLineCitationBaseIds.add(item.getCitationBaseId());
                });
        if (CollectionUtils.isNotEmpty(testLineArtifactBaseIds)) {
            TestLineCitationReq testLineCitationReq = new TestLineCitationReq();
            testLineCitationReq.setArtifactBaseIds(testLineArtifactBaseIds);
            testLineCitationReq.setCitationBaseIds(testLineCitationBaseIds);
            testLineCitationReq.setArtifactType(ArtifactType.TestLine.getType());
            testLineCitationReqs.add(testLineCitationReq);
        }
        AtomicReference<Long> rootPpBaseId = new AtomicReference<>(0L);
        testLines.stream().filter(item -> NumberUtil.toInt(item.getPpVersionId()) > 0)
                .forEach(item -> {
                    rootPpBaseId.set(item.getRootPpBaseId());
                    ppTLArtifactBaseIds.add(item.getArtifactBaseId());
                    ppTLCitationBaseIds.add(item.getCitationBaseId());
                });
        if (CollectionUtils.isNotEmpty(ppTLArtifactBaseIds)) {
            TestLineCitationReq testLineCitationReq = new TestLineCitationReq();
            testLineCitationReq.setArtifactBaseIds(ppTLArtifactBaseIds);
            testLineCitationReq.setCitationBaseIds(ppTLCitationBaseIds);
            testLineCitationReq.setArtifactType(ArtifactType.PP.getType());
            testLineCitationReq.setRootPpBaseId(rootPpBaseId.get());
            testLineCitationReqs.add(testLineCitationReq);
        }
        GetTestLineCitationInfoRsp testLineCitationInfoList = testLineClient.getTestLineCitationInfoList(testLineCitationReqs, labId);

        return testLineCitationInfoList;
    }

    /**
     * 保存 多语言数据
     * @param testLineCitationInfoRsps
     * @param testLineBaseLangMap
     * @param ppArtifactRelLangMap
     * @param citationLangMap
     * @param ppLangMap
     * @param ppSectionLangMap
     */
    private void buildLangMaps(GetTestLineCitationInfoRsp testLineCitationInfoRsps, Map<Long, Long> testLineBaseLangMap, Map<Long, Long> ppArtifactRelLangMap
            , Map<Long, Long> citationLangMap, Map<Long, Long> ppLangMap, Map<Long, Long> ppSectionLangMap) {

        for (TestLineCitationRsp testLineCitationRsp : testLineCitationInfoRsps.getTestLines()) {
            if (CollectionUtils.isNotEmpty(testLineCitationRsp.getTestLineLangs())) {
                testLineCitationRsp.getTestLineLangs().forEach(item -> {
                    testLineBaseLangMap.put(testLineCitationRsp.getTestLineBaseId(), item.getTestLineLangBaseId());
                });
            }
            if (CollectionUtils.isNotEmpty(testLineCitationRsp.getPpArtifactLangs())) {
                testLineCitationRsp.getPpArtifactLangs().forEach(item -> {
                    ppArtifactRelLangMap.put(testLineCitationRsp.getPpArtifactRelId(), item.getPpArtifactLangBaseId());
                });
            }
            if (CollectionUtils.isNotEmpty(testLineCitationRsp.getCitations())) {
                for (CitationBaseInfoRsp citationBaseInfoRsp : testLineCitationRsp.getCitations()) {
                    if (CollectionUtils.isEmpty(citationBaseInfoRsp.getCitationLangs())) {
                        continue;
                    }
                    citationBaseInfoRsp.getCitationLangs().stream().forEach(item -> {
                        citationLangMap.put(citationBaseInfoRsp.getCitationBaseId(), item.getCitationLangBaseId());
                    });
                }
            }
        }
        if (CollectionUtils.isNotEmpty(testLineCitationInfoRsps.getPpLangs())) {
            testLineCitationInfoRsps.getPpLangs().forEach(item -> {
                ppLangMap.put(item.getPpBaseId(), item.getPpLangBaseId());
            });
        }
        if (CollectionUtils.isNotEmpty(testLineCitationInfoRsps.getPpSections())) {
            testLineCitationInfoRsps.getPpSections().forEach(item -> {
                if (CollectionUtils.isEmpty(item.getPpSectionLangs())) {
                    return;
                }
                item.getPpSectionLangs().forEach(lang -> {
                    ppSectionLangMap.put(item.getSectionBaseId(), lang.getPpSectionLangBaseId());
                });
            });
        }
    }



    public TestLineCitationRsp getTestLineStandarBaseByArtifactBaseId(List<TestLineCitationRsp> testLineStandards, SaveTestLineReq reqObject, Boolean isAddPPTL) {
        ArtifactType artifactType = isAddPPTL ? ArtifactType.PP : ArtifactType.TestLine;

        ArtifactType finalArtifactType = artifactType;
        return testLineStandards.stream().filter(testLineStandard->
            (
                finalArtifactType == ArtifactType.TestLine &&
                NumberUtil.equals(testLineStandard.getTestLineBaseId(), reqObject.getArtifactBaseId())
            ) ||
            (
                finalArtifactType == ArtifactType.PP &&
                NumberUtil.equals(testLineStandard.getPpArtifactRelId(), reqObject.getArtifactBaseId())
            )
        ).findFirst().orElse(null);
    }

    private Long appendPpTestLineRelationshipInfoPOS(String orderId, List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOListSaved,
                                                     TestLineInstancePO objTestLineInstancePO, TestLineCitationRsp objTestLineStandarBase, Long rootPpBaseId) {
        PPTestLineRelationshipInfoPO ppTestLineRelationship = this.buildPpTestLineRelationshipInfoPO(orderId, objTestLineInstancePO, objTestLineStandarBase, rootPpBaseId);
        ppTestLineRelationshipInfoPOListSaved.add(ppTestLineRelationship);
        return ppTestLineRelationship.getPpBaseId();
    }


    private void appendTestlineCitationLanguageRelInfoPOS(String orderId, List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOsExisted,
                                                          List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOSSaved, Map<Long, Long> citationLangMap) {

        if (CollectionUtils.isEmpty(citationLangMap.keySet())) {
            return;
        }
        for (Long citationBaseId : citationLangMap.keySet()){
            if (orderLanguageRelInfoPOsExisted.stream()
                    .filter(item -> NumberUtil.equals(item.getLangBaseId(), citationLangMap.get(citationBaseId))
                            && LangTypeEnum.check(item.getLangType(), LangTypeEnum.Citation)).collect(Collectors.toSet()).size() > 0) {
                continue;
            }
            //build new OrderLanguageRelInfoPO
            orderLanguageRelInfoPOSSaved.add(buildOrderLanguageRelIInfo(citationBaseId, LanguageType.CHI.getLanguageId(), citationLangMap.get(citationBaseId), orderId, LangTypeEnum.Citation.getType()));
        }
    }

    private void appendPpTestlineLanguageRelInfoPOS(String orderId, List<OrderLanguageRelInfoPO> orderLanguageRelInfoDbList,
                                                    List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOSSaved, List<Long> ppArtifactRelIds, Map<Long, Long> ppArtifactRelLangMap) {
        if (CollectionUtils.isEmpty(ppArtifactRelLangMap.keySet())) {
            return;
        }
        for (Long ppArtifactRelId : ppArtifactRelLangMap.keySet()) {
            if (orderLanguageRelInfoDbList.stream()
                    .filter(item -> NumberUtil.equals(item.getLangBaseId(), ppArtifactRelLangMap.get(ppArtifactRelId))
                            && LangTypeEnum.check(item.getLangType(), LangTypeEnum.PpArtifactRel)).collect(Collectors.toSet()).size() > 0) {
                continue;
            }
            //build new OrderLanguageRelInfoPO
            orderLanguageRelInfoPOSSaved.add(buildOrderLanguageRelIInfo(ppArtifactRelId, LanguageType.CHI.getLanguageId(), ppArtifactRelLangMap.get(ppArtifactRelId), orderId, LangTypeEnum.PpArtifactRel.getType()));
        }
    }

    private void appendTestlineLanguageRelInfoPOS(String orderId, List<OrderLanguageRelInfoPO> orderLanguageRelInfoDbList,
                                                  List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOSSaved, Map<Long, Long> testLineBaseLangMap) {
        if (CollectionUtils.isEmpty(testLineBaseLangMap.keySet())) {
            return;
        }
        for (Long testLineBaseId : testLineBaseLangMap.keySet()){
            if (orderLanguageRelInfoDbList.stream()
                    .filter(item -> NumberUtil.equals(item.getLangBaseId(), testLineBaseLangMap.get(testLineBaseId))
                            && LangTypeEnum.check(item.getLangType(), LangTypeEnum.TestLine)).collect(Collectors.toSet()).size() > 0) {
                continue;
            }
            orderLanguageRelInfoPOSSaved.add(buildOrderLanguageRelIInfo(testLineBaseId, LanguageType.CHI.getLanguageId(), testLineBaseLangMap.get(testLineBaseId),orderId,LangTypeEnum.TestLine.getType()));
        }
    }

    private void appendPpLanguageRelInfoPOS(String orderId, List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOS_existed,
                                            List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOSSaved, Map<Long, Long> ppLangMap) {

        if (CollectionUtils.isEmpty(ppLangMap.keySet())) {
            return;
        }
        for (Long ppBaseId : ppLangMap.keySet()){
            if (orderLanguageRelInfoPOS_existed.stream()
                    .filter(item -> NumberUtil.equals(item.getLangBaseId(), ppLangMap.get(ppBaseId))
                            && LangTypeEnum.check(item.getLangType(), LangTypeEnum.PP)).collect(Collectors.toSet()).size() > 0) {
                continue;
            }
            orderLanguageRelInfoPOSSaved.add(buildOrderLanguageRelIInfo(ppBaseId, LanguageType.CHI.getLanguageId(), ppLangMap.get(ppBaseId), orderId, LangTypeEnum.PP.getType()));
        }
    }

    private void appendPpSectionLanguageRelInfoPOS(String orderId, List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOS_existed,
                                                   List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOSSaved, Map<Long, Long> ppSectionLangMap) {
        if (CollectionUtils.isEmpty(ppSectionLangMap.keySet())) {
            return;
        }
        for (Long ppSectionBaseId : ppSectionLangMap.keySet() ){
            if (orderLanguageRelInfoPOS_existed.stream()
                    .filter(item -> NumberUtil.equals(item.getLangBaseId(), ppSectionLangMap.get(ppSectionBaseId))
                            && LangTypeEnum.check(item.getLangType(), LangTypeEnum.Section)).collect(Collectors.toSet()).size() > 0) {
                continue;
            }
            orderLanguageRelInfoPOSSaved.add(buildOrderLanguageRelIInfo(ppSectionBaseId, LanguageType.CHI.getLanguageId(), ppSectionLangMap.get(ppSectionBaseId), orderId, LangTypeEnum.Section.getType()));

        }
    }

    /**
     *
     * @param originalOrderId
     * @param subOrderId
     * @param subcontractRels
     * @param testLineInstancePOList
     */
    private void appendSubTestLinRelInfoPOS(String originalOrderId, String subOrderId,
                                            List<SubcontractRelInfoPO> subcontractRels,
                                            List<TestLineInstancePO> testLineInstancePOList,
                                            List<PPTestLineRelationshipInfoPO> ppTestLineRelList) {
        if (CollectionUtils.isNotEmpty(testLineInstancePOList)) {
            for (TestLineInstancePO testLinePO : testLineInstancePOList){
                SubcontractRelInfoPO subcontractRelInfoPO = new SubcontractRelInfoPO();
                subcontractRelInfoPO.setOriginalOrderId(originalOrderId);
                subcontractRelInfoPO.setSubOrderId(subOrderId);
                // add Tl时 主单rel字段设置null
                subcontractRelInfoPO.setOriginalRelId(null);
                subcontractRelInfoPO.setSubRelId(testLinePO.getID());
                subcontractRelInfoPO.setRelType(TableType.TestLine.getTableId());
                subcontractRelInfoPO.setEventType(EventType.None.getType());
                subcontractRelInfoPO.setStatus(1);
                subcontractRelInfoPO.setCreatedBy(UserHelper.getLocalUser().getRegionAccount());
                subcontractRelInfoPO.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
                subcontractRelInfoPO.setCreatedDate(DateUtils.getNow());
                subcontractRelInfoPO.setModifiedDate(DateUtils.getNow());
                subcontractRels.add(subcontractRelInfoPO);
            }
        }
        if (CollectionUtils.isNotEmpty(ppTestLineRelList)) {
            for (PPTestLineRelationshipInfoPO ppTestLineRelationshipInfos : ppTestLineRelList){
                SubcontractRelInfoPO subcontractRelInfoPO = new SubcontractRelInfoPO();
                subcontractRelInfoPO.setOriginalOrderId(originalOrderId);
                subcontractRelInfoPO.setSubOrderId(subOrderId);
                // add Tl时 主单rel字段设置null
                subcontractRelInfoPO.setOriginalRelId(null);
                subcontractRelInfoPO.setSubRelId(ppTestLineRelationshipInfos.getID());
                subcontractRelInfoPO.setRelType(TableType.PPTestLineRel.getTableId());
                subcontractRelInfoPO.setEventType(EventType.None.getType());
                subcontractRelInfoPO.setStatus(1);
                subcontractRelInfoPO.setCreatedBy(UserHelper.getLocalUser().getRegionAccount());
                subcontractRelInfoPO.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
                subcontractRelInfoPO.setCreatedDate(DateUtils.getNow());
                subcontractRelInfoPO.setModifiedDate(DateUtils.getNow());
                subcontractRels.add(subcontractRelInfoPO);
            }
        }

    }


    private List<OrderLanguageRelInfoPO> getOrderLanguageRelInfoPOS(String orderId) {
        //search orderLanguageRelInfoMapper
        List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOS_existed = orderLanguageRelMapper.getOrderLanguageInfoList(orderId, null);
        if (CollectionUtils.isEmpty(orderLanguageRelInfoPOS_existed)) {
            orderLanguageRelInfoPOS_existed = Lists.newArrayList();
        }
        return orderLanguageRelInfoPOS_existed;
    }

    private OrderLanguageRelInfoPO buildOrderLanguageRelIInfo(Long objectBaseId, int languageId,Long langBaseId,String orderId,int langType) {
        OrderLanguageRelInfoPO objOrderLanguageRelInfoPO=new OrderLanguageRelInfoPO();
        objOrderLanguageRelInfoPO.setObjectBaseId(objectBaseId);
        objOrderLanguageRelInfoPO.setLangBaseId(langBaseId);
        objOrderLanguageRelInfoPO.setLangType(langType);
        objOrderLanguageRelInfoPO.setLanguageId(languageId);
        objOrderLanguageRelInfoPO.setOrderId(orderId);
        objOrderLanguageRelInfoPO.setLangStatus(1);
        objOrderLanguageRelInfoPO.setCreatedDate(DateUtils.getNow());
        objOrderLanguageRelInfoPO.setModifiedDate(DateUtils.getNow());
        return objOrderLanguageRelInfoPO;
    }

    /**
     * 获取已保存TestLineInstance 并返回Map结构
     * @param oldTestLines
     * @param testLineStandards
     * @return
     */
    private Map<String, TestLineInstancePO> getSavedTestLineInstanceList(List<TestLineInstancePO> oldTestLines, List<TestLineCitationRsp> testLineStandards){
		/* 查询已存在OrderTestLine */
        Map<String, TestLineInstancePO> testLineMaps = Maps.newHashMap();

        for (TestLineInstancePO oldTestLine : oldTestLines) {
            if (TestLineStatus.check(oldTestLine.getTestLineStatus(), TestLineStatus.Cancelled)) {
                continue;
            }
            TestLineCitationRsp testLine = testLineStandards.stream()
                    .filter(e -> NumberUtil.equals(oldTestLine.getTestLineBaseId(), e.getTestLineBaseId())).findFirst().orElse(null);
            if(testLine == null){
                continue;
            }
            List<CitationBaseInfoRsp> citations = testLine.getCitations();
            if(citations == null || citations.isEmpty()){
                continue;
            }
            CitationBaseInfoRsp citation = citations.stream()
                    .filter(c ->NumberUtil.equals(oldTestLine.getCitationBaseId(), c.getCitationBaseId())).findFirst().orElse(null);
            if(citation == null){
                continue;
            }
            String key = String.format("%s_%s_%s_%s", oldTestLine.getTestLineID(), citation.getCitationId(), citation.getCitationVersionId(), citation.getCitationSectionId());
            if (!testLineMaps.containsKey(key)) {
                testLineMaps.put(key, oldTestLine);
                continue;
            }
            TestLineInstancePO testLineInstancePO = testLineMaps.get(key);
            // 如果map中保存的 状态为 Typing DR 的testLine,map不更新
            if (TestLineStatus.check(testLineInstancePO.getTestLineStatus(), TestLineStatus.Typing, TestLineStatus.DR)) {
                continue;
            }
            testLineMaps.put(key, oldTestLine);
        }
        return testLineMaps;
    }

    private PPTestLineRelationshipInfoPO buildPpTestLineRelationshipInfoPO(String orderId, TestLineInstancePO testLineInstancePO, TestLineCitationRsp testLineCitationRsp,
                                                                           Long rootPpBaseId) {
        PPTestLineRelationshipInfoPO ppTestLineRelationship = new PPTestLineRelationshipInfoPO();
        ppTestLineRelationship.setGeneralOrderInstanceID(orderId);
        ppTestLineRelationship.setID(UUID.randomUUID().toString());

        if (NumberUtil.toInt(rootPpBaseId) > 0) {
            ppTestLineRelationship.setSectionID(testLineCitationRsp.getSectionId());
            if (NumberUtil.toInt(testLineCitationRsp.getSectionLevel()) > 0) {
                ppTestLineRelationship.setSectionLevel(String.valueOf(testLineCitationRsp.getSectionLevel()));
            }
            ppTestLineRelationship.setSectionName(testLineCitationRsp.getSectionName());
        }

        // 设置CSPP 信息
        if (CollectionUtils.isNotEmpty(testLineCitationRsp.getCsppTestLineInfo())) {
            // DIG-9398 此场景下，在localtrims中仅有一条CSPP 下的有效TestLine
            CsppTestLineInfoRsp csppTestLineInfoRsp = testLineCitationRsp.getCsppTestLineInfo().get(0);
            PpTestLineExtFieldsDTO ppTestLineExtFieldsDTO = new PpTestLineExtFieldsDTO();
            ppTestLineExtFieldsDTO.setTestLineAid(csppTestLineInfoRsp.getAid());
            ppTestLineExtFieldsDTO.setTestLineArtifactRelId(csppTestLineInfoRsp.getPpArtifactRelId());
            ppTestLineExtFieldsDTO.setTestLineVersionId(csppTestLineInfoRsp.getTestLineVersionId());
            ppTestLineRelationship.setExtFields(ppTestLineAssembler.convertPpTestLineExtStr(ppTestLineExtFieldsDTO));
        }


        ppTestLineRelationship.setTestLineInstanceID(testLineInstancePO.getID());
        ppTestLineRelationship.setPpArtifactRelId(NumberUtil.toLong(testLineCitationRsp.getPpArtifactRelId()));
        ppTestLineRelationship.setPpBaseId(NumberUtil.toLong(testLineCitationRsp.getPpBaseId()));
        ppTestLineRelationship.setCreatedBy(UserHelper.getLocalUser().getRegionAccount());
        ppTestLineRelationship.setCreatedDate(DateUtils.getNow());

        // ppTestLineRelationship.setPPNotes(citationBaseStandarInfo.getPpNotes());
        ppTestLineRelationship.setAid(NumberUtil.toLong(testLineCitationRsp.getArtifactId()));

        //objTestLineStandarBase.getPpBaseId()的最顶层PP，即用户输入进行搜索的PP
        ppTestLineRelationship.setRootPpBaseId(NumberUtil.toLong(rootPpBaseId));
        //这里赋默认0，当by pp添加时，会重新赋值
        ppTestLineRelationship.setSubPpRelSeq(0);
        return ppTestLineRelationship;
    }

    /**
     * 根据StandardBase与CitationBase构造TestLine
     * @param testLineBase
     * @param citation
     * @param isWorkingInstructionTl
     * @param isNeedCheckComplete
     * @param order
     * @return
     */
    private TestLineInstancePO buildTestLineInstancePO(TestLineCitationRsp testLineBase,
                                                       CitationBaseInfoRsp citation, Boolean isWorkingInstructionTl, Boolean isNeedCheckComplete, 
                                                       GeneralOrderInstanceInfoPO order) {
        TestLineInstancePO testLine = new TestLineInstancePO();
        testLine.setID(UUID.randomUUID().toString());
        testLine.setGeneralOrderInstanceID(order.getID());
        testLine.setTestLineID(testLineBase.getTestLineId());
        testLine.setTestLineVersionID(testLineBase.getTestLineVersionId());
        testLine.setTestLineStatus(TestLineStatus.Typing.getStatus());
        testLine.setConditionStatus(ConditionStatus.UnConfirmed.getStatus());
        testLine.setProductLineAbbr(testLineBase.getProductLineAbbr());

        testLine.setTestLineBaseId(testLineBase.getTestLineBaseId());
        testLine.setCitationBaseId(citation.getCitationBaseId());
        testLine.setLabSectionBaseId(testLineBase.getLabSectionBaseId());
        testLine.setCitationId(citation.getCitationId());
        //add by vincent 2020年9月20日 添加新字段
        testLine.setCitationVersionId(citation.getCitationVersionId());

        // DIG-8685 TestLineType 根据枚举值判断 初始值
        int testLineType = TestLineType.None.getType();
        TrimsTestLineType trimsType = TrimsTestLineType.findType(testLineBase.getTestLineType());
        if (trimsType != null) {
            TestLineType testLineTypeByTrimsType = TestLineType.getTestLineTypeByTrimsType(trimsType.getType());
            if (testLineTypeByTrimsType != null) {
                testLineType = testLineTypeByTrimsType.getType();
            }
        }

        // 判断CSPP
        if (ArtifactType.check(testLineBase.getArtifactType(), ArtifactType.PP)){
            testLineType |= TestLineType.CSPP.getType();
        }
        // 设置Claim值
        if (isWorkingInstructionTl) {
            testLineType |= TestLineType.Claim.getType();
        }
        if (!isNeedCheckComplete) {
            testLineType |= TestLineType.CloneTestLine.getType();
        }
        // DIG-9398 设置CSPP 获取testLine 标记
        if (CollectionUtils.isNotEmpty(testLineBase.getCsppTestLineInfo())) {
            testLineType |= TestLineType.CSPP_TESTLINE_TRIMS_DATA.getType();
        }

        testLine.setTestLineType(testLineType);

        testLine.setActiveIndicator(true);
        testLine.setCreatedBy(UserHelper.getLocalUser().getRegionAccount());
        testLine.setCreatedDate(DateUtils.getNow());

        // 补充设置labId、labCode和orderNo
        testLine.setLabId(order.getLabId());
        testLine.setLabCode(order.getLabCode());
        testLine.setOrderNo(order.getOrderNo());

        return testLine;
    }

    /**
     * 设置status 为708的逻辑
     * @param testLineRedisList
     * @param testLineInstancePOListSave
     * @param testLineInstancePOList_existed
     * @param ppTestLineRels_existed
     * @param orderNo
     * @return
     */
    private CustomResult setDrFlag(List<TestLineCitationRsp> testLineRedisList,
                                   List<TestLineInstancePO> testLineInstancePOListSave,
                                   List<TestLineInstancePO> testLineInstancePOList_existed,
                                   List<PPTestLineRelationshipInfoPO> ppTestLineRels_existed,
                                   String orderNo) {
        // 校验 类型 不同的testLine  不能添加
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(true);

        // 根据现有订单中的aid获取testLine 类型
        List<String> cancelTestLineIds = testLineInstancePOList_existed.stream()
                .filter(item -> TestLineStatus.check(item.getTestLineStatus(), TestLineStatus.Cancelled))
                .map(TestLineInstancePO::getID).collect(Collectors.toList());
        List<Long> aids = ppTestLineRels_existed.stream()
                .filter(item -> !cancelTestLineIds.contains(item.getTestLineInstanceID()))
                .map(PPTestLineRelationshipInfoPO::getAid).collect(Collectors.toList());
        List<PpArtifactRsp> ppArtifactRels = ppArtifactRelClient.getPpArtifactRelListByArtifactIds(aids);
        Map<Integer, PpArtifactRsp> ppArtifactMaps = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(ppArtifactRels)) {
            ppArtifactMaps = ppArtifactRels.stream().collect(Collectors.toMap(PpArtifactRsp::getTestLineId, Function.identity(), (k1, k2) -> k1));
        }

        // DIG-8447
        List<String> errorMsgs = Lists.newArrayList();
        // 已存在的 testLine获取key
        Map<Integer, List<TestLineInstancePO>> testLineIdGroupMaps = testLineInstancePOList_existed.stream()
                .filter(item -> !TestLineStatus.check(item.getTestLineStatus(), TestLineStatus.Cancelled)).collect(Collectors.groupingBy(TestLineInstancePO::getTestLineID));
        for (TestLineCitationRsp tlCitationItem : testLineRedisList) {
            if (!testLineIdGroupMaps.containsKey(tlCitationItem.getTestLineId())) {
                continue;
            }
            // 订单中没有此条testLine,直接跳过
            List<TestLineInstancePO> existedTLs = testLineIdGroupMaps.get(tlCitationItem.getTestLineId());
            if (CollectionUtils.isEmpty(existedTLs)) {
                continue;
            }
            for (Integer testLineType : TestLineType.maps.keySet()){
                TestLineInstancePO typeTL = existedTLs.stream()
                        .filter(item -> TestLineType.check(item.getTestLineType(), TestLineType.findType(testLineType) ))
                        .findFirst().orElse(null);
                TrimsTestLineType trimsTestLineType = TestLineType.getTrimsTestLineType(testLineType);
                if (trimsTestLineType != null || TestLineType.check(testLineType, TestLineType.None)) {
                    // 订单中已有 这个类型的testLine,不能添加
                    if (typeTL != null && !TrimsTestLineType.check(tlCitationItem.getTestLineType(), trimsTestLineType)) {
                        errorMsgs.add(String.format("TL ID: [%s]  has different TestLineType %s in orderNo: [%s] and can not be added .",
                                tlCitationItem.getTestLineId(), TestLineType.findType(testLineType).getMessage(), orderNo));
                    }
                    // 订单中没有 此类型,但是新加的testline是特殊类型
                    if (typeTL == null && trimsTestLineType != null && TrimsTestLineType.check(tlCitationItem.getTestLineType(), trimsTestLineType)) {
                        errorMsgs.add(String.format("TL ID: [%s]  has different TestLineType %s in orderNo: [%s] and can not be added .",
                                tlCitationItem.getTestLineId(), trimsTestLineType.getType(), orderNo));
                    }
                }
            }

            // 获取订单中现有的 testLine类型
            PpArtifactRsp ppArtifactRsp = ppArtifactMaps.get(tlCitationItem.getTestLineId());
            // ppArtifactRsp== null 的场景是addtestLine场景，此场景 testLIne的 TlExecutionClassificationCode默认为1
            if (ppArtifactRsp == null) {
                ppArtifactRsp = new PpArtifactRsp();
                ppArtifactRsp.setTlExecutionClassificationCode(TlExecutionClassificationCodeEnums.NeedToTest.getCode());
            }
            // add TestLine 默认NeedToTest
            if (NumberUtil.toInt(tlCitationItem.getArtifactId()) == 0) {
                tlCitationItem.setTlExecutionClassificationCode(TlExecutionClassificationCodeEnums.NeedToTest.getCode());
            }
            // 需要添加的testLine与订单中的TestLine类型不一致
            if (!NumberUtil.equals(tlCitationItem.getTlExecutionClassificationCode(), ppArtifactRsp.getTlExecutionClassificationCode())) {
                errorMsgs.add(String.format("TL ID: [%s]  has different TLExecutionClassification in orderNo: [%s] and can not be added .", tlCitationItem.getTestLineId(), orderNo));
            }
        }
        if (CollectionUtils.isNotEmpty(errorMsgs)) {
            return rspResult.fail(StringUtils.join(errorMsgs, ";"));
        }

        // 存放本次添加testLine的 drFlag
        Map<String, Integer> drFlagMap = new HashMap<String, Integer>();
        for (TestLineCitationRsp testLine : testLineRedisList) {
            TestLineInstancePO objTestLineInstanceSavePO=testLineInstancePOListSave.stream()
                    .filter(testLineInstancePO -> testLineInstancePO.getTestLineBaseId().intValue()== testLine.getTestLineBaseId().intValue()).findFirst().orElse(null);
            if (objTestLineInstanceSavePO == null){
                continue;
            }
            String key = objTestLineInstanceSavePO.getTestLineID() + "_" + objTestLineInstanceSavePO.getCitationVersionId() + "_"
                    + objTestLineInstanceSavePO.getStandardSectionID();
            if (drFlagMap.containsKey(key)) {
                if (StringUtils.equalsIgnoreCase(
                        TlExecutionClassification.getMessage(testLine.getTlExecutionClassificationCode()==null?
                                TlExecutionClassification.NeedToTest.getStatus():testLine.getTlExecutionClassificationCode()),
                        TlExecutionClassification.NeedToTest.getMessage())) {
                    drFlagMap.put(key, testLine.getTlExecutionClassificationCode()==null
                            ?TlExecutionClassification.NeedToTest.getStatus():testLine.getTlExecutionClassificationCode());
                }
            } else {
                drFlagMap.put(key, testLine.getTlExecutionClassificationCode()==null?
                        TlExecutionClassification.NeedToTest.getStatus():testLine.getTlExecutionClassificationCode());
            }
        }
        if (CollectionUtils.isNotEmpty(testLineInstancePOListSave)) {
            for (TestLineInstancePO testLineInstancePO : testLineInstancePOListSave) {
                String key = testLineInstancePO.getTestLineID() + "_" + testLineInstancePO.getCitationVersionId()+ "_"
                        + testLineInstancePO.getStandardSectionID();
                if(drFlagMap.containsKey(key)){
                    if (!testLineInstancePOList_existed.stream().anyMatch(testLineInstancePO1 -> testLineInstancePO1.getID().equals(testLineInstancePO.getID()))) {// insert
                        if (StringUtils.equalsIgnoreCase(drFlagMap.get(key).toString(), TlExecutionClassification.InformationReviewOnly.getStatus().toString())) {
                            testLineInstancePO.setTestLineStatus(TestLineStatus.DR.getStatus());
                        }

                    }else{
                        //708是dr类型
                        if (StringUtils.equalsIgnoreCase(testLineInstancePO.getTestLineStatus().toString(), "708")) {
                            if (StringUtils.equalsIgnoreCase(drFlagMap.get(key).toString(),
                                    TlExecutionClassification.NeedToTest.getStatus().toString())) {
                                testLineInstancePO.setTestLineStatus(TestLineStatus.Typing.getStatus());
                            }
                        }
                    }

                }
            }

        }
        return rspResult;
    }

    /**
     *
     * @param testLineInstanceId
     * @return
     */
    public TestLineInstancePO getTestLineInstanceById(String testLineInstanceId){
        return testLineMapper.getTestLineInstanceById(testLineInstanceId);
    }

    /**
     *
     * @param jobNo
     * @return
     */
    public List<JobTestLineDto> getTestLineInstanceByJobNo(String jobNo){
        return testLineMapper.getTestLineInstanceByJobNo(jobNo);
    }

    /**
     *
     * @param req
     * @return
     */
    @AccessRule(reportStatus = { ReportStatus.New, ReportStatus.Draft, ReportStatus.Combined }, isInclude = true)
    public CustomResult uploadTestLineReportOld(TestLineReportListReq req){
        logger.info("TestLineInstanceService.uploadTestLineReport Start(OrderNo_{}), Req: {}", req.getOrderNo(), req);
        CustomResult result = new CustomResult();
        if (StringUtils.isEmpty(req.getOrderNo())){
            result.setMsg("请求的OrderNo不能为空.");
            return result;
        }
        if (StringUtils.isEmpty(req.getJobNo())){
            result.setMsg("请求的JobNo不能为空.");
            return result;
        }
        String orderNo = req.getOrderNo();
        String jobNo = req.getJobNo();
        List<TestLineReportReq> testLineReports = req.getTestLineReports();
        if (testLineReports == null || testLineReports.isEmpty()){
            result.setMsg("请求的testLineReports 列表不能为空.");
            return result;
        }
        GeneralOrderInstanceInfoPO oldOrder = orderMapper.getOrderInfo(orderNo);
        if (oldOrder == null){
            result.setMsg("对应找到对应的OrderNo信息.");
            return result;
        }
        ReportInfoPO reportByOrderNo = reportMapper.getReportByOrderNo(req.getOrderNo());
        if (reportByOrderNo == null) {
            result.setMsg("该orderno查询不到report信息.");
            return result;
        }
        if (!ReportStatus.check(reportByOrderNo.getReportStatus(), ReportStatus.New, ReportStatus.Draft, ReportStatus.Combined)) {
            result.setMsg("请检查Report Status, 当前状态不能操作.");
            return result;
        }

        // DIG-9329 Fast TL 需要先操作LabIn
        CustomResult<Boolean> statusResult = statusClient.getOrderStatus(orderNo,-1, OrderStatusEnum.Testing);
        Boolean isCompleted = statusResult.getData();
        if (!statusResult.isSuccess() || isCompleted == null || !isCompleted){
            return statusResult.fail("Fail to send back for Order Status not being Testing!");
        }


        HashMap<Integer, String> testLineReportMaps = Maps.newHashMap();
        HashSet<Integer> testLineIds = Sets.newHashSet();
        testLineReports.forEach(testLine->{
            List<Integer> tlIds = testLine.getTestLineIds();
            if (tlIds == null || tlIds.isEmpty()){
                return;
            }
            testLineIds.addAll(tlIds);
            for (Integer testLineId: tlIds) {
                testLineReportMaps.put(testLineId, testLine.getReportUrl());
            }
        });
        Set<Integer> keyTestLine = testLineReportMaps.keySet();
        TestLineReportParamReq reqParams = new TestLineReportParamReq();
        reqParams.setOrderNo(orderNo);
        reqParams.setTestLineIds(Lists.newArrayList(testLineIds));

        // 检查当前OrderNo对应的TestLine
        List<TestLineReportPo> tlReports = testLineMapper.getTestLineByTestLineIds(reqParams);
        if (tlReports == null || tlReports.isEmpty()){
            result.setMsg("未找到对应有效的 TestLine信息.");
            return result;
        }

        // 根据OrderNo获取BuId、LocationID
        OrderInfoDto order = orderClient.getOrderInfoByOrderNo(orderNo);
        if (order == null){
            result.setMsg(String.format("未找到对应的OrderNo(%s)信息.", orderNo));
            return result;
        }

        List<TestLineInstancePO> testLineFiles = Lists.newArrayList();
        HashMap<String, UploadFileInfo> groupReportMaps = Maps.newHashMap();
        TestLineInstancePO testLineFile = null;
        HashSet<Integer> success = Sets.newHashSet();
        HashSet<Integer> failures = Sets.newHashSet();

        for (TestLineReportPo tlReport: tlReports){
            if (tlReport.getActive() != null && tlReport.getActive() == 1){
                failures.add(tlReport.getTestLineId());
                continue;
            }
            if (!TestLineStatus.check(tlReport.getTestLineStatus(), TestLineStatus.Typing, TestLineStatus.Entered)){
                failures.add(tlReport.getTestLineId());
                logger.info("当前OrderNo({})的TestLine({}) Status 为：{}.", orderNo, tlReport.getId(), tlReport.getTestLineStatus());
                continue;
            }
            if (keyTestLine.contains(tlReport.getTestLineId())){
                String reportUrl = testLineReportMaps.get(tlReport.getTestLineId());
                if (!groupReportMaps.containsKey(reportUrl)){
                    UploadFileInfo file = new UploadFileInfo();
                    file.setBuId(order.getBUID());
                    file.setLocationID(order.getLocationID());
                    file.setOrderId(tlReport.getOrderId());
                    file.setId(tlReport.getId());
                    file.setTestLineId(tlReport.getTestLineId());
                    file.setReportUrl(reportUrl);
                    groupReportMaps.put(reportUrl, file);
                }
            }
            success.add(tlReport.getTestLineId());
            testLineFile = new TestLineInstancePO();
            testLineFile.setID(tlReport.getId());
            testLineFile.setTestLineID(tlReport.getTestLineId());
            testLineFile.setTestLineStatus(TestLineStatus.Completed.getStatus());
            if (TestLineStatus.check(tlReport.getTestLineStatus(), TestLineStatus.SubContracted)){
                testLineFile.setTestLineStatus(TestLineStatus.SubContracted.getStatus());
            }
            testLineFiles.add(testLineFile);
        }
        TestLineReportRsp testLineRsp = new TestLineReportRsp();
        if (groupReportMaps.isEmpty()){
            testLineRsp.setFailures(failures);
            result.setData(testLineRsp);
            result.setMsg("未找到OrderNo下有效Testline (仅允许：无样式TL，且TL是Typing或Entered状态）.");
            return result;
        }
        List<String> testLineInstanceIds = Lists.newArrayList();
        ConcurrentHashMap<String, String> reportFiles = this.parallelUploadFile(groupReportMaps);
        for (TestLineInstancePO file: testLineFiles) {
            file.setModifiedBy("System");
            file.setModifiedDate(DateUtils.getNow());
            if (!testLineReportMaps.containsKey(file.getTestLineID())){
                failures.add(file.getTestLineID());
                success.remove(file.getTestLineID());
                continue;
            }
            String reportUrl = testLineReportMaps.get(file.getTestLineID());
            if (!reportFiles.containsKey(reportUrl)){
                failures.add(file.getTestLineID());
                success.remove(file.getTestLineID());
                continue;
            }
            testLineInstanceIds.add(file.getID());
            file.setFileID(reportFiles.get(reportUrl));
        }
        testLineRsp.setSuccess(success);
        testLineRsp.setFailures(failures);
        result.setData(testLineRsp);
        if (reportFiles.isEmpty()){
            result.setMsg("附件上传失败.");
            return result;
        }

        List<TestLineTestDataReq> datas = req.getDatas();

        CustomResult checkResult = this.checkUploadData(oldOrder, datas, testLineInstanceIds);
        if (!checkResult.isSuccess()) {
            result.setData(null);
            return result.fail(checkResult.getMsg());
        }

        List<com.sgs.testdatabiz.facade.model.req.fast.TestLineTestDataReq> testLineTestDataReqs = BeanUtil.copyToList(datas, com.sgs.testdatabiz.facade.model.req.fast.TestLineTestDataReq.class);
        TestDataInfoReq testDataInfoReq = new TestDataInfoReq();
        testDataInfoReq.setReportNo(reportByOrderNo.getReportNo());
        testDataInfoReq.setDatas(testLineTestDataReqs);
        testDataInfoReq.setJobNo(jobNo);
        testDataInfoReq.setObjectNo(jobNo);
        testDataInfoReq.setCompletedDate(DateUtils.getNow());
        testDataInfoReq.setOrderNo(orderNo);
        testDataInfoReq.setProductLineCode(order.getBUCode());
        testDataInfoReq.setLabCode(oldOrder.getLabCode());

        // DIG-9591 starlims fast 校验回传结果
        BaseResponse checkResponse = reportDataHandlerService.checkBackData(testDataInfoReq, SourceTypeEnum.FAST);
        if (!checkResponse.isSuccess()) {
            return result.fail(String.format("客户校验失败，错误原因：%s", checkResponse.getMessage()));
        }

        result.setSuccess(transactionTemplate.execute((tranStatus)->{
            boolean isSuccess = testLineStatusService.batchUpdateTestLineStatus(TestLineModuleType.UploadTestLineReport, testLineFiles).isSuccess();

            // POSL-4495 fast数据录入TestData服务

            BaseResponse baseResponse = testDataBizClient.fastSaveTestData(testDataInfoReq);
            if (baseResponse != null && baseResponse.getStatus() != 200) {
//                result.setMsg(String.format("fastSaveTestData End(OrderNo_%s),Req:{%s},Rsp:{%s},Exception:{%s}", orderNo,testDataInfoReq,baseResponse.getData(),baseResponse.getMessage()));
//                result.setData(null);
                logger.info("testDataBizClient.fastSaveTestData End(OrderNo_{}),Req:{},Rsp:{},Exception:{}",orderNo,testDataInfoReq,baseResponse.getData(),baseResponse.getMessage());
//                return false;
            }

            if (!isSuccess){
                // 回滚事务
                tranStatus.setRollbackOnly();
                return false;
            }
            this.checkPreOrderStatus(oldOrder);

            return true;
        }));
        //result.setSuccess(testLineMapper.batchUpdateTestlineInstanceFileId(testLineFiles) > 0);

        logger.info("TestLineInstanceService.uploadTestLineReport End(OrderNo_{}), Req: {}, Rsp: {}", req.getOrderNo(), req, result);
        return result;
    }

    /**
     *
     * @param oldOrder 订单号
     * @param datas 入参相关数据
     * @param testLineInstanceIds Success 相关的TestLine
     * @return
     */
    private CustomResult checkUploadData(GeneralOrderInstanceInfoPO oldOrder, List<TestLineTestDataReq> datas, List<String> testLineInstanceIds) {
        CustomResult rspResult = new CustomResult();

        String orderNo = oldOrder.getOrderNo();
        // DIG-8159 判断订单创建时间（ 大于 2022-09-12 需要校验相关testData的逻辑   小于2022-09-12 不需要校验 testData的逻辑）
        if (oldOrder.getCreatedDate().getTime() <= DateUtils.parseDate("2022-09-12 00:00:00").getTime()) {
            logger.info("uploadTestLineReport CreatedDate(OrderNo_{}_date{}), 不需要校验Datas", orderNo, oldOrder.getCreatedDate());
            rspResult.setSuccess(true);
            return rspResult;
        }

        // POSL-4495 添加校验
        if (CollectionUtils.isEmpty(datas)) {
            return rspResult.fail("Failed to send back, lack of TestLine result data, please check and try again !");
        }
        // 获取testLine下的所有 matrix sample 信息
        List<TestMatrixTestLineInfo> testMatrixInfos = testMatrixMapper.getMatrixInfoByTestLineIds(testLineInstanceIds);
        // 订单中所有的MatrixId
        Set<String> allMatrixIds = testMatrixInfos.stream().map(TestMatrixTestLineInfo::getTestMatrixId).collect(Collectors.toSet());

        Set<String> matrixIdsReq = Sets.newHashSet();
        datas.stream().forEach(item -> {
            matrixIdsReq.addAll(item.getMatrixIds());
        });

        List<String> unReqMatrixIds = allMatrixIds.stream().filter(item -> !matrixIdsReq.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unReqMatrixIds)) {
            List<TestLineSampleTypeInfo> matrixInfo = testMatrixMapper.getTestLineSampleInfoByMatrixIds(unReqMatrixIds);
            Set<Integer> testLineIds = Sets.newHashSet();
            Set<String> sampleNos = Sets.newHashSet();
            for (TestLineSampleTypeInfo sampleItem : matrixInfo){
                testLineIds.add(sampleItem.getTestLineId());
                sampleNos.add(sampleItem.getSampleNo());
            }
            if (CollectionUtils.isNotEmpty(testLineIds)) {
                return rspResult.fail(String.format("Failed to send back, lack of TestLine【%s】 sample【%s】 data, please check and try again !",
                        StringUtils.join(testLineIds, ","), StringUtils.join(sampleNos, ",")));
            }
        }
        for (TestLineTestDataReq dataReq : datas){
            if (CollectionUtils.isEmpty(dataReq.getTestResults())) {
                return rspResult.fail(String.format("Failed to send back, lack of TestLine data, TL " + StringUtils.join(dataReq.getTestLineIds(), ",") + " (Sample "+dataReq.getSampleNo() + "), please check and try again !"));
            }
        }

        OrderTrfRelationshipDTO relationshipDTO = orderClient.getOrderTrfRelationshipByOrderNo(orderNo, ProductLineContextHolder.getProductLineCode());
        if(Objects.isNull(relationshipDTO) || !RefSystemIdEnum.check(relationshipDTO.getReferenceId(), RefSystemIdEnum.Shein,RefSystemIdEnum.SheinSupplier)) {
            rspResult.setSuccess(true);
            return rspResult;
        }
        List<String> lackAnalyteName = Lists.newArrayList();
        for (TestLineTestDataReq dataReq : datas){
            for (TestLineTestResultReq resultReq : dataReq.getTestResults()){
                List<TestResultLangReq> languages = resultReq.getLanguages();
                if (CollectionUtils.isEmpty(languages)) {
                    lackAnalyteName.add(resultReq.getAnalyteAlias());
                    continue;
                }
                TestResultLangReq testResultLangReq = languages.stream().filter(item -> NumberUtil.equals(item.getLanguageId(), 2)).findFirst().orElse(null);
                if (testResultLangReq == null || StringUtils.isBlank(testResultLangReq.getAnalyteAlias())) {
                    lackAnalyteName.add(resultReq.getAnalyteAlias());
                    continue;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(lackAnalyteName)) {
            return rspResult.fail(String.format("Failed to send back, Analyte【%s】 data lacks Chinese, please check and try again !", StringUtils.join(lackAnalyteName, ",")));
        }

        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param groupReportMaps
     * @return
     */
    private ConcurrentHashMap<String, String> parallelUploadFile(HashMap<String, UploadFileInfo> groupReportMaps){
        ConcurrentHashMap<String, String> reportFiles = new ConcurrentHashMap();
        final CountDownLatch latch = new CountDownLatch(groupReportMaps.size());
        Iterator<Map.Entry<String, UploadFileInfo>> entrys = groupReportMaps.entrySet().iterator();
        while (entrys.hasNext()){
            Map.Entry<String, UploadFileInfo> entry = entrys.next();
            ThreadConfig.getThreadPoolTaskExecutor().submitListenable(()-> fileClient.uploadFileWithoutGeneric(reportFiles, entry.getValue()))
                    .addCallback(new ListenableFutureCallback<CustomResult>() {
                        @Override
                        public void onSuccess(CustomResult limits) {
                            latch.countDown();
                        }
                        @Override
                        public void onFailure(Throwable t) {
                            // TODO Auto-generated method stub
                            latch.countDown();
                        }
                    });
        }
        try {
            // 设定超时时间单位：毫秒
            boolean completed = latch.await(10000, TimeUnit.MILLISECONDS);
            if (!completed) {
                logger.warn("parallelUploadFile latch timeout after {} ms, remaining: {}", 10000, latch.getCount());
            }
        } catch (InterruptedException ie) {
            // DIG-8555 Either re-interrupt this method or rethrow the "InterruptedException" that can be caught here.
            Thread.currentThread().interrupt();
        }
        return reportFiles;
    }

    public List<JobTestLineForEmDto> getTestLineInstanceByJobNoForEm(List<String> jobNos){
        return testLineMapper.getTestLineInstanceByJobNoForEm(jobNos);
    }

    /**
     *
     * @param orderNo
     * @return
     */
    public CustomResult getTestLineBreakDownInfoList(String orderNo){
        TestLineBreakDownRsp rspResult = new TestLineBreakDownRsp();
        rspResult.setSamples(testSampleMapper.getOriginalSampleBreakDownInfoList(orderNo));

        List<TestLineBreakDownInfo> testLines = testLineMapper.getTestLineBreakDownInfoList(orderNo);
        if (testLines == null || testLines.isEmpty()){
            return CustomResult.newSuccessInstance(testLines);
        }

        ReportInfoPO reportByOrderNo = reportMapper.getReportByOrderNo(orderNo);
        if (Objects.isNull(reportByOrderNo) || Objects.isNull(reportByOrderNo.getReportStatus())) {
            return CustomResult.failure("Get Report info fail！");
        }
        Integer reportStatus = reportByOrderNo.getReportStatus();

        Optional<TestLineBreakDownInfo> optional = testLines.stream().filter(o -> Objects.isNull(o.getTestLineStatus())).findFirst();
        if (optional.isPresent()) {
            return CustomResult.failure("Get Testline info fail！");
        }

        List<TestLineBreakDownInfo> chemTestLines = Lists.newArrayList();
        testLines.forEach(testLine->{
            // 根据TestLineType 设置CSPP
            testLine.setCsPP(TestLineType.check(testLine.getTestLineType(), TestLineType.CSPP) ? 1 : 0);
            String labSectionName = testLine.getLabSectionName();
            //TL chem 分包必须要分到chem实验室，正常单productLineAbbr cchemLab，或者 labSection contins chem
            if ( (StringUtils.isNoneBlank(testLine.getSubContractTestLineId()) && labSectionName.toLowerCase().contains("chem"))
                    || StringUtils.equalsIgnoreCase(testLine.getProductLineAbbr(), "CChemLab")
                    || (StringUtils.isNotBlank(labSectionName) && labSectionName.toLowerCase().contains("chem"))){
                chemTestLines.add(testLine);
            }
            testLine.setTestLineStatus(buildTestLineStatusResult(reportStatus, testLine.getTestLineStatus()));
        });
        rspResult.setTestLines(chemTestLines);
        return CustomResult.newSuccessInstance(rspResult);
    }

    private Integer buildTestLineStatusResult(Integer reportStatus, Integer testLineStatus) {
        if (!ReportStatus.check(reportStatus, ReportStatus.New, ReportStatus.Combined, ReportStatus.Draft)) {
            return BomTestLineStatus.COMPLETED.getCode();
        }
        if (TestLineStatus.check(testLineStatus, TestLineStatus.Typing, TestLineStatus.DR)) {
            return  BomTestLineStatus.NEW.getCode();
        }
        if (TestLineStatus.check(testLineStatus, TestLineStatus.Entered)) {
            return  BomTestLineStatus.TESTING.getCode();
        }
        return BomTestLineStatus.COMPLETED.getCode();
    }

    /**
     *
     * @param reqObject/
     */
    public CustomResult returnTestLine(ReturnTestLineReq reqObject){
        CustomResult rspResult = new CustomResult();

        UserInfo localUser = UserHelper.getLocalUser();
        //TestLineInstancePO line = testLineInstanceMapper.selectByPrimaryKey(reqObject.getTestLineInstanceId());
        TestLineInstancePO lineNew = new TestLineInstancePO();
        lineNew.setID(reqObject.getTestLineInstanceId());
        lineNew.setTestLineStatus(TestLineStatus.Entered.getStatus());
        lineNew.setModifiedDate(DateUtils.getNow());
        lineNew.setModifiedBy(localUser.getRegionAccount());

        rspResult.setSuccess(transactionTemplate.execute((tranStatus)->{
            boolean isSuccess = testLineStatusService.updateTestLineStatus(TestLineModuleType.ReturnTestLine, lineNew).isSuccess();
            if (!isSuccess ){
                // 回滚事务
                tranStatus.setRollbackOnly();
                return false;
            }
            return true;
        }));
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getTestLineInfoForReTest(GetTestLineInfoForReTestReq reqObject) {
        CustomResult rspResult = new CustomResult(false);
        if (reqObject == null || StringUtils.isEmpty(reqObject.getTestLineInstanceId())) {
            rspResult.setMsg("PLEASE CHECK PARAMETER");
            return rspResult;
        }
        String testLineInstanceId = reqObject.getTestLineInstanceId();
        List<GetTestLineInfoForReTestInfo> testLineInfoForReTest = testMatrixMapper.getTestLineInfoForReTest(testLineInstanceId);
        if (CollectionUtils.isEmpty(testLineInfoForReTest)) {
            rspResult.setMsg("PLEASE CHECK PARAMETER");
            return rspResult;
        }
        rspResult.setData(testLineInfoForReTest);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @AccessRule(reportStatus = { ReportStatus.Approved, ReportStatus.Cancelled })
    @BizLog(bizType = BizLogConstant.DATA_ENTRY_OPERATION_HISTORY, operType="Retest")
    public CustomResult reTest(ReTestReq reqObject){
        CustomResult rspResult = new CustomResult();
        String testLineInstanceID = reqObject.getTestLineInstanceId();
        // delete all testdata

        List<String> testMatrixs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(reqObject.getTestMatrixId())) {
            testMatrixs = reqObject.getTestMatrixId();
        } else {
            testMatrixs = testMatrixMapper.getTestMatrixByTestLineId(testLineInstanceID);
        }

        List<String> testLineInstanceIDs = new ArrayList<>();
        testLineInstanceIDs.add(testLineInstanceID);

        GeneralOrderInstanceInfoPO order = orderMapper.getOrderByTestLineInstanceId(testLineInstanceID);

        String username=UserHelper.getLocalUser().getRegionAccount();

        // 修改testline的状态为typing  DIG-6577 retest后 testLine 状态变为Entry
        TestLineInstancePO line = testLineMapper.getTestLineById(testLineInstanceID);
        line.setTestLineStatus(TestLineStatus.Entered.getStatus());
        line.setModifiedDate(DateUtils.getNow());
        line.setModifiedBy(username);
        //DIG-6913 retest 不清空
//        //DIG-5555 retest 清空无样式的文件
//        line.setFileID(null);
        line.setValidateBy(null);
        line.setValidateDate(null);

        // 业务动作日志记录
        ActionLogDTO dto = new ActionLogDTO();
        dto.setTestLineInstanceId(testLineInstanceID);
        dto.setOrderNo(order.getOrderNo());
        String testValue = String.valueOf(line.getTestLineID());
        // 获取Matrix的Sample
        if (CollectionUtils.isNotEmpty(testMatrixs)) {
            List<String> sampleList = testMatrixMapper.getSampleListByMatrixs(testMatrixs);
            if (CollectionUtils.isNotEmpty(sampleList)) {
                testValue = String.format("%s(%s)", testValue, StringUtils.join(sampleList, ","));
            }
        }

        int preOrderStatus = orderClient.getOrderStatusByOrderNo(order.getOrderNo());
        BizLogHelper.setValue(order.getOrderNo(), testValue);


        List<String> finalTestMatrixs = testMatrixs;
        rspResult.setSuccess(transactionTemplate.execute((tranStatus)->{
            // DIG-6913 retest 不清空 TestData
//            boolean failedDelIsSuccess = testDataMapper.deleteFailedByTLIdAndMatrixIDs(testLineInstanceID, finalTestMatrixs) > 0;
//            boolean testDataDelIsSuccess = testDataMapper.deleteByTLIdAndMatrixId(testLineInstanceID, finalTestMatrixs) > 0;

            ReportInfoPO reportInfo = new ReportInfoPO();
            ReportInfoPO reportPO = new ReportInfoPO();
            // DIG-6913 清空所有Conclusion
            boolean conclusionDelIsSuccess = conclusionMapper.deleteConclusionByTestLineInstanceId(testLineInstanceID) > 0;
            boolean recalculationUpdIsSuccess = true;
            boolean md5DelIsSuccess = true;
            if(conclusionDelIsSuccess){
                reportPO = reportMapper.getReportByOrderNo(order.getOrderNo());

                if(CommUtil.null2Int(reportPO.getRecalculationFlag())==1){
                    reportPO.setRecalculationFlag(2);

                    reportInfo.setRecalculationFlag(2);
                    reportInfo.setModifiedBy(username);
                    reportInfo.setModifiedDate(DateUtils.getNow());
                    reportInfo.setID(reportPO.getID());
                    recalculationUpdIsSuccess = reportMapper.updateRecalculationFlagByReportId(reportInfo) > 0;
                    //在retest的时候把reportConclusionMD5的值清除掉

                    md5DelIsSuccess = reportMapper.deleteMd5ByReportId(reportPO.getID()) > 0;
                }
            }

            // DIG-6577 不清空footNotes
//            boolean ppConditionGroupUpdIsSuccess = testPpConditionGroupExtMapper.deleteByConditionGroupIDsByTLId(testLineInstanceID) > 0;

            boolean testLineUpdIsSuccess = testLineStatusService.updateMatrixStatus(TestLineModuleType.ReTest, testLineInstanceID, Sets.newHashSet(finalTestMatrixs)).isSuccess();

            if (!testLineUpdIsSuccess){
                tranStatus.setRollbackOnly(); // 回滚事务
                return false;
            }else {
                SysStatusReq reqStatus = new SysStatusReq();
                reqStatus.setObjectNo(order.getOrderNo());
                reqStatus.setNewStatus(PreOrderStatus.Testing.getStatus());
                reqStatus.setOldStatus(PreOrderStatus.Reporting.getStatus());
                // POSL-5688 pending情况下 忽略 原状态
                if (com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(preOrderStatus, com.sgs.preorder.facade.model.enums.OrderStatus.Pending)) {
                    reqStatus.setIgnoreOldStatus(Boolean.TRUE);
                }
                reqStatus.setImmediatelyExecute(true);
                reqStatus.setUserName(UserHelper.getLocalUser().getRegionAccount());
                statusClient.insertStatusInfo(reqStatus);
                return true;
            }
        }));

        if (rspResult.isSuccess()){
            CustomResult customResult = jobService.handleRetest(Lists.newArrayList(testLineInstanceID));
            logger.info("jobService.handleRetest返回结果:{}", JSON.toJSONString(customResult));
        }
        return rspResult;
    }

    public CustomResult getSubContractTestLine(GetSubContractTestLineReq getSubContractTestLineReq){
        CustomResult rspResult = new CustomResult();

        List<SubcontractDTO> list = null;
        List<SubcontractDTO> defaultlist = subContractExtMapper.queryDefaultSubcontractTestLine(getSubContractTestLineReq.getOrderNo());
        List<SubcontractDTO> subContractlist = subContractExtMapper.querySubcontractBySubcontractId(getSubContractTestLineReq.getSubContractId());

        if (getSubContractTestLineReq.getAllflag() == 1) {// 展示所有TL
            list = subContractExtMapper.querySubContractTestLine(getSubContractTestLineReq.getOrderNo());

            if (!defaultlist.isEmpty()) {
                for (SubcontractDTO sub : list) {
                    for (SubcontractDTO dd : defaultlist) {
                        if (!dd.getTestLineInstanceID().equals(sub.getTestLineInstanceID())) {
                            sub.setFlag(true);
                            break;
                        }
                    }
                }
            } else {
                for (SubcontractDTO sub : list) {
                    sub.setFlag(true);
                }
            }
            for (SubcontractDTO dd : subContractlist) {
                for (SubcontractDTO sub : list) {
                    if (dd.getTestLineInstanceID().equals(sub.getTestLineInstanceID())) {
                        sub.setSelectFlag(true);
                        sub.setFlag(false);
                        break;
                    }
                }
            }

        } else {
            list = new ArrayList<>();
            list.addAll(defaultlist);
            if (!subContractlist.isEmpty()) {
                for (SubcontractDTO sub : subContractlist) {
                    sub.setSelectFlag(true);
                    list.add(sub);
                }
            }
        }

        List<CitationNameRsp> citationNameRsps = trimsLocalCitationService.getCitationNameByOrderId(list.get(0).getGeneralOrderInstanceID());
        // 获取本地化数据
        this.buildSubContractInfo(list,citationNameRsps);

        for (SubcontractDTO dto : list) {
            if (StringUtils.isBlank(dto.getLabSection())) {
                if (StringUtils.isBlank(dto.getSubContractName())) {
                    dto.setLabSection("/");
                } else {
                    dto.setLabSection(dto.getSubContractName());
                }
            }
        }

        TestLineTatConfigExample testLineTatConfigPONull = new TestLineTatConfigExample();
        List<TestLineTatConfigPO> testLineTATConfigPOS = testLineTatConfigMapper.selectByExample(testLineTatConfigPONull);
        list.forEach(x->{
            List<TestLineTatConfigPO> collect = testLineTATConfigPOS.stream().filter(y -> y.getTestLineID().equals(Integer.valueOf(x.getTestLineId()))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                /*String token = getSubContractTestLineReq.getToken();
                Map<String, Object> params = new HashMap<>();
                params.put("orderNo", getSubContractTestLineReq.getOrderNo());
                params.put("token", token);*/
                GetOrderInfoRsp getOrderParams = new GetOrderInfoRsp();
                getOrderParams.setOrderNo(getSubContractTestLineReq.getOrderNo());

                try {
                    PreOrder generalOrder = preOrderClient.getGeneralOrder(getOrderParams);
                    if (collect.get(0).getTat() > Integer.valueOf(generalOrder.getTat())) {
                        x.setLongTatMsg("TestLine TAT:" + collect.get(0).getTat() + " Days");
                    }
                } catch (Exception e) {
                    logger.error("调用OrderApi出错：", e);
                }
            }
        });

        List<GetSubContractTestLineRsp> getSubContractTestLineRsps = new ArrayList<GetSubContractTestLineRsp>(list.size());
        list.forEach(subcontractDTO -> {
            GetSubContractTestLineRsp objGetSubContractTestLineRsp = new GetSubContractTestLineRsp();
            BeanUtils.copyProperties(subcontractDTO, objGetSubContractTestLineRsp);
            objGetSubContractTestLineRsp.setTestLineInstanceId(subcontractDTO.getTestLineInstanceID());
            getSubContractTestLineRsps.add(objGetSubContractTestLineRsp);
        });
        //TODO v-tl别名修改
        testLineLocalService.build(getSubContractTestLineReq.getOrderNo(), getSubContractTestLineRsps);
        rspResult.setData(getSubContractTestLineRsps);
        rspResult.setSuccess(true);
        return rspResult;
    }


    /**
     *
     * @param subContractDtos
     */
    private void buildSubContractInfo(List<SubcontractDTO> subContractDtos,List<CitationNameRsp> citationNameRsps) {
        if (CollectionUtils.isEmpty(subContractDtos)) {
            return;
        }
        List<Long> testLineBaseIds = subContractDtos.stream().map(SubcontractDTO::getTestLineBaseId).distinct().collect(Collectors.toList());
        List<Long> labSectionBaseIds = subContractDtos.stream().map(SubcontractDTO::getLabSectionBaseId).distinct().collect(Collectors.toList());
        List<GetTestLineBaseInfoRsp> testLineBaseInfo = testLineClient.getTestLineBaseInfo(testLineBaseIds, LanguageType.EN, null);

        Map<Long, GetTestLineBaseInfoRsp> testLineBaseMap = testLineBaseInfo.stream().collect(Collectors.toMap(GetTestLineBaseInfoRsp::getTestLineBaseId, Function.identity(), (k1, k2) -> k1));
        Map<String, CitationNameRsp> fullNameMap = Maps.newHashMap();
        citationNameRsps.forEach(c->{
            String key = String.format("%s_%s", c.getCitationBaseId(), NumberUtil.toLong(c.getPpArtifactRelId()));
            fullNameMap.put(key,c);
        });
        // labSection 暂不处理
        Map<Long, String> labSectionBaseMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(labSectionBaseIds)) {
            List<GetLabSectionBaseInfoRsp> labSectionBaseInfo = labSectionClient.getLabSectionBaseInfo(labSectionBaseIds);
            labSectionBaseMap = labSectionBaseInfo.stream().collect(Collectors.toMap(GetLabSectionBaseInfoRsp::getLabSectionBaseId, GetLabSectionBaseInfoRsp::getLabSectionName, (k1, k2) -> k1));
        }

        for (SubcontractDTO sub : subContractDtos){
            if (testLineBaseMap.containsKey(sub.getTestLineBaseId())) {
                GetTestLineBaseInfoRsp getTestLineBaseInfoRsp = testLineBaseMap.get(sub.getTestLineBaseId());
                sub.setTestItem(getTestLineBaseInfoRsp.getEvaluationName());
                sub.setProductLineAbbr(getTestLineBaseInfoRsp.getProductLineAbbr());
            }

            Long relId = null;
            if(CollectionUtils.isNotEmpty(sub.getPpArtifactRelIds())){
                List<Long> relIdsList = sub.getPpArtifactRelIds().stream().filter(aLong -> !NumberUtil.equals(aLong,0L)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(relIdsList)){
                    relId = relIdsList.get(0);
                }
            }
            String key = String.format("%s_%s",sub.getCitationBaseId(),NumberUtil.toLong(relId));
            if (fullNameMap.containsKey(key)) {
                CitationNameRsp citationNameRsp = fullNameMap.get(key);
                sub.setTestStandard(citationNameRsp.getCitationFullName());
            }
            if (labSectionBaseMap.containsKey(sub.getLabSectionBaseId())) {
                sub.setLabSection(labSectionBaseMap.get(sub.getLabSectionBaseId()));
            }
        }
    }

    @BizLog(bizType=BizLogConstant.TEST_HISTORY,operType="Change Matrix")
    @AccessRule(reportStatus = { ReportStatus.Approved, ReportStatus.Cancelled }, subContractType = SubContractOperationTypeEnums.updateTLStatus)
    public CustomResult updateTLStatus(UpdateTLStatusReq reqObject){
        CustomResult result = new CustomResult<>();
        if (StringUtils.isBlank(reqObject.getTestLineInstanceId())) {
            result.setSuccess(false);
            result.setMsg( "params is Empty");
            return result;
        }
        TestLineInstancePO po = testLineMapper.getTestLineInfo(reqObject.getTestLineInstanceId());
        if (po == null) {
            result.setSuccess(false);
            result.setMsg( "Get data error");
            return result;
        }
        GeneralOrderInstanceInfoPO order = generalOrderInstanceInfoMapper.selectByPrimaryKey(po.getGeneralOrderInstanceID());
        //校验当前订单是否是内部分包被锁定订单
        List<SubcontractDTO> subcontractDTOS = subContractExtMapper.querySubContractListByTLIds(Lists.newArrayList(reqObject.getTestLineInstanceId()));
        if(CollectionUtils.isNotEmpty(subcontractDTOS)){
            //有做分包
            SubcontractDTO subcontractDTO = subcontractDTOS.get(0);
            String subContractNo = subcontractDTO.getSubContractNo();
            SubContractExample example=new SubContractExample();
            example.createCriteria().andSubContractNoEqualTo(subContractNo);

            List<SubContractPO> subContractPOs = subContractMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(subContractPOs)){
                SubContractPO subContractPO = subContractPOs.get(0);
                Integer subContractOrder = subContractPO.getSubContractOrder();
                //说明是内部分包，需要进行校验
                if(subContractOrder!=null && subContractOrder.compareTo(1)==0){
                    Integer dataLock = subContractPO.getDataLock();
                    if(dataLock!=null && dataLock.compareTo(1)==0){
                        result.setSuccess(false);
                        result.setMsg(" The TL was locked. Please notice Subcontract To Lab to unlock first.");//order.getOrderNo() +
                        return result;
                    }
                }
            }
        }

//        result.setSuccess(transactionTemplate.execute((tranStatus)->{
//            Integer status = po.getTestLineStatus();
//            if (TestLineStatus.check(status,TestLineStatus.Submit,TestLineStatus.Completed)){
//                // DIG-6914 change 时 不更改 TestLine 状态
////                List<TestLineInstancePO> testLines=Lists.newArrayList();
////                TestLineInstancePO testLineInstancePO=new TestLineInstancePO();
////                testLineInstancePO.setTestLineStatus(TestLineStatus.Entered.getStatus());
////                testLineInstancePO.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
////                testLineInstancePO.setModifiedDate(DateUtils.getNow());
////                testLineInstancePO.setID(po.getID());
////                testLines.add(testLineInstancePO);
////                testLineStatusService.batchUpdateTestLineStatus(TestLineModuleType.Change, testLines);
//                if (StringUtils.isNotBlank(reqObject.getSubcontractId())){
//                    SubContractPO subContract = new SubContractPO();
//                    subContract.setID(reqObject.getSubcontractId());
//                    subContract.setStatus(1);
//                    subContract.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
//                    subContract.setModifiedDate(DateUtils.getNow());
//                    subContractMapper.updateByPrimaryKeySelective(subContract);
//                }
//                // DIG-2234
//                // add by vincent POSL 1137 2018年7月26日
//                // modify by vincent 2018年12月7日 最新需求，在点击Change按钮修改TL状态时，不再重置job状态
//                // orderStatusChangeUtil.updateJobStatusByChangeTLstatus(token, id);
//                SysStatusReq reqStatus = new SysStatusReq();
//                reqStatus.setObjectNo(order.getOrderNo());
//                reqStatus.setOldStatus(com.sgs.preorder.facade.model.enums.OrderStatus.Reporting.getStatus());
//                reqStatus.setNewStatus(com.sgs.preorder.facade.model.enums.OrderStatus.Testing.getStatus());
//                reqStatus.setUserName(UserHelper.getLocalUser().getRegionAccount());
//                statusClient.insertStatusInfo(reqStatus);
//            }
//            return true;
//        }));
//        BizLogHelper.setValue(order.getOrderNo(), po.getTestLineID());
        result.setSuccess(true);
        return result;
    }

    @AccessRule(reportStatus = { ReportStatus.Approved, ReportStatus.Cancelled })
    public CustomResult getTlTemplateData(TlTemplateReq tlTemplateReq){
        CustomResult rspResult = new CustomResult();
        TestLineExportRsp testLineExportRsp=new TestLineExportRsp();
        GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO= generalOrderInstanceInfoMapper.selectByPrimaryKey(tlTemplateReq.getGeneralOrderInstanceID());
        TestSampleInfoExample testSampleInfoExample=new TestSampleInfoExample();
        testSampleInfoExample.createCriteria().andOrderNoEqualTo(generalOrderInstanceInfoPO.getOrderNo()).andActiveIndicatorEqualTo(true);
        List<TestSampleInfoPO> testSampleInfoPOS= testSampleInfoMapper.selectByExample(testSampleInfoExample);
        List<TestSampleRsp> testSampleRsps=Lists.newArrayList();
        for(TestSampleInfoPO samplePO:testSampleInfoPOS){
            TestSampleRsp testSampleRsp=new TestSampleRsp();
            BeanUtils.copyProperties(samplePO,testSampleRsp);
            // DIG-9263 修复 下载模板中NC 标记未展示问题
            testSampleRsp.setApplicable(samplePO.getApplicable() ? 1 : 0);
            testSampleRsp.setId(samplePO.getID());
            testSampleRsps.add(testSampleRsp);
        }
        testSampleInfoPOS.forEach(testSampleInfoPO -> {
            testSampleInfoPO.setCategory(StringUtils.isBlank(testSampleInfoPO.getCategory())?SampleType.OriginalSample.getCategoryPhy():testSampleInfoPO.getCategory());
        });
        Set<String> testSampleIds = testSampleInfoPOS.stream().map(TestSampleInfoPO::getID).collect(Collectors.toSet());
        List<TestSampleLangInfoPO> langInfoPOS = testSampleLangMapper.getTestSampleListIds(testSampleIds);
        if(CollectionUtils.isNotEmpty(langInfoPOS)){
            Map<String,List<TestSampleLangInfoPO>> sampleLangMap = langInfoPOS.stream().collect(Collectors.groupingBy(TestSampleLangInfoPO::getSampleId));

            testSampleRsps.forEach(x -> {
                List<TestSampleLangInfoPO> langs = sampleLangMap.get(x.getId());
                x.setMaterial(testSampleLangService.getMaterialText(langs,"\\",Lists.newArrayList(LanguageType.EN.getLanguageId(),LanguageType.CHI.getLanguageId()),","));
            });
        }

        testLineExportRsp.setSampleList(testSampleRsps);
        OrderTestLineReq reqObject=new OrderTestLineReq();
        reqObject.setOrderId(tlTemplateReq.getGeneralOrderInstanceID());
        CustomResult<List<OrderTestLineRsp>>  customResult=this.getTestLineListByOrderId(reqObject);
        testLineExportRsp.setQueryTestLineRsps(customResult.getData());
        testLineExportRsp.setOrderNo(generalOrderInstanceInfoPO.getOrderNo());
        testLineExportRsp.setCustomerGroupName(generalOrderInstanceInfoPO.getCustomerGroupName());
        OrderIdReq reqOrder = new OrderIdReq();
        reqOrder.setOrderNo(generalOrderInstanceInfoPO.getOrderNo());

        SlOrderInfoRep slOrderInfoRep= orderClient.getSlOrderInfoByOrderId(reqOrder);
        testLineExportRsp.setCr(StringUtils.isNoneBlank(slOrderInfoRep.getCr()) ? slOrderInfoRep.getCr() : "");
        testLineExportRsp.setOr(StringUtils.isNoneBlank(slOrderInfoRep.getOr()) ? slOrderInfoRep.getOr() : "");
        testLineExportRsp.setOrderExpectDueDate(slOrderInfoRep.getOrderExpectDueDate());
        rspResult.setData(testLineExportRsp);
        rspResult.setSuccess(true);
        return rspResult;
    }

    private String nullToWhitespace(String str){
        if (str == null){
            return " ";
        }else {
            return str;
        }
    }

    public CustomResult getConfirmMatrixGemoData(GemoReq gemoReq){
        CustomResult customResult=new CustomResult();
        List<GemoRsp> gemoRsps= testLineMapper.getConfirmMatrixGemoTestLine(gemoReq);
        if(gemoRsps.isEmpty()) {
            customResult.setSuccess(true);
            customResult.setData(Lists.newArrayList());
            return  customResult;
        }

        // POSL-3843  CSPP Category的取值逻辑，取CSPP下面第一个TL相关的属性
        gemoRsps.forEach(gemo -> {
            if (!TestLineType.check(gemo.getTestLineType(), TestLineType.CSPP)) {
                return;
            }
            QueryPpTestLineReq req = new QueryPpTestLineReq();
            req.setPpVersionId(gemo.getTestLineVersionID());
            List<QueryPpTestLineRsp> ppTestLineList = ppClient.getPpTestLineList(req);
            if (CollectionUtils.isEmpty(ppTestLineList)) {
                return;
            }
            QueryPpTestLineRsp queryPpTestLineRsp = ppTestLineList.get(0);
            gemo.setTestLineBaseId(queryPpTestLineRsp.getTestLineBaseId());
        });

        // gemo DIG-6331
        // 本地化 testLineName 别名修改
        gemoRsps.forEach(gemoRsp -> {
            testLineLocalService.build(gemoRsp.getOrderNo(), Arrays.asList(gemoRsp));
        });
        setGemoDTO(gemoRsps,gemoReq.getCustomerCode());

        customResult.setSuccess(true);
        customResult.setData(gemoRsps);
        return  customResult;
    }
    @Autowired
    private ReportService reportService;

    public CustomResult<List<GemoRsp>> getSoftcopyGemoData(GemoReq gemoReq) {
        List<String> reportNos = gemoReq.getReportNos();
        CustomResult<List<GemoRsp>> customResult = new CustomResult<>();
        customResult.setSuccess(true);
        if (CollectionUtils.isEmpty(reportNos)) {
            return customResult;
        }
        ReportInfoExample example = new ReportInfoExample();
        example.createCriteria().andReportNoIn(reportNos);
        List<ReportInfoPO> reportInfoPOS = reportInfoMapper.selectByExample(example);
        List<GemoRsp> gemoDTOs = Lists.newArrayList();
        for (ReportInfoPO report : reportInfoPOS) {
            CustomResult<List<GetTestLineConclusionRsp>> testLineConclusionResult = reportService.getTestLineConclusion(report.getReportNo());
            List<GetTestLineConclusionRsp> testLineConclusionRsps = testLineConclusionResult.getData();
            if (CollectionUtils.isEmpty(testLineConclusionRsps)) {
                continue;
            }
            GetTestLineConclusionRsp testLineConclusionRsp = testLineConclusionRsps.get(0);
            List<ReportTestLineConclusionDTO> distinctTestLineList = testLineConclusionRsp.getDistinctTestLineList();
            if (CollectionUtils.isEmpty(distinctTestLineList)) {
                customResult.setMsg(String.format("Conclusion(602) of ReportNo [%s] must not be Null ", report.getReportNo()));
                customResult.setSuccess(false);
                return customResult;
            }
            for (ReportTestLineConclusionDTO testLineConclusionDTO : distinctTestLineList) {
                GemoRsp gemoRsp = new GemoRsp();
                gemoRsp.setTestLineID(testLineConclusionDTO.getTestLineID());
                gemoRsp.setEvaluationAlias(testLineConclusionDTO.getTestItem());
                PriorityLevel levelFromMessage = PriorityLevel.getLevelFromMessage(testLineConclusionDTO.getTestLineConclusion());
                gemoRsp.setDescription(levelFromMessage == null ? testLineConclusionDTO.getTestLineConclusion() : levelFromMessage.getResultMsg());
                gemoRsp.setOrderNo(report.getOrderNo());
                gemoRsp.setTestLineVersionID(testLineConclusionDTO.getTestLineVersionId());
                gemoRsp.setTestLineInstanceId(testLineConclusionDTO.getTestLineInstanceID());
                gemoRsp.setTestLineBaseId(testLineConclusionDTO.getTestLineBaseId());
                gemoRsp.setTestLineType(testLineConclusionDTO.getTestLineType());
                gemoRsp.setPpVersionId(testLineConclusionDTO.getPpVersionId());
                gemoDTOs.add(gemoRsp);
            }
        }

        // POSL-3843  CSPP Category的取值逻辑，取CSPP下面第一个TL相关的属性
        gemoDTOs.forEach(gemo -> {
            if (!TestLineType.check(gemo.getTestLineType(), TestLineType.CSPP)) {
                return;
            }
            QueryPpTestLineReq req = new QueryPpTestLineReq();
            req.setPpVersionId(gemo.getTestLineVersionID());
            List<QueryPpTestLineRsp> ppTestLineList = ppClient.getPpTestLineList(req);
            if (CollectionUtils.isEmpty(ppTestLineList)) {
                return;
            }
            QueryPpTestLineRsp queryPpTestLineRsp = ppTestLineList.get(0);
            gemo.setTestLineBaseId(queryPpTestLineRsp.getTestLineBaseId());
        });


        if (CollectionUtils.isNotEmpty(gemoDTOs)) {
            setGemoDTO(gemoDTOs, gemoReq.getCustomerCode());
        }
        customResult.setData(gemoDTOs);

        return customResult;
    }

    private void setGemoDTO(List<GemoRsp> gemoDTOs,String customerCode) {
        Set<Long> testLineBaseSet = Sets.newHashSet();
        gemoDTOs.forEach(gemoDTO -> {
            testLineBaseSet.add(gemoDTO.getTestLineBaseId());
        });

        List<CustomerTestCategoryRsp> customerTestCategoryInfo = customerConclusionClient.getCustomerTestCategoryInfo(testLineBaseSet, 2431676);

//        TrimsTestLineReq reqObject=new TrimsTestLineReq();
//        reqObject.setVersionIdentifiers(testLineVersionSet);
//        reqObject.setCustomerType("Customer");
//        reqObject.setCustomerCode("2431676");
//        List<TestLineInfo> testLineList = trimsClient.getTestLineList(reqObject);
        Map<Long, CustomerTestCategoryRsp> testLineMap =Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(customerTestCategoryInfo)) {
            customerTestCategoryInfo.forEach(testLine -> {
                testLineMap.put(testLine.getTestLineBaseId(), testLine);
            });
        }
        gemoDTOs.forEach(gemoDTO -> {
            CustomerTestCategoryRsp customerTestCategoryRsp = testLineMap.get(gemoDTO.getTestLineBaseId());
            gemoDTO.setCustomerTestCategoryId(customerTestCategoryRsp == null ? "" : customerTestCategoryRsp.getCustomTestCategoryId().toString());
            gemoDTO.setCustomerTestCategoryName(customerTestCategoryRsp == null ? "" : customerTestCategoryRsp.getCustomTestCategoryName());
        });
    }

//    @TestLinePending(filedName = "testLineInstanceId",type=TestLinePendingTypeEnums.TL_ID)
    @AccessRule(testLinePendingType = TestLinePendingTypeEnums.TestLineInstanceId)
    public CustomResult<Object> checkTestLineSubContractStatus(SingleTestLienReq req) {
        CustomResult result = new CustomResult();

        String testLineInstanceId = req.getTestLineInstanceId();

        Map<String,Object> map = new HashMap<>();
        List<SubContractTestLineMappingPO> mapping=subContractExtMapper.queryMappingByTestLineIds(Arrays.asList(testLineInstanceId));

        if(CollectionUtils.isNotEmpty(mapping)){
            SubContractPO subContractPO=subContractMapper.selectByPrimaryKey(mapping.get(0).getSubContractID());
            Integer dataLock = subContractPO.getDataLock();
            map.put("dataLock",dataLock);
            if(subContractPO.getStatus() == 2){
                String subContractID = mapping.get(0).getSubContractID();
                map.put("subContractID",subContractID);
            }
            String subContractNo = subContractPO.getSubContractNo();
            Map<String,Object> param = new HashMap<>();
            param.put("subContractNo",subContractNo);
            SubContractExternalRelationshipPO objSubContractExternalRelationshipPO =new SubContractExternalRelationshipPO();
            objSubContractExternalRelationshipPO.setSubContractNo(subContractNo);
            SubContractExternalRelationshipPO slimJobPO = subContractExtMapper.querySubSlimJobByOrderNo(objSubContractExternalRelationshipPO);
            if(slimJobPO!=null && slimJobPO.getSubContractType()!=null && slimJobPO.getSubContractType().compareTo(1)==0){
                map.put("tlIsInternalSub",true);
            }
            result.setData(map);
        }
        result.setSuccess(true);
        return result;
    }

    public List<TestLineInstancePO> getTestLineByOrderIdAndStatus(TestLineStatusReq testLine) {
        return testLineMapper.getTestLineByOrderIdAndStatus(testLine);
    }

    /**
     * 获取 TestLine 或 PP 的中文名称
     *
     * @param getTestLinePPNameZHReq
     * @return
     */
    public CustomResult<List<GetTestLinePPNameZHRsp>> getTestLinePPNameZH(GetTestLinePPNameZHReq getTestLinePPNameZHReq) {
        CustomResult result = new CustomResult();
        result.setSuccess(true);
        if (StringUtils.isBlank(getTestLinePPNameZHReq.getOrderNo())) {
            result.setSuccess(false);
            result.setMsg("OrderNo params is Empty");
            return result;
        }
        Map<String, List<GetTestLinePPNameZHRsp>> nameZhRspMap = new HashMap<>();
        // 获取所有的TestLineId PP的对应中文名
        GeneralOrderInstanceInfoPO orderInstanceInfoPO = orderMapper.getOrderInfo(getTestLinePPNameZHReq.getOrderNo());
        if (orderInstanceInfoPO == null) {
            return result.fail("请确认OrderNo是否正确，或是否已操作ToTest!");
         }
        List<PpTestLineRelCitationInfo> relCitationInfos = ppTestLineRelMapper.getPPTestLineRelCitationInfoByOrderId(Lists.newArrayList(orderInstanceInfoPO.getID()),Lists.newArrayList());
        List<GetTestLineEvaluationAliasItemReq> ids = Lists.newArrayList();
        relCitationInfos.forEach(x->{
            GetTestLineEvaluationAliasItemReq itemReq = new GetTestLineEvaluationAliasItemReq();
            itemReq.setPpArtifactRelId(x.getPpArtifactRelId());
            itemReq.setCitationBaseId(x.getCitationBaseId());
            ids.add(itemReq);
        });
        List<TestLineEvaluationAliasRsp> aliasRsps = testLineClient.getTestLineEvaluationAlias(ids);
        List<GetTestLinePPNameZHRsp> testLineNameZHRspList = relCitationInfos.stream().map(x->{
            GetTestLinePPNameZHRsp testLinePPNameZHRsp = new GetTestLinePPNameZHRsp();
            testLinePPNameZHRsp.setTestLineInstanceId(x.getTestLineInstanceId());
            testLinePPNameZHRsp.setTestLineId(String.valueOf(x.getTestLineID()));
            testLinePPNameZHRsp.setCreateTime(x.getCreatedDate());
            testLinePPNameZHRsp.setPpArtifactRelId(x.getPpArtifactRelId());
            testLinePPNameZHRsp.setPpBaseId(x.getPpBaseId());
            Optional<TestLineEvaluationAliasRsp> optional = aliasRsps.stream().filter(aliasRsp-> aliasRsp.getPpArtifactRelId() != 0 && aliasRsp.getPpArtifactRelId() == x.getPpArtifactRelId().intValue()).findFirst();
            if(!optional.isPresent()){
                optional = aliasRsps.stream().filter(aliasRsp-> aliasRsp.getCitationBaseId() == x.getCitationBaseId().intValue()).findFirst();//aliasRsp.getCitationBaseId() == x.getCitationBaseId().intValue()
            }
            if(optional.isPresent()){
                List<TestLineEvaluationAliasLanguageRsp> list = optional.get().getLanguages();
                if(CollectionUtils.isNotEmpty(list)){
                    testLinePPNameZHRsp.setEvaluationName(list.get(0).getEvaluationAlias());
                }
            }

            return testLinePPNameZHRsp;
        }).collect(Collectors.toList());

        List<GetTestLinePPNameZHRsp> finalList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(testLineNameZHRspList)) {
            //testLineLocalService.build(getTestLinePPNameZHReq.getOrderNo(),testLineNameZHRspList,true,false);
            Map<String,List<GetTestLinePPNameZHRsp>> map = testLineNameZHRspList.stream().collect(Collectors.groupingBy(GetTestLinePPNameZHRsp::getTestLineId));
            map.forEach((s, getTestLinePPNameZHRsps) -> {
                List<GetTestLinePPNameZHRsp> filList = getTestLinePPNameZHRsps.stream().filter(x-> NumberUtil.toInt(x.getPpArtifactRelId())>0 && StringUtils.isNotBlank(x.getEvaluationName())).sorted(Comparator.comparing(GetTestLinePPNameZHRsp::getCreateTime)).collect(Collectors.toList());
                if(filList.isEmpty()){
                    filList = getTestLinePPNameZHRsps.stream().filter(x-> StringUtils.isNotBlank(x.getEvaluationName())).sorted(Comparator.comparing(GetTestLinePPNameZHRsp::getCreateTime)).collect(Collectors.toList());
                }
                if(CollectionUtils.isNotEmpty(filList)){
                    finalList.add(filList.get(0));
                }
            });
            nameZhRspMap.put("TLZHList", finalList);
        }
        result.setData(nameZhRspMap);
        result.setSuccess(true);
        return result;
    }

    public CustomResult<List<ProductLineRsp>> getBuCodeList() {
        CustomResult<List<ProductLineRsp>> result = new CustomResult<>();
        String redis_key = "Notes_get_all_bu_list";
        Object o = redisHelper.get(redis_key);
        List<ProductLineRsp> productLineRsps = Lists.newArrayList();
        if(o==null){
            productLineRsps = trimsClient.queryAllProductLine();
            redisHelper.set(redis_key,productLineRsps,4 * 3600L);
        }else{
            productLineRsps = (List<ProductLineRsp>) o;
        }
        try{
            String currentLabCode = tokenClient.getUser().getCurrentLabCode();
            for (ProductLineRsp rsp : productLineRsps) {
                String productLineAbbr = rsp.getProductLineAbbr();
                rsp.setDefaultAbbr(StringUtils.equalsIgnoreCase(productLineAbbr, ProductLineUtil.getProductLineCode(currentLabCode).getProductLineAbbr()));//labCode[1]
            }
        }catch (Exception e){
            logger.error("Matrix table 添加TL时，获取用户当前labCode异常");
        }
        result.setSuccess(true);
        result.setData(productLineRsps);
        return result;
    }

    /**
     *
     * @param orderId
     * @return
     */
    public <T> List<T> getIncludeCompletedTLId(String orderId, Map<String, T> maps){
        return this.getIncludeCompletedTLId(orderId, null, maps);
    }

    /**
     *
     * @param orderId
     * @param testLines
     * @param maps
     * @param <T>
     * @return
     */
    public <T> List<T> getIncludeCompletedTLId(String orderId, List<TestLineStatusRsp> testLines, Map<String, T> maps){
        if (testLines == null || testLines.isEmpty()){
            testLines = testLineMapper.getTestLineStatusByOrderId(orderId);
        }
        if (testLines == null || testLines.isEmpty()){
            return Lists.newArrayList(maps.values());
        }
        for (TestLineStatusRsp testLine: testLines) {
            if (TestLineStatus.check(testLine.getTestLineStatus(), TestLineStatus.Completed)){
                maps.remove(testLine.getTestLineInstanceId());
                continue;
            }
        }
        return Lists.newArrayList(maps.values());
    }

    /**
     *
     * @param lists
     * @param predicate
     * @param <T>
     * @return
     */
    public static <T> List<T> filter(List<T> lists, Predicate<T> predicate){
        if (lists == null || lists.isEmpty()){
            return Lists.newArrayList();
        }
        if (predicate == null){
            return lists;
        }
        return lists.stream().filter(predicate).collect(Collectors.toList());
    }

    public CustomResult<List<QueryTestLineByOrderNoRsp>> queryTestLineByOrderNo(TestLineBreakDownReq req){
        CustomResult rspResult = new CustomResult();
        if(Objects.isNull(req) || StringUtils.isBlank(req.getOrderNo())){
            return rspResult.fail("参数不能为空");
        }
        List<QueryTestLineByOrderNoRsp> list = testLineMapper.queryTestLineByOrderNo(req.getOrderNo());
        rspResult.setData(list);
        rspResult.setSuccess(true);
        return rspResult;
    }

    public CustomResult sortTestLine(TestLineBreakDownReq reqObject){
        logger.info("sortTestLine reqParam:{}",JSON.toJSONString(reqObject));
        CustomResult rspResult = new CustomResult();
        String orderNo = reqObject.getOrderNo();

        Integer languageId = orderReportClient.getReportLangByOrderNo(orderNo);
        TemplatePO templatePO = templateExtMapper.selectTempleteInfoByOrderNo(orderNo);
        if(templatePO == null){
            return rspResult.fail("query TemplatePO is null");
        }
        List<TestResultTemplateInfo> resultTemplateInfos = Lists.newArrayList();
        if(TemplateRequestTypeEnum.check(templatePO.getRequestType(),TemplateRequestTypeEnum.ByCustomer)){
            resultTemplateInfos = reportTemplateExtMapper.getReportTemplateInfoCustomer(templatePO.getID());
        }else {
            resultTemplateInfos = reportTemplateExtMapper.getReportTemplateInfoTemplate(templatePO.getID());
        }
        if (CollectionUtils.isEmpty(resultTemplateInfos)) {
            return rspResult.fail("query TemplatePO is null");
        }
        TestResultTemplateInfo resultTemplateInfo = resultTemplateInfos.get(0);

        boolean hasPpTem = resultTemplateInfo.getSectionCodes().contains(ReportTemplateSectionCodeEnum.PP.getCode())?true:false;//是否有PP模板
        boolean isSpecial = NumberUtil.toInt(resultTemplateInfo.getSpecialSequenceFlag())==0?false:true;
        boolean isOB = (NumberUtil.toInt(languageId) == ReportLanguage.ChineseReportOnly.getLanguageId()
                || NumberUtil.toInt(languageId) == ReportLanguage.ChineseAndEnglishReport.getLanguageId())//ReportLanguage.ChineseAndEnglishReport
                && TestResultLanguageType.checkType(resultTemplateInfo.getTestResultLanguageType(),TestResultLanguageType.Follow_order_language)?false:true;
        Integer templateLanguage = LanguageType.EN.getLanguageId();
        if(TestResultLanguageType.checkType(resultTemplateInfo.getTestResultLanguageType(),TestResultLanguageType.Follow_order_language)){
            templateLanguage = languageId;
        }

        logger.info("sortTestLine -> orderNO:{},hasPpTem:{},isSpecial:{},isOB:{},templateLanguage:{}",orderNo,hasPpTem,isSpecial,isOB,templateLanguage);


        GeneralOrderInstanceInfoPO orderInstanceInfoPO = orderMapper.getOrderInfo(orderNo);
        List<TestLinePpInfo> testLinePpInfos = testLineInstanceExtMapper.queryPpTestLineInfoByOrderNo(reqObject.getOrderNo(),templateLanguage);
        if(CollectionUtils.isEmpty(testLinePpInfos)){
            return rspResult.fail(String.format("根据orderNo:{} 查询TestLine(queryPpTestLineInfoByOrderNo)为空",orderNo));
        }

        Set<String> testLineInstanceIds = testLinePpInfos.stream().map(TestLinePpInfo::getTestLineInstanceId).collect(Collectors.toSet());
        List<TestLinePpInfo> subContractLabs = testLineInstanceExtMapper.querySubContractByTestLineInstanceIds(testLineInstanceIds);
        Map<String,TestLinePpInfo> subContractMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(subContractLabs)){
            subContractMap = subContractLabs.stream().collect(Collectors.toMap(TestLinePpInfo::getTestLineInstanceId,Function.identity(),(o1,o2)->o1));
        }

        Set<Long> ppBaseIds = Sets.newHashSet(),
        ppArtifactRelIds = Sets.newHashSet();
        List<Long> labSectionBaseIds = Lists.newArrayList();
        Set<Long> citationBaseIds = Sets.newHashSet();
        List<Integer> testLineIds = Lists.newArrayList();
        for (TestLinePpInfo ppTestLineRel : testLinePpInfos) {

            long ppBaseId = NumberUtil.toLong(ppTestLineRel.getPpBaseId());
            if (ppBaseId > 0) {
                ppBaseIds.add(ppBaseId);
            }

            long ppArtifactRelId = NumberUtil.toLong(ppTestLineRel.getPpArtifactRelId());
            if (ppArtifactRelId > 0) {
                ppArtifactRelIds.add(ppArtifactRelId);
            }
            long citationBaseId = NumberUtil.toLong(ppTestLineRel.getCitationBaseId());
            if (citationBaseId > 0) {
                citationBaseIds.add(citationBaseId);
            }
            long labSectionBaseId = NumberUtil.toLong(ppTestLineRel.getLabSectionBaseId());
            if(labSectionBaseId > 0){
                labSectionBaseIds.add(labSectionBaseId);
            }
            TestLinePpInfo subcontractInfo = subContractMap.get(ppTestLineRel.getTestLineInstanceId());
            if(Objects.nonNull(subcontractInfo)){
                ppTestLineRel.setSubContractLabCode(subcontractInfo.getSubContractLabCode());
            }
            testLineIds.add(ppTestLineRel.getTestLineId());
        }

        testLineIds = testLineIds.stream().distinct().collect(Collectors.toList());


        Map<Long,GetPpBaseInfoRsp> ppMap = Maps.newHashMap();
        Set<Integer> ppNos = Sets.newHashSet();
        if(CollectionUtils.isNotEmpty(ppBaseIds)){
            List<GetPpBaseInfoRsp> pps = ppClient.getPpBaseInfoList(ppBaseIds);
            ppMap = pps.stream().collect(Collectors.toMap(GetPpBaseInfoRsp::getPpBaseId,Function.identity(),(o1,o2)->o1));
            ppNos = pps.stream().map(GetPpBaseInfoRsp::getPpNo).collect(Collectors.toSet());
        }

        Map<Long,GetCitationBaseInfoRsp> citationMap = Maps.newHashMap();
        List<GetCitationBaseInfoRsp> citations = citationClient.getCitationBaseInfo(citationBaseIds,Lists.newArrayList(LanguageType.EN.getLanguageId(),LanguageType.CHI.getLanguageId()));
        if(CollectionUtils.isNotEmpty(citations)){
            citationMap = citations.stream().collect(Collectors.toMap(GetCitationBaseInfoRsp::getCitationBaseId,Function.identity(),(o1,o2)->o1));
        }

        Map<Long,GetLabSectionBaseInfoRsp> labMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(labSectionBaseIds)){
            List<GetLabSectionBaseInfoRsp> labSections = labSectionClient.getLabSectionBaseInfo(labSectionBaseIds);
            labMap = labSections.stream().collect(Collectors.toMap(GetLabSectionBaseInfoRsp::getLabSectionBaseId,Function.identity(),(o1,o2)->o1));
        }
        Map<Long,PpArtifactInfoRsp> artMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(ppArtifactRelIds)){
            CustomResult<List<PpArtifactInfoRsp>> ppArtifactRels = ppArtifactRelClient.getPpArtifactBaseInfoList(ppArtifactRelIds,Lists.newArrayList(LanguageType.EN.getLanguageId(),LanguageType.CHI.getLanguageId()));
            if(!ppArtifactRels.isSuccess() && CollectionUtils.isNotEmpty(ppArtifactRels.getData())){
                logger.info("TestLineService.sortTestLine(getPpArtifactBaseInfoList) error,errorMsg:{}",ppArtifactRels.getMsg());
                return rspResult.fail(ppArtifactRels.getMsg());
            }
            artMap = ppArtifactRels.getData().stream().collect(Collectors.toMap(PpArtifactInfoRsp::getPpArtifactRelId,Function.identity(),(o1,o2)->o1));
        }

        TestDataQueryReq queryReq = new TestDataQueryReq();
        queryReq.setLabCode(orderInstanceInfoPO.getLabCode());
        queryReq.setSourceTypes(Lists.newArrayList(SourceTypeEnum.SLIM.getCode()));
        queryReq.setOrderNo(orderNo);
        queryReq.setTestLineIds(testLineIds);
        queryReq.setProductLineCode(StringUtils.defaultString(ProductLineContextHolder.getProductLineCode(),"SL"));
        List<TestDataInfo> testDataInfos = testDataBizClient.queryTestData(queryReq);
        Map<String,List<TestDataInfo>> testDataMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(testDataInfos)){
            testDataMap = testDataInfos.stream().collect(Collectors.groupingBy(testDataInfo -> testDataInfo.getTestLineId()+"_"+testDataInfo.getPpVersionId()));
        }


        Map<Long, GetPpBaseInfoRsp> finalPpMap = ppMap;
        Map<Long, GetLabSectionBaseInfoRsp> finalLabMap = labMap;
        Map<Long, PpArtifactInfoRsp> finalArtMap = artMap;
        Map<Long, GetCitationBaseInfoRsp> finalCitationMap = citationMap;
        Map<String, List<TestDataInfo>> finalTestDataMap = testDataMap;
        testLinePpInfos.forEach(x->{
            x.setPpNo(finalPpMap.getOrDefault(x.getPpBaseId(),new GetPpBaseInfoRsp()).getPpNo());
            GetLabSectionBaseInfoRsp labSectionBaseInfoRsp = finalLabMap.getOrDefault(x.getLabSectionBaseId(),new GetLabSectionBaseInfoRsp());
            x.setLabSectionSeq(labSectionBaseInfoRsp.getLabSectionSeq());
            x.setLabSeq(StringUtils.isEmpty(labSectionBaseInfoRsp.getLabSectionName())?0:1);
            PpArtifactInfoRsp ppArtifactInfoRsp = finalArtMap.get(x.getPpArtifactRelId());
            if(Objects.nonNull(ppArtifactInfoRsp)){
                x.setTestLineSeq(ppArtifactInfoRsp.getTestLineSeq());
                x.setEvaluationAlias(ppArtifactInfoRsp.getEvaluationAlias());
            }else {
                GetCitationBaseInfoRsp baseInfoRsp = finalCitationMap.get(x.getCitationBaseId());
                x.setEvaluationAlias(baseInfoRsp.getEvaluationAlias());
            }
            x.setIfNotPP(StringUtils.isEmpty(x.getSectionLevel()) && x.getPpNo() == null ? 1:0);
            String key = x.getTestLineId()+"_"+NumberUtil.toInt(finalPpMap.getOrDefault(x.getPpBaseId(),new GetPpBaseInfoRsp()).getPpVersionId());
            List<TestDataInfo> dataInfos = finalTestDataMap.get(key);
            if(CollectionUtils.isNotEmpty(dataInfos)){
                x.setSlimTestLineSeq(dataInfos.get(0).getTestLineSeq());
            }
        });

        String customerGroupCode = "";
        CustomResult<CustomerSimplifyInfoRsp> customResult = customerClient.getCustomerSimplifyInfo(orderNo, CustomerType.Buyer);

        if(!customResult.isSuccess() ){
            logger.error("查询customerGroupCode失败,orderNo:{},errorMsg:{}",orderNo,customResult.getMsg());
            return rspResult.fail("查询customerGroupCode失败");
        }
        CustomerSimplifyInfoRsp customerInfo = customResult.getData();
        if(Objects.nonNull(customerInfo)){
            customerGroupCode = customerInfo.getCustomerGroupCode();
        }
        QueryTestLineStyleReq styleReq = new QueryTestLineStyleReq();
        styleReq.setTestLineIds(testLineIds);
        styleReq.setCustomerGroupCode(customerGroupCode);
        styleReq.setProductLineCode(StringUtils.defaultString(ProductLineContextHolder.getProductLineCode(),"SL"));
        List<TestLineStyleDto> styleDtos = testLineStyleClient.getTestLineStyle(styleReq);
        Map<Integer,TestLineStyleDto> styleMap = styleDtos.stream().collect(Collectors.toMap(TestLineStyleDto::getTestLineId,Function.identity(),(o1,o2)->o1));
        Map<Integer,TestLinePpInfo> map = Maps.newHashMap();
        testLinePpInfos.forEach(x->{
            x.setHasStyle(0);
            TestLineStyleDto styleDto = styleMap.get(x.getTestLineId());
            if(styleDto.getTestLineStyle() != null && !DataEntryStyleEnum.check(styleDto.getTestLineStyle(),DataEntryStyleEnum.NON_STYLE)){
                x.setHasStyle(1);
            }
            map.put(x.getTestLineHashCode(),x);
        });

        // DIG-9191 ppOrdering 排序
        int customerAccountId = customerClient.getCustomerAccountId(orderInstanceInfoPO.getCustomerGroupCode());
        List<PpOrderingRsp> ppOrdering = ppClient.getPPOrdering(customerAccountId, Lists.newArrayList(ppNos));

        for (TestLinePpInfo testLinePpInfo : testLinePpInfos) {
            for (PpOrderingRsp ppOrderingRsp : ppOrdering) {
                if (NumberUtil.equals(testLinePpInfo.getPpNo(), ppOrderingRsp.getPpNumber())) {
                    testLinePpInfo.setPpOrderingSeq(ppOrderingRsp.getSequence());
                    break;
                }
            }
        }

        testLinePpInfos = map.values().stream().collect(Collectors.toList());
        List<TestLinePpInfo> subTestLines = testLinePpInfos.stream().filter(testLinePpInfo -> StringUtils.isNotBlank(testLinePpInfo.getSubContractLabCode())).collect(Collectors.toList());
        subTestLines = subTestLines.stream().sorted(new TestLineSubContractComparator(true)).collect(Collectors.toList());
        testLinePpInfos.removeAll(subTestLines);
        List<TestLinePpInfo> noStyletLines = Lists.newArrayList();
        if(!isOB){
            noStyletLines = testLinePpInfos.stream().filter(testLinePpInfo -> testLinePpInfo.getHasStyle().equals(0)).collect(Collectors.toList());
            noStyletLines = noStyletLines.stream().sorted(new TestLineNoStyleComparator(true)).collect(Collectors.toList());
            testLinePpInfos.removeAll(noStyletLines);
        }
        testLinePpInfos = testLinePpInfos.stream().sorted(new TestLineComparator(true,hasPpTem,isSpecial,isOB)).collect(Collectors.toList());
        testLinePpInfos.addAll(noStyletLines);
        testLinePpInfos.addAll(subTestLines);
        rspResult.setData(testLinePpInfos);
        rspResult.setSuccess(true);
        return rspResult;
    }

    public CustomResult<UserLabBuInfo> checkAndGetLabId(String orderNo, String labCode) {
        CustomResult<UserLabBuInfo> rspResult = new CustomResult<>(true);
        OrderCrossLabRelPO crossLab = crossLabExtMapper.getCrossLabRelByOrderNo(orderNo);
        if(crossLab!=null){
            UserLabBuInfo userLabBuInfo = userManagementClient.getUserLabBuInfo(tokenClient.getToken());
            String currentLabCode = userLabBuInfo.getLabCode();
            String toLab = crossLab.getToLab();
            if(!Lists.newArrayList(labCode,toLab).contains(currentLabCode)){
                return rspResult.fail("Pls switch lab code in the upper-right corner and try again");
            }
            if(StringUtils.equalsIgnoreCase(currentLabCode,toLab)){
                rspResult.setData(userLabBuInfo);
            }
        }
        return rspResult;
    }

    /**
     * getTestLineCount
     * @param fromDate
     * @param endDate
     * @param sgsToken
     * @return
     */
    public CustomResult<Integer> getTestLineCount(String fromDate,String endDate,String sgsToken){
        Map<String, Object> queryMap = new HashMap<String, Object>();
        queryMap.put("fromDate", fromDate);
        queryMap.put("endDate", endDate);
        queryMap.put("labCode", tokenClient.getUserLabCode(sgsToken));
        CustomResult<Integer> rspResult = new CustomResult<>(true);
        rspResult.setData(testLineMapper.getTestLineCount(queryMap));
        return rspResult;
    }

}
