package com.sgs.otsnotes.domain.service.reportdata.copy;

import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.otsnotes.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.otsnotes.domain.service.reportdata.ReportDataDealService;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.ordercopy.SysCopyInfo;
import com.sgs.otsnotes.facade.model.req.subcontract.InvalidateSubcontractDataReq;
import com.sgs.otsnotes.integration.FrameWorkClient;
import com.sgs.otsnotes.integration.v2.ReportDataClientV2;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ReportDataReplaceCopyService extends ReportDataDealService {
    private static final Logger logger = LoggerFactory.getLogger(ReportDataReplaceCopyService.class);



    public ReportDataReplaceCopyService(ReportDataClientV2 subContractDataClient, FrameWorkClient frameWorkClient) {
        super(subContractDataClient, frameWorkClient);
    }




    /**
     * 处理更新ResultData场景 (Amend-Replace)
     */
    public CustomResult<List<ReportTestDataInfo>> handleUpdateResultData(SysCopyInfo reqParams) {
        CustomResult<List<ReportTestDataInfo>> rspResult = new CustomResult<>();

        logger.info("【ReportDataCopyService】orderNo:{}, 开始处理更新ResultData场景, oldReportNo: {}", reqParams.getOldReportNo());

        try {

            // 添加新的分包数据（参考新增场景）
            CustomResult<List<ReportTestDataInfo>> listCustomResult = handleAddResultData(reqParams, reqParams.getOldReportNo());
            if (!listCustomResult.isSuccess()) {
                return rspResult.fail(listCustomResult.getMsg());
            }
            if (CollectionUtils.isEmpty(listCustomResult.getData())) {
                return rspResult.success();
            }

            // 1. 使旧report下的数据无效
            InvalidateSubcontractDataReq invalidateReq = new InvalidateSubcontractDataReq();
            invalidateReq.setReportNo(reqParams.getOldReportNo());
            invalidateReq.setLabCode(reqParams.getLabBu().getLabCode());
            invalidateReq.setOrderNo(reqParams.getRootOrderNo());
            invalidateReq.setActiveIndicator(Boolean.FALSE);
            invalidateReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());

            BaseResponse<Void> invalidateResult = subContractDataClient.invalidateSubcontractData(invalidateReq);
            if (!invalidateResult.isSuccess()) {
                logger.error("【ReportDataCopyService】orderNo:{}, 使数据无效失败: {}", reqParams.getOrderNo(), invalidateResult.getMessage());
                return rspResult.fail("使调用RD数据无效失败: " + invalidateResult.getMessage());
            }

            return listCustomResult;

        } catch (Exception e) {
            logger.error("【ReportDataCopyService】orderNo:{}, 处理更新ResultData失败", e);
            return rspResult.fail("处理更新ResultData失败: " + e.getMessage());
        }
    }


}
