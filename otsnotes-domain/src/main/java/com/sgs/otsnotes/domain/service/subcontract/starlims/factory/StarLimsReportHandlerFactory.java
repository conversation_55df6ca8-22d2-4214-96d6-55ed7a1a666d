package com.sgs.otsnotes.domain.service.subcontract.starlims.factory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.otsnotes.domain.service.subcontract.starlims.annotation.StarLimsReportProcessor;
import com.sgs.otsnotes.domain.service.subcontract.starlims.context.StarLimsReportContext;
import com.sgs.otsnotes.domain.service.subcontract.starlims.handler.StarLimsReportHandler;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description StarLims报告处理器工厂
 * 负责扫描和注册处理器，并根据产品线类型获取对应的处理器链
 * <AUTHOR>
 * @Date 2024-03-21
 */
@Component
@Slf4j
public class StarLimsReportHandlerFactory implements ApplicationContextAware {
    
    /**
     * 处理器缓存
     * key: 产品线类型
     * value: 处理器列表
     */
    private final Map<ProductLineType, List<StarLimsReportHandler>> handlerCache = new ConcurrentHashMap<>();
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        // 1. 获取所有处理器
        Map<String, Object> handlerBeans = applicationContext.getBeansWithAnnotation(StarLimsReportProcessor.class);
        
        log.info("扫描到{}个StarLims报告处理器", handlerBeans.size());
        
        // 2. 注册处理器
        handlerBeans.forEach((name, bean) -> {
            if (bean instanceof StarLimsReportHandler) {
                StarLimsReportHandler handler = (StarLimsReportHandler) bean;
                StarLimsReportProcessor annotation = handler.getClass().getAnnotation(StarLimsReportProcessor.class);
                
                // 2.1 获取处理器支持的产品线
                ProductLineType[] productLines = annotation.productLines();
                List<ProductLineType> supportedLines = new ArrayList<>();
                
                // 判断是否支持所有产品线（长度为0或包含ALL类型）
                boolean supportAll = productLines.length == 0 || 
                    Arrays.asList(productLines).contains(ProductLineType.ALL);
                
                if (supportAll) {
                    // 支持所有产品线
                    supportedLines.addAll(Arrays.asList(ProductLineType.values()));
                    // 移除ALL类型，因为它只是一个标记，不是实际的产品线类型
                    supportedLines.remove(ProductLineType.ALL);
                    log.debug("处理器[{}]支持所有产品线", handler.getClass().getSimpleName());
                } else {
                    supportedLines.addAll(Arrays.asList(productLines));
                }
                
                // 2.2 处理排除的产品线
                ProductLineType[] excludeLines = annotation.excludeProductLines();
                if (excludeLines.length > 0) {
                    List<ProductLineType> excludeList = Arrays.asList(excludeLines);
                    supportedLines.removeAll(excludeList);
                    log.debug("处理器[{}]排除产品线: {}", handler.getClass().getSimpleName(), excludeList);
                }
                
                // 2.3 解析产品线特定顺序配置
                Map<ProductLineType, Integer> productLineOrders = parseProductLineOrders(annotation);
                
                // 2.4 注册处理器
                supportedLines.forEach(productLine -> {
                    List<StarLimsReportHandler> handlers = handlerCache.computeIfAbsent(
                        productLine, k -> new ArrayList<>());
                        
                    // 创建处理器包装类，包含产品线特定的顺序
                    ProductLineHandlerWrapper wrapper = new ProductLineHandlerWrapper(
                        handler, 
                        productLine,
                        productLineOrders.getOrDefault(productLine, annotation.order())
                    );
                    handlers.add(wrapper);
                    
                    log.info("注册处理器: {}, 产品线: {}, 优先级: {}, 分组: {}, 类型: {}", 
                        handler.getClass().getSimpleName(), 
                        productLine,
                        wrapper.getOrder(),
                        annotation.group(),
                        annotation.type());
                });
            }
        });
        
        // 3. 按产品线分别排序处理器链
        handlerCache.forEach((productLine, handlers) -> {
            handlers.sort(Comparator.comparingInt(StarLimsReportHandler::getOrder));
            log.debug("产品线[{}]处理器排序完成,共{}个处理器", productLine, handlers.size());
        });
        
        // 4. 打印处理器链信息
        handlerCache.forEach((productLine, handlers) -> {
            String handlerNames = handlers.stream()
                .map(h -> String.format("%s(order=%d,type=%s)", 
                    h.getHandlerName(),
                    h.getOrder(),
                    ((ProductLineHandlerWrapper)h).delegate.getType()))
                .collect(Collectors.joining(" -> "));
            log.info("产品线[{}]处理器链: {}", productLine, handlerNames);
        });
    }
    
    /**
     * 获取指定产品线的处理器链
     * @param productLineType 产品线类型
     * @return 处理器链
     */
    public List<StarLimsReportHandler> getHandlers(ProductLineType productLineType) {
        return handlerCache.getOrDefault(productLineType, new ArrayList<>());
    }
    
    /**
     * 解析产品线特定顺序配置
     * @param annotation 处理器注解
     * @return 产品线顺序映射
     */
    private Map<ProductLineType, Integer> parseProductLineOrders(StarLimsReportProcessor annotation) {
        Map<ProductLineType, Integer> orderMap = new HashMap<>();
        String[] productLineOrders = annotation.productLineOrders();
        
        if (productLineOrders == null || productLineOrders.length == 0) {
            return orderMap;
        }
        
        for (String orderConfig : productLineOrders) {
            try {
                String[] parts = orderConfig.split(":");
                if (parts.length != 2) {
                    log.warn("产品线顺序配置格式错误: {}", orderConfig);
                    continue;
                }
                
                ProductLineType productLine = ProductLineType.valueOf(parts[0]);
                int order = Integer.parseInt(parts[1]);
                orderMap.put(productLine, order);
            } catch (Exception e) {
                log.warn("解析产品线顺序配置失败: {}", orderConfig, e);
            }
        }
        
        return orderMap;
    }
    
    /**
     * 产品线处理器包装类
     * 用于在初始化时确定处理器在特定产品线中的顺序
     */
    private static class ProductLineHandlerWrapper implements StarLimsReportHandler {
        private final StarLimsReportHandler delegate;
        private final ProductLineType productLine;
        private final int order;
        
        public ProductLineHandlerWrapper(StarLimsReportHandler delegate, ProductLineType productLine, int order) {
            this.delegate = delegate;
            this.productLine = productLine;
            this.order = order;
        }
        
        @Override
        public boolean handle(StarLimsReportContext context) {
            return delegate.handle(context);
        }
        
        @Override
        public boolean supports(StarLimsReportContext context) {
            return delegate.supports(context);
        }
        
        @Override
        public int getOrder() {
            return order;
        }

        public String getHandlerName() {
            return delegate.getClass().getSimpleName();
        }
        
        @Override
        public StarLimsReportProcessor.ProcessorType getType() {
            return delegate.getType();
        }
        
        @Override
        public String getGroup() {
            return delegate.getGroup();
        }
    }
} 