package com.sgs.otsnotes.domain.service.subcontract;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.dataentry.domain.enums.ConclusionSourceTypeEnum;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.id.TypeId;
import com.sgs.framework.model.enums.*;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.grus.bizlog.common.BizLog;
import com.sgs.otsnotes.NotesCapabilityConstants;
import com.sgs.otsnotes.core.annotation.AccessRule;
import com.sgs.otsnotes.core.common.RedisHelper;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.config.StarLimsConfig;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.enums.PolicyType;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.core.util.ListUtils;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.ConclusionService;
import com.sgs.otsnotes.domain.service.SubContractService;
import com.sgs.otsnotes.domain.service.TestGroupService;
import com.sgs.otsnotes.domain.service.cache.ConclusionListService;
import com.sgs.otsnotes.domain.service.reportdata.ReportDataHandlerService;
import com.sgs.otsnotes.facade.SubReportFacade;
import com.sgs.otsnotes.facade.SubReportService;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.dto.conclusion.ConclusionExtraDto;
import com.sgs.otsnotes.facade.model.dto.subreport.SubReportDTO;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.enums.LanguageType;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.otsnotes.facade.model.enums.starlims.StarLimsCheckTypeEnums;
import com.sgs.otsnotes.facade.model.enums.starlims.StarLimsDFFLanguageEnums;
import com.sgs.otsnotes.facade.model.enums.starlims.StarLimsObjectTypeEnums;
import com.sgs.otsnotes.facade.model.enums.starlims.StarLimsTrackTypeEnums;
import com.sgs.otsnotes.facade.model.info.report.ReceiveStarLimsInfo;
import com.sgs.otsnotes.facade.model.info.starlims.StarLimsRelInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractTestMatrixInfo;
import com.sgs.otsnotes.facade.model.req.TestGroupReq;
import com.sgs.otsnotes.facade.model.req.conclusion.SubcontractConclusionBatchReq;
import com.sgs.otsnotes.facade.model.req.starlims.*;
import com.sgs.otsnotes.facade.model.req.starlims.receive.*;
import com.sgs.otsnotes.facade.model.req.subcontract.SubContractChemTestMatrixReq;
import com.sgs.otsnotes.facade.model.req.subcontract.SubcontractCompleteReq;
import com.sgs.otsnotes.facade.model.req.subcontract.SubcontractConfirmReq;
import com.sgs.otsnotes.facade.model.req.subcontract.SubcontractTestingReq;
import com.sgs.otsnotes.facade.model.rsp.starlims.ChemPpArtifactTestLineInfoRsp;
import com.sgs.otsnotes.facade.model.rsp.starlims.ReceiveStarLimsReportDocRsp;
import com.sgs.otsnotes.facade.model.starLims.temp.ReportInfoTempRsp;
import com.sgs.otsnotes.infra.repository.pp.PpTestLineAssembler;
import com.sgs.otsnotes.infra.service.TestSampleLangService;
import com.sgs.otsnotes.integration.*;
import com.sgs.otsnotes.integration.trimslocal.CitationClient;
import com.sgs.otsnotes.integration.trimslocal.PpArtifactRelClient;
import com.sgs.otsnotes.integration.v2.ReportDataClientV2;
import com.sgs.otsnotes.subcontract.app.executor.ToStarlimsExecutor;
import com.sgs.otsnotes.subcontract.tostarlims.matrix.bizprocess.bizprocess.BizFlowFacade;
import com.sgs.otsnotes.subcontract.tostarlims.matrix.bizprocess.bizprocess.StarLimsHandler;
import com.sgs.otsnotes.subcontract.inf.context.SubContractChemTestMatrixContext;
import com.sgs.preorder.facade.model.dto.dff.TestrptCoverpageDffSimpleDTO;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.req.SysStatusReq;
import com.sgs.testdatabiz.facade.model.req.starlims.ReceiveStarLimsReportReq;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.v2.ReportTestDataFacade;
import com.sgs.testdatabiz.facade.v2.ReportTestDataService;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactRsp;
import com.sgs.trimslocal.facade.model.citation.rsp.CitationVersionRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description starlims 相关业务公共service
 * <p>Body 中的objectNo 是starlims 自己的单号，对应我们subcontract_externale_rel表的externaleNo</p>
 * <p>Body 中的subcontractNo 是OTSNotes创建分包的单号，对应subcontract表的subcontractNo</p>
 *
 * @ClassName
 * <AUTHOR>
 * @Date 5/27/2021
 *
 */
@Service
@Slf4j
public class StarLimsCommonService {
    private static final Logger logger = LoggerFactory.getLogger(StarLimsCommonService.class);
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private FrameWorkClient frameWorkClient;
    @Autowired
    private LocaliLayerClient localiLayerClient;
    @Autowired
    private SubContractExtMapper subContractExtMapper;
    @Autowired
    private PPTestLineRelMapper ppTestLineRelMapper;
    @Autowired
    private PpTestLineAssembler ppTestLineAssembler;
    @Autowired
    private SampleExtMapper sampleExtMapper;
    @Autowired
    private GpoOrderClient gpoOrderClient;
    @Autowired
    private StarLimsConfig starLimsConfig;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private SubReportExtMapper subReportExtMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private SubContractRequirementExtMapper subContractRequirementExtMapper;
    @Autowired
    private SubcontractExternalRelExtMapper subcontractExternalRelExtMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private PpArtifactRelClient ppArtifactRelClient;
    @Autowired
    private CitationClient citationClient;
    @Autowired
    private DffClient dffClient;
    @Autowired
    private PPArtifactRelMapper ppArtifactRelMapper;
    @Autowired
    private TestSampleGroupMapper testSampleGroupMapper;
    @Autowired
    private OrderReportClient orderReportClient;
    @Autowired
    private ConclusionListService conclusionListService;
    @Autowired
    private StatusClient statusClient;
    @Autowired
    private FcmQuotationClient fcmQuotationClient;
    @Autowired
    private TestDataBizClient testDataBizClient;
    @Autowired
    private TestSampleLangMapper testSampleLangMapper;
    @Autowired
    private StarlimsClient starlimsClient;
    @Autowired
    private TestSampleLangService testSampleLangService;
    @Autowired
    private ReportTestDataFacade reportTestDataFacade;
    @Autowired
    private ReportTestDataService reportTestDataService;
    @Autowired
    private SubReportFacade subReportFacade;
    @Autowired
    private SubReportService subReportRemoteService;
    @Autowired
    private StarLimsHandler starLimsHandler;
    @Autowired
    private GpoNotesClient gpoNotesClient;

    @Autowired
    private ReportDataClientV2 reportDataClientV2;

    @Resource
    private SubContractService subContractService;
    @Resource
    @Lazy
    private ReportDataHandlerService reportDataHandlerService;

    @Autowired
    private RedisHelper redisHelper;

    private static final String StarLimsName = "StarLims System";

    private static final String AUTH_KEY="authId";


    @Resource
    private ConclusionService conclusionService;
    @Resource
    private TestGroupService testGroupService;

    @Resource
    private ToStarlimsExecutor toStarlimsExecutor;


    @Autowired
    @Lazy
    private BizFlowFacade bizFlowFacade;


    /**
     * 接收startDate
     *
     * @param body
     * @return
     */
    public CustomResult updateTimeTrack(ReceiveStarLimsUpdateTimeBodyReq body) {
        CustomResult result = new CustomResult<>();
        result.setSuccess(false);
        if (body == null) {
            logger.info("接收到LocalIlayer的[updateTimeTrack]请求,参数为null ");
            result.setMsg("Receive params is null ,please check the param that your send");
            return result;
        }
        /*ProductLineContextHolder.setProductLineCode(body.getProductLineCode(),true);*/

        logger.info("[updateTimeTrack],参数：{}", JSONObject.toJSONString(body));
        result = this.checkStarLimsData(body);
        if (!result.isSuccess()) {
            return result;
        }
        SubContractPO subcontractInfo = subContractService.selectBySubcontractNo(body.getSubContractNo());
        if (subcontractInfo == null) {
            logger.info("subContractNo:{}[checkSubcontractStatus] 获取不到分包信息", body.getSubContractNo());
            result.setMsg("unable to find subcontract information");
            return result;
        }

        /// 分包渠道校验？
        Integer subContractOrder = subcontractInfo.getSubContractOrder();
        if (!SubContractType.check(subContractOrder, SubContractType.ToStarLims)) {
            logger.info("subContractNo:{}[checkSubcontractStatus] 当前分包单不是starLims的分包单：subContractOrder={}",  body.getSubContractNo(), subContractOrder);
            result.setMsg("not starLIMS subcontract");
            return result;
        }

        //校验subcontract_slim表是否已经存在folderNo = externalNo
        String objectNo = body.getObjectNo();
        String orderNo = subcontractInfo.getOrderNo();
        String subContractNo = subcontractInfo.getSubContractNo();
        //当前请求的类型
        /*
        add by vincent 2022年3月21日
            添加校验，当数据库 ext_relationship中不存在当前请求的folderNo（objectNo）的时候，
            不能进行FolderCreation 之外的操作 ，
            否则后面的补偿永远进不来
        */
        String trackType = body.getTrackType();


        //TODO 策略模式改造
        //FolderCreation
        if(StarLimsTrackTypeEnums.check(trackType, StarLimsTrackTypeEnums.FolderCreation)){
            SubcontractConfirmReq subcontractConfirmReq = new SubcontractConfirmReq();
            subcontractConfirmReq.setSubcontractNo(subContractNo);
            subcontractConfirmReq.setExternalNo(objectNo);
            subcontractConfirmReq.setOpRegionAccount(StarLimsName);
            subcontractConfirmReq.setSubContractType(SubContractType.ToStarLims.getType());
            CustomResult<Boolean> createRelationshipResult = subContractService.confirmSubcontract(subcontractConfirmReq);
            //已存在关忽略创建关系
            if (!createRelationshipResult.isSuccess()) {
                result.fail(createRelationshipResult.getMsg());
                result.setSuccess(false);
                return result;
            }
            result.setSuccess(true);
            return result;
        }
        else if(StarLimsTrackTypeEnums.check(trackType, StarLimsTrackTypeEnums.CommitFolder)){
            SubcontractTestingReq subcontractTestingReq = new SubcontractTestingReq();
            // 需要更新startDate
            subcontractTestingReq.setSubcontractNo(subContractNo);// where
            subcontractTestingReq.setStartDate(body.getOperationDate()!=null? DateUtils.parseDate(body.getOperationDate()):null);
            subcontractTestingReq.setOpRegionAccount(StarLimsName);
            subcontractTestingReq.setSubContractType(SubContractType.ToStarLims.getType());
            subcontractTestingReq.setExternalNo(objectNo);
            CustomResult<Boolean> testingResult = subContractService.testingSubcontract(subcontractTestingReq);
            if (!testingResult.isSuccess()) {
                result.fail(testingResult.getMsg());
                result.setSuccess(false);
                return result;
            }
            result.setSuccess(true);
            return result;
        }else if(StarLimsTrackTypeEnums.check(trackType, StarLimsTrackTypeEnums.FolderReported)){
            Date completedDate = Optional.ofNullable(DateUtils.parseDate(body.getOperationDate()) ).orElse(DateUtils.getNow());
            //非SL：走GPO
            if(!ProductLineType.SL.getProductLineAbbr().equals(body.getProductLineCode())){
//                com.sgs.otsnotes.integration.dto.SubcontractCompleteReq completeReq = new com.sgs.otsnotes.integration.dto.SubcontractCompleteReq();
//                completeReq.setSubContractNo(subContractNo);
//                completeReq.setProductLineCode(body.getProductLineCode());
//                completeReq.setDatetime(DateUtil.formatDate(completedDate));
//                CustomResult gpoResult = gpoNotesClient.completeSubcontract(completeReq);
//                if(!gpoResult.isSuccess()){
//                    result.fail(gpoResult.getMsg());
//                    return result;
//                }
            }
            //SL
            else{
                SubcontractCompleteReq subcontractCompleteReq  = new SubcontractCompleteReq();
                subcontractCompleteReq.setSubcontractNo(subContractNo);
                subcontractCompleteReq.setOpRegionAccount(StarLimsName);
                subcontractCompleteReq.setCompleteDate(completedDate);
                subcontractCompleteReq.setProductLineCode(body.getProductLineCode());
                CustomResult<Boolean> customResult = subContractService.completeSubcontract(subcontractCompleteReq);
                if(!customResult.isSuccess()){
                    result.fail(customResult.getMsg());
                    return result;
                }
            }



            result.setSuccess(true);
            return result;
        }else{
            result.fail("trackType invalid ");
            return result;
        }
    }

    /**
     * 接收报告
     * @param body
     * @return
     */
    public CustomResult receiveReportDoc(ReceiveStarLimsReportDocBodyReq body) {
        CustomResult rspResult = new CustomResult();
        if (body == null) {
            logger.info("接收到LocalIlayer的[receiveReportDoc]请求,参数为null ");
            return rspResult.fail("Receive params is null ,please check the param that your send");
        }
        //打印关键key信息
        logger.info("[receiveReportDoc old] 接收到请求参数：orderNo:{},reportNo:{},subContractNo:{},objectNo:{},externalObjectNo:{}",body.getOrderNo(),body.getReportNo(),body.getSubContractNo(),body.getObjectNo(),body.getExternalObjectNo());


        ProductLineType productLineType = ProductLineType.findProductLineAbbr(body.getProductLineCode());
        if (productLineType == null) {
            return rspResult.fail("接收到参数:{},ProductLineCode不能为空");
        }

        String orderNo = body.getOrderNo();
        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfo(orderNo);
        if (orderInfo == null) {
            return rspResult.fail(String.format("未找到订单(%s)信息.", orderNo));
        }

        switch (productLineType) {
            case SL:
                return doReceiveReportSubContractSL(orderInfo, body);
            default:
                return doReceiveReportSubContractOther(orderInfo, body);
        }
    }

    /**
     *
     * @param orderInfo
     * @param body
     * @return
     */
    private CustomResult doReceiveReportSubContractSL(GeneralOrderInstanceInfoPO orderInfo, ReceiveStarLimsReportDocBodyReq body) {
        String orderNo = orderInfo.getOrderNo();

        //log.info("[receiveReportDoc] 接收到请求参数：{},进行入参校验",JSONObject.toJSONString(body));
        ReceiveStarLimsBodyBaseReq receiveStarLimsBodyBase = new ReceiveStarLimsBodyBaseReq();
        BeanUtils.copyProperties(body, receiveStarLimsBodyBase);
        CustomResult<ReceiveStarLimsInfo> rspResult = this.checkStarLimsData(receiveStarLimsBodyBase);
        if (!rspResult.isSuccess()) {
            return rspResult;
        }
        ReceiveStarLimsInfo report = rspResult.getData();

        //公共校验结束 ，自身校验继续
        //log.info("[receiveReportDoc] 接收到请求参数：{},进行body校验",JSONObject.toJSONString(body));
        CustomResult result = this.checkReceiveReport(body);
        if (!result.isSuccess()) {
            return result;
        }
        String subContractNo = body.getSubContractNo();
        String folderNo = body.getObjectNo();
        logger.info("[receiveReportDoc]orderNo:{} subContractNo:{} objectNo:{}进行分包单数据校验开始", orderNo, subContractNo, folderNo);
        result = this.checkSubcontractStatus(subContractNo, StarLimsCheckTypeEnums.receiveReport);
        if (!result.isSuccess()) {
            return result;
        }

        logger.info("[receiveReportDoc]orderNo:{} subContractNo:{} objectNo:{}进行分包单数据校验Success", orderNo, subContractNo, folderNo);

        List<ReceiveFileInfo> fileInfos = body.getFiles().stream().filter(receiveFileInfo -> "Word".equalsIgnoreCase(receiveFileInfo.getFileType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fileInfos)) {
            logger.info("[receiveReportDoc]orderNo:{} subContractNo:{} objectNo:{} 文件列表中没有word格式文件[校验入参的fileType eq: word]", orderNo, subContractNo, folderNo);
            return result.fail("接收到参数,文件列表没有word格式文件");
        }
        ConclusionListPO conclusionListPO = new ConclusionListPO();
        if (Objects.nonNull(body.getConclusionId())) {
            conclusionListPO = conclusionListService.getConclusionList(body.getConclusionId());
            if (Objects.isNull(conclusionListPO)) {
                logger.info("[receiveReportDoc]orderNo:{} subContractNo:{} objectNo:{},bodyConclusion:{} 根据conclusion 配置表tb_conclusion_list中未查到对应数据", orderNo, subContractNo, folderNo, body.getConclusionId());
                return result.fail("根据conclusion 配置表tb_conclusion_list中未查到对应数据");
            }
        }

        // 获取starlims返回的文件类型
        String fileType = SubContractReportRequirement.CUSTOMER_REPORT_WORD.getName();
        if (CollectionUtils.isNotEmpty(body.getFiles())) {
            if (StringUtils.equalsIgnoreCase(SubContractReportRequirement.CUSTOMER_REPORT_PDF.getName(), body.getFiles().get(0).getFileType())) {
                fileType = SubContractReportRequirement.CUSTOMER_REPORT_PDF.getName();
            }
        }

        if(Func.isNotEmpty(body.getOriginalReportNo())){
            com.sgs.otsnotes.facade.model.req.subcontract.SubReportCancelReq subReportCancelReq  = new com.sgs.otsnotes.facade.model.req.subcontract.SubReportCancelReq();
            subReportCancelReq.setExternalReportNo(body.getOriginalReportNo());
            subReportCancelReq.setSubcontractNo(subContractNo);
            subReportCancelReq.setOpRegionAccount(StarLimsName);
            subReportCancelReq.setFileType(fileType);
            subReportCancelReq.setProductLineCode(body.getProductLineCode());
            CustomResult<Boolean> customResult = subContractService.cancelSubReport(subReportCancelReq);
            if(!customResult.isSuccess()){
                return result.fail(customResult.getMsg());
            }
        }

        String orderInfoID = orderInfo.getID();

        OrderInfoDto orderInfoPre = orderClient.getOrderInfoByOrderNo(orderNo);
        if (orderInfoPre != null) {
            if (OperationType.check(orderInfoPre.getOperationType(), OperationType.HostSubContract, OperationType.ExecSubContract, OperationType.LightSubContract)) {
                body.setParentOrderNo(orderInfoPre.getOldOrderNo());
            }
        }

        // 查看当前分包单 有多少folderNo 在subcontract_external表
        SubContractExternalRelationshipPO queryPO = new SubContractExternalRelationshipPO();
        queryPO.setSubContractNo(subContractNo);
        queryPO.setSubContractType(SubContractType.ToStarLims.getType());
        List<SubContractExternalRelationshipPO> existStarLIMSList = subcontractExternalRelExtMapper.queryExternalInfoList(queryPO);
        // 数据库存在几条回传的folderNo
        int existFolderNoCount = 0;
        if (CollectionUtils.isNotEmpty(existStarLIMSList)) {
            existFolderNoCount = existStarLIMSList.size();
        }
        logger.info("[receiveReportDoc]orderNo:{} subContractNo:{} objectNo:{},获取已经存在DB的数据 existFolderNoCount:{} ", orderNo, subContractNo, folderNo, existFolderNoCount);
        //检查sub_report 信息，
        SubReportPO queryParam = new SubReportPO();
        queryParam.setObjectNo(subContractNo);
        queryParam.setObjectType(SubReportObjectTypeEnums.starlims.getObjectType());
        queryParam.setGeneralOrderInstanceID(orderInfoID);
        // 数据库存在多少组报告 ，一个folderNo 可以对应N个 report
        List<SubReportPO> subReportPOList = subReportExtMapper.querySubReportInfoListByObjectNo(queryParam);
        int existReceivedReportCount = 0;
        // 分组 有多少组
        Map<String, List<SubReportPO>> folderNoGroupReport = subReportPOList.stream().collect(Collectors.groupingBy(SubReportPO::getObjectNo));
        logger.info("[receiveReportDoc] OrderNo:{} subContractNo:{} 是否存在多组folderNo:{}", orderNo, subContractNo, existReceivedReportCount, MapUtils.isNotEmpty(folderNoGroupReport));
        if (MapUtils.isNotEmpty(folderNoGroupReport)) {
            Set<String> subReportFolderNos = folderNoGroupReport.keySet();
            existReceivedReportCount = subReportFolderNos.size();
            logger.info("[receiveReportDoc] OrderNo:{} subContractNo:{}  目前分包单已经存在{} 组folderNo[existReceivedReportCount]：{}", orderNo, subContractNo, existReceivedReportCount, subReportFolderNos);
        }

        SubContractPO subcontractInfo = subContractExtMapper.getSubContractInfoByNo(subContractNo);

        Map<String, List<SubReportPO>> fileNameGroupReport = subReportPOList.stream().collect(Collectors.groupingBy(SubReportPO::getFilename));
        /*
         *  第一次的最后一份报告 判断逻辑是：
         *  除了当前入参的 当前分包单下的 所有folderNo 都至少有一份report
         *  且当前接口入参的folderNo 满足还不存在任何report
         *  那么当前的report 就是最后一份report，需要触发TestLine，subcontract的状态更新
         */
        boolean isFirstLastReport = false;
        boolean onlyUpdateCompleteDate = false;//当触发complete之后的每一次数据传递，都需要进行completeDate的更新
        //从db中获取 本次传递来的是否是同名文件，是：进行更新，否：就进行新增
        /*SubReportPO shouldUpdateSubReport = subReportPOList.stream()
                .filter(subReportPO -> StringUtils.equals(subReportPO.getFilename(), body.getFileName()))
                .findFirst().orElse(null);*/
        List<SubReportPO> subReports = Lists.newArrayList();

        // DIG-8555 Remove this "break" statement or make it conditional.
        // 下面的那个break说明for循环处理只会执行第一条，所以做以下写法上的修改，逻辑不变
        if (CollectionUtils.isNotEmpty(fileInfos)) {
            ReceiveFileInfo fileInfo = fileInfos.get(0);
            //for(ReceiveFileInfo fileInfo : fileInfos){
            List<SubReportPO> list = fileNameGroupReport.get(fileInfo.getFileName());
            logger.info("[receiveReportDoc] OrderNo:{} subContractNo:{} 遍历文件，根据文件名:{}获取分组的subReportList:{} ", orderNo, subContractNo, fileInfo.getFileName(), CollectionUtils.isEmpty(list));
            SubReportPO subReport = new SubReportPO();
            if (CollectionUtils.isEmpty(list)) {
                //只有进行报告新增时，才需要计算是不是最后一份报告，如果是更新，那么就不能计算
                //否则会出现3个分包单，回来更新同一个报告三次就会当成完成，实际还有两个单子没回来
                boolean existsOtherReport = folderNoGroupReport.containsKey(folderNo);
                logger.info("[receiveReportDoc] OrderNo:{} subContractNo:{} 遍历文件 folderNo是否已经存在:{}", orderNo, subContractNo, existsOtherReport);

                if (!existsOtherReport) {
                    isFirstLastReport = existReceivedReportCount + 1 == existFolderNoCount;
                }
                logger.info("[receiveReportDoc] OrderNo:{} subContractNo:{} 判断是否是第一次的最后一份report:{}", orderNo, subContractNo, isFirstLastReport);
                subReport.setID(UUID.randomUUID().toString());
                subReport.setGeneralOrderInstanceID(orderInfoID);
                subReport.setObjectType(SubReportObjectTypeEnums.starlims.getObjectType());
                subReport.setObjectNo(body.getSubContractNo());
                subReport.setFilename(fileInfo.getFileName());
                subReport.setCreatedBy(StarLimsName);
                subReport.setCreatedDate(DateUtils.getNow());
                subReport.setSubReportNo(body.getExternalObjectNo());
            } else {
                subReport = list.get(0);
                subReport.setSubReportNo(StrUtil.blankToDefault(subReport.getSubReportNo(),body.getExternalObjectNo()));
            }
            onlyUpdateCompleteDate = existFolderNoCount >= existReceivedReportCount;
            subReport.setCloudID(fileInfo.getFileId());
            subReport.setModifiedBy(StarLimsName);
            subReport.setModifiedDate(DateUtils.getNow());
            LanguageType languageType = LanguageType.findStarLimsCode(body.getLanguageId());
            if (languageType != null) {
                subReport.setLanguageId(languageType.getLanguageId());
            }else{
                // starLims 返回001 需要转换为3
                if(StringUtils.equals(body.getLanguageId(), Constants.STAR_LIMES_EN_AND_CN_LANGUAGEID)){
                    subReport.setLanguageId(NumberUtil.toInt(ReportLanguage.MultilingualReport.getCode()));
                }
            }
            subReport.setConclusionId(conclusionListPO.getID());
            subReport.setSubcontractId(subcontractInfo.getID());
            subReport.setTestMatrixMergeMode(SubReportTestMatrixMergeMode.Ignore.getCode());
            subReports.add(subReport);
            //break;
        }

        // DIG-9591 starlims fast 校验回传结果
        ReceiveStarLimsReportReq reqObject = new ReceiveStarLimsReportReq();
        BeanUtils.copyProperties(body, reqObject);
        reqObject.setExcludeCustomerInterface(String.valueOf(Optional.ofNullable(body.getReportTags()).orElse(0)));
        reqObject.setLanguageId(body.getLanguageId());
        reqObject.setParentOrderNo(body.getOrderNo());
        reqObject.setReportNo(report.getReportNo());
        reqObject.setSubContractNo(subContractNo);
        reqObject.setLabCode(orderInfo.getLabCode());
        reqObject.setExternalId(reqObject.getExternalId());
        reqObject.setCompletedDate(DateUtils.parseDate(body.getApproveDate()));
        BaseResponse checkResponse = reportDataHandlerService.checkBackData(reqObject, SourceTypeEnum.STARLIMS);
        if (!checkResponse.isSuccess()) {
            return result.fail(String.format("客户校验失败，错误原因：%s", checkResponse.getMessage()));
        }

        List<SubReportPO> finalShouldUpdateSubReports = subReports;
        boolean finalIsLastReport = isFirstLastReport;
        boolean finalOnlyUpdateCompleteDate = onlyUpdateCompleteDate;
        return transactionTemplate.execute(transStatus -> {
            CustomResult tranResult = new CustomResult();
            logger.info("[receiveReportDoc] OrderNo:{} subContractNo:{} 开启事务，进行入库操作",orderNo,subContractNo);
            if(subReportExtMapper.saveBatchSubReportInfo(finalShouldUpdateSubReports) <= 0){
                transStatus.setRollbackOnly();
                return tranResult.fail("更新SubReport数据失败.");
            }

            logger.info("[receiveReportDoc] OrderNo:{} subContractNo:{} finalIsLastReport:{},finalOnlyUpdateCompleteDate:{}",orderNo,subContractNo,finalIsLastReport,finalOnlyUpdateCompleteDate);
            CustomResult customResult = this.receiveReportDocV2(reqObject);
            if (!customResult.isSuccess()) {
                logger.error("SubContractNo_{}, folderNo_{}_Error, ErrorMsg: {}", body.getSubContractNo(), body.getObjectNo(), customResult.getMsg());
                transStatus.setRollbackOnly();
            }
            return customResult;
        });
    }

    public List<TestGroupReq> buildTestGroup(ReceiveStarLimsReportDocBodyReq req, Set<String> reportIds) {
        List<TestGroupReq> testGroupReqList = Lists.newArrayList();
        Integer languageId = LanguageType.EN.getLanguageId();
        LanguageType languageType = LanguageType.findStarLimsCode(req.getLanguageId());
        if (languageType != null) {
            languageId = languageType.getLanguageId();
        }
        for (String reportId : reportIds) {
            ReportInfoPO reportInfo = reportMapper.getReportInfoByReportId(reportId);
            Assert.notNull(reportInfo);

            TestGroupReq testGroupReq = new TestGroupReq();
            testGroupReq.setReportNo(reportInfo.getReportNo());
            testGroupReq.setReportId(reportInfo.getID());
            testGroupReq.setRefSystemId(SubReportObjectTypeEnums.starlims.getRefSystemId());
            testGroupReq.setObjectNo(req.getSubContractNo());
            testGroupReq.setSourceObjectNo(req.getReportNo());

            TestGroupDealDTO testGroupDealDTO = new TestGroupDealDTO();
            testGroupDealDTO.setLanguageId(languageId);
            testGroupDealDTO.setTestGroup(JSON.toJSONString(req.getTestGroup()));
            String mergeExtData = JSON.toJSONString(testGroupDealDTO);

            TestGroupInfoPO testGroupInfoPO = testGroupService.queryTestGroup(testGroupReq.getRefSystemId(),
                    testGroupReq.getSourceObjectNo(),
                    reportId,
                    testGroupReq.getObjectNo());
            if (testGroupInfoPO != null) {
                mergeExtData = testGroupService.mergeExtData(testGroupInfoPO.getExtData(), JSON.toJSONString(testGroupDealDTO));
            }

            testGroupReq.setExtData(mergeExtData);
            testGroupReqList.add(testGroupReq);
        }

        return testGroupReqList;
    }

    public List<ConclusionExtraDto> buildConclusionList(ReceiveStarLimsReportDocBodyReq body, Set<String> reportIds) {

        List<ConclusionExtraDto> conclusionExtraDtos = this.buildSummaryConclusion(body, reportIds);

        // 检查数据库中已存在的记录并进行合并处理 extData
        List<ConclusionExtraDto> processedConclusionList = checkAndMergeExistingConclusions(conclusionExtraDtos, body);
        return processedConclusionList;

    }


    /**
     *
     * @param starLimsReportDocBodyReq
     * @return
     */
    private List<ConclusionExtraDto> buildSummaryConclusion(ReceiveStarLimsReportDocBodyReq starLimsReportDocBodyReq, Set<String> reportIds) {
        if (CollectionUtils.isEmpty(starLimsReportDocBodyReq.getConclusionSummary())) {
            return Lists.newArrayList();
        }

        UserInfo localUser = UserHelper.getLocalUser();
        String userName = "Starlims";
        if (localUser != null) {
            userName = localUser.getRegionAccount();
        }

        List<ConclusionExtraDto> conclusionDtos = Lists.newArrayList();

        for (String reportId : reportIds) {
            //增加ReportId校验
            ReportInfoPO reportInfo = reportMapper.getReportInfoByReportId(reportId);
            Assert.notNull(reportInfo, "ReportId无效，请检查数据");

            Integer languageId = LanguageType.EN.getLanguageId();
            LanguageType languageType = LanguageType.findStarLimsCode(starLimsReportDocBodyReq.getLanguageId());
            if (languageType != null) {
                languageId = languageType.getLanguageId();
            }
            // mapping 数据
            List<ConclusionSummaryMappingChemDTO> conclusionSummaryDTOS = this.mappingChemData(starLimsReportDocBodyReq, languageId);


            for (ConclusionSummaryMappingChemDTO conclusionSummaryItem : conclusionSummaryDTOS){
                Assert.isTrue(StringUtils.isNotBlank(conclusionSummaryItem.getItemType()), ResponseCode.ILLEGAL_ARGUMENT, "Please Check Starlims Item Type, Cannot be Empty");

                StarlimsItemType starlimsItemType = StarlimsItemType.getItemType(conclusionSummaryItem.getItemType());
                Assert.notNull(starlimsItemType, String.format("Please Check Starlims Item Type:[%s]", conclusionSummaryItem.getItemType()));
                ArtifactType artifactType = starlimsItemType.getArtifactType();

                ConclusionExtraDto conclusionDto = new ConclusionExtraDto();
                conclusionDto.setRefSystemId(SubReportObjectTypeEnums.starlims.getRefSystemId());
                conclusionDto.setExtraConclusionInstanceId(conclusionSummaryItem.getItemId());
                conclusionDto.setOrderNo(starLimsReportDocBodyReq.getOrderNo());
                conclusionDto.setReportId(reportId);
                conclusionDto.setSourceObjectNo(reportInfo.getReportNo());
                conclusionDto.setObjectNo(starLimsReportDocBodyReq.getSubContractNo());
                conclusionDto.setConclusionSourceType(ConclusionSourceTypeEnum.postback.getType());
                conclusionDto.setConclusionId(conclusionSummaryItem.getConclusionId());

                conclusionDto.setExtData(conclusionSummaryItem.getExtData());
                conclusionDto.setCreatedBy(userName);
                conclusionDto.setModifiedBy(userName);
                conclusionDto.setObjectType(artifactType.getType());
                conclusionDto.setObjectName(conclusionSummaryItem.getItemText());
                conclusionDto.setConclusionLevel(starlimsItemType.getConclusionType().getCode());
                conclusionDto.setConclusionDesc(conclusionSummaryItem.getConclusionAlias());
                conclusionDto.setSequence(conclusionSummaryItem.getSorter());
                switch (artifactType) {
                    case TestLine:
                        conclusionDto.setObjectInstanceId(conclusionSummaryItem.getTestLineInstanceId());
                        conclusionDto.setObjectId(conclusionSummaryItem.getTestLineId());
                        conclusionDto.setObjectVersionId(conclusionSummaryItem.getTestLineVersionId());
                        break;
                    case PP:
                        conclusionDto.setObjectInstanceId(String.valueOf(conclusionSummaryItem.getPpNo()));
                        conclusionDto.setObjectId(conclusionSummaryItem.getPpNo());
                        conclusionDto.setObjectVersionId(conclusionSummaryItem.getPpVersionId());
                        break;
                }
                if (CollectionUtils.isNotEmpty(conclusionSummaryItem.getLanguages())) {
                    conclusionSummaryItem.getLanguages().forEach(item -> {
                        if(StringUtils.equalsIgnoreCase(item.getLanguageId(), LanguageType.CHI.getStarlimsCode())){
                            item.setLangId(LanguageType.CHI.getLanguageId());
                        } else {
                            item.setLangId(LanguageType.EN.getLanguageId());
                        }
                    });
                }

                conclusionDtos.add(conclusionDto);
            }
        }

        return conclusionDtos;
    }

    private List<ConclusionSummaryMappingChemDTO> mappingChemData(ReceiveStarLimsReportDocBodyReq receiveStarLimsReportReq, Integer languageId) {
        List<ConclusionSummaryMappingChemDTO> mappingChemDTOS = Lists.newArrayList();

        // 判断数据是否有值
        List<ConclusionSummaryDTO> conclusionSummary = receiveStarLimsReportReq.getConclusionSummary();
        if (CollectionUtils.isEmpty(conclusionSummary)) {
            return mappingChemDTOS;
        }

        String subContractNo = receiveStarLimsReportReq.getSubContractNo();
        String orderNo = receiveStarLimsReportReq.getOrderNo();
        String productLineCode = receiveStarLimsReportReq.getProductLineCode();

        // 获取相关数据
        List<ChemPpArtifactTestLineInfoRsp> chemTestMatrixs = this.getChemTestMatrixList(orderNo, subContractNo, productLineCode);
        /*// 获取信息失败直接返回
        if (CollectionUtils.isEmpty(chemTestMatrixs)) {
            return mappingChemDTOS;
        }*/
        // 组装Aid 基本信息，方便后面匹配数据
        Map<Long, PpArtifactRsp> ppArtifactMaps = Maps.newHashMap();
        Map<Integer, CitationVersionRsp> citationVersionMaps = Maps.newHashMap();
        buildPpAidInfo(conclusionSummary, ppArtifactMaps, chemTestMatrixs, citationVersionMaps);

        for (ConclusionSummaryDTO conclusionSummaryDTO : conclusionSummary) {
            // 组装 analyteList
            ConclusionSummaryMappingChemDTO conclusionSummaryMappingChemDTO = this.mappingChemItemData(orderNo, chemTestMatrixs, ppArtifactMaps, citationVersionMaps, conclusionSummaryDTO, languageId);

            mappingChemDTOS.add(conclusionSummaryMappingChemDTO);
        }

        return mappingChemDTOS;
    }

    /**
     * 检查并合并数据库中已存在的结论记录
     *
     * @param conclusionList 待处理的结论列表
     * @param reqObject 请求对象
     * @return 处理后的结论列表
     */
    private List<ConclusionExtraDto> checkAndMergeExistingConclusions(List<ConclusionExtraDto> conclusionList, ReceiveStarLimsReportDocBodyReq reqObject) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(conclusionList)) {
            return conclusionList;
        }

        List<ConclusionExtraDto> result = Lists.newArrayList();

        for (ConclusionExtraDto conclusionDto : conclusionList) {
            // 只处理有extraConclusionInstanceId的记录
            if (StringUtils.isEmpty(conclusionDto.getExtraConclusionInstanceId())) {
                result.add(conclusionDto);
                continue;
            }

            // 查询数据库中是否已存在该记录
            ConclusionExtraDto existingConclusion = conclusionService.getByReportIdAndObjectNoAndItemId(
                    conclusionDto.getReportId(),
                    conclusionDto.getObjectNo(),
                    conclusionDto.getExtraConclusionInstanceId()
            );

            // 如果不存在，则直接添加新记录
            if (existingConclusion == null) {
                result.add(conclusionDto);
                continue;
            }

            // 存在则合并extData
            String mergedExtData = mergeExtData(existingConclusion.getExtData(), conclusionDto.getExtData());
            conclusionDto.setExtData(mergedExtData);
            conclusionDto.setId(existingConclusion.getId()); // 确保更新而不是插入

            result.add(conclusionDto);
        }

        return result;
    }

    /**
     * 合并extData字段，兼容不同的JSON格式
     *
     * @param existingExtData 数据库中的extData
     * @param newExtData 新的extData
     * @return 合并后的extData
     */
    private String mergeExtData(String existingExtData, String newExtData) {
        if (StringUtils.isBlank(existingExtData)) {
            return newExtData;
        }

        try {
            // 尝试将现有数据解析为JSON数组
            List<ConclusionSummaryDTO> existingDataList = Lists.newArrayList();

            // 解析existingData
            parseConclusionData(existingExtData, existingDataList);

//            // 解析existingData
//            parseConclusionData(newExtData, existingDataList);

            // 解析新数据
            ConclusionSummaryDTO newData = JSONObject.parseObject(newExtData, ConclusionSummaryDTO.class);

            // 合并两个列表，避免重复
            Integer newLanguageId = newData.getLanguageId();

            // 检查是否已存在相同languageId的数据
            boolean exists = false;
            for (int i = 0; i < existingDataList.size(); i++) {
                ConclusionSummaryDTO existingDataMap = existingDataList.get(i);
                Integer existingLanguageId = existingDataMap.getLanguageId();

                if (existingLanguageId != null && existingLanguageId.equals(newLanguageId)) {
                    // 替换已存在的同语言数据
                    existingDataList.set(i, newData);
                    exists = true;
                    break;
                }
            }

            // 如果不存在，则添加
            if (!exists) {
                existingDataList.add(newData);
            }

            // 转换回JSON字符串
            return JSON.toJSONString(existingDataList);
        } catch (Exception e) {
            log.error("合并extData失败", e);
            return newExtData; // 如果发生错误，返回新数据
        }
    }

    private void parseConclusionData(String existingExtData, List<ConclusionSummaryDTO> existingDataList) {
        try {
            // 尝试将现有数据解析为JSON数组
            List<ConclusionSummaryDTO> existingDatas = JSON.parseArray(existingExtData, ConclusionSummaryDTO.class);
            existingDataList.clear();
            existingDataList.addAll(existingDatas);
        } catch (Exception e) {
            // 如果不是数组，则尝试解析为单个对象并创建新数组
            ConclusionSummaryDTO existingObj = JSON.parseObject(existingExtData, ConclusionSummaryDTO.class);
            LanguageType code = LanguageType.findCode(existingObj.getLanguageId());
            if (code != null) {
                logger.info("数据库中保存的数据 language字段为空， existingObj：{}，直接保存新数据newExtData：{}", existingExtData);
                existingObj.setLanguageId(code.getLanguageId());
            }
            existingDataList.add(existingObj);
        }
    }

    /**
     * @param orderInfo
     * @param reqObject
     * @return
     */
    private CustomResult doReceiveReportSubContractOther(GeneralOrderInstanceInfoPO orderInfo, ReceiveStarLimsReportDocBodyReq reqObject) {
        CustomResult rspResult = new CustomResult();
        ConclusionListPO conclusion = new ConclusionListPO();
        if (Objects.nonNull(reqObject.getConclusionId())) {
            conclusion = conclusionListService.getConclusionList(reqObject.getConclusionId());
            if (Objects.isNull(conclusion)) {
                logger.info("[receiveReportDoc]orderNo:{} subContractNo:{} objectNo:{},bodyConclusion:{} 根据conclusion 配置表tb_conclusion_list中未查到对应数据", reqObject.getOrderNo(), reqObject.getSubContractNo(), reqObject.getObjectNo(), reqObject.getConclusion());
                return rspResult.fail("根据conclusion 配置表tb_conclusion_list中未查到对应数据");
            }
        }
        reqObject.setConclusion(Objects.nonNull(conclusion.getPriorityLevel())?String.valueOf(conclusion.getPriorityLevel()):null);

        // 获取starlims返回的文件类型
        String fileType = SubContractReportRequirement.CUSTOMER_REPORT_WORD.getName();
        if (CollectionUtils.isNotEmpty(reqObject.getFiles())) {
            if (StringUtils.equalsIgnoreCase(SubContractReportRequirement.CUSTOMER_REPORT_PDF.getName(), reqObject.getFiles().get(0).getFileType())) {
                fileType = SubContractReportRequirement.CUSTOMER_REPORT_PDF.getName();
            }
        }

        // DIG-9722 判断是否是清洗数据逻辑
        boolean isRepairData = StringUtils.equalsIgnoreCase(reqObject.getCaller(), "RepairData");

        // build ReportTestDataInfo
        ReceiveStarLimsReportReq receiveStarLimsReportReq = new ReceiveStarLimsReportReq();
        BeanUtils.copyProperties(reqObject, receiveStarLimsReportReq);
        receiveStarLimsReportReq.setExcludeCustomerInterface(String.valueOf(Optional.ofNullable(reqObject.getReportTags()).orElse(0)));
        receiveStarLimsReportReq.setLanguageId(reqObject.getLanguageId());
        receiveStarLimsReportReq.setParentOrderNo(reqObject.getOrderNo());
        receiveStarLimsReportReq.setReportNo(reqObject.getReportNo());
        receiveStarLimsReportReq.setSubContractNo(reqObject.getSubContractNo());
        receiveStarLimsReportReq.setLabCode(orderInfo.getLabCode());
        receiveStarLimsReportReq.setExternalId(reqObject.getExternalId());
        receiveStarLimsReportReq.setCompletedDate(DateUtils.parseDate(reqObject.getApproveDate()));

        String finalFileType = fileType;
        return transactionTemplate.execute(transaction -> {
            if(Func.isNotEmpty(reqObject.getOriginalReportNo()) && !isRepairData){
                log.info("SubContractNo {} 下的 orignalReportNo {}不为空触发 gpo cancel sub report ",reqObject.getSubContractNo(),reqObject.getOriginalReportNo());
                com.sgs.otsnotes.facade.model.req.subcontract.SubReportCancelReq subReportCancelReq  = new com.sgs.otsnotes.facade.model.req.subcontract.SubReportCancelReq();
                subReportCancelReq.setExternalReportNo(reqObject.getOriginalReportNo());
                subReportCancelReq.setSubcontractNo(reqObject.getSubContractNo());
                subReportCancelReq.setOpRegionAccount(StarLimsName);
                subReportCancelReq.setFileType(finalFileType);
                subReportCancelReq.setProductLineCode(reqObject.getProductLineCode());
                CustomResult<Boolean> customResult = subContractService.cancelSubReport(subReportCancelReq);
                if(!customResult.isSuccess()){
                    transaction.setRollbackOnly();
                    return rspResult.fail(customResult.getMsg());
                }
            };

            //DIG-9276 获取正确的reportNo modify by vincent 2024年2月27日
            CustomResult customResult = this.receiveReportDocV2(receiveStarLimsReportReq);
            if (!customResult.isSuccess()) {
                rspResult.fail(customResult.getMsg());
                transaction.setRollbackOnly();
                return rspResult;
            }

            // DIG-9713 修改Starlims 返回后 的处理顺序
            CustomResult<List<ReceiveStarLimsReportDocRsp>> reportResult = gpoNotesClient.receiveStarLimsReportDoc(reqObject);
            List<ReceiveStarLimsReportDocRsp> reports = reportResult.getData();
            if (reports == null || reports.isEmpty()) {
                transaction.setRollbackOnly();
                return rspResult.fail(reportResult.getMsg());
            }

            if(CollectionUtils.isNotEmpty(reqObject.getConclusionSummary()) && CollectionUtils.isNotEmpty(reportResult.getData())) {

                List<ReceiveStarLimsReportDocRsp> data = reportResult.getData();
                Set<String> reportIds = data.stream()
                        .filter(item -> StringUtils.isNotEmpty(item.getReportId()))
                        .map(ReceiveStarLimsReportDocRsp::getReportId).collect(Collectors.toSet());

                if (CollectionUtils.isNotEmpty(reportIds)) {
                    SubcontractConclusionBatchReq conclusionBatchReq = new SubcontractConclusionBatchReq();
                    conclusionBatchReq.setSubcontractNo(reqObject.getSubContractNo());
                    conclusionBatchReq.setRefSystemId(SubReportObjectTypeEnums.starlims.getRefSystemId());
//                    conclusionBatchReq.setReportNo(reqObject.getReportNo());
                    conclusionBatchReq.setExternalBizNo(reqObject.getExternalObjectNo());
                    List<ConclusionExtraDto> conclusionList = this.buildConclusionList(reqObject, reportIds);
                    conclusionBatchReq.setConclusionList(conclusionList);

                    conclusionService.batchConclusion(conclusionBatchReq);

                    // 保存 TestGroup
                    List<TestGroupDTO> testGroup = reqObject.getTestGroup();
                    if (CollectionUtils.isNotEmpty(testGroup)) {
                        List<TestGroupReq> testGroupReqs = this.buildTestGroup(reqObject, reportIds);
                        testGroupService.batchTestGroup(testGroupReqs);
                    }
                }
            }


            // DIG-9407 gpo SZ HL word场景下，不需要校验reportNo
            // （查询代码记录之后，此处之所以校验reportNo ,是为了获取ReportNo后保存RD ,在改造后，先保存RD再回调GPO，所以此处校验可以去掉 ）
            /*if (reports.size() > 1) {
                transaction.setRollbackOnly();
                return rspResult.fail("receiveReportDoc 返回多条Report No.");
            }
            ReceiveStarLimsReportDocRsp report = reports.get(0);
            if (report == null || StringUtils.isBlank(report.getReportNo())) {
                transaction.setRollbackOnly();
                return rspResult.fail("receiveReportDoc 返回Report No为空.");
            }*/
            rspResult.setSuccess(Boolean.TRUE);
            return rspResult;
        });
    }

    /**
     * 接收报告
     *
     * @param body
     * @return
     */
    public CustomResult receiveReportDocV2(ReceiveStarLimsReportDocBodyReq body) {
        CustomResult result = new CustomResult<>();

        if (body == null) {
            logger.info("接收到LocalIlayer的[receiveReportDoc]请求,参数为null ");
            result.setMsg("Receive params is null ,please check the param that your send");
            return result;
        }
        //log.info("[receiveReportDoc] 接收到请求参数：{}",JSONObject.toJSONString(body));
        if (StringUtils.isBlank(body.getProductLineCode())) {
            return result.fail("接收到参数:{},ProductLineCode不能为空");
        }

        //log.info("[receiveReportDoc] 接收到请求参数：{},进行入参校验",JSONObject.toJSONString(body));
        /// 鉴权类校验
        ReceiveStarLimsBodyBaseReq receiveStarLimsBodyBase = new ReceiveStarLimsBodyBaseReq();
        BeanUtils.copyProperties(body, receiveStarLimsBodyBase);
        CustomResult<ReceiveStarLimsInfo> rspResult = this.checkStarLimsData(receiveStarLimsBodyBase);
        if (!rspResult.isSuccess()) {
            return rspResult;
        }

        //公共校验结束 ，自身校验继续
        //log.info("[receiveReportDoc] 接收到请求参数：{},进行body校验",JSONObject.toJSONString(body));
        /// 基础参数校验
        result = this.checkReceiveReport(body);
        if (!result.isSuccess()) {
            return result;
        }

        // build SubReport
        com.sgs.framework.core.base.BaseResponse<SubReportDTO> buildSubReportResp = subReportFacade.buildSubReport4Starlims(body);
        if (!buildSubReportResp.isSuccess()) {
            logger.error("[receiveReportDocV2] orderNo:{} subContractNo:{} objectNo:{} buildSubReport error!", body.getOrderNo(), body.getSubContractNo(), body.getObjectNo());
            return result.fail("内部错误,buildSubReport error");
        }
        logger.info("[receiveReportDocV2] orderNo:{} subContractNo:{} objectNo:{} buildSubReport success", body.getOrderNo(), body.getSubContractNo(), body.getObjectNo());

        // import SubReport
        SubReportDTO subReportDTO = buildSubReportResp.getData();
        com.sgs.framework.core.base.BaseResponse<Boolean> importSubReportResp = subReportRemoteService.importReport(subReportDTO);
        if (!importSubReportResp.isSuccess()) {
            logger.error("[receiveReportDocV2] orderNo:{} subContractNo:{} objectNo:{} importReport error!", body.getOrderNo(), body.getSubContractNo(), body.getObjectNo());
            return result.fail("内部错误,importReport error");
        }
        logger.info("[receiveReportDocV2] orderNo:{} subContractNo:{} objectNo:{} importReport success", body.getOrderNo(), body.getSubContractNo(), body.getObjectNo());
        return CustomResult.newSuccessInstance();

//
//        // 在starlims分包报告返回时，可以获取report data并存储至test_data表
//        BaseResponse baseResponse = testDataBizClient.saveStarlimsReportData(body, report.getReportNo(), subContractNo, orderInfo.getLabCode());
//        if(baseResponse == null){
//            result.setSuccess(false);
//            return result;
//        }
//        if (baseResponse.getStatus() != 200) {
//            result.setMsg(baseResponse.getMessage());
//            result.setSuccess(false);
//        }
//        return result;
    }

    /**
     *
     * @param reportDoc
     * @return
     */
    public CustomResult receiveReportDocV2(ReceiveStarLimsReportReq reportDoc) {
        CustomResult rspResult = new CustomResult();

        String rawDataJson = JSON.toJSONString(reportDoc);
        logger.info("[receiveReportDoc] OrderNo:{} subContractNo:{}", reportDoc.getOrderNo(), reportDoc.getSubContractNo());
        com.sgs.framework.core.base.BaseResponse<ReportTestDataInfo> buildReportTestDataResp = reportDataClientV2.build4StarLims(rawDataJson);
        if (!buildReportTestDataResp.isSuccess() || buildReportTestDataResp.getData() == null) {
            logger.error("build ReportTestDataInfo error ,code:{} ,errMsg:{}", buildReportTestDataResp.getStatus(), buildReportTestDataResp.getMessage());
            return rspResult.fail(buildReportTestDataResp.getMessage());
        }
        // import ReportTestDataInfo
        ReportTestDataInfo reportTestDataInfo = buildReportTestDataResp.getData();
        reportTestDataInfo.setExcludeCustomerInterface(reportDoc.getExcludeCustomerInterface());
        com.sgs.framework.core.base.BaseResponse<Void> importResp = reportDataClientV2.importData(reportTestDataInfo);

        rspResult.setSuccess(importResp.isSuccess());
        rspResult.setMsg(importResp.getMessage());
        if (!importResp.isSuccess()) {
            logger.error("ReportTestDataInfo import failed, errMsg:{}", importResp.getMessage());
        }
        return rspResult;
    }

    /**
     * 报告回传回来 针对文件信息的校验
     *
     * @param body
     * @return
     */
    private CustomResult checkReceiveReport(ReceiveStarLimsReportDocBodyReq body) {
        CustomResult result = new CustomResult<>();
        result.setSuccess(false);
        String orderNo = body.getOrderNo();

        List<ReceiveFileInfo> list = body.getFiles();
        if (CollectionUtils.isEmpty(list)) {
            logger.info("[checkReceiveReport] OrderNo:{} , files 为空", body.getOrderNo());
            result.setMsg("there has some parameter are null,please check [files] ");
        }
        String approveDate = body.getApproveDate();
        for (ReceiveFileInfo x : list) {
            if (StringUtils.isBlank(x.getCloudId()) || StringUtils.isBlank(x.getFileId())
                    || StringUtils.isBlank(x.getFileName()) || StringUtils.isBlank(approveDate)) {
                logger.info("[checkReceiveReport]OrderNo:{} , cloudId:{},fileId:{},fileName:{},approveDate:{} 存在空值", orderNo, x.getCloudId(), x.getFileId(), x.getFileName(), approveDate);
                result.setMsg("there has some parameter are null,please check [cloudId,objectType,fileName,approveDate] ");
                break;
            }
        }

        result.setSuccess(true);
        return result;
    }

    /**
     * <p>
     * 校验当前分包状态是否是cancel
     * </p>
     * 根据当前回调类型，对new状态有区分校验
     * <br/> updateTime 会校验是否已经存在startDate
     *
     * @param subContractNo
     * @param checkTypeEnums
     * @return
     */
    public CustomResult<SubContractPO> checkSubcontractStatus(String subContractNo, StarLimsCheckTypeEnums checkTypeEnums) {
        CustomResult<SubContractPO> result = new CustomResult<>();
        result.setSuccess(false);
        SubContractPO subcontractInfo = subContractService.selectBySubcontractNo(subContractNo);
        if (subcontractInfo == null) {
            logger.info("subContractNo:{}[checkSubcontractStatus] 获取不到分包信息", subContractNo);
            result.setMsg("unable to find subcontract information");
            return result;
        }

        /// 分包渠道校验？
        Integer subContractOrder = subcontractInfo.getSubContractOrder();
        if (!SubContractType.check(subContractOrder, SubContractType.ToStarLims)) {
            logger.info("subContractNo:{}[checkSubcontractStatus] 当前分包单不是starLims的分包单：subContractOrder={}", subContractNo, subContractOrder);
            result.setMsg("not starLIMS subcontract");
            return result;
        }
        Integer status = subcontractInfo.getStatus();
        // 需要当前状态是非cancel 才可以继续
        if (SubContractStatusEnum.check(status, SubContractStatusEnum.Cancelled)) {
            logger.info("subContractNo:{}[checkSubcontractStatus] 分包已经cancel", subContractNo);
            result.setMsg("subcontract already cancelled");
            return result;
        }
        // 1 receive bizNo -> statue = new
        // 2 update Time -》 statue = new & startDate==null
        // 3 receive report -> status 非cancel ,且subContractOrder = starlims(4)
        boolean subStatusIsNew = SubContractStatusEnum.check(status, SubContractStatusEnum.Created);
        boolean subStatusIsTesting = SubContractStatusEnum.check(status, SubContractStatusEnum.Testing);
        //todo 以下逻辑在检测数据回传时不需要关注？
        switch (checkTypeEnums) {
            case receiveBizNo:
                if (!subStatusIsNew) {
                    //TODO zhi 需要记录log到Preorder 表
                    logger.info("subContractNo:{}[checkSubcontractStatus] 分包状态不是new,status:{}", subContractNo, status);
                    result.setMsg("the status of subcontract is not 'new' ");
                    return result;
                }
                break;
            case updateTime:
                if (!(subStatusIsNew || subStatusIsTesting)) {
                    logger.info("subContractNo:{}[checkSubcontractStatus] 分包状态不是new,status:{}", subContractNo, status);
                    result.setMsg("the status of subcontract is not 'new' ");
                    return result;
                }
                /*if(startDate!=null){
                    logger.info("subContractNo:{}[checkSubcontractStatus] 已经存在startDate",subContractNo);
                    result.setMsg("the starDate already exist ");
                    return result;
                }*/
                break;
            case receiveReport:
                // DIG-8606
                if (SubContractStatusEnum.check(status, SubContractStatusEnum.Created)) {
                    logger.info("subContractNo:{}[checkSubcontractStatus] 分包New", subContractNo);
                    result.setMsg("Subcontract Status was still New.");
                    return result;
                }else if (SubContractStatusEnum.check(status, SubContractStatusEnum.Complete)) {
                    logger.info("subContractNo:{}[checkSubcontractStatus] 分包Complete", subContractNo);
                    result.setMsg("Unable to upload report because subcontract status is 'Completed'");
                    return result;
                }
                break;
            default:
                break;
        }
        result.setData(subcontractInfo);
        result.setSuccess(true);
        return result;
    }

    /**
     * 回调时，校验本地数据状态
     * 校验body的基本信息
     *
     * @param body 传递子类进来
     * @return
     */
    public CustomResult<ReceiveStarLimsInfo> checkStarLimsData(ReceiveStarLimsBodyBaseReq body) {
        CustomResult result = new CustomResult<>();
        result.setSuccess(false);
        //1校验authId是否匹配
        if (body == null) {
            logger.info("[checkStarLimsData]OrderNo:{} 接收到LocalIlayer的请求，参数body为null");
            result.setMsg("parameter 'body' can't be null");
            return result;
        }
        String orderNo = body.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            logger.info("[checkStarLimsData] 接收到LocalIlayer的请求，参数orderNo为null 无法继续校验");
            result.setMsg("parameter 'orderNo' can't be null");
            return result;
        }
        String objectType = body.getObjectType();
        String starLIMSAuthId = starLimsConfig.getStarLimsAuthId();
        String authId = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest().getHeader("authId");
        //receiveReportDoc方法和updateTimeTrack方法有二个参数（objectType/authId）有细微差别，需要区分校验
        if (StringUtils.isEmpty(authId)) {
            //走updateTimeTrack
            authId = body.getAuthId();
        } else {
            //走receiveReportDoc
            objectType = objectType.toLowerCase();
        }
        if (!StringUtils.equalsIgnoreCase(starLIMSAuthId, authId)) {
            logger.info("[checkStarLimsData]OrderNo:{} 接收到LocalIlayer的请求，authId不匹配 local:{}-param:{}", orderNo, starLIMSAuthId, body.getAuthId());
            result.setMsg("there has parameter no match for 'authId'");
            return result;
        }
        String subContractNo = body.getSubContractNo();
        String objectNo = body.getObjectNo();
        if (StringUtils.isBlank(subContractNo)
                || StringUtils.isBlank(objectType)
                || StringUtils.isBlank(objectNo)) {
            logger.info("[checkStarLimsData]OrderNo:{} 接收到LocalIlayer的请求，subcontract:{},objectType:{},objectNo:{} 存在空值", orderNo, subContractNo, objectType, objectNo);
            result.setMsg("there has some parameter are null,please check [subContractNo,objectType,objectNo] ");
            return result;
        }
        if (!StarLimsObjectTypeEnums.check(objectType, StarLimsObjectTypeEnums.starlims)) {
            logger.info("[checkStarLimsData] OrderNo:{} subcontract:{}  接收到LocalIlayer的请求，objectType:{} 不匹配", orderNo, subContractNo, objectType);
            result.setMsg("there has parameter no match for 'objectType'");
            return result;
        }
        //根据orderNo 校验当前report状态，如果存在201 204 206 则拒绝
        //这个sql查询的是当前有效的report 不是report集合
        ReportInfoPO reportPO = reportMapper.getReportByOrderNo(orderNo);
        if (reportPO == null) {
            logger.info("[checkStarLimsData] OrderNo:{},subcontract:{} 接收到LocalIlayer的请求，查询不到report", orderNo, subContractNo);
            result.setMsg("query no report data by parameter 'orderNo'");
            return result;
        }
        Integer reportStatus = reportPO.getReportStatus();
        if (!ReportStatus.check(reportStatus, ReportStatus.New, ReportStatus.Draft, ReportStatus.Combined)) {
            logger.info("[checkStarLimsData] OrderNo:{},subcontract:{} 接收到LocalIlayer的请求，查询到report状态为：{} ，不予许处理本地分包单", orderNo, subContractNo, reportStatus);
            result.setMsg("report status not in new,draft,combined, refuse this request.");
            return result;
        }
        ReceiveStarLimsInfo starLims = new ReceiveStarLimsInfo();
        starLims.setReportNo(reportPO.getReportNo());

        result.setData(starLims);
        result.setSuccess(true);
        return result;
    }




    /**
     * 新的 toStarLims
     *
     * @param reqObject
     * @return
     */
    @BizLog(bizType = BizLogConstant.ORDER_OPERATION_HISTORY, operType = "To StarLims")
    @AccessRule(policyType = PolicyType.SubcontractId,enableParamLockKey = true)
    public CustomResult toStarLims(SubContractStarLimsReq reqObject) {
        return toStarlimsExecutor.toStarlims(reqObject);
    }





    public CustomResult<List<SubContractTestMatrixInfo>> getSubContractTestMatrixList(SubContractChemTestMatrixReq reqObject) {
        CustomResult<List<SubContractTestMatrixInfo>> rspResult = new CustomResult<>();

        rspResult.setData(subContractExtMapper.getSubContractTestMatrixList(reqObject.getSubContractNo()));
        rspResult.setSuccess(Boolean.TRUE);
        return rspResult;
    }









    /**
     * @param starLims
     * @param subCOntract
     */
    private void getAndTransformDffData(SubContractToStarLimsInfo starLims, SubContractOrderInfo subCOntract) {
        String orderNo = subCOntract.getOrderNo();
        ReportInfoPO report = reportMapper.getReportByOrderNo(orderNo);
        List<TestrptCoverpageDffSimpleDTO> product = dffClient.getUnpivotProduct(orderNo, report.getReportNo());
        if (CollectionUtils.isEmpty(product)) {
            return;
        }
        TestrptCoverpageDffSimpleDTO dffSimpleDTO = product.get(0);
        String dffFormName = dffSimpleDTO.getDffFormName();
        String dffFormID = dffSimpleDTO.getDffFormID();
        starLims.setdFFFormName(dffFormName);

        // DIG-9252 GPO 临时处理，dFFFormID 中同步提供 FormID + gridId
        Set<String> dffFormIds = product.stream().filter(item -> StringUtils.isNotEmpty(item.getDffFormID()))
                .map(TestrptCoverpageDffSimpleDTO::getDffFormID)
                .collect(Collectors.toSet());
        starLims.setdFFFormID(StringUtils.join(dffFormIds, ","));
        // 组装dffs对象
        product.sort((a, b) -> a.getLanguageID().compareTo(b.getLanguageID()));
        List<DffInfo> dffInfos = Lists.newArrayList();
        product.forEach(dff -> {
            String starLimsLanCode = StarLimsDFFLanguageEnums.getStarLimsCodeByLanguageId(dff.getLanguageID());
            DffInfo dffInfo = new DffInfo();
            dffInfo.setDffCode(dff.getFieldCode());
            dffInfo.setDffValue(dff.getDisplayData());
            dffInfo.setLangId(starLimsLanCode);
            if (StringUtils.isBlank(dffInfo.getDffValue())) {
                return;
            }
            dffInfos.add(dffInfo);
        });
        //设置dffs
        starLims.setDffs(dffInfos);
    }

    /**
     * starlims/save Subcontrat 不存在父单，就是当前单，当subcontract 数据回来，且触发subcontract变为testing时，同步触发当前订单状态为testing
     *
     * @param currentOrderNo 当前订单的orderNo
     */
    public void syncOrderTestingToPreOrder(String subContractNo, String currentOrderNo) {
       /* //获取当前单最新状态
        CustomResult<Integer> lastOrderStatusRsp = statusClient.queryObjectLastStatusByObjectTypeAndObjectNo(currentOrderNo, ObjectType.Order);
        if(!lastOrderStatusRsp.isSuccess()){
            logger.info("分包：{} 触发order：{} 变更Testing错误，调用preorder 接口异常",subContractNo,currentOrderNo);
            return;
        }

        Integer currentOrderLastStatus = lastOrderStatusRsp.getData();
        logger.info("分包：{} 触发order：{} 变更Testing，获取到最新状态为：{}",subContractNo,currentOrderNo,currentOrderLastStatus);
        //只更新原单位new /confirm 到testing
        if(com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(currentOrderLastStatus,
                com.sgs.preorder.facade.model.enums.OrderStatus.New,
                com.sgs.preorder.facade.model.enums.OrderStatus.Confirmed)){
            //触发tbStatus的更新
        }*/
        logger.info("分包：{} 触发order：{} 变更Testing开始", subContractNo, currentOrderNo);
        SysStatusReq sysStatusReq = new SysStatusReq();
        sysStatusReq.setUserName("System");
        sysStatusReq.setIgnoreOldStatus(true);
        sysStatusReq.setNewStatus(com.sgs.preorder.facade.model.enums.OrderStatus.Testing.getStatus());
        sysStatusReq.setObjectNo(currentOrderNo);
        statusClient.insertStatusInfo(sysStatusReq);
        logger.info("分包：{} 触发order：{} 变更Testing结束", subContractNo, currentOrderNo);
    }

    /**
     * @param reqObject
     * @return
     */
    public CustomResult cleanStarlimsData(SubcontractExternalRelReq reqObject) {
        CustomResult rspResult = new CustomResult();
        do {
            /*reqObject.setOrderNo("SL923092600650TX");
            reqObject.setSubContractNo("GZSL2360065001GZCCL");
            reqObject.setFolderNo("CAN23-0001131");*/

            List<StarLimsRelInfo> externalRels = subcontractExternalRelExtMapper.getStarLimsRelInfoList(reqObject);
            if (externalRels == null || externalRels.isEmpty()) {
                // DIG-8555 A "NullPointerException" could be thrown; "externalRels" is nullable here.
                if (externalRels == null) {
                    externalRels = Lists.newArrayList();
                }
                if (StringUtils.isBlank(reqObject.getFolderNo())) {
                    return rspResult.fail("StarLims 接口返回对象为空.");
                }
                StarLimsRelInfo externalRel = new StarLimsRelInfo();
                externalRel.setOrderNo(reqObject.getOrderNo());
                externalRel.setSubContractNo(reqObject.getSubContractNo());
                externalRel.setExternalNo(reqObject.getFolderNo());

                externalRels.add(externalRel);
            }
            // TODO 多线程处理
            for (StarLimsRelInfo externalRel : externalRels) {
                reqObject.setLastModifiedTime(externalRel.getLastModifiedTime());

                FolderReportInfoReq folderReport = new FolderReportInfoReq();
                folderReport.setExternalParentId(externalRel.getOrderNo());
                folderReport.setExternalId(externalRel.getSubContractNo());
                folderReport.setFolderNo(externalRel.getExternalNo());

                ReportInfoTempRsp rspObject = starlimsClient.getFolderReportInfoList(folderReport, externalRel.getSubContractLabCode());
                if (rspObject == null) {
                    if (StringUtils.isNotBlank(reqObject.getFolderNo())) {
                        return rspResult.fail("StarLims 接口返回对象为空.");
                    }
                    continue;
                }
                List<ReceiveStarLimsReportReq> folderReports = rspObject.getReport();
                if (folderReports == null || folderReports.isEmpty()) {
                    if (StringUtils.isNotBlank(reqObject.getFolderNo())) {
                        return rspResult.fail(String.format("StarLims未返回数据(%s_%s).", rspObject.getStatus(), rspObject.getDetails()));
                    }
                    continue;
                }

                folderReports.forEach(reportDoc -> {
                    OrderInfoDto order = orderClient.getOrderInfoByOrderNo(reportDoc.getOrderNo());
                    if (order == null) {
                        return;
                    }
                    ReportInfoPO report = reportMapper.getReportByOrderNo(reportDoc.getOrderNo());
                    if (report == null) {
                        return;
                    }
                    reportDoc.setProductLineCode(order.getBUCode());
                    reportDoc.setParentOrderNo(order.getOrderNo());
                    reportDoc.setSubContractNo(reportDoc.getObjectNo());
                    reportDoc.setLabCode(String.format("%s %s", order.getLocationCode(), order.getBUCode()));
                    reportDoc.setReportNo(report.getReportNo());
                    reportDoc.setObjectNo(reportDoc.getObjectNo());
                    //reportDoc.setCompletedDate(DateUtils.parseDate(reportDoc.getApprovedDate()));
                    reportDoc.setCompletedDate(reportDoc.getCompletedDate());

                    BaseResponse baseResponse = testDataBizClient.cleanStarlimsData(reportDoc);
                    if (baseResponse.getStatus() != 200) {

                    }
                });
            }
        } while (StringUtils.isBlank(reqObject.getFolderNo()));

        rspResult.setSuccess(true);
        return rspResult;
    }

    public CustomResult<Boolean> cancelSubReport(StarLimsSubReportCancelReq subReportCancelReq) {
        CustomResult<Boolean> rspResult = new CustomResult<>(false);
        if (subReportCancelReq == null) {
            logger.info("接收到[cancelSubReport]请求,参数为null ");
            return rspResult.fail("subReportCancelReq params is null ,please check the param that your send");
        }

        //starlims鉴权
        CustomResult<Boolean> authResult = authStarLims(subReportCancelReq);
        if(!authResult.isSuccess()){
            return authResult;
        }

        logger.info("[cancelSubReport] 接收到请求参数：{}", JSONObject.toJSONString(subReportCancelReq));
        ProductLineType productLineType = ProductLineType.findProductLineAbbr(subReportCancelReq.getProductLineCode());
        if (productLineType == null) {
            return rspResult.fail("接收到参数:{},ProductLineCode不能为空");
        }
        //领域服务cancel
        return subContractService.cancelSubReport(subReportCancelReq);
    }

    private CustomResult<Boolean> authStarLims(StarlimsAuthReq authReq){
        CustomResult<Boolean> rspResult = new CustomResult<>(false);
        if (authReq == null) {
            logger.info("接收到请求参数为null ");
            return rspResult.fail("Request params is null ,please check the param that your send");
        }
        String starLIMSAuthId = starLimsConfig.getStarLimsAuthId();
        String authId = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest().getHeader(AUTH_KEY);
        if (StringUtils.isEmpty(authId)) {
            authId = authReq.getAuthId();
        }
        if (!StringUtils.equalsIgnoreCase(starLIMSAuthId, authId)) {
            logger.info("authStarLims request，authId不匹配 local:{}-param:{}",starLIMSAuthId, authId);
            rspResult.fail("there has parameter no match for 'authId'");
            return rspResult;
        }
        rspResult.setSuccess(true);
        return rspResult;
    }


    /**
     *
     * @param orderNo
     * @param subContractNo
     * @param productLineCode
     * @return
     */
    public List<ChemPpArtifactTestLineInfoRsp> getChemTestMatrixList(String orderNo, String subContractNo, String productLineCode) {
        SubContractChemTestMatrixReq subContract = new SubContractChemTestMatrixReq();
        subContract.setOrderNo(orderNo);
        subContract.setSubContractNo(subContractNo);
        subContract.setProductLineCode(productLineCode);

        SubContractChemTestMatrixContext chemTestMatrixContext = new SubContractChemTestMatrixContext();
        chemTestMatrixContext.setSubcontractNo(subContractNo);
        chemTestMatrixContext.setProductLineCode(productLineCode);
        chemTestMatrixContext.setRequestId(TypeId.generate().toString());
        bizFlowFacade.execute(NotesCapabilityConstants.GetSubContractChemTestMatrixContext,chemTestMatrixContext);

        List<ChemPpArtifactTestLineInfoRsp> chemMatrixList = chemTestMatrixContext.getChemMatrixList();
        if(Objects.isNull(chemMatrixList)){
            return Lists.newArrayList();
        }
        logger.info("getChemTestMatrixList orderNo:[{}], subContractNo:[{}]  ", orderNo, subContractNo);
        return chemMatrixList;
    }

    /**
     *
     * @param chemTestMatrixs
     * @return
     */
    public Map<Long, PpArtifactRsp> buildPpArtifactMaps(List<ChemPpArtifactTestLineInfoRsp> chemTestMatrixs) {
        Map<Long, PpArtifactRsp> ppArtifactMaps = Maps.newHashMap();
        Set<Long> artifactIds = Sets.newHashSet();
        chemTestMatrixs.forEach(tm -> {
            if (NumberUtil.toLong(tm.getChemAid()) > 0) {
                artifactIds.add(tm.getChemAid());
            }
        });

        List<PpArtifactRsp> ppArtifacts = ppArtifactRelClient.getPpArtifactRelListByArtifactIds(Lists.newArrayList(artifactIds));
        if (ppArtifacts == null) {
            ppArtifacts = Lists.newArrayList();
        }
        ppArtifacts.forEach(ppArtifact -> {
            ppArtifactMaps.put(ppArtifact.getArtifactId(), ppArtifact);
        });
        return ppArtifactMaps;
    }


    private void buildPpAidInfo(List<ConclusionSummaryDTO> conclusionSummary,
                                Map<Long, PpArtifactRsp> ppArtifactMaps,
                                List<ChemPpArtifactTestLineInfoRsp> chemTestMatrixs,
                                Map<Integer, CitationVersionRsp> citationVersionMaps) {
        Set<Long> aIds = Sets.newHashSet();
        Set<Integer> citationVersionIds = Sets.newHashSet();
        chemTestMatrixs.forEach(tm -> {
            if (NumberUtil.toLong(tm.getChemAid()) > 0) {
                aIds.add(tm.getChemAid());
            }
            if (NumberUtil.toLong(tm.getAid()) > 0) {
                aIds.add(tm.getAid());
            }
            if (NumberUtil.toInt(tm.getCitationVersionId()) > 0) {
                citationVersionIds.add(tm.getCitationVersionId());
            }
        });
        // 订单中完全没有的情况下 需要加上此逻辑
        conclusionSummary.forEach(tm -> {
            if (NumberUtil.toLong(tm.getAId()) > 0) {
                aIds.add(tm.getAId());
            }
            if (NumberUtil.toInt(tm.getCitationExternalId()) > 0) {
                citationVersionIds.add(tm.getCitationExternalId());
            }
        });
        List<PpArtifactRsp> ppArtifacts = ppArtifactRelClient.getPpArtifactRelListByArtifactIds(Lists.newArrayList(aIds));
        if (ppArtifacts == null) {
            ppArtifacts = Lists.newArrayList();
        }
        ppArtifacts.forEach(ppArtifact -> {
            ppArtifactMaps.put(ppArtifact.getArtifactId(), ppArtifact);
        });

        List<CitationVersionRsp> citationVersions = citationClient.getCitationInfoByCitationVersionId(citationVersionIds);
        if (citationVersions != null){
            citationVersions.forEach(citationVersion->{
                citationVersionMaps.put(citationVersion.getCitationVersionId(), citationVersion);
            });
        }
    }

    private ConclusionSummaryMappingChemDTO mappingChemItemData(String orderNo, List<ChemPpArtifactTestLineInfoRsp> chemTestMatrixs,
                                     Map<Long, PpArtifactRsp> ppArtifactMaps,
                                     Map<Integer, CitationVersionRsp> citationVersionMaps,
                                     ConclusionSummaryDTO conclusionSummaryDTO,
                                     Integer languageId) {

        ConclusionSummaryMappingChemDTO conclusionSummaryMappingChemDTO = new ConclusionSummaryMappingChemDTO();
        BeanUtil.copyProperties(conclusionSummaryDTO, conclusionSummaryMappingChemDTO);
        // 保存原始数据
        ConclusionSummaryDTO conclusionSummaryDealDTO = new ConclusionSummaryDTO();
        BeanUtil.copyProperties(conclusionSummaryDTO, conclusionSummaryDealDTO);
        conclusionSummaryDealDTO.setLanguageId(languageId);
        conclusionSummaryMappingChemDTO.setExtData(JSON.toJSONString(conclusionSummaryDealDTO));


        Long aid = NumberUtil.toLong(conclusionSummaryDTO.getAId());
        int testLineId = NumberUtil.toInt(conclusionSummaryDTO.getTestLineId());
        Integer chemPpNo = NumberUtil.toInt(conclusionSummaryDTO.getPpNo());
        Integer chemPpVersionId = NumberUtil.toInt(conclusionSummaryDTO.getPpVersionId());
        Integer citationVersionId = NumberUtil.toInt(conclusionSummaryDTO.getCitationExternalId());
        Integer citationTypeId = 0;
        CitationType citationType = CitationType.findCitationType(conclusionSummaryDTO.getCitationType());
        if (citationType != null) {
            citationTypeId = citationType.getType();
        }


        ArtifactType artifactType = StarlimsItemType.getArtifactTypeByItemType(conclusionSummaryDTO.getItemType());
        switch (artifactType) {
            case PP:
                buildChemPpInfo(chemTestMatrixs, conclusionSummaryMappingChemDTO, chemPpNo, chemPpVersionId);
                break;
            case TestLine:
                buildTestLineInfo(chemTestMatrixs, ppArtifactMaps, conclusionSummaryMappingChemDTO, aid, testLineId, chemPpNo, chemPpVersionId, citationVersionId, orderNo);

                break;
        }
        return conclusionSummaryMappingChemDTO;
    }

    private void buildTestLineInfo(List<ChemPpArtifactTestLineInfoRsp> chemTestMatrixs, Map<Long, PpArtifactRsp> ppArtifactMaps,
                                   ConclusionSummaryMappingChemDTO conclusionSummaryMappingChemDTO,
                                   Long aid, int testLineId, Integer chemPpNo, Integer chemPpVersionId, Integer citationVersionId, String orderNo) {
        //  根据 订单中的matrix对象 匹配 starlims 返回的matrix 对象
        ChemPpArtifactTestLineInfoRsp chemTestLine = mappingChemPpArtifactTestLineInfo(chemTestMatrixs, aid, testLineId, chemPpNo, citationVersionId);
        if (chemTestLine == null) {
            chemTestLine = new ChemPpArtifactTestLineInfoRsp();
            chemTestLine.setPpNo(chemPpNo);
            chemTestLine.setPpVersionId(chemPpVersionId);
            chemTestLine.setAid(aid);
            chemTestLine.setTestLineId(testLineId);
            chemTestLine.setCitationVersionId(citationVersionId);
        }
        PpArtifactRsp ppArtifact = ppArtifactMaps.get(aid);
        if (ppArtifact != null) {
            chemTestLine.setCitationType(ppArtifact.getCitationType());
        }
        chemTestLine.setOrderNo(orderNo);

        if (StringUtils.isNotBlank(chemTestLine.getTestLineInstanceId())) {
            conclusionSummaryMappingChemDTO.setTestLineInstanceId(chemTestLine.getTestLineInstanceId());
        } else {
            // 匹配不上时，md5 新建一个
            conclusionSummaryMappingChemDTO.setTestLineInstanceId(DigestUtils.md5Hex(findPpTestLineCitationKey(chemTestLine).toLowerCase()));
        }
        conclusionSummaryMappingChemDTO.setPpNo(chemTestLine.getPpNo());
        conclusionSummaryMappingChemDTO.setPpVersionId(chemTestLine.getPpVersionId());
        conclusionSummaryMappingChemDTO.setTestLineId(chemTestLine.getTestLineId());
    }

    private void buildChemPpInfo(List<ChemPpArtifactTestLineInfoRsp> chemTestMatrixs, ConclusionSummaryMappingChemDTO conclusionSummaryMappingChemDTO, Integer chemPpNo, Integer chemPpVersionId) {
        // 判断返回的PpNo 是否存在于订单的ChemPp 中，如果存在，则转换成订单中的PPNo
        Map<Integer, ChemPpArtifactTestLineInfoRsp> chemPpNoMaps = chemTestMatrixs.stream().filter(tm -> NumberUtil.toInt(tm.getChemPpNo()) > 0 && NumberUtil.toInt(tm.getPpNo()) > 0)
                .collect(Collectors.toMap(ChemPpArtifactTestLineInfoRsp::getChemPpNo, Function.identity(), (k1, k2) -> k1));
        if (chemPpNoMaps.containsKey(chemPpNo)) {
            ChemPpArtifactTestLineInfoRsp chemPpArtifactTestLineInfoRsp = chemPpNoMaps.get(chemPpNo);
            conclusionSummaryMappingChemDTO.setPpNo(chemPpArtifactTestLineInfoRsp.getPpNo());
            conclusionSummaryMappingChemDTO.setPpVersionId(chemPpArtifactTestLineInfoRsp.getPpVersionId());
        } else {
            conclusionSummaryMappingChemDTO.setPpNo(chemPpNo);
            conclusionSummaryMappingChemDTO.setPpVersionId(chemPpVersionId);
        }
    }


    private ChemPpArtifactTestLineInfoRsp mappingChemPpArtifactTestLineInfo(List<ChemPpArtifactTestLineInfoRsp> chemTestMatrixs,
                                                                            long aid, int testLineId, int chemPpNo, int citationVersionId) {
        ChemPpArtifactTestLineInfoRsp chemTestLine = ListUtils.findFirst(chemTestMatrixs, tm -> {
            if (!NumberUtil.equals(tm.getTestLineId(), testLineId)) {
                return false;
            }
            Integer matrixPpNo = NumberUtil.doZeroIntegerDefVal(tm.getChemPpNo(), tm.getPpNo());
            // chemPpNo > 0 的时候，匹配规则 ppno. + tlID + citationID + citationType
            if (citationVersionId > 0 && NumberUtil.toInt(chemPpNo) > 0 && NumberUtil.equals(matrixPpNo, chemPpNo) && NumberUtil.equals(tm.getCitationVersionId(), citationVersionId)) {
                return true;
            }
            if (citationVersionId > 0 && NumberUtil.toInt(chemPpNo) == 0 && NumberUtil.equals(tm.getCitationVersionId(), citationVersionId)) {
                return true;
            }
            if (citationVersionId <= 0) {
                if (aid == 0) {
                    return false;
                }
                return NumberUtil.toInt(tm.getChemAid()) > 0 && NumberUtil.equals(tm.getChemAid(), aid);
            }
            // TL没有PP，用TL ID+materialNumber，匹配Order里的TL Id+ SampleNo
            if (aid == 0) {
                return true;
            }
            return false;
        });
        return chemTestLine;
    }


    public String findPpTestLineCitationKey(ChemPpArtifactTestLineInfoRsp chemPpTestLineInfo) {
        if (chemPpTestLineInfo == null) {
            return null;
        }
        return String.format("%s_%s_%s_%s_%s", chemPpTestLineInfo.getOrderNo(), NumberUtil.toInt(chemPpTestLineInfo.getAid()),
                NumberUtil.toInt(chemPpTestLineInfo.getTestLineId()),
                NumberUtil.toInt(chemPpTestLineInfo.getCitationVersionId()),
                NumberUtil.toInt(chemPpTestLineInfo.getCitationType()));
    }


}
