package com.sgs.otsnotes.domain.service.sample.validator.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.sgs.otsnotes.dbstorages.mybatis.extmapper.OrderMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.GeneralOrderInstanceInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO;
import com.sgs.otsnotes.domain.service.SampleService;
import com.sgs.otsnotes.domain.service.sample.context.SampleBreakDownContext;
import com.sgs.otsnotes.domain.service.sample.context.SampleBreakDownResult;
import com.sgs.otsnotes.domain.service.sample.validator.SampleBreakDownValidator;
import com.sgs.otsnotes.domain.service.testline.TestLineManager;
import com.sgs.otsnotes.domain.service.testline.biz.TestLineBizService;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.otsnotes.facade.model.req.TestMatrixReq;
import com.sgs.otsnotes.integration.OrderClient;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.enums.OperationType;

/**
 * 样品分解验证器实现类
 * 
 * 【重构组件】复用SampleService中的现有验证方法，提供清晰的验证接口
 * 
 * 实现策略：
 * - 通过依赖注入获取SampleService实例
 * - 复用已有的public验证方法
 * - 按照验证顺序执行各项检查
 * - 统一处理验证结果
 * 
 * <AUTHOR>
 * @since 重构版本
 */
@Component
public class SampleBreakDownValidatorImpl implements SampleBreakDownValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(SampleBreakDownValidatorImpl.class);
    
    @Autowired
    private SampleService sampleService;
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private ReportMapper reportMapper;
    
    @Autowired
    private OrderClient orderClient;

    @Autowired
    private TestLineManager testLineManager;
    @Autowired
    private TestLineBizService testLineBizService;
    /**
     * 验证样品分解上下文
     * 
     * 按照业务流程顺序执行验证：
     * 1. 基础参数验证（包含样品重复性验证和样品树结构验证）
     * 2. 订单信息验证
     * 3. 操作类型验证
     * 4. 报告状态验证
     * 5. 重复样品验证
     * 6. 样品排序计算
     * 7. 重置混样编号
     * 8. 测试矩阵预处理 - 提取请求的测试线ID集合
     * 
     * 注意：
     * - checkSampleParameters已经内部调用了checkSampleRepeat和checkSampleTree
     * - 数据加载和NC状态验证将在处理器组件中实现
     * 
     * @param context 样品分解上下文
     * @return 验证结果
     */
    @Override
    public SampleBreakDownResult validate(SampleBreakDownContext context) {
        logger.info("【样品分解验证器】开始验证样品分解上下文，订单号: {}", context.getReqObject().getOrderNo());
        
        try {
            // 1. 基础参数验证（已包含样品重复性验证和样品树结构验证）
            CustomResult paramResult = sampleService.checkSampleParameters(context.getReqObject());
            if (!paramResult.isSuccess()) {
                logger.error("【样品分解验证器】参数验证失败: {}", paramResult.getMsg());
                return SampleBreakDownResult.fail("参数验证失败: " + paramResult.getMsg());
            }
            
            // 2. 订单信息验证
            GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfo(context.getReqObject().getOrderNo());
            if (order == null) {
                logger.error("【样品分解验证器】订单不存在: {}", context.getReqObject().getOrderNo());
                return SampleBreakDownResult.fail(String.format("当前OrderNo [%s] 不存在.", context.getReqObject().getOrderNo()));
            }
            
            // 将订单信息设置到上下文中，供后续组件使用
            context.setOrder(order);
            context.setOrderNo(order.getOrderNo());
            context.setOrderId(order.getID());
            // 3. 操作类型验证
            OrderInfoDto orderInfo = orderClient.getOrderInfoByOrderNo(context.getReqObject().getOrderNo());
            if (OperationType.check(orderInfo.getOperationType(), OperationType.NewSubContract)) {
                logger.error("【样品分解验证器】订单操作类型不允许此操作: {}, 操作类型: {}", 
                        context.getReqObject().getOrderNo(), orderInfo.getOperationType());
                return SampleBreakDownResult.fail(String.format("当前OrderNo [%s] 不能此操作.", context.getReqObject().getOrderNo()));
            }
            
            // 4. 报告状态验证 (POSL-2363)
            ReportInfoPO oldReport = reportMapper.getReportByOrderNo(context.getReqObject().getOrderNo());
            if (oldReport == null) {
                logger.error("【样品分解验证器】报告不存在: {}", context.getReqObject().getOrderNo());
                return SampleBreakDownResult.fail(String.format("未找到当前OrderNo [%s] 下的Report 信息.", context.getReqObject().getOrderNo()));
            }
            if (!ReportStatus.check(oldReport.getReportStatus(), ReportStatus.New, ReportStatus.Combined, ReportStatus.Draft)) {
                logger.error("【样品分解验证器】报告状态不允许操作: {}, 状态: {}", 
                        context.getReqObject().getOrderNo(), oldReport.getReportStatus());
                return SampleBreakDownResult.fail(String.format("当前Report[%s] 的状态为New、Combin、Draft才允许回传.", oldReport.getReportStatus()));
            }
            
            // 将报告信息设置到上下文中，供后续组件使用
            context.setReport(oldReport);
            
            // 5. 重复样品验证
            sampleService.verifyDuplicateSamples(context.getReqObject(), order.getID());
            
            // 6. 重新计算样品排序
            sampleService.recalculateSort(context.getReqObject().getSamples(), null);
            
            // 7. 重置混样编号
            CustomResult resetResult = sampleService.resetMixSampleNo(context.getReqObject());
            if (!resetResult.isSuccess()) {
                logger.error("【样品分解验证器】重置混样编号失败: {}", resetResult.getMsg());
                return SampleBreakDownResult.fail("重置混样编号失败: " + resetResult.getMsg());
            }
            
            // 8. 测试矩阵预处理 - 提取请求的测试线ID集合
            List<TestMatrixReq> requestMatrixs = context.getReqObject().getMatrixs();
            if (requestMatrixs == null) {
                requestMatrixs = new ArrayList<>();
            }
            Set<String> requestTestLineIds = new HashSet<>();
            requestMatrixs.forEach(matrix -> {
                requestTestLineIds.add(matrix.getTestLineInstanceId());
            });
            
            // 将请求的测试线ID集合设置到上下文中，供后续组件使用
            context.setRequestTestLineIds(requestTestLineIds);
            
            // 9. 检查测试线是否存在样品
//            List<String> testLineInstanceIds = testLineManager.getTestLineInstanceIds(context.getOrder().getID());
//            // 使用AtomicBoolean解决lambda表达式中变量必须是final或effectively final的问题
//            final AtomicBoolean validationFailed = new AtomicBoolean(false);
//            checkUnAssginSample(context, requestMatrixs, testLineInstanceIds, validationFailed);
//            if(validationFailed.get()){
//                return SampleBreakDownResult.fail("Please select at least one sample because TL has been confirmed or limit already exits");
//            }
            logger.info("【样品分解验证器】所有基础验证通过，订单号: {}", context.getReqObject().getOrderNo());
            return SampleBreakDownResult.success(context);
            
        } catch (Exception e) {
            logger.error("【样品分解验证器】验证过程发生异常，订单号: {}", context.getReqObject().getOrderNo(), e);
            return SampleBreakDownResult.fail("验证过程发生异常: " + e.getMessage());
        }
    }

    /**
     * 检查未分配样品的测试线状态
     * 
     * 业务规则：已确认的测试线必须包含测试矩阵信息
     * 
     * @param context 样品分解上下文
     * @param requestMatrixs 请求的测试矩阵列表
     * @param testLineInstanceIds 测试线实例ID列表
     * @param validationFailed 验证失败标记
     */
    private void checkUnAssginSample(SampleBreakDownContext context, List<TestMatrixReq> requestMatrixs, 
                                   List<String> testLineInstanceIds, AtomicBoolean validationFailed) {
        
        // 构建测试线ID与测试矩阵的映射关系
        Map<String, List<TestMatrixReq>> testLineToMatrixMap = buildTestLineMatrixMap(requestMatrixs);
        
        // 检查每个测试线的状态
        for (String testLineInstanceId : testLineInstanceIds) {
            if (isInvalidTestLineState(testLineInstanceId, testLineToMatrixMap)) {
                validationFailed.set(true);
                logger.error("【样品分解验证器】测试线已确认但缺少matrix信息，测试线ID: {}, 订单号: {}",
                        testLineInstanceId, context.getReqObject().getOrderNo());
            }
        }
    }
    
    /**
     * 构建测试线ID与测试矩阵的映射关系
     * 
     * @param requestMatrixs 请求的测试矩阵列表
     * @return 测试线ID到测试矩阵列表的映射
     */
    private Map<String, List<TestMatrixReq>> buildTestLineMatrixMap(List<TestMatrixReq> requestMatrixs) {
        if (CollectionUtils.isEmpty(requestMatrixs)) {
            return Collections.emptyMap();
        }
        return requestMatrixs.stream()
                .collect(Collectors.groupingBy(TestMatrixReq::getTestLineInstanceId));
    }
    
    /**
     * 判断测试线状态是否无效
     * 
     * 无效状态：测试线已确认但没有对应的测试矩阵信息
     * 
     * @param testLineInstanceId 测试线实例ID
     * @param testLineToMatrixMap 测试线到矩阵的映射
     * @return true如果测试线状态无效
     */
    private boolean isInvalidTestLineState(String testLineInstanceId, Map<String, List<TestMatrixReq>> testLineToMatrixMap) {
        // 检查测试线是否已确认
        boolean isTestLineConfirmed = testLineBizService.hasTestLineConfirmed(testLineInstanceId);
        if (!isTestLineConfirmed) {
            return false;
        }
        
        // 检查是否有对应的测试矩阵信息
        List<TestMatrixReq> matrixList = testLineToMatrixMap.get(testLineInstanceId);
        boolean hasMatrixInfo = CollectionUtils.isNotEmpty(matrixList);
        
        // 已确认的测试线必须有测试矩阵信息
        return !hasMatrixInfo;
    }
}