package com.sgs.otsnotes.domain.service.subcontract.starlims.context;

import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description StarLims报告处理结果
 * 封装处理结果信息
 * <AUTHOR>
 * @Date 2024-03-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StarLimsReportResult {
    /**
     * 是否成功
     */
    @Builder.Default
    private boolean success = true;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 报告编号
     */
    private String reportNo;
    
    /**
     * 扩展数据
     */
    @Builder.Default
    private Map<String, Object> extraData = new HashMap<>();
    
    /**
     * 设置失败结果
     * @param message 失败消息
     */
    public void fail(String message) {
        this.success = false;
        this.message = message;
    }
    
    /**
     * 设置成功结果
     * @param message 成功消息
     */
    public void success(String message) {
        this.success = true;
        this.message = message;
    }
    
    /**
     * 添加扩展数据
     * @param key 键
     * @param value 值
     */
    public void addExtraData(String key, Object value) {
        this.extraData.put(key, value);
    }
} 