package com.sgs.otsnotes.domain.service.copy;

import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.domain.service.reportdata.ReportDataDealService;
import com.sgs.otsnotes.domain.service.reportdata.copy.ReportDataAmendCopyService;
import com.sgs.otsnotes.domain.service.reportdata.copy.ReportDataReplaceCopyService;
import com.sgs.otsnotes.domain.service.reportdata.copy.ReportDataSplitCopyService;
import com.sgs.otsnotes.facade.model.annotation.CopyServiceType;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.enums.OrderBizType;
import com.sgs.otsnotes.facade.model.ordercopy.SysCopyInfo;
import com.sgs.otsnotes.integration.v2.ReportDataClientV2;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.v2.ReportTestDataService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.sgs.framework.model.enums.OrderCopyType.*;

@Service
@CopyServiceType(copyType = {
    Extract,
    TranslationReport,
    Replace,
    TestLine,
    Sample,
    Conclusion
}, bizType = OrderBizType.ReportTestData)
public class ReportDataCopyService extends BaseCopyService<String, List<ReportTestDataInfo>> {
    private static final Logger logger = LoggerFactory.getLogger(ReportDataCopyService.class);

    @Autowired
    private ReportDataClientV2 subContractDataClient;

    @Autowired
    private ReportTestDataService reportTestDataService;

    @Autowired
    private ReportDataDealService reportDataDealService;

    @Autowired
    private ReportDataAmendCopyService amendCopyService;

    @Autowired
    private ReportDataSplitCopyService splitCopyService;

    @Autowired
    private ReportDataReplaceCopyService replaceCopyService;

    /**
     * 主要处理方法，根据copyType执行不同的测试结果数据处理逻辑
     * 
     * @param reqObject1 请求对象
     * @param reqParams 复制参数
     * @return 处理结果
     */
    @Override
    protected CustomResult<List<ReportTestDataInfo>> doInvoke(String reqObject1, SysCopyInfo reqParams) {
        CustomResult<List<ReportTestDataInfo>> rspResult = new CustomResult();

        if (!reportDataDealService.queryReportDataConfig()) {
            return rspResult.success();
        }

        try {
            switch (reqParams.getCopyType()) {
                case Extract:
                case TranslationReport:
                    return amendCopyService.handleAddResultData(reqParams, reqParams.getOldReportNo());
                case Sample:
                case Conclusion:
                case TestLine:
                    // 新增ResultData场景
                    return splitCopyService.handleCopyReportDataAndInValidaOldReportNoScenario(reqParams);
                case Replace:
                    // 更新ResultData场景 (Amend-Replace)
                    return replaceCopyService.handleUpdateResultData(reqParams);
                default:
                    logger.info("不支持的操作类型: {}", reqParams.getCopyType());
                    return rspResult.fail("不支持的操作类型");
            }
        } catch (Exception e) {
            logger.error("处理testResultData失败", e);
            return rspResult.fail("处理testResultData失败: " + e.getMessage());
        }
    }


    /**
     *
     * @param allReportTestDataInfo
     * @return
     */
    @Override
    public CustomResult doCopy(List<ReportTestDataInfo> allReportTestDataInfo) {
        CustomResult rspResult = new CustomResult();

        rspResult.setSuccess(CollectionUtils.isEmpty(allReportTestDataInfo));
        if (rspResult.isSuccess()) {
            return rspResult;
        }

        // 4. 调用RD接口保存数据
        CustomResult customResult = reportDataDealService.saveReportData(allReportTestDataInfo);

        rspResult.setSuccess(customResult.isSuccess());
        return rspResult;

    }




}
