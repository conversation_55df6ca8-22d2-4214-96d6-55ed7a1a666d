package com.sgs.otsnotes.domain.service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.SortedMap;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.sgs.otsnotes.core.config.DisConf;
import com.sgs.otsnotes.domain.service.testlineremark.TestLineRemarkService;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.extsystem.facade.model.customer.rsp.CheckTestLineMappingRsp;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.datasource.DatabaseTypeEnum;
import com.sgs.framework.model.enums.CheckTypeEnum;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.enums.SheinCreateTypeEnum;
import com.sgs.framework.model.enums.SpliteMixConclusionEnum;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.grus.bizlog.common.BizLog;
import com.sgs.grus.bizlog.common.BizLogHelper;
import com.sgs.otsnotes.core.annotation.AccessRule;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.core.util.ExceptionUtil;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.core.util.StringUtil;
import com.sgs.otsnotes.dbstorages.mybatis.config.DatabaseContextHolder;
import com.sgs.otsnotes.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ConclusionInfoExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.LimitGroupMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.LimitMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.LogMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.OrderMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.PPBaseMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.PPSampleRelMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.PPTestLineRelMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ProductAttrMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportMatrixRelMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.SlimSubContractExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.SubContractExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestConditionGroupLanguageMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestConditionGroupMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestConditionMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestDataMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestLineMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestMatrixMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestPositionMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestSampleExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestSampleGroupMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestSampleLangMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestSampleMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TrimsPPTestLineRelMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.AssignSampleInfo;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.AnalyteInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.SubContractTestLineMappingMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.TestMatrixInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.AnalyteInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.AnalyteInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.GeneralOrderInstanceInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.LimitGroupInstancePO;
import com.sgs.otsnotes.dbstorages.mybatis.model.LimitInstancePO;
import com.sgs.otsnotes.dbstorages.mybatis.model.LogInfoWithBLOBs;
import com.sgs.otsnotes.dbstorages.mybatis.model.PPArtifactRelInfoWithBLOBs;
import com.sgs.otsnotes.dbstorages.mybatis.model.PPSampleRelationshipInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.ReportMatrixRelationShipInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.SlimSubcontractPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.SubContractTestLineMappingExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.SubContractTestLineMappingPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionGroupInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestMatrixInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestMatrixInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestMatrixPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleExtInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleGroupInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleLangInfoPO;
import com.sgs.otsnotes.domain.service.cache.FrameWorkService;
import com.sgs.otsnotes.domain.service.comparator.AssignSampleInfoMixComparator;
import com.sgs.otsnotes.domain.service.sample.SampleBreakDownFacadeService;
import com.sgs.otsnotes.domain.service.testline.TestLineStatusService;
import com.sgs.otsnotes.domain.service.testmatrix.MixSampleRequirementService;
import com.sgs.otsnotes.facade.model.common.BizException;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.comparator.TestSampleComparator;
import com.sgs.otsnotes.facade.model.dto.SegmentDTO;
import com.sgs.otsnotes.facade.model.dto.TestSampleExtDTO;
import com.sgs.otsnotes.facade.model.enums.BomTestLineStatus;
import com.sgs.otsnotes.facade.model.enums.CategoryType;
import com.sgs.otsnotes.facade.model.enums.ConditionStatus;
import com.sgs.otsnotes.facade.model.enums.LogOperationEnums;
import com.sgs.otsnotes.facade.model.enums.LogOperationTypeEnums;
import com.sgs.otsnotes.facade.model.enums.MatrixStatus;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.otsnotes.facade.model.enums.SampleSourceTypeEnum;
import com.sgs.otsnotes.facade.model.enums.SampleType;
import com.sgs.otsnotes.facade.model.enums.SubContractOperationTypeEnums;
import com.sgs.otsnotes.facade.model.enums.TestLineModuleType;
import com.sgs.otsnotes.facade.model.enums.TestLinePendingFlagEnums;
import com.sgs.otsnotes.facade.model.enums.TestLinePendingTypeEnums;
import com.sgs.otsnotes.facade.model.enums.TestLineStatus;
import com.sgs.otsnotes.facade.model.info.PPSampleRelInfo;
import com.sgs.otsnotes.facade.model.info.PPTestLineRelInfo;
import com.sgs.otsnotes.facade.model.info.TestSampleInfo;
import com.sgs.otsnotes.facade.model.info.matrix.TestMatrixWithSampleInfo;
import com.sgs.otsnotes.facade.model.info.sample.OriginalSampleInfo;
import com.sgs.otsnotes.facade.model.info.sample.QuerySampleInfo;
import com.sgs.otsnotes.facade.model.info.sample.TestLineSampleTypeInfo;
import com.sgs.otsnotes.facade.model.info.sample.TestSampleDTO;
import com.sgs.otsnotes.facade.model.info.sample.TestSampleExtInfo;
import com.sgs.otsnotes.facade.model.req.OrderSubContractReq;
import com.sgs.otsnotes.facade.model.req.QuerySkuCodeReq;
import com.sgs.otsnotes.facade.model.req.SampleBreakDownReq;
import com.sgs.otsnotes.facade.model.req.SampleGroupReq;
import com.sgs.otsnotes.facade.model.req.SlimMappingReq;
import com.sgs.otsnotes.facade.model.req.TestMatrixReq;
import com.sgs.otsnotes.facade.model.req.TestSampleReq;
import com.sgs.otsnotes.facade.model.req.sample.AssignSampleCancelReq;
import com.sgs.otsnotes.facade.model.req.sample.AssignSampleReq;
import com.sgs.otsnotes.facade.model.req.sample.ChangeCategoryItemReq;
import com.sgs.otsnotes.facade.model.req.sample.ChangeCategoryReq;
import com.sgs.otsnotes.facade.model.req.sample.CheckAssignSampleReq;
import com.sgs.otsnotes.facade.model.req.sample.CheckMixAssignSampleReq;
import com.sgs.otsnotes.facade.model.req.sample.CopyTestLineGetSampleReq;
import com.sgs.otsnotes.facade.model.req.sample.DeleteOriginalSampleReq;
import com.sgs.otsnotes.facade.model.req.sample.OriginalSampleByOrderNoReq;
import com.sgs.otsnotes.facade.model.req.sample.SampleComponentItemReq;
import com.sgs.otsnotes.facade.model.req.sample.SampleComponentReq;
import com.sgs.otsnotes.facade.model.req.sample.SampleExtInfoReq;
import com.sgs.otsnotes.facade.model.req.sample.SampleIdsReq;
import com.sgs.otsnotes.facade.model.req.sample.SaveAssignSampleReq;
import com.sgs.otsnotes.facade.model.req.sample.TestSampleCheckReq;
import com.sgs.otsnotes.facade.model.req.sample.TestSampleSubmitReq;
import com.sgs.otsnotes.facade.model.req.testLine.TestLineInstanceIdsReq;
import com.sgs.otsnotes.facade.model.req.trims.TestReq;
import com.sgs.otsnotes.facade.model.rsp.OrderInfoForSlimRsp;
import com.sgs.otsnotes.facade.model.rsp.PPRelationShipRsp;
import com.sgs.otsnotes.facade.model.rsp.assignsample.AssignSampleRsp;
import com.sgs.otsnotes.facade.model.rsp.assignsample.AssignSampleTips;
import com.sgs.otsnotes.facade.model.rsp.assignsample.SamplePPSRsp;
import com.sgs.otsnotes.facade.model.rsp.assignsample.SampleRsp;
import com.sgs.otsnotes.facade.model.rsp.sample.ChildSampleRsp;
import com.sgs.otsnotes.facade.model.rsp.sample.CopyTestSampleRsp;
import com.sgs.otsnotes.facade.model.rsp.sample.TestResultRsp;
import com.sgs.otsnotes.facade.model.rsp.sample.TestSampleSubmitRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.TestLineMatrixSampleRsp;
import com.sgs.otsnotes.infra.repository.testline.TestLineRepository;
import com.sgs.otsnotes.infra.service.TestSampleLangService;
import com.sgs.otsnotes.integration.DffClient;
import com.sgs.otsnotes.integration.FrameWorkClient;
import com.sgs.otsnotes.integration.OrderClient;
import com.sgs.otsnotes.integration.TokenClient;
import com.sgs.otsnotes.integration.TrfRelClient;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.dto.order.OrderTrfRelationshipDTO;
import com.sgs.preorder.facade.model.dto.order.OrderTrfRelationshipItemDTO;
import com.sgs.preorder.facade.model.dto.productextfields.SampleExtFieldDto;
import com.sgs.preorder.facade.model.enums.OperationType;
import com.sgs.preorder.facade.model.info.OrderTrfRelInfo;
import com.sgs.preorder.facade.model.info.ProductInfo;
import com.sgs.preorder.facade.model.info.trfdff.TrfRelComponentExtInfo;
import com.sgs.preorder.facade.model.rsp.order.OrderSimplifyInfoRsp;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;

@Service
public class SampleService{
    private static final Logger logger = LoggerFactory.getLogger(SampleService.class);
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private TestSampleMapper testSampleMapper;
    @Autowired
    private TestSampleGroupMapper testSampleGroupMapper;
    @Autowired
    private TestMatrixMapper testMatrixMapper;
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private TestConditionMapper testConditionMapper;
    @Autowired
    private SubContractExtMapper subContractMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private SlimSubContractExtMapper slimSubContractExtMapper;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private TrfRelClient trfRelClient;
    @Autowired
    private SubContractTestLineMappingMapper subContractTestLineMappingMapper;
    @Autowired
    private AnalyteInfoMapper analyteInfoMapper;
    @Autowired
    private TrimsPPTestLineRelMapper trimsPPTestLineRelMapper;
    //这是本地的扩展mapper
    @Autowired
    private PPTestLineRelMapper ppTestLineRelMapper;
    @Autowired
    private PPSampleRelMapper ppSampleRelMapper;
    @Autowired
    private TestDataMapper testDataMapper;
    @Autowired
    private TestMatrixInfoMapper testMatrixInfoMapper;
    @Autowired
    private ConclusionInfoExtMapper conclusionInfoExtMapper;
    @Autowired
    private LimitGroupMapper limitGroupMapper;
    @Autowired
    private LimitMapper limitMapper;
    @Autowired
    private TestConditionGroupMapper testConditionGroupMapper;
    @Autowired
    private TestConditionGroupLanguageMapper testConditionGroupLanguageMapper;
    @Autowired
    private TestPositionMapper testPositionMapper;
    @Autowired
    private ProductAttrMapper productAttrMapper;
    @Autowired
    private ReportMatrixRelMapper reportMatrixRelMapper;
    @Autowired
    private TestLinePendingService testLinePendingService;
    @Autowired
    private TestMatrixService testMatrixService;
    @Autowired
    private TestLineStatusService testLineStatusService;
    @Autowired
    private LogMapper logMapper;
    @Autowired
    private TestSampleExtMapper testSampleExtMapper;
    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private FrameWorkClient frameWorkClient;
    @Autowired
    private  PPBaseMapper ppBaseMapper;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private CommonService commonService;
    @Autowired
    private TestSampleLangMapper testSampleLangMapper;
    @Autowired
    private TestSampleLangService testSampleLangService;
    @Autowired
    private DffClient dffClient;
    @Autowired
    private FrameWorkService frameWorkService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private TestLineRepository testLineRepository;
    @Autowired
    private TestLineRemarkService testLineRemarkService;
    @Autowired
    private MixSampleRequirementService mixSampleRequirementService;
    @Autowired
    private SampleBreakDownFacadeService sampleBreakDownFacadeService;
    @Autowired
    private DisConf disConf;

    public CustomResult updateBreakDown(SampleBreakDownReq reqObject) {
        CustomResult rspReuslt = new CustomResult();
        String key = String.format("%s_%s_%s", this.getClass().getName(),"updateBreakDown",reqObject.getOrderNo());
        RLock lock = redissonClient.getLock(key);

        try{
            //拿不到锁，提示不能重复请求
            if(!lock.tryLock(1L,10L, TimeUnit.SECONDS)){
                return rspReuslt.fail(String.format("请稍后，当前%s的请求还未处理结束", reqObject.getOrderNo()));
            }
        }catch (Exception e){
            //如果产生异常，就不用拿着锁了，直接释放掉
            try{
                if(lock.isLocked()){
                    lock.unlock();
                }
            }catch (Exception ex){

            }
            return rspReuslt.fail(String.format("请稍后，当前%s的请求还未处理结束", reqObject.getOrderNo()));
        }

        try{
            logger.info("SampleService.updateBreakDown Order(BOM_{}),reqObject：{}.",reqObject.getOrderNo(), reqObject);

            // 【重构】使用重构后的处理逻辑
            if(disConf.isSwitchNewUpdateBreakDown()) {
                return sampleBreakDownFacadeService.executeRefactoredBreakDown(reqObject);
            }else {
                //旧版本
                return updateBreakDownOld(reqObject);
            }
          //  return updateBreakDownOld(reqObject);
        }catch (Exception ex){
            logger.error("SampleService.updateBreakDown, OrderNo_{}（Req：{}） Error：",reqObject.getOrderNo(), reqObject, ex);
            rspReuslt.setSuccess(false);
            rspReuslt.setMsg(ex.getMessage());
            rspReuslt.setStackTrace(ExceptionUtil.getStackTrace(ex));
            return rspReuslt;
        }finally {
            //执行结束，释放锁
            if(lock.isLocked()){
                lock.unlock();
            }
        }
    }
    /**
     * @Desc Bom 回传
     * @param reqObject
     * @return
     */
//    @AccessRule(subContractType = SubContractOperationTypeEnums.UpdateBreakDown)
    public CustomResult updateBreakDownOld(SampleBreakDownReq reqObject) {
        CustomResult rspReuslt = new CustomResult();
        String key = String.format("%s_%s_%s", this.getClass().getName(),"updateBreakDown",reqObject.getOrderNo());
        RLock lock = redissonClient.getLock(key);
        try{
            //拿不到锁，提示不能重复请求
            if(!lock.tryLock(1L,10L, TimeUnit.SECONDS)){
                return rspReuslt.fail(String.format("请稍后，当前%s的请求还未处理结束", reqObject.getOrderNo()));
            }
        }catch (Exception e){
            //如果产生异常，就不用拿着锁了，直接释放掉
            try{
                if(lock.isLocked()){
                    lock.unlock();
                }
            }catch (Exception ex){

            }
            return rspReuslt.fail(String.format("请稍后，当前%s的请求还未处理结束", reqObject.getOrderNo()));
        }

        try{
            logger.info("SampleService.updateBreakDown Order(BOM_{}),reqObject：{}.",reqObject.getOrderNo(), reqObject);
            /*
             * 1、只有沒有Matrix的Sample才能標記Not Test
             * 2、如果TL是NC或者Sample是Not Test，則不能建立Matrix
             * 3、SampleParentID+SampleType检查类型，是否跨界如：O
             * 4、NC的样品，Bom不再回传过来，需要本地做处理 add vincent 2020年12月1日
             * */
            rspReuslt = this.checkSampleParameters(reqObject);
            if (!rspReuslt.isSuccess()){
                return rspReuslt;
            }
            rspReuslt = new CustomResult();

            GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfo(reqObject.getOrderNo());
            if (order == null) {
                rspReuslt.setMsg(String.format("当前OrderNo [%s] 不存在.", reqObject.getOrderNo()));
                return rspReuslt;
            }
            OrderInfoDto orderInfo = orderClient.getOrderInfoByOrderNo(reqObject.getOrderNo());
            if (OperationType.check(orderInfo.getOperationType(), OperationType.NewSubContract)) {
                return rspReuslt.fail(String.format("当前OrderNo [%s] 不能此操作.", reqObject.getOrderNo()));
            }
            // POSL-2363
            ReportInfoPO oldReport = reportMapper.getReportByOrderNo(reqObject.getOrderNo());
            if (oldReport == null){
                rspReuslt.setMsg(String.format("未找到当前OrderNo [%s] 下的Report 信息.", reqObject.getOrderNo()));
                return rspReuslt;
            }
            if (!ReportStatus.check(oldReport.getReportStatus(), ReportStatus.New, ReportStatus.Combined, ReportStatus.Draft)){
                rspReuslt.setMsg(String.format("当前Report[%s] 的状态为New、Combin、Draft才允许回传.", oldReport.getReportStatus()));
                return rspReuslt;
            }

            verifyDuplicateSamples(reqObject, order.getID());
            // 重新计算SampleSeq
            this.recalculateSort(reqObject.getSamples(), null);
            //
            rspReuslt = this.resetMixSampleNo(reqObject);
            if (!rspReuslt.isSuccess()){
                return rspReuslt;
            }
            rspReuslt = new CustomResult();

            List<TestMatrixReq> requestMatrixs = reqObject.getMatrixs();
            if (requestMatrixs == null){
                requestMatrixs = Lists.newArrayList();
            }
            Set<String> requestTestLineIds = Sets.newHashSet();
            requestMatrixs.forEach(matrix->{
                requestTestLineIds.add(matrix.getTestLineInstanceId());
            });



            // 过滤 Not Test // Applicable
            Map<String, TestSampleInfoPO> oldSampleMaps = Maps.newHashMap();
            Map<String, SampleType> oldSampleIds = Maps.newHashMap();
            Map<String, PPTestLineRelInfo> oldPPSampleRelMaps = Maps.newHashMap();
            Map<String, String> oldSampleGroupIds = Maps.newHashMap();

            Set<String> phySampleIds = Sets.newHashSet();
            List<TestSampleInfoPO> oldSamples = testSampleMapper.getSampleByOrderNo(reqObject.getOrderNo());
            //原样NC标记，因为最新逻辑2020年12月1日 ，Bom 不会回传NC的样品回来了，需要本地做处理

            Map<String, Boolean> originalSampleNCMap = getOriginalSampleNCMap(oldSamples, phySampleIds, oldSampleIds, oldSampleMaps);
            List<PPSampleRelInfo> oldPPSampleRels = testSampleMapper.getPPSampleRelByOrderId(order.getID());
            setPPsampleRelMap(oldPPSampleRels, phySampleIds, requestTestLineIds, oldPPSampleRelMaps);

            List<TestSampleGroupInfoPO> oldSampleGroups = getTestSampleGroupInfoPOS(reqObject, oldSampleIds, oldSampleGroupIds);

            // 过滤 TL是NC、activeIndicator 为 0
            Map<String, TestLineInstancePO> orderMatchedTestLineMaps = Maps.newHashMap();
            DatabaseContextHolder.setDatabaseRouting(DatabaseTypeEnum.Master,false);
            List<TestLineInstancePO> orderTestLines = testLineRepository.getTestLineByOrderId(order.getID());
            for (TestLineInstancePO oldTestline: orderTestLines){
                if (!requestTestLineIds.contains(oldTestline.getID())){
                    continue;
                }
                if (!TestLineStatus.check(oldTestline.getTestLineStatus(), TestLineStatus.Typing, TestLineStatus.Entered)){
                    rspReuslt.setMsg(String.format("当前Test Line(%s)状态为%s，只允许Typing的状态回传.", oldTestline.getTestLineID(), TestLineStatus.findStatus(oldTestline.getTestLineStatus())));
                    return rspReuslt;
                }
                orderMatchedTestLineMaps.put(oldTestline.getID(), oldTestline);
            }
            Map<String, TestMatrixPO> oldTestMatrixMaps = Maps.newHashMap();
            Map<String, Set<String>> oldMatrixSampleIds = Maps.newHashMap();
            setOldTestMatrixMaps(order, oldMatrixSampleIds, oldTestMatrixMaps);

            /*
             * 1、检查是否OriginalSample 是否一致
             * 2、检查是否跨Order的Sample存在
             * 3、tre_report_matrix_relationship
             * 4、tre_pp_sample_relationship
             * */

            // 检查Nc
            rspReuslt = this.checkSampleNc(reqObject, oldSamples, oldMatrixSampleIds, oldSampleGroups);
            if (!rspReuslt.isSuccess()){
                return rspReuslt;
            }
            rspReuslt = new CustomResult();

            List<TestSampleReq> samples = reqObject.getSamples();
            List<TestSampleGroupInfoPO> testSampleGroups = Lists.newArrayList();
            List<TestSampleInfoPO> testSamples = Lists.newArrayList();
            List<TestSampleLangInfoPO> testSampleLangs = Lists.newArrayList();
            Set<String> newSampleIds = Sets.newHashSet();
            Set<String> sampleSetIds = Sets.newHashSet();

            Set<String> testSampleIds = samples.stream().map(TestSampleReq::getSampleId).collect(Collectors.toSet());
            Map<String,List<TestSampleLangInfoPO>> langMap = Maps.newHashMap();
            if(!CollectionUtils.isEmpty(testSampleIds)){
                List<TestSampleLangInfoPO> langs = testSampleLangMapper.getTestSampleListIds(testSampleIds);
                langMap = langs.stream().collect(Collectors.groupingBy(TestSampleLangInfoPO::getSampleId));
            }
            List<String> delSampleLangIds = Lists.newArrayList();
            TestSampleInfoPO testSample;
            SampleType parentSampleType;
            for (TestSampleReq sample : samples) {
                SampleType sampleType = SampleType.findType(sample.getSampleType());
                if (sampleType == null) {
                    rspReuslt.setMsg(String.format("未找到对应的sample(%s)类型.", sample.getSampleNo()));
                    return rspReuslt;
                }
                if (sample.getNoTest() == null){
                    sample.setNoTest(false);
                }
                if (sampleType == SampleType.OriginalSample) {
                    String originalSampleId = sample.getSampleId();
                    if (StringUtils.isBlank(originalSampleId)){
                        rspReuslt.setMsg(String.format("对应的sample(%s)，ExecutionSystemSampleId 属性不能为空.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    TestSampleInfoPO sampleItem = oldSampleMaps.get(originalSampleId);
                    if (sampleItem == null){
                        rspReuslt.setMsg(String.format("未找该Original Sample(%s).", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    if (!SampleType.equals(sampleItem.getSampleType(), SampleType.OriginalSample)){
                        rspReuslt.setMsg(String.format("请求的Original Sample(%s)类型无效.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    //这个判断的场景是，同步之后，notes做了NC,但是Bom 没有再同步
                    Boolean applicable = sampleItem.getApplicable();
                    if (applicable != null && applicable.booleanValue()){
                        rspReuslt.setMsg(String.format("请求的原样Sample(%s)已被Not Test，无法操作.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    if (oldSampleIds.containsKey(originalSampleId)){
                        oldSampleIds.remove(originalSampleId);
                    }
                    sampleSetIds.add(originalSampleId);
                    continue;
                }
                if (sample.getNoTest() && oldMatrixSampleIds.containsKey(sample.getSampleId())){
                    rspReuslt.setMsg(String.format("当前Sample(%s)已Not Test，无法Matrix.", sample.getSampleNo()));
                    return rspReuslt;
                }
                if (StringUtils.isBlank(sample.getSampleId()) || StringUtils.isBlank(sample.getSampleNo())){
                    rspReuslt.setMsg(String.format("当前SampleId(%s)或SampleNo(%s)不能为空.", sample.getSampleId(), sample.getSampleNo()));
                    return rspReuslt;
                }
                parentSampleType = this.findParentSampleType(samples, sample);
                TestSampleReq parentSample = this.findParentSample(samples,oldSamples, sample);
                Boolean parentSampleIsNC = originalSampleNCMap.get(parentSample.getSampleId());
                switch (sampleType) {
                    case Sample: // 102
                        if (parentSampleType == null || parentSampleType != SampleType.OriginalSample || parentSampleIsNC) {
                            rspReuslt.setMsg(String.format("未找到对应的sample(%s) 的父样,或者父样已经Not Test", sample.getSampleNo()));
                            return rspReuslt;
                        }
                        // 是否设置为ShareSample，即105
                        rspReuslt = this.shareSample(reqObject, oldSampleGroupIds, testSampleGroups, oldSampleMaps, sample);
                        if (!rspReuslt.isSuccess()) {
                            return rspReuslt;
                        }
                        sampleType = SampleType.findType(sample.getSampleType());
                        break;
                    case SubSample: // 103
                        if (parentSampleType == null || parentSampleType != SampleType.OriginalSample || parentSampleIsNC) {
                            rspReuslt.setMsg(String.format("未找到对应的sample(%s) 的父样,或者父样已经Not Test", sample.getSampleNo()));
                            return rspReuslt;
                        }
                        break;
                    case MixSample:  // 104 可以是任何样品
                        rspReuslt = this.mixSample(reqObject, oldSampleGroupIds, testSampleGroups, sample);
                        if (!rspReuslt.isSuccess()) {
                            return rspReuslt;
                        }
                        break;
                    case ShareSample:  // 105 该值只能是原样，且不能删除
                        if (parentSampleType == null || parentSampleType != SampleType.OriginalSample || parentSampleIsNC) {
                            rspReuslt.setMsg(String.format("未找到对应的sample(%s) 的父样,或者父样已经Not Test", sample.getSampleNo()));
                            return rspReuslt;
                        }
                        rspReuslt = this.shareSample(reqObject, oldSampleGroupIds, testSampleGroups, oldSampleMaps, sample);
                        if (!rspReuslt.isSuccess()) {
                            return rspReuslt;
                        }
                        sampleType = SampleType.findType(sample.getSampleType());
                        break;
                }
                //校验通过，原DB数据就不需要处理了
                if (oldSampleIds.containsKey(sample.getSampleId())){
                    oldSampleIds.remove(sample.getSampleId());
                }
                if(oldSampleIds.containsKey(parentSample.getSampleId())){
                    oldSampleIds.remove(parentSample.getSampleId());
                }
                testSample = new TestSampleInfoPO();
                // 检查SampleId是否当前OrderNo下
                if (!oldSampleMaps.containsKey(sample.getSampleId())){
                    newSampleIds.add(sample.getSampleId());
                }
                testSample.setID(sample.getSampleId());
                testSample.setOrderNo(reqObject.getOrderNo());
                testSample.setCategory(sampleType.getCategoryChem());
                if (!sample.isChem() && (sampleType == SampleType.MixSample)){
                    testSample.setCategory(sampleType.getCategoryPhy());
                }
                testSample.setSampleParentID(sample.getSampleParentId());
                testSample.setSampleNo(sample.getSampleNo());
                testSample.setDescription(sample.getSampleDesc());
                testSample.setSampleType(sampleType.getSampleType());
                testSample.setSampleSeq(sample.getSampleSeq());
                /*testSample.setMaterial(StringUtil.substr(sample.getMaterial(), 300));*/
                testSample.setColor(sample.getColor());
                testSample.setEndUse(sample.getEndUse());
                testSample.setApplicable(sample.getNoTest());
                testSample.setSampleRemark(sample.getRemark());
                testSample.setOtherSampleInfo(sample.getOtherSampleInfo());
                testSample.setSourceType(SampleSourceTypeEnum.SGS.getCode());

                testSample.setActiveIndicator(true);
                testSample.setCreatedBy("BOM");
                testSample.setCreatedDate(DateUtils.getNow());
                testSample.setModifiedBy("BOM");
                testSample.setModifiedDate(DateUtils.getNow());
                testSamples.add(testSample);

                // TODO Sample Lang
                if(!SampleType.check(sample.getSampleType(),SampleType.MixSample)){
                    List<TestSampleLangInfoPO> langs = langMap.get(sample.getSampleId());
                    testSampleLangs.addAll(testSampleLangService.getSampleLangInfoList(testSample, sample.getMaterials(),langs));
                    if(!CollectionUtils.isEmpty(langs)){
                        delSampleLangIds.addAll(langs.stream().map(TestSampleLangInfoPO::getId).collect(Collectors.toList()));
                    }
                }
                sampleSetIds.add(testSample.getID());
                rspReuslt = new CustomResult();
            }
            List<String> delSampleIds = Lists.newArrayList();
            Iterator<Map.Entry<String, SampleType>> sampleIds = oldSampleIds.entrySet().iterator();
            while (sampleIds.hasNext()) {
                Map.Entry<String, SampleType> entry = sampleIds.next();
                TestSampleInfoPO oldSample = oldSampleMaps.get(entry.getKey());
                String sampleNo = "";
                if (oldSample != null){
                    sampleNo = oldSample.getSampleNo();
                }
                //当前原样如果是NC ，就不用判断删除逻辑了，因为Bom不会回传NC样品
                Boolean isNC = originalSampleNCMap.get(entry.getKey());
                isNC = isNC!=null ?isNC:false;
                if ((entry.getValue() == null || entry.getValue() == SampleType.OriginalSample) && !isNC){
                    rspReuslt.setMsg(String.format("Original Sample(%s)不能删除.", sampleNo));
                    return rspReuslt;
                }
                delSampleIds.add(entry.getKey());
            }

            List<String> delSampleGroupIds = Lists.newArrayList();
            Iterator<Map.Entry<String, String>> sampleGroupIds = oldSampleGroupIds.entrySet().iterator();
            while (sampleGroupIds.hasNext()) {
                Map.Entry<String, String> entry = sampleGroupIds.next();
                if (StringUtils.isBlank(entry.getValue())){
                    continue;
                }
                delSampleGroupIds.add(entry.getValue());
            }

            if (!newSampleIds.isEmpty()){
                List<String> orderNos = testSampleMapper.getSampleIdList(Lists.newArrayList(newSampleIds));
                if (orderNos != null && orderNos.size() > 0 &&  !orderNos.contains(reqObject.getOrderNo())){
                    rspReuslt.setMsg(String.format("当前SampleIds(%s)未在Order下.", StringUtils.join(newSampleIds, ",")));
                    return rspReuslt;
                }
            }
            ReportInfoPO report = reportMapper.getReportByOrderNo(reqObject.getOrderNo());
            if (report == null){
                rspReuslt.setMsg(String.format("未找到Order(%s)下的Report信息.", reqObject.getOrderNo()));
                return rspReuslt;
            }

            CustomResult<List<TestMatrixPO>> rspMatrix = this.handleTestMatrix(order.getID(), reqObject, oldPPSampleRelMaps, sampleSetIds, oldTestMatrixMaps, orderMatchedTestLineMaps);
            if (!rspMatrix.isSuccess()){
                return rspMatrix;
            }

            List<String> conclusionTestMatrixIds = conclusionInfoExtMapper.getConclusionTestMatrixIds(order.getID());

            List<String> delPPSampleRelIds = Lists.newArrayList();
            List<String> delMatrixIds = Lists.newArrayList();
            Iterator<Map.Entry<String, TestMatrixPO>> orderTestMatrixMaps = oldTestMatrixMaps.entrySet().iterator();
            while (orderTestMatrixMaps.hasNext()) {
                Map.Entry<String, TestMatrixPO> entry = orderTestMatrixMaps.next();
                TestMatrixPO testMatrix = entry.getValue();
                TestLineInstancePO testLine = orderMatchedTestLineMaps.get(testMatrix.getTestLineInstanceID());
                if (testLine == null){
                    continue;
                }
                TestLineStatus testLineStatus = TestLineStatus.findStatus(testLine.getTestLineStatus());
                if (testLineStatus == null || !(testLineStatus == TestLineStatus.Typing || testLineStatus == TestLineStatus.DR)) {
                    rspReuslt.setMsg(String.format("当前TestLine(%s)状态为(%s)不能删除Matrix.", testMatrix.getTestLineInstanceID(), testLineStatus));
                    return rspReuslt;
                }
                if (phySampleIds.contains(testMatrix.getTestSampleID())){
                    continue;
                }
                if (!conclusionTestMatrixIds.isEmpty() && conclusionTestMatrixIds.contains(testMatrix.getID())){
                    rspReuslt.setMsg(String.format("当前TestLine(%s)已入录conclusion不能删除Matrix(%s).", testMatrix.getTestLineInstanceID(), testMatrix.getID()));
                    return rspReuslt;
                }
                delMatrixIds.add(testMatrix.getID());
            }
            // DIG-9600 增matrix 校验
            if (CollectionUtil.isNotEmpty(delSampleIds)) {
                List<TestLineSampleTypeInfo> testLineSampleInfos = testMatrixMapper.getTestLineSampleCopyTestByIds(delSampleIds);
                if (CollectionUtil.isNotEmpty(testLineSampleInfos)) {
                    for (TestLineSampleTypeInfo testLineSampleInfo : testLineSampleInfos) {
                        if (TestLineStatus.check(testLineSampleInfo.getTestLineStatus(), TestLineStatus.Typing, TestLineStatus.DR)) {
                            continue;
                        }
                        TestLineStatus testLineStatus = TestLineStatus.findStatus(testLineSampleInfo.getTestLineStatus());
                        return rspReuslt.fail(String.format("当前TestLine(%s)状态为(%s)不能删除Matrix.",
                                testLineSampleInfo.getTestLineId(), testLineStatus));
                    }
                }
            }

            List<TestMatrixPO> newTestMatrixs = rspMatrix.getData();
            //
            List<PPSampleRelationshipInfoPO> ppSampleRels = this.handlePPSampleRel(oldPPSampleRelMaps, delPPSampleRelIds, reqObject.getUserName());
            //
            List<ReportMatrixRelationShipInfoPO> reportMatrixRels = this.handleReportMatrixRel(newTestMatrixs, report.getID(), reqObject.getUserName());

            rspReuslt = validateToSlimSubcontracts(reqObject);
            if (!rspReuslt.isSuccess()){
                return rspReuslt;
            }

            // 构造slim subcontract
            List<SlimSubcontractPO> slimSubcontractList = convertToSlimSubcontracts(order, reqObject);

            int trans = transactionTemplate.execute((tranStatus)->{
                int execNum = 1;
                if (!delMatrixIds.isEmpty()){
                    execNum += testMatrixMapper.batchDelete(delMatrixIds);
                }
                if (!delPPSampleRelIds.isEmpty()){
                    execNum += testSampleMapper.delPPSampleRelInfo(delPPSampleRelIds);
                }
                if (!delSampleGroupIds.isEmpty()){
                    execNum += testSampleGroupMapper.delTestSampleGroupInfo(delSampleGroupIds);
                }
                // TODO 检查原样是未传送过来
                if (!delSampleIds.isEmpty()){
                    // 还有考虑一种情况，Sample没有删除，只是没有Assign对应的TestLine
                    execNum += testMatrixMapper.delTestMatrixBySampleIds(delSampleIds);
                    //
                    logger.info("OrderNo:{} updateBreakDown 进行db操作，delSampleId:[{}]",order.getOrderNo(), delSampleIds);
                    execNum += testSampleMapper.batchDelete(delSampleIds);
                }
                // 删除子样下对应的Sample
                if (!testSamples.isEmpty() && testSampleMapper.batchInsert(testSamples) <= 0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }
                if (!testSampleLangs.isEmpty() && testSampleLangMapper.batchInsert(testSampleLangs) <= 0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }

                if(!delSampleLangIds.isEmpty() && testSampleLangMapper.deleteByIds(delSampleLangIds) <= 0){
                    tranStatus.setRollbackOnly();
                    return execNum;
                }

                if (!testSampleGroups.isEmpty() && testSampleGroupMapper.batchInsert(testSampleGroups) <= 0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }
                if (!ppSampleRels.isEmpty() && testSampleMapper.batchSavePPSampleRelInfo(ppSampleRels) <= 0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }
                if (!newTestMatrixs.isEmpty() && testMatrixMapper.batchInsert(newTestMatrixs) <= 0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }
                if (!reportMatrixRels.isEmpty() && reportMapper.batchSaveReportMatrixRelInfo(reportMatrixRels) <= 0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }

                //处理procedure & scheme 的临表数据
                if(! CollectionUtils.isEmpty(reqObject.getSlimMapping())){
                    slimSubContractExtMapper.deleteSlimSubcontractWithoutToSlim(reqObject.getOrderNo());
                    if(! CollectionUtils.isEmpty(slimSubcontractList)) {
                        slimSubContractExtMapper.batchInsert(slimSubcontractList);
                    }
                }
                return execNum;
            });
            //
            this.matrixUpdateCondition(order.getID(), orderTestLines);

            rspReuslt.setSuccess(trans > 0);
        }catch (Exception ex){
            logger.error("SampleService.updateBreakDown, OrderNo_{}（Req：{}） Error：",reqObject.getOrderNo(), reqObject, ex);
            rspReuslt.setSuccess(false);
            rspReuslt.setMsg(ex.getMessage());
            rspReuslt.setStackTrace(ExceptionUtil.getStackTrace(ex));
        }finally {
            //执行结束，释放锁
            if(lock.isLocked()){
                lock.unlock();
            }
        }
        return rspReuslt;
    }

    public void setOldTestMatrixMaps(GeneralOrderInstanceInfoPO order, Map<String, Set<String>> oldMatrixSampleIds, Map<String, TestMatrixPO> oldTestMatrixMaps) {
        List<TestMatrixPO> orderMatrixs = testMatrixMapper.getTestMatrixByOrderId(order.getID());
        for (TestMatrixPO testMatrix:orderMatrixs){
            if (!oldMatrixSampleIds.containsKey(testMatrix.getTestSampleID())){
                oldMatrixSampleIds.put(testMatrix.getTestSampleID(), Sets.newHashSet());
            }
            oldMatrixSampleIds.get(testMatrix.getTestSampleID()).add(testMatrix.getTestLineInstanceID());
//                if (!testLineIds.contains(testMatrix.getTestLineInstanceID())){
//                    continue;
//                }
            oldTestMatrixMaps.put(String.format("%s_%s", testMatrix.getTestSampleID(), testMatrix.getTestLineInstanceID()), testMatrix);
        }
    }

    @NotNull
    public List<TestSampleGroupInfoPO> getTestSampleGroupInfoPOS(SampleBreakDownReq reqObject, Map<String, SampleType> oldSampleIds, Map<String, String> oldSampleGroupIds) {
        List<TestSampleGroupInfoPO> oldSampleGroups = testSampleGroupMapper.getSampleGroupByOrderNo(reqObject.getOrderNo());
        for (TestSampleGroupInfoPO sampleGroup: oldSampleGroups){
            if (!oldSampleIds.containsKey(sampleGroup.getSampleID())){
                continue;
            }
            oldSampleGroupIds.put(String.format("%s_%s", sampleGroup.getSampleID(), sampleGroup.getSampleGroupID()), sampleGroup.getID());
        }
        return oldSampleGroups;
    }

    public  void setPPsampleRelMap(List<PPSampleRelInfo> oldPPSampleRels, Set<String> phySampleIds, Set<String> requestTestLineIds, Map<String, PPTestLineRelInfo> oldPPSampleRelMaps) {
        PPTestLineRelInfo ppTestLineRel;
        for (PPSampleRelInfo ppSampleRel: oldPPSampleRels){
            if (phySampleIds.contains(ppSampleRel.getTestSampleId()) || !requestTestLineIds.contains(ppSampleRel.getTestLineId())){
                continue;
            }
            String tlRelKey = String.format("%s_%s", ppSampleRel.getPpTLRelId(), ppSampleRel.getTestLineId());
            if (!oldPPSampleRelMaps.containsKey(tlRelKey)){
                ppTestLineRel = new PPTestLineRelInfo();
                ppTestLineRel.setPpTLRelId(ppSampleRel.getPpTLRelId());
                ppTestLineRel.setTestSampleId(ppSampleRel.getTestSampleId());
                ppTestLineRel.setTestLineId(ppSampleRel.getTestLineId());
                oldPPSampleRelMaps.put(tlRelKey, ppTestLineRel);
            }
            if (StringUtils.isBlank(ppSampleRel.getTestSampleId()) || StringUtils.isBlank(ppSampleRel.getPpSampleRelId())){
                continue;
            }
            ppTestLineRel = oldPPSampleRelMaps.get(tlRelKey);
            ppTestLineRel.getPpSampleRelMaps().put(ppSampleRel.getTestSampleId(), ppSampleRel.getPpSampleRelId());
        }
    }

    @NotNull
    public  Map<String, Boolean> getOriginalSampleNCMap(List<TestSampleInfoPO> oldSamples, Set<String> phySampleIds, Map<String, SampleType> oldSampleIds, Map<String, TestSampleInfoPO> oldSampleMaps) {
        Set<String> sampleIdSet = Sets.newHashSet();
        Map<String,Boolean> originalSampleNCMap = Maps.newHashMap();
        for (TestSampleInfoPO sample: oldSamples){
            if(SampleType.check(sample.getSampleType(),SampleType.OriginalSample)){
                originalSampleNCMap.put(sample.getID(),sample.getApplicable());
            }

            SampleType sampleType = SampleType.findType(sample.getSampleType());
            if (sampleType != SampleType.OriginalSample && !StringUtils.equalsIgnoreCase(sampleType.getCategoryChem(), sample.getCategory())){
                phySampleIds.add(sample.getID());
                continue;
            }
            oldSampleIds.put(sample.getID(), sampleType);
            oldSampleMaps.put(sample.getID(), sample);
            sampleIdSet.add(sample.getID());
        }
        return originalSampleNCMap;
    }

    /**
     * 校验是否含有重复assign的sample
     *
     * 业务逻辑：
     * 1. 过滤掉sampleId为空的TestMatrixReq（unassign操作时sampleId为空字符串）
     * 2. 按sampleId + testLineInstanceId + ppId分组检测重复assign
     * 3. 如果同一组合出现多次，则抛出重复assign异常
     *
     * @param reqObject 样品分解请求对象
     * @param orderId 订单ID
     * @throws BizException 当检测到重复assign时抛出异常
     */
    public void verifyDuplicateSamples(SampleBreakDownReq reqObject, String orderId) {
        // 过滤掉sampleId为空的matrix记录（unassign操作时sampleId会是空字符串）
        Map<String, List<TestMatrixReq>> matrixMaps = reqObject.getMatrixs().stream()
                .filter(matrix -> StringUtils.isNotBlank(matrix.getSampleId())) // 过滤空sampleId
                .collect(Collectors.groupingBy(o -> o.getSampleId().toLowerCase()+ "&" + o.getTestLineInstanceId().toLowerCase() + "&" + o.getPpId()));

        Map<String, String> sampleNoMap = reqObject.getSamples().stream().collect(Collectors.toMap(TestSampleReq::getSampleId, TestSampleReq::getSampleNo));
        Map<String, PPRelationShipRsp> relationShipRspMap = ppBaseMapper.getPpBaseInfoByOrderId(orderId).stream().collect(Collectors.toMap(o -> o.getPpRelationId(), o -> o));

        StringBuilder sb = new StringBuilder();
        matrixMaps.forEach((k ,v) -> {
            // 只有当同一个组合键出现多次时才认为是重复assign
            if (v.size() <= 1) {
                return;
            }
            String[] idArr = k.split("&");
            TestLineInstancePO testLineInstancePO = testLineRepository.getBaseTestLineById(idArr[1]);
            String sampleNo = sampleNoMap.get(idArr[0]);
            PPRelationShipRsp ppRelationShipRsp = relationShipRspMap.get(idArr[2]);
            String ppNo = Objects.isNull(ppRelationShipRsp) ? "" : ppRelationShipRsp.getPpNo() + "";
            sb.append(String.format(Constants.DUPLICATE_ASSIGN_SAMPLE_MSG, ppNo, testLineInstancePO.getTestLineID(), sampleNo)).append(";");
        });
        String errMsg = sb.toString();
        if (StringUtils.isNoneEmpty(errMsg)) {
            throw new BizException(errMsg);
        }
    }

    private void verifySampleCanAssignOrUnAssign(Integer reportStatus, SampleBreakDownReq reqObject, GeneralOrderInstanceInfoPO order,
                                                 List<TestSampleInfoPO> samples, Set<String> sampleIdSet) {
        List<TestMatrixPO> testMatrixPOS = testMatrixMapper.getTestMatrixByOrderId(order.getID());
        Map<String, TestMatrixPO> testMatrixPOMap = testMatrixPOS.stream().collect(
                Collectors.toMap(o -> o.getTestSampleID() + "&" + o.getTestLineInstanceID(), o -> o));
        Map<String, TestMatrixReq> matrixReqMap = new HashMap<>();
        Set<String> matrixSampleIdSet = new HashSet<>();
        reqObject.getMatrixs().forEach(o ->{
            matrixReqMap.put(o.getSampleId() + "&" + o.getTestLineInstanceId(), o);
            matrixSampleIdSet.add(o.getSampleId());
        });

        Set<String> assignTestLineSet = reqObject.getMatrixs().stream()
                .filter(o -> !testMatrixPOMap.containsKey(o.getSampleId() + "&" + o.getTestLineInstanceId()))
                .map(t -> t.getTestLineInstanceId()).collect(Collectors.toSet());
        Set<String> unAssignTestLineDbSet = testMatrixPOS.stream()
                .filter(o -> !matrixReqMap.containsKey(o.getTestSampleID() + "&" + o.getTestLineInstanceID()))
                .map(t -> t.getTestSampleID() + "&" + t.getTestLineInstanceID()).collect(Collectors.toSet());

//        verifySamplesWithoutAssign(sampleIdSet, reqObject, testMatrixPOS.stream()
//                .filter(o -> !matrixReqMap.containsKey(o.getTestSampleID() + "&" + o.getTestLineInstanceID()))
//                .map(t -> t.getTestSampleID()).collect(Collectors.toSet()));

        Map<String, TestSampleInfoPO> sampleMap = samples.stream().collect(Collectors.toMap(o -> o.getID(), o -> o));
        List<TestLineMatrixSampleRsp> testLineMatrixSampleRspList = testLineMapper.getTestLineMatrixSampleRspByOrderId(order.getID(), order.getOrderNo());
        StringBuilder sb = new StringBuilder();
        testLineMatrixSampleRspList.forEach(o -> {
//            sampleIdSet.add(o.getTestSampleID());
            String key = o.getTestSampleID() + "&" + o.getTestLineInstanceId();
            TestSampleInfoPO testSampleInfoPO = sampleMap.get(o.getTestSampleID());
            if (Objects.nonNull(testSampleInfoPO) && SampleType.check(testSampleInfoPO.getSampleType(),SampleType.OriginalSample)) {
                if (unAssignTestLineDbSet.contains(key) || (assignTestLineSet.contains(o.getTestLineInstanceId()) && StringUtils.isEmpty(o.getTestSampleID()))) {
                    throw new BizException(String.format(Constants.ORIGINAL_SAMPLE_NO_OPERATION, testSampleInfoPO.getSampleNo()));
                }
            }

            String labSectionName = o.getLabSectionName();
            //TL chem 分包必须要分到chem实验室，正常单productLineAbbr cchemLab，或者 labSection contins chem
            boolean isExclude = (StringUtils.isNoneBlank(o.getTestLineInstanceId())
                    && StringUtils.isNoneEmpty(labSectionName) && labSectionName.toLowerCase().contains("chem"))
                    || StringUtils.equalsIgnoreCase(o.getProductLineAbbr(), "CChemLab")
                    || (StringUtils.isNotBlank(labSectionName) && labSectionName.toLowerCase().contains("chem"));
            if (!isExclude){
                return;
            }
            Integer bomTestLineStatus = buildTestLineStatusResult(reportStatus, o.getTestLineStatus());
            if (BomTestLineStatus.NEW.getCode() == bomTestLineStatus) {
                return;
            }
            if (BomTestLineStatus.TESTING.getCode() == bomTestLineStatus) {
                if (unAssignTestLineDbSet.contains(key)) {
                    sb.append(String.format(Constants.TESTING_TO_UN_ASSIGN_MSG, o.getTestLineID()) + ";");
                }
                return;
            }
            if (assignTestLineSet.contains(o.getTestLineInstanceId()) && StringUtils.isEmpty(o.getTestSampleID())) {
                sb.append(String.format(Constants.COMPLETED_TO_ASSIGN_MSG, o.getTestLineID()) + ";");
            }
            if (unAssignTestLineDbSet.contains(key)) {
                sb.append(String.format(Constants.COMPLETED_TO_UN_ASSIGN_MSG, o.getTestLineID()) + ";");
            }
        });

        StringBuilder sampleNoSb = new StringBuilder();
        reqObject.getSamples().stream().filter(o -> matrixSampleIdSet.contains(o.getSampleId()) && SampleType.check(o.getSampleType(),SampleType.OriginalSample))
                .forEach(o -> sampleNoSb.append(o.getSampleNo() +","));
        if (StringUtils.isNoneEmpty(sampleNoSb.toString())) {
            sampleNoSb.deleteCharAt(sampleNoSb.length() - 1);
            sb.append(String.format(Constants.ORIGINAL_SAMPLE_NO_OPERATION, sampleNoSb) + ";");
        }
        String errMsg = sb.toString();
        if (StringUtils.isNoneEmpty(errMsg)) {
            throw new BizException(errMsg);
        }
    }

    public Integer buildTestLineStatusResult(Integer reportStatus, Integer testLineStatus) {
        if (!ReportStatus.check(reportStatus, ReportStatus.New, ReportStatus.Combined, ReportStatus.Draft)) {
            return BomTestLineStatus.COMPLETED.getCode();
        }
        if (TestLineStatus.check(testLineStatus, TestLineStatus.Typing, TestLineStatus.DR)) {
            return  BomTestLineStatus.NEW.getCode();
        }
        if (TestLineStatus.check(testLineStatus, TestLineStatus.Entered)) {
            return  BomTestLineStatus.TESTING.getCode();
        }
        return BomTestLineStatus.COMPLETED.getCode();
    }

    /**
     * 校验是否含有没有assign的sample
     * 校验情况
     * 1.被Mix样使用到的Sample不检验
     * 2.新增sample
     * 3.sample没有assign
     * 4.unAssign之后sample是否还存在assign的数据
     *
     * @param sampleIdSet
     * @param reqObject
     */
    private void verifySamplesWithoutAssign(Set<String> sampleIdSet, SampleBreakDownReq reqObject, Set<String> unAssignTestLineDbSet) {
        Set<String> matrixSampleIdSet = reqObject.getMatrixs().stream().map(o -> o.getSampleId()).collect(Collectors.toSet());
        List<String> mixSampleIds = new ArrayList<>();
        List<TestSampleReq> othSampleReqList = new ArrayList<>();
        reqObject.getSamples().forEach(o -> {
            if (!SampleType.check(o.getSampleType(), SampleType.OriginalSample) && !o.getNoTest()) {
                othSampleReqList.add(o);
            }
            if (SampleType.check(o.getSampleType(), SampleType.MixSample) && matrixSampleIdSet.contains(o.getSampleId())) {
                mixSampleIds.add(o.getSampleId());
            }
        });
        Map<String, TestSampleInfoPO> testSampleInfoPOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(mixSampleIds)) {
            List<TestSampleInfoPO> testSampleInfoPOList = testSampleMapper.getSampleByMixSampleID(mixSampleIds);
            testSampleInfoPOMap.putAll(testSampleInfoPOList.stream().collect(Collectors.toMap(o -> o.getID(), o -> o)));
        }

        List<TestSampleReq> newSamples = reqObject.getSamples().stream().filter(o -> !sampleIdSet.contains(o.getSampleId())).collect(Collectors.toList());
        List<TestSampleReq> testSampleReqList = newSamples.stream().filter(o ->
                !SampleType.check(o.getSampleType(), SampleType.OriginalSample)
                && !matrixSampleIdSet.contains(o.getSampleId()) && !o.getNoTest()
                && (CollectionUtils.isEmpty(testSampleInfoPOMap) || !testSampleInfoPOMap.containsKey(o.getSampleId()))).collect(Collectors.toList());
        StringBuilder sampleNoSb = new StringBuilder();
        Set<String> msgSet = new HashSet<>();
        testSampleReqList.forEach(o -> {
            sampleNoSb.append(o.getSampleNo() + ",");
            msgSet.add(o.getSampleNo());
        });

        reqObject.getSamples().stream().filter(o -> !SampleType.check(o.getSampleType(), SampleType.OriginalSample)
                && !matrixSampleIdSet.contains(o.getSampleId())
                && !StringUtils.isEmpty(o.getSampleNo())
                && !o.getNoTest()
                && (CollectionUtils.isEmpty(testSampleInfoPOMap) ||!testSampleInfoPOMap.containsKey(o.getSampleId())))
                .forEach(o -> {
                    if (!msgSet.contains(o.getSampleNo())) {
                        sampleNoSb.append(o.getSampleNo() + ",");
                        msgSet.add(o.getSampleNo());
                    }
                });

        Map<String, String> sampleNoMap = othSampleReqList.stream().collect(Collectors.toMap(TestSampleReq::getSampleId, TestSampleReq::getSampleNo));
        unAssignTestLineDbSet.stream().filter(o -> !matrixSampleIdSet.contains(o)
                && (CollectionUtils.isEmpty(testSampleInfoPOMap) ||!testSampleInfoPOMap.containsKey(o)))
                .forEach(o -> {
            String sampleNo = sampleNoMap.get(o);
            if (!msgSet.contains(sampleNo) && !StringUtils.isEmpty(sampleNo)) {
                sampleNoSb.append(sampleNo + ",");
                msgSet.add(sampleNo);
            }
        });

        if (StringUtils.isNoneEmpty(sampleNoSb.toString())) {
            sampleNoSb.deleteCharAt(sampleNoSb.length() - 1);
        }
        String errMsg = sampleNoSb.toString();
        if (StringUtils.isNoneEmpty(errMsg)) {
            throw new BizException(String.format(Constants.SAMPLE_UN_ASSIGN_MSG, errMsg));
        }
    }

    /**
     *
     * @param orderId
     * @param testLines
     */
    public void matrixUpdateCondition(String orderId, List<TestLineInstancePO> testLines){
        List<TestMatrixPO> testMatrixs = testMatrixMapper.getTestMatrixByOrderId(orderId);
        if (testMatrixs == null){
            testMatrixs = Lists.newArrayList();
        }
        Map<String, TestMatrixPO> testMatrixMaps = Maps.newHashMap();
        // Matrix里TestLine对应Assign Sample的数量
        Map<String, Set<String>> matrixTestLineMaps = Maps.newHashMap();
        testMatrixs.forEach(testMatrix->{
            String testLineId = testMatrix.getTestLineInstanceID();
            if (!matrixTestLineMaps.containsKey(testLineId)){
                matrixTestLineMaps.put(testLineId, Sets.newHashSet());
            }
            matrixTestLineMaps.get(testLineId).add(testMatrix.getID());
            testMatrixMaps.put(testMatrix.getID(), testMatrix);
        });
        List<String> delConditionIds = Lists.newArrayList();
        List<TestConditionInfoPO> testConditions = testConditionMapper.getTestConditionListByOrderId(orderId);
        for (TestConditionInfoPO testCondition: testConditions){
            // 如果不存在Matrix，则删除Condition
            if (!testMatrixMaps.containsKey(testCondition.getTestMatrixID())){
                delConditionIds.add(testCondition.getID());
                continue;
            }
            String testLineId = testCondition.getTestLineInstanceID();
            if (matrixTestLineMaps.containsKey(testLineId)){
                matrixTestLineMaps.get(testLineId).remove(testCondition.getTestMatrixID());
            }
        }

        if (!delConditionIds.isEmpty()){
            testConditionMapper.delTestConditionByConditionIds(delConditionIds);
        }

        if (testLines == null || testLines.isEmpty()){
            return;
        }
        List<TestLineInstancePO> updateConditionStatus = Lists.newArrayList();
        for (TestLineInstancePO testLine: testLines){
            testLine.setModifiedDate(DateUtils.getNow());
            ConditionStatus conditionStatus = ConditionStatus.findStatus(testLine.getConditionStatus());
            if (conditionStatus == null || conditionStatus == ConditionStatus.NoCondition){
                // TODO 未找对应的ConditionStatus
                continue;
            }
            // 该Test Line 是否有Assign过Sample，如果没有则判断他原先是否设过Confirmed
            if (!matrixTestLineMaps.containsKey(testLine.getID())){
                if (conditionStatus != ConditionStatus.UnConfirmed){
                    testLine.setConditionStatus(ConditionStatus.UnConfirmed.getStatus());
                    updateConditionStatus.add(testLine);
                }
                continue;
            }
            Set<String> testMatrixIds = matrixTestLineMaps.get(testLine.getID());
            if (testMatrixIds == null || testMatrixIds.isEmpty()){
                testLine.setConditionStatus(ConditionStatus.Confirmed.getStatus());
                updateConditionStatus.add(testLine);
                continue;
            }
            if (conditionStatus == ConditionStatus.Confirmed){
                testLine.setConditionStatus(ConditionStatus.UnConfirmed.getStatus());
                updateConditionStatus.add(testLine);
            }
        }
        if (updateConditionStatus.isEmpty()){
            return;
        }
        testLineMapper.updateBatchConditionStatus(updateConditionStatus);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult checkSampleParameters(SampleBreakDownReq reqObject){
        CustomResult rspReuslt = new CustomResult();
        if (reqObject == null) {
            rspReuslt.setMsg("请求对象不能为空.");
            return rspReuslt;
        }
        if (StringUtils.isBlank(reqObject.getOrderNo())) {
            rspReuslt.setMsg("请求的OrderNo. 不能为空.");
            return rspReuslt;
        }
        List<TestSampleReq> samples = reqObject.getSamples();
        if (samples == null || samples.isEmpty()){
            rspReuslt.setMsg("请求的samples不能为空.");
            return rspReuslt;
        }
        //校验sample是否重复
        rspReuslt = this.checkSampleRepeat(samples);
        if(!rspReuslt.isSuccess()){
            return rspReuslt;
        }
        //检查样品父子关系
        rspReuslt = this.checkSampleTree(samples);
        if (!rspReuslt.isSuccess()){
            return rspReuslt;
        }
        rspReuslt = new CustomResult();
        List<SampleGroupReq> groups = reqObject.getGroups();
        if (groups == null){
            groups = Lists.newArrayList();
        }
        List<TestMatrixReq> matrixs = reqObject.getMatrixs();
        if (matrixs == null){
            matrixs = Lists.newArrayList();
        }
        Map<String, Boolean> sampleIdToTestMap = Maps.newHashMap();
        for (TestSampleReq sample: samples) {
            sample.setChem(false);
            SampleType sampleType = SampleType.findType(sample.getSampleType());
            if (sampleType == null) {
                rspReuslt.setMsg(String.format("当前SampleNo(%s)，SampleType类型无效.", sample.getSampleNo()));
                return rspReuslt;
            }
            if (StringUtils.isBlank(sample.getSampleNo())){
                rspReuslt.setMsg(String.format("当前Sample(%s)，SampleNo不能为空.", sample.getSampleId()));
                return rspReuslt;
            }
            if (StringUtils.isBlank(sample.getSampleDesc())){
                rspReuslt.setMsg(String.format("当前SampleNo(%s)，SampleDesc不能为空.", sample.getSampleNo()));
                return rspReuslt;
            }
            if (!(sampleType == SampleType.OriginalSample || sampleType == SampleType.MixSample) && (sample.getSampleSeq() == null || sample.getSampleSeq().intValue() <= 0)){
                rspReuslt.setMsg(String.format("当前SampleNo(%s)，SampleSeq不能为空或为小于零.", sample.getSampleNo()));
                return rspReuslt;
            }
            if (sample.getNoTest() == null){
                sample.setNoTest(false);
            }
            if (StringUtils.isNotBlank(sample.getExecutionSystemSampleId())){
                sample.setExecutionSystemSampleId(sample.getExecutionSystemSampleId().toLowerCase());
            }
            if (StringUtils.isNotBlank(sample.getSampleId())){
                sample.setSampleId(sample.getSampleId().toLowerCase());
            }
            if (StringUtils.isNotBlank(sample.getSampleParentId())){
                sample.setSampleParentId(sample.getSampleParentId().toLowerCase());
            }
            switch (sampleType){
                case Sample:
                    if (!NumberUtil.isNumber(sample.getSampleNo())){
                        rspReuslt.setMsg(String.format("当前SampleNo(%s)只能是非零的数字.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    sample.setSampleNo(String.format("%s%s", sampleType.getCategoryChem(), sample.getSampleNo()));
                    break;
                case SubSample:
                    if (!NumberUtil.isLetterDigit(sample.getSampleNo())){
                        rspReuslt.setMsg(String.format("当前SampleNo(%s)只能是数字+字母.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    sample.setSampleNo(String.format("%s%s", sampleType.getCategoryChem(), sample.getSampleNo()));
                    break;
                case MixSample:
                    if (StringUtils.isNotBlank(sample.getSampleParentId())){
                        rspReuslt.setMsg(String.format("当前SampleNo(%s)的SampleParentId必须为空.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    break;
                case ShareSample:
                    sample.setSampleNo(String.format("%s%s", sampleType.getCategoryChem(), sample.getSampleNo()));
                    break;
            }
            if (sampleType != SampleType.OriginalSample) {
                sampleIdToTestMap.put(sample.getSampleId(), sample.getNoTest());
                continue;
            }
            String oldSampleId = sample.getSampleId();
            sample.setSampleId(sample.getExecutionSystemSampleId());
            sampleIdToTestMap.put(sample.getSampleId(), sample.getNoTest());

            samples.forEach(sampleItem->{
                if (StringUtils.equalsIgnoreCase(sampleItem.getSampleParentId(), oldSampleId)){
                    sampleItem.setSampleParentId(sample.getSampleId());
                }
            });
            groups.forEach(group->{
                if (StringUtils.equalsIgnoreCase(group.getSampleGroupId(), oldSampleId)){
                    group.setSampleGroupId(sample.getSampleId());
                }
                if (StringUtils.equalsIgnoreCase(group.getSampleId(), oldSampleId)){
                    group.setSampleId(sample.getSampleId());
                }
            });
            matrixs.forEach(matrix->{
                if (StringUtils.equalsIgnoreCase(matrix.getSampleId(), oldSampleId)){
                    matrix.setSampleId(sample.getSampleId());
                }
            });
        }

        for (SampleGroupReq group: groups){
            if (StringUtils.isNotBlank(group.getSampleGroupId())){
                group.setSampleGroupId(group.getSampleGroupId().toLowerCase());
            }
            if (StringUtils.isNotBlank(group.getSampleId())){
                group.setSampleId(group.getSampleId().toLowerCase());
            }
            if (!sampleIdToTestMap.containsKey(group.getSampleGroupId())){
                rspReuslt.setMsg(String.format("Groups集合对象里的SampleGroupId(%s)无效.", group.getSampleGroupId()));
                return rspReuslt;
            }
            if (!sampleIdToTestMap.containsKey(group.getSampleId())){
                rspReuslt.setMsg(String.format("Groups集合对象里的SampleId(%s)无效.", group.getSampleId()));
                return rspReuslt;
            }
        }

        for (TestMatrixReq matrix: matrixs){
            if (StringUtils.isNotBlank(matrix.getPpId())){
                matrix.setPpId(matrix.getPpId().toLowerCase());
            }
            if (StringUtils.isNotBlank(matrix.getSampleId())){
                matrix.setSampleId(matrix.getSampleId().toLowerCase());
            }
            if (StringUtils.isNotBlank(matrix.getTestLineInstanceId())){
                matrix.setTestLineInstanceId(matrix.getTestLineInstanceId().toLowerCase());
            }
            if (sampleIdToTestMap.containsKey(matrix.getSampleId()) && sampleIdToTestMap.get(matrix.getSampleId())){
                rspReuslt.setMsg(String.format("当前Sample(%s)noTest设置为True，无法Matrix.", matrix.getSampleId()));
                return rspReuslt;
            }
        }

        if(! CollectionUtils.isEmpty(reqObject.getSlimMapping())) {
            for (SlimMappingReq slimMappingReq : reqObject.getSlimMapping()) {
                if(StringUtils.isBlank(slimMappingReq.getReportScheme()) && StringUtils.isBlank(slimMappingReq.getTestScheme())){
    //                throw new IllegalArgumentException("sample "+slimMappingReq.getSampleNo()+" testline " + slimMappingReq.getTestLineID() +" has no scheme");
                    rspReuslt.setMsg(String.format("sample(%s) testline(%s)  has no scheme", slimMappingReq.getSampleNo(),slimMappingReq.getTestLineID()));
                    return rspReuslt;
                }

                if(StringUtils.isBlank(slimMappingReq.getSampleNo())){
                    //throw new IllegalArgumentException(" testline " + slimMappingReq.getTestLineID() +" has no sample");
                    rspReuslt.setMsg(String.format("testline(%s) has no sample", slimMappingReq.getTestLineID()));
                    return rspReuslt;
                }

                List<String> schemes = Lists.newArrayList();
                if(StringUtils.isNotBlank(slimMappingReq.getTestScheme())) {
                    String[] testSchemes = doReplaceAndSplit(slimMappingReq.getTestScheme());
                    schemes.addAll(Arrays.asList(testSchemes));
                }
                if(StringUtils.isNotBlank(slimMappingReq.getReportScheme())){
                    String[] reportSchemes = doReplaceAndSplit(slimMappingReq.getReportScheme());
                    schemes.addAll(Arrays.asList(reportSchemes));
                }
                //schemes 必须要有 至少一条
                if(schemes.isEmpty()){
    //                throw new IllegalArgumentException("sample "+slimMappingReq.getSampleNo()+" testline " + slimMappingReq.getTestLineID() +" has no scheme");
                    rspReuslt.setMsg("Test Scheme or Report Scheme has no value");
                }

            }
        }
        rspReuslt.setSuccess(true);
        return rspReuslt;
    }

    /**
     * add by vincent 2020年4月10日11:44:45
     * 校验bom接口回传数据中 sampleNo是否重复
     * @param samples
     * @return
     */
    private CustomResult checkSampleRepeat(List<TestSampleReq> samples){
        CustomResult rspReuslt = new CustomResult();
        rspReuslt.setSuccess(true);
        List<String> allSample = samples.stream().map(sample -> sample.getSampleNo().toUpperCase()).collect(Collectors.toList());
        Set allSampleSet = new HashSet<>();
        allSampleSet.addAll(allSample);
        if(allSample.size()!=allSampleSet.size()){
            rspReuslt.setSuccess(false);
            //找到重复的sample，返回提示信息
            Map<String,Boolean> map = new HashMap<>();
            Set<String> repeatSampleNo = new HashSet<>();
            for (String no : allSample) {
                no = no.trim();
                Boolean isNo = map.get(no);
                if(isNo!=null && isNo){
                    repeatSampleNo.add(no);
                }else{
                    map.put(no,true);
                }
            }
            rspReuslt.setMsg("Sample No[ "+StringUtils.join(repeatSampleNo,",")+" ] can't be repeat");
        }
        return rspReuslt;
    }

    /**
     *
     * @param samples
     * @return
     */
    private CustomResult checkSampleTree(List<TestSampleReq> samples){
        CustomResult rspReuslt = new CustomResult();
        if (samples == null || samples.isEmpty()){
            rspReuslt.setMsg("请求的samples不能为空.");
            return rspReuslt;
        }
        for (TestSampleReq sample: samples) {
            SampleType sampleType = SampleType.findType(sample.getSampleType());
            if (sampleType == null) {
                rspReuslt.setMsg(String.format("当前SampleNo(%s)，SampleType类型无效.", sample.getSampleNo()));
                return rspReuslt;
            }
            switch (sampleType){
                case Sample:
                case SubSample:
                case ShareSample:
                    SampleType parentSampleType = this.findParentSampleType(samples, sample);
                    if (parentSampleType == null || parentSampleType != SampleType.OriginalSample) {
                        rspReuslt.setMsg(String.format("未找到对应的sample(%s) 的父样.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    break;
            }
        }
        rspReuslt.setSuccess(true);
        return rspReuslt;
    }

    /**
     *
     * @param testSamples
     * @param parentSample
     */
    public void recalculateSort(List<TestSampleReq> testSamples, TestSampleReq parentSample){
        if (testSamples == null || testSamples.isEmpty()){
            return;
        }
        if (parentSample == null){
            parentSample = new TestSampleReq();
        }
        String parentSampleId = parentSample.getSampleId();
        List<TestSampleReq> samples = testSamples.stream().filter(sample -> StringUtil.equalsIgnoreCase(sample.getSampleParentId(), parentSampleId)).collect(Collectors.toList());
        if (samples == null || samples.isEmpty()){
            return;
        }
        samples.sort(new TestSampleComparator(true));
        //int sampleSeq = 101000000;
        // 104 - 999000001 - 1000000
        int sampleSeq = 1, mixSampleSeq = 1;
        for (TestSampleReq sample: samples) {
            SampleType sampleType = SampleType.findType(sample.getSampleType());
            switch (sampleType){
                case OriginalSample:
                    sample.setSampleSeq(sampleType.getSampleSeq() + sampleType.getSeed() * sampleSeq++);
                    break;
                case Sample:
                case ShareSample:
                    sample.setSampleSeq(parentSample.getSampleSeq() + sampleType.getSampleSeq() + sampleType.getSeed() * sampleSeq++);
                    break;
                case SubSample:
                    sample.setSampleSeq(parentSample.getSampleSeq() + sampleSeq++);
                    break;
                case MixSample:
                    sample.setSampleSeq(sampleType.getSampleSeq() + mixSampleSeq++);
                    break;
            }
            this.recalculateSort(testSamples, sample);
        }
    }

    /**
     *
     * @param reqObject
     */
    public CustomResult resetMixSampleNo(SampleBreakDownReq reqObject){
        CustomResult rspReuslt = new CustomResult();
        List<SampleGroupReq> groups = reqObject.getGroups();
        if (groups == null || groups.isEmpty()){
            rspReuslt.setSuccess(true);
            return rspReuslt;
        }
        List<TestSampleReq> testSamples = reqObject.getSamples();
        if (testSamples == null || testSamples.isEmpty()){
            rspReuslt.setSuccess(true);
            return rspReuslt;
        }
        SortedMap<Integer, String> sampleNos = Maps.newTreeMap();
        Set<String> sampleNoSet = new HashSet<>();
        for (TestSampleReq testSample: testSamples){
            SampleType sampleType = SampleType.findType(testSample.getSampleType());
            if (sampleType == null || sampleType != SampleType.MixSample){
                continue;
            }
            List<SampleGroupReq> sampleGroups = groups.stream().filter(group -> StringUtils.equalsIgnoreCase(group.getSampleId(), testSample.getSampleId())).collect(Collectors.toList());
            if (sampleGroups == null || sampleGroups.isEmpty()){
                continue;
            }
            sampleNos.clear();
            for (SampleGroupReq group: sampleGroups){
                TestSampleReq groupSample = testSamples.stream().filter(sample -> StringUtils.equalsIgnoreCase(sample.getSampleId(), group.getSampleGroupId())).findFirst().orElse(null);
                if (groupSample == null){
                    continue;
                }
                SampleType mixSampleType = SampleType.findType(groupSample.getSampleType());
                if (mixSampleType == SampleType.MixSample){
                    rspReuslt.setMsg(String.format("当前Sample(%s)已是Mix样，不能再做Mix Sample.", groupSample.getSampleNo()));
                    return rspReuslt;
                }
                if (mixSampleType != SampleType.OriginalSample){
                    testSample.setChem(true);
                }
                sampleNos.put(groupSample.getSampleSeq(), groupSample.getSampleNo());
            }
            if (sampleNos.isEmpty()){
                continue;
            }
            testSample.setSampleNo(StringUtils.join(sampleNos.values(), "+"));

            if (sampleNoSet.contains(testSample.getSampleNo())) {
                rspReuslt.setMsg(String.format("Sample No[%s] can't be repeat", testSample.getSampleNo()));
                return rspReuslt;
            }
            sampleNoSet.add(testSample.getSampleNo());
        }
        rspReuslt.setSuccess(true);
        return rspReuslt;
    }

    /**
     * 检查是否NC
     * 1、是否Assign Sample(即matrix)
     * 2、当前是否有子样
     * 3、是否有Mix、Share
     * @param reqObject
     * @param oldSamples
     * @param oldMatrixSampleIds
     * @param oldSampleGroups
     * @return
     */
    public CustomResult checkSampleNc(SampleBreakDownReq reqObject, List<TestSampleInfoPO> oldSamples, Map<String, Set<String>> oldMatrixSampleIds, List<TestSampleGroupInfoPO> oldSampleGroups){
        CustomResult rspReuslt = new CustomResult();
        List<TestSampleReq> samples = reqObject.getSamples();
        if (samples == null || samples.isEmpty()){
            rspReuslt.setMsg("未找到对应的Samples.");
            return rspReuslt;
        }
        if (oldSamples == null){
            oldSamples = Lists.newArrayList();
        }
        Map<String, Boolean> oldSampleIdNCMap = Maps.newHashMap();
        if(!CollectionUtils.isEmpty(oldSamples)){
            oldSampleIdNCMap = oldSamples.stream().collect(Collectors.toMap(TestSampleInfoPO::getID, TestSampleInfoPO::getApplicable, (k1, k2) -> k1));
        }
        if (oldSampleGroups == null){
            oldSampleGroups = Lists.newArrayList();
        }
        List<SampleGroupReq> groups = reqObject.getGroups();
        if (groups == null){
            groups = Lists.newArrayList();
        }
        List<TestMatrixReq> matrixs = reqObject.getMatrixs();
        if (matrixs == null){
            matrixs = Lists.newArrayList();
        }
        for (TestSampleReq sample: samples){
            String sampleParentId = sample.getSampleParentId();
            //因为目前Bom 不回传NC样品了，所以这里，如果是子样的话，需要校验原样是否已经被NC了
            //既然回来了原样，那就说明在Notes不是NC的
            if ( sample.getNoTest() == null || !sample.getNoTest().booleanValue()){
                continue;
            }
            SampleType sampleType = SampleType.findType(sample.getSampleType());
            if (sampleType == SampleType.OriginalSample){
                rspReuslt.setMsg(String.format("当前原样SampleNo(%s)不能NoTest.", sample.getSampleNo()));
                return rspReuslt;
            }
            //这一段的判断逻辑是，利用目前DB中存在的Sample parentId，来找传递进来的NC的SampleID
            Boolean currentDBHasChildSample = oldSamples.stream().filter(oldSample -> StringUtils.equalsIgnoreCase(oldSample.getSampleParentID(), sample.getSampleId())).count() > 0;
            //这一段的判断逻辑是，传递的参数的Sample 是NC的，但是又有子样
            Boolean currentParamDataHasChildSample = samples.stream().filter(newSample -> StringUtils.equalsIgnoreCase(newSample.getSampleParentId(), sample.getSampleId())).count() > 0;
            // 如果有关联关系，那么传递进来的sample 就是有子样，但是又做了NC，业务上是不允许的
            if (currentDBHasChildSample || currentParamDataHasChildSample ){
                rspReuslt.setMsg(String.format("当前SampleNo(%s)存在子样不能NoTest.", sample.getSampleNo()));
                return rspReuslt;
            }
            // 是否Assign Sample(即matrix)
            if (oldMatrixSampleIds.containsKey(sample.getSampleId()) ||
                matrixs.stream().filter(matrix -> StringUtils.equalsIgnoreCase(matrix.getSampleId(), sample.getSampleId())).count() > 0){
                rspReuslt.setMsg(String.format("当前SampleNo(%s)已Assign Sample不能NoTest.", sample.getSampleNo()));
                return rspReuslt;
            }
            //
            if (oldSampleGroups.stream().filter(sampleGroup -> StringUtils.equalsIgnoreCase(sampleGroup.getSampleGroupID(), sample.getSampleId())).count() > 0 ||
                groups.stream().filter(sampleGroup -> StringUtils.equalsIgnoreCase(sampleGroup.getSampleGroupId(), sample.getSampleId())).count() > 0){
                rspReuslt.setMsg(String.format("当前SampleNo(%s)已Share或Mix不能NoTest.", sample.getSampleNo()));
                return rspReuslt;
            }
        }
        rspReuslt.setSuccess(true);
        return rspReuslt;
    }

    /**
     *
     * @param orderId
     * @param reqObject
     * @param oldPPSampleRelMaps
     * @param newSampleIds
     * @param oldTestMatrixMaps
     * @param testLineMaps
     * @return
     */
    public CustomResult handleTestMatrix(
            String orderId,
            SampleBreakDownReq reqObject,
            Map<String, PPTestLineRelInfo> oldPPSampleRelMaps,
            Set<String> newSampleIds,
            Map<String, TestMatrixPO> oldTestMatrixMaps,
            Map<String, TestLineInstancePO> testLineMaps
    ){
        CustomResult rspReuslt = new CustomResult();
        List<TestMatrixReq> matrixs = reqObject.getMatrixs();
        if (matrixs == null || matrixs.isEmpty()) {
            rspReuslt.setSuccess(true);
            rspReuslt.setData(Lists.newArrayList());
            return rspReuslt;
        }
        Set<String> testMatrixIds = Sets.newHashSet();
        List<TestMatrixPO> testMatrixs = Lists.newArrayList();
        TestMatrixPO testMatrix;
        for (TestMatrixReq matrix : matrixs) {
            String testLineId = matrix.getTestLineInstanceId();
            if (StringUtils.isBlank(matrix.getPpId())  || StringUtils.isBlank(testLineId)) {
                rspReuslt.setMsg("matrix 对象ppId、SampleId、TestLineInstanceId为空.");
                return rspReuslt;
            }
            if(StringUtils.isBlank(matrix.getSampleId())){
                continue;
            }
            matrix.setPpId(matrix.getPpId().toLowerCase());
            matrix.setSampleId(matrix.getSampleId().toLowerCase());
            testLineId = testLineId.toLowerCase();
            matrix.setTestLineInstanceId(testLineId);
            if (!newSampleIds.contains(matrix.getSampleId())) {
                rspReuslt.setMsg(String.format("未找到对的SampleId(%s).", matrix.getSampleId()));
                return rspReuslt;
            }
            if (!testLineMaps.containsKey(testLineId)) {
                rspReuslt.setMsg(String.format("未找到对的TestLineInstanceId(%s).", testLineId));
                return rspReuslt;
            }
            String tlRelKey = String.format("%s_%s", matrix.getPpId(), matrix.getTestLineInstanceId());
            PPTestLineRelInfo ppTestLineRel = oldPPSampleRelMaps.get(tlRelKey);
            if (ppTestLineRel == null) {
                rspReuslt.setMsg(String.format("未找到对的PpId(%s).", matrix.getPpId()));
                return rspReuslt;
            }
            if (!StringUtils.equalsIgnoreCase(ppTestLineRel.getTestLineId(), testLineId)){
                rspReuslt.setMsg(String.format("该PpId(%s)下未找到对应的testLineId(%s).", matrix.getPpId(), testLineId));
                return rspReuslt;
            }
            Map<String, String> ppSampleRelMaps = ppTestLineRel.getPpSampleRelMaps();
            Set<String> newSampleRels = ppTestLineRel.getNewSampleRels();
            String matrixKey = String.format("%s_%s", matrix.getSampleId(), testLineId);
            if (oldTestMatrixMaps.containsKey(matrixKey)){
                oldTestMatrixMaps.remove(matrixKey);
                ppSampleRelMaps = ppTestLineRel.getPpSampleRelMaps();
                if (ppSampleRelMaps.containsKey(matrix.getSampleId())){
                    ppSampleRelMaps.remove(matrix.getSampleId());
                }else{
                    newSampleRels.add(matrix.getSampleId());
                }
                testMatrixIds.add(matrixKey);
                continue;
            }
            if (testMatrixIds.contains(matrixKey)){
                if (ppSampleRelMaps.containsKey(matrix.getSampleId())){
                    ppSampleRelMaps.remove(matrix.getSampleId());
                }else{
                    newSampleRels.add(matrix.getSampleId());
                }
                continue;
            }
            TestLineInstancePO testLine = testLineMaps.get(testLineId);
            TestLineStatus testLineStatus = TestLineStatus.findStatus(testLine.getTestLineStatus());
            if (testLineStatus == null || !(testLineStatus == TestLineStatus.Typing || testLineStatus == TestLineStatus.DR || testLineStatus == TestLineStatus.Entered)) {
                rspReuslt.setMsg(String.format("当前TestLineId(%s)，TestLineStatus(%s)为不能建立Matrix.", testLineId, testLineStatus));
                return rspReuslt;
            }
            testMatrixIds.add(matrixKey);
            newSampleRels.add(matrix.getSampleId());

            testMatrix = new TestMatrixPO();
            testMatrix.setID(UUID.randomUUID().toString());
            testMatrix.setGeneralOrderInstanceID(orderId);
            testMatrix.setTestSampleID(matrix.getSampleId());
            testMatrix.setTestLineInstanceID(testLineId);
            testMatrix.setMatrixGroupId(0);
            // Bom 回传 status = Typing
            testMatrix.setMatrixStatus(MatrixStatus.Typing.getStatus());

            testMatrix.setActiveIndicator(true);

            testMatrix.setCreatedBy("BOM");
            testMatrix.setCreatedDate(DateUtils.getNow());
            testMatrix.setModifiedBy("BOM");
            testMatrix.setModifiedDate(DateUtils.getNow());

            testMatrixs.add(testMatrix);
        }
        rspReuslt.setSuccess(true);
        rspReuslt.setData(testMatrixs);
        return rspReuslt;
    }

    /**
     *
     * @param oldPPSampleRelMaps
     * @param userName
     * @return
     */
    public List<PPSampleRelationshipInfoPO> handlePPSampleRel(Map<String, PPTestLineRelInfo> oldPPSampleRelMaps, List<String> delPPSampleRelIds, String userName){
        List<PPSampleRelationshipInfoPO> ppSampleRels = Lists.newArrayList();
        if (oldPPSampleRelMaps == null || oldPPSampleRelMaps.isEmpty()){
            return ppSampleRels;
        }
        PPSampleRelationshipInfoPO ppSampleRel;
        Iterator<Map.Entry<String, PPTestLineRelInfo>> ppSampleRelMaps = oldPPSampleRelMaps.entrySet().iterator();
        while (ppSampleRelMaps.hasNext()) {
            Map.Entry<String, PPTestLineRelInfo> entry = ppSampleRelMaps.next();
            PPTestLineRelInfo testLineRel = entry.getValue();
            if (testLineRel == null){
                continue;
            }
            Map<String, String> oldPPSampleRels = testLineRel.getPpSampleRelMaps();
            if (oldPPSampleRels != null && oldPPSampleRels.size() > 0){
                delPPSampleRelIds.addAll(oldPPSampleRels.values());
            }
            Set<String> newSampleRels = testLineRel.getNewSampleRels();
            if (newSampleRels == null || newSampleRels.isEmpty()){
                continue;
            }
            for (String sampleId: newSampleRels){
                ppSampleRel = new PPSampleRelationshipInfoPO();
                ppSampleRel.setID(UUID.randomUUID().toString());
                ppSampleRel.setPPTLRelID(testLineRel.getPpTLRelId());
                ppSampleRel.setTestSampleID(sampleId);

                ppSampleRel.setCreatedBy(userName);
                ppSampleRel.setCreatedDate(DateUtils.getNow());
                ppSampleRel.setModifiedBy(userName);
                ppSampleRel.setModifiedDate(DateUtils.getNow());
                ppSampleRels.add(ppSampleRel);
            }
        }
        return ppSampleRels;
    }

    /**
     *
     * @param newTestMatrixs
     * @param reportId
     * @param userName
     * @return
     */
    public List<ReportMatrixRelationShipInfoPO> handleReportMatrixRel(List<TestMatrixPO> newTestMatrixs, String reportId, String userName){
        List<ReportMatrixRelationShipInfoPO> reportMatrixRels = Lists.newArrayList();
        if (newTestMatrixs == null || newTestMatrixs.isEmpty()){
            return reportMatrixRels;
        }
        ReportMatrixRelationShipInfoPO reportMatrixRel;
        for (TestMatrixPO matrix: newTestMatrixs){
            reportMatrixRel = new ReportMatrixRelationShipInfoPO();
            reportMatrixRel.setID(UUID.randomUUID().toString());
            reportMatrixRel.setReportID(reportId);
            reportMatrixRel.setTestMatrixID(matrix.getID());

            reportMatrixRel.setCreatedBy("BOM");
            reportMatrixRel.setCreatedDate(DateUtils.getNow());
            reportMatrixRel.setModifiedBy("BOM");
            reportMatrixRel.setModifiedDate(DateUtils.getNow());
            reportMatrixRels.add(reportMatrixRel);
        }
        return reportMatrixRels;
    }

    /**
     *
     * @param reqObject
     * @param testSampleGroups
     * @param sample
     * @return
     */
    public CustomResult mixSample(
            SampleBreakDownReq reqObject,
            Map<String, String> oldSampleGroupIds,
            List<TestSampleGroupInfoPO> testSampleGroups,
            TestSampleReq sample) {
        CustomResult rspReuslt = new CustomResult();

        List<SampleGroupReq> groups = reqObject.getGroups();
        if (groups == null || groups.isEmpty()) {
            rspReuslt.setMsg(String.format("未找到对应的Mix Sample(%s)信息.", sample.getSampleNo()));
            return rspReuslt;
        }
        List<SampleGroupReq> sampleGroups = groups.stream().filter(group -> StringUtils.equalsIgnoreCase(group.getSampleId(), sample.getSampleId())).collect(Collectors.toList());
        if (sampleGroups == null || sampleGroups.isEmpty()) {
            rspReuslt.setMsg(String.format("未找到对应的Mix Sample(%s).", sample.getSampleNo()));
            return rspReuslt;
        }
        if (sampleGroups.size() <= 1) {
            //rspReuslt.setMsg("Please select two sample at least!");
            rspReuslt.setMsg(String.format("至少选择两个才能Mix Sample(%s).", sample.getSampleNo()));
            return rspReuslt;
        }
        if (sample.getNoTest().booleanValue()){
            rspReuslt.setMsg(String.format("Mix Sample(%s)不能设置为NoTest.", sample.getSampleNo()));
            return rspReuslt;
        }
        rspReuslt.setSuccess(true);

        this.toTestSampleGroupInfo(oldSampleGroupIds, testSampleGroups, sampleGroups, reqObject.getUserName());
        return rspReuslt;
    }

    /**
     *
     * @param reqObject
     * @param oldSampleGroupIds
     * @param testSampleGroups
     * @param oldSampleMaps
     * @param sample
     * @return
     */
    public CustomResult shareSample(
            SampleBreakDownReq reqObject,
            Map<String, String> oldSampleGroupIds,
            List<TestSampleGroupInfoPO> testSampleGroups,
            Map<String, TestSampleInfoPO> oldSampleMaps,
            TestSampleReq sample) {
        CustomResult rspReuslt = new CustomResult();
        List<SampleGroupReq> groups = reqObject.getGroups();
        if (groups == null || groups.isEmpty()) {
            //rspReuslt.setMsg(String.format("未找到对应的Share Sample(%s)信息.", sample.getSampleNo()));
            sample.setSampleType(SampleType.Sample.getSampleType());
            rspReuslt.setSuccess(true);
            return rspReuslt;
        }
        List<SampleGroupReq> sampleGroups = groups.stream().filter(group -> StringUtils.equalsIgnoreCase(group.getSampleId(), sample.getSampleId())).collect(Collectors.toList());
        if (sampleGroups == null || sampleGroups.isEmpty()) {
            //rspReuslt.setMsg(String.format("未找到对应的Share Sample(%s).", sample.getSampleNo()));
            sample.setSampleType(SampleType.Sample.getSampleType());
            rspReuslt.setSuccess(true);
            return rspReuslt;
        }

        for (SampleGroupReq sampleGroup : sampleGroups) {
            if (StringUtils.isNotBlank(sampleGroup.getSampleGroupId())){
                sampleGroup.setSampleGroupId(sampleGroup.getSampleGroupId().toLowerCase());
            }
            if (StringUtils.isNotBlank(sampleGroup.getSampleId())){
                sampleGroup.setSampleId(sampleGroup.getSampleId().toLowerCase());
            }
            if (!oldSampleMaps.containsKey(sampleGroup.getSampleGroupId())) {
                rspReuslt.setMsg(String.format("未找到Share Sample(%s)原样.", sample.getSampleNo()));
                return rspReuslt;
            }
            TestSampleInfoPO testSamplePO = oldSampleMaps.get(sampleGroup.getSampleGroupId());
            if (!SampleType.equals(testSamplePO.getSampleType(), SampleType.OriginalSample)) {
                rspReuslt.setMsg(String.format("Share Sample(%s)只能是原样.", sample.getSampleNo()));
                return rspReuslt;
            }
        }
        List<TestSampleReq> samples = reqObject.getSamples();
        // 如果就一个原样，且是自己的父样，如果不是则抛异常
        if (sampleGroups.size() == 1) {
            String sampleGroupId = sampleGroups.get(0).getSampleGroupId();
            List<TestSampleReq> testSamples = samples.stream().filter(testSample -> StringUtils.equalsIgnoreCase(testSample.getSampleId(), sampleGroupId)).collect(Collectors.toList());
            if (testSamples == null || testSamples.isEmpty()) {
                rspReuslt.setMsg(String.format("未找到对应的Share Sample(%s).", sample.getSampleNo()));
                return rspReuslt;
            }
            if (!StringUtils.equalsIgnoreCase(testSamples.get(0).getExecutionSystemSampleId(), sample.getExecutionSystemSampleId())) {
                rspReuslt.setMsg(String.format("当前的Share Sample(%s)在不同的原样上.", sample.getSampleNo()));
                return rspReuslt;
            }
            sample.setSampleType(SampleType.Sample.getSampleType());
        }
        if (sampleGroups.stream().filter(group -> StringUtils.equalsIgnoreCase(group.getSampleId(), sample.getSampleId()) && StringUtils.equalsIgnoreCase(group.getSampleGroupId(), sample.getSampleParentId())).count() <= 0){
            rspReuslt.setMsg(String.format("未找到对应的Share Sample(%s)原样.", sample.getSampleNo()));
            return rspReuslt;
        }
        rspReuslt.setSuccess(true);
        if (sampleGroups.size() == 1){
            return rspReuslt;
        }
        this.toTestSampleGroupInfo(oldSampleGroupIds, testSampleGroups, sampleGroups, reqObject.getUserName());
        return rspReuslt;
    }

    /**
     *
     * @param testSampleGroups
     * @param sampleGroups
     * @param userName
     */
    private void toTestSampleGroupInfo(
            Map<String, String> oldSampleGroupIds,
            List<TestSampleGroupInfoPO> testSampleGroups,
            List<SampleGroupReq> sampleGroups,
            String userName){
        TestSampleGroupInfoPO testSampleGroup;
        for (SampleGroupReq sampleGroup: sampleGroups){
            String groupKey = String.format("%s_%s", sampleGroup.getSampleId(), sampleGroup.getSampleGroupId());
            if (oldSampleGroupIds.containsKey(groupKey)){
                oldSampleGroupIds.remove(groupKey);
                continue;
            }
            testSampleGroup = new TestSampleGroupInfoPO();
            // TODO New ID
            testSampleGroup.setID(UUID.randomUUID().toString());
            testSampleGroup.setSampleGroupID(sampleGroup.getSampleGroupId());
            testSampleGroup.setSampleID(sampleGroup.getSampleId());

            testSampleGroup.setActiveIndicator(true);
            testSampleGroup.setCreatedBy(userName);
            testSampleGroup.setCreatedDate(DateUtils.getNow());
            testSampleGroup.setModifiedBy(userName);
            testSampleGroup.setModifiedDate(DateUtils.getNow());

            testSampleGroups.add(testSampleGroup);
        }
    }

    /**
     *
     * @param samples
     * @param oldSamples
     * @param childSample
     * @return
     */
    public  TestSampleReq findParentSample(List<TestSampleReq> samples,List<TestSampleInfoPO> oldSamples, TestSampleReq childSample) {
        if (samples == null || samples.isEmpty()) {
            return null;
        }
        if (StringUtils.isBlank(childSample.getSampleParentId())) {
            return childSample;
        }

        TestSampleInfoPO testSampleInfoPO = oldSamples.stream().filter(sample -> (
                SampleType.equals(sample.getSampleType(), childSample.getSampleType()) &&
                        StringUtils.equalsIgnoreCase(sample.getID(), childSample.getSampleParentId()))
        ).findFirst().orElse(null);
        if(testSampleInfoPO!=null){
            TestSampleReq req = new TestSampleReq();
            req.setSampleId(testSampleInfoPO.getID());
            req.setSampleParentId(testSampleInfoPO.getSampleParentID());
            req.setSampleType(testSampleInfoPO.getSampleType());
            req.setNoTest(testSampleInfoPO.getApplicable());
            return this.findParentSample(samples,oldSamples, req);
        }

        TestSampleReq parentSample = samples.stream().filter(sample ->(
                SampleType.equals(sample.getSampleType(), childSample.getSampleType()) &&
                        StringUtils.equalsIgnoreCase(sample.getSampleId(), childSample.getSampleParentId()))
        ).findFirst().orElse(null);
        if (parentSample != null) {
            return this.findParentSample(samples,oldSamples, parentSample);
        }
        return null;
    }
    /**
     * @param samples
     * @param childSample
     */
    public SampleType findParentSampleType(List<TestSampleReq> samples, TestSampleReq childSample) {
        if (samples == null || samples.isEmpty()) {
            return null;
        }
        if (StringUtils.isBlank(childSample.getSampleParentId())) {
            return SampleType.findType(childSample.getSampleType());
        }
        TestSampleReq parentSample = samples.stream().filter(sample ->(
                SampleType.equals(sample.getSampleType(), childSample.getSampleType()) &&
                        StringUtils.equalsIgnoreCase(sample.getSampleId(), childSample.getSampleParentId()))
        ).findFirst().orElse(null);
        if (parentSample != null) {
            return this.findParentSampleType(samples, parentSample);
        }
        return null;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult<List<TestSampleDTO>> getSubContractOriginalSampleList(OrderSubContractReq reqObject){
        CustomResult rspResult = new CustomResult();
        if (reqObject == null){
            rspResult.setMsg("请求的对象不能为空.");
            return rspResult;
        }
        if (StringUtils.isBlank(reqObject.getOrderNo())){
            rspResult.setMsg("请求的订单号不能为空.");
            return rspResult;
        }
        if (StringUtils.isBlank(reqObject.getSubContractNo())){
            rspResult.setMsg("请求SubContractNo不能为空.");
            return rspResult;
        }
        List<TestSampleDTO> originalSampleIds = this.getTestSampleList(reqObject);
        rspResult.setData(originalSampleIds);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public List<TestSampleDTO> getTestSampleList(OrderSubContractReq reqObject){
        if (StringUtils.isBlank(reqObject.getOrderNo()) || StringUtils.isBlank(reqObject.getSubContractNo())) {
            return Lists.newArrayList();
        }

        List<TestSampleDTO> sampleInfos = subContractMapper.getSubContractSampleList(reqObject);
        if (CollectionUtils.isEmpty(sampleInfos)){
            return Lists.newArrayList();
        }

        // 需要转换的时候，直接返回subcontract下的所有Sample信息
        if (reqObject.getConvertOriginalSample() != null && reqObject.getConvertOriginalSample()) {
            return sampleInfos;
        }

        List<TestSampleInfo> testSamples = testSampleMapper.getTestSampleList(reqObject.getOrderNo());
        if (testSamples == null || testSamples.isEmpty()){
            return sampleInfos;
        }
        Set<String> sampleIds = sampleInfos.stream().map(TestSampleDTO::getID).collect(Collectors.toSet());

        Map<String, TestSampleInfo> sampleMaps = Maps.newHashMap();
        for (TestSampleInfo sample: testSamples){
            if (sampleMaps.containsKey(sample.getSampleId())){
                continue;
            }
            sampleMaps.put(sample.getSampleId(), sample);
        }

        Map<String, SampleType> testSampleIds = Maps.newHashMap();
        for (String sampleId: sampleIds){
            this.testSampleBFS(sampleMaps, testSampleIds, sampleId);
        }

        List<TestSampleInfoPO> testSampleIdList = testSampleMapper.getTestSampleIdList(Lists.newArrayList(testSampleIds.keySet()));
        List<TestSampleDTO> testSampleDTOS = BeanUtil.copyToList(testSampleIdList, TestSampleDTO.class);
        if (reqObject.getSampleType() == 1){
            return testSampleDTOS;
        }

        Set<String> originalSampleIds = Sets.newHashSet();
        for (Map.Entry<String, SampleType> entry: testSampleIds.entrySet()){
            if (entry.getValue() != SampleType.OriginalSample){
                continue;
            }
            originalSampleIds.add(entry.getKey());
        }
        return testSampleDTOS.stream().filter(sample -> originalSampleIds.contains(sample.getID())).collect(Collectors.toList());
    }

    /**
     *
     * @param sampleMaps
     * @param testSampleIds
     * @param testSampleId
     */
    private void testSampleBFS(Map<String, TestSampleInfo> sampleMaps, Map<String, SampleType> testSampleIds, String testSampleId){
        if (StringUtils.isBlank(testSampleId)){
            return;
        }
        TestSampleInfo testSample = sampleMaps.get(testSampleId);
        if (testSample == null){
            return;
        }
        SampleType sampleType = SampleType.findType(testSample.getSampleType());
        if (sampleType == null){
            return;
        }
        if (!testSampleIds.containsKey(testSample.getSampleId())){
            testSampleIds.put(testSample.getSampleId(), sampleType);
        }
        if (sampleType == SampleType.OriginalSample){
            return;
        }
        if (sampleType == SampleType.Sample || sampleType == SampleType.SubSample){
            this.testSampleBFS(sampleMaps, testSampleIds, testSample.getSampleParentId());
            return;
        }
        List<String> sampleGroupIds = testSample.getSampleGroupIds();
        if (sampleGroupIds == null || sampleGroupIds.isEmpty()){
            return;
        }
        for (String sampleGroupId: sampleGroupIds){
            /*if (sampleType == SampleType.ShareSample){
                continue;
            }*/
            this.testSampleBFS(sampleMaps, testSampleIds, sampleGroupId);
        }
    }

    /**
     *
     * @param samples
     * @param sampleIDNeeds
     * @param leafSampleId
     */
    private void getRootSampleById(List<TestSampleInfoPO> samples, Set<String> sampleIDNeeds, String leafSampleId) {
        for (TestSampleInfoPO samplePO : samples) {
            if (!StringUtils.equalsIgnoreCase(samplePO.getID(), leafSampleId)) {
                continue;
            }
            sampleIDNeeds.add(samplePO.getID());
            if (StringUtils.isNotEmpty(samplePO.getSampleParentID())) {
                getRootSampleById(samples, sampleIDNeeds, samplePO.getSampleParentID());
            }
        }
    }

    /**
     * 校验slimMapping
     * @param reqObject
     * @return
     */
    public CustomResult validateToSlimSubcontracts(SampleBreakDownReq reqObject) {
        CustomResult customResult = new CustomResult();
        //校验证Sample是否存在
        List<String> allSampleIds = reqObject.getSamples().stream().map(TestSampleReq::getSampleId).distinct()
            .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(allSampleIds)){
            customResult.setSuccess(false);
            customResult.setMsg("传入的samples为空");
            return customResult;
        }

        if(CollectionUtils.isEmpty(reqObject.getSlimMapping())){
            //如果不传，则直接跳出
            customResult.setSuccess(true);
            return customResult;
        }

        List<String> slimSampleIds = reqObject.getSlimMapping().stream()
            .filter(p -> StringUtils.isNotBlank(p.getSampleId())).map(SlimMappingReq::getSampleId)
            .distinct()
            .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(slimSampleIds)){
            customResult.setSuccess(false);
            customResult.setMsg("slim sample不能为空");
            return customResult;
        }

        for (String slimSampleId : slimSampleIds) {
            boolean present = allSampleIds.stream()
                .anyMatch(p -> StringUtils.equalsIgnoreCase(slimSampleId, p));
            if(! present){
                customResult.setSuccess(false);
                customResult.setMsg("sample"+slimSampleId+"不存在");
                return customResult;
            }
        }

        //校验testLineId是否存在
        List<String> testlineInstanceIds = reqObject.getSlimMapping().stream()
            .filter(p -> StringUtils.isNotBlank(p.getTestLineID())).map(SlimMappingReq::getTestLineInstanceId)
            .distinct()
            .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(testlineInstanceIds)){
            customResult.setSuccess(false);
            customResult.setMsg("传入的testline为空");
            return customResult;
        }
        List<TestLineInstancePO> testLineByIds = testLineMapper.getTestLineByIds(testlineInstanceIds);
        if(CollectionUtils.isEmpty(testLineByIds)){
            customResult.setSuccess(false);
            customResult.setMsg("传入的testline不存在!");
            return customResult;
        }
        if(testlineInstanceIds.size() != testLineByIds.size()){
            customResult.setSuccess(false);
            customResult.setMsg("传入的testline不存在!!");
            return customResult;
        }

        customResult.setSuccess(true);
        return customResult;
    }

    /**
     * 转换原始数据
     * @param order
     * @param reqObject
     * @return
     */
    public List<SlimSubcontractPO> convertToSlimSubcontracts(GeneralOrderInstanceInfoPO order,SampleBreakDownReq reqObject) {
        List<SlimSubcontractPO> slimSubcontractList = Lists.newArrayList();

        List<SlimMappingReq> slimMappingReqs = reqObject.getSlimMapping();
        if(CollectionUtils.isEmpty(slimMappingReqs)){
            return slimSubcontractList;
        }
        List<String> testlineInstanceIds = slimMappingReqs.stream()
            .filter(p -> StringUtils.isNotBlank(p.getTestLineID())).map(p -> p.getTestLineInstanceId())
            .distinct()
            .collect(Collectors.toList());

        //如果回传的TL已经分包，则不再接收所有mapping数据

        SubContractTestLineMappingExample subContractTestLineMappingExample = new SubContractTestLineMappingExample();
        subContractTestLineMappingExample.createCriteria().andTestLineInstanceIDIn(testlineInstanceIds);

        List<SubContractTestLineMappingPO> subContractTestLineMappingPOS = subContractTestLineMappingMapper.selectByExample(subContractTestLineMappingExample);

        if(! CollectionUtils.isEmpty(subContractTestLineMappingPOS)){
            return slimSubcontractList;
        }

        List<String> sampleIds = reqObject.getSamples().stream()
            .filter(p -> StringUtils.isNotBlank(p.getSampleId())).map(p->p.getSampleId())
            .distinct().collect(Collectors.toList());

        List<TestSampleInfoPO> testSampleInfoPOS = Lists.newArrayList(); //testSampleMapper.getTestSampleIdList(sampleIds);
        //merge 新增的sample
        //现在不merge了，以传过的为准
        for (String sampleId : sampleIds) {
            Optional<TestSampleReq> first = reqObject.getSamples().stream()
                .filter(p -> StringUtils.equalsIgnoreCase(p.getSampleId(), sampleId))
                .findFirst();
            if(! first.isPresent()) {
                continue;
            }
            TestSampleReq sampleReq = first.get();
            String material = testSampleLangService.getMaterialTextByMaterialInfos(sampleReq.getMaterials());
            if(SampleType.check(sampleReq.getSampleType(),SampleType.MixSample)){
                List<String> sampleGroupIds = reqObject.getGroups().stream()
                        .filter(groupReq->StringUtils.equalsIgnoreCase(groupReq.getSampleId(),sampleId))
                        .map(SampleGroupReq::getSampleGroupId).collect(Collectors.toList()); // 两个组成样sampleId
                List<String> materials = Lists.newArrayList();
                for (String groupId : sampleGroupIds){
                    Optional<TestSampleReq> group = reqObject.getSamples().stream().filter(p->StringUtils.equalsIgnoreCase(p.getSampleId(),groupId)).findFirst(); //组成样sample信息
                    if(!group.isPresent()){
                        continue;
                    }
                    materials.add(testSampleLangService.getMaterialTextByMaterialInfos(group.get().getMaterials()));
                }
                material = StringUtils.join(materials,"+");
            }
            TestSampleInfoPO testSampleInfoPO = new TestSampleInfoPO();
            testSampleInfoPO.setID(first.get().getSampleId());
            testSampleInfoPO.setSampleNo(first.get().getSampleNo());
            testSampleInfoPO.setColor(first.get().getColor());
            testSampleInfoPO.setMaterial(material);
            testSampleInfoPO.setSampleRemark(first.get().getRemark());
            testSampleInfoPO.setComposition(first.get().getComposition());
            testSampleInfoPO.setSampleType(first.get().getSampleType());
            testSampleInfoPO.setDescription(first.get().getSampleDesc());
            testSampleInfoPOS.add(testSampleInfoPO);
        }

        OrderInfoForSlimRsp orderInfoForSlim = orderClient.getOrderInfoForSlim(order.getOrderNo());
        for (SlimMappingReq slimMappingReq : slimMappingReqs) {
            List<String> schemes = Lists.newArrayList();

            if(StringUtils.isNotBlank(slimMappingReq.getTestScheme())) {
                String[] testSchemes = doReplaceAndSplit(slimMappingReq.getTestScheme());
                schemes.addAll(Arrays.asList(testSchemes));
            }
            if(StringUtils.isNotBlank(slimMappingReq.getReportScheme())){
                String[] reportSchemes = doReplaceAndSplit(slimMappingReq.getReportScheme());
                schemes.addAll(Arrays.asList(reportSchemes));
            }
            //schemes 必须要有 至少一条
            if(schemes.isEmpty()){
                //前面已经作校验，这里可以schemes不可能为空
                throw new IllegalArgumentException("sample "+slimMappingReq.getSampleNo()+" testline " + slimMappingReq.getTestLineID() +" has no scheme");
            }

            Optional<TestSampleInfoPO> optionalTestSampleInfoPO = testSampleInfoPOS.stream()
                .filter(p -> StringUtils.equalsIgnoreCase(p.getID(), slimMappingReq.getSampleId()))
                .findFirst();

            if(optionalTestSampleInfoPO.isPresent()){
                for(String scheme : schemes){
                    if(!StringUtils.isBlank(scheme)){
                        slimSubcontractList.add(
                            convertToSlimSubcontract(slimMappingReq,scheme,order,orderInfoForSlim,optionalTestSampleInfoPO.get())
                        );
                    }
                }
            }


        }

        return slimSubcontractList;
    }

    private SlimSubcontractPO convertToSlimSubcontract(SlimMappingReq slimMappingReq,String scheme,GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO,OrderInfoForSlimRsp orderInfoForSlimRsp,TestSampleInfoPO testSampleInfoPO) {
        SlimSubcontractPO slimSubcontractPO = new SlimSubcontractPO();
        slimSubcontractPO.setID(UUID.randomUUID().toString());
        slimSubcontractPO.setOrderNo(generalOrderInstanceInfoPO.getOrderNo());
        slimSubcontractPO.setTestLineID(Integer.valueOf(slimMappingReq.getTestLineID()));
        slimSubcontractPO.setProductCode(slimMappingReq.getProductCode());
        slimSubcontractPO.setSampleNo(testSampleInfoPO.getSampleNo());
        slimSubcontractPO.setScheme(scheme);
        slimSubcontractPO.setCustomerGroupCode(generalOrderInstanceInfoPO.getCustomerGroupCode());
        slimSubcontractPO.setCustomerGroupName(generalOrderInstanceInfoPO.getCustomerGroupName());
        slimSubcontractPO.setResponsibleTeamCode(orderInfoForSlimRsp.getResponsibleTeamCode());
        slimSubcontractPO.setCR(orderInfoForSlimRsp.getcR());
        slimSubcontractPO.setCustomerName(generalOrderInstanceInfoPO.getCustomerName());
        slimSubcontractPO.setExpectedOrderDueDate(DateUtils.format(orderInfoForSlimRsp.getExpectedOrderDueDate()));
        slimSubcontractPO.setCountryOfDestination(orderInfoForSlimRsp.getCountryOfDestination());
        slimSubcontractPO.setCountryOfOrigin(orderInfoForSlimRsp.getCountryOfOrigin());
        slimSubcontractPO.setProductDescription(orderInfoForSlimRsp.getProductDescription());
        slimSubcontractPO.setProcedure(slimMappingReq.getProcedure());
        slimSubcontractPO.setSampleColor(testSampleInfoPO.getColor());
        slimSubcontractPO.setSampleMaterial(testSampleInfoPO.getMaterial());
        slimSubcontractPO.setSampleRemark(testSampleInfoPO.getSampleRemark());
        slimSubcontractPO.setComposition(testSampleInfoPO.getComposition());
        slimSubcontractPO.setSampleType(testSampleInfoPO.getSampleType() == SampleType.MixSample.getSampleType() ? "Composite":"Specimen");
        slimSubcontractPO.setSampleDescription(testSampleInfoPO.getDescription());
//        slimSubcontractPO.setActiveIndicator(1);
        slimSubcontractPO.setSyncStatus(0);
        // TODO Mingyang DateUtils.getNow()
        slimSubcontractPO.setCreatedDate(new Date());
        slimSubcontractPO.setModifiedDate(new Date());
        slimSubcontractPO.setModifiedBy("bom");
        slimSubcontractPO.setCreatedBy("bom");


        return slimSubcontractPO;
    }

    @NotNull
    private String[] doReplaceAndSplit(String str){
        return str.replaceAll("，", ",").replaceAll("；", ",")
                .replaceAll(";", ",").split("\\,");
    }

    /**
     * 分配样品，用于页面的sample信息展示
     */
//    @TestLinePending(filedName = "ppTestLineRelIds",type=TestLinePendingTypeEnums.PP_TL_REL_ID_List)
    @AccessRule(testLinePendingType = TestLinePendingTypeEnums.AssignSamplePpTlId)
    public CustomResult assignSample(AssignSampleReq reqObj) {
        CustomResult<Object> result = new CustomResult<>();
        String orderId = reqObj.getOrderId();
        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(orderId);
        if(order==null){
            result.setSuccess(false);
            return result;
        }

        String orderNo = order.getOrderNo();
        //查询所有sample
        List<AssignSampleInfo> allAssignSampleRspList = testSampleMapper.queryAllSample(orderNo);
        Map<String,AssignSampleInfo> sampleMap = allAssignSampleRspList.stream().collect(Collectors.toMap(TestSampleInfoPO::getID,Function.identity(),(o1,o2)->o1));

        List<String> mixSampleIds = allAssignSampleRspList.stream().filter(s->SampleType.check(s.getSampleType(),SampleType.MixSample)).map(AssignSampleInfo::getID).collect(Collectors.toList());
        Map<String,List<TestSampleGroupInfoPO>> groupMap = Maps.newHashMap();
        if(!CollectionUtils.isEmpty(mixSampleIds)){
            List<TestSampleGroupInfoPO> groupInfoPOS = testSampleGroupMapper.getTestSampleGroupList(mixSampleIds);
            groupMap = groupInfoPOS.stream().collect(Collectors.groupingBy(TestSampleGroupInfoPO::getSampleID));
        }


        Map<String, List<TestSampleGroupInfoPO>> finalGroupMap = groupMap;
        allAssignSampleRspList.forEach(x->{
            if(SampleType.check(x.getSampleType(),SampleType.MixSample)){
                List<TestSampleGroupInfoPO> groupInfoPOS = finalGroupMap.get(x.getID());
                List<AssignSampleInfo> groupSampleInfos = Lists.newArrayList();
                LinkedList<List<TestSampleLangInfoPO>> langs = Lists.newLinkedList();
                groupInfoPOS.forEach(y->{
                    AssignSampleInfo sampleInfo = sampleMap.get(y.getSampleGroupID());
                    if(sampleInfo != null && !CollectionUtils.isEmpty(sampleInfo.getMaterialLangs())){
                        groupSampleInfos.add(sampleInfo);
                    }
                });
                groupSampleInfos.sort(new AssignSampleInfoMixComparator(true));
                groupSampleInfos.forEach(y->{
                    langs.add(y.getMaterialLangs());
                });
                x.setMaterial(testSampleLangService.getMixMaterialText(langs,"\\",Lists.newArrayList(LanguageType.English.getLanguageId(),LanguageType.Chinese.getLanguageId()),","));
            }else {
                List<TestSampleLangInfoPO> langs = x.getMaterialLangs();
                x.setMaterial(testSampleLangService.getMaterialText(langs,"\\",Lists.newArrayList(LanguageType.English.getLanguageId(),LanguageType.Chinese.getLanguageId()),","));
            }
        });
        //进行子样，子子样，共样等展示的拼接
        List<AssignSampleInfo> assignSampleInfos = this.filterSample(reqObj, allAssignSampleRspList);
        //查询当前关联到的所有pptlRel
        List<String> ppTestLineRelIds = reqObj.getPpTestLineRelIds();
        int assignType = reqObj.getAssignType();
        //0是正常assign 1是change里面
        List<PPTestLineRelationshipInfoPO> ppTestLineRelListByIds = Lists.newArrayList();
        if(assignType==1 && !CollectionUtils.isEmpty(ppTestLineRelIds)){
            ppTestLineRelListByIds = ppTestLineRelMapper.getPPTestLineRelListByIds(ppTestLineRelIds);
            ppTestLineRelIds = ppTestLineRelListByIds.stream().map(rel->rel.getID()).collect(Collectors.toList());
        }
        //当前TL 对应的所有assignSample
        List<PPSampleRelInfo> assignedSampleList = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(ppTestLineRelIds)){
            assignedSampleList = ppSampleRelMapper.getAssignedSampleByPPTLRelIds(ppTestLineRelIds);
        }
        //对已经assign的sample 进行checked标记
        List<SampleRsp> sampelResult = getSampelResult(assignSampleInfos, assignedSampleList);

        //获取所有pp信息，用来展示
        List<SamplePPSRsp> samplePPSRspList = getSamplePPSRspList(ppTestLineRelIds);

        //获取所有tips信息
        List<AssignSampleTips> tips = mixSampleRequirementService.getAssignSampleTips(ppTestLineRelIds,StringUtils.defaultString(ProductLineContextHolder.getProductLineCode() , ProductLineType.SL.getProductLineAbbr()),order.getOrderNo());

        AssignSampleRsp rsp = new AssignSampleRsp();
        rsp.setSamples(sampelResult);
        rsp.setPps(samplePPSRspList);
        rsp.setTips(tips);
        result.setSuccess(true);
        result.setData(rsp);
        return result;
    }



    private List<SampleRsp> getSampelResult(List<AssignSampleInfo> assignSampleInfos,
            List<PPSampleRelInfo> assignedSampleList) {
        Map<String, Integer> sampelIdActive = assignedSampleList.stream().collect(Collectors.toMap(PPSampleRelInfo::getTestSampleId, PPSampleRelInfo::getActiveIndicator, (k1, k2) -> k2));
        Map<String, List<PPSampleRelInfo>> sampleIdRelInfoMap = assignedSampleList.stream().collect(Collectors.groupingBy(PPSampleRelInfo::getTestSampleId));
        Set<String> assignedSampleIdSet = sampleIdRelInfoMap.keySet();
        List<SampleRsp> sampelResult = new ArrayList<>();
        for (AssignSampleInfo sa : assignSampleInfos) {
            SampleRsp rsp = new SampleRsp();
            String id = sa.getID();
            rsp.setId(id);
            rsp.setCategory(sa.getCategory());
            rsp.setSampleNo(sa.getSampleNo());
            rsp.setSampleType(sa.getSampleType());
            rsp.setDescription(sa.getDescription());
            rsp.setComposition(sa.getComposition());
            rsp.setMaterial(sa.getMaterial());
            rsp.setColor(sa.getColor());
            rsp.setEndUse(sa.getEndUse());
            rsp.setOtherSampleInfo(sa.getOtherSampleInfo());
            rsp.setSampleRemark(sa.getSampleRemark());
            rsp.setSequenceNo(sa.getSequenceNo());
            rsp.setConvertSampleNo(sa.getConvertSampleNo());
            rsp.setActiveIndicator(sa.getActiveIndicator()?1:0);
            if(assignedSampleIdSet.contains(id)){
                rsp.setActiveIndicator(sampelIdActive.get(id));
                rsp.setChecked(true);
            }
            List<PPSampleRelInfo> ppSampleRels = sampleIdRelInfoMap.get(id);
            if(!CollectionUtils.isEmpty(ppSampleRels)){
                Set<String> assignedPPTLRelIdSet = ppSampleRels.stream().map(rel -> rel.getPpTLRelId()).collect(Collectors.toSet());
                rsp.setPpTlRelIds(Lists.newArrayList(assignedPPTLRelIdSet));
            }
            rsp.setSampleTypeNew(SampleType.getShortMessage(sa.getSampleType()));

            //前端逻辑移到后端
//            if(da.sampleType == 101){
//                da.category = 'O';
//            }else{
//                da.category = 'P';
//            }
            if (StringUtils.isBlank(rsp.getCategory())){
                SampleType sampleType = SampleType.findType(rsp.getSampleType());
                rsp.setCategory(sampleType.getCategoryPhy());
            }

            sampelResult.add(rsp);
        }
        return sampelResult;
    }

    private List<SamplePPSRsp> getSamplePPSRspList(List<String> ppTestLineRelIds) {
        List<SamplePPSRsp> samplePPSRspList = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(ppTestLineRelIds)){
            samplePPSRspList = ppTestLineRelMapper.getPPInfoByPPTLRelIds(ppTestLineRelIds);
        }
        for (SamplePPSRsp samplePPSRsp : samplePPSRspList) {
            String ppName = samplePPSRsp.getPpName();
            samplePPSRsp.setPpName(StringUtils.isBlank(ppName)?"/":ppName);
        }
        return samplePPSRspList;
    }

    private List<AssignSampleInfo> filterSample(AssignSampleReq reqObj, List<AssignSampleInfo> sampleList) {
        String cancelFlag = reqObj.getCancelFlag();
        String category = reqObj.getCategory();
        int assignType = reqObj.getAssignType();
        if(assignType==0){
            if(StringUtils.isNotBlank(category)){
                if("P".equalsIgnoreCase(category)){
                    sampleList=sampleList.stream().filter(sampl -> (null == sampl.getCategory() || "p".equalsIgnoreCase(sampl.getCategory()) || "o".equalsIgnoreCase(sampl.getCategory()))).collect(Collectors.toList());
                }else if("C".equalsIgnoreCase(category)){
                    sampleList=sampleList.stream().filter(sampl -> (null == sampl.getCategory() || "c".equalsIgnoreCase(sampl.getCategory()) || "o".equalsIgnoreCase(sampl.getCategory()))).collect(Collectors.toList());
                }
            }
            if(StringUtils.isNotBlank(cancelFlag) && "Y".equalsIgnoreCase(cancelFlag)){
                sampleList=sampleList.stream().filter(sampl -> sampl.getActiveIndicator()).collect(Collectors.toList());
            }
        }

        List<AssignSampleInfo> sample101List = sampleList.stream().filter(sa -> sa.getSampleType() != null && sa.getSampleType().compareTo(101) == 0).collect(Collectors.toList());
        List<AssignSampleInfo> sample102List = sampleList.stream().filter(sa -> sa.getSampleType() != null && sa.getSampleType().compareTo(102) == 0).collect(Collectors.toList());
        List<AssignSampleInfo> sample103List = sampleList.stream().filter(sa -> sa.getSampleType() != null && sa.getSampleType().compareTo(103) == 0).collect(Collectors.toList());
        List<AssignSampleInfo> sample104List = sampleList.stream().filter(sa -> sa.getSampleType() != null && sa.getSampleType().compareTo(104) == 0).collect(Collectors.toList());
        List<AssignSampleInfo> sample105List = sampleList.stream().filter(sa -> sa.getSampleType() != null && sa.getSampleType().compareTo(105) == 0).collect(Collectors.toList());
        String redNo =  "<font style='text-decoration:line-through;color:red;'>%s</font>";
        for (AssignSampleInfo sample101 : sample101List) {
            String id101 = sample101.getID();
            String sampleNo101 = sample101.getSampleNo();
            Boolean active101 = sample101.getActiveIndicator();
            String coverSampleNo101 = sampleNo101;
            if(!active101){
                coverSampleNo101 = String.format(redNo, coverSampleNo101);
            }
            sample101.setSequenceNo(sampleNo101);
            sample101.setConvertSampleNo(coverSampleNo101);
            //从102之间找子样
            for (AssignSampleInfo sample102 : sample102List) {
                String sampleParentID102 = sample102.getSampleParentID();
                if(!id101.equals(sampleParentID102)){
                    continue;
                }
                String id102 = sample102.getID();
                String sampleNo102 = sample102.getSampleNo();
                Boolean active102 = sample102.getActiveIndicator();
                String coverSampleNo102 = sampleNo101+"-"+sampleNo102;
                if(!active102){
                    coverSampleNo102 = String.format(redNo,coverSampleNo102);
                }
                sample102.setSequenceNo(sampleNo102);
                sample102.setConvertSampleNo(coverSampleNo102);
                //子子样
                for (AssignSampleInfo sample103 : sample103List) {
                    String sampleParentID103 = sample103.getSampleParentID();
                    if(!id102.equals(sampleParentID103)){
                        continue;
                    }
                    Boolean active103 = sample103.getActiveIndicator();
                    String sampleNo103 = sample103.getSampleNo();
                    String coverSampleNo103 = sampleNo101+"-"+sampleNo102+"-"+sampleNo103;
                    if(!active103){
                        coverSampleNo103 = String.format(redNo,coverSampleNo103);
                    }
                    sample103.setSequenceNo(sampleNo103);
                    sample103.setConvertSampleNo(coverSampleNo103);
                    //sample103.setSampleNo(sampleNo103);
                }
                //sample102.setSampleNo(sampleNo102);

            }
            //从105 找组合样
            for (AssignSampleInfo sample105 : sample105List) {
                String sampleParentID105 = sample105.getSampleParentID();
                if(!id101.equals(sampleParentID105)){
                    continue;
                }
                //需要去group 表查询组合样样品
                String id105 = sample105.getID();
                List<TestSampleInfoPO> groupSampleList = testSampleGroupMapper.getSamplesBySampleId(id105);
                if(CollectionUtils.isEmpty(groupSampleList)){
                    continue;
                }
                List<String> noList = groupSampleList.stream().map(g -> g.getSampleNo()).collect(Collectors.toList());
                String join = StringUtils.join(noList, "/");
                Boolean active105 = sample105.getActiveIndicator();
                String sampleNo105 = sample105.getSampleNo();
                String coverSampleNo105 = join+"-"+sampleNo105;
                if(!active105){
                    coverSampleNo105 = String.format(redNo,coverSampleNo105);
                }
                sample105.setConvertSampleNo(coverSampleNo105);
                sample105.setSequenceNo(sampleNo105);
                //子子样
                for (AssignSampleInfo sample103 : sample103List) {
                    String sampleParentID103 = sample103.getSampleParentID();
                    if(!id105.equals(sampleParentID103)){
                        continue;
                    }
                    Boolean active103 = sample103.getActiveIndicator();
                    String sampleNo103 = sample103.getSampleNo();
                    String coverSampleNo103 = sampleNo101+"-"+sampleNo105+"-"+sampleNo103;
                    if(!active103){
                        coverSampleNo103 = String.format(redNo,coverSampleNo103);
                    }
                    sample103.setSequenceNo(sampleNo103);
                    sample103.setConvertSampleNo(coverSampleNo103);
                    //sample103.setSampleNo(sampleNo103);
                }
                //sample105.setSampleNo(sampleNo105);
            }
        }

        for (AssignSampleInfo sample104 : sample104List) {
            String coverSampleNo104 =sample104.getSampleNo();
            Boolean active104 = sample104.getActiveIndicator();
            if(!active104){
                coverSampleNo104 = String.format(redNo,coverSampleNo104);
            }
            sample104.setConvertSampleNo(coverSampleNo104);
        }

        List<AssignSampleInfo> result = Lists.newArrayList();
        result.addAll(sample101List);
        result.addAll(sample102List);
        result.addAll(sample103List);
        result.addAll(sample104List);
        result.addAll(sample105List);
        result.sort((a,b)->a.getSampleSeq().compareTo(b.getSampleSeq()));
        return result;

    }




    public CustomResult checkAssignSample(CheckAssignSampleReq req) {
        CustomResult<Object> result = new CustomResult<>();
        List<String> testLindIds = req.getTestLineIds();
        if(CollectionUtils.isEmpty(testLindIds)){
            result.setSuccess(false);
            result.setMsg("Params miss");
            return result;
        }
        boolean isEmpty = checkAssignSample(testLindIds);

        result.setSuccess(true);
        //老接口，如果null  就返回true
        result.setData(isEmpty);
        return result;
    }

    public boolean checkAssignSample(List<String> testLineInstanceIds) {

        if(testLineInstanceIds==null||testLineInstanceIds.isEmpty()){
            return true;
        }
        AnalyteInfoExample example = new AnalyteInfoExample();
        example.createCriteria().andTestLineInstanceIDIn(testLineInstanceIds);
        List<AnalyteInfoPO> list = analyteInfoMapper.selectByExample(example);
        boolean isEmpty = CollectionUtils.isEmpty(list);
        //TODO 目前还没有anaylte的本地化数据，所以还是校验老的数据 2020年7月15日
        /*
        if(!isEmpty){
            List<Long> baseInfoIdList = list.stream().map(a -> a.getAnalyteBaseId()).collect(Collectors.toList());
            AnalyteBaseInfoExample baseInfoExample = new AnalyteBaseInfoExample();
            baseInfoExample.createCriteria().andIdIn(baseInfoIdList);
            List<AnalyteBaseInfoPO> analyteBaseInfoPOS = analyteBaseInfoMapper.selectByExample(baseInfoExample);
            isEmpty = CollectionUtils.isEmpty(analyteBaseInfoPOS);
        }*/
        //校验是否有conditionGroup
        List<TestConditionGroupInfoPO> conditionGroup = testConditionGroupMapper.getTestConditionGroupListByTestLineIds(testLineInstanceIds);
        isEmpty = isEmpty && CollectionUtils.isEmpty(conditionGroup);
        return isEmpty;
    }


    /**
     *@Description  简述：
     * <br/>对sample增减，影响不同的表数据
     * <br/>新增sample,需要同步增加test_matrix,report_matrix,pp_sample_rel
     * <br/>删除sample,需要同步删除test_matrx,report_matrix,pp_sample_rel,
     * <br/>position,productAttr,limit,limitGroup,condition,conditionGroup,conditionGroupLanguage
     *
     *@Param
     *@Return
     *<AUTHOR>
     *@CreateDate
     *@ModifyDate 2020年7月6日
     *@Modify Vincent.Zhi
     */
    @AccessRule(
        reportStatus = { ReportStatus.Cancelled, ReportStatus.Approved,ReportStatus.Completed,ReportStatus.Replaced },
            disGroupKey = Constants.DISGROUP_KEY.AssignSample_SaveConclusion,
        isVersionIncr = true,
        versionGroupKey = "saveAssignSample"
            // change 和正常的 save  参数不一致
//            , subContractType = SubContractOperationTypeEnums.SaveAssignSample
    )
    @BizLog(bizType= BizLogConstant.TEST_HISTORY,operType="Change Add Matrix")
    public CustomResult saveAssignSample(SaveAssignSampleReq req) {
        logger.info("saveAssignSample request  orderNo={},user={}",req.getOrderNo(), SystemContextHolder.getRegionAccount());

        CustomResult<Boolean> result = new CustomResult<>();
        UserInfo user = UserHelper.getLocalUser();
        if(user==null){
            logger.info("saveAssignSample orderNo:{}-get user fail",req.getOrderNo());
            result.setSuccess(false);
            result.setMsg("Get User fail!");
            return result;
        }
        int assignType = req.getAssignType();
        String orderId = req.getOrderId();
        List<String> sampleIds = req.getSampleIds();
        Map<String, List<String>> samplePPIds = req.getSamplePPIds();
        List<String> ppTestLineRelIds = new ArrayList<>();
        if(assignType==1){
            List<String> finalPpTestLineRelIds = ppTestLineRelIds;
            AtomicReference<Boolean> allSelectPP = new AtomicReference<>(true);
            samplePPIds.forEach((k, v)->{
                if(CollectionUtils.isEmpty(v)){
                    allSelectPP.set(false);
                }
                finalPpTestLineRelIds.addAll(v);
            });
            if(!allSelectPP.get()){
                result.setSuccess(false);
                result.setMsg("Please Select PP for Sample!");
                return result;
            }
            ppTestLineRelIds = finalPpTestLineRelIds;
        }else{
            ppTestLineRelIds = req.getPpTestLineRelIds();
        }
        if(CollectionUtils.isEmpty(ppTestLineRelIds)){
            result.setSuccess(false);
            result.setMsg("请求的TestLine 不能为空.");
            return result;
        }
        //vincent 2021年1月13日 校验是否有TL处于Pending状态
        //TODO vincent save方法由于正常assign 和change传递的参数不统一,且change传递的参数没有标识性，导致无法用AOP处理，这里暂时先在代码中做判断
        boolean hasPending = testLinePendingService.hasPendingTestLineByPPTLRelIds(ppTestLineRelIds);
        if(hasPending){
            result.setSuccess(false);
            result.setMsg(TestLinePendingFlagEnums.getPendingMsg());
            return result;
        }

        GeneralOrderInstanceInfoPO orderInfo = this.orderMapper.getOrderInfoByOrderId(orderId);
        if(orderInfo==null){
            result.setSuccess(false);
            result.setMsg("Order not exist or param error!");
            return result;
        }

        // DIG-9947 check sample 的状态
        CustomResult customResult = this.checkSampleActiveOrNC(sampleIds);
        if (!customResult.isSuccess()) {
            return result.fail(customResult.getMsg());
        }

        String orderNo = orderInfo.getOrderNo();
        //1.校验TL,已经TL是否cancel
        List<PPTestLineRelationshipInfoPO> currentOptionsPPTLRelList = this.ppTestLineRelMapper.getByIds(ppTestLineRelIds);
        if(CollectionUtils.isEmpty(currentOptionsPPTLRelList)){
            result.setSuccess(false);
            result.setMsg("请求的TestLine 不能为空.");
            return result;
        }
        Set<String> optionsTLIDSet = currentOptionsPPTLRelList.stream().map(rel -> rel.getTestLineInstanceID()).collect(Collectors.toSet());
        List<TestLineInstancePO> allTestLineList = testLineRepository.getTestLineByOrderId(orderId);

        if(CollectionUtils.isEmpty(allTestLineList)){
            result.setSuccess(false);
            result.setMsg("未找到该订单对应的TestLine.");
            return result;
        }
        //找到tl
        List<TestLineInstancePO> dbFindOptionsTestLine = allTestLineList.stream().filter(tl -> optionsTLIDSet.contains(tl.getID())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(dbFindOptionsTestLine) || dbFindOptionsTestLine.size()!=optionsTLIDSet.size()){
            result.setSuccess(false);
            result.setMsg("未找到TestLine");
            return result;
        }

        // DIG-7785 校验绑定希音的sample
        CustomResult sheinResult = this.checkSheinAssignSample(req.getOrderNo(), sampleIds, assignType, optionsTLIDSet, dbFindOptionsTestLine, orderInfo.getCustomerGroupCode());
        if (!sheinResult.isSuccess()) {
            return result.fail(sheinResult.getMsg());
        }

        List<TestLineInstancePO> hasCancelTL = dbFindOptionsTestLine.stream().filter(tl -> TestLineStatus.check(tl.getTestLineStatus(), TestLineStatus.Cancelled)).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(hasCancelTL)){
            result.setSuccess(false);
            result.setMsg("TestLine状态已Cancelled，请刷新页面.");
            return result;
        }
        List<TestLineInstancePO> hasNCTL = dbFindOptionsTestLine.stream().filter(tl -> TestLineStatus.check(tl.getTestLineStatus(), TestLineStatus.NC)).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(hasNCTL)){
            result.setSuccess(false);
            result.setMsg("TestLine is NC,can't assign Sample");
            return result;
        }

        //准备数据 all ppTlRel
        List<PPTestLineRelationshipInfoPO> allPPTLRelList = this.ppTestLineRelMapper.getPPTestLineRelListByOrderId(orderId);
        Map<String, PPTestLineRelationshipInfoPO> pptlRelIdRelPOMap = allPPTLRelList.stream()
                .collect(Collectors.toMap(PPTestLineRelationshipInfoPO::getID, Function.identity(), (k1, k2) -> k1));
        Map<String, List<PPTestLineRelationshipInfoPO>> tlIdPPTLRelListMap = allPPTLRelList.stream()
                .collect(Collectors.groupingBy(PPTestLineRelationshipInfoPO::getTestLineInstanceID));

        //all trims pp tl rel
        Set<Long> trimsPPTLRelSet = allPPTLRelList.stream().map(rel -> rel.getPpArtifactRelId()).collect(Collectors.toSet());
        List<PPArtifactRelInfoWithBLOBs> allTrimsPPTLRelList =  this.trimsPPTestLineRelMapper.queryByIds(Lists.newArrayList(trimsPPTLRelSet));
        Map<Long, PPArtifactRelInfoWithBLOBs> trimsPPTLRelIdMap = allTrimsPPTLRelList.stream().collect(Collectors.toMap(PPArtifactRelInfoWithBLOBs::getId, Function.identity()));
        //all matrix
        List<TestMatrixPO> allTestMatrixList = this.testMatrixMapper.getTestMatrixListByOrderId(orderId);
        //all condition
        List<TestConditionInfoPO> allConditionList = this.testConditionMapper.getTestConditionListByOrderId(orderId);
        //all limit
        List<LimitInstancePO> allLimitList = this.limitMapper.getLimitListByOrderId(orderId);
        //all limit group
        List<LimitGroupInstancePO> allLimitGroupList = this.limitGroupMapper.getLimitGroupListByOrderNo(orderNo);
        //active report
        ReportInfoPO reportInfoPO = this.reportMapper.getReportByOrderNo(orderNo);
        //查询当前订单的所有ppsamplerel
        List<PPSampleRelationshipInfoPO> allDBPPSampleRelList = this.ppSampleRelMapper.getPPSampleRelListByOrderId(orderId);
        Map<String, List<PPSampleRelationshipInfoPO>> dbSamplePPRelMap = allDBPPSampleRelList.stream().collect(Collectors.groupingBy(PPSampleRelationshipInfoPO::getTestSampleID));

        //2.处理pp sample rel关系 分为change的操作（只能增加sample或修改sample对应的pp） 和 正常的操作
        List<PPSampleRelInfo> oldPPSampleRelList = this.ppSampleRelMapper.getAssignedSampleByPPTLRelIds(ppTestLineRelIds);
        Map<String, PPSampleRelInfo> ppSampleRelIdPOMap = oldPPSampleRelList.stream().collect(Collectors.toMap(PPSampleRelInfo::getPpSampleRelId, Function.identity(), (k1, k2) -> k2));
        Map<String, List<PPSampleRelInfo>> oldSampleIdPPSampleRelList = oldPPSampleRelList.stream().collect(Collectors.groupingBy(rel -> rel.getTestSampleId()));
        //处理sampleId ppId关联关系，用来处理limitGroup
        Map<String, PPTestLineRelationshipInfoPO> pptlRelIdMap = allPPTLRelList.stream().collect(Collectors.toMap(PPTestLineRelationshipInfoPO::getID, Function.identity()));
        Map<String,List<Long>> sampleIdPPIDListMap = Maps.newHashMap();
        oldSampleIdPPSampleRelList.forEach((sId,relList)->{
            List<Long> ppIdList = sampleIdPPIDListMap.get(sId);
            if(CollectionUtils.isEmpty(ppIdList)){
                ppIdList = new ArrayList<>();
            }
            Set<Long> ppIds = relList.stream().map(rel -> {
                String ppTLRelId = rel.getPpTLRelId();
                PPTestLineRelationshipInfoPO po = pptlRelIdMap.get(ppTLRelId);
                return trimsPPTLRelIdMap.containsKey(po.getPpArtifactRelId()) ? po.getPpBaseId() : null;
            }).collect(Collectors.toSet());
            ppIdList.addAll(ppIds);
            //sample 与 pp的关系 此时的pp是ppBaseID
            sampleIdPPIDListMap.put(sId,ppIdList);
        });

        //修改的要找到 需要增加sampleRel还是删除sampelRel
        List<PPSampleRelationshipInfoPO> waitAddSampleRelList = new ArrayList<>();
        List<String> waitDeleteSampleRelList = new ArrayList<>();

        List<TestMatrixPO> waitDeleteMatrixList = new ArrayList<>();
        List<TestMatrixPO> waitAddMatrixList = new ArrayList<>();
        List<TestLineInstancePO> waitAddMatrixTestLineList = new ArrayList<>();
        List<ReportMatrixRelationShipInfoPO> waitAddReportMatrixRelList = Lists.newArrayList();
        if(assignType!=1){
            // 因为同一个tl 可以有多个pp 需要去重处理
            for (String pagePPTestLineRelId : ppTestLineRelIds) {
                //找到增删的sample
                List<String> pageDeleteSampleIdList = new ArrayList<>();
                List<String> pageAddSampleList = new ArrayList<>();
                List<String> pageUpdateSampleList = new ArrayList<>();
                //DB中当前ppTL已经assign的sample
                Set<String> oldAssignSample = oldPPSampleRelList.stream()
                        .filter(oldpsrel->oldpsrel.getPpTLRelId().equals(pagePPTestLineRelId))
                        .map(oldpsrel -> oldpsrel.getTestSampleId()).collect(Collectors.toSet());
                //db没有，页面进来的全部新增
                if(CollectionUtils.isEmpty(oldAssignSample)){
                    pageAddSampleList.addAll(sampleIds);
                }else{
                    //找出db比页面多的就是删除
                    pageDeleteSampleIdList.addAll(oldAssignSample);
                    pageDeleteSampleIdList.removeAll(sampleIds);
                    //找出页面比db多的就是新增
                    pageAddSampleList.addAll(sampleIds);
                    pageAddSampleList.removeAll(oldAssignSample);
                    //去掉新增，就是需要修改的
                    pageUpdateSampleList.addAll(sampleIds);
                    pageUpdateSampleList.removeAll(pageAddSampleList);
                }
                //非change 的assign 保存 是有取消勾选的逻辑的，而且pptlrel有且只有一个,samplePPIds是不会有值的
                //所有sample 需要分配给所有pptlRel
                for (String pageAddSampleId : pageAddSampleList) {
                    PPSampleRelationshipInfoPO po = this.createPPSampleRelPO(pageAddSampleId, pagePPTestLineRelId, user.getRegionAccount());
                    waitAddSampleRelList.add(po);
                }

                for (String pageDeleteSampleId : pageDeleteSampleIdList) {
                    List<PPSampleRelInfo> deleteInfo = oldPPSampleRelList.stream()
                            .filter(psrel->psrel.getTestSampleId().equals(pageDeleteSampleId))
                            .filter(psrel->psrel.getPpTLRelId().equals(pagePPTestLineRelId))
                            .collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(deleteInfo)){
                        List<String> deleteIds = deleteInfo.stream()
                                .filter(psrel->psrel.getPpTLRelId().equals(pagePPTestLineRelId))
                                .map(info -> info.getPpSampleRelId())
                                .collect(Collectors.toList());
                        waitDeleteSampleRelList.addAll(deleteIds);
                    }
                }

                //处理matrix testline-sample 先处理删除的sample
                String testLineInstanceID = pptlRelIdRelPOMap.get(pagePPTestLineRelId).getTestLineInstanceID();
                TestLineInstancePO testLineInstancePO= testLineRepository.getBaseTestLineById(testLineInstanceID);
                //当前tl对应的所有pp
                List<PPTestLineRelationshipInfoPO> currentTLAllPPTLList = tlIdPPTLRelListMap.get(testLineInstanceID);
                for (String pageDeleteSampleId : pageDeleteSampleIdList) {
                    //1根据sampleId 找ppSample关系，除了本身之外还有没有其它 同Tl的pp在使用当前sample，如果没有 就可以删除matrix，否则需要保留
                    //List<PPSampleRelInfo> currentSampleAllPPSampleRelList = oldSampleIdPPSampleRelList.get(pageDeleteSampleId);
                    List<PPSampleRelationshipInfoPO> currentSampleAllPPSampleRelList = dbSamplePPRelMap.get(pageDeleteSampleId);
                    //3 页面传入的pptlrel中，是否有待处理或者已经处理的pptlrelid，因为动作统一，所以如果存在，那么当前matrix是可以删除的
                    //不再有同tl 的其它pp 占用sample 可以删除matrix
                    List<String> currentSampleAssignedPP = currentSampleAllPPSampleRelList.stream().map(ppSampleRelInfo -> ppSampleRelInfo.getPPTLRelID()).collect(Collectors.toList());
                    currentSampleAssignedPP.removeAll(ppTestLineRelIds);
                    //剩余的 pptl 是否和当前tl 相同 如果不同 可以删除matrix 相同的话 就需要保留当前sample 对应的amtrix
                    boolean hasOtherTlAssignSample = false;
                    for (String pptlId : currentSampleAssignedPP) {
                        PPTestLineRelationshipInfoPO ppTestLineRelationshipInfoPO = pptlRelIdRelPOMap.get(pptlId);
                        if(ppTestLineRelationshipInfoPO!=null){
                            String testLineInstanceID1 = ppTestLineRelationshipInfoPO.getTestLineInstanceID();
                            if(testLineInstanceID.equals(testLineInstanceID1)){
                                hasOtherTlAssignSample = true;
                            }
                        }
                    }
                    if(!hasOtherTlAssignSample){
                        List<TestMatrixPO> shouldDeleteMatrix = allTestMatrixList.stream()
                                .filter(m -> m.getTestLineInstanceID().equals(testLineInstanceID)
                                        && m.getTestSampleID().equals(pageDeleteSampleId))
                                .collect(Collectors.toList());
                        waitDeleteMatrixList.addAll(shouldDeleteMatrix);
                    }
                }
                for (String pageAddSampleId : pageAddSampleList) {
                    //源matrix中不存在就可以添加，但是需后续waitAddAMtrixList需要去重，否则matrix会重复
                    long count = allTestMatrixList.stream()
                            .filter(m -> m.getTestLineInstanceID().equals(testLineInstanceID))
                            .filter(m -> m.getTestSampleID().equals(pageAddSampleId)).count();
                    if(count==0){
                        TestMatrixPO po = this.createTestMatrixPO(pageAddSampleId, testLineInstanceID, orderId, user.getRegionAccount(), testLineInstancePO == null ? TestLineStatus.Typing.getStatus() : testLineInstancePO.getTestLineStatus());
                        waitAddMatrixList.add(po);
                    }
                }
                /*if(!dealTlSet.contains(testLineInstanceID)){
                    dealTlSet.add(testLineInstanceID);

                }*/
            }
        }else if(assignType==1){//change的处理
            //找到增删的sample
            List<String> pageDeleteSampleIdList = new ArrayList<>();
            List<String> pageAddSampleList = new ArrayList<>();
            List<String> pageUpdateSampleList = new ArrayList<>();
            //DB中当前ppTL已经assign的sample
            Set<String> oldAssignSample = oldPPSampleRelList.stream()
                    .map(oldpsrel -> oldpsrel.getTestSampleId()).collect(Collectors.toSet());
            //db没有，页面进来的全部新增
            if(CollectionUtils.isEmpty(oldAssignSample)){
                pageAddSampleList.addAll(sampleIds);
            }else{
                //找出db比页面多的就是删除
                pageDeleteSampleIdList.addAll(oldAssignSample);
                pageDeleteSampleIdList.removeAll(sampleIds);
                //找出页面比db多的就是新增
                pageAddSampleList.addAll(sampleIds);
                pageAddSampleList.removeAll(oldAssignSample);
                //去掉新增，就是需要修改的
                pageUpdateSampleList.addAll(sampleIds);
                pageUpdateSampleList.removeAll(pageAddSampleList);
            }

            //change 的操作，只会同时存在一个TL 应当sample为切入点进行操作
            for (String addSampleId : pageAddSampleList) {
                List<String> pageSelectPPTLRelList = samplePPIds.get(addSampleId);
                for (String pptlRelId : pageSelectPPTLRelList) {
                    PPSampleRelationshipInfoPO po = this.createPPSampleRelPO(addSampleId, pptlRelId, user.getRegionAccount());
                    waitAddSampleRelList.add(po);
                }
                String testLineInstanceID = dbFindOptionsTestLine.get(0).getID();
                TestLineInstancePO testLineInstancePO= testLineRepository.getBaseTestLineById(testLineInstanceID);
                TestMatrixPO po = this.createTestMatrixPO(addSampleId, testLineInstanceID, orderId, user.getRegionAccount(), testLineInstancePO == null ? TestLineStatus.Typing.getStatus() : testLineInstancePO.getTestLineStatus());
                waitAddMatrixTestLineList.add(testLineInstancePO);
                waitAddMatrixList.add(po);
            }
            for (String updateSampleId : pageUpdateSampleList) {
                List<String> pagePPTLRelList = samplePPIds.get(updateSampleId);
                List<PPSampleRelInfo> oldPPSampleRel = oldSampleIdPPSampleRelList.get(updateSampleId);
                if(CollectionUtils.isEmpty(oldPPSampleRel)){
                    for (String pptlRelId : pagePPTLRelList) {
                        PPSampleRelationshipInfoPO po = this.createPPSampleRelPO(updateSampleId, pptlRelId, user.getRegionAccount());
                        waitAddSampleRelList.add(po);
                    }
                }else{
                    Map<String, List<PPSampleRelInfo>> oldPpTlRelIdRelList = oldPPSampleRel.stream().collect(Collectors.groupingBy(rel -> rel.getPpTLRelId()));
                    //db 删除页面，如果有多余数据 就是需要删除的
                    Set<String> oldPptlRelSet = Sets.newHashSet(oldPpTlRelIdRelList.keySet());
                    oldPptlRelSet.removeAll(pagePPTLRelList);
                    for (String shouldDeletePPTLRelId : oldPptlRelSet) {
                        List<PPSampleRelInfo> shouldDeleteList = oldPpTlRelIdRelList.get(shouldDeletePPTLRelId);
                        List<String> deleteIds = shouldDeleteList.stream().map(rel -> rel.getPpSampleRelId()).collect(Collectors.toList());
                        waitDeleteSampleRelList.addAll(deleteIds);
                    }
                    //页面删除DB ，多出来的数据是需要新增的
                    oldPptlRelSet = oldPpTlRelIdRelList.keySet();
                    pagePPTLRelList.removeAll(oldPptlRelSet);
                    for (String shouldAddPPTLRelId : pagePPTLRelList) {
                        PPSampleRelationshipInfoPO po = this.createPPSampleRelPO(updateSampleId, shouldAddPPTLRelId, user.getRegionAccount());
                        waitAddSampleRelList.add(po);
                    }
                }
            }
        }


        //处理waitAddMatrixList 去重
        Map<String, List<TestMatrixPO>> groupAddMatrix = waitAddMatrixList.stream().collect(Collectors.groupingBy(m -> m.getTestLineInstanceID() + "_" + m.getTestSampleID()));
        waitAddMatrixList.clear();
        groupAddMatrix.forEach((k,v)->{
            waitAddMatrixList.add(v.get(0));
        });

        //处理waitAddMatrixTestLineList 去重
        Map<String, List<TestLineInstancePO>> groupAddMatrixTestLine = waitAddMatrixTestLineList.stream().collect(Collectors.groupingBy(m -> m.getID()));
        waitAddMatrixTestLineList.clear();
        groupAddMatrixTestLine.forEach((k,v)->{
            waitAddMatrixTestLineList.add(v.get(0));
        });

        //根据需要新增的matrix，来新增reportMatrix
        if(ReportStatus.check(reportInfoPO.getReportStatus(),ReportStatus.New,ReportStatus.Approved,ReportStatus.Draft,ReportStatus.Combined)){
            for (TestMatrixPO testMatrixPO : waitAddMatrixList) {
                ReportMatrixRelationShipInfoPO po = new ReportMatrixRelationShipInfoPO();
                po.setID(UUID.randomUUID().toString());
                po.setReportID(reportInfoPO.getID());
                po.setTestMatrixID(testMatrixPO.getID());
                po.setModifiedBy(user.getRegionAccount());
                po.setModifiedDate(DateUtils.getNow());
                po.setCreatedBy(user.getRegionAccount());
                po.setCreatedDate(DateUtils.getNow());
                waitAddReportMatrixRelList.add(po);
            }
        }

        //根据删除的sampelRel 删除对应limitGroup limit 等数据
        List<LimitGroupInstancePO> waitDeleteLimitGroupList = Lists.newArrayList();
        List<String> waitDeleteProductAttributeByLimitGroupIdsList = Lists.newArrayList();
        List<LimitInstancePO> waitDeleteLimitByParamList = Lists.newArrayList();
        List<String> waitDeleteConclusionByPPSampleRelIdList = Lists.newArrayList();
        for (String deleteSampleRelId : waitDeleteSampleRelList) {
            List<Long> ppBaseIdList = sampleIdPPIDListMap.get(deleteSampleRelId);
            List<LimitGroupInstancePO> shouldDeleteLimitGroup = allLimitGroupList.stream()
                    .filter(lg -> deleteSampleRelId.equals(lg.getTestSampleID()) && ppBaseIdList.contains(lg.getPpBaseId()))
                    .collect(Collectors.toList());
            List<String> shouldDeleteLimitGroupIdsList = shouldDeleteLimitGroup.stream().map(l -> l.getID()).collect(Collectors.toList());
            waitDeleteLimitGroupList.addAll(shouldDeleteLimitGroup);
            waitDeleteProductAttributeByLimitGroupIdsList.addAll(shouldDeleteLimitGroupIdsList);
            //删除limit 还有一个 需要用参数去删除，三个参数是sampleId ，tlId,ppBaseId
            PPSampleRelInfo deletePPSampleRelInfo = ppSampleRelIdPOMap.get(deleteSampleRelId);
            String ppTLRelId = deletePPSampleRelInfo.getPpTLRelId();
            //拼装删除数据
            PPTestLineRelationshipInfoPO ppTestLineRelationshipInfoPO = pptlRelIdMap.get(ppTLRelId);
            String testLineInstanceID = ppTestLineRelationshipInfoPO.getTestLineInstanceID();
            Long ppArtifactRelId = ppTestLineRelationshipInfoPO.getPpArtifactRelId();
            Long ppBaseId = trimsPPTLRelIdMap.containsKey(ppArtifactRelId) ? ppTestLineRelationshipInfoPO.getPpBaseId(): null;
            LimitInstancePO deleteLimitInstancePO = new LimitInstancePO();
            deleteLimitInstancePO.setTestSampleID(deleteSampleRelId);
            deleteLimitInstancePO.setTestLineInstanceID(testLineInstanceID);
            deleteLimitInstancePO.setPpBaseId(ppBaseId);
            waitDeleteLimitByParamList.add(deleteLimitInstancePO);
            //删除conclusion的list
            waitDeleteConclusionByPPSampleRelIdList.add(deletePPSampleRelInfo.getPpSampleRelId());
        }

        //从所有存在matrix中的tl 找到
        List<String> waitDeletePositionByMatrixList = Lists.newArrayList();
        List<String> waitDeleteProductAttributeByMatrixList = Lists.newArrayList();
        List<String> waitDeleteConclusionByMatrixIdList = Lists.newArrayList();
        List<String> waitDeleteReportMatrixRelByMatrixIdList = Lists.newArrayList();
        List<String> waitDeleteConditionByIdsList = Lists.newArrayList();
        List<String> waitDeleteConditionGroupByIdsList = Lists.newArrayList();
        List<String> waitDeleteLimitByIdsList = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(waitDeleteMatrixList)){
            //根据matrix的删除，同步删除position
            Set<String> deleteMatrixId = waitDeleteMatrixList.stream().map(m -> m.getID()).collect(Collectors.toSet());
            //根据要删除的matrix，删除对应reportmatrx
            waitDeleteReportMatrixRelByMatrixIdList.addAll(deleteMatrixId);
            waitDeletePositionByMatrixList.addAll(deleteMatrixId);
            //limit 处理
            List<String> shouldDeleteLimitIdList = allLimitList.stream().filter(l -> deleteMatrixId.contains(l.getTestMatrixID())).map(l -> l.getID()).collect(Collectors.toList());
            //同步删除limit /limit lanugae
            waitDeleteLimitByIdsList.addAll(shouldDeleteLimitIdList);
            //根据matrix的删除，同步删除tb_product_attribute_instance
            waitDeleteProductAttributeByMatrixList.addAll(deleteMatrixId);
            //有删除matrix，同步删除conclusion
            waitDeleteConclusionByMatrixIdList.addAll(deleteMatrixId);

            //根据tl+sampleId找到需要删除的condition
            Set<String> tlSampleIdSet = waitDeleteMatrixList.stream().map(po -> po.getTestLineInstanceID() + "_" + po.getTestSampleID()).collect(Collectors.toSet());
            List<TestConditionInfoPO> shouldDeleteCondition = allConditionList.stream().filter(c -> tlSampleIdSet.contains(c.getTestLineInstanceID() + "_" + c.getTestSampleID())).collect(Collectors.toList());
            List<String> shouldDeleteConditionIds = shouldDeleteCondition.stream().map(c -> c.getID()).collect(Collectors.toList());
            waitDeleteConditionByIdsList.addAll(shouldDeleteConditionIds);
            //同步删除对应的conditongroup/language
            List<String> shouldDeleteConditionGroup = waitDeleteMatrixList.stream()
                    .filter(m -> StringUtils.isNotBlank(m.getTestConditionGroupID()))
                    .map(m -> m.getTestConditionGroupID())
                    .collect(Collectors.toList());
            List<String> shouldDeleteMatrixIds = waitDeleteMatrixList.stream()
                    .filter(m -> StringUtils.isNotBlank(m.getTestConditionGroupID()))
                    .map(m -> m.getID())
                    .collect(Collectors.toList());
            //从正常db中，查找是否还有关联关系，如果有，就不删除，因为有主外键关系
            List<TestMatrixPO> stillExistsGroup = allTestMatrixList.stream()
                    .filter(m -> shouldDeleteConditionGroup.contains(m.getTestConditionGroupID()) && !shouldDeleteMatrixIds.contains(m.getID()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(stillExistsGroup)){
                waitDeleteConditionGroupByIdsList.addAll(shouldDeleteConditionGroup);
            }
        }
        //根据db存留的matrix，找到需要更新tlconditionStatus的TL
        List<TestLineInstancePO> waitUpdateTestLineInsatnce = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(waitAddMatrixList) || !CollectionUtils.isEmpty(waitDeleteMatrixList)){
            List<String> addMatrixTlIdList = waitAddMatrixList.stream().map(m -> m.getTestLineInstanceID()).collect(Collectors.toList());
            List<String> delMatrixTlIdList = waitDeleteMatrixList.stream().map(m -> m.getTestLineInstanceID()).collect(Collectors.toList());

            List<TestLineInstancePO> shouldUpdateConditionStatusTlList = allTestLineList.stream()
                    .filter(tl -> addMatrixTlIdList.contains(tl.getID()) || delMatrixTlIdList.contains(tl.getID()))
                    .filter(tl->tl.getConditionStatus()!=null && tl.getConditionStatus().compareTo(1001)!=0)
                    .collect(Collectors.toList());
            List<TestLineInstancePO> shouldUpdateTLs = shouldUpdateConditionStatusTlList.stream().map(tl -> {
                TestLineInstancePO po = new TestLineInstancePO();
                po.setID(tl.getID());
                po.setConditionStatus(ConditionStatus.UnConfirmed.getStatus());
                po.setModifiedBy(user.getRegionAccount());
                po.setModifiedDate(new Date());
                return po;
            }).collect(Collectors.toList());
            waitUpdateTestLineInsatnce.addAll(shouldUpdateTLs);
        }

        // 处理删除的Matrix 对应reRmmark
        testLineRemarkService.dealTestLineRemarkForDelMatrix(waitDeleteMatrixList, user.getRegionAccount());


        Integer execute = transactionTemplate.execute(trans -> {
            int i = 0;
            //删除reportMatrix
            if(!CollectionUtils.isEmpty(waitDeleteReportMatrixRelByMatrixIdList)){
                i = this.reportMatrixRelMapper.deleteByMatrixIDList(waitDeleteReportMatrixRelByMatrixIdList);
                this.rollBack(i,trans);
            }
            //删除condition
            if(!CollectionUtils.isEmpty(waitDeleteConditionByIdsList)){
                i = this.testConditionMapper.deleteByIds(waitDeleteConditionByIdsList);
                this.rollBack(i,trans);
            }

            //删除conditionGroupMultipleLanguage
            if(!CollectionUtils.isEmpty(waitDeleteConditionGroupByIdsList)){
                i = this.testConditionGroupLanguageMapper.deleteByConditionGroupIds(waitDeleteConditionGroupByIdsList);
                this.rollBack(i,trans);
            }
            //删除position
            if(!CollectionUtils.isEmpty(waitDeletePositionByMatrixList)){
                i = this.testPositionMapper.deleteBytestMatrixIDs(waitDeletePositionByMatrixList);
                this.rollBack(i,trans);
            }
            //删除productAttr
            if(!CollectionUtils.isEmpty(waitDeleteProductAttributeByMatrixList)){
                i = this.productAttrMapper.deleteByTestMatrixIDs(waitDeleteProductAttributeByMatrixList);
                this.rollBack(i,trans);
            }
            //删除productAttr
            if(!CollectionUtils.isEmpty(waitDeleteProductAttributeByLimitGroupIdsList)){
                i = this.productAttrMapper.deleteByLimitGroupIds(waitDeleteProductAttributeByLimitGroupIdsList);
                this.rollBack(i,trans);
            }
            //删除limit
            if(!CollectionUtils.isEmpty(waitDeleteLimitByParamList)){
                i = this.limitMapper.deleteLimitAndTestDataFailed(waitDeleteLimitByParamList);
                this.rollBack(i,trans);
            }
            //通过matrixid 删除limit limitLanguage
            if(!CollectionUtils.isEmpty(waitDeleteLimitByIdsList)){
                i = this.limitMapper.batchDelete(waitDeleteLimitByIdsList);
                this.rollBack(i,trans);
            }
            //删除limitGroup
            if(!CollectionUtils.isEmpty(waitDeleteLimitGroupList)){
                List<String> list = waitDeleteLimitGroupList.stream().map(m -> m.getID()).collect(Collectors.toList());
                i = this.limitGroupMapper.batchDelete(list);
                this.rollBack(i,trans);
            }
            //删除conclusion
            if(!CollectionUtils.isEmpty(waitDeleteConclusionByPPSampleRelIdList)){
                i = this.conclusionInfoExtMapper.deleteConclusionByPPSampleRelIDs(waitDeleteConclusionByPPSampleRelIdList);
                this.rollBack(i,trans);
            }
            //删除conclusion
            if(!CollectionUtils.isEmpty(waitDeleteConclusionByMatrixIdList)){
                i = this.conclusionInfoExtMapper.batchDelete(waitDeleteConclusionByMatrixIdList);
                this.rollBack(i,trans);
            }
            //删除ppSampleRel
            if(!CollectionUtils.isEmpty(waitDeleteSampleRelList)){
                i = this.ppSampleRelMapper.deleteByIds(waitDeleteSampleRelList);
                this.rollBack(i,trans);
            }
            //删除matrix
            if(!CollectionUtils.isEmpty(waitDeleteMatrixList)){
                List<String> list = waitDeleteMatrixList.stream().map(m -> m.getID()).collect(Collectors.toList());
                i = this.testMatrixMapper.batchDeleteMatrix(list);
                this.rollBack(i,trans);
            }

            //删除conditionGroup
            if(!CollectionUtils.isEmpty(waitDeleteConditionGroupByIdsList)){
                i = this.testConditionGroupMapper.deleteByIds(waitDeleteConditionGroupByIdsList);
                this.rollBack(i,trans);
            }


            //添加ppSampleRel
            if(!CollectionUtils.isEmpty(waitAddSampleRelList)){
                i = this.ppSampleRelMapper.batchInsert(waitAddSampleRelList);
                this.rollBack(i,trans);
            }
            //添加matrix
            if(!CollectionUtils.isEmpty(waitAddMatrixList)){
                i = this.testMatrixMapper.batchInsert(waitAddMatrixList);
                this.rollBack(i,trans);
                // change 操作时，添加 matrix 会修改TL 状态
                if (assignType==1) {
                    // waitAddMatrixTestLineList 只有一个值
                    waitAddMatrixTestLineList.forEach( tl -> {
                        if(!TestLineStatus.check(tl.getTestLineStatus(),TestLineStatus.Typing, TestLineStatus.DR)) {
                            testLineStatusService.updateMatrixStatus(TestLineModuleType.ChangeAddMatrix, tl.getID(), waitAddMatrixList.stream().map(TestMatrixPO::getID).collect(Collectors.toSet()));
                            BizLogHelper.setValue(orderInfo.getOrderNo(), tl.getTestLineID());
                        }
                    });
                }
            }
            //添加reportMatrix
            if(!CollectionUtils.isEmpty(waitAddReportMatrixRelList)){
                i = this.reportMatrixRelMapper.batchInsert(waitAddReportMatrixRelList);
                this.rollBack(i,trans);
            }

            //更新testLine
            if(!CollectionUtils.isEmpty(waitUpdateTestLineInsatnce)){
                i = this.testLineMapper.updateBatchConditionStatus(waitUpdateTestLineInsatnce);
                this.rollBack(i,trans);
            }

            //DIG-7875
            if(reportInfoPO.getRecalculationFlag()!=null && reportInfoPO.getRecalculationFlag().compareTo(1)==0){
                reportInfoPO.setRecalculationFlag(2);
                reportInfoPO.setModifiedBy(user.getRegionAccount());
                reportInfoPO.setModifiedDate(DateUtils.getNow());
                i = reportMapper.updateReportRecalculationFlag(reportInfoPO);
                this.rollBack(i,trans);
            }

            return i;
        });

        result.setSuccess(true);
        result.setData(true);
        return result;
    }

    private void rollBack(int i, TransactionStatus trans){
        if(i<0){
            trans.setRollbackOnly();
        }
    }

    /**
     *
     * @param sampleIds
     * @return
     */
    private CustomResult checkSampleActiveOrNC(List<String> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return CustomResult.newSuccessInstance();
        }
        List<TestSampleInfoPO> testSampleIdList = testSampleMapper.getTestSampleIdList(sampleIds);
        if (CollectionUtils.isEmpty(testSampleIdList)) {
            return CustomResult.failure("Sample not exist or param error!");
        }
        long count1 = testSampleIdList.stream().filter(item -> item.getApplicable() || !item.getActiveIndicator()).count();
        if (count1 > 0) {
            return CustomResult.failure("Sample has InActive or NC!");
        }
        return CustomResult.newSuccessInstance();
    }

    /**
     * DIG-7785 校验绑定希音的sample
     * @param orderNo
     * @param sampleIds
     * @param assignType
     * @param optionsTLIDSet
     * @param testLineInfos
     * @param customerGroupCode
     * @return
     */
    private CustomResult checkSheinAssignSample(String orderNo, List<String> sampleIds, int assignType, Set<String> optionsTLIDSet, List<TestLineInstancePO> testLineInfos, String customerGroupCode) {
        CustomResult result = new CustomResult(false);
        //
        // 获取 订单类型 tb_order_trf_relationship.RefObjectType
        OrderInfoDto preOrderInfo = orderClient.getOrderInfoByOrderNo(orderNo);
        if (preOrderInfo == null) {
            return result.fail("获取订单信息失败");
        }
        List<OrderTrfRelInfo> orderTrfRelInfos = orderClient.getOrderTrfRelationship(preOrderInfo.getID(), ProductLineContextHolder.getProductLineCode());
        if (CollectionUtils.isEmpty(orderTrfRelInfos)) {
            return result.success();
        }
        List<OrderTrfRelInfo> originalSampleRelInfos = orderTrfRelInfos.stream()
                .filter(item -> RefSystemIdEnum.check(item.getRefSystemId(), RefSystemIdEnum.Shein, RefSystemIdEnum.SheinSupplier)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(originalSampleRelInfos) || CollectionUtils.isEmpty(sampleIds)) {
            return result.success();
        }

        List<CheckTestLineMappingRsp> checkTestLineMappingRsps = commonService.checkTestLineMappingExists(testLineInfos, customerGroupCode, StringUtils.defaultString(ProductLineContextHolder.getProductLineCode() , ProductLineType.SL.getProductLineAbbr()),
                orderTrfRelInfos.get(0).getRefSystemId());
        Set<Integer> noOriginalsampleLine = checkTestLineMappingRsps.stream()
                .filter(item -> Objects.isNull(item.getAllowToAssign()) || item.getAllowToAssign().isEmpty()
                        || !item.getAllowToAssign().get("originalSample")).map(CheckTestLineMappingRsp::getTestLineId).collect(Collectors.toSet());

        if(!CollectionUtils.isEmpty(noOriginalsampleLine)){
            List<TestSampleInfoPO> testSampleIdList = testSampleMapper.getTestSampleIdList(sampleIds);
            List<TestSampleInfoPO> originalSamples = testSampleIdList.stream()
                    .filter(item -> SampleType.check(item.getSampleType(), SampleType.OriginalSample) && item.getActiveIndicator()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(originalSamples)) {
                if (assignType != 1) {
                    return result.fail("当前订单对应的客户不接收原样的测试结果");
                }
                // 获取原样的testLine下的matrix
                Set<String> originalSampleIds = originalSamples.stream().map(TestSampleInfoPO::getID).collect(Collectors.toSet());
                List<TestMatrixPO> matrixListBySampleIds = testMatrixMapper.getMatrixInfoBySampleIds(optionsTLIDSet, originalSampleIds);
                if (CollectionUtils.isEmpty(matrixListBySampleIds)) {
                    return result.fail("当前订单对应的客户不接收原样的测试结果");
                }
                Set<TestMatrixPO> collect = matrixListBySampleIds.stream().filter(item -> item.getActiveIndicator()).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(collect)) {
                    return result.fail("当前订单对应的客户不接收原样的测试结果");
                }
            }
        }

        List<OrderTrfRelInfo> sheinInfo = originalSampleRelInfos.stream().filter(item -> RefSystemIdEnum.check(item.getRefSystemId(), RefSystemIdEnum.Shein, RefSystemIdEnum.SheinSupplier)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sheinInfo)) {
            return result.success();
        }

        CheckMixAssignSampleReq checkMixAssignSampleReq = new CheckMixAssignSampleReq();
        checkMixAssignSampleReq.setSampleIds(sampleIds);
        List<String> inActivateMatrixSampleByTestLineId = testMatrixMapper.getInActivateMatrixSampleByTestLineId(optionsTLIDSet);
        if (assignType == 1 && !CollectionUtils.isEmpty(inActivateMatrixSampleByTestLineId)) {
            checkMixAssignSampleReq.setCancelSamples(inActivateMatrixSampleByTestLineId);
        }
        checkMixAssignSampleReq.setNeedCheckTls(testLineInfos.stream()
                .filter(item -> !TestLineType.check(item.getTestLineType(), TestLineType.Pretreatment))
                .map(TestLineInstancePO::getTestLineID).collect(Collectors.toSet()));

        //List<CheckTestLineMappingRsp> checkTestLineMappingRsps = commonService.checkTestLineMappingExists(testLineInfos, customerGroupCode, StringUtils.defaultString(ProductLineContextHolder.getProductLineCode() , ProductLineType.SL.getProductLineAbbr()));

        String productCategory = StringUtils.defaultString(preOrderInfo.getPostfix(), StringUtils.EMPTY).toLowerCase();
        Set<Integer> noSplitTestLine = checkTestLineMappingRsps.stream()
                .filter(item -> item.getSplitMixSetting() != null
                        && SpliteMixConclusionEnum.check(
                        item.getSplitMixSetting().getOrDefault(productCategory, item.getSplitMixSetting().get("default")),
                        SpliteMixConclusionEnum.NOT_SPLIT)).map(CheckTestLineMappingRsp::getTestLineId).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(noSplitTestLine)) {
            checkMixAssignSampleReq.setMixAndChildTLs(noSplitTestLine);
            Set<Integer> testLineIdSets = testLineInfos.stream().map(TestLineInstancePO::getTestLineID).collect(Collectors.toSet());
            if (noSplitTestLine.size() != testLineIdSets.size()) {
                return result.fail(String.format("testLine(%s)可以添加重复测点的，请单独做AssignSample操作！", StringUtils.join(noSplitTestLine, ",")));
            }
        }

        checkMixAssignSampleReq.setCheckTypeTLs(checkTestLineMappingRsps.stream().filter(item-> StringUtils.isNotBlank(item.getItemCode()) && CheckTypeEnum.check(item.getCheckType(),CheckTypeEnum.NHR)).map(CheckTestLineMappingRsp::getTestLineId).collect(Collectors.toSet()));
        CustomResult<Boolean> checkResult = this.checkMixAssignSample(checkMixAssignSampleReq);
        if (!checkResult.isSuccess() || !checkResult.getData()) {
            return result.fail(checkResult.getMsg());
        }

        result.setSuccess(true);
        return result;
    }

    /**
     * 检查 TestLine 是否是
     * @param
     * @return
     */
    /*public Set<Integer> checkSheinCheckType(List<CheckTestLineMappingRsp> checkTestLineMappingRsps,CheckTestLineMappingEnum mappingEnum) {
        if (CollectionUtils.isEmpty(checkTestLineMappingRsps)) {
            return Sets.newHashSet();
        }
        return checkTestLineMappingRsps.stream()
                .filter(item -> item.getCheckTestLine() != -1 && CheckTestLineMappingEnum.check(item.getCheckTestLine(), mappingEnum))
                .map(CheckTestLineMappingRsp::getTestLineId)
                .collect(Collectors.toSet());
    }*/

    private TestMatrixPO createTestMatrixPO(String sampleId,String tlId,String orderId, String userName, Integer testLineStatus){
        TestMatrixPO po = new TestMatrixPO();
        po.setID(UUID.randomUUID().toString());
        po.setModifiedDate(DateUtils.getNow());
        po.setModifiedBy(userName);
        po.setCreatedDate(DateUtils.getNow());
        po.setCreatedBy(userName);
        po.setActiveIndicator(true);
        po.setMatrixStatus(testLineStatus);
        po.setMatrixGroupId(0);
        po.setTestSampleID(sampleId);
        po.setTestLineInstanceID(tlId);
        po.setGeneralOrderInstanceID(orderId);
        return po;
    }

    private PPSampleRelationshipInfoPO createPPSampleRelPO(String sampleId,String pptlId,String userName){
        PPSampleRelationshipInfoPO po = new PPSampleRelationshipInfoPO();
        po.setID(UUID.randomUUID().toString());
        po.setPPTLRelID(pptlId);
        po.setTestSampleID(sampleId);
        po.setCreatedBy(userName);
        po.setModifiedBy(userName);
        po.setCreatedDate(DateUtils.getNow());
        po.setModifiedDate(DateUtils.getNow());
        return po;
    }

    @AccessRule(subContractType = SubContractOperationTypeEnums.CancelAssignSample)
    public CustomResult assignSampleCancel(AssignSampleCancelReq req) {
        UserInfo user = UserHelper.getLocalUser();
        CustomResult<Boolean> result = new CustomResult<>(false);
        String testSampleId = req.getTestSampleId();
        String testLineInstanceId = req.getTestLineInstanceId();
        int matrixGroupId = req.getMatrixGroupId();
        if(StringUtils.isBlank(testSampleId) || StringUtils.isBlank(testLineInstanceId)){
            result.setSuccess(false);
            return result.fail("Params Error");
        }

        GeneralOrderInstanceInfoPO orderInstanceInfoPO = orderMapper.getOrderByTestLineInstanceId(testLineInstanceId);
        String orderNo = orderInstanceInfoPO.getOrderNo();

        TestMatrixInfoExample example = new TestMatrixInfoExample();
        example.createCriteria().andTestSampleIDEqualTo(testSampleId)
                .andTestLineInstanceIDEqualTo(testLineInstanceId);
                //.andMatrixGroupIdEqualTo(matrixGroupId);
        List<TestMatrixInfoPO> testMatrixInfoPOS = testMatrixInfoMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(testMatrixInfoPOS)){
            return result.fail("Query Data Error");
        }

        TestLineInstancePO testLine = testLineMapper.getTestLineById(testLineInstanceId);
        if(TestLineStatus.check(testLine.getTestLineStatus(),TestLineStatus.Cancelled)){
            return result.fail("This TestLine already cancelled.");
        }

        TestMatrixInfoPO testMatrixInfoPO = testMatrixInfoPOS.get(0);
        Boolean activeIndicator = testMatrixInfoPO.getActiveIndicator();

        TestDataInfoPO updatePO = new TestDataInfoPO();
        updatePO.setTestSampleID(testSampleId);
        updatePO.setTestLineInstanceID(testLineInstanceId);
        updatePO.setModifiedBy(user.getRegionAccount());
        updatePO.setModifiedDate(new Date());
        //查询时是true的话，那么更新后就是false，因此testData要增加NoNeedTest
        if(activeIndicator){
            updatePO.setTestValueRemark("NoNeedTest");
            TestMatrixInfoExample matrixInfoExample = new TestMatrixInfoExample();
            matrixInfoExample.createCriteria().andTestLineInstanceIDEqualTo(testLineInstanceId).andActiveIndicatorEqualTo(true).andTestSampleIDNotEqualTo(testSampleId);
            int count = testMatrixInfoMapper.countByExample(matrixInfoExample);
            if(count <= 0){
                return result.fail("Fail to cancel! It's only one sample of the TL. Please Assign other sample to TL or Cancel TL first!");
            }
        }

        ReportInfoPO reportInfo = reportMapper.getReportByOrderNo(orderNo);

        Set<String> testMatrixIds = testMatrixInfoPOS.stream().map(TestMatrixInfoPO::getID).collect(Collectors.toSet());

        // DIG-7782 将 Mix样置为有效  需要校验  希音的场景 （mix样 不能同组成mix的样品同步 有效）
        if (!activeIndicator) {
            // DIG-7785
            CustomResult customResult = this.checkSheinCancelMatrix(orderNo, orderInstanceInfoPO.getCustomerGroupCode(), testLine, testSampleId, testMatrixInfoPO.getID());
            if (!customResult.isSuccess()) {
                return result.fail(customResult.getMsg());
            }
        }

        Integer execute = transactionTemplate.execute(trans -> {
            int i = 0;

            testLineStatusService.updateMatrixStatus(TestLineModuleType.ChangeCancelMatrix, testLineInstanceId, testMatrixIds);

            //更新testData表数据为NoNeedTest DIG-2206

            i = testDataMapper.updateTestDataNoNeedTest(updatePO);
            if (i < 0) {
                trans.setRollbackOnly();
            }

            TestMatrixPO matrixPO = new TestMatrixPO();
            matrixPO.setTestLineInstanceID(testLineInstanceId);
            matrixPO.setTestSampleID(testSampleId);
            matrixPO.setActiveIndicator(!activeIndicator);
            matrixPO.setModifiedBy(user.getRegionAccount());
            matrixPO.setModifiedDate(DateUtils.getNow());
            int j = testMatrixMapper.updateBySampleIdAndTestLineId(matrixPO);
            //按照老逻辑，需要继续更新别的，如果i小于等于0，不用触发回滚
            if (j > 0 && reportInfo.getRecalculationFlag() != null && reportInfo.getRecalculationFlag().compareTo(1) == 0) {
                reportInfo.setRecalculationFlag(2);
                reportInfo.setModifiedBy(user.getRegionAccount());
                reportInfo.setModifiedDate(DateUtils.getNow());
                reportMapper.updateRecalculationFlagByReportId(reportInfo);
            }
            return i;
        });

        //TODO 需要插入Log
        result.setSuccess(execute > -1);
        result.setData(true);
        return result;
    }


    /**
     *
     * @param orderNo
     * @param customerGroupCode
     * @param testLineInstancePO
     * @param testSampleId
     * @param testMatrixId
     * @return
     */
    private CustomResult checkSheinCancelMatrix(String orderNo, String customerGroupCode, TestLineInstancePO testLineInstancePO, String testSampleId, String testMatrixId) {
        CustomResult rspResult = new CustomResult(false);

        // 获取 订单类型 tb_order_trf_relationship.RefObjectType
        OrderInfoDto preOrderInfo = orderClient.getOrderInfoByOrderNo(orderNo);
        if (preOrderInfo == null) {
            return rspResult.fail("获取订单信息失败");
        }
        List<OrderTrfRelInfo> orderTrfRelInfos = orderClient.getOrderTrfRelationship(preOrderInfo.getID(), ProductLineContextHolder.getProductLineCode());
        if (CollectionUtils.isEmpty(orderTrfRelInfos)) {
            return rspResult.success();
        }

        // DIG-8787 支持希音供应商 的重复测点校验
//        List<OrderTrfRelInfo> originalSampleRelInfos = orderTrfRelInfos.stream().filter(item -> RefObjectTypeEnum.check(item.getRefObjectType(), RefObjectTypeEnum.OriginalSample)).collect(Collectors.toList());
        List<OrderTrfRelInfo> sheinInfo = orderTrfRelInfos.stream().filter(item -> RefSystemIdEnum.check(item.getRefSystemId(), RefSystemIdEnum.Shein,RefSystemIdEnum.SheinSupplier)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sheinInfo)) {
            return rspResult.success();
        }
        // POSL-4660 Pretreatment 不校验
        if (TestLineType.check(testLineInstancePO.getTestLineType(), TestLineType.Pretreatment)) {
            return rspResult.success();
        }

        List<CheckTestLineMappingRsp> checkTestLineMappingRsps = commonService.checkTestLineMappingExists(Lists.newArrayList(testLineInstancePO), customerGroupCode,
                StringUtils.defaultString(ProductLineContextHolder.getProductLineCode() , ProductLineType.SL.getProductLineAbbr()),orderTrfRelInfos.get(0).getRefSystemId());

        // 此testLine为 耐互染色
        List<CheckTestLineMappingRsp> checkType = checkTestLineMappingRsps.stream()
                .filter(item -> StringUtils.isNotBlank(item.getItemCode()) && CheckTypeEnum.check(item.getCheckType(),CheckTypeEnum.NHR)).collect(Collectors.toList());
        Set<Integer> checkTypeTLs = checkTestLineMappingRsps.stream().filter(item-> StringUtils.isNotBlank(item.getItemCode()) && CheckTypeEnum.check(item.getCheckType(),CheckTypeEnum.NHR)).map(CheckTestLineMappingRsp::getTestLineId).collect(Collectors.toSet());

        String productCategory = StringUtils.defaultString(preOrderInfo.getPostfix(), StringUtils.EMPTY).toLowerCase();
        Set<Integer> noSplitTestLine = checkTestLineMappingRsps.stream()
                .filter(item -> item.getSplitMixSetting() != null
                        && SpliteMixConclusionEnum.check(
                        item.getSplitMixSetting().getOrDefault(productCategory, item.getSplitMixSetting().get("default")),
                        SpliteMixConclusionEnum.NOT_SPLIT)).map(CheckTestLineMappingRsp::getTestLineId).collect(Collectors.toSet());

        // 当前testLIne下有效的sample
        List<TestMatrixWithSampleInfo> testSampleInfoPOS = testMatrixMapper.getTestMatrixWithSampleList(testLineInstancePO.getID());

        List<String> allSampleIds = testSampleInfoPOS.stream()
                .filter(item -> !SampleType.check(item.getSampleType(), SampleType.MixSample) && item.getActiveIndicator())
                .map(TestMatrixWithSampleInfo::getTestSampleID).collect(Collectors.toList());
        List<String> mixSampleIds = testSampleInfoPOS.stream()
                .filter(item -> SampleType.check(item.getSampleType(), SampleType.MixSample) && item.getActiveIndicator())
                .map(TestMatrixWithSampleInfo::getTestSampleID).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mixSampleIds)) {
            mixSampleIds = Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(allSampleIds)) {
            allSampleIds = Lists.newArrayList();
        }
        TestSampleInfoPO sampleInfoById = testSampleMapper.getSampleInfoById(testSampleId);
        if (SampleType.check(sampleInfoById.getSampleType(), SampleType.OriginalSample)) {
            return rspResult.fail("当前订单对应的客户不接收原样的测试结果");
        }

        if (SampleType.check(sampleInfoById.getSampleType(), SampleType.MixSample)) {
            mixSampleIds.add(testSampleId);
        } else {
            allSampleIds.add(testSampleId);
        }

        // DIG-7862
        if (!CollectionUtils.isEmpty(checkTypeTLs) && !CollectionUtils.isEmpty(allSampleIds)) {
            rspResult.setMsg(String.format("耐互染色TestLine{%s}只允许Assign Sample类型为Mix的样品", StringUtils.join(checkTypeTLs, ",")));
            rspResult.setData(true);
            return rspResult;
        }

        if (!CollectionUtils.isEmpty(mixSampleIds)) {
            List<TestSampleGroupInfoPO> testSampleGroupList = testSampleGroupMapper.getTestSampleGroupList(mixSampleIds);
            if (CollectionUtils.isEmpty(noSplitTestLine) && CollectionUtils.isEmpty(checkType) && testSampleGroupList.stream().map(TestSampleGroupInfoPO::getSampleGroupID).collect(Collectors.toSet()).size() < testSampleGroupList.size()) {
                return rspResult.fail("校验失败！TestLine上不能出现重复的测点");
            }

            CustomResult customResult = this.checkMainMaterial(checkTypeTLs, mixSampleIds, testSampleGroupList);
            if (!customResult.isSuccess()) {
                rspResult.setSuccess(false);
                rspResult.setData(false);
                rspResult.setMsg(customResult.getMsg());
                return rspResult;
            }

            if (!CollectionUtils.isEmpty(testSampleGroupList)) {
                for (TestSampleGroupInfoPO item : testSampleGroupList) {
                    // POSL-4585 校验可以添加重复测点
                    if (!CollectionUtils.isEmpty(noSplitTestLine)) {
                        continue;
                    }
                    if (allSampleIds.contains(item.getSampleGroupID())) {
                        return rspResult.fail("校验失败！TestLine上不能出现重复的测点");
                    }
                }
            }
        }


        rspResult.setSuccess(true);
        return rspResult;
    }



//    @TestLinePending(filedName = "testLineInstanceId", type=TestLinePendingTypeEnums.TL_ID)
    @AccessRule(testLinePendingType = TestLinePendingTypeEnums.TestLineInstanceId)
    public CustomResult<List<CopyTestSampleRsp>> copyTestLineGetSample(CopyTestLineGetSampleReq copyTestLineGetSampleReq) {
        String testLineInstanceId =copyTestLineGetSampleReq.getTestLineInstanceId();
        String matrixGroupId = copyTestLineGetSampleReq.getMatrixGroupId();
        CustomResult<List<CopyTestSampleRsp>> rspResult = new CustomResult<>();

        //校验是否内部分包 并且lock
        CustomResult result = testMatrixService.checkTLSubDataLock(copyTestLineGetSampleReq.getTestLineInstanceId());
        if(!result.isSuccess()){
            return result;
        }

        List<CopyTestSampleRsp> list = Lists.newArrayList();
        List<TestSampleInfoPO> copyTestSamples = testSampleMapper.getCopyTestSample(matrixGroupId, testLineInstanceId);
        if (! CollectionUtils.isEmpty(copyTestSamples)) {
            for (TestSampleInfoPO copyTestSample : copyTestSamples) {
                CopyTestSampleRsp copyTestSampleRsp = new CopyTestSampleRsp();
                BeanUtils.copyProperties(copyTestSample,copyTestSampleRsp);
                list.add(copyTestSampleRsp);
            }
        }
        rspResult.setData(list);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * @param reqObject
     * @return
     */
    public CustomResult deleteOriginalSample(DeleteOriginalSampleReq reqObject) {
        CustomResult rspResult = new CustomResult(false);

        if (reqObject == null || StringUtils.isBlank(reqObject.getSampleId())) {
            return rspResult.fail("params must be not empty!");
        }
        String sampleId = reqObject.getSampleId();
        List<String> matrixListBySampleIds = testMatrixMapper.getMatrixListBySampleIds(Sets.newHashSet(sampleId));
        if (!CollectionUtils.isEmpty(matrixListBySampleIds)) {
            return rspResult.fail("This Sample has Assigned!");
        }
        String orderNo = reqObject.getOrderNo();
        List<TestSampleInfoPO> sampleInfoByParentId = testSampleMapper.getSampleInfoByParentId(orderNo, sampleId);
        if (!CollectionUtils.isEmpty(sampleInfoByParentId)) {
            return rspResult.fail("This Sample has Child!");
        }
        List<TestSampleGroupInfoPO> testSampleGroups = testSampleGroupMapper.getTestSampleGroupBySampleGroupId(Lists.newArrayList(sampleId));
        if (!CollectionUtils.isEmpty(testSampleGroups)) {
            return rspResult.fail("This Sample has Mix Sample!");
        }

        LogInfoWithBLOBs log = new LogInfoWithBLOBs();
        log.setId(UUID.randomUUID().toString());
        log.setUserName(reqObject.getUserName());
        log.setObjectId(sampleId);
        log.setObjectType(LogOperationTypeEnums.sample.getCode());
        log.setOperationDate(DateUtils.getNow());
        log.setOperationType(LogOperationEnums.del.getDesc());
        log.setOldValue(null);
        log.setNewValue(JSONObject.toJSONString(sampleInfoByParentId));

        Integer execute = transactionTemplate.execute(trans -> {
            int i = testSampleMapper.batchDelete(Lists.newArrayList(sampleId));
            if (i < 0) {
                trans.setRollbackOnly();
            }
            i = logMapper.batchInsert(Lists.newArrayList(log));
            if (i < 0) {
                trans.setRollbackOnly();
            }
            return i;
        });

        rspResult.setData(execute > -1);
        rspResult.setSuccess(execute > -1);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getOriginalSampleInfoList(OriginalSampleByOrderNoReq reqObject) {
        CustomResult rspResult = new CustomResult();
        List<OriginalSampleInfo> originalSamples = testSampleMapper.getOriginalSampleInfoList(reqObject.getOrderNo());
        rspResult.setData(originalSamples);
        rspResult.setSuccess(true);
        return rspResult;
    }

    public CustomResult getTestSampleListByOrderNo(OriginalSampleByOrderNoReq reqObject){
        CustomResult rspResult = new CustomResult();
        List<QuerySampleInfo> list = testSampleMapper.getTestSampleListByOrderNo(reqObject.getOrderNo());
        rspResult.setData(list);
        rspResult.setSuccess(true);
        return rspResult;
    }

    public CustomResult queryTestResultByOrderNo(OriginalSampleByOrderNoReq reqObject){
        CustomResult rspResult = new CustomResult();
        List<TestResultRsp> list = testSampleMapper.queryTestResultByOrderNo(reqObject.getOrderNo());
        rspResult.setData(list);
        rspResult.setSuccess(true);
        return rspResult;
    }

    public CustomResult getSampleExtInfo(SampleComponentReq reqObject){
        CustomResult rspResult = new CustomResult();
        UserInfo userInfo = tokenClient.getUser();
        if(Objects.isNull(userInfo)){
            return rspResult.fail("登录失效");
        }
        String orderNo = reqObject.getOrderNo();
        OrderTrfRelationshipDTO relationshipDTO = orderClient.getOrderTrfRelationshipByOrderNo(orderNo, ProductLineContextHolder.getProductLineCode());
        if(Objects.isNull(relationshipDTO)){
            return rspResult.fail("未绑定trfNO");
        }
        if(!RefSystemIdEnum.check(relationshipDTO.getReferenceId(),RefSystemIdEnum.Shein,RefSystemIdEnum.SheinSupplier)){
            return rspResult.fail("未绑定希音trfNo");
        }
        Integer referenceId = relationshipDTO.getReferenceId();

        List<SampleComponentItemReq> components = reqObject.getComponents();
        Map<String, SampleComponentItemReq> sampleNoSKU = Maps.newHashMap();
        if(!CollectionUtils.isEmpty(components)){
            sampleNoSKU = components.stream()
                    .filter(c->StringUtils.isNotBlank(c.getMaterialSku()))
                    .collect(Collectors.toMap(req->String.format("%s_%s",req.getSampleParentId() , req.getSampleNo()), Function.identity(), (k1, k2) -> k1));
        }

        List<OrderTrfRelationshipItemDTO> refList = relationshipDTO.getReferenceNos();
//        List<String> trfNos = refList.stream().map(OrderTrfRelationshipItemDTO::getReferenceNo).collect(Collectors.toList());
//        Map<String, Set<String>> trfMaterialMap = this.getTrfMaterialMap(orderNo, referenceId, trfNos);
        Map<String, String> originalSampleIdExtSampleNoMap = this.getOriginalSampleIdExtSampleNoMap(orderNo);

        Map<String,OrderTrfRelationshipItemDTO> parentMap = refList.stream().collect(Collectors.toMap(OrderTrfRelationshipItemDTO::getRefObjectId,o->o,(o1,o2)->o1));
        List<TestSampleExtInfo> list = testSampleExtMapper.selectTestSampleExtByOrderNo(reqObject.getOrderNo());
        List<TestSampleExtDTO> rList = Lists.newArrayList();
        Long count = list.stream().filter(x->StringUtils.isBlank(x.getMaterialSku())).count();
        boolean isSheinSupplier = RefSystemIdEnum.check(relationshipDTO.getReferenceId(), RefSystemIdEnum.SheinSupplier);
        if (isSheinSupplier) {
            count = NumberUtil.toLong(list.size());
        }
        List<String> skuList = Lists.newArrayList();
        CustomResult<OrderSimplifyInfoRsp> orderResult = orderClient.getOrderSimplifyInfo(orderNo);
        if(!orderResult.isSuccess() || Objects.isNull(orderResult.getData())){
            return rspResult.fail("未查询到该订单信息");
        }
        OrderSimplifyInfoRsp infoRsp = orderResult.getData();
        List<TestSampleExtInfoPO> extList = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(list)){
            Map<String, SampleComponentItemReq> finalSampleNoSKU = sampleNoSKU;
            rList = list.stream().map(x->{
                String sampleNo = x.getSampleNo();
                String sampleParentID = x.getSampleParentID();
                String key = String.format("%s_%s",sampleParentID,sampleNo);
                SampleComponentItemReq componentItemReq = finalSampleNoSKU.get(key);
                TestSampleExtDTO extDTO = new TestSampleExtDTO();
                BeanUtils.copyProperties(x,extDTO);
                extDTO.setMaterialCategory(orderService.splitCategoryValue(x.getMaterialCategory()));
                extDTO.setMaterialCategoryText(JSONObject.parseArray(x.getMaterialCategoryText(),String.class));
                extDTO.setMaterialCategoryTextString(String.join("/",x.getMaterialCategoryText()));
                OrderTrfRelationshipItemDTO itemDTO = parentMap.get(x.getSampleParentID());
                /*if(Objects.isNull(itemDTO) && RefSystemIdEnum.check(relationshipDTO.getReferenceId(),RefSystemIdEnum.Shein)){
                    return extDTO;
                }*/
                if(!Objects.isNull(itemDTO)){
                    extDTO.setCreateType(itemDTO.getCreateType());
                }

                if(RefSystemIdEnum.check(relationshipDTO.getReferenceId(),RefSystemIdEnum.SheinSupplier)){
                    extDTO.setCreateType(SheinCreateTypeEnum.BUILD_SELF.getCode());
                }
                List<String> materialSkuOptions = Lists.newArrayList();
                if(StringUtils.isBlank(x.getMaterialSku())){
                    String materialSku = StringUtils.EMPTY;
                    if (componentItemReq != null && StringUtils.isNotBlank(componentItemReq.getMaterialSku())) {
                        materialSku = componentItemReq.getMaterialSku();
                    } else {
                        materialSku = this.buildSKUNumber(infoRsp, orderNo, 1);
                    }
                    extDTO.setMaterialSku(materialSku);
                    materialSkuOptions.add(materialSku);
                    TestSampleExtInfoPO extInfoPO = new TestSampleExtInfoPO();
                    extInfoPO.setSampleId(x.getSampleId());
                    extInfoPO.setFieldCode("materialSku");
                    extInfoPO.setFieldName("materialSku");
                    extInfoPO.setFieldValue(materialSku);
                    extInfoPO.setStatus(1);
                    extInfoPO.setCreatedBy(userInfo.getRegionAccount());
                    extInfoPO.setCreatedDate(new Date());
                    extInfoPO.setModifiedBy(userInfo.getRegionAccount());
                    extInfoPO.setModifiedDate(new Date());
                    extList.add(extInfoPO);
                } else {
                    materialSkuOptions.add(extDTO.getMaterialSku());
                }
                String extSampleMaterialSku = StringUtils.EMPTY;
                if (originalSampleIdExtSampleNoMap.containsKey(x.getSampleParentID()) && StringUtils.isNotBlank(originalSampleIdExtSampleNoMap.get(x.getSampleParentID()))) {
                    extSampleMaterialSku = originalSampleIdExtSampleNoMap.get(x.getSampleParentID());
                }
                if (StringUtils.isNotBlank(extSampleMaterialSku) && isSheinSupplier) {
                    if (materialSkuOptions.contains(extSampleMaterialSku) && materialSkuOptions.size() > 0) {
                        materialSkuOptions.add(this.buildSKUNumber(infoRsp, orderNo, 1));
                    } else {
                        materialSkuOptions.add(extSampleMaterialSku);
                    }
                }
                extDTO.setMaterialSkuOptions(materialSkuOptions);

                if (componentItemReq != null && StringUtils.isNotBlank(componentItemReq.getMaterialItem())) {
                    String materialItem = componentItemReq.getMaterialItem();
                    TestSampleExtInfoPO extInfoItem = new TestSampleExtInfoPO();
                    extInfoItem.setSampleId(x.getSampleId());
                    extInfoItem.setFieldCode("materialItem");
//                        extInfoItem.setFieldName("materialItem");
                    extInfoItem.setFieldValue(materialItem);
                    extInfoItem.setStatus(1);
                    extInfoItem.setCreatedBy(userInfo.getRegionAccount());
                    extInfoItem.setCreatedDate(new Date());
                    extInfoItem.setModifiedBy(userInfo.getRegionAccount());
                    extInfoItem.setModifiedDate(new Date());
                    extList.add(extInfoItem);
                }
                if (componentItemReq != null && StringUtils.isNotBlank(componentItemReq.getMaterialCategory())) {
                    String materialCategory = componentItemReq.getMaterialCategory();
                    TestSampleExtInfoPO extInfoCategory = new TestSampleExtInfoPO();
                    extInfoCategory.setSampleId(x.getSampleId());
                    extInfoCategory.setFieldCode("materialCategory");
//                        extInfoCategory.setFieldName("materialCategory");
                    extInfoCategory.setFieldText(orderService.splitCategory(materialCategory));
                    extInfoCategory.setStatus(1);
                    extInfoCategory.setCreatedBy(userInfo.getRegionAccount());
                    extInfoCategory.setCreatedDate(new Date());
                    extInfoCategory.setModifiedBy(userInfo.getRegionAccount());
                    extInfoCategory.setModifiedDate(new Date());
                    extList.add(extInfoCategory);
                }

            return extDTO;
            }).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(extList)){
                int result = testSampleExtMapper.batchInsert(extList);
                if(result < extList.size()){
                    return rspResult.fail("生成materialSku失败");
                }
            }
        }
        rspResult.setData(rList);
        rspResult.setSuccess(true);
        return rspResult;
    }


    private String buildSKUNumber(OrderSimplifyInfoRsp infoRsp, String orderNo, int count) {
        QuerySkuCodeReq skuCodeReq = new QuerySkuCodeReq();
        skuCodeReq.setReqBuCode(infoRsp.getBuCode());
        skuCodeReq.setReqLocationCode(infoRsp.getLocationCode());
        skuCodeReq.setSystemId(2);
        skuCodeReq.setQuantity(NumberUtil.toInt(count));
        skuCodeReq.setNumberRuleCode("SheinSKUNo");
        SegmentDTO segmentDTO = new SegmentDTO();
        segmentDTO.setTransactionNo(orderNo);
        skuCodeReq.setNumberSegment(segmentDTO);
        CustomResult<List<String>> skuResult = frameWorkClient.querySheinSku(skuCodeReq);
        if (CollectionUtils.isEmpty(skuResult.getData())) {
            throw new RuntimeException("自动生成materialSku失败");
        }
        return skuResult.getData().get(0);
    }

    /**
     *
     * @param orderNo
     * @param referenceId
     * @param trfNos
     * @return
     */
    private Map<String, Set<String>> getTrfMaterialMap(String orderNo, Integer referenceId, List<String> trfNos) {
        Map<String, Set<String>> trfMaterialSkuMaps = Maps.newHashMap();
        List<TrfRelComponentExtInfo> trfComponentInfoByTrfNo = trfRelClient.getTrfComponentInfoByTrfNo(trfNos, referenceId);
        if (CollectionUtil.isNotEmpty(trfComponentInfoByTrfNo)){
            trfComponentInfoByTrfNo.forEach(trf -> {
                if (StringUtils.isBlank(trf.getMaterialSku())) {
                    return;
                }
                if (!trfMaterialSkuMaps.containsKey(trf.getTrfNo())) {
                    trfMaterialSkuMaps.put(trf.getTrfNo(), Sets.newHashSet());
                }
                trfMaterialSkuMaps.get(trf.getTrfNo()).add(trf.getMaterialSku());
            });
        }




        return trfMaterialSkuMaps;
    }

    private Map<String, String> getOriginalSampleIdExtSampleNoMap(String orderNo) {
        // 获取PreOrder信息
        List<ProductInfo> originalSampleInfos = dffClient.getOriginalSampleInfos(orderNo, StringUtils.defaultIfBlank(ProductLineContextHolder.getProductLineCode(), ProductLineType.SL.getProductLineAbbr()));
        Map<String, String> sampleIdExtFieldsMaps = originalSampleInfos.stream()
                .filter(item -> StringUtils.isNotBlank(item.getRefSampleID()) && StringUtils.isNotBlank(item.getExtFields()))
                .collect(Collectors.toMap(ProductInfo::getRefSampleID, ProductInfo::getExtFields, (k1, k2) -> k1));

        Map<String, String> sampleIdExtSampleNoMaps = Maps.newHashMap();
        if (CollectionUtil.isEmpty(sampleIdExtFieldsMaps)) {
            return sampleIdExtSampleNoMaps;
        }
        for (String originalSampleId : sampleIdExtFieldsMaps.keySet()) {
            if (StringUtils.isBlank(sampleIdExtFieldsMaps.get(originalSampleId))) {
                continue;
            }
            String extField = sampleIdExtFieldsMaps.get(originalSampleId);
            SampleExtFieldDto sampleExtFieldDto = JSON.parseObject(extField, SampleExtFieldDto.class);
            sampleIdExtSampleNoMaps.put(originalSampleId, sampleExtFieldDto.getExternalSampleNo());
        }
        return sampleIdExtSampleNoMaps;
    }

    public CustomResult saveSampleExtInfo(SampleExtInfoReq reqObject){
        CustomResult rspResult = new CustomResult();
        UserInfo userInfo = UserHelper.getLocalUser();
        if(Objects.isNull(reqObject) || CollectionUtils.isEmpty(reqObject.getData()) ){
            rspResult.fail("参数错误");
            return rspResult;
        }
        List<String> sampleNos = Lists.newArrayList();
        try{
            List<TestSampleExtInfoPO> list = Lists.newArrayList();
            List<TestSampleExtDTO> sampleExtDTOS = reqObject.getData();
            Set<String> sampleIds = sampleExtDTOS.stream().map(TestSampleExtDTO::getSampleId).collect(Collectors.toSet());
            List<TestSampleInfoPO> sampleInfoPOS = testSampleMapper.getTestSampleIdList(Lists.newArrayList(sampleIds));
            Map<String,TestSampleInfoPO> sampleMap = sampleInfoPOS.stream().collect(Collectors.toMap(TestSampleInfoPO::getID,Function.identity(),(o1,o2)->o1));
            List<TestSampleInfoPO> updateSamples = Lists.newArrayList();
            for(TestSampleExtDTO x: reqObject.getData()){
                TestSampleInfoPO sampleInfoPO = sampleMap.get(x.getSampleId());
                for(Field field : x.getClass().getFields()){
                    TestSampleExtInfoPO extInfoPO = new TestSampleExtInfoPO();
                    if("materialCategoryText".equals(field.getName())){
                        continue;
                    }
                    extInfoPO.setSampleId(x.getSampleId());
                    extInfoPO.setFieldCode(field.getName());
                    extInfoPO.setFieldName(field.getName());
                    extInfoPO.setStatus(1);
                    extInfoPO.setCreatedBy(userInfo.getRegionAccount());
                    extInfoPO.setCreatedDate(new Date());
                    extInfoPO.setModifiedBy(userInfo.getRegionAccount());
                    extInfoPO.setModifiedDate(new Date());
                    if(field.get(x) instanceof List){
                        List<String> materialCategory = (List<String>) field.get(x);
                        extInfoPO.setFieldValue(String.join("/",materialCategory));
                    }else {
                        extInfoPO.setFieldValue(Objects.isNull(field.get(x))?null:field.get(x).toString());
                    }
                    if("materialCategory".equals(field.getName())){
                        extInfoPO.setFieldText(JSONObject.toJSONString(x.getMaterialCategoryText()));//String.join("/",x.getMaterialCategoryText())
                        // POSL-6360 不是客户回来的样品，才可以覆盖
                        if(!CollectionUtils.isEmpty(x.getMaterialCategoryText()) && !SampleSourceTypeEnum.check(sampleInfoPO.getSourceType(), SampleSourceTypeEnum.CUSTOMER)) {
                            String description = x.getMaterialCategoryText().get(x.getMaterialCategoryText().size()-1);
                            if(StringUtils.isBlank(description) && x.getMaterialCategoryText().size() > 1){
                                description = x.getMaterialCategoryText().get(x.getMaterialCategoryText().size()-2);
                            }
                            sampleInfoPO.setDescription(description);
                        }
                    }
                    if("materialItem".equals(field.getName())) {
                        // POSL-6360 不是客户回来的样品，才可以覆盖
                        if(!SampleSourceTypeEnum.check(sampleInfoPO.getSourceType(), SampleSourceTypeEnum.CUSTOMER)){
                            if(StringUtils.isBlank(sampleInfoPO.getOtherSampleInfo())){
                                sampleInfoPO.setOtherSampleInfo(extInfoPO.getFieldValue());
                            }else {
                                sampleNos.add(sampleInfoPO.getSampleNo());
                            }
                        }
                    }
                    list.add(extInfoPO);
                }
                updateSamples.add(sampleInfoPO);
            }
            if(!CollectionUtils.isEmpty(list)){
                testSampleExtMapper.batchInsert(list);
            }
            if (!CollectionUtils.isEmpty(updateSamples)){
                testSampleMapper.updatePreOrdeSample(updateSamples);
            }
            transactionTemplate.execute(trans -> {
                int execNum = 0;
                if(!CollectionUtils.isEmpty(list) && testSampleExtMapper.batchInsert(list) < 1){
                    trans.setRollbackOnly();
                    return execNum;
                }
                if (!CollectionUtils.isEmpty(updateSamples) && testSampleMapper.updatePreOrdeSample(updateSamples) < 1){
                    trans.setRollbackOnly();
                    return execNum;
                }
                return execNum;
            });
        }catch (IllegalAccessException e){
            logger.error("保存testSampleExt表出错,参数:{},message：{}",JSONObject.toJSONString(reqObject),e.getMessage(),e);
        }
        if(!CollectionUtils.isEmpty(sampleNos)){
            rspResult.setMsg(String.format("SampleNos: %s ,Other Sample Info is not null,Please fill in manually",StringUtils.join(sampleNos,"、")));
        }
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * 查询所有子样数据
     * 子样是 没有被assign，没有子子样，没有做share样，没有cancel ，没有nc的
     * @param reqObject
     * @return
     */
    public CustomResult getComponentSample(SampleIdsReq reqObject) {
        if(reqObject == null || StringUtils.isBlank(reqObject.getOrderNo())){
            return CustomResult.failure("Parameter error");
        }
        CustomResult<List<ChildSampleRsp>> result = new CustomResult<>();
        String orderNo = reqObject.getOrderNo();
        OrderTrfRelationshipDTO relationshipDTO = orderClient.getOrderTrfRelationshipByOrderNo(orderNo, ProductLineContextHolder.getProductLineCode());
        if(Objects.isNull(relationshipDTO)){
           return CustomResult.failure("未绑定trfNO");
        }
        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfo(orderNo);
        String orderId = orderInfo.getID();
        List<TestSampleInfoPO> allSample = testSampleMapper.getSampleByOrderNo(orderNo);
        List<TestSampleGroupInfoPO> allSampleGroup = testSampleGroupMapper.getSampleGroupByOrderNo(orderNo);
        List<TestMatrixPO> matrixList = testMatrixMapper.getTestMatrixByOrderId(orderId);
        //原样
        Map<String, String> originalSampleIdNoMap = allSample.stream().filter(s -> SampleType.check(s.getSampleType(), SampleType.OriginalSample))
                .collect(Collectors.toMap(TestSampleInfoPO::getID, TestSampleInfoPO::getSampleNo));
        //子子样
        List<String> subSampleIdList = allSample.stream().filter(s -> SampleType.check(s.getSampleType(), SampleType.SubSample))
                .map(s -> s.getSampleParentID()).distinct().collect(Collectors.toList());
        //有被assign
        List<String> matrixSampleIds = matrixList.stream().map(m -> m.getTestSampleID()).distinct().collect(Collectors.toList());
        //有mix /share等
        List<String> groupSampleIds = allSampleGroup.stream().map(g -> g.getSampleGroupID()).distinct().collect(Collectors.toList());

        List<TestSampleInfoPO> normalSample = allSample.stream()
                .filter(s -> s.getActiveIndicator() && !s.getApplicable())//正常状态，非cancel,非NC
                .filter(s -> SampleType.check(s.getSampleType(), SampleType.Sample))//找到子样
                .filter(s -> !groupSampleIds.contains(s.getID()))//没有做mix，share
                .filter(s -> !matrixSampleIds.contains(s.getID()))//没有被assign
                .filter(s -> !subSampleIdList.contains(s.getID()))//没有子子样
                .collect(Collectors.toList());

        List<ChildSampleRsp> rsps = normalSample.stream().map(s -> {
            ChildSampleRsp rsp = new ChildSampleRsp();
            String sampleParentID = s.getSampleParentID();
            String parentSampleNo = originalSampleIdNoMap.get(sampleParentID);
            rsp.setSampleId(s.getID());
            String sampleNo = parentSampleNo+"-"+s.getSampleNo();
            rsp.setSampleNo(sampleNo);
            rsp.setSampleDesc(s.getDescription());
            rsp.setCategory(s.getCategory());
            return rsp;
        }).collect(Collectors.toList());
        result.setData(rsps);
        result.setSuccess(true);
        return result;
    }

    /**
     * 批量更新category
     * @param req
     * @return
     */
    public CustomResult updateComponentSampleCategory(ChangeCategoryReq req) {
        if(req == null || StringUtils.isBlank(req.getOrderNo()) || CollectionUtils.isEmpty(req.getItems())){
            return CustomResult.failure("Parameter error");
        }
        //目前是只针对绑定了希音的
        String orderNo = req.getOrderNo();
        OrderTrfRelationshipDTO relationshipDTO = orderClient.getOrderTrfRelationshipByOrderNo(orderNo, ProductLineContextHolder.getProductLineCode());
        if(Objects.isNull(relationshipDTO)){
            return CustomResult.failure("未绑定trfNO");
        }

        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfo(orderNo);
        String orderId = orderInfo.getID();
        List<TestSampleInfoPO> allSample = testSampleMapper.getSampleByOrderNo(orderNo);
        Map<String, String> idNoMap = allSample.stream().collect(Collectors.toMap(TestSampleInfoPO::getID, TestSampleInfoPO::getSampleNo, (k1, k2) -> k1));

        List<TestSampleGroupInfoPO> allSampleGroup = testSampleGroupMapper.getSampleGroupByOrderNo(orderNo);
        //所有的子样
        List<TestSampleInfoPO> dbChildSampleList = allSample.stream()
                .filter(s -> SampleType.check(s.getSampleType(), SampleType.Sample))
                .collect(Collectors.toList());

        List<TestMatrixPO> matrixList = testMatrixMapper.getTestMatrixByOrderId(orderId);
        //NC
        List<String> NCSampleList = allSample.stream()
                .filter(s -> SampleType.check(s.getSampleType(), SampleType.Sample))
                .filter(s -> !s.getActiveIndicator())
                .map(s -> s.getID())
                .distinct().collect(Collectors.toList());
        //cancel
        List<String> cancelSampleList = allSample.stream()
                .filter(s -> SampleType.check(s.getSampleType(), SampleType.Sample))
                .filter(s -> s.getApplicable())
                .map(s -> s.getID())
                .distinct().collect(Collectors.toList());
        //子子样
        List<String> subSampleIdList = allSample.stream().filter(s -> SampleType.check(s.getSampleType(), SampleType.SubSample))
                .map(s -> s.getSampleParentID()).distinct().collect(Collectors.toList());
        //有被assign
        List<String> matrixSampleIds = matrixList.stream().map(m -> m.getTestSampleID()).distinct().collect(Collectors.toList());
        //有mix /share等
        List<String> groupSampleIds = allSampleGroup.stream().map(g -> g.getSampleGroupID()).distinct().collect(Collectors.toList());

        List<ChangeCategoryItemReq> items = req.getItems();

        UserInfo user = tokenClient.getUser();
        String regionAccount = user.getRegionAccount();
        //校验当前sample是否可以进行操作change category
        for (ChangeCategoryItemReq sample : items) {
            String sampleId = sample.getSampleId();
            String no = idNoMap.get(sampleId);
            if(subSampleIdList.contains(sampleId)){
                return CustomResult.failure("有Component[%s]添加了Subcomponent不能修改Category，请检查后重新打开当前页面",no);
            }
            if(matrixSampleIds.contains(sampleId)){
                return CustomResult.failure("有Component[%s]分配TL则不能修改Category，请检查后重新打开当前页面",no);
            }
            if(groupSampleIds.contains(sampleId)){
                return CustomResult.failure("有Component[%s]参与Mix 或者 Share不能修改Category，请检查后重新打开当前页面",no);
            }
            if(NCSampleList.contains(sampleId)){
                return CustomResult.failure("有Component[%s]已经NC不能修改Category，请检查后重新打开当前页面",no);
            }
            if(cancelSampleList.contains(sampleId)){
                return CustomResult.failure("有Component[%s]已经Cancel不能修改Category，请检查后重新打开当前页面",no);
            }
            sample.setModifiedBy(regionAccount);
            sample.setModifiedDate(DateUtils.getNow());

        }
        /**
         * 逻辑发生改变，C转为P的时候，需要清空C的标识，同时重新计算P对应的序号
         * 同理，P转为C的时候，需要序号加字母C，同时重新计算当前序号
         */
        Map<String, TestSampleInfoPO> idPoMap = dbChildSampleList.stream()
                .collect(Collectors.toMap(TestSampleInfoPO::getID, Function.identity(), (k1, k2) -> k1));

        //分出 物理，化学，因为化学的有C ，所以要去C之后计算最大值
        List<TestSampleInfoPO> chemChildList = dbChildSampleList.stream().filter(s -> CategoryType.check(s.getCategory(), CategoryType.Chem)).collect(Collectors.toList());
        List<Integer> chemChildNoList = chemChildList.stream().map(s -> NumberUtil.toInt(s.getSampleNo().replaceAll("^[A-Za-z]", ""))).collect(Collectors.toList());
        //物理的子样，就是纯数字，不用加C
        List<TestSampleInfoPO> phyChildList = dbChildSampleList.stream().filter(s -> CategoryType.check(s.getCategory(), CategoryType.Phy)).collect(Collectors.toList());
        List<Integer> phyChildNoList = phyChildList.stream().map(s -> NumberUtil.toInt(s.getSampleNo())).collect(Collectors.toList());

        int cheMaxNo = CollectionUtils.isEmpty(chemChildNoList) ? 0 : Collections.max(chemChildNoList);
        int phyMaxNo = CollectionUtils.isEmpty(phyChildNoList) ? 0 : Collections.max(phyChildNoList);

        List<ChangeCategoryItemReq> changeList = Lists.newArrayList();
        for (ChangeCategoryItemReq pageItem : items) {
            String pageSampleId = pageItem.getSampleId();
            String pageCategory = pageItem.getCategory();
            TestSampleInfoPO dbSampleInfo = idPoMap.get(pageSampleId);
            String dbCategory = dbSampleInfo.getCategory();
            //没有修改，就不处理了
            if(dbCategory.equalsIgnoreCase(pageCategory)){
                continue;
            }
            //当前db是P,但是页面转成了C ,所以no需要加上字母C，同时sampleNo 改成所有子样中chem的最大no+1
            if("P".equalsIgnoreCase(dbCategory)){
                String newSampleNo = "C"+(++cheMaxNo);
                pageItem.setSampleNo(newSampleNo);
            }
            //当前db是C,页面转成了P，所以no需要去掉原本的字母C,同时改成所有子样中，phy的最大值+1
            if("C".equalsIgnoreCase(dbCategory)){
                pageItem.setSampleNo(String.valueOf(++phyMaxNo));
            }
            changeList.add(pageItem);
        }

        CustomResult<Object> result = new CustomResult<>();
        int i = transactionTemplate.execute(transactionStatus ->  testSampleMapper.batchUpdateCategoryById(changeList));
        result.setSuccess(i>-1);
        return result;
    }

    /**
     * 校验提交数据是否符合要求
     * @param req
     * @return
     */
    public CustomResult checkSubmitData(TestSampleSubmitReq req) {
        CustomResult<Object> result = new CustomResult<>(true);
        String orderNo = req.getOrderNo();
        //if ( 增减的Notes.tb_test_sampe.SampleType = 102 & component->Original Sample对应的PreOrder.tb_order_trf_relationship.createType = 2)
        //check当前绑定的数据
        OrderInfoDto orderInfoByOrderNo = orderClient.getOrderInfoByOrderNo(orderNo);
        if(orderInfoByOrderNo==null){
            return result.fail("Query order info fail");
        }
        String id = orderInfoByOrderNo.getID();
        List<OrderTrfRelInfo> orderTrfRelationship = orderClient.getOrderTrfRelationship(id, ProductLineContextHolder.getProductLineCode());
        //没有绑定关系，不处理
        if(CollectionUtils.isEmpty(orderTrfRelationship)){
            return result;
        }
        //有绑定关系，且存在绑定关系type为2的数据
        List<OrderTrfRelInfo> trfRelList = orderTrfRelationship.stream().filter(o -> NumberUtil.toInt(o.getCreateType()) == 2).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(trfRelList)){
            return result;
        }
        //找到绑定的是2的数据的原样信息
        Set<String> bindOriginalSampleIdSet = trfRelList.stream().map(r -> r.getRefObjectId()).collect(Collectors.toSet());
        //然后筛选出页面数据为绑定了trf的数据
        List<TestSampleCheckReq> pageTestSamples = req.getTestSamples();
        //页面 原样绑定了trf关系的 子样
        List<TestSampleCheckReq> pageComponent = pageTestSamples.stream()
                .filter(s -> SampleType.check(s.getSampleType(),SampleType.Sample))
                .filter(s -> bindOriginalSampleIdSet.contains(s.getSampleParentId()))
                .collect(Collectors.toList());
        //页面数据没有对应的子样，不处理
        if(CollectionUtils.isEmpty(pageComponent)){
            return result;
        }

        //先看页面有么有新增，就是参数中没有id的数据，如果有，直接提示，不用和db的做比较
        //必须是子样102 的新增
        List<TestSampleCheckReq> newComponent = pageComponent.stream().filter(s -> StringUtils.isBlank(s.getId())).collect(Collectors.toList());

        TestSampleSubmitRsp rsp = new TestSampleSubmitRsp();
        if(!CollectionUtils.isEmpty(newComponent)){
            List<String> sampleNos = newComponent.stream().map(s -> s.getSampleNo()).collect(Collectors.toList());
            rsp.setBindComponent(1);
            rsp.setSampleNos(sampleNos);
            result.setData(rsp);
            return result;
        }

        //根据sample 查找当前DB中的所有sample
        List<TestSampleInfoPO> dbSampleList = testSampleMapper.getSampleByOrderNo(orderNo);
        //查找语DB不同 102 sample
        List<TestSampleInfoPO> dbComponent = dbSampleList.stream()
                .filter(s -> SampleType.check(s.getSampleType(), SampleType.Sample))
                .filter(s -> bindOriginalSampleIdSet.contains(s.getSampleParentID()))//必须是绑定了2的原样的子样才行
                .collect(Collectors.toList());
        //这种情况存在于，两个页面单开 db中已经没有子样信息， 正常的bind trf createType=2的数据是不会有这种情况的，加判断是确保万一
        if(CollectionUtils.isEmpty(dbComponent)){
            //页面进来的所有子样，都要提示
            List<String> sampleNos = pageComponent.stream().map(s -> s.getSampleNo()).collect(Collectors.toList());
            rsp.setBindComponent(1);
            rsp.setSampleNos(sampleNos);
            result.setData(rsp);
            return result;
        }

        List<String> pageComponentsIdList = pageComponent.stream().map(s -> s.getId()).collect(Collectors.toList());
        //数据库的子样 和 页面传进来的做对比，主要看删除，因为新增在上面已经处理了，删除的话，就以DB为基准
        List<TestSampleInfoPO> delPageComponent = dbComponent.stream().filter(dbS -> !pageComponentsIdList.contains(dbS.getID())).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(delPageComponent)){
            return result;
        }
        List<String> pageSampleNos = delPageComponent.stream().map(s -> s.getSampleNo()).collect(Collectors.toList());
        rsp.setBindComponent(1);
        rsp.setSampleNos(pageSampleNos);
        result.setData(rsp);
        return result;
    }


    /**
     * DIG-7782 check Assign Mix Sample
     * @param reqObject
     * @return
     */
    public CustomResult<Boolean> checkMixAssignSample(CheckMixAssignSampleReq reqObject) {
        CustomResult<Boolean> rspResult = new CustomResult(false);
        if (reqObject == null || (CollectionUtils.isEmpty(reqObject.getSampleIds()) && CollectionUtils.isEmpty(reqObject.getMatrixIds()))) {
            return rspResult.fail("请检查参数（sampleIds, matrixIds）");
        }
        List<String> sampleIds = reqObject.getSampleIds();
        List<String> matrixIds = reqObject.getMatrixIds();
        // 耐互染色 TestLine
        if (CollectionUtils.isEmpty(reqObject.getCheckTypeTLs())) {
            reqObject.setCheckTypeTLs(Sets.newHashSet());
        }
        Set<Integer> checkTypeTLs = reqObject.getCheckTypeTLs();
        // 可以同时添加Mix样和包含的子样
        if (CollectionUtils.isEmpty(reqObject.getMixAndChildTLs())) {
            reqObject.setMixAndChildTLs(Sets.newHashSet());
        }
        Set<Integer> mixAndChildTLs = reqObject.getMixAndChildTLs();

        rspResult.setSuccess(true);
        // 需要校验的testLine
        if (CollectionUtils.isEmpty(reqObject.getNeedCheckTls())) {
            rspResult.setMsg("校验成功！（testLine 不需要校验）");
            rspResult.setData(true);
            return rspResult;
        }
        Set<Integer> needCheckTls = reqObject.getNeedCheckTls();

        if (!CollectionUtils.isEmpty(sampleIds)) {
            // 获取 sampleId 中的 Mix 样
            List<TestSampleInfoPO> sampleIdList = testSampleMapper.getTestSampleIdList(sampleIds);
            if (CollectionUtils.isEmpty(sampleIdList)) {
                return rspResult.fail("请检查参数（sampleIds）");
            }
            List<String> mixSampleIds = sampleIdList.stream()
                    .filter(sa -> SampleType.check(sa.getSampleType(), SampleType.MixSample) && sa.getActiveIndicator())
                    .map(TestSampleInfoPO::getID).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(reqObject.getCancelSamples())) {
                reqObject.setCancelSamples(Lists.newArrayList());
            }
            mixSampleIds.removeIf(item -> reqObject.getCancelSamples().contains(item));

            // DIG-7862
            List<String> notMixSampleIds = sampleIdList.stream()
                    .filter(sa -> !SampleType.check(sa.getSampleType(), SampleType.MixSample) && sa.getActiveIndicator())
                    .map(TestSampleInfoPO::getID).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(checkTypeTLs) && !CollectionUtils.isEmpty(notMixSampleIds)) {
                return rspResult.fail(String.format("耐互染色TestLine{%s}只允许Assign Sample类型为Mix的样品", StringUtils.join(checkTypeTLs, ",")));
            }

            if (CollectionUtils.isEmpty(mixSampleIds)) {
                rspResult.setMsg("校验成功！（无Mix样）");
                rspResult.setData(true);
                return rspResult;
            }
            List<TestSampleGroupInfoPO> testSampleGroupList = testSampleGroupMapper.getTestSampleGroupList(mixSampleIds);
            if (!CollectionUtils.isEmpty(testSampleGroupList)) {
                // mixAndChildTLs的testLine 不需要校验重复测点 （已在前面校验，如果是mixAndChildTLs，只会单独assignSample）
                if (!CollectionUtils.isEmpty(mixAndChildTLs)) {
                    rspResult.setMsg("校验成功！");
                    rspResult.setData(true);
                    return rspResult;
                }
                // 判断Mix样的组合样是否重复（1+2 2+3）
                if (CollectionUtils.isEmpty(checkTypeTLs) && testSampleGroupList.stream().map(TestSampleGroupInfoPO::getSampleGroupID).collect(Collectors.toSet()).size() < testSampleGroupList.size()) {
                    rspResult.setSuccess(false);
                    rspResult.setData(false);
                    rspResult.setMsg("校验失败！TestLine上不能出现重复的测点");
                    return rspResult;
                }

                CustomResult customResult = this.checkMainMaterial(checkTypeTLs, mixSampleIds, testSampleGroupList);
                if (!customResult.isSuccess()) {
                    rspResult.setSuccess(false);
                    rspResult.setData(false);
                    rspResult.setMsg(customResult.getMsg());
                    return rspResult;
                }

                for (TestSampleGroupInfoPO item : testSampleGroupList){
                    if (sampleIds.contains(item.getSampleGroupID()) && !reqObject.getCancelSamples().contains(item.getSampleGroupID())) {
                        rspResult.setSuccess(false);
                        rspResult.setData(false);
                        rspResult.setMsg("校验失败！TestLine上不能出现重复的测点");
                        return rspResult;
                    }
                }
            }
            rspResult.setMsg("校验成功！");
            rspResult.setData(true);
            return rspResult;
        }

        // 根据matrixId 获取 testLine和 sample
        List<TestLineSampleTypeInfo> testLineSampleInfoByMatrixIds = testMatrixMapper.getTestLineSampleInfoByMatrixIds(matrixIds);

        if (CollectionUtils.isEmpty(testLineSampleInfoByMatrixIds)) {
            return rspResult.fail("请检查参数matrixIds, 获取TestLine 信息失败");
        }

        // testLine 分组检查 104 其中是否有重复的 sample
        // DIG-9324 Map Bug 修复
        Map<Integer, List<TestLineSampleTypeInfo>> testLineMaps = testLineSampleInfoByMatrixIds.stream().collect(Collectors.groupingBy(TestLineSampleTypeInfo::getTestLineId));

        Set<Integer> tlSets = Sets.newHashSet();
        for (Integer testLineId : testLineMaps.keySet()){
            List<TestLineSampleTypeInfo> testLineSampleTypeInfos = testLineMaps.get(testLineId);

            // 只校验 需要 校验的  testLine
            if (!needCheckTls.contains(NumberUtil.toInt(testLineId))) {
                continue;
            }

            List<String> mixSampleIds = testLineSampleTypeInfos.stream()
                    .filter(item -> SampleType.check(item.getSampleType(), SampleType.MixSample))
                    .map(TestLineSampleTypeInfo::getSampleId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(mixSampleIds)) {
                continue;
            }
            List<String> notMixSampleIds = testLineSampleTypeInfos.stream()
                    .filter(item -> !SampleType.check(item.getSampleType(), SampleType.MixSample))
                    .map(TestLineSampleTypeInfo::getSampleId).collect(Collectors.toList());
            List<TestSampleGroupInfoPO> testSampleGroupList = testSampleGroupMapper.getTestSampleGroupList(mixSampleIds);
            if (CollectionUtils.isEmpty(testSampleGroupList)) {
                continue;
            }
            if (!CollectionUtils.isEmpty(checkTypeTLs) && !CollectionUtils.isEmpty(notMixSampleIds)) {
                rspResult.setMsg(String.format("耐互染色{%s}只允许Assign Sample类型为Mix的样品", StringUtils.join(checkTypeTLs, ",")));
                rspResult.setData(true);
                return rspResult;
            }
            // POSL-4585 testLine Mix样和包含的子样 的时候  不需要校验重复测点
            Integer testLine = testLineSampleTypeInfos.stream().map(TestLineSampleTypeInfo::getTestLineId).findFirst().orElse(0);
            if (mixAndChildTLs.contains(testLine)) {
                continue;
            }

            if (CollectionUtils.isEmpty(checkTypeTLs) &&  testSampleGroupList.stream().map(TestSampleGroupInfoPO::getSampleGroupID).collect(Collectors.toSet()).size() < testSampleGroupList.size()) {
                rspResult.setSuccess(false);
                rspResult.setData(false);
                rspResult.setMsg("校验失败！TestLine上不能出现重复的测点");
                return rspResult;
            }

            Integer sampleTestLineId = testLineSampleTypeInfos.get(0).getTestLineId();
            if (checkTypeTLs.contains(sampleTestLineId)) {
                CustomResult customResult = this.checkMainMaterial(checkTypeTLs, mixSampleIds, testSampleGroupList);
                if (!customResult.isSuccess()) {
                    rspResult.setSuccess(false);
                    rspResult.setData(false);
                    rspResult.setMsg(customResult.getMsg());
                    return rspResult;
                }
            }

            for (TestSampleGroupInfoPO item : testSampleGroupList){
                if (notMixSampleIds.contains(item.getSampleGroupID())) {
                    tlSets.add(testLineSampleTypeInfos.get(0).getTestLineId());
                    continue;
                }
            }

        }
        if (!CollectionUtils.isEmpty(tlSets)) {
            rspResult.setSuccess(false);
            rspResult.setData(false);
            rspResult.setMsg(String.format("TL [%s] 下面出现了重复测点", StringUtils.join(tlSets, ",")));
            return rspResult;
        }

        rspResult.setData(true);
        return rspResult;
    }

    /**
     * 校验 Mix Sample组合的Component中必须有一个是Main Material且只能有一个
     * @param checkTypeTLs
     * @param mixSampleIds
     * @param testSampleGroupList
     * @return
     */
    private CustomResult checkMainMaterial(Set<Integer> checkTypeTLs, List<String> mixSampleIds, List<TestSampleGroupInfoPO> testSampleGroupList ) {
        CustomResult<Boolean> rspResult = new CustomResult(false);

        if (CollectionUtils.isEmpty(checkTypeTLs)) {
            return rspResult.success();
        }
        List<TestSampleExtInfo> testSampleExtBySampleId = testSampleExtMapper.getTestSampleExtBySampleId(mixSampleIds);
        if (CollectionUtils.isEmpty(testSampleExtBySampleId)) {
            return rspResult.success();
        }
        Set<String> mainMaterialSampleId = testSampleExtBySampleId.stream()
                .filter(item -> NumberUtil.equals(NumberUtil.toInt(item.getMainMaterial()), 1))
                .map(TestSampleExtInfo::getSampleId).collect(Collectors.toSet());

        Map<String, List<TestSampleGroupInfoPO>> mixSampleMaterialMap = testSampleGroupList.stream().collect(Collectors.groupingBy(TestSampleGroupInfoPO::getSampleID));
        for (String key : mixSampleMaterialMap.keySet()){
            List<TestSampleGroupInfoPO> testSampleGroupInfoPOS = mixSampleMaterialMap.get(key);
            Set<String> materialSampleIds = testSampleGroupInfoPOS.stream().map(TestSampleGroupInfoPO::getSampleGroupID).collect(Collectors.toSet());
            if (mainMaterialSampleId.stream().filter(item -> materialSampleIds.contains(item)).collect(Collectors.toSet()).size() != 1) {
                rspResult.setSuccess(false);
                rspResult.setData(false);
                rspResult.setMsg(String.format("校验失败！耐互染色TestLine{%s}上,同一个Mix样必须有一个是Main Material且只能有一个", StringUtils.join(checkTypeTLs, ",")));
                return rspResult;
            }
        }
        rspResult.setSuccess(true);
        return rspResult;
    }


    public CustomResult<List<TestSampleExtInfo>> querySampleExtInfo(SampleIdsReq reqObj) {
        CustomResult<List<TestSampleExtInfo>> result = new CustomResult<>();
        if(reqObj == null || StringUtils.isBlank(reqObj.getOrderNo())){
            return result.fail("参数异常");
        }

        String orderNo = reqObj.getOrderNo();
        List<TestSampleExtInfo> testSampleExtInfos = testSampleExtMapper.selectTestSampleExtByOrderNo(orderNo);
        result.setData(testSampleExtInfos);
        result.setSuccess(true);
        return result;
    }

    public CustomResult<List<TestLineSampleTypeInfo>> queryTestSampleByTestLineInstanceId(TestLineInstanceIdsReq reqObject){
        CustomResult rspResult = new CustomResult();
        if(reqObject == null || CollectionUtils.isEmpty(reqObject.getTestLineInstanceIds())){
            return rspResult.fail("testLineInstanceIds 不能为空");
        }
        List<TestLineSampleTypeInfo> list = testSampleMapper.getTestSampleListByTestLineInstanceId(reqObject.getTestLineInstanceIds());
        rspResult.setData(list);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param testSampleLangs
     * @param testSampleId
     * @param material
     * @return
     */
    private String getTestSampleLangId(List<TestSampleLangInfoPO> testSampleLangs, String testSampleId, String material){
        String testSampleLangId = UUID.randomUUID().toString();
        if (testSampleLangs == null || testSampleLangs.isEmpty() || StringUtils.isBlank(testSampleId)){
            return testSampleLangId;
        }
        for (TestSampleLangInfoPO lang: testSampleLangs){
            if (!StringUtils.equalsIgnoreCase(lang.getSampleId(), testSampleId)){
                continue;
            }
            if (!StringUtils.equalsIgnoreCase(lang.getMaterial(), material)){
                continue;
            }
            if (!LanguageType.check(lang.getLanguageId(), LanguageType.English)){
                continue;
            }
            testSampleLangId = lang.getId();
            testSampleLangs.removeIf(testSampleLang-> StringUtils.equalsIgnoreCase(testSampleLang.getId(), lang.getId()));
            break;
        }
        return testSampleLangId;
    }


    public CustomResult cleanSampleSourceTypeHistory(TestReq reqObject){
        CustomResult rspResult = new CustomResult();
        if(reqObject == null || StringUtils.isAnyBlank(reqObject.getStarDate(), reqObject.getEndDate())){
            return rspResult.fail("StarDate/EndDate 不能为空");
        }
        int i = testSampleMapper.cleanSampleSourceTypeHistory(reqObject.getStarDate(), reqObject.getEndDate());

        rspResult.setData(i);
        rspResult.setSuccess(true);
        return rspResult;
    }

}