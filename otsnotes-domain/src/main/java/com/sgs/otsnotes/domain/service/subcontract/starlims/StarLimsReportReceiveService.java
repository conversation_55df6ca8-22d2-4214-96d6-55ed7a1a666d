package com.sgs.otsnotes.domain.service.subcontract.starlims;

import com.sgs.framework.core.base.CustomResult;
import com.sgs.otsnotes.facade.model.req.starlims.receive.ReceiveStarLimsReportDocBodyReq;

/**
 * StarLims报告接收服务接口
 */
public interface StarLimsReportReceiveService {
    
    /**
     * 接收StarLims报告
     * @param request 接收请求
     * @return 处理结果
     */
    CustomResult<Void> receiveReportDoc(ReceiveStarLimsReportDocBodyReq request);
} 