package com.sgs.otsnotes.domain.service.sample.validator;

import com.sgs.otsnotes.domain.service.sample.context.SampleBreakDownContext;
import com.sgs.otsnotes.domain.service.sample.context.SampleBreakDownResult;

/**
 * 样品分解验证器接口
 * 
 * 【重构组件】负责样品分解过程中的各种验证逻辑
 * 
 * 主要功能：
 * - 参数有效性验证
 * - 业务规则验证
 * - 数据一致性验证
 * 
 * <AUTHOR>
 * @since 重构版本
 */
public interface SampleBreakDownValidator {
    
    /**
     * 验证样品分解上下文
     * 
     * 执行所有必要的验证逻辑，包括：
     * 1. 参数基础验证（已包含样品重复性验证和样品树结构验证）
     * 2. 混样编号重置
     * 3. NC状态验证
     * 4. 重复样品验证
     * 
     * @param context 样品分解上下文，包含所有验证所需的数据
     * @return 验证结果，包含成功/失败状态和错误信息
     */
    SampleBreakDownResult validate(SampleBreakDownContext context);
} 