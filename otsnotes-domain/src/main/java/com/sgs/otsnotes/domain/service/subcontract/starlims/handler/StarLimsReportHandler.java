package com.sgs.otsnotes.domain.service.subcontract.starlims.handler;

import org.springframework.core.Ordered;

import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.otsnotes.domain.service.subcontract.starlims.annotation.StarLimsReportProcessor;
import com.sgs.otsnotes.domain.service.subcontract.starlims.context.StarLimsReportContext;

/**
 * @Description StarLims报告处理器接口
 * 所有的处理器必须实现此接口
 * <AUTHOR>
 * @Date 2024-03-21
 */
public interface StarLimsReportHandler extends Ordered {
    
    /**
     * 处理报告接收
     * @param context 上下文
     * @return 处理结果，true表示处理成功，继续执行下一个处理器，false表示处理失败，终止执行
     */
    boolean handle(StarLimsReportContext context);
    
    /**
     * 是否支持处理该上下文
     * @param context 上下文
     * @return true表示支持，false表示不支持
     */
    default boolean supports(StarLimsReportContext context) {
        StarLimsReportProcessor annotation = this.getClass().getAnnotation(StarLimsReportProcessor.class);
        if (annotation == null) {
            return false;
        }
        
        // 检查产品线支持
        // 1. 如果productLines为空，则支持所有产品线
        // 2. 如果productLines不为空:
        //    - 如果包含ProductLineType.ALL，则支持所有产品线
        //    - 否则只支持指定的产品线
        ProductLineType[] supportedLines = annotation.productLines();
        if (supportedLines.length > 0) {
            // 首先检查是否支持所有产品线
            for (ProductLineType productLine : supportedLines) {
                if (productLine == ProductLineType.ALL) {
                    return true;
                }
            }
            
            // 如果不支持所有产品线，则检查是否支持当前产品线
            boolean match = false;
            for (ProductLineType productLine : supportedLines) {
                if (productLine == context.getProductLineType()) {
                    match = true;
                    break;
                }
            }
            if (!match) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取处理器优先级，数字越小优先级越高
     * 默认从注解中获取优先级
     * @return 优先级
     */
    @Override
    default int getOrder() {
        StarLimsReportProcessor annotation = this.getClass().getAnnotation(StarLimsReportProcessor.class);
        return annotation != null ? annotation.order() : Integer.MAX_VALUE;
    }
    
    /**
     * 获取处理器类型
     * @return 处理器类型
     */
    default StarLimsReportProcessor.ProcessorType getType() {
        StarLimsReportProcessor annotation = this.getClass().getAnnotation(StarLimsReportProcessor.class);
        return annotation != null ? annotation.type() : StarLimsReportProcessor.ProcessorType.COMMON;
    }
    
    /**
     * 获取处理器分组
     * @return 处理器分组
     */
    default String getGroup() {
        StarLimsReportProcessor annotation = this.getClass().getAnnotation(StarLimsReportProcessor.class);
        return annotation != null ? annotation.group() : "";
    }

    /**
     * 获取处理器分组
     * @return 处理器分组
     */
    default String getHandlerName() {
        return this.getClass().getSimpleName();
    }
} 