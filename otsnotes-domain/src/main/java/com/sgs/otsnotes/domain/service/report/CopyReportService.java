package com.sgs.otsnotes.domain.service.report;

import com.beust.jcommander.internal.Sets;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.id.TypeId;
import com.sgs.framework.model.enums.EventTypeEnum;
import com.sgs.framework.model.enums.OrderCopyType;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.ReportTestMatrixMergeMode;
import com.sgs.grus.bizlog.common.BizLog;
import com.sgs.grus.bizlog.common.BizLogHelper;
import com.sgs.otsnotes.core.annotation.AccessRule;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.config.SysConstants;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.enums.PolicyType;
import com.sgs.otsnotes.core.info.CopyCallbackInfo;
import com.sgs.otsnotes.core.kafka.KafkaProducer;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.core.util.ListHelper;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.SubContractService;
import com.sgs.otsnotes.domain.service.copy.CopyFactory;
import com.sgs.otsnotes.domain.service.reportdata.copy.ReportDataReWorkCopyService;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractExternalRelationshipDTO;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.info.condition.TestConditionMatrixInfo;
import com.sgs.otsnotes.facade.model.info.report.CopyReportFailInfo;
import com.sgs.otsnotes.facade.model.info.report.CopyReportRelInfo;
import com.sgs.otsnotes.facade.model.info.testline.ReferSampleMatrixInfo;
import com.sgs.otsnotes.facade.model.info.testline.TestLineAnalyteInfo;
import com.sgs.otsnotes.facade.model.kafka.GeneralMessage;
import com.sgs.otsnotes.facade.model.kafka.KafkaTopicConsts;
import com.sgs.otsnotes.facade.model.kafka.SyncReportStatusMessage;
import com.sgs.otsnotes.facade.model.kafka.SyncStatusMessage;
import com.sgs.otsnotes.facade.model.ordercopy.SysCopyInfo;
import com.sgs.otsnotes.facade.model.po.SubcontractRelInfoPO;
import com.sgs.otsnotes.facade.model.req.SubcontractRelReq;
import com.sgs.otsnotes.facade.model.req.order.CopySampleInfoReq;
import com.sgs.otsnotes.facade.model.req.report.*;
import com.sgs.otsnotes.facade.model.req.subcontract.ReviseSubReportReq;
import com.sgs.otsnotes.facade.model.rsp.CheckReportReworkRsp;
import com.sgs.otsnotes.facade.model.rsp.CopySampleInfoRsp;
import com.sgs.otsnotes.facade.model.rsp.CurrentCopySampleInfoRsp;
import com.sgs.otsnotes.facade.model.rsp.sample.TestSampleSimplifyInfo;
import com.sgs.otsnotes.infra.repository.pp.impl.PpRepositoryImpl;
import com.sgs.otsnotes.integration.*;
import com.sgs.otsnotes.integration.req.ReportDataCancelReq;
import com.sgs.preorder.facade.model.common.BaseResponse;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.enums.CustomerType;
import com.sgs.preorder.facade.model.enums.OrderStatus;
import com.sgs.preorder.facade.model.info.ReportInfo;
import com.sgs.preorder.facade.model.req.SysStatusReq;
import com.sgs.preorder.facade.model.req.order.OrderStatusReq;
import com.sgs.preorder.facade.model.req.report.GetReportNoReq;
import com.sgs.preorder.facade.model.req.report.ReworkReportExternalNoReq;
import com.sgs.preorder.facade.model.rsp.customer.CustomerSimplifyInfoRsp;
import com.sgs.preorder.facade.model.rsp.order.OrderSimplifyInfoRsp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sgs.otsnotes.facade.model.enums.PreOrderStatus.*;
import static com.sgs.otsnotes.facade.model.enums.SampleType.MixSample;


@Service
public class CopyReportService {
    private final Logger logger = LoggerFactory.getLogger(CopyReportService.class);
    @Autowired
    private OrderSubcontractRelMapper subcontractRelMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private TestSampleMapper testSampleMapper;
    @Autowired
    private SampleExtMapper sampleExtMapper;
    @Autowired
    private ReferDataRelationshipExtMapper referDataRelationshipExtMapper;
    @Autowired
    private PPTestLineRelMapper ppTestLineRelMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private OrderReportClient reportClient;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private LimitMapper limitMapper;
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private TestLineInstanceExtMapper testLineInstanceExtMapper;
    @Autowired
    private SubContractExtMapper subContractExtMapper;
    @Autowired
    private ConclusionInfoExtMapper conclusionInfoExtMapper;
    @Autowired
    private CopyFactory copyFactory;
    @Autowired
    private ReportReworkApplyRecordsInfoExtMapper reportReworkApplyRecordsInfoExtMapper;
    @Autowired
    private ReportReworkApproverRecordsInfoExtMapper reportReworkApproverRecordsInfoExtMapper;
    @Autowired
    private KafkaProducer kafkaProducer;
    @Autowired
    private StatusClient statusClient;

    @Autowired
    private ReportMatrixRelMapper reportMatrixRelMapper;
    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private QrCodeClient qrCodeClient;
    @Autowired
    private TestDataClient testDataClient;

    @Autowired
    private ExtSystemClient extSystemClient;
    @Autowired
    private OrderAttachmentClient orderAttachmentClient;
    @Autowired
    private ReportReworkBusiness reportReworkBusiness;
    @Resource
    private SubContractService subContractService;

    @Autowired
    private PpRepositoryImpl ppRepository;

    @Autowired
    private ReportReworkRecordService reportReworkRecordService;

    @Autowired
    private ReportDataReWorkCopyService reportDataDealService;
    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult<CopySampleInfoRsp> searchOrderSampleInfo(CopySampleInfoReq reqObject) {
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(false);
        if (reqObject == null || StringUtils.isEmpty(reqObject.getOrderNo())) {
            rspResult.setMsg("PLEASE CHECK DATA");
            return rspResult;
        }
        String orderNo = reqObject.getOrderNo();
        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfo(orderNo);
        if (orderInfo == null) {
            rspResult.setMsg("PLEASE CHECK DATA");
            return rspResult;
        }
        CopySampleInfoRsp copySampleInfoRsp = new CopySampleInfoRsp();
        copySampleInfoRsp.setOrderNo(orderNo);
        copySampleInfoRsp.setOrderId(orderInfo.getID());

        List<ApplicableSampleInfo> applicableSamples = testSampleMapper.getApplicableSample(orderNo);

        List<SampleGroupInfo> sampleGroupInfo = testSampleMapper.getSampleGroupInfo(orderNo);

        // 设置原样
        for (ApplicableSampleInfo applicableSampleInfo : applicableSamples) {
            this.findParentSample(sampleGroupInfo, applicableSampleInfo);
        }

//        // 设置share样
//        // 判断样品是否为share样   share样 的原样 不能做CopyReport
//        Set<String> sampleShareSet = Sets.newHashSet();
//        sampleGroupInfo.forEach(sampleGroup -> {
//            if (!SampleType.check(sampleGroup.getSampleType(), ShareSample)) {
//                return;
//            }
//            if (StringUtils.isEmpty(sampleGroup.getSampleParentID()) || StringUtils.isEmpty(sampleGroup.getSampleGroupID())) {
//                return;
//            }
//            if (StringUtils.equalsIgnoreCase(sampleGroup.getSampleParentID(), sampleGroup.getSampleGroupID())) {
//                return;
//            }
//            if (sampleShareSet.contains(sampleGroup.getSampleGroupID())) {
//                return;
//            }
//            sampleShareSet.add(sampleGroup.getSampleGroupID());
//        });
//        // 不显示share样的原样
//        List<ApplicableSampleInfo> collect = applicableSamples.stream()
//                .filter(sampleInfo -> !sampleShareSet.contains(sampleInfo.getSampleId()))
//                .collect(Collectors.toList());

        copySampleInfoRsp.setApplicableSamples(applicableSamples);
        rspResult.setData(copySampleInfoRsp);
        rspResult.setSuccess(true);
        return rspResult;

    }

    /**
     *
     * @param sampleGroupInfos
     * @param applicableSample
     */
    private void findParentSample(List<SampleGroupInfo> sampleGroupInfos, ApplicableSampleInfo applicableSample) {
        if (CollectionUtils.isEmpty(sampleGroupInfos) || applicableSample == null) {
            return;
        }
//        if (SampleType.check(applicableSamples.getSampleType(), MixSample)) {
//            return;
//        }
        if (CollectionUtils.isNotEmpty(applicableSample.getOriginalSamples())) {
            return;
        }

        List<SampleGroupInfo> sampleGroups = sampleGroupInfos.stream()
                .filter(sampleGroup -> StringUtils.equalsIgnoreCase(sampleGroup.getSampleId(), applicableSample.getSampleParentID()) &&
                        SampleType.equals(sampleGroup.getSampleType(), applicableSample.getSampleType()))
                .collect(Collectors.toList());

        Set<String> originalSamples = Sets.newHashSet();
        if (SampleType.check(applicableSample.getSampleType(), MixSample)) {
            sampleGroups = sampleGroupInfos.stream()
                    .filter(sampleGroup -> StringUtils.equalsIgnoreCase(sampleGroup.getSampleId(), applicableSample.getSampleId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sampleGroups)) {
                return;
            }
            for (SampleGroupInfo sampleGr : sampleGroups) {
                Set<String> originalSamplesMix = applicableSample.getOriginalSamples();
                List<SampleGroupInfo> collect = sampleGroupInfos.stream()
                        .filter(sampleGroup -> StringUtils.equalsIgnoreCase(sampleGroup.getSampleId(), sampleGr.getSampleGroupID()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(collect)) {
                    return;
                }
                if (CollectionUtils.isEmpty(originalSamplesMix)) {
                    originalSamplesMix = Sets.newHashSet();
                }
                if (originalSamplesMix.contains(sampleGr.getSampleParentID())) {
                    continue;
                }

                for (SampleGroupInfo sample : collect) {
                    if (StringUtils.isNotEmpty(sample.getSampleParentID())) {
                        // TODO
                        this.findParentSample(collect, applicableSample);
                        continue;
                    }
                    originalSamplesMix.add(sampleGr.getSampleGroupID());
                    applicableSample.setOriginalSamples(originalSamplesMix);
                    continue;

                }

            }
            return;
        }

        if (CollectionUtils.isEmpty(sampleGroups)) {
            originalSamples.add(applicableSample.getSampleId());
            applicableSample.setOriginalSamples(originalSamples);
            return;
        }
        for (SampleGroupInfo sampleGr : sampleGroups) {
            if (StringUtils.isEmpty(sampleGr.getSampleParentID())) {
                originalSamples.add(sampleGr.getSampleId());
                applicableSample.setOriginalSamples(originalSamples);
                return;
            }

            List<SampleGroupInfo> collect = sampleGroups.stream()
                    .filter(sampleGroup -> StringUtils.equalsIgnoreCase(sampleGroup.getSampleParentID(), applicableSample.getSampleParentID()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                originalSamples.add(sampleGr.getSampleParentID());
                applicableSample.setOriginalSamples(originalSamples);
                return;
            }
            this.findParentSample(collect, applicableSample);
        }
        return;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getCurrentCopySampleInfo(CopySampleInfoReq reqObject) {
        CustomResult rspResult = new CustomResult();
        CurrentCopySampleInfoRsp currentCopySampleInfoRsp = new CurrentCopySampleInfoRsp();
        rspResult.setSuccess(false);
        if (reqObject == null || StringUtils.isEmpty(reqObject.getOrderNo())) {
            rspResult.setMsg("PLEASE CHECK DATA");
            return rspResult;
        }
        String orderNo = reqObject.getOrderNo();

        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfo(orderNo);
        if (orderInfo == null) {
            rspResult.setMsg("PLEASE CHECK DATA");
            return rspResult;
        }
        // 获取订单信息
        OrderInfoDto orderInfoPre = orderClient.getOrderInfoByOrderNo(orderNo);
        // 获取operationType
        Integer operationType = orderInfoPre.getOperationType();
        // 获取订单状态
        Integer orderStatus = orderInfoPre.getOrderStatus();
        // 获取报告状态
        ReportInfoPO reportByOrderNo = reportMapper.getReportByOrderNo(orderNo);
        if (reportByOrderNo == null) {
            rspResult.setMsg("PLEASE CHECK DATA");
            return rspResult;
        }
        Integer reportStatus = reportByOrderNo.getReportStatus();

        String orderId = orderInfo.getID();
        List<CopyReportSampleInfo> currentCopySampleInfo = testSampleMapper.getCurrentCopySampleInfo(orderId);

//        // 判断样品是否为share样   share样 的原样 不能做CopyReport
//        Set<String> sampleShareSet = Sets.newHashSet();
//        List<SampleGroupInfo> sampleGroupInfo = testSampleMapper.getSampleGroupInfo(orderNo);
//        sampleGroupInfo.forEach(sampleGroup -> {
//            if (!SampleType.check(sampleGroup.getSampleType(), ShareSample)) {
//                return;
//            }
//            if (StringUtils.isEmpty(sampleGroup.getSampleParentID()) || StringUtils.isEmpty(sampleGroup.getSampleGroupID())) {
//                return;
//            }
//            if (StringUtils.equalsIgnoreCase(sampleGroup.getSampleParentID(), sampleGroup.getSampleGroupID())) {
//                return;
//            }
//            if (sampleShareSet.contains(sampleGroup.getSampleGroupID())) {
//                return;
//            }
//            sampleShareSet.add(sampleGroup.getSampleGroupID());
//
//        });
//
//        currentCopySampleInfo.forEach(sampleInfo -> {
//            if (sampleShareSet.contains(sampleInfo.getSampleId())) {
//                sampleInfo.setShareOriginalSample(true);
//            } else {
//                sampleInfo.setShareOriginalSample(false);
//            }
//        });


        currentCopySampleInfoRsp.setOrderStatus(orderStatus);
        currentCopySampleInfoRsp.setReportStatus(reportStatus);
        currentCopySampleInfoRsp.setOperationType(operationType);
        currentCopySampleInfoRsp.setCurrentCopySampleInfo(currentCopySampleInfo);

        rspResult.setData(currentCopySampleInfoRsp);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult saveCopyReport(SaveCopyReportReq reqObject){
        CustomResult<CopyReportRelInfo> rspResult = this.getCopyReportInfo(EventType.ReferData, reqObject);
        if (!rspResult.isSuccess()){
            rspResult.setData(null);
            return rspResult;
        }
        CopyReportRelInfo rspObject = rspResult.getData();
        List<Long> delRelIds = rspObject.getDelRelIds();
        List<SubcontractRelInfoPO> rels = rspObject.getRels();

        if (delRelIds.isEmpty() && rels.isEmpty()){
            rspResult.setSuccess(false);
            rspResult.setMsg("未找到对应的Matrix.");
            return rspResult;
        }

        rspResult.setSuccess(transactionTemplate.execute((trans) -> {
            if (!delRelIds.isEmpty()){
                subcontractRelMapper.batchDelete(delRelIds);
            }
            if (!rels.isEmpty()){
                subcontractRelMapper.batchInsert(rels);
            }
            return true;
        }));
        return rspResult;
    }

    /**
     *
     * @param eventType
     * @param reqObject
     * @return
     */
    public CustomResult<CopyReportRelInfo> getCopyReportInfo(EventType eventType, SaveCopyReportReq reqObject){
        CustomResult<CopyReportRelInfo> rspResult = new CustomResult();

        List<CopyReportSampleInfo> referSamples = reqObject.getSamples();
        if (referSamples == null || referSamples.isEmpty()){
            rspResult.setMsg("请选择对应的Source Sample.");
            return rspResult;
        }
        CopyReportSampleInfo sample = referSamples.get(0);
        if (StringUtils.isBlank(sample.getOrderNo())){
            rspResult.setMsg("Refer OrderNo不能为空.");
            return rspResult;
        }
        List<String> relSampleIds = referDataRelationshipExtMapper.getReferDataRelSampleIds(sample.getOrderNo());
        if (!relSampleIds.isEmpty()){
            rspResult.setMsg("该订单已被Refer Data.");
            return rspResult;
        }
        GeneralOrderInstanceInfoPO referOrder = orderMapper.getOrderInfo(sample.getOrderNo());

        SubcontractRelReq reqRel = new SubcontractRelReq();
        reqRel.setOrderId(referOrder.getID());
        reqRel.setRelTypes(Lists.newArrayList(TableType.Sample.getTableId()));

        Map<String, SubcontractRelInfoPO> relIds = Maps.newHashMap();
        subcontractRelMapper.getSubcontractRelList(reqRel).forEach(rel->{
            relIds.put(rel.getSubRelId(), rel);
        });
        CopyReportRelInfo rspObject = new CopyReportRelInfo();
        Set<String> sourceSampleIds = Sets.newHashSet();
        List<Long> delRelIds = Lists.newArrayList();
        Map<String, CopyReportSampleInfo> referSampleMaps = Maps.newHashMap();
        Map<String, String> sourceOrderMaps = Maps.newHashMap();

        Set<String> referSampleIds = Sets.newHashSet();

        for (CopyReportSampleInfo referSample: referSamples) {
            String sampleId = referSample.getSampleId();
            if (StringUtils.isBlank(sampleId)){
                rspResult.setMsg("Refer Sample不能为空.");
                return rspResult;
            }
            SubcontractRelInfoPO rel = relIds.get(sampleId);
            // 当EventType为2时，则校验当前的Source SampleId是否变更
            if (rel != null){
                boolean isCopyReport = false;
                switch (EventType.findType(rel.getEventType())){
                    case ReferData:
                        if (!StringUtils.equalsIgnoreCase(rel.getOriginalRelId(), referSample.getSourceSampleId())){
                            delRelIds.add(rel.getId());
                        }
                        break;
                    case CopyReport:
                        if (!StringUtils.equalsIgnoreCase(rel.getOriginalRelId(), referSample.getSourceSampleId())){
                            rspResult.setMsg(String.format("Sample No(%s)已Copy Report不能再变更.", referSample.getSampleNo()));
                            return rspResult;
                        }
                        isCopyReport = true;
                        break;
                }
                if (isCopyReport){
                    rspObject.setCopyReport(true);
                    continue;
                }
            }
            if (StringUtils.isBlank(referSample.getSourceOrderNo()) || StringUtils.isBlank(referSample.getSourceSampleId())){
                // TODO 都为空则忽略
                continue;
            }
            if (!sourceOrderMaps.containsKey(referSample.getSourceOrderNo())){
                GeneralOrderInstanceInfoPO sourceOrder = orderMapper.getOrderInfo(referSample.getSourceOrderNo());
                if (sourceOrder == null){
                    rspResult.setMsg("Source Order不存在.");
                    return rspResult;
                }
                sourceOrderMaps.put(sourceOrder.getOrderNo(), sourceOrder.getID());
            }
            referSample.setSourceOrderId(sourceOrderMaps.get(referSample.getSourceOrderNo()));

            if (StringUtils.equalsIgnoreCase(referSample.getOrderNo(), referSample.getSourceOrderNo())){
                rspResult.setMsg("Refer Order不能与Source Order一致.");
                return rspResult;
            }
            if (StringUtils.equalsIgnoreCase(referSample.getSampleId(), referSample.getSourceSampleId())){
                rspResult.setMsg("Refer Sample不能与Source Sample一致.");
                return rspResult;
            }
            // 校验Source Sample是否有被重复选择
            String sourceSampleId = referSample.getSourceSampleId();
            if (sourceSampleIds.contains(sourceSampleId)){
                rspResult.setMsg(String.format("Sample No(%s)不能被重复选择.", referSample.getSampleNo()));
                return rspResult;
            }
            sourceSampleIds.add(sourceSampleId);
            referSampleMaps.put(referSample.getSampleId(), referSample);
            referSampleIds.addAll(Lists.newArrayList(referSample.getSampleId(), sourceSampleId));
        }
        List<ReferSampleMatrixInfo> referSampleMatrixs = Lists.newArrayList();
        if (!referSampleIds.isEmpty()){
            // DIG-6840 copy Test 也需要 关联 refer
            referSampleMatrixs = ppRepository.getReferSampleMatrixInfoList(referSampleIds);
        }

        Map<String, TestSampleSimplifyInfo> testSampleMaps = Maps.newHashMap();
        sampleExtMapper.getSampleInfoListByOrderNo(sample.getOrderNo()).forEach(testSample->{
            testSampleMaps.put(testSample.getSampleId(), testSample);
        });
        List<SubcontractRelInfoPO> rels = Lists.newArrayList();
        UserInfo localUser = UserHelper.getLocalUser();
        Map<ReferSampleMatrixInfo, ReferSampleMatrixInfo> oldMatrixMaps = Maps.newHashMap();

        for (Map.Entry<String, CopyReportSampleInfo> entry: referSampleMaps.entrySet()) {
            CopyReportSampleInfo referSample = entry.getValue();
            if (referSample == null){
                continue;
            }
            if (EventType.check(referSample.getEventType(), EventType.CopyReport)){
                continue;
            }
            List<ReferSampleMatrixInfo> referMatrixs = ListHelper.filter(referSampleMatrixs, rm -> StringUtils.equalsIgnoreCase(rm.getTestSampleId(), referSample.getSampleId()));
            int referMatrixCount = referMatrixs.size();
            if (referMatrixCount <= 0){
                continue;
            }
            List<ReferSampleMatrixInfo> sourceMatrixs = ListHelper.filter(referSampleMatrixs, rm -> StringUtils.equalsIgnoreCase(rm.getTestSampleId(), referSample.getSourceSampleId()));
            if (sourceMatrixs.size() <= 0){
                rspResult.setMsg(String.format("Refer Sample(%s)与Source Sample Matrix不一致.", referSample.getSampleNo()));
                return rspResult;
            }
            referMatrixs.removeIf(rm->{
                String referMatrixKey = String.format("%s_%s_%s_%s", NumberUtil.toInt(rm.getPpNo()), rm.getTestLineId(), rm.getCitationId(), rm.getMatrixGroupId());
                List<TestConditionMatrixInfo> testConditions = rm.getTestConditions() == null ? Lists.newArrayList() : rm.getTestConditions();
                int smCount = sourceMatrixs.size();
                sourceMatrixs.removeIf(sm->{
                    String sourceMatrixKey = String.format("%s_%s_%s_%s", NumberUtil.toInt(sm.getPpNo()), sm.getTestLineId(), sm.getCitationId(), sm.getMatrixGroupId());
                    if (StringUtils.equalsIgnoreCase(referMatrixKey, sourceMatrixKey)){
                        if (testConditions.containsAll(sm.getTestConditions())){
                            // TODO 会有多条数据
                            oldMatrixMaps.put(sm, rm);
                            return true;
                        }
                        return false;
                    }
                    return false;
                });
                return smCount != sourceMatrixs.size();
            });
            if (referMatrixCount == referMatrixs.size()){
                rspResult.setMsg(String.format("Refer Sample(%s)与Source Sample Matrix不一致.", referSample.getSampleNo()));
                return rspResult;
            }
            if (!this.checkParentSocureOrder(testSampleMaps, referSampleMaps, testSampleMaps.get(referSample.getSampleId()), referSample.getSourceOrderNo())){
                TestSampleSimplifyInfo testSample = testSampleMaps.get(referSample.getSampleId());
                if (testSample == null){
                    rspResult.setMsg(String.format("未找到样品(%s)对应的基本信息.", referSample.getSampleNo()));
                    return rspResult;
                }
                if (SampleType.check(testSample.getSampleType(), SampleType.MixSample)){
                    rspResult.setMsg(String.format("Mix样(%s)要与原始样要来自相同订单.", testSample.getSampleNo()));
                    return rspResult;
                }
                rspResult.setMsg(String.format("子样(%s)要与父样的订单相同.", testSample.getSampleNo()));
                return rspResult;
            }
            SubcontractRelInfoPO rel = relIds.get(referSample.getSampleId());
            if (rel == null){
                rel = new SubcontractRelInfoPO();
            }
            rel.setOriginalOrderId(referSample.getSourceOrderId());
            rel.setSubOrderId(referOrder.getID());
            rel.setOriginalRelId(referSample.getSourceSampleId());
            rel.setSubRelId(referSample.getSampleId());
            rel.setRelType(TableType.Sample.getTableId());
            rel.setEventType(eventType.getType());
            rel.setStatus(1);
            rel.setCreatedBy(localUser.getRegionAccount());
            rel.setCreatedDate(DateUtils.getNow());
            rel.setModifiedBy(localUser.getRegionAccount());
            rel.setModifiedDate(DateUtils.getNow());

            rels.add(rel);
        }

        rspObject.setOrderNo(sample.getOrderNo());
        rspObject.setOldMatrixMaps(oldMatrixMaps);
        rspObject.setTestSampleMaps(testSampleMaps);
        rspObject.setDelRelIds(delRelIds);
        rspObject.setRels(rels);

        rspResult.setData(rspObject);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult copyReport(SaveCopyReportReq reqObject){

        CustomResult<CopyReportRelInfo> rspResult = this.getCopyReportInfo(EventType.CopyReport, reqObject);
        CopyReportRelInfo rspObject = rspResult.getData();
        if (!rspResult.isSuccess()){
            return rspResult;
        }
        rspResult = new CustomResult();

        // TODO 临时处理  后续改为 入参
        ProductLineContextHolder.setProductLineCode(ProductLineType.SL.getProductLineAbbr(), ProductLineType.SL.getProductLineAbbr());


        Map<ReferSampleMatrixInfo, ReferSampleMatrixInfo> oldMatrixMaps = rspObject.getOldMatrixMaps();
        List<String> referTestLineInstanceIds = Lists.newArrayList();
        List<String> referTestSampleIds = Lists.newArrayList();

        oldMatrixMaps.values().forEach(matrix->{
            if (!referTestSampleIds.contains(matrix.getTestSampleId())){
                referTestSampleIds.add(matrix.getTestSampleId());
            }
            if (!referTestLineInstanceIds.contains(matrix.getTestLineInstanceId())){
                referTestLineInstanceIds.add(matrix.getTestLineInstanceId());
            }
        });
        if (referTestSampleIds == null || referTestSampleIds.isEmpty()){
            String formatMsg = rspObject.isCopyReport() ? "该Refer 订单(%s)已Copy Report." : "请选择Refer 订单(%s)对应的Source Sample.";
            rspResult.setMsg(String.format(formatMsg, rspObject.getOrderNo()));
            return rspResult;
        }
        List<CopyReportSampleInfo> referSamples = reqObject.getSamples();

        CustomResult<OrderSimplifyInfoRsp> referResult = orderClient.getOrderSimplifyInfo(rspObject.getOrderNo());
        OrderSimplifyInfoRsp order = referResult.getData();
        if (order == null){
            rspResult.setMsg(String.format("未找到Refer 订单(%s)信息.", rspObject.getOrderNo()));
            return rspResult;
        }
        // 校验订单是否为内部分包单（PreOrder表b_general_order.operationType为5），如果是则不Copy
        if (NumberUtil.equals(order.getOperationType(), 5)){
            rspResult.setMsg(String.format("Refer 订单(%s)为内部分包单，则不允许Copy.", rspObject.getOrderNo()));
            return rspResult;
        }
        // OrderStatus包含：New、Confirmed、Testing
        if (!PreOrderStatus.checkStatus(order.getOrderStatus(), New, Confirmed, Testing)){
            rspResult.setMsg(String.format("Refer 订单(%s)状态必需为(New、Confirmed、Testing).", rspObject.getOrderNo()));
            return rspResult;
        }

        // ReportStatus包含：New、Draft、Combined
        ReportInfoPO referReport = reportMapper.getReportByOrderNo(rspObject.getOrderNo());
        if (referReport == null){
            rspResult.setMsg(String.format("未找到Refer 订单(%s)对应的Report信息.", rspObject.getOrderNo()));
            return rspResult;
        }
        if (!ReportStatus.check(referReport.getReportStatus(), ReportStatus.New,ReportStatus.Draft, ReportStatus.Combined)){
            rspResult.setMsg(String.format("Refer 订单(%s)的Report状态必需为(New、Draft、Combined).", rspObject.getOrderNo()));
            return rspResult;
        }

        /**
         * 1、校验Refer 订单的Sample是否在tb_limit_group_instance表里，Check字段TestSampleId，存在则忽略该条（提示用户）
         * 2、校验Refer 订单的TestLine是否在tb_limit_instance表里，Check字段TestLineInstanceID，存在则忽略该条（提示用户）
         */
        List<String> testSampleIds = limitMapper.getLimitTestSampleIds(referTestSampleIds);
        List<String> testLineInstanceIds = conclusionInfoExtMapper.getConclusionTestLineInstanceIds(referReport.getID(), referTestLineInstanceIds);
        Map<String, CopyReportFailInfo> failMaps = Maps.newHashMap();
        Map<String, TestSampleSimplifyInfo> testSampleMaps = rspObject.getTestSampleMaps();

        oldMatrixMaps.keySet().removeIf(sourceMatrix ->{
            ReferSampleMatrixInfo referMatrix = oldMatrixMaps.get(sourceMatrix);
            if (referMatrix == null){
                return false;
            }
            if (sourceMatrix.getAnalytes() == null){
                sourceMatrix.setAnalytes(Lists.newArrayList());
            }
            if (referMatrix.getAnalytes() == null){
                referMatrix.setAnalytes(Lists.newArrayList());
            }
            if (!referMatrix.getAnalytes().containsAll(sourceMatrix.getAnalytes())){
                this.doCopyReportFailInfo(CopyReportFailType.Analyte, referMatrix, testSampleMaps, failMaps);
                return true;
            }
            if (testSampleIds.contains(referMatrix.getTestSampleId())){
                this.doCopyReportFailInfo(CopyReportFailType.Limit, referMatrix, testSampleMaps, failMaps);
                return true;
            }
            if (testLineInstanceIds.contains(referMatrix.getTestLineInstanceId())){
                this.doCopyReportFailInfo(CopyReportFailType.Conclusion, referMatrix, testSampleMaps, failMaps);
                return true;
            }
            return false;
        });

        if (oldMatrixMaps.isEmpty()){
            rspResult.setMsg(this.getCopyReportFailInfo(failMaps));
            return rspResult;
        }

        // TestLineInstanceId, 新老
        Map<String, Boolean> testLineInstanceIdMaps = Maps.newHashMap();
        for (Map.Entry<ReferSampleMatrixInfo, ReferSampleMatrixInfo> rsm: oldMatrixMaps.entrySet()) {
            // Key(Source)，Value(Refer)
            testLineInstanceIdMaps.put(rsm.getKey().getTestLineInstanceId(), false);
            testLineInstanceIdMaps.put(rsm.getValue().getTestLineInstanceId(), true);
        }

        List<TestLineInstancePO> testLineInfos = testLineMapper.getBaseTestLineByIds(Lists.newArrayList(testLineInstanceIdMaps.keySet()));
        Set<String> testLineCompleteIdSets = Sets.newHashSet();
        List<String> generalOrderIds = testLineInfos.stream().map(TestLineInstancePO::getGeneralOrderInstanceID).distinct().collect(Collectors.toList());
        List<TestLineInstancePO> repeatTestLineByOrderId = testLineMapper.getRepeatTestLineByOrderId(generalOrderIds);
        // DIG-7123 找到多个Complete的同一个TestLine
        Set<Integer> testLineCompleteRepeatSets = Sets.newHashSet();
        repeatTestLineByOrderId.stream().forEach(tl -> {
            if (!TestLineStatus.check(tl.getTestLineStatus(), TestLineStatus.Completed)) {
                return;
            }
            String key = String.format("%s_%s_%s", tl.getTestLineID(), tl.getTestLineVersionID(), tl.getCitationVersionId());
            if (!testLineCompleteIdSets.contains(key)) {
                testLineCompleteIdSets.add(key);
                return;
            }
            testLineCompleteRepeatSets.add(tl.getTestLineID());
        });
        // 获取TestLine状态，Refer、Socure
        for (TestLineInstancePO testLine: testLineInfos) {// 新(refer)
            if (TestLineStatus.check(testLine.getTestLineStatus(), TestLineStatus.Completed)) {
                oldMatrixMaps.keySet().removeIf(sourceMatrix -> {
                    if (testLineCompleteRepeatSets.contains(sourceMatrix.getTestLineId())){
                        this.doCopyReportFailInfo(CopyReportFailType.SourceTLRepeat, sourceMatrix, testSampleMaps, failMaps, testLine.getTestLineStatus());
                        return true;
                    }
                    return false;
                });
            }

            if (testLineInstanceIdMaps.get(testLine.getID())) {
                if (!TestLineStatus.check(testLine.getTestLineStatus(), TestLineStatus.Typing)) {
                    oldMatrixMaps.values().removeIf(referMatrix -> {
                        if (StringUtils.equalsIgnoreCase(referMatrix.getTestLineInstanceId(), testLine.getID())){
                            this.doCopyReportFailInfo(CopyReportFailType.ReferTLStatus, referMatrix, testSampleMaps, failMaps, testLine.getTestLineStatus());
                            return true;
                        }
                        return false;
                    });
                }
                continue;
            }
            if (!TestLineStatus.check(testLine.getTestLineStatus(), TestLineStatus.Completed)) {
                oldMatrixMaps.keySet().removeIf(sourceMatrix -> {
                    if (StringUtils.equalsIgnoreCase(sourceMatrix.getTestLineInstanceId(), testLine.getID())){
                        this.doCopyReportFailInfo(CopyReportFailType.SourceTLStatus, sourceMatrix, testSampleMaps, failMaps, testLine.getTestLineStatus());
                        return true;
                    }
                    return false;
                });
            }
        }

        if (oldMatrixMaps.isEmpty()){
            rspResult.setMsg(this.getCopyReportFailInfo(failMaps));
            return rspResult;
        }

        // ConclusionMode需一致（preorder.tb_general_order_instance.ConclusionMode）
        GeneralOrderInstanceInfoPO referOrder = orderMapper.getOrderInfo(rspObject.getOrderNo());
        if (referOrder == null){
            rspResult.setMsg("未找到Refer订单信息.");
            return rspResult;
        }
        int referConclusionMode = NumberUtil.doZeroDefVal(referOrder.getConclusionMode(), ConclusionMode.MATRIX.getId());

        // Buyer需一致（preorder.tb_customer_instance.BuyerGroup，且CustomerUsage为buyer）
        CustomResult<CustomerSimplifyInfoRsp> customerResult = customerClient.getCustomerSimplifyInfo(rspObject.getOrderNo(), CustomerType.Buyer);
        CustomerSimplifyInfoRsp referCustomer = customerResult.getData();
        if (referCustomer == null){
            referCustomer = new CustomerSimplifyInfoRsp();
        }
        // Report Language需一致（preorder.tb_test_request.ReportLanguage）
        Integer referLanguageId = reportClient.getReportLangByOrderNo(rspObject.getOrderNo());

        Set<String> sourceOrderMaps = Sets.newHashSet();
        Map<String, SysCopyInfo> copyReportMaps = Maps.newHashMap();

        for (CopyReportSampleInfo referSample: referSamples) {
            if (StringUtils.isBlank(referSample.getSourceOrderNo()) || StringUtils.isBlank(referSample.getSourceSampleId())){
                continue;
            }
            // region 【ConclusionMode需一致】
            if (sourceOrderMaps.contains(referSample.getSourceOrderNo())){
                continue;
            }
            GeneralOrderInstanceInfoPO sourceOrder = orderMapper.getOrderInfo(referSample.getSourceOrderNo());
            if (sourceOrder == null){
                rspResult.setMsg("未找到Refer订单信息.");
                return rspResult;
            }
            int sourceConclusionMode = NumberUtil.doZeroDefVal(sourceOrder.getConclusionMode(), ConclusionMode.MATRIX.getId());
            if (!NumberUtil.equals(referConclusionMode, sourceConclusionMode)){
                rspResult.setMsg(String.format("Source 订单(%s)与Refer订单 Conclusion Mode不一致.", referSample.getSourceOrderNo()));
                return rspResult;
            }
            sourceOrderMaps.add(referSample.getSourceOrderNo());

            // endregion

            // region 【ReportStatus包含：New、Draft、Combined】
            ReportInfoPO sourceReport = reportMapper.getReportByOrderNo(referSample.getSourceOrderNo());
            if (sourceReport == null){
                rspResult.setMsg(String.format("未找到Source 订单(%s)对应的Report信息.", referSample.getSourceOrderNo()));
                return rspResult;
            }
            // DIG-6716 不判断原单report状态
//            if (!ReportStatus.check(sourceReport.getReportStatus(), ReportStatus.Approved)){
//                rspResult.setMsg(String.format("Source 订单(%s)的Report状态必需为(Approved).", referSample.getSourceOrderNo()));
//                return rspResult;
//            }
            // endregion

            // region 【Buyer需一致】
            customerResult = customerClient.getCustomerSimplifyInfo(referSample.getSourceOrderNo(), CustomerType.Buyer);
            CustomerSimplifyInfoRsp sourceCustomer = customerResult.getData();
            if (sourceCustomer == null){
                sourceCustomer = new CustomerSimplifyInfoRsp();
            }
            if (!NumberUtil.equals(referCustomer.getBossNumber(), sourceCustomer.getBossNumber(), true)){
                rspResult.setMsg(String.format("Source 订单(%s)与Refer订单 Buyer不一致.", referSample.getSourceOrderNo()));
                return rspResult;
            }
            // endregion

            // region 【OrderStatus包含：Completed、Closed】
            CustomResult<OrderSimplifyInfoRsp> sourceResult = orderClient.getOrderSimplifyInfo(referSample.getSourceOrderNo());
            OrderSimplifyInfoRsp sourceOrderInfo = sourceResult.getData();
            if (sourceOrderInfo == null){
                rspResult.setMsg(String.format("未找到Source 订单(%s)信息.", referSample.getSourceOrderNo()));
                return rspResult;
            }
            // DIG-6716 不判断原单状态
//            if (!PreOrderStatus.checkStatus(sourceOrderInfo.getOrderStatus(), Completed, Closed)){
//                rspResult.setMsg(String.format("Source 订单(%s)状态必需为(Completed、Closed).", referSample.getSourceOrderNo()));
//                return rspResult;
//            }
            // endregion

            // region 【Report Language需一致】
            Integer sourceLanguageId = reportClient.getReportLangByOrderNo(referSample.getSourceOrderNo());
            if (!NumberUtil.equals(referLanguageId, sourceLanguageId, true)){
                rspResult.setMsg(String.format("Source 订单(%s)与Refer订单 Report Language不一致.", referSample.getSourceOrderNo()));
                return rspResult;
            }
            // endregion

            if (copyReportMaps.containsKey(sourceOrder.getID())){
                continue;
            }
            SysCopyInfo reqCopy = new SysCopyInfo();
            reqCopy.setCopyType(OrderCopyType.CopyReport);
            reqCopy.setOrderId(referOrder.getID());
            reqCopy.setOrderNo(referOrder.getOrderNo());
            reqCopy.setOldOrderId(sourceOrder.getID());
            reqCopy.setOldOrderNo(sourceOrder.getOrderNo());

            reqCopy.setReportId(referReport.getID());
            reqCopy.setReportNo(referReport.getReportNo());
            reqCopy.setOldReportId(sourceReport.getID());
            reqCopy.setOldReportNo(sourceReport.getReportNo());
            copyReportMaps.put(sourceOrder.getID(), reqCopy);
        }
        UserInfo localUser = UserHelper.getLocalUser();

        SubcontractRelReq reqRel = new SubcontractRelReq();
        reqRel.setOrderId(referOrder.getID());
        reqRel.setRelTypes(Lists.newArrayList(TableType.TestLine.getTableId()));

        Map<String, SubcontractRelInfoPO> relIds = Maps.newHashMap();
        subcontractRelMapper.getSubcontractRelList(reqRel).forEach(rel->{
            relIds.put(rel.getSubRelId(), rel);
        });
        List<Long> delRelIds = rspObject.getDelRelIds();
        List<SubcontractRelInfoPO> rels = rspObject.getRels();
        Set<String> sourceOrderIds = Sets.newHashSet();

        for (Map.Entry<ReferSampleMatrixInfo, ReferSampleMatrixInfo> rsm: oldMatrixMaps.entrySet()) {
            ReferSampleMatrixInfo sourceMatrix = rsm.getKey();
            ReferSampleMatrixInfo referMatrix = rsm.getValue();

            SysCopyInfo sourceOrder = copyReportMaps.get(sourceMatrix.getOrderId());
            if (sourceOrder == null){
                /*rspResult.setMsg(String.format("Source 订单(%s)与Refer订单 Report Language不一致.", referSample.getSourceOrderNo()));*/
                return rspResult;
            }
            sourceOrder.getOldTestMatrixIds().put(sourceMatrix.getTestMatrixId(), referMatrix.getTestMatrixId());
            sourceOrder.getOldSampleIds().put(sourceMatrix.getTestSampleId(), referMatrix.getTestSampleId());
            sourceOrder.getOldTestLineInstanceIds().put(sourceMatrix.getTestLineInstanceId(), referMatrix.getTestLineInstanceId());
            sourceOrder.getOldPpBaseIds().put(sourceMatrix.getPpBaseId(), referMatrix.getPpBaseId());
            sourceOrder.getOldPpSampleRelIds().put(sourceMatrix.getPpSampleRelId(), referMatrix.getPpSampleRelId());
            Map<String, String> oldTestAnalyteIds = sourceOrder.getOldTestAnalyteIds();

            List<TestLineAnalyteInfo> referAnalytes = referMatrix.getAnalytes();
            for (TestLineAnalyteInfo sourceAnalyte: sourceMatrix.getAnalytes()) {
                List<TestLineAnalyteInfo> analytes = referAnalytes.stream().filter(analyte -> NumberUtil.equals(analyte.getAnalyteId(), sourceAnalyte.getAnalyteId()) && StringUtils.equalsIgnoreCase(analyte.getReportUnit(), sourceAnalyte.getReportUnit())).collect(Collectors.toList());
                if (analytes == null || analytes.isEmpty()){
                    continue;
                }
                analytes.forEach(analyte->{
                    oldTestAnalyteIds.put(sourceAnalyte.getAnalyteInstanceId(), analyte.getAnalyteInstanceId());
                });
            }

            SubcontractRelInfoPO rel = relIds.get(referMatrix.getTestLineInstanceId());
            if (rel == null){
                rel = new SubcontractRelInfoPO();
            }
            if (relIds.containsKey(referMatrix.getTestLineInstanceId())){
                relIds.remove(referMatrix.getTestLineInstanceId());
            }
            rel.setOriginalOrderId(sourceMatrix.getOrderId());
            rel.setSubOrderId(referMatrix.getOrderId());
            rel.setOriginalRelId(sourceMatrix.getTestLineInstanceId());
            rel.setSubRelId(referMatrix.getTestLineInstanceId());
            rel.setRelType(TableType.TestLine.getTableId());
            rel.setEventType(EventType.CopyReport.getType());
            rel.setStatus(1);
            rel.setCreatedBy(localUser.getRegionAccount());
            rel.setCreatedDate(DateUtils.getNow());
            rel.setModifiedBy(localUser.getRegionAccount());
            rel.setModifiedDate(DateUtils.getNow());

            rels.add(rel);

            sourceOrderIds.add(sourceMatrix.getOrderId());
        }

        if (!relIds.isEmpty()){
            relIds.values().forEach(rel->{
                delRelIds.add(rel.getId());
            });
        }

        List<CopyCallbackInfo> callbacks = Lists.newArrayList();
        copyReportMaps.values().forEach(copyReport->{
            if (!sourceOrderIds.contains(copyReport.getOldOrderId())){
                return;
            }
            CustomResult<List<CopyCallbackInfo>> callResult = copyFactory.doCopyCallback(copyReport);
            if (!callResult.isSuccess()){
                return;
            }
            callbacks.addAll(callResult.getData());
        });

        SysCopyInfo reqCopy = new SysCopyInfo();
        reqCopy.setCopyType(OrderCopyType.CopyReport);
        reqCopy.setOrderId(referOrder.getID());
        reqCopy.setOrderNo(referOrder.getOrderNo());

        rspResult.setSuccess(transactionTemplate.execute((trans) -> {
            // TODO
            if (!callbacks.isEmpty()){
                copyFactory.doCopyTransaction(reqCopy, callbacks);
            }
            if (!delRelIds.isEmpty()){
                subcontractRelMapper.batchDelete(delRelIds);
            }
            if (!rels.isEmpty()){
                subcontractRelMapper.batchInsert(rels);
            }
            return true;
        }));
        rspResult.setMsg(this.getCopyReportFailInfo(failMaps));
        return rspResult;
    }

    /**
     *
     * @param failType
     * @param referMatrix
     * @param testSampleMaps
     * @param failMaps
     */
    private void doCopyReportFailInfo(CopyReportFailType failType, ReferSampleMatrixInfo referMatrix, Map<String, TestSampleSimplifyInfo> testSampleMaps, Map<String, CopyReportFailInfo> failMaps){
        this.doCopyReportFailInfo(failType, referMatrix, testSampleMaps, failMaps, 0);
    }

    /**
     *
     * @param failType
     * @param referMatrix
     * @param testSampleMaps
     * @param failMaps
     */
    private void doCopyReportFailInfo(CopyReportFailType failType, ReferSampleMatrixInfo referMatrix, Map<String, TestSampleSimplifyInfo> testSampleMaps, Map<String, CopyReportFailInfo> failMaps, Integer testLineStatus){
        String failKey = String.format("%s_%s_%s", failType, referMatrix.getTestSampleId(), testLineStatus);

        CopyReportFailInfo fail = null;
        if (!failMaps.containsKey(failKey)){
            fail = new CopyReportFailInfo();
            fail.setFailType(failType);
            fail.setSampleId(referMatrix.getTestSampleId());
            if (testSampleMaps.containsKey(referMatrix.getTestSampleId())){
                fail.setSampleNo(testSampleMaps.get(referMatrix.getTestSampleId()).getSampleNo());
            } else {
                fail.setSampleNo(referMatrix.getSampleNo());
            }
            fail.setTestLineStatus(TestLineStatus.findStatus(testLineStatus));
            failMaps.put(failKey, fail);
        }
        fail = failMaps.get(failKey);
        fail.getTestLineIds().add(referMatrix.getTestLineId());
    }

    /**
     *
     * @param failMaps
     * @return
     */
    private String getCopyReportFailInfo(Map<String, CopyReportFailInfo> failMaps){
        if (failMaps.isEmpty()){
            // TODO
            return "";
        }
        List<String> fails = Lists.newArrayList();
        for (CopyReportFailInfo fail: failMaps.values()) {
            CopyReportFailType failType = fail.getFailType();
            String testLineIds = StringUtils.join(fail.getTestLineIds(), ",");
            switch (failType){
                case Limit:
                case Analyte:
                case Conclusion:
                    fails.add(String.format(failType.getFormat(), fail.getSampleNo(), testLineIds));
                    break;
                case ReferTLStatus:
                case SourceTLStatus:
                case SourceTLRepeat:
                    TestLineStatus testLineStatus = fail.getTestLineStatus();
                    fails.add(String.format(failType.getFormat(), fail.getSampleNo(), testLineIds, testLineStatus.getMessage()));
                    break;
            }
        }
        return StringUtils.join(fails, "\r\n");
    }


    /**
     *
     * @param testSampleMaps
     * @param referSampleMaps
     * @param testSample
     * @param sourceOrderNo
     * @return
     */
    private boolean checkParentSocureOrder(Map<String, TestSampleSimplifyInfo> testSampleMaps, Map<String, CopyReportSampleInfo> referSampleMaps, TestSampleSimplifyInfo testSample, String sourceOrderNo){
        if (testSample == null){
            return false;
        }
        SampleType sampleType = SampleType.findType(testSample.getSampleType());
        if (sampleType == null){
            return false;
        }
        switch (sampleType){
            case OriginalSample:
                CopyReportSampleInfo originalSample = referSampleMaps.get(testSample.getSampleId());
                if (originalSample == null || StringUtils.isBlank(originalSample.getSourceOrderNo())){
                    return true;
                }
                return StringUtils.equalsIgnoreCase(originalSample.getSourceOrderNo(), sourceOrderNo);
            case MixSample:
                List<String> sampleGroupIds = testSample.getSampleGroupIds();
                if (sampleGroupIds == null || sampleGroupIds.isEmpty()){
                    return true;
                }
                for (String sampleGroupId: sampleGroupIds) {
                    CopyReportSampleInfo mixSample = referSampleMaps.get(sampleGroupId);
                    if (mixSample == null){
                        continue;
                    }
                    if (!StringUtils.equalsIgnoreCase(mixSample.getSourceOrderNo(), sourceOrderNo)){
                        return false;
                    }
                }
                return true;
            case ShareSample:
                CopyReportSampleInfo shareSample = referSampleMaps.get(testSample.getSampleId());
                if (shareSample == null || StringUtils.isBlank(shareSample.getSourceOrderNo())){
                    return checkParentSocureOrder(testSampleMaps, referSampleMaps, testSampleMaps.get(testSample.getSampleParentId()), sourceOrderNo);
                }
                return StringUtils.equalsIgnoreCase(shareSample.getSourceOrderNo(), sourceOrderNo);
        }
        List<TestSampleSimplifyInfo> parentSamples = testSampleMaps.values().stream().filter(ts -> StringUtils.equalsIgnoreCase(ts.getSampleId(), testSample.getSampleParentId())).collect(Collectors.toList());

        // DIG-8555 Remove this "return" statement or make it conditional.
        // 下面for循环里的return没有条件，相当于只执行一次，写法违反了规约 所以做写法上的调整 逻辑不变
        /*if (parentSamples == null || parentSamples.isEmpty()){
            // TODO
            return true;
        }
        for (TestSampleSimplifyInfo parentSample: parentSamples) {
            String sampleId = parentSample.getSampleId();
            CopyReportSampleInfo sample = referSampleMaps.get(sampleId);
            // 如果不为空，且Source OrderNo不为空，则示为已选中了对应的Source Sample
            if (sample != null && StringUtils.isNotBlank(sample.getSourceOrderNo())){
                if (!StringUtils.equalsIgnoreCase(sample.getSourceOrderNo(), sourceOrderNo)){
                    return false;
                }
            }
            return checkParentSocureOrder(testSampleMaps, referSampleMaps, testSampleMaps.get(sampleId), sourceOrderNo);
        }*/
        if (CollectionUtils.isEmpty(parentSamples)) {
            return true;
        } else {
            String sampleId = parentSamples.get(0).getSampleId();
            CopyReportSampleInfo sample = referSampleMaps.get(sampleId);
            // 如果不为空，且Source OrderNo不为空，则示为已选中了对应的Source Sample
            if (sample != null && StringUtils.isNotBlank(sample.getSourceOrderNo())){
                if (!StringUtils.equalsIgnoreCase(sample.getSourceOrderNo(), sourceOrderNo)){
                    return false;
                }
            }
            return checkParentSocureOrder(testSampleMaps, referSampleMaps, testSampleMaps.get(sampleId), sourceOrderNo);
        }

        //return false;
    }

    /**
     * @param  reqObject
     * @return  CustomResult
     * */
    @BizLog(bizType = BizLogConstant.REPORT_OPERATION_HISTORY,operType = "Rework")
    @AccessRule(policyType = PolicyType.Report, orderStatus = {com.sgs.preorder.facade.model.enums.OrderStatus.Pending})
    public CustomResult reworkApprove(ReworkApproveReq reqObject){
        CustomResult rspResult = new CustomResult();
        UserInfo userInfo = UserHelper.getLocalUser();
        String reportId = reqObject.getReportId();

        CustomResult<ReportInfo> reportInfoResult = reportReworkBusiness.checkReportStatus(reqObject);
        if (!reportInfoResult.isSuccess()){
            return reportInfoResult;
        }
        CustomResult<CheckReportReworkRsp> reworkRspResult = checkReworkApprove(reportId,ReportActionEnum.Approval);
        if(!reworkRspResult.isSuccess()){
            return reworkRspResult;
        }
        String sgsToken =tokenClient.getToken();
        String orderNo = reqObject.getOrderNo();
        ReportInfoPO oldReport = reportMapper.getReportInfoByReportId(reportId);
        GetReportNoReq req = new GetReportNoReq();
        req.setOrderNo(orderNo);
        req.setReportNo(oldReport.getReportNo());
        req.setType(3);
        req.setSgsToken(tokenClient.getToken());
        BaseResponse<ReportInfo> newReportResp = reportClient.createReport(req);
        if (newReportResp == null || newReportResp.getStatus() != 200||Objects.isNull(newReportResp.getData())) {
            logger.error(">>>>>>User {} reportRework Failed,createReport error  orderNo={},", userInfo.getRegionAccount(), orderNo);
            rspResult.setMsg("reportRework Failed,please try again");
            rspResult.setSuccess(false);
            return rspResult;
        }
        ReportInfo reportInfo = newReportResp.getData();
        //如果preorder返回数据为空，则认为是失败，通知preorder删除最新的一条
        if (StringUtils.isBlank(reportInfo.getReportNo())) {
            logger.error(">>>>>>User {} reportRework Failed,reportNo={},Get ReportNo Failed! newReportNo 为空.", userInfo.getName(), reportInfo.getReportNo());
            rspResult.setMsg("Get ReportNo Failed!");
            rspResult.setSuccess(false);
            // DIG-4485
            GeneralMessage<GetReportNoReq> msg = new GeneralMessage();
            msg.setUserName(userInfo.getRegionAccount());
            msg.setActionType("reworkReportStatus");
            msg.setData(req);
            kafkaProducer.doSend(KafkaTopicConsts.TOPIC_OTSNOTES_SYNCSTATUS, oldReport.getReportNo(), msg);
            return rspResult;
        }
        //1. notes中增加数据，并且状态为new
        ReportInfoPO newReport = new ReportInfoPO();
        BeanUtils.copyProperties(oldReport, newReport);
        newReport.setID(UUID.randomUUID().toString());
        // 设置reportType为rework
        newReport.setReportTypeID("e16c0267-9aac-11e7-b1df-00163e0a4f4a");
        newReport.setReportNo(reportInfo.getReportNo());
        newReport.setParentReportNo(oldReport.getReportNo());
        newReport.setReportStatus(ReportStatus.New.getCode());
        newReport.setApprover(null);
        newReport.setApproverBy(null);
        newReport.setApproverDate(null);
        newReport.setCreatedBy(userInfo.getRegionAccount());
        newReport.setCreatedDate(new Date());
        // DIG-5439 rework后 RecalculationFlag 设置为0
        newReport.setRecalculationFlag(0);
        newReport.setModifiedBy(userInfo.getRegionAccount());
        newReport.setModifiedDate(new Date());
        newReport.setReportVersion(reportInfo.getReportVersion());
        newReport.setTestMatrixMergeMode(ReportTestMatrixMergeMode.Host.getCode());
        newReport.setRootReportNo(oldReport.getRootReportNo());
        reportMapper.saveReportInfo(newReport);

        //2. 更新preorder中新的报告状态为new
        logger.info(">>>>>>User {} reportRework,Call Trims-updateReportStatus,params={},{},201",userInfo.getRegionAccount(), orderNo,  reportInfo.getReportNo());
        syncPreorderReportStatus(orderNo,  reportInfo.getReportNo(), String.valueOf(ReportStatus.New.getCode()), newReport.getID());//mq

        //3. 更新notes中老的report状态为rework
        oldReport.setReportStatus(ReportStatus.Reworked.getCode());
        reportMapper.updateReportStatusInfo(oldReport);

        // POSL-3942 2021-12-27 更新 tb_external_no_rel数据
        ReworkReportExternalNoReq externalNoReq = new ReworkReportExternalNoReq();
        externalNoReq.setOrderNo(orderNo);
        externalNoReq.setNewReportNo( reportInfo.getReportNo());
        externalNoReq.setNewReportId(newReport.getID());
        externalNoReq.setToken(reqObject.getToken());
        BaseResponse baseResponse = reportClient.reworkExternalNoRel(externalNoReq); // new preorder
        if (baseResponse == null || baseResponse.getStatus() != 200) {
            logger.error(">>>>>>User {} reportRework Failed,reportNo={}, update ExternalNoRel Failed! newReportNo 为空.", userInfo.getRegionAccount(),  reportInfo.getReportNo());
            rspResult.setMsg("update ExternalNoRel Failed!!");
            rspResult.setSuccess(false);
        }

        // DIG-9399 删除 Attachment(Report without DSS)
        CustomResult customResult = orderAttachmentClient.delOrderAtt(orderNo, SysConstants.DICT_KEY_REPORT_WITHOUT_DSS);
        if (!customResult.isSuccess()) {
            return rspResult.fail("删除 Report without DSS 附件失败");
        }

        //4. 更新preorder中老的report状态为rework
        logger.info(">>>>>>User {} reportRework,Call Trims-updateReportStatus,params={},{},205",userInfo.getRegionAccount(), orderNo, oldReport.getReportNo());
        syncPreorderReportStatus(orderNo, oldReport.getReportNo(), String.valueOf(ReportStatus.Reworked.getCode()));//mq

        // 判断Order状态,如果为Completed或Closed状态， 将Order状态置为Reporting状态
        Integer orderStatus = orderClient.getOrderStatusByOrderNo(orderNo);// old order
        if (orderStatus != null ) {
            if (OrderStatus.checkStatus(orderStatus,OrderStatus.Closed,OrderStatus.Completed)) { // 5   10

                // 判断Order状态,如果为Completed或Closed状态， 将Order状态置为Reporting状态
                // call preOrder changeOrderStatus
                logger.info("OrderNo:{} reportRework操作，执行状态更新，oldStatus:{}-newStatus:{}",orderNo,orderStatus,OrderStatus.Reporting.getStatus());
                //ExecuteResult<String> order = trimsTools.preorderInsertChangeLog(token, "order", orderNo, CommUtil.null2Int(str), 9, new Date()); // 老order
                SysStatusReq statusReq = new SysStatusReq();
                statusReq.setObjectType("order");
                statusReq.setAction(String.valueOf(EventTypeEnum.ReWork.getType()));
                statusReq.setObjectNo(orderNo);
                statusReq.setOldStatus(orderStatus);
                statusReq.setNewStatus(OrderStatus.Reporting.getStatus());
                statusReq.setUserName(userInfo.getRegionAccount());
                statusReq.setFromSystemId(3);
                statusReq.setToSystemId(2);
                statusReq.setImmediatelyExecute(Boolean.TRUE);
                CustomResult statusResult = statusClient.insertStatusInfo(statusReq);
                if(statusResult == null || !statusResult.isSuccess()){
                    logger.info("OrderNo:{} reportRework操作，执行状态更新【Fail】，oldStatus:{}-newStatus:{},Preorder接口返回响应为null",orderNo, orderStatus,OrderStatus.Reporting.getStatus());
                }else{
                    logger.info("OrderNo:{} reportRework操作，执行状态更新【{}】，oldStatus:{}-newStatus:{}",orderNo,statusResult!=null && statusResult.isSuccess()?"Success":"Fail", orderStatus,OrderStatus.Reporting.getStatus());
                }

            }
        }
        copyMatrixInfo(oldReport.getID(), newReport.getID(),  reportInfo.getReportNo(), userInfo, orderNo);
        rspResult = qrCodeClient.deleteReport(oldReport.getReportNo(),oldReport.getOrderNo());

        // POSL-5107 删除 RD 数据
        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfo(orderNo);
        ReportDataCancelReq reportDataCancelReq  = new ReportDataCancelReq();
        reportDataCancelReq.setOrderNo(orderNo);
        reportDataCancelReq.setReportNo(oldReport.getReportNo());
        reportDataCancelReq.setReportStatus(ReportStatus.Reworked.getCode());
        reportDataCancelReq.setLabCode(oldReport.getLabCode());
        reportDataCancelReq.setToken(sgsToken);
        reportDataCancelReq.setRequestId(TypeId.generate("").toString());

        extSystemClient.cancelReportData(reportDataCancelReq);

        // DIG-10175 rework时 需要替换 ReportTestData 三张表中的ReportNo 数据
        if (reportDataDealService.queryReportDataConfig()) {
            reportDataDealService.handleReWorkReportNoScenario(orderNo, oldReport.getReportNo(), reportInfo.getReportNo(), oldReport.getLabCode());
        }


        // DIG-8405 获取订单下的SubContractNo
         cancelMainOrderSubcontractData(orderInfo);

        OrderStatusReq mqObject = new OrderStatusReq();
        mqObject.setOrderNo(oldReport.getOrderNo());
        mqObject.setEventType(EventTypeEnum.ReWork.getType());
        mqObject.setReportId(reportId);
        orderClient.doSendOrderStatus(mqObject);

        rspResult.setSuccess(true);
        CheckReportReworkRsp reworkRsp = reworkRspResult.getData();
        String bizVal = String.format("Reason:%s<br/>Applied By:%s",reworkRsp.getReason(),reworkRsp.getApplyBy());
        BizLogHelper.setValue(reqObject.getOrderNo(),bizVal);
        return rspResult;
    }

    private void cancelMainOrderSubcontractData(GeneralOrderInstanceInfoPO orderInfo) {
        OrderInfoDto orderInfoPre = orderClient.getOrderInfoByOrderNo(orderInfo.getOrderNo());
        if(Objects.isNull(orderInfoPre)){
            return;
        }
        if(StringUtils.isBlank(orderInfoPre.getOldOrderNo())){
            return;
        }

        List<SubContractExternalRelationshipDTO> subVOs = subContractExtMapper.querySubContractList(orderInfoPre.getOldOrderNo());
        if(CollectionUtils.isEmpty(subVOs)){
            return;
        }
        //获取有效的subcontract
        SubContractExternalRelationshipDTO subContractPO =  subVOs.stream()
               .filter(o -> StringUtils.equalsIgnoreCase(o.getExternalNo(), orderInfo.getOrderNo()))
               .filter(s -> !SubContractStatusEnum.check(s.getStatus(), SubContractStatusEnum.Cancelled))
               .findFirst().orElse(null);
        if(Objects.isNull(subContractPO)){
            return;
        }

        //调用Revise
        ReviseSubReportReq reqObject = new ReviseSubReportReq();
        reqObject.setOrderNo(orderInfoPre.getOldOrderNo());
        reqObject.setObjectType(SubReportObjectTypeEnums.subcontract.getObjectType());
        reqObject.setObjectNo(subContractPO.getSubContractNo());
        subContractService.reviseSubReport(reqObject);
        /*
        //主单rd数据 cancel
        TestDataDeleteReq testDataDeleteReq = new TestDataDeleteReq();
        testDataDeleteReq.setLabCode(orderInfo.getLabCode());
        testDataDeleteReq.setObjectNo(subContractPO.getSubContractNo());
        testDataDeleteReq.setProductLineCode(StringUtils.defaultString(ProductLineContextHolder.getProductLineCode(), ProductLineType.SL.getProductLineAbbr()));
        testDataBizClient.deleteTestData(testDataDeleteReq);

        //主单 test data conclusion cancel
        subContractService.cancelSubcontractTestData(subContractPO.getSubContractNo());*/
    }

    public CustomResult<CheckReportReworkRsp> checkReworkApprove(String reportId, ReportActionEnum reportAction){
        CustomResult rspResult = new CustomResult();
        UserInfo userInfo = UserHelper.getLocalUser();
        CheckReportReworkReq reworkReq = new CheckReportReworkReq();
        reworkReq.setLabCode(userInfo.getCurrentLabCode());
        reworkReq.setReportID(reportId);
        CheckReportReworkRsp reworkRsp = reportReworkApplyRecordsInfoExtMapper.getCheckReportReworkInfo(reworkReq);
        if(Objects.isNull(reworkRsp)){
            return rspResult.fail("ReportNo is invalid");
        }
        Integer reportStatus = reworkRsp.getReportStatus();
        if(reportStatus == null || !ReportStatus.check(reportStatus,ReportStatus.Approved)){
            return rspResult.fail("Report Status is not Approve");
        }
        rspResult.setSuccess(reworkRsp.getApplyId() != null && reworkRsp.getApplyId() > 0);
        if (reportAction == ReportActionEnum.Reject){
            if (rspResult.isSuccess()){
                rspResult.setMsg("申请记录已存在.");
            }
            return rspResult;
        }
        if (!rspResult.isSuccess()){
            rspResult.setMsg("申请记录不存在.");
            return rspResult;
        }
        ReportReworkApproverRecordsInfoPO reportRework = new ReportReworkApproverRecordsInfoPO();
        reportRework.setReportID(reportId);
        reportRework.setLabCode(userInfo.getCurrentLabCode());
        reportRework.setApplyStatus(2);
        reportRework.setApproverTime(new Date());
        reportRework.setApproverBy(userInfo.getRegionAccount());
        rspResult.setSuccess(reportReworkRecordService.reportReworkApproval(reportRework) > 0);
        rspResult.setData(reworkRsp);
        return rspResult;
    }

    public String syncPreorderReportStatus(String orderNo, String reportNo, String reportStatus) {
        return syncPreorderReportStatus(orderNo, reportNo, reportStatus, null);
    }

    public String syncPreorderReportStatus(String orderNo, String reportNo, String reportStatus, String reportId) {
        logger.info("updateReportStatus : OrderNo = {}, ReportNo = {}, ReportStatus = {}",orderNo,reportNo,reportStatus);

        SyncReportStatusMessage data = new SyncReportStatusMessage();
        data.setOrderNo(orderNo);
        data.setReportNo(reportNo);
        data.setReportStatus(reportStatus);
        data.setReportId(reportId);

        SyncStatusMessage syncData = new SyncStatusMessage();
        syncData.setSyncReportStatusMessage(data);

        GeneralMessage<SyncStatusMessage> msg = new GeneralMessage<>();
        msg.setActionType("syncReportStatus");
        msg.setData(syncData);
        msg.setProductLineCode(ProductLineType.SL.getProductLineAbbr());

        kafkaProducer.doSend(KafkaTopicConsts.TOPIC_OTSNOTES_SYNCSTATUS, reportNo,msg);

        return "success";
    }

    public void copyMatrixInfo(String oldReportId, String newReportID, String newReportNo, UserInfo user,
                               String orderNo) {

        logger.info(">>>>>>reportRework-copyMatrixInfo,oldReportId={},newReportID={},newReportNo={},user={},orderNo={}",oldReportId, newReportID, newReportNo, user.getRegionAccount(), orderNo);
        List<ReportMatrixRelationShipInfoPO> objs = new ArrayList<ReportMatrixRelationShipInfoPO>();

        List<ReportMatrixRelationShipInfoPO> matrixMappingList = reportMatrixRelMapper
                .getReportMatrixRelListByReportId(oldReportId);
        logger.info("matrixMappingList size = " + matrixMappingList.size());
        if (matrixMappingList != null && matrixMappingList.size() > 0) {
            for (ReportMatrixRelationShipInfoPO rm : matrixMappingList) {
                ReportMatrixRelationShipInfoPO newRm = new ReportMatrixRelationShipInfoPO();
                newRm.setTestMatrixID(rm.getTestMatrixID());
                newRm.setCreatedBy(user.getRegionAccount());
                newRm.setCreatedDate(new Date());
                newRm.setID(UUID.randomUUID().toString());
                newRm.setModifiedBy(user.getRegionAccount());
                newRm.setModifiedDate(new Date());
                newRm.setReportID(newReportID);
                objs.add(newRm);
            }
            reportMatrixRelMapper.batchInsert(objs);
        }
        ConclusionInfoPO infoPO = new ConclusionInfoPO();
        infoPO.setConclusionLevelID(601);
        infoPO.setReportID(oldReportId);
        List<ConclusionInfoPO> conclusionList = conclusionInfoExtMapper.getConclusionInfoByReportNo(infoPO);
        if (conclusionList != null && conclusionList.size() > 0) {
            List<ConclusionInfoPO> newList = new ArrayList<ConclusionInfoPO>();
            for (ConclusionInfoPO con : conclusionList) {
                ConclusionInfoPO newCon = new ConclusionInfoPO();
                BeanUtils.copyProperties(con, newCon);
                newCon.setID(UUID.randomUUID().toString());
                newCon.setReportID(newReportID);
                newCon.setCreatedBy(user.getRegionAccount());
                newCon.setCreatedDate(new Date());
                newCon.setModifiedBy(user.getRegionAccount());
                newCon.setModifiedDate(new Date());
                newList.add(newCon);
            }
            conclusionInfoExtMapper.batchInsert(newList);
        }
    }
}
