package com.sgs.otsnotes.domain.service.reportdata;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.config.SysConstants;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.ordercopy.SysCopyInfo;
import com.sgs.otsnotes.facade.model.req.subcontract.InvalidateSubcontractDataReq;
import com.sgs.otsnotes.facade.model.req.subcontract.QuerySubcontractDataReq;
import com.sgs.otsnotes.facade.model.req.subcontract.ReportDataReplaceReq;
import com.sgs.otsnotes.integration.FrameWorkClient;
import com.sgs.otsnotes.integration.v2.ReportDataClientV2;
import com.sgs.preorder.facade.model.req.BuParamReq;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataResultInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataTestMatrixInfo;
import com.sgs.testdatabiz.facade.model.testdata.query.TestDataTestMatrixInfoAdvance;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class ReportDataDealService {
    private static final Logger logger = LoggerFactory.getLogger(ReportDataDealService.class);

    protected ReportDataClientV2 subContractDataClient;

    protected FrameWorkClient frameWorkClient;

    /**
     *
     * @return
     */
    public Boolean queryReportDataConfig() {
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setGroupCode(Constants.BU_PARAM.GENERAL_SWITCH.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.GENERAL_SWITCH.SCENE_POINT.REPORT_DATA_COPY_SWITCH);
        buParamReq.setProductLineCode(StringUtils.defaultString(ProductLineContextHolder.getProductLineCode(), ProductLineType.SL.getProductLineAbbr()));
        buParamReq.setSystemId(SysConstants.SYSTEMID);
        BuParamValueRsp buParam = frameWorkClient.getBuParam(buParamReq);
        if (buParam == null || StringUtils.isBlank(buParam.getParamValue())) {
            return Boolean.FALSE;
        }
        return StringUtils.equalsIgnoreCase(Constants.BU_PARAM.GENERAL_SWITCH.SCENE_POINT.VALUES.TRUE, buParam.getParamValue());
    }




    /**
     * 查询RD中已保存的数据
     */
    public List<ReportTestDataInfo> queryExistingData(String orderNo, String reportNo, String labCode) {
        List<ReportTestDataInfo> reportTestDataInfos = Lists.newArrayList();
        try {
            QuerySubcontractDataReq queryReq = new QuerySubcontractDataReq();
            queryReq.setOrderNo(orderNo);
//            queryReq.setObjectNo();
            queryReq.setLabCode(labCode);
            queryReq.setReportNo(reportNo);
            queryReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());

            BaseResponse<List<ReportTestDataInfo>> queryResult = subContractDataClient.queryReportTestData(queryReq);
            if (!queryResult.isSuccess()) {
                logger.error("【ReportDataCopyService】orderNo:{}, 查询分包数据失败: {}", orderNo, queryResult.getMessage());
                return reportTestDataInfos;
            }

            return queryResult.getData();
        } catch (Exception e) {
            logger.error("查询已保存数据失败", e);
            return reportTestDataInfos;
        }
    }

    public CustomResult saveReportData(List<ReportTestDataInfo> reportTestDataInfos) {
        CustomResult rspResult = new CustomResult();
        if (reportTestDataInfos.isEmpty()) {
            return rspResult.success();
        }
        for (ReportTestDataInfo reportTestDataInfo : reportTestDataInfos) {
            BaseResponse<Void> importResult = subContractDataClient.importData(reportTestDataInfo);
            if (!importResult.isSuccess()) {
                logger.error("【ReportDataCopyService】orderNo:{}, 保存测试数据失败: {}", reportTestDataInfo.getOrderNo(), importResult.getMessage());
                return rspResult.fail("保存测试数据失败: " + importResult.getMessage());
            }
        }
        return rspResult.success();

    }




    /**
     * 根据指定的Matrix筛选数据
     */
    public List<ReportTestDataInfo> filterDataByMatrix(
            List<ReportTestDataInfo> testResultData,
            String reportNo,
            Map<String, String> oldTestMatrixIds) {

        if (CollectionUtils.isEmpty(testResultData) || oldTestMatrixIds.isEmpty()) {
            return Lists.newArrayList();
        }

        testResultData.forEach(testResult -> {
            if (CollectionUtils.isEmpty(testResult.getTestMatrixs())) {
                return;
            }
            if (StringUtils.equalsIgnoreCase(testResult.getReportNo(), reportNo)) {
                return;
            }
            // 去掉 新单中的不存在的Matrix
            testResult.getTestMatrixs().removeIf(testMatrix -> !oldTestMatrixIds.containsKey(testMatrix.getTestMatrixId()));
        });

        // 去掉没有matrix结果的数据
        testResultData.removeIf(result -> CollectionUtils.isEmpty(result.getTestMatrixs()));

        return testResultData;
    }



    /**
     * 转换为ReportTestDataInfo格式
     */
    public List<ReportTestDataInfo> transformToReportTestDataInfos(
            List<ReportTestDataInfo> filteredData,
            SysCopyInfo reqParams, UserInfo localUser) {

        List<ReportTestDataInfo> newReportTestDataInfo = Lists.newArrayList();
        if (CollectionUtils.isEmpty(filteredData)) {
            return newReportTestDataInfo;
        }
        filteredData.forEach(testResult -> {
            ReportTestDataInfo reportTestDataInfo = transformToReportTestDataInfo(testResult, reqParams, localUser);
            if (reportTestDataInfo != null) {
                newReportTestDataInfo.add(transformToReportTestDataInfo(testResult, reqParams, localUser));
            }
        });

        return newReportTestDataInfo;
    }



    public static ReportTestDataInfo transformToReportTestDataInfo(ReportTestDataInfo filteredData, SysCopyInfo reqParams, UserInfo localUser) {
        // 输入参数校验
        if (filteredData == null || reqParams == null) {
            return null;
        }

        ReportTestDataInfo reportTestDataInfo = new ReportTestDataInfo();

        // 初始数据与新单数据一致
        BeanUtil.copyProperties(filteredData, reportTestDataInfo);

        // 替换数据
        reportTestDataInfo.setOrderNo(reqParams.getRootOrderNo()); // 保存为LogicOrderNo
        reportTestDataInfo.setReportNo(reqParams.getReportNo());
        reportTestDataInfo.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        reportTestDataInfo.setRegionAccount("System");

        // 转换测试矩阵数据
        List<TestDataTestMatrixInfoAdvance> originalMatrixList = filteredData.getTestMatrixs();
        if (CollectionUtils.isEmpty(originalMatrixList)) {
            reportTestDataInfo.setTestMatrixs(Lists.newArrayList());
            return reportTestDataInfo;
        }

        List<TestDataTestMatrixInfoAdvance> transformedMatrixList = originalMatrixList.stream()
                .map(testMatrixInfo -> transformTestMatrixInfo(testMatrixInfo, reqParams))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        reportTestDataInfo.setTestMatrixs(transformedMatrixList);
        return reportTestDataInfo;
    }

    /**
     * 转换单个测试矩阵信息
     */
    public static TestDataTestMatrixInfoAdvance transformTestMatrixInfo(TestDataTestMatrixInfoAdvance testMatrixInfo, SysCopyInfo reqParams) {
        if (testMatrixInfo == null) {
            return null;
        }

        String oldMatrixId = testMatrixInfo.getTestMatrixId();
        String newMatrixId = reqParams.getOldTestMatrixIds().get(oldMatrixId);
        String newTestLineInstanceId = reqParams.getOldTestLineInstanceIds().get(testMatrixInfo.getTestLineInstanceId());
        String newSampleInstanceId = reqParams.getOldSampleIds().get(testMatrixInfo.getTestSampleId());

        // 如果关键ID映射缺失，则跳过该矩阵
        if (StringUtils.isBlank(newMatrixId) || StringUtils.isBlank(newTestLineInstanceId) || StringUtils.isBlank(newSampleInstanceId)) {
            return null;
        }

        TestDataTestMatrixInfoAdvance matrixInfo = new TestDataTestMatrixInfoAdvance();
        BeanUtil.copyProperties(testMatrixInfo, matrixInfo);

        // 更新业务ID为新订单中的指定ID
        matrixInfo.setTestMatrixId(newMatrixId);
        matrixInfo.setTestLineInstanceId(newTestLineInstanceId);
        matrixInfo.setTestSampleId(newSampleInstanceId);

        // 转换测试结果数据
        List<TestDataResultInfo> originalResults = testMatrixInfo.getTestResults();
        if (CollectionUtils.isNotEmpty(originalResults)) {
            List<TestDataResultInfo> transformedResults = originalResults.stream()
                    .map(dataInfo -> transformTestDataResultInfo(dataInfo, reqParams))
                    .collect(Collectors.toList());
            matrixInfo.setTestResults(transformedResults);
        } else {
            matrixInfo.setTestResults(Lists.newArrayList());
        }

        return matrixInfo;
    }


    /**
     * 转换单个测试结果信息
     */
    public static TestDataResultInfo transformTestDataResultInfo(TestDataResultInfo dataInfo, SysCopyInfo reqParams) {
        if (dataInfo == null) {
            return null;
        }

        TestDataResultInfo resultInfo = new TestDataResultInfo();
        BeanUtil.copyProperties(dataInfo, resultInfo);

        Map<String, String> oldTestDataIds = reqParams.getOldTestDataIds();
        // 不做终止判断，仅有内部分包/Fast 场景有 TestdataId
        if (oldTestDataIds != null && oldTestDataIds.containsKey(dataInfo.getTestDataId())) {
            resultInfo.setTestDataId(oldTestDataIds.get(dataInfo.getTestDataId()));
        }

        return resultInfo;
    }



    /**
     * 处理新增ResultData场景
     * 支持: Amend-Extract
     */
    public CustomResult<List<ReportTestDataInfo>> handleAddResultData(SysCopyInfo reqParams, String oldReportNo) {
        CustomResult<List<ReportTestDataInfo>> rspResult = new CustomResult<>();

        logger.info("【ReportDataCopyService】orderNo:{}, 开始处理新增ResultData场景, copyType: {}, oldReportNo: {}",
                reqParams.getOrderNo(), reqParams.getCopyType(), oldReportNo);

        try {
            // 1. 查询RD中已保存的数据
            List<ReportTestDataInfo> queryResult = queryExistingData(reqParams.getRootOrderNo(), oldReportNo, reqParams.getLabBu().getLabCode());
            if (CollectionUtils.isEmpty(queryResult)) {
                logger.warn("【ReportDataCopyService】orderNo:{}, 未查询到已保存的测试数据, reportNo: {}", reqParams.getRootOrderNo(), oldReportNo);
                return rspResult.success(); // 无数据时直接返回成功
            }

            // 2. 数据结构变换和筛选
            List<ReportTestDataInfo> filteredData = filterDataByMatrix(queryResult, reqParams.getReportNo(), reqParams.getOldTestMatrixIds());

            if (CollectionUtils.isEmpty(filteredData)) {
                logger.info("【ReportDataCopyService】orderNo:{}, 筛选后无数据需要处理", reqParams.getRootOrderNo());
                return rspResult.success();
            }
            UserInfo localUser = UserHelper.getLocalUser();
            // 3. 转换为ReportTestDataInfo格式
            List<ReportTestDataInfo> allReportTestDataInfo = transformToReportTestDataInfos(filteredData, reqParams, localUser);

            rspResult.setData(allReportTestDataInfo);

            return rspResult.success();

        } catch (Exception e) {
            logger.error("【ReportDataCopyService】orderNo:{}, 处理新增ResultData失败", e);
            return rspResult.fail("处理新增ResultData失败: " + e.getMessage());
        }
    }


}
