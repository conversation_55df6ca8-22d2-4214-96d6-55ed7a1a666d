package com.sgs.otsnotes.domain.service.sample.processor.impl;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.framework.core.datasource.DatabaseTypeEnum;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.dbstorages.mybatis.config.DatabaseContextHolder;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestMatrixMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestSampleGroupMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestSampleLangMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestSampleMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.SlimSubcontractPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestMatrixPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleGroupInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleLangInfoPO;
import com.sgs.otsnotes.domain.service.SampleService;
import com.sgs.otsnotes.domain.service.sample.context.SampleBreakDownContext;
import com.sgs.otsnotes.domain.service.sample.context.SampleBreakDownResult;
import com.sgs.otsnotes.domain.service.sample.processor.SampleBreakDownProcessor;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.enums.SampleSourceTypeEnum;
import com.sgs.otsnotes.facade.model.enums.SampleType;
import com.sgs.otsnotes.facade.model.enums.TestLineStatus;
import com.sgs.otsnotes.facade.model.info.PPSampleRelInfo;
import com.sgs.otsnotes.facade.model.info.PPTestLineRelInfo;
import com.sgs.otsnotes.facade.model.info.sample.TestLineSampleTypeInfo;
import com.sgs.otsnotes.facade.model.req.SampleBreakDownReq;
import com.sgs.otsnotes.facade.model.req.TestMatrixReq;
import com.sgs.otsnotes.facade.model.req.TestSampleReq;
import com.sgs.otsnotes.infra.repository.testline.TestLineRepository;
import com.sgs.otsnotes.infra.service.TestSampleLangService;

import cn.hutool.core.collection.CollectionUtil;

/**
 * 样品分解处理器实现类
 * 
 * 【重构组件】实现样品分解的核心业务逻辑处理
 * 
 * 处理流程：
 * 1. 数据加载和构造 - 加载订单相关的所有旧数据
 * 2. NC状态验证 - 验证样品的NC状态
 * 3. 样品数据处理 - 处理新样品数据，包括混样、共享样品等
 * 4. 测试矩阵处理 - 处理测试矩阵关系
 * 5. 数据库事务操作 - 执行批量数据库操作
 * 6. 后处理操作 - 执行条件更新等后续处理
 * 
 * <AUTHOR>
 * @since 重构版本
 */
@Component
public class SampleBreakDownProcessorImpl implements SampleBreakDownProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(SampleBreakDownProcessorImpl.class);
    
    @Autowired
    private SampleService sampleService;
    
    @Autowired
    private TestSampleMapper testSampleMapper;
    
    @Autowired
    private TestSampleGroupMapper testSampleGroupMapper;
    
    @Autowired
    private TestMatrixMapper testMatrixMapper;
    
    @Autowired
    private TestLineRepository testLineRepository;
    
    @Autowired
    private TestSampleLangMapper testSampleLangMapper;
    
    @Autowired
    private TestSampleLangService testSampleLangService;
    
    @Autowired
    private com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportMapper reportMapper;
    
    @Autowired
    private com.sgs.otsnotes.dbstorages.mybatis.extmapper.OrderMapper orderMapper;
    
    @Autowired
    private com.sgs.otsnotes.dbstorages.mybatis.extmapper.ConclusionInfoExtMapper conclusionInfoExtMapper;
    
    @Autowired
    private com.sgs.otsnotes.dbstorages.mybatis.extmapper.SlimSubContractExtMapper slimSubContractExtMapper;
    
    @Autowired
    private org.springframework.transaction.support.TransactionTemplate transactionTemplate;
    
    /**
     * 处理样品分解的核心业务逻辑
     */
    @Override
    public SampleBreakDownResult process(SampleBreakDownContext context) {
        logger.info("【样品分解处理器】开始处理样品分解，订单号: {}", context.getReqObject().getOrderNo());
        
        try {
            // 1. 数据加载和构造
            SampleBreakDownResult loadResult = loadDataFromDatabase(context);
            if (!loadResult.isSuccess()) {
                return loadResult;
            }
            
            // 2. NC状态验证
            SampleBreakDownResult ncResult = processNCValidation(context);
            if (!ncResult.isSuccess()) {
                return ncResult;
            }
            
            // 3. 样品数据处理
            SampleBreakDownResult sampleResult = processSampleData(context);
            if (!sampleResult.isSuccess()) {
                return sampleResult;
            }
            
            // 4. 测试矩阵处理
            SampleBreakDownResult matrixResult = processTestMatrix(context);
            if (!matrixResult.isSuccess()) {
                return matrixResult;
            }
            
            // 5. Slim子承包商验证和处理
            SampleBreakDownResult slimResult = processSlimSubcontracts(context);
            if (!slimResult.isSuccess()) {
                return slimResult;
            }
            
            // 6. 数据库事务操作
            SampleBreakDownResult dbResult = executeDatabaseOperations(context);
            if (!dbResult.isSuccess()) {
                return dbResult;
            }
            
            // 6. 后处理操作
            SampleBreakDownResult postResult = executePostProcessing(context);
            if (!postResult.isSuccess()) {
                return postResult;
            }

            logger.info("【样品分解处理器】处理完成，订单号: {}", context.getReqObject().getOrderNo());
            return SampleBreakDownResult.success(context);
            
        } catch (Exception e) {
            logger.error("【样品分解处理器】处理过程发生异常，订单号: {}", context.getReqObject().getOrderNo(), e);
            return SampleBreakDownResult.fail("处理过程发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 数据加载和构造
     * 
     * 对应原始代码第459-527行的数据加载逻辑：
     * 1. 预处理测试矩阵请求，提取测试线ID集合
     * 2. 加载旧样品数据并过滤
     * 3. 构造样品映射关系和NC标记
     * 4. 加载PP样品关系数据
     * 5. 加载样品组数据
     * 6. 加载和验证测试线数据
     * 7. 加载测试矩阵数据
     */
    private SampleBreakDownResult loadDataFromDatabase(SampleBreakDownContext context) {
        logger.info("【样品分解处理器】开始加载数据库数据，订单号: {}", context.getReqObject().getOrderNo());
        
        try {
            SampleBreakDownReq reqObject = context.getReqObject();
            String orderId = context.getOrderId();
            
            // 1. 预处理测试矩阵请求，提取测试线ID集合
            List<TestMatrixReq> requestMatrixs = reqObject.getMatrixs();
            if (requestMatrixs == null) {
                requestMatrixs = Lists.newArrayList();
            }
            Set<String> requestTestLineIds = Sets.newHashSet();
            requestMatrixs.forEach(matrix -> {
                requestTestLineIds.add(matrix.getTestLineInstanceId());
            });

            
            // 2. 初始化数据容器
            Map<String, TestSampleInfoPO> oldSampleMaps = Maps.newHashMap();
            Map<String, SampleType> oldSampleIds = Maps.newHashMap();
            Map<String, PPTestLineRelInfo> oldPPSampleRelMaps = Maps.newHashMap();
            Map<String, String> oldSampleGroupIds = Maps.newHashMap();
            Set<String> phySampleIds = Sets.newHashSet();
            Set<String> sampleIdSet = Sets.newHashSet();
            
            // 3. 加载订单信息
            com.sgs.otsnotes.dbstorages.mybatis.model.GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfo(reqObject.getOrderNo());
            if (order == null) {
                logger.error("【样品分解处理器】未找到订单信息，订单号: {}", reqObject.getOrderNo());
                return SampleBreakDownResult.fail(String.format("当前OrderNo [%s] 不存在.", reqObject.getOrderNo()));
            }
            context.setOrder(order);
            
            // 4. 加载Report信息
            com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO report = reportMapper.getReportByOrderNo(reqObject.getOrderNo());
            if (report == null) {
                logger.error("【样品分解处理器】未找到Report信息，订单号: {}", reqObject.getOrderNo());
                return SampleBreakDownResult.fail(String.format("未找到Order(%s)下的Report信息.", reqObject.getOrderNo()));
            }
            context.setReport(report);
            
            // 5. 加载旧样品数据
            List<TestSampleInfoPO> oldSamples = testSampleMapper.getSampleByOrderNo(reqObject.getOrderNo());
            
            // 6. 构造原样NC标记映射关系
            Map<String, Boolean> originalSampleNCMap = sampleService.getOriginalSampleNCMap(
                oldSamples, phySampleIds, oldSampleIds, oldSampleMaps);
            
            // 7. 加载PP样品关系数据
            List<PPSampleRelInfo> oldPPSampleRels = testSampleMapper.getPPSampleRelByOrderId(orderId);
            sampleService.setPPsampleRelMap(oldPPSampleRels, phySampleIds, requestTestLineIds, oldPPSampleRelMaps);
            
            // 8. 加载样品组数据
            List<TestSampleGroupInfoPO> oldSampleGroups = sampleService.getTestSampleGroupInfoPOS(
                reqObject, oldSampleIds, oldSampleGroupIds);
            
            // 9. 加载测试线数据并过滤验证
            Map<String, TestLineInstancePO> orderMatchedTestLineMaps = Maps.newHashMap();
            DatabaseContextHolder.setDatabaseRouting(DatabaseTypeEnum.Master, false);
            List<TestLineInstancePO> orderTestLines = testLineRepository.getTestLineByOrderId(orderId);
            
            for (TestLineInstancePO oldTestline : orderTestLines) {
                if (!requestTestLineIds.contains(oldTestline.getID())) {
                    continue;
                }
                if (!TestLineStatus.check(oldTestline.getTestLineStatus(), TestLineStatus.Typing, TestLineStatus.Entered)) {
                    // 【关键差异修复】原始逻辑是直接返回错误，不是continue
                    logger.error("【样品分解处理器】测试线状态验证失败，订单号: {}, 测试线: {}, 状态: {}", 
                            reqObject.getOrderNo(), oldTestline.getTestLineID(), oldTestline.getTestLineStatus());
                    return SampleBreakDownResult.fail(String.format("当前Test Line(%s)状态为%s，只允许Typing的状态回传.", 
                            oldTestline.getTestLineID(), TestLineStatus.findStatus(oldTestline.getTestLineStatus())));
                }
                orderMatchedTestLineMaps.put(oldTestline.getID(), oldTestline);
            }
            
            // 10. 加载测试矩阵数据 - 【关键差异修复】按原始逻辑构建oldMatrixSampleIds
            Map<String, TestMatrixPO> oldTestMatrixMaps = Maps.newHashMap();
            Map<String, Set<String>> oldMatrixSampleIds = Maps.newHashMap();
            sampleService.setOldTestMatrixMaps(order, oldMatrixSampleIds, oldTestMatrixMaps);

            // 11. 将加载的数据设置到上下文中
            context.setOldSamples(oldSamples);
            context.setOldSampleMaps(oldSampleMaps);
            context.setOldSampleIds(oldSampleIds);
            context.setOldPPSampleRelMaps(oldPPSampleRelMaps);
            context.setOldSampleGroupIds(oldSampleGroupIds);
            context.setOldSampleGroups(oldSampleGroups);
            context.setPhySampleIds(phySampleIds);
            context.setSampleIdSet(sampleIdSet);
            context.setOriginalSampleNCMap(originalSampleNCMap);
            context.setOldTestMatrixMaps(oldTestMatrixMaps);
            context.setOldMatrixSampleIds(oldMatrixSampleIds);  // 【修复】添加缺失的数据
            context.setOrderMatchedTestLineMaps(orderMatchedTestLineMaps);
            context.setOldTestMatrixMaps(oldTestMatrixMaps);
            context.setOldMatrixSampleIds(oldMatrixSampleIds);  // 【修复】添加缺失的数据
            context.setRequestTestLineIds(requestTestLineIds);
            
            logger.info("【样品分解处理器】数据加载完成，订单号: {}, 加载旧样品: {}, 测试线: {}, 测试矩阵: {}", 
                    reqObject.getOrderNo(), oldSamples.size(), orderMatchedTestLineMaps.size(), oldTestMatrixMaps.size());
            
            return SampleBreakDownResult.success(context);
            
        } catch (Exception e) {
            logger.error("【样品分解处理器】数据加载失败，订单号: {}", context.getReqObject().getOrderNo(), e);
            return SampleBreakDownResult.fail("数据加载失败: " + e.getMessage());
        }
    }
    
    /**
     * NC状态验证
     * 
     * 对应原始代码中的checkSampleNc方法调用
     */
    private SampleBreakDownResult processNCValidation(SampleBreakDownContext context) {
        logger.info("【样品分解处理器】开始NC状态验证，订单号: {}", context.getReqObject().getOrderNo());
        
        try {
            SampleBreakDownReq reqObject = context.getReqObject();
            List<TestSampleInfoPO> oldSamples = context.getOldSamples();
            Map<String, Set<String>> oldMatrixSampleIds = context.getOldMatrixSampleIds();
            List<TestSampleGroupInfoPO> oldSampleGroups = context.getOldSampleGroups();
            
            // 调用原有的NC验证逻辑
            CustomResult ncResult = sampleService.checkSampleNc(reqObject, oldSamples, oldMatrixSampleIds, oldSampleGroups);
            if (!ncResult.isSuccess()) {
                logger.warn("【样品分解处理器】NC状态验证失败，订单号: {}, 原因: {}", 
                        reqObject.getOrderNo(), ncResult.getMsg());
                return SampleBreakDownResult.fail(ncResult.getMsg());
            }
            
            logger.info("【样品分解处理器】NC状态验证通过，订单号: {}", context.getReqObject().getOrderNo());
            return SampleBreakDownResult.success(context);
            
        } catch (Exception e) {
            logger.error("【样品分解处理器】NC状态验证异常，订单号: {}", context.getReqObject().getOrderNo(), e);
            return SampleBreakDownResult.fail("NC状态验证异常: " + e.getMessage());
        }
    }
    
    /**
     * 样品数据处理
     * 
     * 处理新样品数据，包括混样、共享样品等业务逻辑
     * 对应原始代码中的样品处理主逻辑
     */
    private SampleBreakDownResult processSampleData(SampleBreakDownContext context) {
        logger.info("【样品分解处理器】开始处理样品数据，订单号: {}", context.getReqObject().getOrderNo());
        
        try {
            SampleBreakDownReq reqObject = context.getReqObject();
            List<TestSampleReq> samples = reqObject.getSamples();
            
            // 1. 初始化数据容器
            initializeDataContainers(context);
            
            // 2. 准备样品语言映射数据
            prepareSampleLanguageData(context, samples);
            
            // 3. 处理每个样品
            for (TestSampleReq sample : samples) {
                SampleBreakDownResult result = processSingleSample(context, sample);
                if (!result.isSuccess()) {
                    return result;
                }
            }
            
            // 4. 处理需要删除的样品
            SampleBreakDownResult deleteResult = processDeletedSamples(context);
            if (!deleteResult.isSuccess()) {
                return deleteResult;
            }
            
            // 5. 处理需要删除的样品组
            processDeletedSampleGroups(context);
            
            // 6. 验证新样品ID的合法性
            SampleBreakDownResult validateResult = validateNewSampleIds(context);
            if (!validateResult.isSuccess()) {
                return validateResult;
            }
            
            logger.info("【样品分解处理器】样品数据处理完成，订单号: {}，处理样品数: {}", 
                    context.getReqObject().getOrderNo(), samples.size());
            return SampleBreakDownResult.success(context);
            
        } catch (Exception e) {
            logger.error("【样品分解处理器】样品数据处理异常，订单号: {}", context.getReqObject().getOrderNo(), e);
            return SampleBreakDownResult.fail("样品数据处理异常: " + e.getMessage());
        }
    }
    
    /**
     * 初始化数据容器
     */
    private void initializeDataContainers(SampleBreakDownContext context) {
        context.setTestSamples(Lists.newArrayList());
        context.setTestSampleGroups(Lists.newArrayList());
        context.setTestSampleLangs(Lists.newArrayList());
        context.setNewSampleIds(Sets.newHashSet());
        context.setSampleSetIds(Sets.newHashSet());
        context.setDelSampleLangIds(Lists.newArrayList());
        
        logger.debug("【样品分解处理器】数据容器初始化完成，订单号: {}", context.getReqObject().getOrderNo());
    }
    
    /**
     * 准备样品语言映射数据
     */
    private void prepareSampleLanguageData(SampleBreakDownContext context, List<TestSampleReq> samples) {
        Set<String> testSampleIds = samples.stream()
                .map(TestSampleReq::getSampleId)
                .collect(Collectors.toSet());
        
        Map<String, List<TestSampleLangInfoPO>> langMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(testSampleIds)) {
            List<TestSampleLangInfoPO> langs = testSampleLangMapper.getTestSampleListIds(testSampleIds);
            langMap = langs.stream().collect(Collectors.groupingBy(TestSampleLangInfoPO::getSampleId));
        }
        context.setLangMap(langMap);
        
        logger.debug("【样品分解处理器】样品语言映射数据准备完成，订单号: {}，语言数据: {}", 
                context.getReqObject().getOrderNo(), langMap.size());
    }
    
    /**
     * 处理单个样品
     */
    private SampleBreakDownResult processSingleSample(SampleBreakDownContext context, TestSampleReq sample) {
        SampleType sampleType = SampleType.findType(sample.getSampleType());
        if (sampleType == null) {
            return SampleBreakDownResult.fail(String.format("未找到对应的sample(%s)类型.", sample.getSampleNo()));
        }
        
        // 设置默认NoTest值
        if (sample.getNoTest() == null) {
            sample.setNoTest(false);
        }
        
        // 处理原样
        if (sampleType == SampleType.OriginalSample) {
            return processOriginalSample(context, sample);
        }
        
        // 验证NoTest样品的矩阵状态
        if (sample.getNoTest() && context.getOldMatrixSampleIds().containsKey(sample.getSampleId())) {
            return SampleBreakDownResult.fail(String.format("当前Sample(%s)已Not Test，无法Matrix.", sample.getSampleNo()));
        }
        
        // 验证样品ID和样品编号
        if (StringUtils.isBlank(sample.getSampleId()) || StringUtils.isBlank(sample.getSampleNo())) {
            return SampleBreakDownResult.fail(String.format("当前SampleId(%s)或SampleNo(%s)不能为空.", 
                    sample.getSampleId(), sample.getSampleNo()));
        }
        
        // 处理非原样类型
        return processNonOriginalSample(context, sample, sampleType);
    }
    
    /**
     * 处理原样
     */
    private SampleBreakDownResult processOriginalSample(SampleBreakDownContext context, TestSampleReq sample) {
        String originalSampleId = sample.getSampleId();
        if (StringUtils.isBlank(originalSampleId)) {
            return SampleBreakDownResult.fail(String.format("对应的sample(%s)，ExecutionSystemSampleId 属性不能为空.", sample.getSampleNo()));
        }
        
        TestSampleInfoPO sampleItem = context.getOldSampleMaps().get(originalSampleId);
        if (sampleItem == null) {
            return SampleBreakDownResult.fail(String.format("未找该Original Sample(%s).", sample.getSampleNo()));
        }
        
        if (!SampleType.equals(sampleItem.getSampleType(), SampleType.OriginalSample)) {
            return SampleBreakDownResult.fail(String.format("请求的Original Sample(%s)类型无效.", sample.getSampleNo()));
        }
        
        // 检查是否已被Not Test
        Boolean applicable = sampleItem.getApplicable();
        if (applicable != null && applicable.booleanValue()) {
            return SampleBreakDownResult.fail(String.format("请求的原样Sample(%s)已被Not Test，无法操作.", sample.getSampleNo()));
        }
        
        // 从旧样品ID中移除，表示已处理
        if (context.getOldSampleIds().containsKey(originalSampleId)) {
            context.getOldSampleIds().remove(originalSampleId);
        }
        context.getSampleSetIds().add(originalSampleId);
        
        return SampleBreakDownResult.success(context);
    }
    
    /**
     * 处理非原样类型
     */
    private SampleBreakDownResult processNonOriginalSample(SampleBreakDownContext context, TestSampleReq sample, SampleType sampleType) {
        // 查找父样品
        SampleType parentSampleType = sampleService.findParentSampleType(context.getReqObject().getSamples(), sample);
        TestSampleReq parentSample = sampleService.findParentSample(context.getReqObject().getSamples(), context.getOldSamples(), sample);
        Boolean parentSampleIsNC = context.getOriginalSampleNCMap().get(parentSample.getSampleId());
        
        // 根据样品类型处理
        SampleBreakDownResult typeResult = processSampleByType(context, sample, sampleType, parentSampleType, parentSampleIsNC);
        if (!typeResult.isSuccess()) {
            return typeResult;
        }
        
        // 重新获取可能被修改的样品类型
        sampleType = SampleType.findType(sample.getSampleType());
        
        // 更新处理状态
        updateProcessingStatus(context, sample, parentSample);
        
        // 创建测试样品对象
        TestSampleInfoPO testSample = createTestSampleInfo(context, sample, sampleType);
        context.getTestSamples().add(testSample);
        
        // 处理样品语言信息
        processSampleLanguage(context, sample, testSample);
        
        context.getSampleSetIds().add(testSample.getID());
        
        return SampleBreakDownResult.success(context);
    }
    
    /**
     * 根据样品类型处理
     */
    private SampleBreakDownResult processSampleByType(SampleBreakDownContext context, TestSampleReq sample, 
                                                      SampleType sampleType, SampleType parentSampleType, Boolean parentSampleIsNC) {
        switch (sampleType) {
            case Sample: // 102
                if (parentSampleType == null || parentSampleType != SampleType.OriginalSample || parentSampleIsNC) {
                    return SampleBreakDownResult.fail(String.format("未找到对应的sample(%s) 的父样,或者父样已经Not Test", sample.getSampleNo()));
                }
                // 处理共享样品逻辑
                CustomResult shareResult = sampleService.shareSample(context.getReqObject(), 
                        context.getOldSampleGroupIds(), context.getTestSampleGroups(), context.getOldSampleMaps(), sample);
                if (!shareResult.isSuccess()) {
                    return SampleBreakDownResult.fail(shareResult.getMsg());
                }
                sampleType = SampleType.findType(sample.getSampleType());
                break;
                
            case SubSample: // 103
                if (parentSampleType == null || parentSampleType != SampleType.OriginalSample || parentSampleIsNC) {
                    return SampleBreakDownResult.fail(String.format("未找到对应的sample(%s) 的父样,或者父样已经Not Test", sample.getSampleNo()));
                }
                break;
                
            case MixSample: // 104 可以是任何样品
                CustomResult mixResult = sampleService.mixSample(context.getReqObject(), 
                        context.getOldSampleGroupIds(), context.getTestSampleGroups(), sample);
                if (!mixResult.isSuccess()) {
                    return SampleBreakDownResult.fail(mixResult.getMsg());
                }
                break;
                
            case ShareSample: // 105 该值只能是原样，且不能删除
                if (parentSampleType == null || parentSampleType != SampleType.OriginalSample || parentSampleIsNC) {
                    return SampleBreakDownResult.fail(String.format("未找到对应的sample(%s) 的父样,或者父样已经Not Test", sample.getSampleNo()));
                }
                CustomResult shareResult2 = sampleService.shareSample(context.getReqObject(), 
                        context.getOldSampleGroupIds(), context.getTestSampleGroups(), context.getOldSampleMaps(), sample);
                if (!shareResult2.isSuccess()) {
                    return SampleBreakDownResult.fail(shareResult2.getMsg());
                }
                sampleType= SampleType.findType(sample.getSampleType());
                break;
        }
        
        return SampleBreakDownResult.success(context);
    }
    
    /**
     * 更新处理状态
     */
    private void updateProcessingStatus(SampleBreakDownContext context, TestSampleReq sample, TestSampleReq parentSample) {
        // 校验通过，原DB数据就不需要处理了
        if (context.getOldSampleIds().containsKey(sample.getSampleId())) {
            context.getOldSampleIds().remove(sample.getSampleId());
        }
        if (context.getOldSampleIds().containsKey(parentSample.getSampleId())) {
            context.getOldSampleIds().remove(parentSample.getSampleId());
        }
        
        // 检查SampleId是否在当前OrderNo下
        if (!context.getOldSampleMaps().containsKey(sample.getSampleId())) {
            context.getNewSampleIds().add(sample.getSampleId());
        }
    }
    
    /**
     * 创建测试样品信息对象
     */
    private TestSampleInfoPO createTestSampleInfo(SampleBreakDownContext context, TestSampleReq sample, SampleType sampleType) {
        TestSampleInfoPO testSample = new TestSampleInfoPO();
        
        testSample.setID(sample.getSampleId());
        testSample.setOrderNo(context.getReqObject().getOrderNo());
        testSample.setCategory(sampleType.getCategoryChem());
        
        // 处理物理类别
        if (!sample.isChem() && (sampleType == SampleType.MixSample)) {
            testSample.setCategory(sampleType.getCategoryPhy());
        }
        
        testSample.setSampleParentID(sample.getSampleParentId());
        testSample.setSampleNo(sample.getSampleNo());
        testSample.setDescription(sample.getSampleDesc());
        testSample.setSampleType(sampleType.getSampleType());
        testSample.setSampleSeq(sample.getSampleSeq());
        testSample.setColor(sample.getColor());
        testSample.setEndUse(sample.getEndUse());
        testSample.setApplicable(sample.getNoTest());
        testSample.setSampleRemark(sample.getRemark());
        testSample.setOtherSampleInfo(sample.getOtherSampleInfo());
        testSample.setSourceType(SampleSourceTypeEnum.SGS.getCode());
        
        // 设置审计字段
        testSample.setActiveIndicator(true);
        testSample.setCreatedBy("BOM");
        testSample.setCreatedDate(DateUtils.getNow());
        testSample.setModifiedBy("BOM");
        testSample.setModifiedDate(DateUtils.getNow());
        
        return testSample;
    }
    
    /**
     * 处理样品语言信息
     */
    private void processSampleLanguage(SampleBreakDownContext context, TestSampleReq sample, TestSampleInfoPO testSample) {
        if (!SampleType.check(sample.getSampleType(), SampleType.MixSample)) {
            List<TestSampleLangInfoPO> langs = context.getLangMap().get(sample.getSampleId());
            context.getTestSampleLangs().addAll(testSampleLangService.getSampleLangInfoList(testSample, sample.getMaterials(), langs));
            
            if (!CollectionUtils.isEmpty(langs)) {
                context.getDelSampleLangIds().addAll(langs.stream().map(TestSampleLangInfoPO::getId).collect(Collectors.toList()));
            }
        }
    }
    
    /**
     * 处理需要删除的样品
     */
    private SampleBreakDownResult processDeletedSamples(SampleBreakDownContext context) {
        List<String> delSampleIds = Lists.newArrayList();
        Iterator<Map.Entry<String, SampleType>> sampleIds = context.getOldSampleIds().entrySet().iterator();
        
        while (sampleIds.hasNext()) {
            Map.Entry<String, SampleType> entry = sampleIds.next();
            TestSampleInfoPO oldSample = context.getOldSampleMaps().get(entry.getKey());
            String sampleNo = "";
            if (oldSample != null) {
                sampleNo = oldSample.getSampleNo();
            }
            
            // 当前原样如果是NC，就不用判断删除逻辑了，因为Bom不会回传NC样品
            Boolean isNC = context.getOriginalSampleNCMap().get(entry.getKey());
            isNC = isNC != null ? isNC : false;
            
            if ((entry.getValue() == null || entry.getValue() == SampleType.OriginalSample) && !isNC) {
                return SampleBreakDownResult.fail(String.format("Original Sample(%s)不能删除.", sampleNo));
            }
            delSampleIds.add(entry.getKey());
        }
        
        context.setDelSampleIds(delSampleIds);
        return SampleBreakDownResult.success(context);
    }
    
    /**
     * 处理需要删除的样品组
     */
    private void processDeletedSampleGroups(SampleBreakDownContext context) {
        List<String> delSampleGroupIds = Lists.newArrayList();
        Iterator<Map.Entry<String, String>> sampleGroupIds = context.getOldSampleGroupIds().entrySet().iterator();
        
        while (sampleGroupIds.hasNext()) {
            Map.Entry<String, String> entry = sampleGroupIds.next();
            if (StringUtils.isBlank(entry.getValue())) {
                continue;
            }
            delSampleGroupIds.add(entry.getValue());
        }
        
        context.setDelSampleGroupIds(delSampleGroupIds);
    }
    
    /**
     * 验证新样品ID的合法性
     */
    private SampleBreakDownResult validateNewSampleIds(SampleBreakDownContext context) {
        if (!context.getNewSampleIds().isEmpty()) {
            List<String> orderNos = testSampleMapper.getSampleIdList(Lists.newArrayList(context.getNewSampleIds()));
            if (orderNos != null && orderNos.size() > 0 && !orderNos.contains(context.getReqObject().getOrderNo())) {
                return SampleBreakDownResult.fail(String.format("当前SampleIds(%s)未在Order下.", 
                        StringUtils.join(context.getNewSampleIds(), ",")));
            }
        }
        
        return SampleBreakDownResult.success(context);
    }
    
    /**
     * 测试矩阵处理 - 管理样品与测试项目的关联关系
     * 
     * 业务流程：
     * 1. 调用原有的handleTestMatrix方法处理测试矩阵
     * 2. 处理需要删除的矩阵和PP关系
     * 3. 调用原有的handlePPSampleRel方法处理PP样品关系
     * 4. 调用原有的handleReportMatrixRel方法处理报告矩阵关系
     * 5. 将处理结果存储到上下文中
     */
    private SampleBreakDownResult processTestMatrix(SampleBreakDownContext context) {
        logger.info("【样品分解处理器】开始处理测试矩阵，订单号: {}", context.getReqObject().getOrderNo());
        
        try {
            // 1. 处理测试矩阵 - 复用原有的handleTestMatrix方法
            CustomResult matrixResult = sampleService.handleTestMatrix(
                context.getOrder().getID(),
                context.getReqObject(),
                context.getOldPPSampleRelMaps(),
                context.getSampleSetIds(),
                context.getOldTestMatrixMaps(),
                context.getOrderMatchedTestLineMaps()
            );
            
            if (!matrixResult.isSuccess()) {
                logger.error("【样品分解处理器】测试矩阵处理失败，订单号: {}, 错误: {}", 
                    context.getReqObject().getOrderNo(), matrixResult.getMsg());
                return SampleBreakDownResult.fail(matrixResult.getMsg());
            }
            
            // 将新创建的测试矩阵存储到上下文中
            @SuppressWarnings("unchecked")
            List<TestMatrixPO> newTestMatrixs = (List<TestMatrixPO>) matrixResult.getData();
            context.setNewTestMatrixs(newTestMatrixs != null ? newTestMatrixs : Lists.newArrayList());
            
            // 2. 处理需要删除的矩阵和PP关系 - 添加缺失的逻辑
            SampleBreakDownResult deleteResult = processMatrixDeletion(context);
            if (!deleteResult.isSuccess()) {
                return deleteResult;
            }
            
            // 3. 处理PP样品关系 - 复用原有的handlePPSampleRel方法
            List<com.sgs.otsnotes.dbstorages.mybatis.model.PPSampleRelationshipInfoPO> ppSampleRels = 
                sampleService.handlePPSampleRel(
                    context.getOldPPSampleRelMaps(),
                    context.getDelPPSampleRelIds(),
                    "BOM"
                );
            context.setPpSampleRels(ppSampleRels != null ? ppSampleRels : Lists.newArrayList());
            
            // 4. 处理报告矩阵关系 - 复用原有的handleReportMatrixRel方法
            List<com.sgs.otsnotes.dbstorages.mybatis.model.ReportMatrixRelationShipInfoPO> reportMatrixRels = 
                sampleService.handleReportMatrixRel(
                    context.getNewTestMatrixs(),
                    context.getReport().getID(),
                    "BOM"
                );
            context.setReportMatrixRels(reportMatrixRels != null ? reportMatrixRels : Lists.newArrayList());
            
            logger.info("【样品分解处理器】测试矩阵处理完成，订单号: {}, 新增矩阵: {}, PP关系: {}, 报告关系: {}", 
                context.getReqObject().getOrderNo(), 
                context.getNewTestMatrixs().size(),
                context.getPpSampleRels().size(),
                context.getReportMatrixRels().size());
            
            return SampleBreakDownResult.success(context);
            
        } catch (Exception e) {
            logger.error("【样品分解处理器】测试矩阵处理异常，订单号: {}", context.getReqObject().getOrderNo(), e);
            return SampleBreakDownResult.fail("测试矩阵处理异常: " + e.getMessage());
        }
    }
    
    /**
     * 处理矩阵删除逻辑
     * 
     * 业务流程：
     * 1. 获取已有结论的测试矩阵ID
     * 2. 遍历旧的测试矩阵，确定需要删除的矩阵
     * 3. 验证测试线状态和结论状态
     * 4. DIG-9600相关的矩阵校验
     */
    private SampleBreakDownResult processMatrixDeletion(SampleBreakDownContext context) {
        logger.debug("【样品分解处理器】开始处理矩阵删除逻辑，订单号: {}", context.getReqObject().getOrderNo());
        
        try {
            // 1. 获取已有结论的测试矩阵ID
            List<String> conclusionTestMatrixIds = conclusionInfoExtMapper.getConclusionTestMatrixIds(context.getOrder().getID());
            
            // 2. 初始化删除列表
            List<String> delPPSampleRelIds = Lists.newArrayList();
            List<String> delMatrixIds = Lists.newArrayList();
            
            // 3. 遍历旧的测试矩阵，确定需要删除的矩阵
            Iterator<Map.Entry<String, TestMatrixPO>> orderTestMatrixMaps = context.getOldTestMatrixMaps().entrySet().iterator();
            while (orderTestMatrixMaps.hasNext()) {
                Map.Entry<String, TestMatrixPO> entry = orderTestMatrixMaps.next();
                TestMatrixPO testMatrix = entry.getValue();
                TestLineInstancePO testLine = context.getOrderMatchedTestLineMaps().get(testMatrix.getTestLineInstanceID());
                
                if (testLine == null) {
                    continue;
                }
                
                // 验证测试线状态
                TestLineStatus testLineStatus = TestLineStatus.findStatus(testLine.getTestLineStatus());
                if (testLineStatus == null || !(testLineStatus == TestLineStatus.Typing || testLineStatus == TestLineStatus.DR)) {
                    return SampleBreakDownResult.fail(String.format("当前TestLine(%s)状态为(%s)不能删除Matrix.", 
                            testMatrix.getTestLineInstanceID(), testLineStatus));
                }
                
                // 如果样品ID在物理样品ID集合中，跳过
                if (context.getPhySampleIds().contains(testMatrix.getTestSampleID())) {
                    continue;
                }
                
                // 验证是否已有结论
                if (!conclusionTestMatrixIds.isEmpty() && conclusionTestMatrixIds.contains(testMatrix.getID())) {
                    return SampleBreakDownResult.fail(String.format("当前TestLine(%s)已入录conclusion不能删除Matrix(%s).", 
                            testMatrix.getTestLineInstanceID(), testMatrix.getID()));
                }
                
                delMatrixIds.add(testMatrix.getID());
            }
            
            // 4. DIG-9600 增matrix 校验
            if (CollectionUtil.isNotEmpty(context.getDelSampleIds())) {
                List<TestLineSampleTypeInfo> testLineSampleInfos = testMatrixMapper.getTestLineSampleCopyTestByIds(context.getDelSampleIds());
                if (CollectionUtil.isNotEmpty(testLineSampleInfos)) {
                    for (TestLineSampleTypeInfo testLineSampleInfo : testLineSampleInfos) {
                        if (TestLineStatus.check(testLineSampleInfo.getTestLineStatus(), TestLineStatus.Typing, TestLineStatus.DR)) {
                            continue;
                        }
                        TestLineStatus testLineStatus = TestLineStatus.findStatus(testLineSampleInfo.getTestLineStatus());
                        return SampleBreakDownResult.fail(String.format("当前TestLine(%s)状态为(%s)不能删除Matrix.",
                                testLineSampleInfo.getTestLineId(), testLineStatus));
                    }
                }
            }
            
            // 5. 将删除列表设置到上下文中
            context.setDelPPSampleRelIds(delPPSampleRelIds);
            context.setDelMatrixIds(delMatrixIds);
            
            logger.debug("【样品分解处理器】矩阵删除逻辑处理完成，订单号: {}, 删除矩阵数: {}", 
                    context.getReqObject().getOrderNo(), delMatrixIds.size());
            
            return SampleBreakDownResult.success(context);
            
        } catch (Exception e) {
            logger.error("【样品分解处理器】矩阵删除逻辑处理异常，订单号: {}", context.getReqObject().getOrderNo(), e);
            return SampleBreakDownResult.fail("矩阵删除逻辑处理异常: " + e.getMessage());
        }
    }
    
    /**
     * Slim子承包商验证和处理
     * 
     * 业务流程：
     * 1. 验证Slim子承包商数据的有效性
     * 2. 构造Slim子承包商数据
     * 3. 将数据存储到上下文中供后续事务处理使用
     * 
     * 复用原有的SampleService中的validateToSlimSubcontracts和convertToSlimSubcontracts方法
     */
    private SampleBreakDownResult processSlimSubcontracts(SampleBreakDownContext context) {
        logger.info("【样品分解处理器】开始处理Slim子承包商，订单号: {}", context.getReqObject().getOrderNo());
        
        try {
            // 1. 验证Slim子承包商数据的有效性 - 复用原有的validateToSlimSubcontracts方法
            CustomResult validateResult = sampleService.validateToSlimSubcontracts(context.getReqObject());
            if (!validateResult.isSuccess()) {
                logger.error("【样品分解处理器】Slim子承包商验证失败，订单号: {}, 错误: {}", 
                    context.getReqObject().getOrderNo(), validateResult.getMsg());
                return SampleBreakDownResult.fail(validateResult.getMsg());
            }
            
            // 2. 构造Slim子承包商数据 - 复用原有的convertToSlimSubcontracts方法
            List<SlimSubcontractPO> slimSubcontractList = 
                sampleService.convertToSlimSubcontracts(context.getOrder(), context.getReqObject());
            
            // 3. 将数据存储到上下文中供后续事务处理使用
            context.setSlimSubcontractList(slimSubcontractList != null ? slimSubcontractList : Lists.newArrayList());
            
            logger.info("【样品分解处理器】Slim子承包商处理完成，订单号: {}, 数据量: {}", 
                context.getReqObject().getOrderNo(), context.getSlimSubcontractList().size());
            
            return SampleBreakDownResult.success(context);
            
        } catch (Exception e) {
            logger.error("【样品分解处理器】Slim子承包商处理异常，订单号: {}", context.getReqObject().getOrderNo(), e);
            return SampleBreakDownResult.fail("Slim子承包商处理异常: " + e.getMessage());
        }
    }
    
    /**
     * 数据库事务操作
     * 
     * 执行所有数据库操作的事务，对应原始代码第776-824行的事务处理逻辑：
     * 1. 删除旧的测试矩阵、PP样品关系、样品组
     * 2. 删除旧的样品数据
     * 3. 插入新的样品、样品语言、样品组数据
     * 4. 插入新的PP样品关系、测试矩阵、报告矩阵关系
     * 5. 处理Slim子承包商数据
     */
    private SampleBreakDownResult executeDatabaseOperations(SampleBreakDownContext context) {
        logger.info("【样品分解处理器】开始执行数据库事务操作，订单号: {}", context.getReqObject().getOrderNo());
        
        try {
            int trans = transactionTemplate.execute((tranStatus) -> {
                int execNum = 1;
                
                // 1. 删除旧的测试矩阵
                if (!context.getDelMatrixIds().isEmpty()) {
                    execNum += testMatrixMapper.batchDelete(context.getDelMatrixIds());
                    logger.debug("【样品分解处理器】删除测试矩阵，数量: {}", context.getDelMatrixIds().size());
                }
                
                // 2. 删除旧的PP样品关系
                if (!context.getDelPPSampleRelIds().isEmpty()) {
                    execNum += testSampleMapper.delPPSampleRelInfo(context.getDelPPSampleRelIds());
                    logger.debug("【样品分解处理器】删除PP样品关系，数量: {}", context.getDelPPSampleRelIds().size());
                }
                
                // 3. 删除旧的样品组
                if (!context.getDelSampleGroupIds().isEmpty()) {
                    execNum += testSampleGroupMapper.delTestSampleGroupInfo(context.getDelSampleGroupIds());
                    logger.debug("【样品分解处理器】删除样品组，数量: {}", context.getDelSampleGroupIds().size());
                }
                
                // 4. 删除旧的样品数据（检查原样是否传送过来）
                if (!context.getDelSampleIds().isEmpty()) {
                    // 还要考虑一种情况，Sample没有删除，只是没有Assign对应的TestLine
                    execNum += testMatrixMapper.delTestMatrixBySampleIds(context.getDelSampleIds());
                    logger.info("【样品分解处理器】订单号: {} 进行db操作，delSampleId: [{}]", 
                            context.getOrder().getOrderNo(), context.getDelSampleIds());
                    execNum += testSampleMapper.batchDelete(context.getDelSampleIds());
                    logger.debug("【样品分解处理器】删除样品，数量: {}", context.getDelSampleIds().size());
                }
                
                // 5. 插入新的样品数据
                if (!context.getTestSamples().isEmpty() && testSampleMapper.batchInsert(context.getTestSamples()) <= 0) {
                    tranStatus.setRollbackOnly(); // 回滚事务
                    logger.error("【样品分解处理器】插入样品数据失败");
                    return execNum;
                }
                
                // 6. 插入新的样品语言数据
                if (!context.getTestSampleLangs().isEmpty() && testSampleLangMapper.batchInsert(context.getTestSampleLangs()) <= 0) {
                    tranStatus.setRollbackOnly(); // 回滚事务
                    logger.error("【样品分解处理器】插入样品语言数据失败");
                    return execNum;
                }
                
                // 7. 删除旧的样品语言数据
                if (!context.getDelSampleLangIds().isEmpty() && testSampleLangMapper.deleteByIds(context.getDelSampleLangIds()) <= 0) {
                    tranStatus.setRollbackOnly();
                    logger.error("【样品分解处理器】删除样品语言数据失败");
                    return execNum;
                }
                
                // 8. 插入新的样品组数据
                if (!context.getTestSampleGroups().isEmpty() && testSampleGroupMapper.batchInsert(context.getTestSampleGroups()) <= 0) {
                    tranStatus.setRollbackOnly(); // 回滚事务
                    logger.error("【样品分解处理器】插入样品组数据失败");
                    return execNum;
                }
                
                // 9. 插入新的PP样品关系数据
                if (!context.getPpSampleRels().isEmpty() && testSampleMapper.batchSavePPSampleRelInfo(context.getPpSampleRels()) <= 0) {
                    tranStatus.setRollbackOnly(); // 回滚事务
                    logger.error("【样品分解处理器】插入PP样品关系数据失败");
                    return execNum;
                }
                
                // 10. 插入新的测试矩阵数据
                if (!context.getNewTestMatrixs().isEmpty() && testMatrixMapper.batchInsert(context.getNewTestMatrixs()) <= 0) {
                    tranStatus.setRollbackOnly(); // 回滚事务
                    logger.error("【样品分解处理器】插入测试矩阵数据失败");
                    return execNum;
                }
                
                // 11. 插入新的报告矩阵关系数据
                if (!context.getReportMatrixRels().isEmpty() && reportMapper.batchSaveReportMatrixRelInfo(context.getReportMatrixRels()) <= 0) {
                    tranStatus.setRollbackOnly(); // 回滚事务
                    logger.error("【样品分解处理器】插入报告矩阵关系数据失败");
                    return execNum;
                }
                
                // 12. 处理procedure & scheme 的临表数据（Slim子承包商）
                if (!CollectionUtils.isEmpty(context.getReqObject().getSlimMapping())) {
                    // 先删除旧的Slim子承包商数据
                    slimSubContractExtMapper.deleteSlimSubcontractWithoutToSlim(context.getReqObject().getOrderNo());
                    
                    // 插入新的Slim子承包商数据
                    if (!CollectionUtils.isEmpty(context.getSlimSubcontractList())) {
                        slimSubContractExtMapper.batchInsert(context.getSlimSubcontractList());
                        logger.debug("【样品分解处理器】插入Slim子承包商数据，数量: {}", context.getSlimSubcontractList().size());
                    }
                }
                
                logger.info("【样品分解处理器】数据库事务操作执行完成，订单号: {}, 操作数: {}", 
                        context.getReqObject().getOrderNo(), execNum);
                return execNum;
            });
            
            // 验证事务执行结果
            if (trans <= 0) {
                logger.error("【样品分解处理器】数据库事务执行失败，订单号: {}", context.getReqObject().getOrderNo());
                return SampleBreakDownResult.fail("数据库事务执行失败");
            }
            
            logger.info("【样品分解处理器】数据库事务操作成功，订单号: {}, 执行数: {}", 
                    context.getReqObject().getOrderNo(), trans);
            return SampleBreakDownResult.success(context);
            
        } catch (Exception e) {
            logger.error("【样品分解处理器】数据库事务操作异常，订单号: {}", context.getReqObject().getOrderNo(), e);
            return SampleBreakDownResult.fail("数据库事务操作异常: " + e.getMessage());
        }
    }
    
    /**
     * 后处理操作
     * 
     * 执行数据库事务成功后的后续处理，对应原始代码第825行的this.matrixUpdateCondition调用：
     * 1. 调用matrixUpdateCondition方法更新测试条件
     * 2. 执行其他必要的后处理逻辑
     */
    private SampleBreakDownResult executePostProcessing(SampleBreakDownContext context) {
        logger.info("【样品分解处理器】开始执行后处理操作，订单号: {}", context.getReqObject().getOrderNo());
        
        try {
            // 1. 调用matrixUpdateCondition方法更新测试条件 - 复用原有的matrixUpdateCondition方法
            sampleService.matrixUpdateCondition(context.getOrder().getID(), context.getOrderTestLines());
            logger.debug("【样品分解处理器】测试条件更新完成，订单号: {}", context.getReqObject().getOrderNo());
            
            // 2. 其他后处理逻辑可以在这里添加
            
            logger.info("【样品分解处理器】后处理操作完成，订单号: {}", context.getReqObject().getOrderNo());
            return SampleBreakDownResult.success(context);
            
        } catch (Exception e) {
            logger.error("【样品分解处理器】后处理操作异常，订单号: {}", context.getReqObject().getOrderNo(), e);
            return SampleBreakDownResult.fail("后处理操作异常: " + e.getMessage());
        }
    }
} 