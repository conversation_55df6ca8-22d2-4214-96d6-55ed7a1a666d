package com.sgs.otsnotes.domain.service.subcontract.starlims.handler.biz.process.common;

import com.sgs.otsnotes.core.util.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.otsnotes.domain.service.subcontract.StarLimsCommonService;
import com.sgs.otsnotes.domain.service.subcontract.starlims.annotation.StarLimsReportProcessor;
import com.sgs.otsnotes.domain.service.subcontract.starlims.context.StarLimsReportContext;
import com.sgs.otsnotes.domain.service.subcontract.starlims.handler.AbstractStarLimsReportHandler;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.otsnotes.facade.model.req.starlims.receive.ReceiveStarLimsReportDocBodyReq;
import com.sgs.testdatabiz.facade.model.req.starlims.ReceiveStarLimsReportReq;

import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * 通用RD报告数据保存处理器
 * 适用于SL、GPO等所有StarLims产品线
 * 只负责调用RD服务保存报告数据
 * 
 * 该处理器应在SL和GPO的责任链中都能被复用。
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@StarLimsReportProcessor(
    productLines = {ProductLineType.ALL},
    order = 480,
    productLineOrders = {"SL:950"},
    description = "通用RD报告数据保存处理器",
    type = StarLimsReportProcessor.ProcessorType.PROCESS,
    group = "rd-process"
)
@Component
@Slf4j
public class CommonRDReportSaveHandler extends AbstractStarLimsReportHandler {

    @Autowired
    private StarLimsCommonService starLimsCommonService;

    /**
     * 处理器主逻辑：调用RD服务保存Starlims报告数据
     * @param context 责任链上下文
     * @return true-处理成功，false-处理失败
     */
    @Override
    protected boolean doHandle(StarLimsReportContext context) {
        // 获取请求参数
        ReceiveStarLimsReportDocBodyReq req = context.getRequest();

        // 构建RD服务需要的请求对象
        ReceiveStarLimsReportReq rdReq = new ReceiveStarLimsReportReq();
        BeanUtils.copyProperties(req, rdReq);
        // 如有特殊字段补充，可在此处添加
        rdReq.setExcludeCustomerInterface(String.valueOf(Optional.ofNullable(req.getReportTags()).orElse(0)));
        rdReq.setLanguageId(req.getLanguageId());
        rdReq.setParentOrderNo(req.getOrderNo());
        rdReq.setReportNo(context.getReportNo());
        rdReq.setSubContractNo(req.getSubContractNo());
        rdReq.setLabCode(context.getOrderInfo().getLabCode());
        rdReq.setExternalId(req.getExternalId());
        rdReq.setCompletedDate(DateUtils.parseDate(req.getApproveDate()));

        // 调用RD服务保存报告数据
        CustomResult customResult = starLimsCommonService.receiveReportDocV2(rdReq);
        if (!customResult.isSuccess()) {
            log.error("RD服务保存Starlims报告数据失败: orderNo={},reportNo={},subContractNo={},msg={}",req.getOrderNo(),context.getReportNo(),req.getSubContractNo(),customResult.getMsg());
            context.getResult().fail(customResult.getMsg());
            // 责任链外部应在事务中调用，遇到fail时回滚
            return false;
        }
        return true;
    }
} 