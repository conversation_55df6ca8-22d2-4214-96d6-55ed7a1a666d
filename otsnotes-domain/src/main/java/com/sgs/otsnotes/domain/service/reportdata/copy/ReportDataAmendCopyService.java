package com.sgs.otsnotes.domain.service.reportdata.copy;

import com.sgs.otsnotes.domain.service.reportdata.ReportDataDealService;
import com.sgs.otsnotes.integration.FrameWorkClient;
import com.sgs.otsnotes.integration.v2.ReportDataClientV2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class ReportDataAmendCopyService extends ReportDataDealService {
    private static final Logger logger = LoggerFactory.getLogger(ReportDataAmendCopyService.class);



    public ReportDataAmendCopyService(ReportDataClientV2 subContractDataClient, FrameWorkClient frameWorkClient) {
        super(subContractDataClient, frameWorkClient);
    }

}
