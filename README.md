# otsnotes-service 项目总览

---

## 1. 项目概述
otsnotes-service 是 SGS 集团用于测试数据管理的服务系统，支撑实验室测试、分包、报告、数据流转等核心业务流程。系统以高可用、高扩展、易维护为目标，采用分层架构和领域驱动设计（DDD），实现了复杂业务场景下的高内聚低耦合。

详细说明请见：[doc/项目概述.md](doc/项目概述.md)

---

## 2. 系统架构
系统采用微服务架构，核心模块包括：核心服务（core）、领域服务（domain）、数据存储（dbstorages）、接口层（facade）、集成层（integration）、Web前端（web）等。各模块职责分明，支持横向扩展和独立部署。

详细说明请见：[doc/系统架构.md](doc/系统架构.md)

---

## 3. 项目结构
- otsnotes-core：核心工具与基础设施
- otsnotes-domain：领域服务与业务逻辑
- otsnotes-dbstorages：MyBatis数据访问与存储
- otsnotes-facade：接口定义层
- otsnotes-facade-impl：接口实现层
- otsnotes-facade-model：DTO/VO/枚举/业务对象
- otsnotes-infra：基础设施与仓储实现
- otsnotes-integration：外部系统集成
- otsnotes-mybatis-generator：代码生成
- otsnotes-test：测试模块
- otsnotes-web：Web前端与控制器

详细说明请见：[doc/项目结构.md](doc/项目结构.md)

---

## 4. 业务视角
系统覆盖分包管理、报告流转、样品管理、结论管理、外部系统对接等核心业务。各业务能力详见业务功能说明文档。

详细说明请见：[doc/业务功能说明.md](doc/业务功能说明.md)

---

## 5. 技术视角
- 技术栈：Spring Boot、MyBatis、Dubbo、Redis、Fastjson、Guava、Lombok 等
- 架构风格：分层架构、领域驱动设计（DDD）、微服务
- 安全：遵循OWASP最佳实践，接口鉴权、数据校验、日志审计
- 性能：缓存优化、批量处理、异步任务

详细说明请见：[doc/技术视角.md](doc/技术视角.md)

---

## 6. 数据视角
- 主要业务实体：订单、分包、样品、报告、结论、外部关系等
- 关系型数据库为主，部分缓存与NoSQL
- 详细ER图、数据流说明见数据文档

详细说明请见：[doc/数据视角.md](doc/数据视角.md)

---

## 7. 部署视角
- 支持Docker容器化部署
- 支持Jenkins自动化CI/CD
- 依赖MySQL、Redis、Dubbo注册中心等基础设施
- 详细部署流程、环境变量说明见部署文档

详细说明请见：[doc/部署视角.md](doc/部署视角.md)

---

## 8. 详细能力与API文档
- 业务能力说明：[doc/业务功能说明.md](doc/业务功能说明.md)
- 领域服务能力API：[doc/api/StarLimsCommonService.md](doc/api/StarLimsCommonService.md) 等

---

> 本文档为自动生成，详细内容请查阅各分章节文档。 