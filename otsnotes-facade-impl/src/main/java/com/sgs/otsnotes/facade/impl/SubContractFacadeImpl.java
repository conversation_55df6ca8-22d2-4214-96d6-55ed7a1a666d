package com.sgs.otsnotes.facade.impl;

import com.sgs.framework.log.annotation.AccessLog;
import com.sgs.otsnotes.core.config.DisConf;
import com.sgs.otsnotes.domain.service.SubContractService;
import com.sgs.otsnotes.domain.service.subcontract.SlimService;
import com.sgs.otsnotes.domain.service.subcontract.StarLimsCommonService;
import com.sgs.otsnotes.domain.service.subcontract.hostsubcontract.HostSubcontractService;
import com.sgs.otsnotes.domain.service.subcontract.starlims.StarLimsReportReceiveService;
import com.sgs.otsnotes.facade.SubContractFacade;
import com.sgs.otsnotes.facade.SubReportService;
import com.sgs.otsnotes.facade.model.common.BaseResponse;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractTestLineDTO;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractTestLineSampleDTO;
import com.sgs.otsnotes.facade.model.info.subcontract.ExternalRelSubContractDTO;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractTestMatrixInfo;
import com.sgs.otsnotes.facade.model.req.DeleteSubReportByIdReq;
import com.sgs.otsnotes.facade.model.req.SubContractReq;
import com.sgs.otsnotes.facade.model.req.SyncOrderReq;
import com.sgs.otsnotes.facade.model.req.UpdateSubContractReq;
import com.sgs.otsnotes.facade.model.req.report.BaseReportReq;
import com.sgs.otsnotes.facade.model.req.starlims.receive.ReceiveStarLimsReportDocBodyReq;
import com.sgs.otsnotes.facade.model.req.starlims.receive.ReceiveStarLimsUpdateTimeBodyReq;
import com.sgs.otsnotes.facade.model.req.starlims.receive.StarLimsSubReportCancelReq;
import com.sgs.otsnotes.facade.model.req.subcontract.*;
import com.sgs.otsnotes.facade.model.rsp.starlims.ChemPpArtifactTestLineInfoRsp;
import com.sgs.otsnotes.facade.model.rsp.subcontract.CheckSubContractStatusChangeRsp;
import com.sgs.otsnotes.facade.model.rsp.subcontract.ImportSubcontractReportDataRsp;
import com.sgs.otsnotes.subcontract.app.tostarlims.matrix.bizprocess.MatrixBuilderBizProcess;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.InputStreamReq;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.SubcontractNoReq;
import com.sgs.testdatabiz.facade.model.rsp.subcontract.ReportDataRsp;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component("subContractFacade")
public class SubContractFacadeImpl implements SubContractFacade {
    @Autowired
    private SubContractService subContractService;
    @Autowired
    private StarLimsCommonService starLimsCommonService;
    @Autowired
    private StarLimsReportReceiveService starLimsReportReceiveService;
    @Autowired
    private SlimService slimService;
    @Autowired
    private SubContractFacade subContractFacade;

    @Autowired
    private HostSubcontractService hostSubcontractService;

    @Resource
    private SubReportService subReportService;

    @Autowired
    private DisConf disConf;

    @Resource
    private MatrixBuilderBizProcess matrixBuilderBizProcess;


    /**
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse<SubContractInfo> getSubContractInfo(GetSubContractInfo reqObject){
        return BaseResponse.newInstance(subContractService.getSubContractInfo(reqObject));
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @AccessLog(bizNo = "${#arg?.subContractId?:#arg?.orderNo}")
    @Override
    public BaseResponse saveSubContract(SaveSubContractReq reqObject){
        return BaseResponse.newInstance(subContractService.saveSubContract(reqObject));
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @AccessLog(bizNo = "${#arg?.subContractNo}")
    @Override
    public BaseResponse bindSubContract(BindSubContractReq reqObject){
        return BaseResponse.newInstance(subContractService.bindSubContract(reqObject));
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @AccessLog(bizNo = "${#arg?.subContractNo}")
    @Override
    public BaseResponse unBindSubContract(BindSubContractReq reqObject) {
        return BaseResponse.newInstance(subContractService.unBindSubContract(reqObject));
    }

    /**
     *
     * @return
     */
    @Override
    public BaseResponse<List<ExternalRelSubContractDTO>> getExternalSubContractInfo() {
        return BaseResponse.newInstance(subContractService.getExternalSubContractInfo());
    }

    /**
     * 查询分包列表
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse querySubContractList(QuerySubContractListReq reqObject) {
        return BaseResponse.newInstance(subContractService.querySubContractList(reqObject));
    }

    /**
     * 查询分包类型
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse querySubContractServiceType(QuerySubContractServiceTypeReq reqObject) {
        return BaseResponse.newInstance(subContractService.querySubContractServiceType(reqObject));
    }

    /**
     * 接收startDate
     * @param reqObject
     * @return
     */
    @AccessLog(bizNo = "${#arg?.subContractNo}")
    @Override
    public BaseResponse updateTimeTrack(ReceiveStarLimsUpdateTimeBodyReq reqObject) {
        return BaseResponse.newInstance(starLimsCommonService.updateTimeTrack(reqObject));
    }

    /**
     * 接收报告
     * @param reqObject
     * @return
     */
    @AccessLog(bizNo = "${#arg?.subContractNo}")
    @Override
    public BaseResponse receiveReportDoc(ReceiveStarLimsReportDocBodyReq reqObject) {
        if (disConf.isSwitchNewStarlimsReceiveReport()) {
            return BaseResponse.newInstance(starLimsReportReceiveService.receiveReportDoc(reqObject));
        }
        return BaseResponse.newInstance(starLimsCommonService.receiveReportDoc(reqObject));
    }

    /**
     * 根据分包id查询分包数据
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse querySubContractBySubContractId(SubContractReq reqObject) {
        return BaseResponse.newInstance(subContractService.querySubContractBySubContractId(reqObject));
    }

    /**
     * 取消subcontract
     *
     * @param reqObject
     * @return
     */
    @AccessLog(bizNo = "${#arg?.subContractId}")
    @Override
    public BaseResponse cancelSubcontract(SubContractReq reqObject) {
        return BaseResponse.newInstance(subContractService.cancelSubcontract(reqObject));
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @AccessLog(bizNo = "${#arg?.subContractId}")
    @Override
    public BaseResponse toSlim(SubContractReq reqObject) {
        return BaseResponse.newInstance(slimService.toSlim(reqObject));
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @AccessLog(bizNo = "${#arg?.orderNo}")
    @Override
    public BaseResponse syncSubContractInfo(SyncOrderReq reqObject) {
        return subContractFacade.syncSubContractInfo(reqObject);
    }
    @AccessLog(bizNo = "${#arg?.subContractNo}")
    @Override
    public BaseResponse updateSubContractData(UpdateSubContractReq reqObject) {
        return BaseResponse.newInstance(subContractService.updateSubContractData(reqObject));
    }

    /**
     * 根据ID刪除SubReport信息
     *
     * @param reqObject
     * @return
     */
    @AccessLog(bizNo = "${#arg?.id}")
    @Override
    public BaseResponse deleteSubReportById(DeleteSubReportByIdReq reqObject) {
        return BaseResponse.newInstance(subContractService.deleteSubReportById(reqObject));
    }
    @AccessLog(bizNo = "${#arg?.subContractNo}")
    @Override
    public BaseResponse saveSubContractReport(SubContractReportReq reqObject) {
        return BaseResponse.newInstance(subContractService.saveSubContractReport(reqObject));
    }
    @AccessLog(bizNo = "${#arg?.objectNo}")
    @Override
    public BaseResponse reviseSubReport(ReviseSubReportReq reqObject) {
        return BaseResponse.newInstance(subContractService.reviseSubReport(reqObject));
    }
    @AccessLog(bizNo = "${#arg?.subContractNo}")
    @Override
    public BaseResponse startSubContract(StartSubContractReq reqObject) {
        return BaseResponse.newInstance(subContractService.startSubContract(reqObject));
    }

    @Override
    public BaseResponse querySubReportByReportId(BaseReportReq req) {
        return BaseResponse.newInstance(subContractService.querySubReportByReportId(req));
    }
    @AccessLog(bizNo = "${#arg?.reportNo}")
    @Override
    public BaseResponse saveSubTestData(SaveSubTestDataReq req) {
        return BaseResponse.newInstance(subContractService.saveSubTestData(req));
    }

    @Override
    public BaseResponse<List<SubContractTestLineSampleDTO>> querySubTestLineSample(GetSubContractInfo reqObject) {
        return BaseResponse.newInstance(subContractService.querySubTestLineSample(reqObject));
    }
    @AccessLog(bizNo = "${#arg?.subcontractNo}")
    @Override
    public BaseResponse updateSubcontractType(UpdateSubcontractTypeInfo reqObject){
        return BaseResponse.newInstance(subContractService.updateSubcontractType(reqObject));
    }

    @Override
    public BaseResponse<List<ChemPpArtifactTestLineInfoRsp>> getSubContractChemTestMatrixList(SubContractChemTestMatrixReq reqObject){
        return BaseResponse.newInstance(starLimsCommonService.getChemTestMatrixList(reqObject.getOrderNo(),reqObject.getSubContractNo(),reqObject.getProductLineCode()));
    }

    @Override
    public BaseResponse<List<SubContractTestMatrixInfo>> getSubContractTestMatrixList(SubContractChemTestMatrixReq reqObject){
        return BaseResponse.newInstance(starLimsCommonService.getSubContractTestMatrixList(reqObject));
    }

    @Override
    public BaseResponse<XSSFWorkbook> downLoadTemplate(SubContractReportReq reqObject) {
        return BaseResponse.newInstance(subContractService.downLoadTemplate(reqObject));
    }

    @Override
    public BaseResponse uploadSubTemplate(InputStreamReq reqObject) {
        return BaseResponse.newInstance(subContractService.uploadSubTemplate(reqObject));
    }

    @Override
    public BaseResponse<ReportDataRsp> querySubTestData(SubcontractNoReq reqObject) {
        return BaseResponse.newInstance(subContractService.querySubTestData(reqObject));
    }

    @Override
    public BaseResponse<List<SubContractTestLineDTO>> getSubContractTestLineInfo(GetSubContractInfoListReq reqObject) {
        return BaseResponse.newInstance(subContractService.getSubContractTestLineInfo(reqObject));
    }
    @AccessLog(bizNo = "${#arg?.subcontractNo}")
    @Override
    public BaseResponse<Boolean> cancelSubReport(StarLimsSubReportCancelReq subReportCancelReq) {
        return BaseResponse.newInstance(starLimsCommonService.cancelSubReport(subReportCancelReq));
    }

    @Override
    public BaseResponse<ImportSubcontractReportDataRsp> importSubcontractReportData(ImportSubcontractReportDataReq reqObject) {
        return BaseResponse.newInstance(hostSubcontractService.importSubcontractReportData(reqObject));
    }

    /**
     * 检查分包状态变更
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse<CheckSubContractStatusChangeRsp> checkSubContractStatusChange(CheckSubContractStatusChangeReq reqObject) {
        return BaseResponse.newInstance(subContractService.checkSubContractStatusChange(reqObject));
    }




}
