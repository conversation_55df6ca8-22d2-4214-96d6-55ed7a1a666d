# PPTestLine对象状态管理规则文档

## 文档概述

本文档基于实际代码分析，详细梳理了PPTestLine（Protocol Package TestLine）对象在系统中的状态管理规则，严格基于代码事实，不包含推测性内容。

**【事实】** 基于以下核心文件的实际代码分析：
- `TestLineStatus.java` - 状态枚举定义（PP测试线复用相同状态）
- `TestLineModuleType.java` - 模块类型定义（包含PP专用操作）
- `TestLineStatusService.java` - 状态管理核心服务
- `PPTestLineRelNewService.java` - PP测试线关系管理服务
- `ProtocolPackageTestLineRelationshipService.java` - PP测试线关系处理服务

## PPTestLine与普通TestLine的关系

### 核心事实

**【事实】** 基于代码分析，PPTestLine与普通TestLine的实际差异：

1. **状态枚举相同**: PPTestLine使用与普通TestLine完全相同的TestLineStatus枚举
2. **专用操作类型**: 拥有AddPPTestLine(2)和PPSummaryDataEntry(34)两个PP专用操作
3. **关联关系**: 通过PPTestLineRelationship表建立PP关联关系
4. **数据来源**: 依赖TRIMS本地化系统获取PP相关数据

### 状态枚举定义

**【事实】** PPTestLine使用与普通TestLine完全相同的TestLineStatus枚举：

| 状态值 | 状态名称 | 英文描述 | 中文含义 |
|--------|----------|----------|----------|
| 701 | Typing | Typing | 录入中 |
| 702 | Submit | Submitted | 已提交 |
| 703 | Completed | Completed | 已完成 |
| 704 | SubContracted | Subcontracted | 已分包 |
| 705 | Entered | Entered | 已录入 |
| 706 | Cancelled | Cancelled | 已取消 |
| 707 | NC | Not Test | 不测试 |
| 708 | DR | Document Review | 待复核 |
| 709 | NA | NA | 不适用 |

## PP专用模块操作类型

**【事实】** 基于TestLineModuleType枚举，PP测试线拥有以下专用操作：

| 操作类型 | 操作值 | 操作说明 | 代码中的实际处理 |
|----------|--------|----------|------------------|
| **AddPPTestLine** | 2 | Add PP TL | 添加PP测试线到订单 |
| **PPSummaryDataEntry** | 34 | PP Summary DataEntry | PP汇总数据录入操作 |

### PP操作的实际状态转换逻辑

**【事实】** 基于TestLineStatusService.java中的实际代码逻辑：

#### PPSummaryDataEntry操作
- **状态转换**: 任意状态 → Entered (705)
- **代码位置**: TestLineStatusService.java 第158行
- **处理逻辑**: 与ReTest、DataEntrySave操作相同，都将状态设置为Entered

## PPTestLine实际状态转换规则

**【事实】** 基于TestLineStatusService.java中的实际代码逻辑：

### 1. PP专用操作的状态转换

#### 1.1 PPSummaryDataEntry操作
- **对象**: TestLine（PP类型）
- **状态转换**: 任意状态 → Entered (705)
- **代码实现**:
  ```java
  case PPSummaryDataEntry:
      // TestLine状态为Entered
      testLineStatus = TestLineStatus.Entered;
      break;
  ```
- **Matrix处理**: 设置oldMatrixStatus为原TestLine状态
- **代码位置**: TestLineStatusService.java 第158行和第337行

#### 1.2 AddPPTestLine操作
- **说明**: 此操作在TestLineModuleType中定义，但在TestLineStatusService中没有专门的状态转换逻辑
- **【推理】**: 可能在其他服务中处理PP测试线的创建逻辑

### 2. PP测试线的通用操作

**【事实】** PP测试线支持所有标准的TestLine操作，状态转换逻辑与普通测试线完全相同：

#### 2.1 数据录入操作
- **DataEntrySave**: 任意状态 → Entered (705)
- **DataEntrySumbit**: 任意状态 → Submit (702)
- **ReTest**: 任意状态 → Entered (705)

#### 2.2 验证操作
- **Validate**: Submit (702) → Completed (703)

#### 2.3 变更操作
- **Change**: Entered/Submit/Completed → Entered (705)
- **ChangeUpdateCondition**: Submit/Completed → Entered (705)
- **ChangeUpdateStandard**: Submit/Completed → Entered (705)
- **ChangeAddMatrix**: Submit/Completed → Entered (705)

#### 2.4 矩阵操作
- **ChangeCancelMatrix**: 根据Matrix状态动态决定目标状态

## PP关联关系管理

**【事实】** 基于ProtocolPackageTestLineRelationshipService.java的实际代码：

### SubPpRelSeq处理逻辑
- **对象**: PPTestLineRelationshipInfoPO
- **处理方法**: buildSubPpSeq()
- **实际逻辑**:
  ```java
  // 默认设置为0
  ppTestLineRel.setSubPpRelSeq(0);
  if (NumberUtil.equals(ppTestLineRel.getPpBaseId(), ppTestLineRel.getRootPpBaseId())) {
      continue; // Root PP情况，跳过
  }
  // 通过TRIMS本地化接口获取SubPP关系
  List<SubPpArtifactRelRsp> subPpArtifactRel = ppArtifactRelClient.findSubPpArtifactRel(
      ppTestLineRel.getPpBaseId(), ppTestLineRel.getRootPpBaseId());
  if (CollectionUtils.isNotEmpty(subPpArtifactRel)) {
      ppTestLineRel.setSubPpRelSeq(subPpArtifactRel.get(0).getTestLineSeq());
  }
  ```

## PP数据查询服务

**【事实】** 基于PPTestLineRelNewService.java的实际功能：

### 主要功能
- **queryPPTLSampleByReportIdNew()**: 通过API调用替代数据库JOIN的方式获取PP测试线样品数据
- **数据来源**:
  - PpClient: 获取PP基础信息
  - CitationClient: 获取引用信息
  - PpArtifactRelClient: 获取PP Artifact关系
- **性能优化**: 使用新的API调用方式替代复杂的数据库JOIN操作

### 数据比对功能
- **compareAndRecordResults()**: 比较原有方法和新方法的结果
- **目的**: 验证新API调用方式的正确性和性能

## PP与普通TestLine的实际差异总结

**【事实】** 基于代码分析的真实差异：

| 对比维度 | 普通TestLine | PPTestLine |
|----------|-------------|------------|
| **状态枚举** | TestLineStatus | TestLineStatus（完全相同） |
| **状态转换逻辑** | TestLineStatusService处理 | TestLineStatusService处理（相同逻辑） |
| **专用操作** | 无 | PPSummaryDataEntry(34) |
| **创建操作** | AddTestLine(1) | AddPPTestLine(2) |
| **关联关系** | 简单测试线关系 | PPTestLineRelationship关联PP |
| **数据查询** | 直接数据库查询 | PPTestLineRelNewService提供API查询 |
| **层级处理** | 无 | ProtocolPackageTestLineRelationshipService处理SubPpRelSeq |
| **外部依赖** | 本地数据 | 依赖TRIMS本地化系统 |

## 关键代码位置

**【事实】** PP测试线相关的核心代码位置：

### 状态管理
- **TestLineStatusService.java 第158行**: PPSummaryDataEntry状态转换逻辑
- **TestLineStatusService.java 第337行**: PPSummaryDataEntry的Matrix处理逻辑

### PP关联关系
- **ProtocolPackageTestLineRelationshipService.java**: buildSubPpSeq()方法处理PP层级关系

### PP数据查询
- **PPTestLineRelNewService.java**: queryPPTLSampleByReportIdNew()方法提供新的查询方式

### 枚举定义
- **TestLineModuleType.java 第14行**: AddPPTestLine(2)定义
- **TestLineModuleType.java 第46行**: PPSummaryDataEntry(34)定义

## 实际业务场景

**【事实】** 基于代码分析的实际业务场景：

### 场景1: PP汇总数据录入
- **操作**: PPSummaryDataEntry
- **状态变更**: 任意状态 → Entered (705)
- **实际处理**: 与ReTest、DataEntrySave操作使用相同的状态转换逻辑
- **Matrix处理**: 保存原TestLine状态到oldMatrixStatus

### 场景2: PP层级关系建立
- **服务**: ProtocolPackageTestLineRelationshipService
- **方法**: buildSubPpSeq()
- **处理逻辑**:
  - Root PP: SubPpRelSeq设置为0
  - Sub PP: 通过TRIMS接口获取TestLineSeq并设置到SubPpRelSeq

### 场景3: PP数据查询优化
- **服务**: PPTestLineRelNewService
- **方法**: queryPPTLSampleByReportIdNew()
- **优化方式**: 使用API调用替代复杂的数据库JOIN操作
- **性能对比**: 提供compareAndRecordResults()方法进行性能对比

## 总结

**【事实】** 基于实际代码分析，PPTestLine状态管理的核心特点：

1. **状态管理统一**: PPTestLine使用与普通TestLine完全相同的状态枚举和转换逻辑
2. **专用操作有限**: 仅有PPSummaryDataEntry一个PP专用的状态转换操作
3. **关联关系复杂**: 通过PPTestLineRelationship建立与PP的关联，支持Root PP和Sub PP层级
4. **外部系统依赖**: 依赖TRIMS本地化系统获取PP相关数据
5. **查询优化**: 提供专门的查询服务优化PP数据获取性能

### 开发注意事项

1. **状态转换**: PP测试线的状态转换逻辑与普通测试线完全一致，无需特殊处理
2. **层级关系**: 需要正确处理PP的层级关系，特别是SubPpRelSeq的计算
3. **外部接口**: 需要处理TRIMS本地化接口调用的异常情况
4. **性能考虑**: PP数据查询涉及多个外部接口调用，需要考虑性能优化

---

**文档生成时间**: 2025-09-28
**基于代码版本**: 当前项目代码
**维护责任**: PP测试线管理模块开发团队
