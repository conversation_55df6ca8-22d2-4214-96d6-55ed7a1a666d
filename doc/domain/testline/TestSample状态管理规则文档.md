# TestSample对象状态管理规则文档

## 文档概述

本文档基于实际代码分析，详细梳理了TestSample对象的状态管理规则，包括样品类型定义、样品生命周期管理、触发动作和触发规则，严格基于代码事实。

**【事实】** 基于以下核心文件的实际代码分析：
- `SampleType.java` - TestSample类型枚举定义
- `SampleService.java` - TestSample核心业务服务 (4586行)
- `TestSampleService.java` - TestSample辅助服务
- `SampleCopyService.java` - TestSample复制服务

## TestSample类型定义

**【事实】** 基于SampleType枚举的实际定义（注：该枚举已标记为@Deprecated）：

| 类型代码 | 类型名称 | 英文描述 | 中文含义 | 短名称 | StarLims类型 |
|----------|----------|----------|----------|--------|-------------|
| **101** | OriginalSample | O | 原样 | O | 101 |
| **102** | Sample | C | 子样 | C | 102 |
| **103** | SubSample | C | 子子样 | SC | 102 |
| **104** | MixSample | C | Mix样 | G | 103 |
| **105** | ShareSample | C | 共享样 | S | 104 |

**【事实】** TestSample没有传统意义上的状态枚举，主要通过SampleType来区分样品类型和处理逻辑。

## TestSample生命周期管理

**【事实】** 基于SampleService中的实际业务流程：

### 1. 样品创建阶段

| 操作类型 | 操作说明 | 触发条件 | 业务逻辑 | 代码位置 |
|----------|----------|----------|----------|----------|
| **updateBreakDown** | 样品分解更新 | 订单存在、样品信息有效 | 创建或更新样品记录，建立样品层级关系 | SampleService.java:309 |
| **updateBreakDownOld** | 旧版样品分解 | 订单存在、样品信息有效 | 兼容旧版本的样品分解逻辑 | SampleService.java:361 |
| **resetMixSampleNo** | 重置混合样编号 | 混合样存在、编号规则变更 | 重新生成混合样的编号 | SampleService.java:1462 |

### 2. 样品分配阶段

| 操作类型 | 操作说明 | 触发条件 | 业务逻辑 | 代码位置 |
|----------|----------|----------|----------|----------|
| **assignSample** | 样品分配查询 | 订单ID有效、PP测试线关系存在 | 获取可分配的样品列表和分配提示信息 | SampleService.java:2344 |
| **saveAssignSample** | 保存样品分配 | 报告状态允许、有分配权限 | 创建TestMatrix、ReportMatrix、PPSampleRel关联关系 | SampleService.java:2682 |
| **checkAssignSample** | 检查样品分配 | 测试线ID非空 | 验证样品分配的合法性和完整性 | SampleService.java:2618 |

### 3. 样品取消阶段

| 操作类型 | 操作说明 | 触发条件 | 业务逻辑 | 代码位置 |
|----------|----------|----------|----------|----------|
| **assignSampleCancel** | 取消样品分配 | 有取消权限、样品已分配 | 删除Matrix关联，清理相关数据 | SampleService.java:3422 |
| **deleteOriginalSample** | 删除原样 | 样品未分配、无Matrix关联 | 物理删除样品记录 | SampleService.java:3664 |

### 4. 样品查询阶段

| 操作类型 | 操作说明 | 触发条件 | 业务逻辑 | 代码位置 |
|----------|----------|----------|----------|----------|
| **getOriginalSampleInfoList** | 获取原样列表 | 订单号有效 | 查询订单下的所有原样信息 | SampleService.java:3717 |
| **getTestSampleListByOrderNo** | 获取测试样品列表 | 订单号有效 | 查询订单下的所有测试样品 | SampleService.java:3725 |
| **getComponentSample** | 获取子样信息 | 订单号有效、绑定TRF | 查询可用的子样（未分配、未取消、未NC） | SampleService.java:4051 |

## 样品类型转换规则

**【事实】** 基于SampleType.equals方法的实际逻辑：

### 样品层级关系
- **OriginalSample → Sample/ShareSample**: 原样可以生成子样或共享样
- **Sample/ShareSample → SubSample**: 子样或共享样可以生成子子样
- **MixSample**: 混合样有特殊的处理逻辑，需要拆分找到对应的原样

### 样品类型检查规则
```java
// 【事实】基于SampleType.equals方法的实际代码
public static boolean equals(Integer parentSampleType, Integer childSampleType) {
    // Sample或ShareSample的父类型必须是OriginalSample
    if (childType == SampleType.Sample || childType == SampleType.ShareSample) {
        return parentType == SampleType.OriginalSample;
    }
    // SubSample的父类型必须是Sample或ShareSample
    if (childType == SampleType.SubSample) {
        return parentType == SampleType.Sample || parentType == SampleType.ShareSample;
    }
    return false;
}
```

## 样品业务规则和约束

**【事实】** 基于SampleService中的业务验证逻辑：

### 1. 样品分配约束
- **报告状态约束**: 只有Cancelled、Approved、Completed、Replaced状态的报告才能进行样品分配
- **TestLine状态约束**: 已Cancelled的TestLine不能分配样品
- **权限约束**: 需要SaveAssignSample权限和相应的分组权限
- **版本控制**: 样品分配操作需要版本递增控制

### 2. 样品删除约束
- **关联检查**: 已分配给TestMatrix的样品不能删除
- **层级检查**: 有子样品的原样不能直接删除
- **状态检查**: 某些状态下的样品不允许删除

### 3. 混合样特殊规则
- **拆分逻辑**: 混合样需要拆分并找到各自的原样
- **PP关联**: 混合样在出报告时只跟在第一个PP下面
- **序号管理**: 混合样有特殊的序号生成和管理规则

## 样品与外部系统集成

**【事实】** TestSample与其他业务对象的集成关系：

### 1. 与TestMatrix的集成
- **创建关联**: 样品分配时创建TestMatrix记录
- **状态同步**: 样品状态变更影响Matrix状态
- **删除联动**: 样品删除时清理相关Matrix

### 2. 与ReportMatrix的集成
- **报告关联**: 通过ReportMatrixRelationship维护样品与报告的关联
- **状态检查**: 报告状态影响样品操作权限
- **数据同步**: 样品变更时同步更新报告相关数据

### 3. 与PP系统的集成
- **PP关联**: 通过PPSampleRelationship维护样品与PP的关联
- **层级管理**: PP系统中的样品层级关系管理
- **数据同步**: 与TRIMS本地化系统的数据同步

### 4. 与分包系统的集成
- **分包样品**: 分包订单中的样品有特殊处理逻辑
- **数据锁定**: 分包数据锁定时限制样品操作
- **状态同步**: 分包完成时同步样品状态

## 权限控制机制

**【事实】** 基于@AccessRule注解的权限控制：

### 样品操作权限
- **saveAssignSample**: reportStatus={Cancelled,Approved,Completed,Replaced}, disGroupKey="AssignSample_SaveConclusion", isVersionIncr=true
- **assignSampleCancel**: subContractType=CancelAssignSample
- **copyTestLineGetSample**: testLinePendingType=TestLineInstanceId
- **assignSample**: testLinePendingType=AssignSamplePpTlId

### 特殊权限控制
- **版本控制**: 样品分配操作需要版本递增，防止并发冲突
- **分组权限**: 通过disGroupKey控制操作分组权限
- **挂起控制**: 通过testLinePendingType控制TestLine挂起状态下的操作

## 数据库事务处理

**【事实】** 样品操作的事务处理机制：

### 事务范围控制
- **样品分配**: 在事务中创建TestMatrix、ReportMatrix、PPSampleRel、Limit、LimitGroup等关联数据
- **样品取消**: 在事务中删除所有相关联的数据，包括Matrix、Condition、Position等
- **样品更新**: 在事务中更新样品信息和相关的语言信息
- **样品删除**: 在事务中删除样品及所有关联数据

### 数据一致性保证
- **关联数据同步**: 样品操作时同步更新所有关联表数据
- **回滚机制**: 任何步骤失败时完整回滚所有变更
- **锁机制**: 使用Redis分布式锁防止并发操作冲突
- **版本控制**: 通过版本号控制并发更新

## 错误处理和异常机制

**【事实】** 基于代码中的错误处理模式：

### 常见错误类型
- **参数验证错误**: "请求对象不能为空"、"订单号不能为空"
- **业务规则违反**: "This Sample has Assigned!"、"TestLine状态已Cancelled"
- **权限验证错误**: 用户无相应操作权限
- **数据完整性错误**: 样品关联数据不完整或冲突
- **并发控制错误**: "不能重复请求"、版本冲突

### 异常处理策略
- **立即返回**: 参数验证失败时立即返回CustomResult.fail()
- **事务回滚**: 业务操作失败时设置trans.setRollbackOnly()
- **分布式锁**: 使用Redis锁防止并发操作
- **日志记录**: 详细记录操作历史和错误信息
- **状态保护**: 错误情况下保持原有状态不变

---

**文档生成时间**: 2025-09-28  
**基于代码版本**: 当前项目代码  
**维护责任**: TestSample管理模块开发团队
