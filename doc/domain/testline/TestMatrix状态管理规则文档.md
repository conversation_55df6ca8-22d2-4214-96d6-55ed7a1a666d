# TestMatrix对象状态管理规则文档

## 文档概述

本文档基于实际代码分析，详细梳理了TestMatrix对象的状态管理规则，包括状态定义、状态转换条件、触发动作和触发规则，严格基于代码事实。

**【事实】** 基于以下核心文件的实际代码分析：
- `MatrixStatus.java` - TestMatrix状态枚举定义
- `TestMatrixService.java` - TestMatrix核心业务服务
- `TestLineStatusService.java` - Matrix状态更新服务
- `TestMatrixCopyService.java` - Matrix复制和状态同步服务

## TestMatrix状态定义

**【事实】** 基于MatrixStatus枚举的实际定义：

| 状态代码 | 状态名称 | 英文描述 | 中文含义 |
|----------|----------|----------|----------|
| **701** | Typing | Typing | 录入中 |
| **702** | Submit | Submitted | 已提交 |
| **703** | Completed | Completed | 已完成 |
| **704** | SubContracted | Subcontracted | 已分包 |
| **705** | Entered | Entered | 已录入 |
| **706** | Cancelled | Cancelled | 已取消 |
| **707** | NC | Not Test | 不测试 |
| **708** | DR | Document Review | 文档审核 |

**【事实】** MatrixStatus与TestLineStatus使用相同的状态代码和含义，但缺少NA(709)状态。

## TestMatrix状态转换规则总览表

**【事实】** 基于TestLineStatusService.updateMatrixStatus方法的实际逻辑：

| 模块操作类型 | 操作说明 | 源状态 | 目标状态 | 触发条件 | 外部系统联动 |
|-------------|----------|--------|----------|----------|-------------|
| **DataEntrySave** | 数据录入保存 | 任意 | Entered(705) | TestLine有效，有录入权限 | 无 |
| **DataEntrySumbit** | 数据录入提交 | 任意 | Submit(702) | 数据完整，有提交权限 | 无 |
| **Validate** | 验证完成 | Submit | Completed(703) | 数据验证通过，有验证权限 | 可能触发订单状态变更 |
| **Change** | 一般变更 | Entered/Submit/Completed | Entered(705) | 有变更权限，报告未发布 | 无 |
| **ChangeUpdateCondition** | 更新测试条件 | Submit/Completed | Entered(705) | 有条件更新权限 | 订单状态同步，分包状态更新 |
| **ChangeUpdateStandard** | 更新测试标准 | Submit/Completed | Entered(705) | 有标准更新权限 | 订单状态同步，分包状态更新 |
| **ChangeAddMatrix** | 添加测试矩阵 | Submit/Completed | Entered(705) | 有矩阵添加权限 | 订单状态同步，分包状态更新 |
| **ReTest** | 重新测试 | 任意 | Entered(705) | 有重测权限，重测原因合理 | 无 |
| **PPSummaryDataEntry** | PP汇总数据录入 | 任意 | Entered(705) | PP类型测试线，有录入权限 | 无 |
| **ChangeCancelMatrix** | 取消/恢复矩阵 | 任意 | 动态决定 | Matrix状态有效，有取消权限 | 无 |

## 详细状态转换规则

### 1. Matrix状态与TestLine状态同步

**【事实】** 基于TestMatrixCopyService.doCopyMatrixConditionId方法的实际逻辑：

#### 状态同步规则
- **活跃Matrix**: MatrixStatus = TestLineStatus（与所属TestLine状态保持一致）
- **取消Matrix**: MatrixStatus = Cancelled(706)（当activeIndicator为false时）
- **代码位置**: TestMatrixCopyService.java 第514-522行

#### 同步触发条件
- **CopyReport操作**: 报告复制时触发Matrix状态同步
- **TestLine状态变更**: TestLine状态改变时，关联的Matrix状态自动同步
- **Matrix激活状态变更**: activeIndicator变更时触发状态同步

### 2. Matrix确认操作 (confirmMatrix)

**【事实】** 基于TestMatrixService.confirmMatrix方法的实际逻辑：

#### 确认条件检查
- **报告状态**: 必须为Approved或Cancelled
- **TestLine状态**: 必须为Typing、Entered、SubContracted或DR状态
- **样品完整性**: 所有样品必须已分配
- **权限验证**: 用户必须有确认权限

#### 确认流程
1. **样品校验**: 检查样品是否全部被使用
2. **状态过滤**: 过滤出符合条件的TestLine
3. **混测校验**: 执行混测样品校验
4. **Matrix生成**: 生成确认的Matrix记录
5. **状态更新**: 更新相关状态信息

### 3. Matrix删除操作 (deleteTest)

**【事实】** 基于TestMatrixService.deleteTest方法的实际逻辑：

#### 删除前置条件
- **TestLine状态**: 必须为Typing状态（分包订单除外）
- **报告状态**: 必须为Approved或Cancelled
- **数据锁定**: 分包数据不能处于锁定状态

#### 删除流程
1. **状态检查**: 验证TestLine状态是否允许删除
2. **Matrix过滤**: 根据MatrixGroupId过滤要删除的Matrix
3. **关联清理**: 删除ReportMatrixRelationship、TestPosition、Conclusion、TestCondition
4. **序号重排**: 重新排列剩余Matrix的GroupId序号
5. **事务提交**: 在事务中执行所有删除操作

### 4. Matrix复制操作 (saveCopyTest)

**【事实】** 基于TestMatrixService.saveCopyTest方法的实际逻辑：

#### 复制前置条件
- **TestLine状态**: 必须为Typing或Entered状态
- **报告状态**: 必须为Approved或Cancelled
- **权限控制**: 需要CopyTestLine权限和TestLineInstanceId挂起类型权限

#### 复制流程
1. **状态验证**: 检查TestLine状态是否允许复制
2. **Matrix查询**: 获取源Matrix信息
3. **新Matrix创建**: 创建新的Matrix记录，状态设置为TestLine当前状态
4. **关联建立**: 建立ReportMatrixRelationship关联
5. **事务提交**: 在事务中完成所有操作

### 5. Matrix取消操作 (cancelAssignSample)

**【事实】** 基于TestMatrixService.cancelAssignSample方法的实际逻辑：

#### 取消条件
- **参数有效性**: TestSampleId和TestLineInstanceId不能为空
- **权限验证**: 需要CancelAssignSample权限
- **Matrix存在性**: 对应的Matrix记录必须存在

#### 取消流程
1. **Matrix查询**: 根据SampleId和TestLineInstanceId查询Matrix
2. **状态切换**: 切换activeIndicator状态（true ↔ false）
3. **TestData处理**: 设置关联TestData为NoNeedTest状态
4. **日志记录**: 记录操作日志信息

## Matrix状态管理特殊规则

### 1. Matrix与TestLine状态依赖关系

**【事实】** Matrix状态高度依赖TestLine状态：

#### 依赖规则
- **创建时**: 新创建的Matrix状态默认为所属TestLine的当前状态
- **同步时**: TestLine状态变更时，所有关联的活跃Matrix状态自动同步
- **取消时**: Matrix被取消时，状态设置为Cancelled，不再跟随TestLine状态

#### 例外情况
- **分包Matrix**: 分包的Matrix可能有独立的状态管理逻辑
- **取消Matrix**: 已取消的Matrix状态固定为Cancelled，不参与同步

### 2. Matrix GroupId管理

**【事实】** Matrix的GroupId用于标识同一TestLine下的不同测试组：

#### GroupId规则
- **连续性**: GroupId必须连续，从0开始递增
- **唯一性**: 同一TestLine下的GroupId不能重复
- **重排序**: 删除Matrix时，后续GroupId自动减1保持连续性

### 3. Matrix ActiveIndicator控制

**【事实】** ActiveIndicator控制Matrix的激活状态：

#### 激活状态规则
- **true**: Matrix处于激活状态，参与状态同步和业务流程
- **false**: Matrix处于取消状态，状态固定为Cancelled
- **切换**: 可以通过cancelAssignSample操作切换激活状态

## 权限控制机制

**【事实】** 基于代码中的@AccessRule注解：

### Matrix操作权限
- **confirmMatrix**: reportStatus={Approved,Cancelled}, disGroupKey="Analyte_Repeat"
- **deleteTest**: reportStatus={Approved,Cancelled}
- **saveCopyTest**: reportStatus={Approved,Cancelled}, subContractType=CopyTestLine, testLinePendingType=TestLineInstanceId
- **cancelAssignSample**: subContractType=CancelAssignSample

### 状态转换权限
- **Matrix状态更新**: 通过TestLineStatusService统一管理，复用TestLine的权限控制
- **分包操作**: 需要相应的分包操作权限
- **报告状态**: 大部分操作需要报告状态为Approved或Cancelled

## 数据库事务处理

**【事实】** 所有Matrix状态变更操作都在数据库事务中执行：

### 事务范围
1. **Matrix状态更新**: 更新test_matrix表的matrix_status字段
2. **关联数据更新**: 更新相关的TestData、Conclusion、TestCondition
3. **关系表维护**: 维护ReportMatrixRelationship等关联表
4. **日志记录**: 记录操作历史和审计信息

### 事务回滚
- **任何步骤失败**: 整个事务回滚，保证数据一致性
- **外部系统调用失败**: 相关操作回滚
- **权限验证失败**: 操作被阻止，不产生数据变更

## 业务场景示例

**【事实】** 基于实际代码的典型业务场景：

### 场景1: 标准Matrix确认流程

**业务描述**: 测试人员确认测试矩阵的标准流程

**状态变更序列**:
1. **初始状态**: TestLine为Typing(701)，Matrix尚未创建
2. **Matrix确认**: 执行confirmMatrix操作，创建Matrix记录
3. **数据录入**: Matrix状态跟随TestLine变为Entered(705)
4. **数据提交**: Matrix状态跟随TestLine变为Submit(702)
5. **验证完成**: Matrix状态跟随TestLine变为Completed(703)

**关键触发条件**:
- 样品必须全部分配完成
- TestLine状态必须为Typing/Entered/SubContracted/DR
- 用户有confirmMatrix权限

### 场景2: Matrix删除流程

**业务描述**: 删除不需要的测试矩阵组

**状态变更序列**:
1. **当前状态**: TestLine为Typing(701)，存在多个MatrixGroup
2. **删除操作**: 执行deleteTest操作，删除指定MatrixGroupId
3. **关联清理**: 删除相关的Conclusion、TestCondition、ReportMatrixRelationship
4. **序号重排**: 后续MatrixGroup的GroupId自动减1
5. **状态保持**: 剩余Matrix状态不变

**关键业务规则**:
- 只有Typing状态的TestLine才能删除Matrix（分包订单除外）
- 删除操作必须在事务中完成
- GroupId必须保持连续性

### 场景3: Matrix复制流程

**业务描述**: 复制现有Matrix到新的样品

**状态变更序列**:
1. **当前状态**: TestLine为Typing(701)或Entered(705)
2. **复制操作**: 执行saveCopyTest操作
3. **新Matrix创建**: 创建新Matrix，状态设置为TestLine当前状态
4. **关联建立**: 建立ReportMatrixRelationship关联
5. **GroupId分配**: 分配新的MatrixGroupId

**关键业务规则**:
- 只有Typing或Entered状态的TestLine才能复制Matrix
- 新Matrix的状态与TestLine当前状态一致
- 必须有CopyTestLine权限

### 场景4: Matrix取消和恢复流程

**业务描述**: 取消或恢复Matrix的分配

**状态变更序列**:
1. **当前状态**: Matrix处于激活状态，activeIndicator=true
2. **取消操作**: 执行cancelAssignSample操作
3. **状态切换**: activeIndicator切换为false，状态变为Cancelled(706)
4. **TestData处理**: 相关TestData设置为NoNeedTest
5. **恢复操作**: 再次执行操作，activeIndicator切换回true，状态恢复

**关键业务规则**:
- 取消后的Matrix状态固定为Cancelled
- 恢复后的Matrix状态跟随TestLine状态
- 操作会影响相关的TestData状态

## Matrix状态与业务流程集成

**【事实】** Matrix状态管理与其他业务流程的集成点：

### 1. 与TestLine状态的集成
- **状态同步**: Matrix状态自动跟随TestLine状态变化
- **状态约束**: Matrix的某些操作受TestLine状态限制
- **状态传播**: TestLine状态变更会批量更新关联Matrix状态

### 2. 与报告生成的集成
- **Matrix过滤**: 只有激活状态的Matrix参与报告生成
- **状态检查**: 报告生成前检查Matrix状态完整性
- **关联维护**: 通过ReportMatrixRelationship维护报告与Matrix的关联

### 3. 与分包流程的集成
- **分包状态**: 分包的Matrix可能有独立的状态管理
- **数据锁定**: 分包数据锁定时限制Matrix操作
- **状态同步**: 分包完成时同步Matrix状态

### 4. 与数据录入的集成
- **录入权限**: Matrix状态影响数据录入权限
- **数据验证**: Matrix状态变更触发数据完整性验证
- **状态联动**: 数据录入操作可能触发Matrix状态变更

## 错误处理和异常机制

**【事实】** 基于代码中的错误处理模式：

### 常见错误类型
1. **状态检查失败**: "Only testline in Typing state can delete test"
2. **参数验证失败**: "请求TestLineInstanceId无效"、"请求TestMatrixIds为空"
3. **权限验证失败**: 用户无相应操作权限
4. **业务规则违反**: "data locked,Please click unlock button first"
5. **数据完整性检查失败**: Matrix关联数据不完整

### 错误处理策略
- **立即返回**: 参数验证失败时立即返回错误
- **事务回滚**: 业务操作失败时回滚所有变更
- **状态保护**: 错误情况下保持原有状态不变
- **日志记录**: 记录详细的错误信息用于问题排查

## 性能优化和最佳实践

**【事实】** 基于代码实现的性能优化策略：

### 批量操作优化
- **批量状态更新**: 使用batchUpdateTestMatrix进行批量状态更新
- **批量删除**: 使用batchDeleteMatrix进行批量删除操作
- **批量查询**: 通过testLineInstanceIds批量查询TestLine状态

### 事务优化
- **事务范围控制**: 将相关操作放在同一事务中执行
- **事务隔离**: 避免长事务影响系统性能
- **回滚策略**: 快速回滚失败的操作

### 查询优化
- **索引利用**: 充分利用testLineInstanceId、testSampleId等索引
- **数据过滤**: 在数据库层面进行状态过滤
- **关联查询**: 优化Matrix与TestLine的关联查询

## 监控和维护

**【事实】** Matrix状态管理的监控和维护机制：

### 状态一致性监控
- **同步检查**: 定期检查Matrix状态与TestLine状态的一致性
- **孤立Matrix**: 监控没有关联TestLine的孤立Matrix
- **状态异常**: 监控状态转换异常的Matrix记录

### 数据质量监控
- **GroupId连续性**: 检查MatrixGroupId的连续性
- **ActiveIndicator一致性**: 检查激活状态与Matrix状态的一致性
- **关联完整性**: 检查Matrix与相关数据的关联完整性

### 维护建议
- **定期清理**: 清理无效的Matrix记录和关联数据
- **状态修复**: 修复状态不一致的Matrix记录
- **性能调优**: 根据监控数据优化查询和更新操作

---

**文档生成时间**: 2025-09-28
**基于代码版本**: 当前项目代码
**维护责任**: TestMatrix管理模块开发团队
