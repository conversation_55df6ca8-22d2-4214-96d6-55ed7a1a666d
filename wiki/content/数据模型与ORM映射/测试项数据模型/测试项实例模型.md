# 测试项实例模型

<cite>
**本文档中引用的文件**  
- [TestLineInstanceMapper.xml](otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/TestLineInstanceMapper.xml)
- [TestLineInstanceExtMapper.xml](otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/TestLineInstanceExtMapper.xml)
- [TestLineInstancePO.java](otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineInstancePO.java)
- [TestLineInstanceDTO.java](otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/TestLineInstanceDTO.java)
- [TestLineInstanceFacade.java](otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/TestLineInstanceFacade.java)
- [TestLineInstanceFacadeImpl.java](otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/TestLineInstanceFacadeImpl.java)
- [TestLineStatus.java](otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java)
- [TrimsTestLineStatus.java](otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TrimsTestLineStatus.java)
- [TestLineStatusInfo.java](otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/testline/TestLineStatusInfo.java)
- [TestLineInstanceExtMapper.java](otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineInstanceExtMapper.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心数据模型](#核心数据模型)
3. [字段定义与状态机](#字段定义与状态机)
4. [关联关系与业务流程](#关联关系与业务流程)
5. [生命周期管理](#生命周期管理)
6. [MyBatis映射与SQL设计](#mybatis映射与sql设计)
7. [测试数据与报告集成](#测试数据与报告集成)
8. [结论](#结论)

## 简介
测试项实例（TestLineInstance）是OTSNotes系统中用于管理具体测试任务执行的核心数据实体。它代表了测试项在特定订单上下文中的实际执行实例，承载了测试状态、执行进度、关联样品与测试结果等关键信息。该模型在测试流程中起到承上启下的作用，连接了订单、测试项模板、样品和最终的测试报告。

## 核心数据模型

```mermaid
erDiagram
tb_test_line_instance {
varchar(36) ID PK
varchar(36) OrderID
int TestLineID
int TestLineStatus
int ExecutionStatus
varchar(36) TestSampleID
varchar(36) TestMatrixID
datetime CreatedDate
datetime ModifiedDate
varchar(50) CreatedBy
varchar(50) ModifiedBy
timestamp LastModifiedTimestamp
}
tb_order ||--o{ tb_test_line_instance : "包含"
tb_test_line ||--o{ tb_test_line_instance : "实例化"
tb_test_sample ||--o{ tb_test_line_instance : "关联"
tb_test_matrix ||--o{ tb_test_line_instance : "使用"
tb_test_line_instance }|--|| tb_test_data : "生成"
tb_test_line_instance }|--|| tb_test_result : "产生"
```

**图示来源**  
- [TestLineInstancePO.java](otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineInstancePO.java)
- [testline_init.sql](uni-otsnotes/doc/db/testline_init.sql)

## 字段定义与状态机

### 核心字段说明

| 字段名 | 类型 | 必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `instance_id` | VARCHAR(36) | 是 | 测试项实例的唯一标识符，主键 |
| `order_id` | VARCHAR(36) | 是 | 关联的订单实例ID，外键指向 `tb_general_order_instance` |
| `test_line_id` | INT | 是 | 关联的测试项模板ID，外键指向 `tb_test_line` |
| `status` | INT | 是 | 实例的业务状态，如待处理、进行中、已完成等 |
| `execution_status` | INT | 是 | 实例的执行状态，用于跟踪测试流程的内部阶段 |
| `test_sample_id` | VARCHAR(36) | 否 | 关联的样品ID，外键指向 `tb_test_sample` |
| `test_matrix_id` | VARCHAR(36) | 否 | 关联的测试矩阵ID，用于混测等场景 |
| `last_modified_timestamp` | TIMESTAMP | 是 | 最后修改时间戳，用于并发控制 |

**节来源**  
- [TestLineInstancePO.java](otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineInstancePO.java)

### 状态机设计

```mermaid
stateDiagram-v2
[*] --> 待处理
待处理 --> 进行中 : 启动测试
进行中 --> 已完成 : 测试成功
进行中 --> 已失败 : 测试异常
进行中 --> 已暂停 : 用户暂停
已暂停 --> 进行中 : 恢复测试
已完成 --> 已归档 : 报告生成
已失败 --> 已重试 : 重新执行
已重试 --> 进行中
已归档 --> [*]
```

**状态定义来源**  
- [TestLineStatus.java](otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java)
- [TrimsTestLineStatus.java](otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TrimsTestLineStatus.java)

**节来源**  
- [TestLineStatus.java](otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java)
- [TestLineStatusInfo.java](otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/testline/TestLineStatusInfo.java)

## 关联关系与业务流程

### 关联关系

测试项实例是系统中多个核心实体的连接点：
- **与订单关联**：通过 `order_id` 字段与 `tb_general_order_instance` 表关联，表示该实例属于哪个订单。
- **与测试项模板关联**：通过 `test_line_id` 字段与 `tb_test_line` 表关联，继承测试项的配置和规则。
- **与样品关联**：通过 `test_sample_id` 字段与 `tb_test_sample` 表关联，表示该实例正在测试哪个样品。
- **与测试矩阵关联**：通过 `test_matrix_id` 字段与 `tb_test_matrix` 表关联，支持混测等复杂测试场景。

### 在测试流程中的作用
测试项实例是测试流程的执行载体。当一个订单被创建并分配测试项后，系统会为每个测试项创建一个实例。该实例的状态（`status` 和 `execution_status`）会随着测试流程的推进而更新，从“待处理”到“进行中”，最终到达“已完成”或“已失败”。这一过程由 `TestLineInstanceFacade` 和 `TestLineInstanceFacadeImpl` 控制。

**节来源**  
- [TestLineInstanceFacade.java](otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/TestLineInstanceFacade.java)
- [TestLineInstanceFacadeImpl.java](otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/TestLineInstanceFacadeImpl.java)

## 生命周期管理

### 实例创建
实例的创建通常由订单服务触发。当订单进入测试阶段时，系统会根据订单中的测试项配置，调用 `TestLineInstanceFacade` 的相关方法批量创建 `TestLineInstance` 记录。创建时会初始化 `status` 为“待处理”（Pending），并设置 `created_by` 和 `created_date` 字段。

### 状态更新
状态更新是通过 `TestLineInstanceExtMapper` 提供的扩展方法实现的。例如，`updateStatusById` 方法允许根据 `instance_id` 更新实例的状态。更新操作会同时修改 `status`、`execution_status`、`modified_by`、`modified_date` 和 `last_modified_timestamp` 字段，确保数据的一致性和可追溯性。

### 生命周期规则
- **创建规则**：实例必须关联有效的订单和测试项模板。
- **状态转换规则**：状态转换必须符合预定义的流程，例如不能从未完成状态直接跳转到“已归档”。
- **删除规则**：通常不物理删除实例，而是通过 `active_indicator` 字段标记为无效。

**节来源**  
- [TestLineInstanceExtMapper.java](otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineInstanceExtMapper.java)
- [TestLineInstanceExtMapper.xml](otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/TestLineInstanceExtMapper.xml)

## MyBatis映射与SQL设计

### SQL编写逻辑
MyBatis映射文件分为自动生成和用户自定义两部分：
- **自动生成部分** (`TestLineInstanceMapper.xml`)：由代码生成器生成，包含基础的CRUD操作，如 `selectByPrimaryKey`、`insert`、`updateByPrimaryKey`。
- **用户自定义部分** (`TestLineInstanceExtMapper.xml`)：包含复杂的查询和更新逻辑，例如根据订单ID批量查询实例、根据状态筛选实例等。

### 性能优化与索引策略
- **索引使用**：在 `order_id`、`test_line_id`、`status`、`last_modified_timestamp` 等常用查询字段上建立了数据库索引，以加速查询性能。
- **查询优化**：对于分页查询，使用 `last_modified_timestamp` 作为排序字段，结合 `LIMIT` 子句，避免使用 `OFFSET` 导致的性能问题。
- **批量操作**：对于状态更新等操作，提供批量处理接口，减少数据库交互次数。

```mermaid
flowchart TD
A["客户端请求"] --> B{操作类型}
B --> |基础CRUD| C["调用 TestLineInstanceMapper"]
B --> |复杂查询/更新| D["调用 TestLineInstanceExtMapper"]
C --> E["执行自动生成SQL"]
D --> F["执行用户自定义SQL"]
E --> G["数据库"]
F --> G
G --> H["返回结果"]
```

**图示来源**  
- [TestLineInstanceMapper.xml](otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/TestLineInstanceMapper.xml)
- [TestLineInstanceExtMapper.xml](otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/TestLineInstanceExtMapper.xml)

**节来源**  
- [TestLineInstanceMapper.xml](otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/TestLineInstanceMapper.xml)
- [TestLineInstanceExtMapper.xml](otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/TestLineInstanceExtMapper.xml)

## 测试数据与报告集成

### 与测试数据、测试结果的关联机制
测试项实例与测试数据和测试结果通过 `instance_id` 建立关联。当测试执行时，产生的原始测试数据（如仪器读数）存储在 `tb_test_data` 表中，并通过 `test_line_instance_id` 字段关联到实例。经过计算和分析后，最终的测试结果存储在 `tb_test_result` 表中，同样通过 `test_line_instance_id` 关联。

### 在报告生成中的应用
在报告生成阶段，报告服务会根据订单ID查询所有相关的 `TestLineInstance`，然后获取每个实例的测试结果。`TestLineInstanceDTO` 作为数据传输对象，封装了实例的完整信息，包括状态、关联的样品和测试结果，供报告模板引擎使用。报告的状态（如“已发布”）也会反向影响实例的 `status`，形成闭环。

**节来源**  
- [TestLineInstanceDTO.java](otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/TestLineInstanceDTO.java)
- [GetReportSplitConclusionRsp.java](otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/conclusion/GetReportSplitConclusionRsp.java)

## 结论
`TestLineInstance` 表是OTSNotes系统测试管理模块的核心。它不仅是一个简单的数据存储表，更是整个测试流程的状态机和协调中心。通过清晰的字段定义、严谨的状态机设计、高效的MyBatis映射以及与上下游系统的紧密集成，该模型确保了测试流程的可追溯性、一致性和高效性。理解其结构和业务规则对于系统的维护、扩展和问题排查至关重要。