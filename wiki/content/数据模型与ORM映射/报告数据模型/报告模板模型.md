# 报告模板模型

<cite>
**本文档引用的文件**  
- [testline_init.sql](uni-otsnotes/doc/db/testline_init.sql)
- [ReportTemplateInfoPO.java](otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportTemplateInfoPO.java)
- [ReportTemplateSectionPO.java](otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportTemplateSectionPO.java)
- [ReportTemplateExtMapper.xml](otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportTemplateExtMapper.xml)
- [ReportTemplateService.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/reporttemplate/ReportTemplateService.java)
- [ReportTemplateSectionCodeEnum.java](otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ReportTemplateSectionCodeEnum.java)
- [ReportTemplateSectionTypeEnum.java](otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ReportTemplateSectionTypeEnum.java)
</cite>

## 目录
1. [引言](#引言)
2. [数据库表结构](#数据库表结构)
3. [核心字段定义](#核心字段定义)
4. [MyBatis映射文件分析](#mybatis映射文件分析)
5. [实体关系图](#实体关系图)
6. [继承机制与覆盖规则](#继承机制与覆盖规则)
7. [权限控制策略](#权限控制策略)
8. [数据访问模式与缓存策略](#数据访问模式与缓存策略)
9. [性能优化方案](#性能优化方案)
10. [最佳实践](#最佳实践)

## 引言
报告模板模型是报告管理系统的核心组成部分，负责管理报告模板的创建、版本控制、多语言支持和权限管理。该模型通过数据库表结构和ORM映射实现对报告模板的持久化存储和高效访问。本文档详细介绍了报告模板相关的数据库表结构、字段定义、MyBatis映射文件的编写逻辑，以及实体之间的关联关系。同时，文档还说明了报告模板的继承机制、覆盖规则和权限控制策略，为开发者提供了数据访问模式、缓存策略和性能优化方案的最佳实践。

## 数据库表结构
报告模板模型主要涉及以下数据库表：

```mermaid
erDiagram
REPORT_TEMPLATE_INFO {
string ID PK
string reportId
string qrcodePath
integer templateSettingId
integer languageId
integer templateTypeId
boolean activeIndicator
string createdBy
datetime createdDate
string modifiedBy
datetime modifiedDate
string templateName
integer container
string containerTemplateFilepath
string dataObject
}
REPORT_TEMPLATE_SECTION {
string ID PK
string parentID FK
integer templateSectionType
string templateID FK
string fileID
string sectionName
integer sequence
integer sectionCode
boolean activeIndicator
string createdBy
string modifiedBy
datetime createdDate
datetime modifiedDate
}
TEMPLATE_CUSTOMER_REL_INFO {
string ID PK
string tempalteID FK
string customerGroupCode
string createdBy
datetime createdDate
string modifiedBy
datetime modifiedDate
}
REPORT_TEMPLATE_INFO ||--o{ REPORT_TEMPLATE_SECTION : "包含"
REPORT_TEMPLATE_INFO ||--o{ TEMPLATE_CUSTOMER_REL_INFO : "关联"
CUSTOMER_REPORT_REQUEST_INFO ||--o{ TEMPLATE_CUSTOMER_REL_INFO : "关联"
```

**图表来源**  
- [ReportTemplateInfoPO.java](otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportTemplateInfoPO.java)
- [ReportTemplateSectionPO.java](otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportTemplateSectionPO.java)

## 核心字段定义
### ReportTemplateInfo表字段
- **ID**: 模板ID，主键，VARCHAR(50)，必填
- **reportId**: 报告ID，VARCHAR(50)，必填
- **qrcodePath**: 二维码路径，VARCHAR(500)
- **templateSettingId**: 模板设置ID，INTEGER(10)
- **languageId**: 语言ID，INTEGER(10)
- **templateTypeId**: 模板类型ID，INTEGER(10)
- **activeIndicator**: 激活指示器，BIT，0: 非激活，1: 激活
- **createdBy**: 创建者，VARCHAR(50)
- **createdDate**: 创建日期，TIMESTAMP(19)
- **modifiedBy**: 修改者，VARCHAR(50)
- **modifiedDate**: 修改日期，TIMESTAMP(19)
- **templateName**: 模板名称，VARCHAR(100)
- **container**: 容器，INTEGER(10)
- **containerTemplateFilepath**: 容器模板文件路径，VARCHAR(500)
- **dataObject**: 数据对象，VARCHAR(100)
- **templateConfig**: 模板配置，VARCHAR(1500)

### ReportTemplateSection表字段
- **ID**: ID(UUID)，主键，VARCHAR(36)，必填
- **parentID**: 父ID，外键指向同一表，VARCHAR(36)
- **templateSectionType**: 子模板类型，INTEGER(10)
- **templateID**: 模板ID，外键指向TB_Template，VARCHAR(36)
- **fileID**: 模板文件ID，外键指向TB_TemplateFile，VARCHAR(36)
- **sectionName**: 模板的章节名称，VARCHAR(100)
- **sequence**: 序列，INTEGER(10)
- **sectionCode**: 模板章节代码，INTEGER(10)
- **activeIndicator**: 激活指示器，BIT，0: 非激活，1: 激活
- **createdBy**: 创建者，VARCHAR(50)
- **createdDate**: 创建日期，TIMESTAMP(19)，默认值[2000-01-01 00:00:00]
- **modifiedBy**: 修改者，VARCHAR(50)
- **modifiedDate**: 修改日期，TIMESTAMP(19)，默认值[2000-01-01 00:00:00]

**本节来源**
- [ReportTemplateInfoPO.java](otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportTemplateInfoPO.java)
- [ReportTemplateSectionPO.java](otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportTemplateSectionPO.java)

## MyBatis映射文件分析
### 模板查询映射
```xml
<select id="getReportTemplateList" resultMap="listMap">
    SELECT
    t_t.ID,
    t_t.TemplateName,
    t_c.ID CustomerID,
    t_c.CustomerGroupCode,
    t_t.OtsFlag,
    t_c.SpecialSequenceFlag,
    t_c.HighLightFailResultFlag,
    t_c.SummaryBorderFlag,
    t_c.BorderFlag,
    t_c.NeedConclusionFlag,
    t_c.TestAgainst,
    t_c.QrCodeFlag,
    t_t.ActiveIndicator,
    t_t.RequestType,
    t_t.CustomerReportRequestID
    FROM
    tb_template t_t
    LEFT JOIN
    tre_template_customer_relationship t_r ON t_t.ID = t_r.TempalteID
    LEFT JOIN
    tb_customer_report_request t_c ON t_c.CustomerGroupCode = t_r.CustomerGroupCode
    <where>
        <if test="templateName != null and templateName!=''">
            and t_t.TemplateName like CONCAT('%',CONCAT(#{templateName},'%'))
        </if>
        <if test="customerCode != null and customerCode!=''">
            and t_c.CustomerGroupCode like CONCAT('%',CONCAT(#{customerCode},'%'))
        </if>
        <if test="active !=null and active!=''">
            AND t_t.ActiveIndicator = #{active}
        </if>
    </where>
    ORDER BY t_t.ActiveIndicator desc,t_c.CustomerGroupCode desc
</select>
```
该SQL语句实现了报告模板的列表查询功能，通过LEFT JOIN关联模板表、模板客户关系表和客户报告请求表，支持按模板名称、客户代码和激活状态进行过滤，并按激活状态和客户代码降序排列。

### 模板保存映射
```xml
<insert id="batchSaveReportTemplateSection">
    INSERT INTO tb_report_template_section (
    ID,CreatedBy, CreatedDate, ModifiedBy, ModifiedDate,ActiveIndicator
    ,ParentID
    ,TemplateSectionType
    ,TemplateID
    ,FileID
    ,SectionName
    ,Sequence
    ,SectionCode
    )
    VALUES
    <foreach collection="saveList" item="item" separator=",">
        (
        #{item.ID},
        #{item.createdBy},
        #{item.createdDate},
        #{item.modifiedBy},
        #{item.modifiedDate},
        #{item.activeIndicator},
        #{item.parentID},
        #{item.templateSectionType},
        #{item.templateID},
        #{item.fileID},
        #{item.sectionName},
        #{item.sequence},
        #{item.sectionCode}
        )
    </foreach>
</insert>
```
该SQL语句实现了报告模板章节的批量保存功能，使用`<foreach>`标签遍历`saveList`集合，将多个章节数据一次性插入到`tb_report_template_section`表中，提高了数据插入效率。

**本节来源**
- [ReportTemplateExtMapper.xml](otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportTemplateExtMapper.xml)

## 实体关系图
```mermaid
erDiagram
REPORT_TEMPLATE_INFO {
string ID PK
string templateName
integer templateTypeId
integer languageId
boolean activeIndicator
string createdBy
datetime createdDate
string modifiedBy
datetime modifiedDate
}
REPORT_TEMPLATE_SECTION {
string ID PK
string parentID FK
integer templateSectionType
string templateID FK
string fileID
string sectionName
integer sequence
integer sectionCode
boolean activeIndicator
string createdBy
string modifiedBy
datetime createdDate
datetime modifiedDate
}
TEMPLATE_CUSTOMER_REL_INFO {
string ID PK
string tempalteID FK
string customerGroupCode
string createdBy
datetime createdDate
string modifiedBy
datetime modifiedDate
}
REPORT_TEMPLATE_INFO ||--o{ REPORT_TEMPLATE_SECTION : "包含"
REPORT_TEMPLATE_INFO ||--o{ TEMPLATE_CUSTOMER_REL_INFO : "关联"
CUSTOMER_REPORT_REQUEST_INFO ||--o{ TEMPLATE_CUSTOMER_REL_INFO : "关联"
```

**图表来源**  
- [ReportTemplateInfoPO.java](otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportTemplateInfoPO.java)
- [ReportTemplateSectionPO.java](otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportTemplateSectionPO.java)

## 继承机制与覆盖规则
报告模板模型通过`ReportTemplateSection`表的`parentID`字段实现章节的继承机制。每个章节可以有一个父章节，形成树形结构。`templateSectionType`字段定义了章节的类型，如封面、测试结果等。

覆盖规则主要体现在模板的版本管理和客户特定配置上。当为特定客户创建模板时，系统会检查是否存在已有的模板配置，如果存在，则允许覆盖或继承原有配置。在`ReportTemplateService`的`saveTemplate`方法中，通过事务管理确保模板及其相关章节、参数关系的原子性更新。

**本节来源**
- [ReportTemplateService.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/reporttemplate/ReportTemplateService.java)

## 权限控制策略
报告模板的权限控制通过`activeIndicator`字段实现。该字段为BIT类型，0表示非激活状态，1表示激活状态。只有激活状态的模板才能被使用。

在`ReportTemplateService`中，`updateActiveStatus`方法用于切换模板的激活状态。该方法首先验证用户权限，然后更新模板的`activeIndicator`字段，并记录修改者和修改时间。此外，系统还通过`TemplateCustomerRelInfo`表管理模板与客户的关联关系，确保只有授权客户才能使用特定模板。

**本节来源**
- [ReportTemplateService.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/reporttemplate/ReportTemplateService.java)

## 数据访问模式与缓存策略
### 数据访问模式
报告模板的数据访问主要通过`ReportTemplateExtMapper`接口实现。该接口定义了模板的查询、保存、更新和删除操作。在`ReportTemplateService`中，通过依赖注入获取Mapper实例，并在事务管理下执行数据库操作。

### 缓存策略
系统采用两级缓存策略：
1. **一级缓存**：MyBatis默认的SqlSession级别缓存，适用于单次请求内的重复查询。
2. **二级缓存**：通过Redis实现的分布式缓存，适用于跨请求的模板数据共享。

在`ReportTemplateService`中，`getReportTemplateList`方法使用了PageHelper进行分页查询，提高了大数据量下的查询性能。同时，系统通过`@Cacheable`注解对常用的模板数据进行缓存，减少数据库访问频率。

**本节来源**
- [ReportTemplateService.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/reporttemplate/ReportTemplateService.java)

## 性能优化方案
### 数据库优化
1. **索引优化**：在`ReportTemplateInfo`表的`templateName`、`activeIndicator`等常用查询字段上创建索引。
2. **查询优化**：使用`LEFT JOIN`替代子查询，减少查询复杂度。
3. **批量操作**：通过`<foreach>`标签实现批量插入和更新，减少数据库交互次数。

### 代码优化
1. **事务管理**：使用`TransactionTemplate`确保数据一致性，避免部分更新导致的数据不一致。
2. **对象映射**：通过`BeanUtils.copyProperties`简化对象属性复制，提高代码可读性。
3. **异常处理**：使用`try-catch`捕获业务异常，确保系统稳定性。

### 缓存优化
1. **缓存预热**：在系统启动时预加载常用模板数据到缓存中。
2. **缓存失效**：在模板更新时及时清除相关缓存，保证数据一致性。
3. **缓存分片**：根据模板类型或客户代码对缓存进行分片，提高缓存命中率。

**本节来源**
- [ReportTemplateService.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/reporttemplate/ReportTemplateService.java)

## 最佳实践
1. **模板命名规范**：使用有意义的模板名称，便于后续维护和查找。
2. **版本控制**：为每个模板创建版本号，便于追踪变更历史。
3. **权限管理**：严格控制模板的创建、修改和删除权限，防止误操作。
4. **性能监控**：定期监控模板查询和更新的性能，及时发现和解决性能瓶颈。
5. **文档化**：为每个模板编写详细的使用说明，便于其他开发者理解和使用。

**本节来源**
- [ReportTemplateService.java](otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/reporttemplate/ReportTemplateService.java)