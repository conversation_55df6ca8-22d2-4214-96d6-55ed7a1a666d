# 项目概述

<cite>
**本文档引用的文件**
- [项目概述.md](file://doc/项目概述.md)
- [系统架构.md](file://doc/系统架构.md)
- [技术视角.md](file://doc/技术视角.md)
- [业务功能说明.md](file://doc/业务功能说明.md)
- [StarLimsConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/StarLimsConfig.java)
- [KafkaProducerConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/KafkaProducerConfig.java)
- [RedisConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/RedisConfig.java)
- [TechBizService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/biz/TechBizService.java)
- [SubContractFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/SubContractFacade.java)
</cite>

## 目录
1. [项目背景与核心价值](#项目背景与核心价值)
2. [主要功能模块](#主要功能模块)
3. [目标用户群体](#目标用户群体)
4. [技术愿景与设计原则](#技术愿景与设计原则)
5. [系统上下文与外部集成](#系统上下文与外部集成)
6. [总结](#总结)

## 项目背景与核心价值

otsnotes-service 是 SGS 集团为实验室信息管理系统（LIMS）打造的核心后端服务，旨在实现测试数据的全流程数字化管理。该系统通过自动化处理订单、报告、分包等关键业务流程，显著提升实验室运营效率，确保数据的一致性与安全性，并支持多实验室、多业务线的协同作业。

其核心业务价值体现在：
- **流程自动化**：减少人工干预，降低操作错误率
- **数据一致性保障**：统一数据源，确保跨系统数据同步
- **高效协同**：支持内外部实验室的分包协作
- **灵活集成**：提供标准化接口，便于与各类外部系统对接

**Section sources**
- [项目概述.md](file://doc/项目概述.md)

## 主要功能模块

otsnotes-service 围绕实验室核心业务构建了多个关键功能模块：

### 订单管理
系统支持从订单创建、分包分配到状态追踪的全生命周期管理。通过统一的订单模型，实现对测试任务的集中调度与监控。

### 报告生成
提供多语言、多格式的报告生成与回传能力。支持自动从外部系统（如StarLIMS）接收报告文档，并进行归档与校验。

### 分包协作
实现与外部实验室的分包协同，包括分包单创建、状态同步、数据交换等功能。通过标准化接口确保分包流程的顺畅执行。

### StarLIMS系统集成
作为与StarLIMS系统对接的核心服务，提供双向数据同步能力，包括测试数据上传、报告回传、状态更新等关键接口。

**Section sources**
- [业务功能说明.md](file://doc/业务功能说明.md)
- [SubContractFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/SubContractFacade.java)

## 目标用户群体

otsnotes-service 服务于多个角色，满足不同用户群体的需求：

- **实验室技术人员**：使用系统进行样品管理、测试数据录入和报告生成，提高日常工作效率。
- **质量管理人员**：通过系统监控订单进度、分包状态和报告质量，确保业务流程符合质量标准。
- **外部合作伙伴**：作为分包方，通过标准化接口与主实验室进行数据交换和业务协同。
- **系统集成开发者**：利用开放的API接口，实现与其他企业系统的无缝集成。

**Section sources**
- [项目概述.md](file://doc/项目概述.md)

## 技术愿景与设计原则

### 领域驱动设计（DDD）
系统采用领域驱动设计思想，将复杂的业务逻辑划分为清晰的领域模型，如订单（Order）、分包（SubContract）、样品（Sample）等，确保业务概念在代码中的准确体现。

### 分层架构
系统遵循清晰的分层架构原则，各层职责分明：
- **核心层（core）**：提供基础工具、配置和通用能力
- **领域层（domain）**：封装核心业务逻辑和领域服务
- **数据层（dbstorages）**：负责数据持久化访问
- **接口层（facade）**：定义对外服务接口
- **集成层（integration）**：处理外部系统对接

```mermaid
graph TD
A[otsnotes-web] --> |REST| B[otsnotes-facade]
B --> |调用| C[otsnotes-domain]
C --> |数据访问| D[otsnotes-dbstorages]
C --> |基础能力| E[otsnotes-core]
C --> |集成| F[otsnotes-integration]
D --> |MyBatis| G[(DB)]
F --> |HTTP/Dubbo| H[外部系统]
```

**Diagram sources**
- [系统架构.md](file://doc/系统架构.md)

### 技术选型
采用成熟稳定的技术栈，包括Spring Boot、MyBatis、Dubbo、Redis和Kafka，确保系统的高性能、高可用性和可扩展性。

**Section sources**
- [系统架构.md](file://doc/系统架构.md)
- [技术视角.md](file://doc/技术视角.md)

## 系统上下文与外部集成

otsnotes-service 作为企业生态系统中的关键组件，与多个外部系统保持紧密交互：

### 外部系统集成
- **StarLIMS**：通过HTTP接口进行测试数据同步和报告回传，配置信息在`StarLimsConfig.java`中定义。
- **Kafka**：作为消息中间件，实现异步任务处理和系统间解耦，生产者配置在`KafkaProducerConfig.java`中。
- **Redis**：用于缓存高频访问数据，提升系统响应速度，集群配置在`RedisConfig.java`中。

### 数据流与交互
系统通过标准化接口与外部系统进行数据交换，确保信息的实时性和一致性。例如，当外部实验室完成测试后，报告文档通过`receiveReportDoc`接口回传至系统，触发后续的归档和校验流程。

```mermaid
sequenceDiagram
participant U as 用户
participant W as Web前端
participant F as Facade
participant D as Domain
participant DB as DB
participant EXT as 外部系统
U->>W : 提交分包/报告/样品等操作
W->>F : 调用API
F->>D : 领域服务处理
D->>DB : 数据持久化
D->>EXT : 外部系统对接
EXT-->>D : 回传/同步数据
D-->>F : 返回处理结果
F-->>W : 返回API响应
W-->>U : 展示结果
```

**Diagram sources**
- [业务功能说明.md](file://doc/业务功能说明.md)

**Section sources**
- [StarLimsConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/StarLimsConfig.java)
- [KafkaProducerConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/KafkaProducerConfig.java)
- [RedisConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/RedisConfig.java)

## 总结

otsnotes-service 作为SGS实验室信息管理的核心服务，通过领域驱动设计和分层架构，实现了订单管理、报告生成、分包协作等关键业务功能。系统不仅服务于内部实验室团队，还通过标准化接口支持与外部合作伙伴的高效协同。其技术架构设计充分考虑了可扩展性、可靠性和集成能力，为企业级LIMS解决方案提供了坚实的基础。

**Section sources**
- [项目概述.md](file://doc/项目概述.md)
- [系统架构.md](file://doc/系统架构.md)