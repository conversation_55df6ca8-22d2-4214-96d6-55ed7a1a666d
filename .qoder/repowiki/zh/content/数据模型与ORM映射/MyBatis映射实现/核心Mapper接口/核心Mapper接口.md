# 核心Mapper接口

<cite>
**本文档中引用的文件**  
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java)
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java)
- [ConclusionMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/ConclusionMapper.java)
- [TestLineInstanceInfo.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmodel/worksheet/TestLineInstanceInfo.java) - *在最近提交中重构*
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java)
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)
- [TestLineService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineService.java)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)
- [ConclusionService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ConclusionService.java)
</cite>

## 目录
1. [核心Mapper接口概述](#核心mapper接口概述)
2. [OrderMapper接口详解](#ordermapper接口详解)
3. [SubContractMapper接口详解](#subcontractmapper接口详解)
4. [ReportMapper接口详解](#reportmapper接口详解)
5. [TestLineMapper接口详解](#testlinemapper接口详解)
6. [ConclusionMapper接口详解](#conclusionmapper接口详解)
7. [MyBatis注解使用规范](#mybatis注解使用规范)
8. [接口使用示例](#接口使用示例)
9. [命名规范与代码风格](#命名规范与代码风格)
10. [复杂查询与关联查询处理](#复杂查询与关联查询处理)
11. [性能优化建议](#性能优化建议)

## 核心Mapper接口概述

在otsnotes-service项目中，核心Mapper接口位于`otsnotes-dbstorages`模块的`mybatis`包下，主要负责与数据库进行交互，实现数据的持久化操作。这些接口基于MyBatis框架，通过定义抽象方法来映射SQL语句，从而实现对数据库表的增删改查操作。核心Mapper接口包括OrderMapper、SubContractMapper、ReportMapper、SampleMapper、TestLineMapper和ConclusionMapper，它们分别对应订单、分包、报告、样品、测试线和结论等业务实体。

这些Mapper接口的设计遵循了分层架构原则，将数据访问逻辑与业务逻辑分离，提高了代码的可维护性和可测试性。通过使用MyBatis注解，如@Select、@Insert、@Update和@Delete，可以直接在接口方法上定义SQL语句，简化了配置文件的编写。此外，接口还支持复杂的查询和关联查询，能够满足多样化的业务需求。

**Section sources**
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java)
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java)
- [ConclusionMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/ConclusionMapper.java)

## OrderMapper接口详解

### 方法定义与参数类型

OrderMapper接口定义了多个方法，用于处理订单相关的数据操作。以下是主要方法的详细说明：

- **saveOrderInfo(GeneralOrderInstanceInfoPO order)**: 保存订单信息，参数为GeneralOrderInstanceInfoPO对象，返回受影响的行数。
- **updateOrderInfo(GeneralOrderInstanceInfoPO order)**: 更新订单信息，参数为GeneralOrderInstanceInfoPO对象，返回受影响的行数。
- **getOrderInfo(@Param("orderNo") String orderNo)**: 根据订单号获取订单信息，参数为订单号，返回GeneralOrderInstanceInfoPO对象。
- **getOrderTestLineSampleList(@Param("jobNo") String jobNo)**: 通过Job No获取订单、客户、样品(原样)、TestLine及Matrix信息，参数为Job No，返回OrderTestLineSamplePo列表。

### 返回值与业务场景

- **int类型返回值**: 表示受影响的行数，通常用于插入、更新和删除操作。
- **GeneralOrderInstanceInfoPO对象**: 包含订单的详细信息，用于查询操作。
- **List<OrderTestLineSamplePo>**: 包含订单、客户、样品等信息的列表，用于批量查询。

### 业务场景

OrderMapper接口主要用于订单的创建、更新、查询和删除操作。例如，在订单创建时，调用saveOrderInfo方法将订单信息保存到数据库；在订单更新时，调用updateOrderInfo方法更新订单状态或其他信息；在订单查询时，调用getOrderInfo方法根据订单号获取订单详情。

**Section sources**
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)

## SubContractMapper接口详解

### 方法定义与参数类型

SubContractMapper接口定义了多个方法，用于处理分包相关的数据操作。以下是主要方法的详细说明：

- **insert(SubContractPO record)**: 插入分包记录，参数为SubContractPO对象，返回受影响的行数。
- **selectByPrimaryKey(String ID)**: 根据主键获取分包记录，参数为ID，返回SubContractPO对象。
- **updateByPrimaryKeySelective(SubContractPO record)**: 根据主键选择性更新分包记录，参数为SubContractPO对象，返回受影响的行数。

### 返回值与业务场景

- **int类型返回值**: 表示受影响的行数，通常用于插入、更新和删除操作。
- **SubContractPO对象**: 包含分包的详细信息，用于查询操作。

### 业务场景

SubContractMapper接口主要用于分包的创建、更新、查询和删除操作。例如，在分包创建时，调用insert方法将分包信息保存到数据库；在分包更新时，调用updateByPrimaryKeySelective方法更新分包状态或其他信息；在分包查询时，调用selectByPrimaryKey方法根据ID获取分包详情。

**Section sources**
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java)

## ReportMapper接口详解

### 方法定义与参数类型

ReportMapper接口定义了多个方法，用于处理报告相关的数据操作。以下是主要方法的详细说明：

- **saveReportInfo(ReportInfoPO report)**: 保存报告信息，参数为ReportInfoPO对象，返回受影响的行数。
- **updateReportStatus(ReportInfoPO report)**: 更新报告状态，参数为ReportInfoPO对象，返回受影响的行数。
- **getReportInfoByReportNo(@Param("reportNo")String reportNo)**: 根据报告号获取报告信息，参数为报告号，返回ReportInfoPO对象。

### 返回值与业务场景

- **int类型返回值**: 表示受影响的行数，通常用于插入、更新和删除操作。
- **ReportInfoPO对象**: 包含报告的详细信息，用于查询操作。

### 业务场景

ReportMapper接口主要用于报告的创建、更新、查询和删除操作。例如，在报告创建时，调用saveReportInfo方法将报告信息保存到数据库；在报告更新时，调用updateReportStatus方法更新报告状态；在报告查询时，调用getReportInfoByReportNo方法根据报告号获取报告详情。

**Section sources**
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)

## TestLineMapper接口详解

### 方法定义与参数类型

TestLineMapper接口定义了多个方法，用于处理测试线相关的数据操作。以下是主要方法的详细说明：

- **batchInsert(@Param("testLines")List<TestLineInstancePO> testLines)**: 批量插入测试线记录，参数为TestLineInstancePO列表，返回受影响的行数。
- **getTestLineInstanceById(String testLineInstanceId)**: 根据测试线实例ID获取测试线信息，参数为测试线实例ID，返回TestLineInstancePO对象。
- **updateTestLineBaseInfo(TestLineInstancePO testLine)**: 更新测试线基本信息，参数为TestLineInstancePO对象，返回受影响的行数。

### 返回值与业务场景

- **int类型返回值**: 表示受影响的行数，通常用于插入、更新和删除操作。
- **TestLineInstancePO对象**: 包含测试线的详细信息，用于查询操作。

### 业务场景

TestLineMapper接口主要用于测试线的批量创建、查询和更新操作。例如，在测试线批量创建时，调用batchInsert方法将多个测试线记录保存到数据库；在测试线查询时，调用getTestLineInstanceById方法根据ID获取测试线详情；在测试线更新时，调用updateTestLineBaseInfo方法更新测试线基本信息。

**Section sources**
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java)
- [TestLineInstanceInfo.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmodel/worksheet/TestLineInstanceInfo.java) - *重构了测试线实例信息模型*

## ConclusionMapper接口详解

### 方法定义与参数类型

ConclusionMapper接口定义了多个方法，用于处理结论相关的数据操作。以下是主要方法的详细说明：

- **insert(ConclusionPO record)**: 插入结论记录，参数为ConclusionPO对象，返回受影响的行数。
- **selectByPrimaryKey(String ID)**: 根据主键获取结论记录，参数为ID，返回ConclusionPO对象。
- **batchUpdate(List<ConclusionPO> list)**: 批量更新结论记录，参数为ConclusionPO列表，返回受影响的行数。

### 返回值与业务场景

- **int类型返回值**: 表示受影响的行数，通常用于插入、更新和删除操作。
- **ConclusionPO对象**: 包含结论的详细信息，用于查询操作。

### 业务场景

ConclusionMapper接口主要用于结论的创建、更新、查询和删除操作。例如，在结论创建时，调用insert方法将结论信息保存到数据库；在结论更新时，调用batchUpdate方法批量更新结论状态或其他信息；在结论查询时，调用selectByPrimaryKey方法根据ID获取结论详情。

**Section sources**
- [ConclusionMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/ConclusionMapper.java)

## MyBatis注解使用规范

在核心Mapper接口中，使用了多种MyBatis注解来定义SQL语句和参数映射。以下是常用的注解及其使用规范：

- **@Select**: 用于定义查询SQL语句，可以直接在注解中编写SQL。
- **@Insert**: 用于定义插入SQL语句，可以直接在注解中编写SQL。
- **@Update**: 用于定义更新SQL语句，可以直接在注解中编写SQL。
- **@Delete**: 用于定义删除SQL语句，可以直接在注解中编写SQL。
- **@Param**: 用于指定方法参数的名称，以便在SQL语句中引用。

这些注解的使用使得SQL语句与Java方法紧密结合，提高了代码的可读性和可维护性。同时，通过使用@Param注解，可以避免参数名称冲突，确保SQL语句的正确执行。

**Section sources**
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java)
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java)
- [ConclusionMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/ConclusionMapper.java)

## 接口使用示例

### 在Service层调用Mapper方法

在Service层中，通过依赖注入的方式获取Mapper接口的实例，然后调用其方法来执行数据库操作。以下是一个示例：

```java
@Service
public class OrderService {
    @Autowired
    private OrderMapper orderMapper;

    public void createOrder(GeneralOrderInstanceInfoPO order) {
        orderMapper.saveOrderInfo(order);
    }

    public GeneralOrderInstanceInfoPO getOrder(String orderNo) {
        return orderMapper.getOrderInfo(orderNo);
    }
}
```

在这个示例中，OrderService类通过@Autowired注解注入了OrderMapper接口的实例，然后在createOrder方法中调用saveOrderInfo方法保存订单信息，在getOrder方法中调用getOrderInfo方法获取订单信息。

**Section sources**
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java)

## 命名规范与代码风格

### 命名规范

- **接口名称**: 以Mapper结尾，如OrderMapper、SubContractMapper等。
- **方法名称**: 采用动词+名词的形式，如saveOrderInfo、updateOrderStatus等。
- **参数名称**: 采用驼峰命名法，如orderNo、reportId等。

### 代码风格

- **注释**: 每个方法都应有详细的注释，说明其功能、参数和返回值。
- **异常处理**: 在必要时进行异常处理，确保程序的健壮性。
- **代码复用**: 尽量复用已有的方法，避免重复代码。

遵循这些命名规范和代码风格，可以提高代码的可读性和可维护性，便于团队协作开发。

**Section sources**
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java)
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java)
- [ConclusionMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/ConclusionMapper.java)

## 复杂查询与关联查询处理

### 复杂查询

在处理复杂查询时，可以通过编写复杂的SQL语句来实现。例如，在ReportMapper中，getReportList方法通过传入GetReportListInfo对象来实现多条件查询：

```java
List<GetReportInfo> getReportList(GetReportListInfo getReportListInfo);
```

### 关联查询

在处理关联查询时，可以通过JOIN操作来实现。例如，在OrderMapper中，getOrderTestLineSampleList方法通过Job No获取订单、客户、样品等信息：

```java
List<OrderTestLineSamplePo> getOrderTestLineSampleList(@Param("jobNo") String jobNo);
```

这些方法通过复杂的SQL语句和JOIN操作，实现了对多个表的关联查询，满足了业务需求。

**Section sources**
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)

## 性能优化建议

### 合理使用索引

在数据库表中，为经常用于查询的字段创建索引，可以显著提高查询性能。例如，在订单表中为orderNo字段创建索引，在报告表中为reportNo字段创建索引。

### 避免N+1查询问题

在处理关联查询时，应避免N+1查询问题。可以通过一次性查询所有相关数据，然后在内存中进行关联，或者使用JOIN操作来减少数据库查询次数。

### 批量操作

在处理大量数据时，应尽量使用批量操作，如batchInsert、batchUpdate等，以减少数据库交互次数，提高性能。

### 缓存机制

对于频繁查询但不经常变化的数据，可以使用缓存机制，如Redis，来减少数据库查询次数，提高响应速度。

通过以上性能优化建议，可以有效提升系统的性能和响应速度，确保在高并发场景下的稳定运行。

**Section sources**
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java)
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java)
- [ConclusionMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/ConclusionMapper.java)