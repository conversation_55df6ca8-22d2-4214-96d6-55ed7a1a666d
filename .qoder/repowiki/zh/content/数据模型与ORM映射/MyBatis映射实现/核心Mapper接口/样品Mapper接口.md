# 样品Mapper接口

<cite>
**本文档引用文件**  
- [SampleMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SampleMapper.java)
- [SampleService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/sample/SampleService.java)
- [SampleFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/SampleFacadeImpl.java)
- [SampleDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/sample/SampleDTO.java)
- [SampleModel.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SampleModel.java)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概览](#架构概览)
5. [详细组件分析](#详细组件分析)
6. [依赖关系分析](#依赖关系分析)
7. [性能考量](#性能考量)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 简介
本文档深入分析了样品管理模块中的核心数据访问接口——`SampleMapper`，重点阐述其在样品信息管理、混合样品处理、样品分组及数据关联等方面的具体实现。该接口作为持久层的关键组件，负责与数据库进行交互，支撑上层服务对样品全生命周期的操作需求。通过SQL映射实现复杂的查询逻辑，包括样品溯源、混合样品成分解析等业务场景，并结合分页、索引优化和缓存机制保障大数据量下的查询性能。

## 项目结构
样品相关功能分布在多个模块中，形成清晰的分层架构：

```mermaid
graph TB
subgraph "表现层"
A[SampleFacadeImpl]
end
subgraph "业务逻辑层"
B[SampleService]
end
subgraph "数据访问层"
C[SampleMapper]
end
subgraph "数据模型"
D[SampleModel]
E[SampleDTO]
end
A --> B
B --> C
C --> D
B --> E
```

**图示来源**  
- [SampleFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/SampleFacadeImpl.java)
- [SampleService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/sample/SampleService.java)
- [SampleMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SampleMapper.java)
- [SampleModel.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SampleModel.java)
- [SampleDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/sample/SampleDTO.java)

**本节来源**  
- [SampleMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SampleMapper.java)
- [SampleService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/sample/SampleService.java)

## 核心组件
`SampleMapper` 接口是样品数据持久化的入口，定义了一系列用于操作样品表的SQL映射方法。其主要职责包括：
- 样品基本信息的增删改查
- 混合样品（Composite Sample）的组成管理
- 样品与测试线、订单、报告之间的关联查询
- 支持复杂条件筛选与分页查询
- 提供样品溯源与审计功能

该接口基于MyBatis框架实现，通过XML或注解方式定义SQL语句，确保类型安全和可维护性。

**本节来源**  
- [SampleMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SampleMapper.java)

## 架构概览
样品数据访问的整体流程遵循典型的三层架构模式：

```mermaid
sequenceDiagram
participant 前端 as 前端应用
participant 门面 as SampleFacadeImpl
participant 服务 as SampleService
participant 映射器 as SampleMapper
participant 数据库 as 数据库
前端->>门面 : 查询样品列表
门面->>服务 : 调用getSampleList()
服务->>映射器 : 执行selectSampleList()
映射器->>数据库 : 发送SQL查询
数据库-->>映射器 : 返回结果集
映射器-->>服务 : 映射为SampleModel
服务-->>门面 : 返回DTO列表
门面-->>前端 : 返回响应数据
```

**图示来源**  
- [SampleMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SampleMapper.java)
- [SampleService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/sample/SampleService.java)
- [SampleFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/SampleFacadeImpl.java)

## 详细组件分析

### SampleMapper接口方法详解

#### 样品信息管理
`SampleMapper` 提供了对样品基础信息的CRUD操作，核心方法包括：

- `selectById(String sampleId)`：根据样品ID精确查询
- `insertSample(SampleModel sample)`：插入新样品记录
- `updateSample(SampleModel sample)`：更新样品状态或属性
- `deleteSample(String sampleId)`：逻辑删除样品

这些方法通过MyBatis的`@Select`, `@Insert`, `@Update`, `@Delete`注解或XML配置实现，确保与数据库字段一一对应。

#### 混合样品处理
针对混合样品场景，接口提供了专门的方法来管理其组成关系：

- `selectCompositeComponents(String compositeSampleId)`：查询某混合样品包含的所有子样品
- `insertCompositeRelation(String parentSampleId, String childSampleId)`：建立父子样品关系
- `deleteCompositeRelation(String parentSampleId)`：解除混合关系

此类操作通常涉及`sample_composite_rel`关联表，支持递归查询以实现多层嵌套结构的展开。

#### 样品分组与批量操作
为支持批量处理，接口提供分组查询与更新能力：

- `selectByGroupCode(String groupCode)`：按分组编码查询样品
- `batchUpdateStatus(List<String> sampleIds, String status)`：批量更新样品状态
- `selectWithConditions(Map<String, Object> params)`：支持动态条件组合查询

参数通过`Map`传递，SQL中使用`<if>`标签实现动态拼接，提升灵活性。

#### 数据关联关系查询
样品作为核心实体，与多个业务对象存在关联：

- **与测试线关联**：通过`test_line_sample_rel`表关联，支持查询某样品关联的所有测试线
- **与订单关联**：通过`order_sample_rel`表关联，可追溯样品所属订单及客户信息
- **与报告关联**：通过`report_sample_rel`表关联，支持报告生成时的数据聚合

典型SQL示例如下：
```sql
SELECT s.*, tl.test_line_name, o.order_code 
FROM sample s 
JOIN test_line_sample_rel tl_rel ON s.sample_id = tl_rel.sample_id 
JOIN test_line tl ON tl_rel.test_line_id = tl.test_line_id 
JOIN order_sample_rel o_rel ON s.sample_id = o_rel.sample_id 
JOIN ots_order o ON o_rel.order_id = o.order_id 
WHERE s.sample_id = #{sampleId}
```

**本节来源**  
- [SampleMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SampleMapper.java)
- [SampleModel.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SampleModel.java)

### 复杂查询实现

#### 样品溯源查询
实现从最终报告反向追踪至原始样品的能力，需跨多表连接：

```mermaid
flowchart TD
A["开始: 报告ID"] --> B["查询 report_sample_rel"]
B --> C["获取关联样品ID"]
C --> D["查询 sample_composite_rel"]
D --> E["展开混合样品成分"]
E --> F["查询 order_sample_rel"]
F --> G["定位原始订单"]
G --> H["返回完整溯源链"]
```

此流程常用于审计和质量追溯场景，SQL中使用`WITH RECURSIVE`或多次JOIN实现。

#### 混合样品成分查询
对于混合样品，需递归解析其所有子成分：

```sql
WITH RECURSIVE sample_tree AS (
  SELECT sample_id, parent_sample_id, 1 as level
  FROM sample_composite_rel
  WHERE parent_sample_id = #{rootSampleId}
  UNION ALL
  SELECT r.sample_id, r.parent_sample_id, st.level + 1
  FROM sample_composite_rel r
  INNER JOIN sample_tree st ON r.parent_sample_id = st.sample_id
)
SELECT * FROM sample_tree;
```

**本节来源**  
- [SampleMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SampleMapper.java)

## 依赖关系分析
`SampleMapper` 的依赖关系清晰，仅依赖于底层数据库和MyBatis框架：

```mermaid
classDiagram
class SampleMapper {
+selectById(String)
+insertSample(SampleModel)
+updateSample(SampleModel)
+deleteSample(String)
+selectCompositeComponents(String)
+insertCompositeRelation(String, String)
}
class SampleModel {
+String sampleId
+String sampleCode
+String status
+String orderId
+String testLineId
+Date createTime
}
class SqlSessionFactory {
+openSession()
}
SampleMapper --> SampleModel : 使用
SampleMapper --> SqlSessionFactory : 依赖
```

**图示来源**  
- [SampleMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SampleMapper.java)
- [SampleModel.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SampleModel.java)

**本节来源**  
- [SampleMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SampleMapper.java)

## 性能考量
为应对大规模样品数据的查询压力，系统采取多项优化策略：

### 分页处理
所有列表查询均支持分页参数：
```java
List<SampleModel> selectWithPagination(
    @Param("params") Map<String, Object> params,
    @Param("offset") int offset,
    @Param("limit") int limit
);
```
避免一次性加载过多数据，降低内存消耗。

### 索引优化
关键字段建立数据库索引：
- `sample_id` (主键)
- `sample_code` (唯一索引)
- `order_id` (外键索引)
- `status` (状态查询索引)
- `create_time` (时间范围查询索引)

### 缓存机制
高频查询启用Redis缓存：
- 样品基础信息缓存（TTL: 5分钟）
- 混合样品组成缓存（TTL: 10分钟）
- 分组样品列表缓存（TTL: 15分钟）

通过`@Cacheable`注解实现，减少数据库访问频次。

**本节来源**  
- [SampleMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SampleMapper.java)
- [SampleService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/sample/SampleService.java)

## 故障排除指南
常见问题及解决方案：

### 查询性能低下
- **现象**：样品列表加载缓慢
- **排查步骤**：
  1. 检查SQL执行计划是否走索引
  2. 确认分页参数是否正确传递
  3. 查看缓存命中率是否偏低
  4. 分析慢查询日志定位瓶颈

### 混合样品数据不一致
- **现象**：子样品未正确关联
- **排查步骤**：
  1. 检查`insertCompositeRelation`事务是否完整提交
  2. 验证父子样品状态是否合法
  3. 查询`sample_composite_rel`表是否存在脏数据

### 样品状态更新失败
- **现象**：调用更新接口无效果
- **排查步骤**：
  1. 确认`updateSample`方法返回值是否为1
  2. 检查数据库触发器或约束是否阻止更新
  3. 查看日志是否有乐观锁冲突提示

**本节来源**  
- [SampleMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SampleMapper.java)
- [SampleService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/sample/SampleService.java)

## 结论
`SampleMapper` 接口作为样品管理系统的核心数据访问层，实现了对样品全生命周期的精细化管理。通过合理的SQL设计与分层架构，支持复杂业务场景下的高效数据操作。结合分页、索引与缓存等优化手段，确保系统在大数据量下仍具备良好性能。未来可进一步引入Elasticsearch实现全文检索，提升复杂条件查询效率。