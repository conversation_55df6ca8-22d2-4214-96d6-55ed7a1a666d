# 报告Mapper接口

<cite>
**本文档引用的文件**
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)
- [ReportInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportInfoPO.java)
- [ReportTypePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportTypePO.java)
- [EmReportDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/EmReportDTO.java)
- [ParentReportRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/report/ParentReportRsp.java)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 引言
报告Mapper接口是OTSNotes系统中负责报告数据持久化和查询的核心组件。该接口通过MyBatis框架实现，提供了对报告信息的增删改查操作，支持复杂的报告状态管理、文件关联和审批流程。本文档全面解析ReportMapper接口的设计与实现，详细说明其在报告生成、状态管理、文件关联等核心功能中的作用。

## 项目结构
报告Mapper接口位于otsnotes-dbstorages模块中，是数据访问层的重要组成部分。该接口通过XML映射文件定义SQL语句，实现了与数据库的交互。接口的设计遵循了分层架构原则，将数据访问逻辑与业务逻辑分离。

```mermaid
graph TD
subgraph "数据访问层"
ReportMapper[ReportMapper接口]
ReportMapperXML[ReportMapper.xml]
ReportInfoPO[ReportInfoPO实体]
ReportTypePO[ReportTypePO实体]
end
subgraph "业务逻辑层"
ReportService[ReportService]
OrderService[OrderService]
ConclusionService[ConclusionService]
end
subgraph "接口层"
ReportFacade[ReportFacade]
end
ReportFacade --> ReportService
ReportService --> ReportMapper
ReportMapper --> ReportMapperXML
ReportMapperXML --> ReportInfoPO
ReportMapperXML --> ReportTypePO
```

**图表来源**
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)

**章节来源**
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)

## 核心组件
报告Mapper接口的核心组件包括报告信息实体(ReportInfoPO)、报告类型实体(ReportTypePO)和接口定义本身。这些组件共同构成了报告数据管理的基础。

**章节来源**
- [ReportInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportInfoPO.java)
- [ReportTypePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportTypePO.java)

## 架构概述
报告Mapper接口采用MyBatis作为持久层框架，通过接口方法与XML映射文件的对应关系实现数据库操作。这种设计模式将SQL语句与Java代码分离，提高了代码的可维护性和可读性。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Service as "ReportService"
participant Mapper as "ReportMapper"
participant DB as "数据库"
Client->>Service : 调用业务方法
Service->>Mapper : 调用Mapper方法
Mapper->>DB : 执行SQL语句
DB-->>Mapper : 返回查询结果
Mapper-->>Service : 返回实体对象
Service-->>Client : 返回业务数据
Note over Client,DB : 报告Mapper接口的调用流程
```

**图表来源**
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)

## 详细组件分析

### 报告信息实体分析
报告信息实体(ReportInfoPO)是报告数据的核心模型，包含了报告的各种属性和状态信息。

```mermaid
classDiagram
class ReportInfoPO {
+String ID
+Integer labId
+String labCode
+String orderNo
+String reportNo
+String parentReportNo
+Date reportDueDate
+Integer reportStatus
+String coverPageTemplateID
+String coverPageTemplateName
+String coverPageTemplatePath
+Boolean coverPageTemplateNewMappingFlag
+String templateID
+String reportTypeID
+String requestID
+Date requestSentDate
+Date requestFinishedDate
+String approverBy
+String approver
+Date approverDate
+String customerCode
+String customerGroupCode
+String certificateId
+String certificateFileCloudKey
+String certificateName
+String amendRemark
+Boolean activeIndicator
+String createdBy
+Date createdDate
+String modifiedBy
+Date modifiedDate
+Integer recalculationFlag
+String conclusionMd5
+String logoAliyunID
+String actualReportNo
+String rootReportNo
+Integer reportVersion
+String testMatrixMergeMode
+getReportNo() String
+setReportNo(String) void
+getReportStatus() Integer
+setReportStatus(Integer) void
+getApproverBy() String
+setApproverBy(String) void
+getApprover() String
+setApprover(String) void
+getApproverDate() Date
+setApproverDate(Date) void
+getRecalculationFlag() Integer
+setRecalculationFlag(Integer) void
+getConclusionMd5() String
+setConclusionMd5(String) void
}
```

**图表来源**
- [ReportInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportInfoPO.java)

**章节来源**
- [ReportInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportInfoPO.java)

### 报告Mapper接口方法分析
报告Mapper接口定义了多个方法，用于处理报告的增删改查操作。

```mermaid
classDiagram
class ReportMapper {
+saveReportInfo(ReportInfoPO) int
+updateReportStatus(ReportInfoPO) int
+updateReportStatusInfo(ReportInfoPO) int
+updateReportRecalculationFlag(ReportInfoPO) int
+updateApproverReportStatus(ReportInfoPO) int
+getReportInfoByReportNo(String) ReportInfoPO
+getReportInfoByParentReportNo(String, String) ReportInfoPO
+getReportInfoByReportId(String) ReportInfoPO
+getReportTypeByDesc(String) ReportTypePO
+getReportTypeByDescAndLanguage(String, Integer) ReportTypePO
+getReportInfoByOrderNo(String) EmReportDTO
+getReportByOrderNo(String) ReportInfoPO
+getReportStatusByReportId(String) Integer
+getReportStatusByOrderNo(String) Integer
+getActivateReportStatusByOrderNo(String) ReportInfoPO
+batchSaveReportMatrixRelInfo(List<ReportMatrixRelationShipInfoPO>) int
+delReportMatrixRelInfo(List<String>) int
+updateRecalculationFlagByReportId(ReportInfoPO) int
+updateReportStatusByOrderNo(ReportInfoPO) int
+updateRequestIdByReportId(ReportInfoPO) void
+deleteMd5ByReportId(String) int
+updateConclusionMd5(ConclusionMd5Info) int
+cancelReportByOrderNo(ReportInfoPO) int
+quertTestLineConclusion(String) List<GetTestLineConclusionRsp>
+getReportTestLineConclusions(String, Integer) List<ReportTestLineConclusionDTO>
+getReportByTestLineInstanceID(String) ReportInfoPO
+updateReportByID(ReportInfoPO) int
+getReportListByOrderNo(String) List<ReportInfoPO>
+getReportList(GetReportListInfo) List<GetReportInfo>
+updateReportDetailById(ReportInfoPO) int
+getFailNoteInfo(String) FailNoteDTO
+getReportTestLineEvaluationAlias(Set<String>) List<ReportTestLineConclusionDTO>
+queryReportTemplateByCustomerCode(String) List<ReportTemplateRsp>
+queryReportFile(String) List<ReportFileDTO>
+getParentReportByReportNo(ParentReportReq) List<ParentReportRsp>
}
```

**图表来源**
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)

**章节来源**
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)

### SQL映射分析
报告Mapper的SQL映射文件定义了具体的数据库操作语句，包括插入、更新、删除和查询操作。

```mermaid
flowchart TD
Start([开始]) --> SaveReport["保存报告信息"]
SaveReport --> Insert["INSERT INTO tb_report"]
Insert --> Update["ON DUPLICATE KEY UPDATE"]
Update --> End1([结束])
Start --> UpdateStatus["更新报告状态"]
UpdateStatus --> UpdateSQL["UPDATE tb_report SET ReportStatus"]
UpdateSQL --> End2([结束])
Start --> GetReport["查询报告信息"]
GetReport --> Select["SELECT * FROM tb_report"]
Select --> Condition["WHERE条件判断"]
Condition --> End3([结束])
Start --> BatchSave["批量保存报告矩阵关系"]
BatchSave --> InsertBatch["INSERT INTO tre_report_matrix_relationship"]
InsertBatch --> Foreach["FOREACH循环"]
Foreach --> End4([结束])
Start --> DeleteRel["删除报告矩阵关系"]
DeleteRel --> DeleteSQL["DELETE FROM tre_report_matrix_relationship"]
DeleteSQL --> InClause["IN子句"]
InClause --> End5([结束])
style Start fill:#9f9,stroke:#333
style End1 fill:#f9f,stroke:#333
style End2 fill:#f9f,stroke:#333
style End3 fill:#f9f,stroke:#333
style End4 fill:#f9f,stroke:#333
style End5 fill:#f9f,stroke:#333
```

**图表来源**
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)

**章节来源**
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)

## 依赖分析
报告Mapper接口与其他组件存在紧密的依赖关系，这些关系构成了系统的数据访问基础。

```mermaid
graph TD
ReportMapper --> ReportInfoPO
ReportMapper --> ReportTypePO
ReportMapper --> ReportMatrixRelationShipInfoPO
ReportMapper --> ConclusionMd5Info
ReportMapper --> EmReportDTO
ReportMapper --> ParentReportRsp
ReportMapper --> GetTestLineConclusionRsp
ReportMapper --> ReportTestLineConclusionDTO
ReportMapper --> GetReportInfo
ReportMapper --> ReportTemplateRsp
ReportMapper --> ReportFileDTO
ReportService --> ReportMapper
OrderService --> ReportMapper
ConclusionService --> ReportMapper
TestLineService --> ReportMapper
SubContractService --> ReportMapper
style ReportMapper fill:#f96,stroke:#333
style ReportInfoPO fill:#69f,stroke:#333
style ReportTypePO fill:#69f,stroke:#333
```

**图表来源**
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [ReportInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportInfoPO.java)

**章节来源**
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)

## 性能考虑
报告Mapper接口在设计时考虑了多种性能优化策略，以确保在高并发场景下的数据一致性。

### 查询优化
接口中的查询方法都使用了适当的索引和LIMIT子句，避免了全表扫描。例如，getReportInfoByReportNo方法通过ReportNo字段进行查询，并使用LIMIT 1限制返回结果数量。

### 批量操作
对于需要处理大量数据的场景，接口提供了批量操作方法，如batchSaveReportMatrixRelInfo，通过单次数据库交互完成多个记录的插入，减少了网络开销和数据库连接次数。

### 缓存机制
虽然Mapper接口本身不直接实现缓存，但其设计考虑了与外部缓存系统的集成。例如，频繁查询的报告类型信息可以通过外部缓存减少数据库访问压力。

**章节来源**
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)

## 故障排除指南
在使用报告Mapper接口时，可能会遇到一些常见问题，以下是相应的解决方案。

### 报告状态更新失败
当报告状态更新失败时，首先检查传入的ReportInfoPO对象是否包含正确的reportNo和reportStatus值。同时确认数据库连接是否正常，以及是否有足够的权限执行UPDATE操作。

### 报告信息查询为空
如果查询返回空结果，需要验证查询条件是否正确。特别是对于getReportInfoByReportNo方法，确保传入的reportNo值在数据库中存在。可以先通过数据库直接查询确认数据的完整性。

### 批量保存性能问题
当批量保存大量报告矩阵关系时，如果出现性能问题，可以考虑分批处理数据，避免单次操作数据量过大导致的超时或内存溢出。

**章节来源**
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)

## 结论
报告Mapper接口是OTSNotes系统中关键的数据访问组件，通过精心设计的接口方法和SQL映射，实现了对报告信息的高效管理。接口不仅支持基本的CRUD操作，还提供了复杂的查询功能，满足了报告生成、状态管理、文件关联等业务需求。通过合理的性能优化和错误处理机制，该接口能够在高并发场景下保持稳定可靠的数据访问服务。