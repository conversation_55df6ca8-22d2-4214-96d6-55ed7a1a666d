# XML映射文件

<cite>
**本文档中引用的文件**   
- [OrderMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/OrderMapper.xml)
- [SubContractMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractMapper.xml)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml) - *在最近的提交中更新*
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/SubContractMapper.java)
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [DataEntryMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/DataEntryMapper.java) - *在最近的提交中更新*
- [GeneralOrderInstanceInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/GeneralOrderInstanceInfoPO.java)
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java)
- [ReportInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportInfoPO.java)
- [DataEntryInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/dataentry/DataEntryInfo.java)
- [DataEntryConclusionReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/req/dataentry/DataEntryConclusionReq.java)
</cite>

## 更新摘要
**已做更改**   
- 更新了DataEntryMapper.xml中关于LabSectionCode模糊查询的SQL语句，确保在正确的表别名上执行
- 添加了对DataEntryMapper.xml文件的详细分析，包括其在数据录入模块中的作用
- 更新了相关章节的来源信息，反映了最新的代码变更
- 修正了模糊查询条件中表别名的使用，防止潜在的查询错误
- 根据重构提交，更新了DataEntryMapper.xml中与测试线实例信息模型相关的SQL映射

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 简介
本文档详细记录了otsnotes-service项目中XML映射文件的实现，重点分析了OrderMapper.xml、SubContractMapper.xml、ReportMapper.xml和DataEntryMapper.xml的结构与内容。文档解释了XML文件中的namespace、resultMap、parameterType等关键元素的使用，深入说明了动态SQL的实现，包括if、choose、when、otherwise、where、set、foreach等标签的使用场景和最佳实践。同时，文档描述了复杂查询的SQL编写规范，如多表关联、子查询、分页查询的实现方式，并为开发者提供了SQL性能优化建议，如索引使用、查询计划分析、避免全表扫描等。最后，文档包含了XML映射文件的维护规范和版本控制策略。

## 项目结构
otsnotes-service项目采用分层架构设计，主要包含核心业务逻辑、数据库存储、领域服务、外观接口等模块。XML映射文件位于`otsnotes-dbstorages`模块的`src/main/resources/sqlmap/userdefined/`目录下，与MyBatis框架配合使用，实现Java对象与数据库表之间的映射。

```mermaid
graph TB
subgraph "otsnotes-service"
subgraph "otsnotes-dbstorages"
OrderMapperXml[OrderMapper.xml]
SubContractMapperXml[SubContractMapper.xml]
ReportMapperXml[ReportMapper.xml]
DataEntryMapperXml[DataEntryMapper.xml]
OrderMapperJava[OrderMapper.java]
SubContractMapperJava[SubContractMapper.java]
ReportMapperJava[ReportMapper.java]
DataEntryMapperJava[DataEntryMapper.java]
GeneralOrderPO[GeneralOrderInstanceInfoPO.java]
SubContractPO[SubContractPO.java]
ReportInfoPO[ReportInfoPO.java]
end
subgraph "otsnotes-domain"
OrderService[OrderService.java]
SubContractService[SubContractService.java]
ReportService[ReportService.java]
DataEntryService[DataEntryService.java]
end
subgraph "otsnotes-facade"
OrderFacade[OrderFacade.java]
SubContractFacade[SubContractFacade.java]
ReportFacade[ReportFacade.java]
DataEntryFacade[DataEntryFacade.java]
end
end
OrderMapperXml --> OrderMapperJava
SubContractMapperXml --> SubContractMapperJava
ReportMapperXml --> ReportMapperJava
DataEntryMapperXml --> DataEntryMapperJava
OrderMapperJava --> GeneralOrderPO
SubContractMapperJava --> SubContractPO
ReportMapperJava --> ReportInfoPO
DataEntryMapperJava --> GeneralOrderPO
OrderService --> OrderMapperJava
SubContractService --> SubContractMapperJava
ReportService --> ReportMapperJava
DataEntryService --> DataEntryMapperJava
OrderFacade --> OrderService
SubContractFacade --> SubContractService
ReportFacade --> ReportService
DataEntryFacade --> DataEntryService
```

**图示来源**
- [OrderMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/OrderMapper.xml)
- [SubContractMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractMapper.xml)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml)
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/SubContractMapper.java)
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [DataEntryMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/DataEntryMapper.java)

**本节来源**
- [OrderMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/OrderMapper.xml)
- [SubContractMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractMapper.xml)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml)

## 核心组件
XML映射文件是MyBatis框架的核心组成部分，负责定义SQL语句与Java方法之间的映射关系。在otsnotes-service项目中，OrderMapper.xml、SubContractMapper.xml、ReportMapper.xml和DataEntryMapper.xml分别对应订单、分包、报告和数据录入三个核心业务模块的数据访问操作。

**本节来源**
- [OrderMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/OrderMapper.xml)
- [SubContractMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractMapper.xml)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml)

## 架构概述
XML映射文件采用标准的MyBatis 3.0 DTD定义，通过namespace属性与对应的Java Mapper接口关联。每个Mapper文件包含多个SQL语句定义，包括insert、update、delete和select操作，通过id属性与接口中的方法名对应。parameterType属性指定输入参数类型，resultType或resultMap属性指定返回结果类型。

```mermaid
classDiagram
class OrderMapper {
+saveOrderInfo(GeneralOrderInstanceInfoPO) int
+updateOrderInfo(GeneralOrderInstanceInfoPO) int
+getOrderInfo(String) GeneralOrderInstanceInfoPO
}
class SubContractMapper {
+saveSubContract(SubContractPO) int
+updateSubContract(SubContractPO) int
+getSubContractByOrderNo(String) SubContractPO
}
class ReportMapper {
+saveReportInfo(ReportInfoPO) int
+updateReportStatus(ReportInfoPO) int
+getReportInfoByReportNo(String) ReportInfoPO
}
class DataEntryMapper {
+getDataEntryList(DataEntryConclusionReq) List<DataEntryInfo>
+getDataEntryListWithoutPP(DataEntryConclusionReq) List<DataEntryInfo>
+getDataEntryListNew(DataEntryConclusionReq) List<DataEntryInfo>
+getDataEntryMatrixListNew(DataEntryMatrixReq) List<DataEntryMatrixInfo>
}
class GeneralOrderInstanceInfoPO {
+String ID
+String orderNo
+Integer orderStatus
+String customerCode
+String customerName
+String customerGroupCode
+String customerGroupName
+Integer labId
+String labCode
+Integer orderLaboratoryID
+Boolean activeIndicator
+Date createdDate
+String createdBy
+Date modifiedDate
+String modifiedBy
+Integer conclusionMode
}
class SubContractPO {
+String ID
+String orderNo
+String subContractNo
+String subContractLabName
+String subContractServiceType
+Date subContractExpectDueDate
+String additionalInfo
+String subContractRemark
+String createdBy
+Date createdDate
+String modifiedBy
+Date modifiedDate
+String subContractLabCode
+String slimJobNo
+Integer status
+String errorMsg
+Integer syncStatus
+Date startDate
+Date completeDate
+String subContractContract
+String subContractContractTel
+String subContractContractEmail
+String toSlimBy
+Integer subContractOrder
+Integer dataLock
+Integer subcontractTAT
+String extData
}
class ReportInfoPO {
+String ID
+Integer labId
+String labCode
+String orderNo
+String reportNo
+String parentReportNo
+Date reportDueDate
+Integer reportStatus
+String coverPageTemplateID
+String coverPageTemplateName
+String coverPageTemplatePath
+Boolean coverPageTemplateNewMappingFlag
+String templateID
+String reportTypeID
+String requestID
+Date requestSentDate
+Date requestFinishedDate
+String approverBy
+String approver
+Date approverDate
+String customerCode
+String customerGroupCode
+String certificateId
+String certificateFileCloudKey
+String certificateName
+String amendRemark
+Boolean activeIndicator
+String createdBy
+Date createdDate
+String modifiedBy
+Date modifiedDate
+Integer recalculationFlag
+String conclusionMd5
+Date lastModifiedTimestamp
+String awbNo
+Integer isLastReport
+String remark
+Boolean isToDm
+Boolean isDeliveryApprove
+String deliverReportFormat
+Integer qrcodeFlag
+Integer approveStatus
+Integer watermarkCode
+String logoAliyunID
+Integer amendReportType
+Boolean sealFlag
+Integer reportFlag
+Integer needReview
+Integer typingFinishedFlag
+String actualReportNo
+Integer operationType
+Date softcopyDeliveryDate
+String externalReportNo
+Integer workFlow
+String editorBy
+String editor
+String reviewerBy
+String reviewer
+Integer subReportReviseFlag
+Integer needToAmendFlag
+Integer signatureLanguage
+String countryOfDestination
+String rootReportNo
+Integer reportVersion
+String testMatrixMergeMode
}
class DataEntryInfo {
+String testLineInstanceId
+String orderNo
+Integer testLineId
+Integer testLineStatus
+String labSectionCode
+Integer labSectionSeq
+Integer testLineStyle
+String subContractLab
+String subContractNo
+String subContractName
+String testLineEvaluation
+String evaluationAlias
+String standardName
+String jobNo
+String orderId
+String ppArtifactRelId
+String citationBaseId
+List<DataEntryMatrixInfo> matrixs
}
OrderMapper --> GeneralOrderInstanceInfoPO : "使用"
SubContractMapper --> SubContractPO : "使用"
ReportMapper --> ReportInfoPO : "使用"
DataEntryMapper --> DataEntryInfo : "使用"
```

**图示来源**
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/SubContractMapper.java)
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [DataEntryMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/DataEntryMapper.java)
- [GeneralOrderInstanceInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/GeneralOrderInstanceInfoPO.java)
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java)
- [ReportInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportInfoPO.java)
- [DataEntryInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/dataentry/DataEntryInfo.java)

## 详细组件分析

### OrderMapper.xml分析
OrderMapper.xml文件定义了订单相关数据访问操作，包括订单信息的保存、更新和查询。文件通过namespace属性与com.sgs.otsnotes.dbstorages.mybatis.extmapper.OrderMapper接口关联。

#### 关键元素说明
- **namespace**: 指定Mapper接口的全限定名，建立XML文件与Java接口的映射关系
- **parameterType**: 指定SQL语句输入参数的Java类型
- **resultType**: 指定SQL语句返回结果的Java类型
- **sql**: 定义可重用的SQL片段，通过include标签引用
- **insert/update/delete/select**: 定义不同类型的SQL操作

#### 动态SQL实现
OrderMapper.xml中使用了多种动态SQL标签，提高了SQL语句的灵活性和可维护性。

```mermaid
flowchart TD
Start([开始]) --> Where["<where>标签"]
Where --> If1["<if test=\"orderNo != null and orderNo != ''\">"]
If1 --> Condition1["AND OrderNo = #{orderNo}"]
Where --> If2["<if test=\"labId > 0\">"]
If2 --> Condition2["AND OrderLaboratoryID = #{labId}"]
Where --> If3["<if test=\"startDate != null\">"]
If3 --> Condition3["AND CreatedDate >= #{startDate}"]
Where --> EndWhere["</where>"]
Start --> If4["<if test=\"subContractNo != null and subContractNo != '' \">"]
If4 --> And["AND tss.SubContractNo = #{subContractNo}"]
If4 --> EndIf["</if>"]
EndWhere --> OrderBy["ORDER BY CreatedDate ASC"]
OrderBy --> Limit["LIMIT ${offset},${rows}"]
Limit --> End([结束])
```

**图示来源**
- [OrderMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/OrderMapper.xml#L150-L180)

**本节来源**
- [OrderMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/OrderMapper.xml)
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [GeneralOrderInstanceInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/GeneralOrderInstanceInfoPO.java)

### SubContractMapper.xml分析
SubContractMapper.xml文件定义了分包相关数据访问操作，包括分包信息的保存、更新和查询。文件通过namespace属性与com.sgs.otsnotes.dbstorages.mybatis.extmapper.SubContractMapper接口关联。

#### 复杂查询实现
SubContractMapper.xml中包含多个复杂查询，涉及多表关联和条件判断。

```mermaid
erDiagram
tb_sub_contract {
string ID PK
string orderNo FK
string subContractNo
string subContractLabName
string subContractServiceType
datetime subContractExpectDueDate
string additionalInfo
string subContractRemark
string createdBy
datetime createdDate
string modifiedBy
datetime modifiedDate
string subContractLabCode
string slimJobNo
integer status
string errorMsg
integer syncStatus
datetime startDate
datetime completeDate
string subContractContract
string subContractContractTel
string subContractContractEmail
string toSlimBy
integer subContractOrder
integer dataLock
integer subcontractTAT
string extData
}
tb_subcontract_external_relationship {
string subContractNo FK
string externalNo PK
}
tb_general_order_instance {
string ID PK
string orderNo UK
integer orderStatus
string customerCode
string customerName
string customerGroupCode
string customerGroupName
integer labId
string labCode
integer orderLaboratoryID
boolean activeIndicator
datetime createdDate
string createdBy
datetime modifiedDate
string modifiedBy
integer conclusionMode
}
tb_sub_contract ||--o{ tb_subcontract_external_relationship : "包含"
tb_general_order_instance ||--o{ tb_sub_contract : "关联"
```

**图示来源**
- [SubContractMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractMapper.xml)
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java)

**本节来源**
- [SubContractMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractMapper.xml)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/SubContractMapper.java)
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java)

### ReportMapper.xml分析
ReportMapper.xml文件定义了报告相关数据访问操作，包括报告信息的保存、更新和查询。文件通过namespace属性与com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportMapper接口关联。

#### 性能优化建议
ReportMapper.xml中的查询语句应考虑以下性能优化措施：
1. 为常用查询条件字段创建索引，如reportNo、orderNo、testLineInstanceId等
2. 避免使用SELECT *，只查询需要的字段
3. 合理使用分页查询，避免返回过多数据
4. 定期分析查询执行计划，优化慢查询

```mermaid
flowchart TD
A["查询报告信息"] --> B["检查索引使用"]
B --> C{"是否有合适索引?"}
C --> |是| D["使用索引扫描"]
C --> |否| E["全表扫描"]
D --> F["返回结果"]
E --> F
F --> G["分析查询性能"]
G --> H{"是否需要优化?"}
H --> |是| I["创建索引或优化查询"]
H --> |否| J["完成"]
I --> K["重新执行查询"]
K --> G
```

**图示来源**
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)

**本节来源**
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [ReportInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportInfoPO.java)

### DataEntryMapper.xml分析
DataEntryMapper.xml文件定义了数据录入相关数据访问操作，包括数据录入列表的查询和矩阵数据的获取。文件通过namespace属性与com.sgs.otsnotes.dbstorages.mybatis.extmapper.DataEntryMapper接口关联。

#### 关键元素说明
- **namespace**: 指定Mapper接口的全限定名，建立XML文件与Java接口的映射关系
- **resultMap**: 定义了DataEntryInfo对象与数据库字段的映射关系，包括嵌套的集合映射
- **parameterType**: 指定SQL语句输入参数的Java类型，如DataEntryConclusionReq
- **select**: 定义查询操作，包括getDataEntryList、getDataEntryListWithoutPP等方法

#### 动态SQL实现
DataEntryMapper.xml中使用了多种动态SQL标签，提高了SQL语句的灵活性和可维护性。

```mermaid
flowchart TD
Start([开始]) --> Where["<where>标签"]
Where --> If1["<if test=\"orderNo != null and orderNo != ''\">"]
If1 --> Condition1["AND o.orderNo = #{orderNo}"]
Where --> If2["<if test=\"jobNo != null and jobNo != ''\">"]
If2 --> Condition2["AND job.jobNo = #{jobNo}"]
Where --> If3["<if test=\"labSectionCode != null and labSectionCode != ''\">"]
If3 --> Condition3["AND lab.LabSectionCode like CONCAT('%',#{labSectionCode},'%')"]
Where --> If4["<if test=\"testLineStatus != 0\">"]
If4 --> Condition4["AND tl.TestLineStatus=#{testLineStatus}"]
Where --> If5["<if test=\"blockTop == null or blockTop == ''\">"]
If5 --> Condition5["AND (o.LabCode = #{labCode} or (oclr.toLab = #{labCode} and oclr.execType = 1))"]
Where --> EndWhere["</where>"]
EndWhere --> OrderBy["ORDER BY CreatedDate ASC"]
OrderBy --> End([结束])
```

**图示来源**
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml#L150-L180)

**本节来源**
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml) - *在最近的提交中更新*
- [DataEntryMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/DataEntryMapper.java) - *在最近的提交中更新*
- [DataEntryInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/dataentry/DataEntryInfo.java)
- [DataEntryConclusionReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/req/dataentry/DataEntryConclusionReq.java)

## 依赖分析
XML映射文件与Java Mapper接口、PO模型类和业务服务层紧密耦合，形成完整的数据访问层。

```mermaid
graph TD
A[OrderMapper.xml] --> B[OrderMapper.java]
B --> C[GeneralOrderInstanceInfoPO.java]
C --> D[OrderService.java]
D --> E[OrderFacade.java]
F[SubContractMapper.xml] --> G[SubContractMapper.java]
G --> H[SubContractPO.java]
H --> I[SubContractService.java]
I --> J[SubContractFacade.java]
K[ReportMapper.xml] --> L[ReportMapper.java]
L --> M[ReportInfoPO.java]
M --> N[ReportService.java]
N --> O[ReportFacade.java]
P[DataEntryMapper.xml] --> Q[DataEntryMapper.java]
Q --> R[DataEntryInfo.java]
R --> S[DataEntryService.java]
S --> T[DataEntryFacade.java]
E --> U[前端/外部系统]
J --> U
O --> U
T --> U
```

**图示来源**
- [OrderMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/OrderMapper.xml)
- [SubContractMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractMapper.xml)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml)
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/SubContractMapper.java)
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [DataEntryMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/DataEntryMapper.java)

**本节来源**
- [OrderMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/OrderMapper.xml)
- [SubContractMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractMapper.xml)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml)

## 性能考虑
XML映射文件的性能直接影响系统整体性能，需重点关注以下方面：
1. SQL语句优化：避免全表扫描，合理使用索引
2. 查询结果集大小：使用分页查询，避免返回过多数据
3. 连接池配置：合理配置数据库连接池参数
4. 缓存策略：对于频繁查询的静态数据，考虑使用缓存
5. 批量操作：对于大量数据的插入、更新操作，使用批量处理

## 故障排除指南
当XML映射文件出现问题时，可参考以下步骤进行排查：
1. 检查namespace是否与Mapper接口匹配
2. 检查SQL语句语法是否正确
3. 检查parameterType和resultType是否正确
4. 检查数据库表结构是否与PO类匹配
5. 查看日志输出，定位具体错误信息
6. 使用数据库管理工具直接执行SQL语句，验证查询逻辑

**本节来源**
- [OrderMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/OrderMapper.xml)
- [SubContractMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractMapper.xml)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml)

## 结论
XML映射文件是otsnotes-service项目数据访问层的核心组件，通过MyBatis框架实现了Java对象与数据库表之间的灵活映射。合理的XML映射文件设计不仅提高了代码的可维护性，还为系统性能优化提供了基础。开发者应遵循最佳实践，合理使用动态SQL，优化查询性能，并建立完善的维护和版本控制策略。