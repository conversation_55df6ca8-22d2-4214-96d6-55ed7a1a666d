# 分包XML映射

<cite>
**本文档引用的文件**   
- [SubContractMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/SubContractMapper.xml)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java)
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java)
- [SubContractExtMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/SubContractExtMapper.java)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 引言
本文档深入分析了`SubContractMapper.xml`文件的实现细节，重点说明分包单相关数据库操作的SQL映射。文档详细解析了分包单创建、状态更新、数据同步等核心操作的SQL语句，包括动态SQL条件构建和复杂查询逻辑。同时，文档解释了`namespace`配置为`com.sgs.otsnotes.dbstorages.mybatis.mapper.SubContractMapper`的关联机制，并说明了`resultMap`如何映射`SubContract`实体与数据库字段，以及在处理分包单与订单、分包单与测试线等关联关系时的映射策略。最后，文档提供了针对分包业务场景的SQL性能优化建议。

## 项目结构
本项目采用分层架构设计，主要分为以下几个模块：
- `otsnotes-core`：核心工具类和配置
- `otsnotes-dbstorages`：数据库存储层，包含MyBatis映射文件和模型
- `otsnotes-domain`：业务逻辑层，包含核心服务实现
- `otsnotes-facade`：外观层，提供对外接口
- `otsnotes-web`：Web层，处理HTTP请求

```mermaid
graph TD
Web[otsnotes-web] --> Facade[otsnotes-facade]
Facade --> Domain[otsnotes-domain]
Domain --> DbStorages[otsnotes-dbstorages]
DbStorages --> Core[otsnotes-core]
```

**图源**
- [项目结构](file://README.md)

**本节来源**
- [README.md](file://README.md)

## 核心组件
分包单功能的核心组件主要包括：
- `SubContractMapper.xml`：MyBatis SQL映射文件
- `SubContractMapper.java`：MyBatis Mapper接口
- `SubContractService.java`：业务服务实现
- `SubContractPO.java`：持久化对象

这些组件共同实现了分包单的创建、查询、更新和删除等核心功能。

**本节来源**
- [SubContractMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/SubContractMapper.xml)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)

## 架构概述
分包单功能的架构采用典型的分层模式，从上到下分别为：
1. Web层：接收HTTP请求
2. 外观层：提供统一的API接口
3. 业务层：实现核心业务逻辑
4. 数据访问层：执行数据库操作

```mermaid
graph TD
Client[客户端] --> Controller[Web控制器]
Controller --> Facade[外观服务]
Facade --> Service[业务服务]
Service --> Mapper[数据访问层]
Mapper --> Database[数据库]
```

**图源**
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)

## 详细组件分析

### SubContractMapper.xml分析
`SubContractMapper.xml`是分包单功能的核心SQL映射文件，定义了所有与分包单相关的数据库操作。

#### 命名空间配置
```xml
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.SubContractMapper">
```
该命名空间配置将XML文件中的SQL语句与Java接口`SubContractMapper`关联起来，MyBatis通过动态代理机制实现接口方法与SQL语句的映射。

#### resultMap映射
```xml
<resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.SubContractPO">
    <id column="ID" property="ID" jdbcType="VARCHAR"/>
    <result column="SUB_CONTRACT_NO" property="subContractNo" jdbcType="VARCHAR"/>
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
    <!-- 其他字段映射 -->
</resultMap>
```
`resultMap`定义了数据库字段与Java对象属性的映射关系，确保数据能够正确地从数据库映射到`SubContractPO`对象。

#### 动态SQL示例
```xml
<select id="selectByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.SubContractExample" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List"/>
    FROM tb_sub_contract
    <where>
        <foreach collection="oredCriteria" item="criteria" separator="or">
            <trim prefix="(" prefixOverrides="and" suffix=")">
                <foreach collection="criteria.criteria" item="criterion">
                    <choose>
                        <when test="criterion.noValue">
                            and ${criterion.condition}
                        </when>
                        <when test="criterion.singleValue">
                            and ${criterion.condition} #{criterion.value}
                        </when>
                        <otherwise>
                            and ${criterion.condition} #{criterion.value,typeHandler=org.apache.ibatis.type.JdbcType.VARCHAR}
                        </otherwise>
                    </choose>
                </foreach>
            </trim>
        </foreach>
    </where>
    <if test="orderByClause != null">
        order by ${orderByClause}
    </if>
</select>
```
该SQL使用了MyBatis的动态SQL特性，支持根据条件动态构建查询语句，提高了SQL的灵活性和复用性。

**本节来源**
- [SubContractMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/SubContractMapper.xml)

### SubContractService.java分析
`SubContractService.java`是分包单业务逻辑的核心实现类，负责处理分包单的各种业务操作。

#### 分包单完成处理
```java
public CustomResult prepareCompleteSubContract(PrepareCompleteSubContractReq reqObject) {
    // ... 业务逻辑
    DatabaseContextHolder.setTargetDataSource(() -> {
        // 找分包单
        SubContractExample subContractExample = new SubContractExample();
        subContractExample.createCriteria().andSubContractNoEqualTo(subContractNo);
        List<SubContractPO> subContractPOS = this.subContractMapper.selectByExample(subContractExample);
        // ... 处理逻辑
    });
    // ... 返回结果
}
```
该方法处理分包单完成的业务逻辑，包括状态更新、测试线状态更新等操作。

#### 分包单绑定
```java
@BizLog(bizType = BizLogConstant.TEST_HISTORY, operType = "Bind SubContract")
public CustomResult bindSubContract(BindSubContractReq reqObject) {
    // ... 参数校验
    boolean isSuccess = transactionTemplate.execute((trans) -> {
        // 更新引用关系
        com.sgs.preorder.facade.model.common.BaseResponse<Boolean> response = orderClient.updateReferenceForBindSub(updateReq);
        // 更新测试线状态
        testLineStatusService.updateTestLineStatus(TestLineModuleType.SubContract, tls);
        // 更新分包单状态
        int execNum = subContractExtMapper.updateSubContractType(updatePO);
        // 插入外部关系
        execNum += subcontractExternalRelationshipMapper.insert(po);
        return execNum > 0;
    });
    // ... 返回结果
}
```
该方法实现了分包单绑定功能，使用事务确保数据一致性。

**本节来源**
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)

## 依赖分析
分包单功能依赖于多个外部服务和组件：

```mermaid
graph TD
SubContractService[SubContractService] --> SubContractMapper[SubContractMapper]
SubContractService --> OrderClient[OrderClient]
SubContractService --> TestLineStatusService[TestLineStatusService]
SubContractService --> TestDataBizClient[TestDataBizClient]
SubContractService --> TokenClient[TokenClient]
SubContractService --> FrameWorkClient[FrameWorkClient]
SubContractMapper --> Database[数据库]
```

**图源**
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)

## 性能考虑
针对分包业务场景，可以考虑以下SQL性能优化建议：

1. **批量操作优化**：对于批量插入或更新操作，使用MyBatis的批量操作功能，减少数据库交互次数。
2. **关联查询优化**：合理使用JOIN操作，避免N+1查询问题。
3. **索引优化**：为常用查询条件的字段创建索引，如`sub_contract_no`、`order_no`等。
4. **分页查询**：对于大数据量的查询，使用分页查询避免内存溢出。
5. **缓存机制**：对于不经常变化的数据，可以使用Redis等缓存机制减少数据库压力。

## 故障排除指南
常见问题及解决方案：

1. **分包单状态更新失败**：检查事务配置，确保事务正确提交。
2. **关联数据查询为空**：检查外键关系是否正确，确保关联数据存在。
3. **性能问题**：使用SQL监控工具分析慢查询，优化SQL语句和索引。
4. **数据不一致**：检查事务边界，确保相关操作在同一个事务中执行。

**本节来源**
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)

## 结论
通过对`SubContractMapper.xml`文件的深入分析，我们了解了分包单相关数据库操作的SQL映射机制。文档详细解析了分包单创建、状态更新、数据同步等核心操作的SQL语句，包括动态SQL条件构建和复杂查询逻辑。同时，文档解释了`namespace`配置的关联机制，并说明了`resultMap`的映射策略。最后，文档提供了针对分包业务场景的SQL性能优化建议，为系统的稳定运行和性能优化提供了指导。