# 订单实例模型

<cite>
**本文档引用的文件**
- [JobDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/JobDTO.java)
- [Job.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Job.java)
- [JobFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/JobFacade.java)
- [JobFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/JobFacadeImpl.java)
</cite>

## 目录
1. [引言](#引言)
2. [订单实例模型概述](#订单实例模型概述)
3. [数据库表结构与ORM映射](#数据库表结构与orm映射)
4. [核心字段定义](#核心字段定义)
5. [订单实例与主订单关系](#订单实例与主订单关系)
6. [状态联动机制](#状态联动机制)
7. [生命周期管理](#生命周期管理)
8. [业务操作与数据一致性](#业务操作与数据一致性)
9. [查询优化与性能监控](#查询优化与性能监控)
10. [结论](#结论)

## 引言
本文档旨在详细描述订单实例（OrderInstance/Job）模型的设计与实现，重点涵盖其数据库表结构、ORM映射、字段定义、状态管理、生命周期及性能优化策略。订单实例作为主订单的执行单元，在系统中承担着测试任务分配、样品处理和状态追踪的核心职责。

## 订单实例模型概述

订单实例（Job）是主订单在具体执行过程中的实例化表现，代表一个可独立执行的测试任务单元。每个订单实例与主订单形成1:N的关系，支持同一订单下多个测试项目并行处理。

**Section sources**
- [JobDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/JobDTO.java)
- [Job.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Job.java)

## 数据库表结构与ORM映射

订单实例的持久化通过MyBatis框架实现，ORM映射定义在`Job.java`实体类中。该类位于`otsnotes-facade-model`模块的`po/xml`包下，采用XML配置方式进行映射管理。

```mermaid
erDiagram
JOB {
string jobNo PK
string generalOrderInstanceID FK
integer jobStatus
datetime labInDate
datetime labOutDate
string remark
integer labSectionId
string labSectionName
string testLineInstanceId
string testLineId
string testItem
integer testLineStatus
}
ORDER_INSTANCE {
string generalOrderInstanceID PK
string orderNo
}
JOB ||--o{ ORDER_INSTANCE : "属于"
```

**Diagram sources**
- [Job.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Job.java)

**Section sources**
- [Job.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Job.java)

## 核心字段定义

订单实例包含以下关键字段：

| 字段名 | 数据类型 | 约束条件 | 说明 |
|-------|--------|---------|------|
| `jobNo` | 字符串 | 主键，非空 | 实例编号，全局唯一 |
| `generalOrderInstanceID` | 字符串 | 外键，非空 | 关联的主订单实例ID |
| `jobStatus` | 整型 | 非空 | 执行状态，引用JobStatus枚举 |
| `labInDate` | 日期时间 | 可为空 | 实验室接收日期 |
| `labOutDate` | 日期时间 | 可为空 | 实验室发出日期 |
| `remark` | 字符串 | 可为空 | 备注信息 |
| `labSectionId` | 整型 | 可为空 | 实验室部门ID |
| `labSectionName` | 字符串 | 可为空 | 实验室部门名称 |
| `testLineInstanceId` | 字符串 | 可为空 | 测试线实例ID |
| `testLineId` | 字符串 | 可为空 | 测试线ID |
| `testItem` | 字符串 | 可为空 | 测试项目名称 |
| `testLineStatus` | 整型 | 可为空 | 测试线状态 |

**Section sources**
- [JobDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/JobDTO.java)

## 订单实例与主订单关系

订单实例与主订单之间存在严格的1:N关系。一个主订单可以生成多个订单实例，每个实例独立执行不同的测试任务。这种设计支持混测、分包等复杂业务场景。

```mermaid
classDiagram
class OrderInstance {
+String generalOrderInstanceID
+String orderNo
+List<Job> jobs
}
class Job {
+String jobNo
+String generalOrderInstanceID
+Integer jobStatus
+Date labInDate
+Date labOutDate
}
OrderInstance "1" *-- "N" Job : 包含
```

**Diagram sources**
- [Job.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Job.java)
- [JobDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/JobDTO.java)

**Section sources**
- [JobDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/JobDTO.java)

## 状态联动机制

订单实例状态与主订单状态存在联动机制。当所有关联的订单实例均完成时，主订单状态自动更新为“已完成”。状态变更通过事件总线触发，确保数据一致性。

```mermaid
flowchart TD
A[订单实例创建] --> B[更新实例状态]
B --> C{所有实例完成?}
C --> |是| D[更新主订单状态为完成]
C --> |否| E[保持主订单进行中状态]
D --> F[触发完成事件]
E --> G[继续监控状态]
```

**Diagram sources**
- [JobFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/JobFacadeImpl.java)

**Section sources**
- [JobFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/JobFacadeImpl.java)

## 生命周期管理

订单实例的生命周期包括创建、执行、完成和归档四个阶段：

1. **创建阶段**：根据测试需求生成实例，分配实例编号
2. **执行阶段**：实验室接收样品，开始测试流程
3. **完成阶段**：测试结果录入，报告生成
4. **归档阶段**：数据归档，资源释放

```mermaid
stateDiagram-v2
[*] --> Created
Created --> InProgress : 接收样品
InProgress --> Completed : 测试完成
InProgress --> Failed : 测试失败
Completed --> Archived : 归档
Failed --> Archived : 归档
Archived --> [*]
```

**Diagram sources**
- [JobFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/JobFacadeImpl.java)

**Section sources**
- [JobFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/JobFacadeImpl.java)

## 业务操作与数据一致性

订单实例的CRUD操作通过`JobFacade`接口暴露，具体实现位于`JobFacadeImpl`中。所有写操作均在事务保护下执行，确保数据一致性。

```mermaid
sequenceDiagram
participant Client as 客户端
participant Facade as JobFacade
participant Service as JobService
participant DB as 数据库
Client->>Facade : 创建订单实例
Facade->>Service : 调用业务逻辑
Service->>DB : 开启事务
DB-->>Service : 事务就绪
Service->>DB : 插入实例记录
Service->>DB : 更新主订单状态
Service->>DB : 提交事务
DB-->>Service : 提交成功
Service-->>Facade : 返回结果
Facade-->>Client : 操作成功
```

**Diagram sources**
- [JobFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/JobFacade.java)
- [JobFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/JobFacadeImpl.java)

**Section sources**
- [JobFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/JobFacade.java)
- [JobFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/JobFacadeImpl.java)

## 查询优化与性能监控

为提升查询性能，系统在`jobNo`、`generalOrderInstanceID`和`jobStatus`字段上建立了复合索引。同时，通过Kafka异步日志记录关键操作，支持性能监控与审计。

```mermaid
graph TD
A[查询请求] --> B{是否按jobNo查询?}
B --> |是| C[使用主键索引]
B --> |否| D{是否按状态查询?}
D --> |是| E[使用状态索引]
D --> |否| F[全表扫描]
C --> G[返回结果]
E --> G
F --> G
```

**Diagram sources**
- [Job.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Job.java)

**Section sources**
- [Job.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Job.java)

## 结论

订单实例模型通过清晰的数据库设计、严格的ORM映射和完善的业务逻辑，实现了测试任务的精细化管理。其与主订单的1:N关系设计灵活支持多种业务场景，状态联动机制确保了数据一致性，而优化的索引策略则保障了系统的高性能运行。