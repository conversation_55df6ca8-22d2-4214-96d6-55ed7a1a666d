# 订单分包模型

<cite>
**本文档引用文件**  
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java)
- [SubContractExtMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/SubContractExtMapper.java)
- [SubContractExternalRelationshipPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractExternalRelationshipPO.java)
- [SubContractExternalRelationshipMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractExternalRelationshipMapper.java)
- [SubContractTestDataInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractTestDataInfoPO.java)
- [SubContractTestDataInfoMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractTestDataInfoMapper.java)
- [SubContractTestLineMappingMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractTestLineMappingMapper.java)
- [SubContractSyncDTO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmodel/subcontract/SubContractSyncDTO.java)
- [SubContractLabPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmodel/masterlist/SubContractLabPO.java)
- [SubContractStatusEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/SubContractStatusEnum.java)
- [SubContractFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/SubContractFacade.java)
- [SubContractFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/SubContractFacadeImpl.java)
- [StarLimsConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/StarLimsConfig.java)
- [SubcontractConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/SubcontractConfig.java)
</cite>

## 目录
1. [引言](#引言)
2. [数据库表结构与ORM映射](#数据库表结构与orm映射)
3. [核心字段定义与约束](#核心字段定义与约束)
4. [订单分包与主订单关联关系](#订单分包与主订单关联关系)
5. [分包数据同步机制](#分包数据同步机制)
6. [业务操作逻辑与数据一致性](#业务操作逻辑与数据一致性)
7. [外部系统集成：StarLIMS交互](#外部系统集成：starlims交互)
8. [查询优化与性能监控](#查询优化与性能监控)
9. [消息队列集成](#消息队列集成)
10. [附录](#附录)

## 引言

订单分包模型是OTSNotes系统中支持外部实验室协作的核心模块，用于管理主订单向外部系统（如StarLIMS）分发检测任务的全生命周期。该模型涵盖分包创建、状态同步、数据回传、错误处理等关键流程，确保跨系统业务的一致性与可追溯性。本文档详细描述其数据库设计、ORM映射、业务逻辑及与外部系统的集成方案。

## 数据库表结构与ORM映射

订单分包模型涉及多个核心数据表，通过MyBatis实现ORM映射，支持复杂查询与事务一致性。

```mermaid
erDiagram
SUBCONTRACT ||--o{ SUBCONTRACT_EXTERNAL_RELATIONSHIP : "1:N"
SUBCONTRACT ||--o{ SUBCONTRACT_TEST_DATA_INFO : "1:N"
SUBCONTRACT ||--o{ SUBCONTRACT_TEST_LINE_MAPPING : "1:N"
SUBCONTRACT }|--|| SUBCONTRACT_LAB : "N:1"
SUBCONTRACT {
string subcontractNo PK
string orderId
string labCode
int status
datetime createTime
datetime updateTime
string createdBy
string updatedBy
string externalSystemId
string syncStatus
string syncMessage
}
SUBCONTRACT_EXTERNAL_RELATIONSHIP {
long id PK
string subcontractNo FK
string externalEntityType
string externalEntityId
string externalEntityNo
datetime createTime
}
SUBCONTRACT_TEST_DATA_INFO {
long id PK
string subcontractNo FK
string testLineId
string testDataJson
datetime createTime
datetime updateTime
}
SUBCONTRACT_TEST_LINE_MAPPING {
long id PK
string subcontractNo FK
string testLineId
string externalTestLineId
string externalTestLineNo
datetime createTime
}
SUBCONTRACT_LAB {
string labCode PK
string labName
string systemUrl
string apiKey
int status
}
```

**图表来源**  
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java)
- [SubContractExternalRelationshipPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractExternalRelationshipPO.java)
- [SubContractTestDataInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractTestDataInfoPO.java)
- [SubContractTestLineMappingMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractTestLineMappingMapper.java)
- [SubContractLabPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmodel/masterlist/SubContractLabPO.java)

**本节来源**  
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java#L1-L100)
- [SubContractExternalRelationshipPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractExternalRelationshipPO.java#L1-L80)

## 核心字段定义与约束

### 分包编号（subcontractNo）
- **数据类型**：VARCHAR(50)
- **约束**：主键，非空，唯一索引
- **生成规则**：由系统根据配置规则生成（如SC-YYYYMMDD-0001）
- **说明**：全局唯一标识一个分包任务

### 分包状态（status）
- **数据类型**：INT
- **约束**：非空，外键关联枚举 `SubContractStatusEnum`
- **取值说明**：
  - 10：已创建（CREATED）
  - 20：已发送（SENT）
  - 30：处理中（PROCESSING）
  - 40：已完成（COMPLETED）
  - 50：已取消（CANCELLED）
  - 60：同步失败（SYNC_FAILED）
- **来源**：[SubContractStatusEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/SubContractStatusEnum.java)

### 关联订单（orderId）
- **数据类型**：VARCHAR(50)
- **约束**：非空，外键（逻辑）
- **说明**：指向主订单系统中的订单ID，用于关联主从关系

### 外部系统标识（externalSystemId）
- **数据类型**：VARCHAR(50)
- **约束**：可为空
- **说明**：外部系统（如StarLIMS）返回的分包任务ID，用于后续状态同步与数据回传

### 同步状态（syncStatus）
- **数据类型**：VARCHAR(20)
- **约束**：可为空
- **取值**：`SUCCESS`, `FAILED`, `PENDING`
- **说明**：记录与外部系统通信的最终状态

### 实验室编码（labCode）
- **数据类型**：VARCHAR(20)
- **约束**：非空，外键关联 `subcontract_lab` 表
- **说明**：标识接收分包任务的外部实验室

**本节来源**  
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java#L50-L150)
- [SubContractStatusEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/SubContractStatusEnum.java#L1-L50)

## 订单分包与主订单关联关系

订单分包与主订单通过 `orderId` 字段建立逻辑关联。系统支持一对多分包模式，即一个主订单可拆分为多个分包任务，分别发送至不同实验室。

- **关联方式**：`SubContractPO.orderId` → 主订单ID
- **级联操作**：
  - 主订单取消时，所有未完成的分包状态置为“已取消”
  - 主订单数据变更时，触发分包数据一致性校验
- **查询支持**：通过 `SubContractExtMapper` 提供按 `orderId` 查询所有分包记录的接口

```mermaid
classDiagram
class SubContractPO {
+String subcontractNo
+String orderId
+String labCode
+Integer status
+String externalSystemId
+String syncStatus
+Date createTime
+Date updateTime
}
class SubContractExtMapper {
+SubContractPO[] selectByOrderId(String orderId)
+SubContractPO[] selectByLabCodeAndStatus(String labCode, Integer status)
+SubContractPO selectByExternalSystemId(String externalSystemId)
}
SubContractExtMapper --> SubContractPO : "返回"
```

**图表来源**  
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java)
- [SubContractExtMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/SubContractExtMapper.java)

**本节来源**  
- [SubContractExtMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/SubContractExtMapper.java#L10-L40)

## 分包数据同步机制

分包数据同步采用异步消息驱动模式，确保高可用与最终一致性。

```mermaid
sequenceDiagram
participant 前端 as 前端应用
participant 服务层 as SubContractFacadeImpl
participant 数据层 as SubContractMapper
participant 消息队列 as Kafka
participant 同步服务 as SubContractSyncService
participant StarLIMS as StarLIMS系统
前端->>服务层 : 创建分包请求
服务层->>数据层 : 插入SubContract记录
服务层->>消息队列 : 发送SYNC_SUBCONTRACT消息
服务层-->>前端 : 返回成功状态=已创建
消息队列->>同步服务 : 消费消息
同步服务->>StarLIMS : HTTP调用toStarLims接口
StarLIMS-->>同步服务 : 返回externalSystemId
同步服务->>数据层 : 更新externalSystemId和status
同步服务->>消息队列 : 发送POLL_SUBCONTRACT_STATUS消息定时轮询
```

- **同步触发**：分包创建后，通过Kafka发送 `SYNC_SUBCONTRACT` 消息
- **重试机制**：失败时基于Kafka延迟队列进行指数退避重试
- **状态轮询**：定时任务轮询外部系统获取最新状态，更新本地 `syncStatus` 和 `status`
- **配置参数**：轮询间隔、重试次数等由 `SubcontractConfig` 统一管理

**图表来源**  
- [SubContractFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/SubContractFacadeImpl.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java)
- [StarLimsConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/StarLimsConfig.java)

**本节来源**  
- [SubContractFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/SubContractFacadeImpl.java#L50-L120)
- [SubcontractConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/SubcontractConfig.java#L1-L30)

## 业务操作逻辑与数据一致性

### 分包创建
1. 校验主订单状态是否允许分包
2. 生成唯一 `subcontractNo`
3. 插入 `subcontract` 主表
4. 批量插入 `subcontract_test_data_info` 和 `subcontract_test_line_mapping`
5. 发送Kafka消息触发同步

### 分包更新
- 仅允许更新 `status`、`syncStatus`、`syncMessage` 等状态字段
- 通过 `@TrimsSyncType` 注解标记同步操作，触发事件总线

### 状态流转
- 状态变更通过状态机控制，禁止非法跳转（如从“已完成”变更为“已创建”）
- 每次状态变更记录操作日志至 `action_log` 表

### 数据一致性保障
- **事务控制**：使用Spring事务管理，确保主表与子表数据原子性
- **幂等性**：通过 `externalSystemId` 唯一索引防止重复创建
- **补偿机制**：对账任务每日校验本地与外部系统状态差异，自动修复

**本节来源**  
- [SubContractFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/SubContractFacadeImpl.java#L120-L200)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java#L1-L50)

## 外部系统集成：StarLIMS交互

### 数据交互流程
1. **发送分包**：调用 `StarLimsFacade.toStarLims()` 接口，传输分包数据（JSON格式）
2. **接收报告**：StarLIMS通过 `receiveReportDoc` 回调接口上传检测结果
3. **数据解析**：使用 `StarLimsReportReceiveServiceImpl` 解析并更新本地结果

### 状态同步
- **主动同步**：系统定时调用 `StarLimsCommonService.getSubcontractStatus()` 获取状态
- **被动通知**：StarLIMS可通过Webhook推送状态变更事件

### 错误处理
- **网络异常**：捕获 `HttpClientUtil` 异常，记录 `syncMessage` 并进入重试队列
- **数据格式错误**：验证JSON Schema，失败时标记 `SYNC_FAILED` 并告警
- **重试策略**：最多重试5次，间隔30秒、1分钟、5分钟、15分钟、30分钟

**本节来源**  
- [StarLimsFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/StarLimsFacade.java#L1-L20)
- [StarLimsReportReceiveServiceImpl.md](file://doc/domain/service/subcontract/starlims/StarLimsReportReceiveServiceImpl.md)
- [StarLimsCommonService.toStarLims.md](file://doc/api/StarLimsCommonService.toStarLims.md)

## 查询优化与性能监控

### 查询优化
- **索引策略**：
  - `subcontract(orderId, status)`：支持按订单查询分包状态
  - `subcontract(labCode, status)`：支持按实验室查询待处理任务
  - `subcontract_external_relationship(externalEntityId)`：支持外部ID反查
- **分页查询**：`SubContractExtMapper` 提供分页接口，避免全表扫描

### 性能监控
- **埋点指标**：
  - 分包创建耗时（P95 < 500ms）
  - 同步成功率（目标 > 99.9%）
  - 消息队列积压量
- **监控工具**：集成Prometheus + Grafana，关键指标可视化
- **告警规则**：同步失败率 > 1% 持续5分钟触发企业微信告警

**本节来源**  
- [SubContractExtMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/SubContractExtMapper.java#L40-L60)
- [KafkaProducer.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/kafka/KafkaProducer.java)

## 消息队列集成

分包系统深度集成Kafka实现异步解耦：

- **主题设计**：
  - `topic.subcontract.sync`：分包同步消息
  - `topic.subcontract.poll`：状态轮询指令
  - `topic.subcontract.result`：外部结果回传
- **消息结构**：JSON格式，包含 `subcontractNo`, `operationType`, `payload`
- **消费保障**：消费者使用 `SgsKafkaConsumerConfig` 配置手动提交，防止消息丢失
- **死信队列**：处理失败消息转入DLQ，供人工干预

**本节来源**  
- [KafkaProducerConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/KafkaProducerConfig.java)
- [SgsKafkaConsumerConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/SgsKafkaConsumerConfig.java)
- [KafkaProducer.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/kafka/KafkaProducer.java)

## 附录

### 分包状态流转图
```mermaid
stateDiagram-v2
[*] --> 已创建
已创建 --> 已发送 : 发送成功
已创建 --> 同步失败 : 发送失败且重试耗尽
已发送 --> 处理中 : 收到外部系统确认
处理中 --> 已完成 : 接收最终报告
处理中 --> 已取消 : 外部取消
已创建 --> 已取消 : 人工取消
同步失败 --> 已取消 : 人工处理
```

### 配置参数表
| 参数名 | 默认值 | 说明 |
|-------|--------|------|
| subcontract.sync.retry.times | 5 | 同步最大重试次数 |
| subcontract.poll.interval.minutes | 10 | 状态轮询间隔（分钟） |
| subcontract.lab.timeout.seconds | 30 | 外部系统调用超时时间 |
| subcontract.batch.size | 100 | 批量操作分页大小 |

**本节来源**  
- [SubcontractConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/SubcontractConfig.java)
- [StarLimsConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/StarLimsConfig.java)