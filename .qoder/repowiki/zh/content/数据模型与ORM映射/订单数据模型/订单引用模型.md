# 订单引用模型

<cite>
**本文档中引用的文件**  
- [CitationBaseInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CitationBaseInfoPO.java)
- [OrderCitationRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/OrderCitationRelInfoPO.java)
- [CitationLangInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CitationLangInfoPO.java)
- [CitationSectionLangInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CitationSectionLangInfoPO.java)
- [OrderCitationRelMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/orderCitationRelMapper.xml)
- [OrderCitationRelInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/OrderCitationRelInfoMapper.xml)
- [OrderCitationRelCopyService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/copy/OrderCitationRelCopyService.java)
- [OrderCitationRelCopyInfo.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmodel/citation/OrderCitationRelCopyInfo.java)
- [CitationBaseReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/req/citation/CitationBaseReq.java)
</cite>

## 目录
1. [引言](#引言)
2. [数据库表结构](#数据库表结构)
3. [ORM映射模型](#orm映射模型)
4. [核心字段说明](#核心字段说明)
5. [订单引用与主订单关联关系](#订单引用与主订单关联关系)
6. [数据存储与检索机制](#数据存储与检索机制)
7. [业务逻辑与数据一致性](#业务逻辑与数据一致性)
8. [使用场景说明](#使用场景说明)
9. [查询优化与全文检索](#查询优化与全文检索)
10. [性能监控方案](#性能监控方案)

## 引言
订单引用模型用于管理订单中引用的标准、方法、法规等信息，支持多语言和版本控制。该模型通过引用编号、引用类型、关联订单等字段建立与主订单的关系，并提供完整的创建、更新、删除操作支持。本文档详细描述了订单引用的数据库表结构、ORM映射、关键字段定义、业务逻辑及使用场景。

## 数据库表结构

```mermaid
erDiagram
ORDER_CITATION_RELATIONSHIP {
bigint id PK
varchar(36) orderId FK
bigint ppBaseId
bigint citationBaseId FK
integer citationType
integer relStatus
timestamp createdDate
timestamp modifiedDate
}
CITATION_BASE_INFO {
bigint id PK
integer citationId
integer citationVersionId
integer citationVersionNo
integer citationType
varchar(512) citationName
varchar(512) citationShortName
integer isCancelled
char(32) bizVersionId
integer citationStatus
timestamp createdDate
bigint modifiedDate
}
CITATION_LANG_INFO {
bigint id PK
integer citationType
integer citationVersionId
integer languageId
varchar(512) citationName
varchar(512) citationShortName
char(32) bizVersionId
integer status
timestamp createdDate
timestamp modifiedDate
}
CITATION_SECTION_LANG_INFO {
bigint id PK
integer citationType
integer citationVersionId
integer citationSectionId
varchar(512) citationSectionName
varchar(1024) citationSectionText
integer languageId
char(32) bizVersionId
integer status
timestamp createdDate
timestamp modifiedDate
}
ORDER_CITATION_RELATIONSHIP ||--o{ CITATION_BASE_INFO : "引用基础信息"
CITATION_BASE_INFO ||--o{ CITATION_LANG_INFO : "多语言信息"
CITATION_BASE_INFO ||--o{ CITATION_SECTION_LANG_INFO : "章节语言信息"
```

**图表来源**  
- [CitationBaseInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CitationBaseInfoPO.java)
- [OrderCitationRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/OrderCitationRelInfoPO.java)
- [CitationLangInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CitationLangInfoPO.java)
- [CitationSectionLangInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CitationSectionLangInfoPO.java)

## ORM映射模型

```mermaid
classDiagram
class OrderCitationRelInfoPO {
+Long id
+String orderId
+Long ppBaseId
+Long citationBaseId
+Integer citationType
+Integer relStatus
+Date createdDate
+Date modifiedDate
+getId() Long
+setId(Long id) void
+getOrderId() String
+setOrderId(String orderId) void
+getPpBaseId() Long
+setPpBaseId(Long ppBaseId) void
+getCitationBaseId() Long
+setCitationBaseId(Long citationBaseId) void
+getCitationType() Integer
+setCitationType(Integer citationType) void
+getRelStatus() Integer
+setRelStatus(Integer relStatus) void
+getCreatedDate() Date
+setCreatedDate(Date createdDate) void
+getModifiedDate() Date
+setModifiedDate(Date modifiedDate) void
}
class CitationBaseInfoPO {
+Long id
+Integer citationId
+Integer citationVersionId
+Integer citationVersionNo
+Integer citationType
+String citationName
+String citationShortName
+Integer isCancelled
+String bizVersionId
+Integer citationStatus
+Date createdDate
+Long modifiedDate
+getId() Long
+setId(Long id) void
+getCitationId() Integer
+setCitationId(Integer citationId) void
+getCitationVersionId() Integer
+setCitationVersionId(Integer citationVersionId) void
+getCitationVersionNo() Integer
+setCitationVersionNo(Integer citationVersionNo) void
+getCitationType() Integer
+setCitationType(Integer citationType) void
+getCitationName() String
+setCitationName(String citationName) void
+getCitationShortName() String
+setCitationShortName(String citationShortName) void
+getIsCancelled() Integer
+setIsCancelled(Integer isCancelled) void
+getBizVersionId() String
+setBizVersionId(String bizVersionId) void
+getCitationStatus() Integer
+setCitationStatus(Integer citationStatus) void
+getCreatedDate() Date
+setCreatedDate(Date createdDate) void
+getModifiedDate() Long
+setModifiedDate(Long modifiedDate) void
}
class CitationLangInfoPO {
+Long id
+Integer citationType
+Integer citationVersionId
+Integer languageId
+String citationName
+String citationShortName
+String bizVersionId
+Integer status
+Date createdDate
+Date modifiedDate
+getId() Long
+setId(Long id) void
+getCitationType() Integer
+setCitationType(Integer citationType) void
+getCitationVersionId() Integer
+setCitationVersionId(Integer citationVersionId) void
+getLanguageId() Integer
+setLanguageId(Integer languageId) void
+getCitationName() String
+setCitationName(String citationName) void
+getCitationShortName() String
+setCitationShortName(String citationShortName) void
+getBizVersionId() String
+setBizVersionId(String bizVersionId) void
+getStatus() Integer
+setStatus(Integer status) void
+getCreatedDate() Date
+setCreatedDate(Date createdDate) void
+getModifiedDate() Date
+setModifiedDate(Date modifiedDate) void
}
class CitationSectionLangInfoPO {
+Long id
+Integer citationType
+Integer citationVersionId
+Integer citationSectionId
+String citationSectionName
+String citationSectionText
+Integer languageId
+String bizVersionId
+Integer status
+Date createdDate
+Date modifiedDate
+getId() Long
+setId(Long id) void
+getCitationType() Integer
+setCitationType(Integer citationType) void
+getCitationVersionId() Integer
+setCitationVersionId(Integer citationVersionId) void
+getCitationSectionId() Integer
+setCitationSectionId(Integer citationSectionId) void
+getCitationSectionName() String
+setCitationSectionName(String citationSectionName) void
+getCitationSectionText() String
+setCitationSectionText(String citationSectionText) void
+getLanguageId() Integer
+setLanguageId(Integer languageId) void
+getBizVersionId() String
+setBizVersionId(String bizVersionId) void
+getStatus() Integer
+setStatus(Integer status) void
+getCreatedDate() Date
+setCreatedDate(Date createdDate) void
+getModifiedDate() Date
+setModifiedDate(Date modifiedDate) void
}
OrderCitationRelInfoPO --> CitationBaseInfoPO : "引用基础信息"
CitationBaseInfoPO --> CitationLangInfoPO : "多语言信息"
CitationBaseInfoPO --> CitationSectionLangInfoPO : "章节语言信息"
```

**图表来源**  
- [OrderCitationRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/OrderCitationRelInfoPO.java)
- [CitationBaseInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CitationBaseInfoPO.java)
- [CitationLangInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CitationLangInfoPO.java)
- [CitationSectionLangInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CitationSectionLangInfoPO.java)

## 核心字段说明

### 引用编号
- **字段名**: `citationId`, `citationVersionId`, `citationVersionNo`
- **数据类型**: INTEGER
- **约束条件**: 必填，`citationId` 和 `citationVersionId` 为必填字段
- **说明**: 唯一标识一个引用及其版本，支持版本控制

### 引用类型
- **字段名**: `citationType`
- **数据类型**: INTEGER
- **约束条件**: 默认值0，必填
- **取值说明**: 
  - 0：无
  - 1：方法
  - 2：法规
  - 3：标准

### 关联订单
- **字段名**: `orderId`
- **数据类型**: VARCHAR(36)
- **约束条件**: 必填，外键关联订单表
- **说明**: 标识该引用所属的订单

### 引用内容
- **字段名**: `citationName`, `citationShortName`, `citationSectionText`
- **数据类型**: VARCHAR
- **长度限制**: `citationName` 和 `citationShortName` 最大512字符，`citationSectionText` 最大1024字符
- **说明**: 存储引用的名称、简称和详细内容

**章节来源**  
- [CitationBaseInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CitationBaseInfoPO.java#L60-L125)
- [OrderCitationRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/OrderCitationRelInfoPO.java#L60-L125)

## 订单引用与主订单关联关系

```mermaid
erDiagram
ORDER ||--o{ ORDER_CITATION_RELATIONSHIP : "包含"
ORDER_CITATION_RELATIONSHIP }|--|| CITATION_BASE_INFO : "引用"
CITATION_BASE_INFO ||--o{ CITATION_LANG_INFO : "多语言"
CITATION_BASE_INFO ||--o{ CITATION_SECTION_LANG_INFO : "章节"
ORDER {
varchar(36) orderId PK
varchar(255) orderName
date orderDate
}
ORDER_CITATION_RELATIONSHIP {
bigint id PK
varchar(36) orderId FK
bigint citationBaseId FK
integer citationType
integer relStatus
}
CITATION_BASE_INFO {
bigint id PK
integer citationId
integer citationVersionId
varchar(512) citationName
}
```

**图表来源**  
- [OrderCitationRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/OrderCitationRelInfoPO.java)
- [CitationBaseInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CitationBaseInfoPO.java)

## 数据存储与检索机制

```mermaid
flowchart TD
Start([开始]) --> CheckOrder["检查订单是否存在"]
CheckOrder --> |存在| CreateRel["创建引用关系"]
CheckOrder --> |不存在| ReturnError["返回错误"]
CreateRel --> StoreRel["存储OrderCitationRelInfoPO"]
StoreRel --> StoreLang["存储多语言信息"]
StoreLang --> Index["建立全文索引"]
Index --> Complete([完成])
RetrieveStart([检索开始]) --> QueryRel["查询OrderCitationRelInfoPO"]
QueryRel --> JoinBase["关联CitationBaseInfoPO"]
JoinBase --> JoinLang["关联CitationLangInfoPO"]
JoinLang --> Filter["应用过滤条件"]
Filter --> Sort["排序结果"]
Sort --> ReturnResult["返回结果"]
```

**图表来源**  
- [OrderCitationRelMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/OrderCitationRelMapper.xml)
- [OrderCitationRelInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/OrderCitationRelInfoMapper.xml)

## 业务逻辑与数据一致性

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Service as "OrderCitationRelCopyService"
participant Mapper as "OrderCitationRelMapper"
participant DB as "数据库"
Client->>Service : 创建引用关系
Service->>Service : 验证参数
Service->>Service : 检查重复
Service->>Mapper : batchInsert
Mapper->>DB : 执行批量插入
DB-->>Mapper : 返回结果
Mapper-->>Service : 返回结果
Service-->>Client : 返回响应
Client->>Service : 更新引用
Service->>Service : 验证权限
Service->>Service : 检查状态
Service->>Mapper : updateByPrimaryKeySelective
Mapper->>DB : 执行更新
DB-->>Mapper : 返回结果
Mapper-->>Service : 返回结果
Service-->>Client : 返回响应
```

**图表来源**  
- [OrderCitationRelCopyService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/copy/OrderCitationRelCopyService.java#L174-L198)
- [OrderCitationRelCopyInfo.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmodel/citation/OrderCitationRelCopyInfo.java)

## 使用场景说明

### 报告生成
在报告生成过程中，系统会根据订单引用信息自动填充标准、方法等引用内容，确保报告的完整性和准确性。

### 结论处理
引用信息用于支持结论的生成和验证，特别是在需要引用特定标准或方法的场景中。

### 多语言支持
通过`CitationLangInfoPO`和`CitationSectionLangInfoPO`支持多语言引用内容，满足国际化需求。

**章节来源**  
- [CitationBaseReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/req/citation/CitationBaseReq.java)
- [CitationLangInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CitationLangInfoPO.java)

## 查询优化与全文检索

### 索引策略
- 在`orderId`、`citationBaseId`、`citationType`字段上建立复合索引
- 在`citationName`字段上建立全文索引
- 在`createdDate`字段上建立时间范围索引

### 查询优化
- 使用分页查询避免大数据量返回
- 预编译常用查询语句
- 缓存热点数据

**章节来源**  
- [OrderCitationRelInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/OrderCitationRelInfoMapper.xml)
- [CitationBaseInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CitationBaseInfoPO.java)

## 性能监控方案

```mermaid
flowchart TD
MonitorStart([监控开始]) --> Collect["收集性能指标"]
Collect --> Metrics["关键指标包括"]
Metrics --> QueryTime["查询响应时间"]
Metrics --> InsertTime["插入响应时间"]
Metrics --> CacheHit["缓存命中率"]
Metrics --> DBLoad["数据库负载"]
Collect --> Alert["设置告警阈值"]
Alert --> |超过阈值| Notify["通知运维人员"]
Alert --> |正常| Continue["继续监控"]
Collect --> Report["生成性能报告"]
Report --> Store["存储历史数据"]
Store --> Analyze["分析趋势"]
Analyze --> Optimize["提出优化建议"]
```

**章节来源**  
- [OrderCitationRelInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/OrderCitationRelInfoMapper.xml)
- [OrderCitationRelCopyService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/copy/OrderCitationRelCopyService.java)