# 任务实体(Job)

<cite>
**本文档中引用的文件**  
- [XXLJobConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/XXLJobConfig.java)
- [JobInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/JobInfoPO.java)
- [JobInfoMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/JobInfoMapper.java)
- [JobExtMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/JobExtMapper.java)
- [JobInfo.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmodel/JobInfo.java)
- [JobVO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmodel/masterlist/JobVO.java)
- [JobPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmodel/masterlist/JobPO.java)
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/job/JobService.java)
- [JobFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/JobFacade.java)
- [JobFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/JobFacadeImpl.java)
- [JobDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/JobDTO.java)
</cite>

## 目录
1. [任务实体概述](#任务实体概述)
2. [数据库表结构](#数据库表结构)
3. [核心字段定义](#核心字段定义)
4. [任务状态机](#任务状态机)
5. [关联实体关系](#关联实体关系)
6. [任务调度机制](#任务调度机制)
7. [执行日志与错误处理](#执行日志与错误处理)
8. [性能优化建议](#性能优化建议)

## 任务实体概述

任务实体（Job）是系统中用于管理各类业务处理任务的核心数据模型。该实体主要负责跟踪任务的生命周期，包括任务创建、调度、执行、状态变更和结果反馈等全过程。任务实体广泛应用于订单处理、报告生成、数据同步等关键业务场景。

任务实体的设计遵循领域驱动设计（DDD）原则，通过分层架构实现数据访问、业务逻辑和接口服务的分离。系统通过MyBatis进行数据库操作，通过XXL-JOB实现分布式任务调度，并通过Dubbo服务进行跨系统调用。

**Section sources**
- [JobInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/JobInfoPO.java#L1-L50)
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/job/JobService.java#L1-L30)

## 数据库表结构

```mermaid
erDiagram
JOB_INFO {
bigint id PK
varchar jobNo UK
varchar jobType
varchar status
varchar orderNo FK
varchar testLineNo
varchar subcontractNo
datetime createTime
datetime updateTime
datetime startTime
datetime endTime
varchar executor
varchar executorGroup
varchar scheduleType
varchar scheduleExpression
varchar priority
varchar retryCount
varchar maxRetryCount
varchar lastErrorMessage
varchar extendInfo
varchar createBy
varchar updateBy
tinyint isDeleted
}
ORDER_INFO {
varchar orderNo PK
varchar customerNo
varchar orderType
varchar status
}
TEST_LINE_INFO {
varchar testLineNo PK
varchar orderNo FK
varchar testCode
varchar status
}
SUBCONTRACT_INFO {
varchar subcontractNo PK
varchar orderNo FK
varchar labCode
varchar status
}
JOB_INFO ||--o{ ORDER_INFO : "关联订单"
JOB_INFO ||--o{ TEST_LINE_INFO : "关联测试项"
JOB_INFO ||--o{ SUBCONTRACT_INFO : "关联分包"
```

**Diagram sources**
- [JobInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/JobInfoPO.java#L1-L100)
- [JobInfoExample.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/JobInfoExample.java#L1-L50)

**Section sources**
- [JobInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/JobInfoPO.java#L1-L150)

## 核心字段定义

### 基础信息字段
- **id**: 任务唯一标识（主键）
- **jobNo**: 任务编号（唯一索引）
- **jobType**: 任务类型（枚举值：ORDER_PROCESS, REPORT_GENERATE, DATA_SYNC等）
- **status**: 任务状态（待处理、处理中、已完成、失败等）
- **createTime**: 创建时间
- **updateTime**: 更新时间
- **createBy**: 创建人
- **updateBy**: 更新人
- **isDeleted**: 删除标记（软删除）

### 执行信息字段
- **startTime**: 任务开始执行时间
- **endTime**: 任务结束时间
- **executor**: 执行器名称
- **executorGroup**: 执行器分组
- **retryCount**: 当前重试次数
- **maxRetryCount**: 最大重试次数
- **lastErrorMessage**: 最后一次错误信息
- **extendInfo**: 扩展信息（JSON格式存储）

### 调度信息字段
- **scheduleType**: 调度类型（立即执行、定时执行、周期执行）
- **scheduleExpression**: 调度表达式（Cron表达式）
- **priority**: 优先级（高、中、低）

### 关联信息字段
- **orderNo**: 关联订单号（外键）
- **testLineNo**: 关联测试项编号
- **subcontractNo**: 关联分包编号

**Section sources**
- [JobInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/JobInfoPO.java#L50-L200)

## 任务状态机

```mermaid
stateDiagram-v2
[*] --> 待处理
待处理 --> 处理中 : "任务被调度器触发"
处理中 --> 已完成 : "任务成功执行"
处理中 --> 失败 : "执行异常且超过最大重试次数"
处理中 --> 待处理 : "执行异常但未超重试次数"
失败 --> 待处理 : "手动重试"
已完成 --> [*]
失败 --> [*]
note right of 处理中
包含子状态：
- 初始化
- 执行中
- 等待回调
end note
```

**Diagram sources**
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/job/JobService.java#L100-L200)
- [JobStatusDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/JobStatusDTO.java#L1-L20)

**Section sources**
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/job/JobService.java#L50-L250)

### 状态转换规则
1. **待处理 → 处理中**：当任务被调度器选中执行时触发
2. **处理中 → 已完成**：任务成功执行并返回成功结果
3. **处理中 → 失败**：任务执行异常且重试次数达到上限
4. **处理中 → 待处理**：任务执行异常但仍在重试次数范围内
5. **失败 → 待处理**：管理员手动触发重试操作

### 业务逻辑
- 任务状态变更需记录操作日志
- 状态变更需触发相应的事件通知
- 关键状态变更需进行权限校验
- 状态机实现采用策略模式，不同任务类型有不同的状态转换规则

## 关联实体关系

```mermaid
classDiagram
class Job {
+String jobNo
+String jobType
+String status
+Date createTime
+Date startTime
+String executor
+String orderNo
+String testLineNo
+String subcontractNo
+String extendInfo
+updateStatus()
+validate()
}
class Order {
+String orderNo
+String customerNo
+String orderType
+String status
+TestLine[] testLines
+Job[] jobs
+createJob()
}
class TestLine {
+String testLineNo
+String orderNo
+String testCode
+String status
+Job[] jobs
+createJob()
}
class SubContract {
+String subcontractNo
+String orderNo
+String labCode
+String status
+Job[] jobs
+createJob()
}
Job --> Order : "关联"
Job --> TestLine : "关联"
Job --> SubContract : "关联"
Order --> Job : "包含"
TestLine --> Job : "包含"
SubContract --> Job : "包含"
```

**Diagram sources**
- [JobInfo.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmodel/JobInfo.java#L1-L50)
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java#L1-L50)
- [TestLineService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/testline/TestLineService.java#L1-L50)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/SubContractService.java#L1-L50)

**Section sources**
- [JobExtMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/JobExtMapper.java#L1-L100)
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/job/JobService.java#L250-L400)

### 关联关系说明
1. **与订单实体（Order）**：一对多关系，一个订单可产生多个任务
2. **与测试项实体（TestLine）**：一对多关系，一个测试项可关联多个任务
3. **与分包实体（SubContract）**：一对多关系，一个分包可产生多个任务
4. **级联操作**：删除订单时，相关任务标记为已删除

## 任务调度机制

```mermaid
sequenceDiagram
participant 调度中心 as 调度中心(XXL-JOB)
participant 任务服务 as JobService
participant 数据库 as 数据库
participant 执行器 as 执行器
调度中心->>数据库 : 查询待处理任务
数据库-->>调度中心 : 返回任务列表
调度中心->>任务服务 : 触发任务执行
任务服务->>任务服务 : 验证任务状态
任务服务->>任务服务 : 更新任务状态为"处理中"
任务服务->>执行器 : 调用执行方法
执行器-->>任务服务 : 返回执行结果
任务服务->>任务服务 : 处理执行结果
任务服务->>数据库 : 更新任务状态和执行信息
任务服务-->>调度中心 : 返回执行状态
```

**Diagram sources**
- [XXLJobConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/XXLJobConfig.java#L1-L100)
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/job/JobService.java#L400-L600)

**Section sources**
- [XXLJobConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/XXLJobConfig.java#L1-L150)

### 调度流程
1. **任务创建**：业务系统调用JobService创建任务
2. **状态初始化**：任务状态设置为"待处理"
3. **调度触发**：XXL-JOB调度器定时扫描待处理任务
4. **执行调用**：调度器调用任务执行方法
5. **状态更新**：根据执行结果更新任务状态
6. **结果反馈**：向相关系统发送任务完成通知

### 调度策略
- **立即执行**：创建后立即调度
- **定时执行**：按指定时间点执行
- **周期执行**：按Cron表达式周期执行
- **依赖执行**：等待前置任务完成后执行

## 执行日志与错误处理

### 日志管理
- **操作日志**：记录任务状态变更、重要操作
- **执行日志**：记录任务执行过程中的关键步骤
- **错误日志**：记录执行异常的详细信息
- **审计日志**：记录敏感操作的审计信息

### 错误处理策略
1. **异常分类处理**：
   - 业务异常：返回错误码，不重试
   - 系统异常：记录错误，进行重试
   - 网络异常：增加重试次数，延长重试间隔

2. **重试机制**：
   - 指数退避算法：重试间隔随次数增加
   - 最大重试次数：可配置，默认3次
   - 重试条件：仅对可恢复异常进行重试

3. **熔断机制**：
   - 连续失败达到阈值时暂停调度
   - 一定时间后自动恢复或手动恢复
   - 发送告警通知相关人员

**Section sources**
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/job/JobService.java#L600-L800)
- [LogService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/LogService.java#L1-L50)

## 性能优化建议

1. **数据库优化**：
   - 在jobNo、status、orderNo字段上创建复合索引
   - 定期归档历史任务数据
   - 使用读写分离减轻主库压力

2. **缓存策略**：
   - 热点任务信息缓存到Redis
   - 缓存任务执行结果，避免重复计算
   - 使用本地缓存减少远程调用

3. **并发控制**：
   - 限制单个执行器的并发任务数
   - 使用信号量控制资源密集型任务
   - 实现任务优先级队列

4. **监控告警**：
   - 监控任务执行时长、成功率
   - 设置任务积压告警阈值
   - 记录任务执行性能指标

5. **批量处理**：
   - 支持批量创建任务
   - 实现批量状态更新
   - 批量查询优化

**Section sources**
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/job/JobService.java#L800-L1000)
- [XXLJobConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/XXLJobConfig.java#L100-L150)