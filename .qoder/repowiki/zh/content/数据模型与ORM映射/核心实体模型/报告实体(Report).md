# 报告实体(Report)

<cite>
**本文档引用的文件**  
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)
- [ReportTypeMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/ReportTypeMapper.xml)
- [SubReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/SubReportMapper.xml)
- [ReportMatrixRelMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMatrixRelMapper.xml)
- [ReportInfoPO.java](file://otsnotes-dbstorages/mybatis/model/ReportInfoPO.java)
- [ReportFilePO.java](file://otsnotes-dbstorages/mybatis/model/ReportFilePO.java)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 引言
本报告旨在全面分析“报告实体(Report)”的数据模型、业务逻辑、状态机、关联关系及生命周期管理。该实体是OTSNotes系统中的核心组成部分，用于管理检测报告的生成、审批、发布和归档全过程。文档将深入解析其数据库结构、服务层实现、状态转换规则以及与其他关键实体（如订单、分包、测试线等）的关系。

## 项目结构
报告实体的功能实现分布在多个模块中，遵循典型的分层架构。

```mermaid
graph TD
subgraph "otsnotes-domain"
ReportService[ReportService]
ConclusionCalcService[ConclusionCalcService]
ValidateDataService[ValidateDataService]
end
subgraph "otsnotes-dbstorages"
ReportMapper[ReportMapper]
ReportInfoPO[ReportInfoPO]
ReportFilePO[ReportFilePO]
end
subgraph "otsnotes-facade"
ReportFacade[ReportFacade]
end
subgraph "otsnotes-facade-model"
ReportInfo[ReportInfo]
ReportRsp[ReportRsp]
end
ReportService --> ReportMapper
ReportService --> ConclusionCalcService
ReportService --> ValidateDataService
ReportFacade --> ReportService
ReportMapper --> ReportInfoPO
ReportMapper --> ReportFilePO
```

**图示来源**
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)

**本节来源**
- [项目结构](file://workspace_path)

## 核心组件
报告实体的核心功能由`ReportService`类驱动，该类负责协调数据访问、业务逻辑、外部服务调用和事件发布。

**本节来源**
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)

## 架构概述
报告实体的处理流程涉及多个系统组件的协作。

```mermaid
sequenceDiagram
participant UI as "用户界面"
participant Facade as "ReportFacade"
participant Service as "ReportService"
participant Mapper as "ReportMapper"
participant DB as "数据库"
participant Kafka as "Kafka"
participant FileClient as "文件服务"
UI->>Facade : 发起报告操作请求
Facade->>Service : 调用业务方法
Service->>Service : 执行业务逻辑 (如状态检查)
alt 需要读取数据
Service->>Mapper : 查询报告信息
Mapper->>DB : 执行SQL查询
DB-->>Mapper : 返回数据
Mapper-->>Service : 返回PO对象
end
alt 需要写入数据
Service->>Mapper : 执行数据操作
Mapper->>DB : 执行SQL更新/插入
DB-->>Mapper : 返回结果
Mapper-->>Service : 返回结果
end
alt 需要生成文件
Service->>FileClient : 请求生成PDF
FileClient-->>Service : 返回文件信息
end
alt 操作成功
Service->>Kafka : 发布状态变更消息
end
Service-->>Facade : 返回响应
Facade-->>UI : 返回结果
```

**图示来源**
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)

## 详细组件分析

### 报告实体数据模型分析

#### 数据库表结构
报告实体的核心信息存储在`report_info`表中。

```mermaid
erDiagram
report_info {
varchar ID PK
varchar OrderNo
varchar ReportNo UK
varchar ParentReportNo FK
varchar ReportType
int ReportStatus
varchar ReportVersion
varchar RootReportNo
varchar ActualReportNo
varchar ConclusionMd5
varchar CreatedBy
timestamp CreatedDate
varchar ModifiedBy
timestamp ModifiedDate
varchar AmendRemark
varchar CertificateName
varchar LogoAliyunID
varchar TestMatrixMergeMode
tinyint ActiveIndicator
tinyint RecalculationFlag
}
report_type {
varchar ID PK
varchar Description
int Position
varchar AmendRemarkRule
int LanguageID
tinyint ActiveIndicator
}
report_file {
varchar ID PK
varchar ReportID FK
varchar Filename
varchar CloudID
varchar ReportFileType
int Status
varchar CreatedBy
timestamp CreatedDate
varchar ModifiedBy
timestamp ModifiedDate
}
sub_report {
varchar ID PK
varchar GeneralOrderInstanceID FK
varchar ObjectNo
varchar ObjectType
varchar Filename
varchar CloudID
varchar Status
int LanguageId
varchar ConclusionId
}
report_info ||--o{ report_file : "包含"
report_info ||--|| report_type : "属于"
report_info ||--o{ sub_report : "关联"
```

**图示来源**
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)
- [ReportTypeMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/ReportTypeMapper.xml)
- [SubReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/SubReportMapper.xml)

#### 核心属性定义
以下是报告实体的主要属性及其说明。

**报告基本信息**
- **ID**: 主键，唯一标识符。
- **ReportNo**: 报告编号，业务主键，全局唯一。
- **OrderNo**: 关联的订单编号。
- **ParentReportNo**: 父报告编号，用于报告合并场景。
- **RootReportNo**: 根报告编号，用于追踪报告链的源头。
- **ActualReportNo**: 实际报告编号，可能与ReportNo不同。
- **ReportVersion**: 报告版本号，用于管理报告修订。

**状态与生命周期**
- **ReportStatus**: 报告状态，使用`ReportStatus`枚举，是状态机的核心。
- **RecalculationFlag**: 重新计算标志，指示报告结论是否需要重新计算。
- **ActiveIndicator**: 激活指示器，软删除标志。

**文件与内容**
- **ConclusionMd5**: 结论内容的MD5哈希值，用于检测内容变更。
- **AmendRemark**: 修订备注。
- **CertificateName**: 证书名称。
- **LogoAliyunID**: 公司Logo在阿里云上的ID。
- **TestMatrixMergeMode**: 测试矩阵合并模式。

**审计信息**
- **CreatedBy/ModifiedBy**: 创建/修改人。
- **CreatedDate/ModifiedDate**: 创建/修改时间。

**本节来源**
- [ReportMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMapper.xml)

### 报告状态机分析

#### 状态转换规则
报告实体遵循严格的状态机，确保业务流程的正确性。

```mermaid
stateDiagram-v2
[*] --> 草稿
草稿 --> 待审批 : 提交审批
待审批 --> 已批准 : 审批通过
待审批 --> 草稿 : 审批驳回
已批准 --> 已发布 : 发布报告
已发布 --> 已归档 : 归档
已批准 --> 待审批 : 申请修订
待审批 --> 已作废 : 作废
已发布 --> 已作废 : 作废
已作废 --> [*]
已归档 --> [*]
note right of 草稿
初始状态，报告正在创建或编辑
end note
note right of 待审批
等待审批人审核
end note
note right of 已批准
已通过审批，可发布
end note
note right of 已发布
报告已对外发布
end note
```

**图示来源**
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)
- [ReportStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ReportStatus.java)

#### 业务逻辑
状态转换由`ReportService`中的方法驱动，例如：
- `submitForApproval()`: 将报告从“草稿”转为“待审批”。
- `approveReport()`: 将报告从“待审批”转为“已批准”。
- `publishReport()`: 将报告从“已批准”转为“已发布”。
- `reworkReport()`: 处理报告修订流程，可能将“已批准”或“已发布”的报告重新置为“待审批”。

**本节来源**
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)

### 关联关系分析

#### 与核心实体的关系
报告实体与多个其他实体存在关联。

```mermaid
classDiagram
class Report {
+String reportNo
+String orderNo
+Integer reportStatus
+ReportFile[] reportFiles
+SubReport[] subReports
+TestMatrix testMatrix
+Conclusion conclusion
+Order order
}
class Order {
+String orderNo
+Report[] reports
}
class SubContract {
+String subcontractNo
+SubReport[] subReports
}
class TestLine {
+String testLineNo
+TestResult[] testResults
}
class Conclusion {
+String conclusionText
+String conclusionId
}
class TestMatrix {
+String matrixId
+TestLine[] testLines
}
class ReportFile {
+String filename
+String cloudId
+String fileType
}
class SubReport {
+String objectNo
+String objectType
+String status
}
Report "1" *-- "0..*" ReportFile : 包含
Report "1" *-- "0..*" SubReport : 关联
Report "1" -- "1" TestMatrix : 使用
Report "1" -- "1" Conclusion : 包含
Report "1" -- "1" Order : 属于
SubContract "1" *-- "0..*" SubReport : 生成
```

**图示来源**
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)
- [ReportMatrixRelMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportMatrixRelMapper.xml)

**本节来源**
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)

## 依赖分析
报告服务依赖于多个内部和外部服务。

```mermaid
graph TD
ReportService[ReportService] --> ReportMapper[ReportMapper]
ReportService --> ConclusionCalcService[ConclusionCalcService]
ReportService --> ValidateDataService[ValidateDataService]
ReportService --> KafkaProducer[KafkaProducer]
ReportService --> fileClient[FileClient]
ReportService --> tokenClient[TokenClient]
ReportService --> orderClient[OrderClient]
ReportService --> customerClient[CustomerClient]
ReportService --> engineClient[EngineClient]
ReportService --> redissonClient[RedissonClient]
subgraph "外部系统"
Kafka[Kafka]
FileStorage[文件存储]
OrderSystem[订单系统]
CustomerSystem[客户系统]
Engine[报告引擎]
end
KafkaProducer --> Kafka
fileClient --> FileStorage
orderClient --> OrderSystem
customerClient --> CustomerSystem
engineClient --> Engine
```

**图示来源**
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)

**本节来源**
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)

## 性能考虑
为确保报告查询和生成的性能，建议采取以下措施：
1. **索引优化**: 在`report_info`表的`ReportNo`, `OrderNo`, `ReportStatus`, `CreatedDate`等常用查询字段上建立索引。
2. **缓存策略**: 使用Redis缓存频繁访问的报告元数据（如报告状态、文件信息），减少数据库压力。
3. **分页查询**: 对于报告列表查询，强制使用分页（`pageIndex`, `pageSize`），避免全表扫描。
4. **异步处理**: 报告生成、文件上传等耗时操作应通过Kafka消息队列异步执行，避免阻塞主流程。

## 故障排除指南
常见问题及解决方案：
- **问题**: 报告状态无法从“草稿”变为“待审批”。
  - **检查**: 确认报告关联的测试线（TestLine）状态是否都已达到可审批状态（如“已完成”）。
- **问题**: 报告文件无法生成。
  - **检查**: 查看`ReportService`日志，确认与文件服务（FileClient）的通信是否正常，检查文件存储服务的可用性。
- **问题**: 报告列表查询缓慢。
  - **检查**: 分析SQL执行计划，确认相关字段是否有索引，考虑增加复合索引。

**本节来源**
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)

## 结论
报告实体是OTSNotes系统中一个复杂且关键的模块，它集成了数据管理、业务流程、文件处理和外部集成。通过对其数据模型、状态机、服务逻辑和依赖关系的深入分析，可以更好地理解其工作原理，为系统的维护、优化和扩展提供坚实的基础。遵循文档中提到的性能优化和故障排除建议，可以有效保障报告功能的稳定和高效运行。