# 分包单实体(SubContract)

<cite>
**本文档引用文件**   
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/SubContractService.java)
- [SubContractStatusEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/SubContractStatusEnum.java)
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java)
- [SubContractExtMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/SubContractExtMapper.java)
- [SubContractFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/SubContractFacade.java)
- [SubContractFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/SubContractFacadeImpl.java)
</cite>

## 目录
1. [引言](#引言)
2. [分包单实体数据模型](#分包单实体数据模型)
3. [分包单状态机与业务规则](#分包单状态机与业务规则)
4. [关联实体关系分析](#关联实体关系分析)
5. [分包单操作服务分析](#分包单操作服务分析)
6. [实际使用场景示例](#实际使用场景示例)
7. [数据完整性约束与性能优化](#数据完整性约束与性能优化)

## 引言
分包单（SubContract）是实验室信息系统中的核心业务实体之一，用于管理将测试任务外包给其他实验室的业务流程。该实体记录了分包单的基本信息、状态流转、同步信息等关键属性，并与订单（Order）、报告（Report）、测试项（TestLine）等核心实体存在紧密关联。本文档旨在全面阐述分包单实体的数据模型、状态机逻辑、关联关系及实际应用场景，为系统开发、维护和使用提供详尽的技术参考。

## 分包单实体数据模型

### 字段定义与数据类型
分包单实体（SubContractPO）的主要字段定义如下：

```java
public class SubContractPO {
    private String ID; // 主键，UUID
    private String subContractNo; // 分包单号，唯一标识
    private String orderNo; // 关联订单号，外键
    private Integer subContractOrder; // 分包类型编码
    private Integer status; // 状态，外键关联SubContractStatusEnum
    private Date createdDate; // 创建时间
    private String createdBy; // 创建人
    private Date modifiedDate; // 修改时间
    private String modifiedBy; // 修改人
    private Date startDate; // 开始测试时间
    private Date completeDate; // 完成时间
    private String externalNo; // 外部系统编号（如SODA编号）
    // ... 其他字段
}
```

### 主键与外键
- **主键**：`ID` 字段，采用UUID作为全局唯一标识。
- **外键**：
  - `orderNo`：引用 `Order` 实体的订单号，建立与订单的一对多关系。
  - `status`：引用 `SubContractStatusEnum` 枚举值，定义分包单的当前状态。
  - `subContractOrder`：引用 `SubContractType` 枚举值，定义分包单的类型（如分包、Slim、StarLims）。

### 约束条件
- **唯一性约束**：`subContractNo` 字段必须唯一，确保每个分包单有唯一的编号。
- **非空约束**：`subContractNo`, `orderNo`, `status`, `createdDate`, `createdBy` 等核心字段不允许为空。
- **状态约束**：`status` 字段的值必须是 `SubContractStatusEnum` 枚举中定义的有效状态。
- **业务逻辑约束**：`startDate` 必须在 `createdDate` 之后，`completeDate` 必须在 `startDate` 之后。

**Section sources**
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java#L1-L50)

## 分包单状态机与业务规则

### 状态定义
分包单状态由 `SubContractStatusEnum` 枚举类定义，包含以下状态：
- **新建 (Created, 0)**：分包单已创建，等待发送。
- **测试中 (Testing, 1)**：分包单已发送，外部实验室已确认并开始测试。
- **已完成 (Complete, 2)**：外部实验室已完成测试，结果已回传。
- **已取消 (Cancelled, 3)**：分包单被取消，流程终止。
- **待处理 (Pend, 4)**：分包单处于待处理状态。

### 状态流转逻辑
分包单的状态流转遵循严格的业务规则，确保流程的正确性和数据的一致性。

```mermaid
stateDiagram-v2
[*] --> 新建
新建 --> 测试中 : 发送/确认
测试中 --> 已完成 : 完成
新建 --> 已取消 : 取消
测试中 --> 已取消 : 取消
已完成 --> [*]
已取消 --> [*]
```

**Diagram sources**
- [SubContractStatusEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/SubContractStatusEnum.java#L16-L87)

### 业务规则
1. **状态变更校验**：
   - 只有在 `新建` 或 `测试中` 状态下，才能调用 `confirmSubcontract` 或 `testingSubcontract` 方法将其状态变更为 `测试中`。
   - 只有在 `测试中` 状态下，才能调用 `completeSubcontract` 方法将其状态变更为 `已完成`。
   - 在 `已完成` 或 `已取消` 状态下，不允许进行任何状态变更操作。
2. **取消规则**：
   - 在 `已完成` 状态下，对于特定产品线（如SL），不允许取消分包报告。
   - 取消操作会触发业务日志记录，并调用下游服务清理相关测试数据。
3. **触发规则**：
   - 当分包单状态变更为 `测试中` 时，会触发关联订单的状态变更为 `测试中`，确保主订单状态与分包状态同步。

**Section sources**
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/SubContractService.java#L200-L300)
- [SubContractStatusEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/SubContractStatusEnum.java#L16-L87)

## 关联实体关系分析

### 与订单（Order）的关联
分包单与订单是典型的 **一对多** 关系。一个订单可以产生多个分包单，每个分包单都关联到一个特定的订单。这种关系通过 `SubContractPO` 中的 `orderNo` 字段与 `Order` 实体的主键建立外键关联。

### 与报告（Report）的关联
分包单与报告的关系是 **间接的多对多** 关系。分包单首先通过 `SubReportPO` 实体与外部报告（如SODA报告）建立关联，然后 `SubReportPO` 再与主系统的 `ReportInfoPO` 建立关联。这种设计实现了分包报告与主报告的灵活映射。

### 与测试项（TestLine）的关联
分包单与测试项的关系是 **间接的一对多** 关系。分包单包含一组需要外包的测试项。这些关联信息通常存储在 `TestLine` 或 `TestLineInstance` 实体中，通过 `subContractNo` 字段进行关联。当分包单状态变更时，会批量更新关联的测试项状态。

```mermaid
erDiagram
ORDER {
string orderNo PK
string status
string labCode
}
SUBCONTRACT {
string subContractNo PK
string orderNo FK
int status
date startDate
date completeDate
}
SUBREPORT {
string subReportNo PK
string subContractNo FK
string objectType
string reportFileType
}
REPORT {
string reportId PK
string orderNo FK
int reportStatus
}
TESTLINE {
string testLineID PK
string orderNo FK
string subContractNo FK
int testLineStatus
}
ORDER ||--o{ SUBCONTRACT : "包含"
SUBCONTRACT ||--o{ SUBREPORT : "生成"
SUBREPORT }o--|| REPORT : "关联"
ORDER ||--o{ TESTLINE : "包含"
SUBCONTRACT ||--o{ TESTLINE : "影响"
```

**Diagram sources**
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java#L1-L50)
- [SubReportPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubReportPO.java#L1-L50)
- [TestLineInstancePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineInstancePO.java#L1-L50)

## 分包单操作服务分析

### 核心服务方法
`SubContractService` 类提供了分包单的核心业务操作方法：

- **`confirmSubcontract`**：创建或确认分包单与外部系统的关联关系。
- **`testingSubcontract`**：将分包单状态更新为“测试中”，并触发关联订单状态变更。
- **`completeSubcontract`**：将分包单状态更新为“已完成”，并批量更新关联的测试项状态。
- **`cancelSubReport`**：取消分包报告，清理相关数据并记录业务日志。

### 事务管理
所有状态变更操作（如 `testingSubcontract`, `completeSubcontract`）均在 `transactionTemplate` 的事务管理下执行，确保数据库操作的原子性。如果在更新测试项状态时失败，整个事务将回滚，防止数据不一致。

### 服务调用流程
```mermaid
sequenceDiagram
participant 前端 as 前端应用
participant 门面 as SubContractFacade
participant 服务 as SubContractService
participant 数据库 as 数据库
participant 下游服务 as 下游服务 (e.g., PreOrder)
前端->>门面 : 调用 completeSubcontract(subcontractNo)
门面->>服务 : 调用 completeSubcontract(subcontractCompleteReq)
服务->>服务 : 启动事务
服务->>数据库 : 查询分包单信息
服务->>数据库 : 更新分包单状态为“已完成”
服务->>数据库 : 查询关联的测试项
服务->>下游服务 : 批量更新测试项状态
下游服务-->>服务 : 返回结果
alt 更新成功
服务->>服务 : 提交事务
服务-->>门面 : 返回成功
门面-->>前端 : 返回成功
else 更新失败
服务->>服务 : 回滚事务
服务-->>门面 : 返回失败
门面-->>前端 : 返回失败
end
```

**Diagram sources**
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/SubContractService.java#L300-L400)
- [SubContractFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/SubContractFacadeImpl.java#L1-L50)

**Section sources**
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/SubContractService.java#L200-L400)
- [SubContractFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/SubContractFacadeImpl.java#L1-L50)

## 实际使用场景示例

### 场景一：创建并完成分包单
1.  **创建**：系统为订单 `OT123` 创建分包单 `SC456`，状态为“新建”。
2.  **发送**：将分包单信息发送至外部实验室系统（如SODA）。
3.  **确认**：外部实验室确认接收，调用 `confirmSubcontract` 接口，状态变更为“测试中”。
4.  **测试**：外部实验室进行测试。
5.  **完成**：外部实验室完成测试并回传结果，调用 `completeSubcontract` 接口。
6.  **结果**：分包单 `SC456` 状态变更为“已完成”，订单 `OT123` 下的关联测试项状态也更新为“已完成”。

### 场景二：取消分包报告
1.  **请求**：用户请求取消分包单 `SC456` 对应的外部报告 `SR789`。
2.  **校验**：系统校验分包单状态（不能是“已完成”或“已取消”）、报告状态等。
3.  **执行**：调用 `cancelSubReport` 方法，删除相关测试数据，并调用下游服务取消报告。
4.  **记录**：记录业务日志，标记该操作。

## 数据完整性约束与性能优化

### 数据完整性约束
- **外键约束**：在数据库层面为 `orderNo` 和 `status` 字段建立外键约束，防止出现孤立记录。
- **唯一索引**：为 `subContractNo` 字段建立唯一索引，保证分包单号的唯一性。
- **非空约束**：对所有必填字段设置 `NOT NULL` 约束。
- **业务规则校验**：在服务层进行充分的业务规则校验，如状态流转校验、参数校验等。

### 性能优化建议
- **索引优化**：为高频查询字段（如 `orderNo`, `status`, `externalNo`）建立复合索引，例如 `idx_order_status(orderNo, status)`，以加速按订单查询分包单列表的性能。
- **缓存策略**：对于不经常变动的分包单基础信息（如分包单号、创建时间），可以考虑使用Redis缓存，减少数据库查询压力。
- **批量操作**：在处理与测试项状态同步等场景时，使用批量更新（batch update）而非逐条更新，显著提升性能。
- **异步处理**：对于非核心的、耗时的操作（如发送通知邮件、记录详细审计日志），可以采用Kafka消息队列进行异步处理，提高主流程的响应速度。

**Section sources**
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java#L1-L20)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/subcontract/SubContractService.java#L1-L50)