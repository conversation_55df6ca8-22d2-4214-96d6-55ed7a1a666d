# 订单实体(Order)

<cite>
**本文档引用的文件**   
- [OrderStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/OrderStatus.java)
- [GeneralOrderInstanceInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/GeneralOrderInstanceInfoPO.java)
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [OrderCitationRelMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderCitationRelMapper.java)
- [OrderLanguageRelMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderLanguageRelMapper.java)
- [OrderSubcontractRelMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderSubcontractRelMapper.java)
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java)
</cite>

## 目录
1. [订单实体概述](#订单实体概述)
2. [核心字段定义](#核心字段定义)
3. [订单状态机](#订单状态机)
4. [关系映射](#关系映射)
5. [数据访问模式](#数据访问模式)
6. [常见问题解决方案](#常见问题解决方案)

## 订单实体概述

订单实体(Order)是系统中的核心业务实体，负责管理订单的全生命周期。该实体通过MyBatis持久化到数据库，包含订单的基本信息、客户信息、状态信息等核心属性。订单实体与其他多个实体存在关联关系，包括分包、报告、测试线等，形成了复杂的业务网络。

**Section sources**
- [GeneralOrderInstanceInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/GeneralOrderInstanceInfoPO.java)

## 核心字段定义

订单实体包含以下核心字段：

```mermaid
erDiagram
ORDER {
string ID PK
string orderNo UK
integer orderStatus
string customerCode
string customerName
string customerGroupCode
string customerGroupName
string labCode
integer orderLaboratoryID
boolean activeIndicator
datetime createdDate
string createdBy
datetime modifiedDate
string modifiedBy
datetime lastModifiedTimestamp
integer conclusionMode
datetime confirmMatrixDate
string customerNameCn
string applicantCustomerCode
string applicantCustomerNameEn
string applicantCustomerNameCN
string applicantCustomerGroupCode
string applicantCustomerGroupName
string responsibleTeamCode
string CSName
string sampleDescription
short returnSample
string technicalSupporter
string suffixNum
}
```

**Diagram sources**
- [GeneralOrderInstanceInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/GeneralOrderInstanceInfoPO.java)

### 字段详细说明

- **ID**: 订单唯一标识符，主键，VARCHAR(36)类型，必填
- **orderNo**: 订单编号，VARCHAR(50)类型
- **orderStatus**: 订单状态，INTEGER(10)类型，关联OrderStatus枚举
- **customerCode**: 客户代码，VARCHAR(50)类型
- **customerName**: 客户名称，VARCHAR(360)类型
- **customerGroupCode**: 客户组代码，VARCHAR(50)类型
- **customerGroupName**: 客户组名称，VARCHAR(360)类型
- **labCode**: 实验室代码，VARCHAR(50)类型
- **orderLaboratoryID**: 订单实验室ID，INTEGER(10)类型
- **activeIndicator**: 激活指示器，BIT类型，默认值1，0表示非激活，1表示激活
- **createdDate**: 创建日期，TIMESTAMP(19)类型
- **createdBy**: 创建者，VARCHAR(50)类型
- **modifiedDate**: 修改日期，TIMESTAMP(19)类型
- **modifiedBy**: 修改者，VARCHAR(50)类型
- **lastModifiedTimestamp**: 最后修改时间戳，TIMESTAMP(19)类型，默认值CURRENT_TIMESTAMP(3)
- **conclusionMode**: 结论模式，INTEGER(10)类型，默认值0，0=按矩阵，1=按PP矩阵
- **confirmMatrixDate**: 确认矩阵日期，TIMESTAMP(19)类型

**Section sources**
- [GeneralOrderInstanceInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/GeneralOrderInstanceInfoPO.java)

## 订单状态机

订单状态机定义了订单在其生命周期中的状态转换规则。系统通过OrderStatus枚举类管理订单状态。

```mermaid
stateDiagram-v2
[*] --> New
New --> Testing : "开始测试"
Testing --> Completed : "测试完成"
Completed --> [*]
```

**Diagram sources**
- [OrderStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/OrderStatus.java)

### 状态定义

订单状态枚举(OrderStatus)包含以下状态：

- **New(1601)**: 新建状态，订单刚创建时的状态
- **Testing(1602)**: 测试中状态，订单开始测试时的状态
- **Completed(1603)**: 已完成状态，订单测试完成时的状态

### 状态转换规则

状态转换通过静态方法进行管理：

- `getOrderStatus(Integer status)`: 根据状态码获取对应的状态枚举
- `check(Integer status, OrderStatus orderStatus)`: 检查指定状态码是否匹配指定状态枚举

状态转换通常由业务逻辑触发，例如在分包服务中调用状态客户端更新订单状态：

```java
SysStatusReq sysStatusReq = new SysStatusReq();
sysStatusReq.setUserName("System");
sysStatusReq.setIgnoreOldStatus(true);
sysStatusReq.setNewStatus(com.sgs.preorder.facade.model.enums.OrderStatus.Testing.getStatus());
sysStatusReq.setObjectNo(currentOrderNo);
statusClient.insertStatusInfo(sysStatusReq);
```

**Section sources**
- [OrderStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/OrderStatus.java)
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java)

## 关系映射

订单实体与其他多个实体存在关联关系，通过外键和关系表进行映射。

### 订单与引用关系

订单与引用实体通过OrderCitationRelMapper进行映射：

```mermaid
erDiagram
ORDER ||--o{ ORDER_CITATION_REL : "包含"
ORDER_CITATION_REL }o--|| CITATION : "引用"
ORDER {
string ID PK
string orderNo
}
ORDER_CITATION_REL {
long ID PK
string orderId FK
long citationId
integer langType
}
CITATION {
long ID PK
string citationText
}
```

**Diagram sources**
- [OrderCitationRelMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderCitationRelMapper.java)

主要操作方法：
- `getOrderCitationRelInfoList(String orderId)`: 根据订单ID获取引用关系列表
- `batchInsert(Collection<OrderCitationRelInfoPO> rels)`: 批量插入引用关系
- `batchUpdate(List<OrderCitationRelInfoPO> rels)`: 批量更新引用关系
- `batchDelete(Set<Long> orderCitationIds)`: 批量删除引用关系

### 订单与语言关系

订单与语言实体通过OrderLanguageRelMapper进行映射：

```mermaid
erDiagram
ORDER ||--o{ ORDER_LANGUAGE_REL : "包含"
ORDER_LANGUAGE_REL }o--|| LANGUAGE : "语言"
ORDER {
string ID PK
string orderNo
}
ORDER_LANGUAGE_REL {
long ID PK
string orderId FK
long objectBaseId
long langBaseId
integer langType
}
LANGUAGE {
long ID PK
string languageCode
string languageName
}
```

**Diagram sources**
- [OrderLanguageRelMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderLanguageRelMapper.java)

主要操作方法：
- `getOrderLanguageInfoList(String orderId, Integer langType)`: 根据订单ID和语言类型获取语言关系列表
- `batchInsert(Collection<OrderLanguageRelInfoPO> langRels)`: 批量插入语言关系
- `batchDelLangRelInfo(String orderId, Set<Long> langBaseIds)`: 根据订单ID和语言基础ID批量删除语言关系

### 订单与分包关系

订单与分包实体通过OrderSubcontractRelMapper进行映射：

```mermaid
erDiagram
ORDER ||--o{ ORDER_SUBCONTRACT_REL : "包含"
ORDER_SUBCONTRACT_REL }o--|| SUBCONTRACT : "分包"
ORDER {
string ID PK
string orderNo
}
ORDER_SUBCONTRACT_REL {
long ID PK
string orderId FK
string subOrderId
string subRelId
integer relType
}
SUBCONTRACT {
string ID PK
string subContractNo
string subContractName
}
```

**Diagram sources**
- [OrderSubcontractRelMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderSubcontractRelMapper.java)

主要操作方法：
- `getSubcontractRelListByOrderId(String orderId)`: 根据订单ID获取分包关系列表
- `getSubcontractRelBySubOrderId(String orderId)`: 根据分包订单ID获取分包关系
- `batchInsert(List<SubcontractRelInfoPO> rels)`: 批量插入分包关系
- `batchDelete(List<Long> relIds)`: 批量删除分包关系

**Section sources**
- [OrderCitationRelMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderCitationRelMapper.java)
- [OrderLanguageRelMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderLanguageRelMapper.java)
- [OrderSubcontractRelMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderSubcontractRelMapper.java)

## 数据访问模式

订单实体的数据访问通过MyBatis Mapper接口实现，提供了丰富的CRUD操作。

### 核心数据访问方法

```mermaid
classDiagram
class OrderMapper {
+int saveOrderInfo(GeneralOrderInstanceInfoPO order)
+int updateOrderInfo(GeneralOrderInstanceInfoPO order)
+int updateOrderConclusionMode(GeneralOrderInstanceInfoPO order)
+int updateOrderStatus(GeneralOrderInstanceInfoPO order)
+GeneralOrderInstanceInfoPO getOrderInfo(String orderNo)
+GeneralOrderInstanceInfoPO getOrderByReportId(String reportId)
+GeneralOrderInstanceInfoPO getOrderInfoByOrderId(String orderId)
+String getOrderLabCode(String orderNo)
+List<OrderTestLineSamplePo> getOrderTestLineSampleList(String jobNo)
+OrderSlimJobInfo queryGeneralOrderIdBySlimJobNo(String externalNo)
+OrderConclusionModeRsp getOrderConclusionMode(DataEntryConclusionReq reqObject)
+int updateOrderConfirmMatrixDate(UpdateConfirmMatrixDateVO updateConfirmMatrixDateVO)
+int updateOrderActive(GeneralOrderInstanceInfoPO po)
}
```

**Diagram sources**
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)

### 查询优化建议

1. **索引优化**: 为常用查询字段创建索引，如orderNo、reportId、orderId等
2. **批量操作**: 使用批量插入、更新、删除方法提高性能
3. **缓存策略**: 对于频繁查询但不常变更的数据，考虑使用Redis缓存
4. **分页查询**: 对于大数据量查询，使用分页避免内存溢出

### 业务逻辑实现

订单服务类(OrderService)实现了核心业务逻辑：

```java
@Service
public class OrderService {
    @Autowired
    private OrderMapper orderMapper;
    
    public CustomResult createOrderInfo(SyncOrderInfo reqObject) {
        CustomResult rspResult = new CustomResult(false);
        logger.info("PreOrder Create Order: {}.", reqObject);
        boolean noChangeFlag = preOrdercheck(reqObject,rspResult);
        if (rspResult.isSuccess()) {
            CustomResult creatResult = createGeneralOrder(reqObject, noChangeFlag);
            if (!creatResult.isSuccess()) {
                return rspResult.fail(creatResult.getMsg());
            }
            rspResult.setMsg(creatResult.getMsg());
        }
        return rspResult;
    }
    
    private boolean preOrdercheck(SyncOrderInfo preOrder, CustomResult rspResult) {
        // 订单校验逻辑
    }
    
    private GeneralOrderInstanceInfoPO generalOrder(SyncOrderInfo preOrder, boolean newOrderFlag, GeneralOrderInstanceInfoPO generalOrderInstanceDTO) {
        // 生成订单逻辑
    }
}
```

**Section sources**
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java)

## 常见问题解决方案

### 订单创建失败

**问题**: 订单创建时返回"Order No is null"错误

**解决方案**: 
1. 确保请求参数中包含orderNo字段
2. 检查orderNo是否为空字符串
3. 验证orderNo格式是否符合系统要求

### 订单状态更新失败

**问题**: 订单状态无法从"新建"更新为"测试中"

**解决方案**:
1. 检查状态码是否正确，Testing状态码为1602
2. 确认状态客户端配置正确
3. 验证订单是否存在且处于可更新状态

### 分包关系同步异常

**问题**: 分包信息无法正确同步到订单

**解决方案**:
1. 检查分包关系映射器的批量插入方法
2. 验证外键约束是否满足
3. 确认事务管理配置正确

### 性能优化建议

1. **数据库连接池**: 配置合适的连接池大小
2. **查询缓存**: 对频繁查询的结果进行缓存
3. **异步处理**: 将非关键操作异步化
4. **批量处理**: 合并多个小操作为批量操作

**Section sources**
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java)