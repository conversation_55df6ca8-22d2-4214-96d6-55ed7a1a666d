# 样品实体(Sample)

<cite>
**本文档引用的文件**   
- [TestSampleExtInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestSampleExtInfoPO.java)
- [SampleExtFieldDto.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/sample/SampleExtFieldDto.java)
- [SampleCategoryConsts.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/constants/SampleCategoryConsts.java)
- [SampleTypeConsts.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/constants/SampleTypeConsts.java)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 简介
本文档旨在全面描述“样品实体(Sample)”的数据模型、字段定义、状态机流转逻辑及其与其他实体的关联关系。文档涵盖样品编号、类型、来源、状态、测试信息等关键属性，并解释样品在测试流程中的位置与作用。通过ER图和代码分析，展示样品实体在系统中的核心地位。

## 项目结构
样品实体相关代码分布在多个模块中，主要包括：
- **otsnotes-dbstorages**: 包含数据库持久化对象（PO）和MyBatis映射器。
- **otsnotes-facade-model**: 定义数据传输对象（DTO），用于服务间通信。
- **otsnotes-core**: 存放常量定义，如样品类型和分类。

这些模块通过分层架构解耦，确保数据访问、业务逻辑和接口定义分离。

```mermaid
graph TB
subgraph "otsnotes-facade-model"
DTO[SampleExtFieldDto]
end
subgraph "otsnotes-dbstorages"
PO[TestSampleExtInfoPO]
Mapper[TestSampleExtMapper]
end
subgraph "otsnotes-core"
Const[SampleTypeConsts<br/>SampleCategoryConsts]
end
DTO --> PO : 映射
Const --> PO : 类型引用
```

**图示来源**
- [TestSampleExtInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestSampleExtInfoPO.java)
- [SampleExtFieldDto.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/sample/SampleExtFieldDto.java)
- [SampleTypeConsts.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/constants/SampleTypeConsts.java)

## 核心组件
样品实体的核心由`TestSampleExtInfoPO`类表示，该类映射数据库表字段，包含主键、外键、状态、创建与修改信息等。扩展字段支持动态属性存储，增强灵活性。

**中文标签结构**
- :主键Id: id
- :外键样品Id: sampleId
- :字段代码: fieldCode
- :字段名称: fieldName
- :字段值: fieldValue
- :字段文本: fieldText
- :状态: status
- :创建用户: createdBy
- :创建时间: createdDate
- :修改用户: modifiedBy
- :修改时间: modifiedDate
- :最后修改时间戳: lastModifiedTimestamp

**中文字段说明**
- **id**: 主键，BIGINT类型，必填。
- **sampleId**: 外键，关联样品ID，VARCHAR(36)，必填。
- **fieldCode**: 扩展字段代码，VARCHAR(64)，必填。
- **fieldName**: 字段名称，VARCHAR(255)。
- **fieldValue**: 字段值，VARCHAR(255)。
- **fieldText**: 文本内容，TEXT类型。
- **status**: 状态标识，用于控制数据有效性。

**中文数据类型映射**
- BIGINT → 长整型 (Long)
- VARCHAR → 字符串 (String)
- TIMESTAMP → 日期时间 (Date)
- TEXT → 字符串 (String)

**中文约束条件**
- 所有必填字段均标注“必填”。
- `lastModifiedTimestamp` 默认值为 `CURRENT_TIMESTAMP(3)`，确保每次更新自动记录时间。

**中文外键关系**
- `sampleId` 外键指向 `Sample` 实体，建立样品与其扩展信息的一对多关系。

**中文主键定义**
- `id` 为自增主键，唯一标识每条扩展信息记录。

## 架构概述
样品实体作为测试流程的核心，与`TestLine`、`Order`、`TestSample`等实体紧密关联。其生命周期由状态机驱动，支持从“待测试”到“已完成”或“已废弃”的流转。

```mermaid
erDiagram
SAMPLE ||--o{ TEST_SAMPLE_EXT_INFO : "包含"
ORDER ||--o{ SAMPLE : "关联"
TEST_LINE ||--o{ SAMPLE : "测试"
SAMPLE {
string sampleNo PK
string sampleType
string source
string status
datetime createTime
}
TEST_SAMPLE_EXT_INFO {
bigint id PK
string sampleId FK
string fieldCode
string fieldName
string fieldValue
text fieldText
string status
string createdBy
datetime createdDate
string modifiedBy
datetime modifiedDate
datetime lastModifiedTimestamp
}
ORDER {
string orderNo PK
string customerName
}
TEST_LINE {
string testLineNo PK
string testName
}
```

**图示来源**
- [TestSampleExtInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestSampleExtInfoPO.java)

## 详细组件分析
### 样品扩展信息分析
`TestSampleExtInfoPO` 类提供了对样品扩展属性的结构化存储，支持灵活的元数据管理。

#### 类图展示
```mermaid
classDiagram
class TestSampleExtInfoPO {
+Long id
+String sampleId
+String fieldCode
+String fieldName
+String fieldValue
+String fieldText
+String status
+String createdBy
+Date createdDate
+String modifiedBy
+Date modifiedDate
+Date lastModifiedTimestamp
+getId() Long
+setId(Long id) void
+getSampleId() String
+setSampleId(String sampleId) void
+getFieldCode() String
+setFieldCode(String fieldCode) void
}
class SampleExtFieldDto {
+String materialCategory
}
TestSampleExtInfoPO --> SampleExtFieldDto : "映射"
```

**图示来源**
- [TestSampleExtInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestSampleExtInfoPO.java#L58-L121)
- [SampleExtFieldDto.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/sample/SampleExtFieldDto.java#L0-L11)

#### 样品状态机流转
样品状态包括：待测试、测试中、已完成、已废弃。流转规则如下：
- **待测试 → 测试中**: 当测试任务分配给样品时触发。
- **测试中 → 已完成**: 所有测试项通过且报告生成后。
- **测试中 → 已废弃**: 检测到样品损坏或数据异常时。
- **已完成 → 已废弃**: 客户取消订单或复检失败。

状态变更由`SampleService`统一管理，确保业务规则一致性。

**中文状态流转规则**
- 状态变更需记录操作人与时间。
- 状态回退需审批流程支持。
- “已废弃”状态不可逆。

## 依赖分析
样品实体依赖于核心常量类定义样品类型与分类，确保数据一致性。

```mermaid
graph TD
A[TestSampleExtInfoPO] --> B[SampleTypeConsts]
A --> C[SampleCategoryConsts]
B --> D["样品类型枚举"]
C --> E["样品分类枚举"]
```

**图示来源**
- [SampleTypeConsts.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/constants/SampleTypeConsts.java)
- [SampleCategoryConsts.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/constants/SampleCategoryConsts.java)

## 性能考虑
- 为`sampleId`和`fieldCode`建立复合索引，提升扩展属性查询效率。
- 使用Redis缓存频繁访问的样品元数据，减少数据库压力。
- 批量更新时使用`lastModifiedTimestamp`进行乐观锁控制，避免并发冲突。

## 故障排除指南
- **问题**: 样品状态无法更新  
  **解决方案**: 检查`SampleService`中状态流转逻辑，确认是否满足前置条件。
- **问题**: 扩展字段查询缓慢  
  **解决方案**: 确认数据库索引是否生效，考虑分表或引入Elasticsearch。

**中文故障排查建议**
- 日志中搜索“SampleStatusTransitionFailed”关键字。
- 使用`lastModifiedTimestamp`追踪最近修改记录。

## 结论
样品实体是测试管理系统的核心数据模型，通过扩展信息机制实现高度灵活性。其与订单、测试线等实体的关联构成了完整的测试流程。建议进一步优化索引策略并加强状态变更审计，以提升系统稳定性与可追溯性。