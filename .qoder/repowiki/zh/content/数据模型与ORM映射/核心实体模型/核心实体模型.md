# 核心实体模型

<cite>
**本文档引用的文件**   
- [Sample.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Sample.java)
- [Job.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Job.java)
- [TestLine.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/trims/TestLine.java)
- [TableType.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TableType.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心实体概述](#核心实体概述)
3. [实体属性与数据类型](#实体属性与数据类型)
4. [实体关系与ER图](#实体关系与er图)
5. [生命周期状态机](#生命周期状态机)
6. [数据完整性与性能优化](#数据完整性与性能优化)

## 引言
本文件旨在全面介绍otsnotes-service系统中的核心数据实体模型。通过详细分析Order（订单）、SubContract（分包）、Report（报告）、Sample（样本）、TestLine（测试线）、Conclusion（结论）和Job（作业）等核心实体，为开发者和初学者提供清晰的数据模型理解。文档涵盖了实体的属性定义、数据类型、主外键关系、实体间关联以及状态生命周期等关键信息。

## 核心实体概述
otsnotes-service系统围绕一系列核心实体构建，这些实体共同构成了业务流程的数据基础。主要实体包括：

- **订单 (Order)**：业务的起点，代表客户提交的检测请求
- **作业 (Job)**：订单的执行单元，包含具体的检测任务
- **样本 (Sample)**：检测的物理对象，与作业关联
- **测试线 (TestLine)**：具体的检测项目，定义了检测标准和方法
- **报告 (Report)**：检测结果的最终呈现
- **结论 (Conclusion)**：对测试结果的总结性判断
- **分包 (SubContract)**：将部分检测任务外包给第三方的业务处理

这些实体通过复杂的关系网络相互连接，形成了完整的检测业务流程。

**Section sources**
- [TableType.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TableType.java#L8-L30)

## 实体属性与数据类型

### 样本实体 (Sample)
样本实体定义了检测对象的详细信息，其主要属性如下：

**属性列表：**
- **DESCRIPTION**：描述信息，String类型
- **SAMPLEIDENT**：样本标识，String类型
- **SAM_DESCRIPTION**：样本描述，String类型
- **SAM_REMARKS**：样本备注，String类型
- **PRODUCTCODE**：产品代码，String类型
- **COLOR**：颜色，String类型
- **MATERIAL**：材料，String类型
- **CLIENTDSCRIPTION**：客户描述，String类型
- **SGSDESCRIPTION**：SGS描述，String类型
- **REMARK**：备注，String类型
- **ident**：内部标识，String类型

**数据结构特点：**
- 使用XML注解进行序列化配置
- 包含多个描述字段以满足不同业务场景需求
- 支持生物字段(SampleBioField)和方案(Scheme)的集合属性

```mermaid
classDiagram
class Sample {
+String description
+String sampleident
+String samDescription
+String samRemarks
+String productCode
+String color
+String material
+String clientDescription
+String SGSDescription
+String remark
+String ident
+List<SampleBioField> sampleBioField
+List<Scheme> schemeList
+getDescription() String
+setSampleident(String) void
+getSchemeList() List<Scheme>
+setIdent(String) void
}
```

**Diagram sources**
- [Sample.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Sample.java#L10-L221)

**Section sources**
- [Sample.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Sample.java#L1-L223)

### 作业实体 (Job)
作业实体代表了检测任务的执行单元，其主要属性包括：

**属性列表：**
- **CCLAS_LABCODE**：实验室代码，String类型
- **PROJ_CODE**：项目代码，String类型
- **OWNERCODE**：所有者代码，String类型
- **PRO_JOB**：生产作业号，String类型
- **PRODUCTCODE**：产品代码，String类型
- **REPORTTPL**：报告模板，String类型
- **COSTCODE**：成本代码，String类型
- **REQUIRED**：要求，String类型
- **OM_SERVICELEVEL**：服务级别，String类型
- **OM_BUYER**：买家，String类型
- **JOBDESCRIPTION**：作业描述，String类型
- **bioField**：生物字段，String类型
- **jobBioFieldList**：作业生物字段列表，List<JobBioField>类型
- **sampleList**：样本列表，List<Sample>类型
- **notes1**：备注1，String类型

**数据结构特点：**
- 与样本实体存在一对多关系
- 包含丰富的业务属性以支持检测流程管理
- 使用XML注解进行数据绑定

```mermaid
classDiagram
class Job {
+String ccLasLabCode
+String projCode
+String ownerCode
+String proJob
+String productCode
+String reporttpl
+String costCode
+String required
+String omServiceLevel
+String omBuyer
+String jobDescription
+String bioField
+String notes1
+List<JobBioField> jobBioFieldList
+List<Sample> sampleList
+getJobDescription() String
+setJobBioFieldList(List<JobBioField>) void
+getSampleList() List<Sample>
+setNotes1(String) void
}
```

**Diagram sources**
- [Job.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Job.java#L10-L255)

**Section sources**
- [Job.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Job.java#L1-L257)

### 测试线实体 (TestLine)
测试线实体定义了具体的检测项目，是检测业务的核心单元。

**属性列表：**
- **generalOrderInstanceID**：通用订单实例ID，String类型
- **testLineInstanceID**：测试线实例ID，String类型
- **orderTestLineID**：订单测试线ID，String类型
- **testLineID**：测试线ID，Integer类型
- **standardList**：标准列表，List<Standard>类型
- **testItem**：测试项目，String类型
- **testLineVersionID**：测试线版本ID，Integer类型
- **PPVersionID**：协议包版本ID，Integer类型
- **PPName**：协议包名称，String类型
- **testLineSequence**：测试线序列，Integer类型
- **status**：状态，String类型
- **aid**：辅助ID，String类型
- **productLineAbbr**：产品线缩写，String类型
- **productLineName**：产品线名称，String类型
- **referenceNote**：参考备注，String类型
- **ppNotes**：协议包备注，String类型

**数据结构特点：**
- 包含复杂的关联关系（标准、法规、实验室部门等）
- 支持多语言特性
- 具有丰富的元数据属性
- 包含版本控制信息

```mermaid
classDiagram
class TestLine {
+String generalOrderInstanceID
+String testLineInstanceID
+String orderTestLineID
+Integer testLineID
+List<Standard> standardList
+String testItem
+Integer testLineVersionID
+Integer PPVersionID
+String PPName
+Integer testLineSequence
+String status
+String aid
+String productLineAbbr
+String productLineName
+String referenceNote
+String ppNotes
+List<TestLineOtherLanguageItem> otherLanguageItems
+List<PPOtherLanguageItems> ppOtherLanguageItems
+getGeneralOrderInstanceID() String
+setTestLineInstanceID(String) void
+getStandardList() List<Standard>
+setTestItem(String) void
+getStatus() String
+setAid(String) void
+getOtherLanguageItems() List<TestLineOtherLanguageItem>
+setPpOtherLanguageItems(List<PPOtherLanguageItems>) void
}
```

**Diagram sources**
- [TestLine.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/trims/TestLine.java#L17-L561)

**Section sources**
- [TestLine.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/trims/TestLine.java#L1-L563)

## 实体关系与ER图

### 实体间关系分析
系统中的核心实体通过以下关系相互连接：

- **订单与作业**：一对多关系，一个订单可包含多个作业
- **作业与样本**：一对多关系，一个作业可包含多个样本
- **样本与测试线**：多对多关系，一个样本可进行多个测试，一个测试可应用于多个样本
- **测试线与结论**：一对一关系，每个测试线对应一个结论
- **作业与报告**：一对一关系，作业完成后生成报告
- **订单与分包**：一对多关系，一个订单可有多个分包任务

### ER图
```mermaid
erDiagram
ORDER {
string generalOrderInstanceID PK
string orderNo
string customerCode
timestamp orderDate
string status
}
JOB {
string proJob PK
string CCLAS_LABCODE
string PROJ_CODE
string OWNERCODE
string PRODUCTCODE
string REPORTTPL
string status
}
SAMPLE {
string sampleident PK
string DESCRIPTION
string PRODUCTCODE
string COLOR
string MATERIAL
}
TESTLINE {
string testLineInstanceID PK
string generalOrderInstanceID FK
string orderTestLineID
integer testLineID
string testItem
integer testLineVersionID
string status
}
CONCLUSION {
string conclusionId PK
string testLineInstanceID FK
string conclusionText
string conclusionType
timestamp createTime
}
REPORT {
string reportId PK
string proJob FK
string reportNo
string status
timestamp generateTime
}
SUBCONTRACT {
string subcontractId PK
string generalOrderInstanceID FK
string subcontractorCode
string status
timestamp createTime
}
ORDER ||--o{ JOB : "包含"
JOB ||--o{ SAMPLE : "包含"
SAMPLE }o--o{ TESTLINE : "关联"
TESTLINE ||--|| CONCLUSION : "产生"
JOB ||--|| REPORT : "生成"
ORDER ||--o{ SUBCONTRACT : "分包"
ORDER ||--o{ TESTLINE : "包含"
```

**Diagram sources**
- [TableType.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TableType.java#L8-L30)
- [Sample.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Sample.java#L10-L221)
- [Job.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/po/xml/Job.java#L10-L255)
- [TestLine.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/trims/TestLine.java#L17-L561)

## 生命周期状态机

### 订单状态机
订单在其生命周期中经历多个状态：

```mermaid
stateDiagram-v2
[*] --> 草稿
草稿 --> 已提交 : 提交订单
已提交 --> 处理中 : 开始处理
处理中 --> 暂停 : 暂停处理
暂停 --> 处理中 : 恢复处理
处理中 --> 已完成 : 完成所有任务
处理中 --> 已取消 : 取消订单
已完成 --> 已归档 : 归档
已取消 --> 已归档 : 归档
```

### 报告状态机
报告的生成和发布过程：

```mermaid
stateDiagram-v2
[*] --> 创建
创建 --> 编辑中 : 开始编辑
编辑中 --> 审核中 : 提交审核
审核中 --> 编辑中 : 审核退回
审核中 --> 已批准 : 审核通过
已批准 --> 已发布 : 发布报告
已发布 --> 已撤销 : 撤销发布
已撤销 --> 编辑中 : 重新编辑
```

### 测试线状态机
测试线的执行状态流转：

```mermaid
stateDiagram-v2
[*] --> 待分配
待分配 --> 处理中 : 分配任务
处理中 --> 待审核 : 完成测试
待审核 --> 处理中 : 审核退回
待审核 --> 已完成 : 审核通过
处理中 --> 已取消 : 取消测试
已完成 --> 已归档 : 归档
```

**Section sources**
- [TestLine.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/trims/TestLine.java#L17-L561)

## 数据完整性与性能优化

### 数据完整性规则
系统实施了严格的数据完整性约束：

- **主键约束**：每个实体都有唯一的主键标识
- **外键约束**：确保实体间引用的完整性
- **非空约束**：关键业务字段不允许为空
- **唯一性约束**：确保业务关键字段的唯一性
- **检查约束**：验证数据的业务规则

### 索引策略
为提高查询性能，系统采用了以下索引策略：

- **主键索引**：所有主键字段自动创建索引
- **外键索引**：在所有外键字段上创建索引
- **常用查询字段索引**：在频繁查询的字段上创建索引
- **复合索引**：在多个字段组合查询的场景下创建复合索引

### 性能优化考虑
系统在设计时考虑了以下性能优化因素：

- **数据分片**：大表采用分片策略提高查询效率
- **缓存机制**：使用Redis缓存频繁访问的数据
- **异步处理**：耗时操作采用异步方式处理
- **批量操作**：支持批量数据导入导出
- **查询优化**：避免N+1查询问题，使用JOIN优化

**Section sources**
- [TableType.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TableType.java#L8-L30)