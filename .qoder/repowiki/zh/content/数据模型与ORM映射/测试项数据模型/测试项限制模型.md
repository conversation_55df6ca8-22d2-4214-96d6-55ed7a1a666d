# 测试项限制模型

<cite>
**本文档引用的文件**  
- [TestLineAnalyteLimitRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineAnalyteLimitRelInfoPO.java)
- [TestLineAnalyteLimitRelInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/TestLineAnalyteLimitRelInfoMapper.xml)
- [TestLineAnalyteLimitRelInfoMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/TestLineAnalyteLimitRelInfoMapper.java)
- [LimitType.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/LimitType.java)
- [LimitDataType.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/LimitDataType.java)
- [LimitGroupTypeInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/limitgroup/LimitGroupTypeInfo.java)
- [LimitGroupItemInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/limitgroup/LimitGroupItemInfo.java)
- [RequirmentService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/RequirmentService.java)
- [WorksheetTemplateService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/WorksheetTemplateService.java)
- [TrimsCacheType.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TrimsCacheType.java)
</cite>

## 目录
1. [引言](#引言)
2. [测试项分析物限制关联模型](#测试项分析物限制关联模型)
3. [TestLineAnalyteLimitRelInfo表结构设计](#testlineanalytelimitrelinfo表结构设计)
4. [限制类型与业务规则](#限制类型与业务规则)
5. [限制与单位、计算公式的关联](#限制与单位、计算公式的关联)
6. [数据验证与结论生成中的限制应用](#数据验证与结论生成中的限制应用)
7. [MyBatis映射文件中的限制查询实现](#mybatis映射文件中的限制查询实现)
8. [限制配置的业务规则与验证逻辑](#限制配置的业务规则与验证逻辑)
9. [实际应用场景](#实际应用场景)
10. [结论](#结论)

## 引言
本文档旨在全面阐述测试项限制数据模型的设计与实现，重点描述测试项分析物与限制之间的关联关系。通过深入分析`TestLineAnalyteLimitRelInfo`表的结构设计，解释限制在测试项中的作用机制、类型分类及应用规则。同时，文档化限制与单位、计算公式的关联方式，以及其在数据验证和结论生成过程中的关键作用。此外，提供MyBatis映射文件中限制相关查询的实现细节，包括多维度限制查询的优化策略，并说明限制配置的业务规则、验证逻辑和典型应用场景。

## 测试项分析物限制关联模型
测试项分析物限制关联模型用于建立测试项版本、分析物版本与限制实例之间的多对多关系。该模型支持在不同测试条件、样本类型和产品属性下灵活配置限制规则，确保测试结果的准确性和合规性。

```mermaid
erDiagram
TEST_LINE_VERSION {
INTEGER testLineVersionId PK
CHAR bizVersionId
INTEGER status
TIMESTAMP createdDate
TIMESTAMP modifiedDate
}
TAL_VERSION {
INTEGER talVersionId PK
INTEGER testLineVersionId FK
INTEGER analyteId
VARCHAR reportUnit
INTEGER status
}
LIMIT_INSTANCE {
BIGINT id PK
INTEGER limitGroupId
VARCHAR limitValue
VARCHAR reportDescription
INTEGER status
}
TEST_LINE_ANALYTE_LIMIT_REL {
BIGINT id PK
INTEGER testLineVersionId FK
INTEGER talVersionId FK
INTEGER talSeq
VARCHAR referenceMethod
CHAR bizVersionId
INTEGER status
TIMESTAMP createdDate
TIMESTAMP modifiedDate
}
TEST_LINE_VERSION ||--o{ TEST_LINE_ANALYTE_LIMIT_REL : "包含"
TAL_VERSION ||--o{ TEST_LINE_ANALYTE_LIMIT_REL : "关联"
LIMIT_INSTANCE }o--|| TEST_LINE_ANALYTE_LIMIT_REL : "引用"
```

**图示来源**  
- [TestLineAnalyteLimitRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineAnalyteLimitRelInfoPO.java#L1-L221)
- [TestLineAnalyteLimitRelInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/TestLineAnalyteLimitRelInfoMapper.xml#L1-L275)

**本节来源**  
- [TestLineAnalyteLimitRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineAnalyteLimitRelInfoPO.java#L1-L221)
- [TestLineAnalyteLimitRelInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/TestLineAnalyteLimitRelInfoMapper.xml#L1-L275)

## TestLineAnalyteLimitRelInfo表结构设计
`TestLineAnalyteLimitRelInfo`表是测试项限制模型的核心，用于存储测试项版本与分析物版本之间的限制关联信息。

### 字段定义
| 字段名 | 类型 | 是否必填 | 默认值 | 说明 |
|--------|------|----------|--------|------|
| Id | BIGINT(19) | 是 | - | 主键，唯一标识一条关联记录 |
| TestLineVersionId | INTEGER(10) | 是 | - | 测试项版本ID，外键关联测试项版本表 |
| TalVersionId | INTEGER(10) | 是 | - | 分析物版本ID，外键关联分析物版本表 |
| TalSeq | INTEGER(10) | 是 | 0 | 分析物序列号，用于排序 |
| ReferenceMethod | VARCHAR(512) | 否 | - | 参考方法描述 |
| BizVersionId | CHAR(32) | 是 | - | 业务版本ID，用于版本控制 |
| Status | INTEGER(10) | 是 | 0 | 状态：0-禁用（默认），1-启用 |
| CreatedDate | TIMESTAMP(19) | 是 | - | 创建时间 |
| ModifiedDate | TIMESTAMP(19) | 否 | - | 修改时间 |

### 约束说明
- **主键约束**：`Id`字段为主键，保证每条记录的唯一性。
- **外键约束**：`TestLineVersionId`和`TalVersionId`分别关联测试项版本表和分析物版本表。
- **状态约束**：`Status`字段采用枚举值，0表示禁用，1表示启用。
- **版本控制**：`BizVersionId`字段用于支持业务数据的版本管理。

**本节来源**  
- [TestLineAnalyteLimitRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineAnalyteLimitRelInfoPO.java#L1-L221)
- [TestLineAnalyteLimitRelInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/TestLineAnalyteLimitRelInfoMapper.xml#L1-L275)

## 限制类型与业务规则
系统支持多种限制类型，每种类型对应不同的应用场景和业务规则。

### 限制类型分类
根据`LimitType.java`枚举定义，系统支持以下限制类型：
- **Client（客户级）**：针对特定客户配置的限制规则
- **Global（全局级）**：适用于所有客户的通用限制规则

```mermaid
classDiagram
class LimitType {
+Client(0, "Client")
+Global(1, "Global")
+getType(key) LimitType
+getType() int
+getMessage() String
}
class LimitDataType {
+Data(0, "Data")
+Numeric(1, "Numeric")
+getType(key) LimitDataType
+getType() int
+getMessage() String
}
class LimitGroupTypeInfo {
+limitGroupTypeId int
+limitGroupTypeName String
+limitGroups LimitGroupItemInfo[]
+getLimitGroupTypeId() int
+setLimitGroupTypeId(int)
+getLimitGroupTypeName() String
+setLimitGroupTypeName(String)
+getLimitGroups() LimitGroupItemInfo[]
+setLimitGroups(LimitGroupItemInfo[])
}
class LimitGroupItemInfo {
+id Integer
+typeId Integer
+text String
+getId() Integer
+setId(Integer)
+getTypeId() Integer
+setTypeId(Integer)
+getText() String
+setText(String)
}
LimitType <|-- LimitGroupTypeInfo : "使用"
LimitDataType <|-- LimitGroupItemInfo : "使用"
```

**图示来源**  
- [LimitType.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/LimitType.java#L1-L43)
- [LimitDataType.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/LimitDataType.java#L1-L42)
- [LimitGroupTypeInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/limitgroup/LimitGroupTypeInfo.java#L1-L43)
- [LimitGroupItemInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/limitgroup/LimitGroupItemInfo.java#L1-L41)

### 业务规则
1. **优先级规则**：客户级限制优先于全局级限制
2. **版本继承**：新版本继承旧版本的限制配置，支持差异化调整
3. **状态控制**：禁用的限制不会参与数据验证和结论生成
4. **缓存机制**：通过`TrimsCacheType.TestLineAnalyteLimitRelInfo`实现限制数据的缓存管理

**本节来源**  
- [LimitType.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/LimitType.java#L1-L43)
- [TrimsCacheType.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TrimsCacheType.java#L29-L29)

## 限制与单位、计算公式的关联
限制配置与测试单位和计算公式紧密关联，确保限制值的准确性和可比性。

### 单位关联
每个分析物版本（TAL）都关联一个报告单位（Report Unit），限制值必须与该单位匹配。系统在数据验证时会自动进行单位一致性检查。

### 计算公式关联
对于需要计算的测试项，限制值可能基于计算公式的结果。系统支持：
- **直接值比较**：将测试结果与固定限制值比较
- **公式结果比较**：先计算测试结果，再与限制值比较
- **动态计算**：根据多个测试项的组合结果进行限制判断

```mermaid
flowchart TD
Start([开始]) --> CheckUnit["检查单位一致性"]
CheckUnit --> UnitValid{"单位匹配?"}
UnitValid --> |否| ReturnError["返回单位不匹配错误"]
UnitValid --> |是| CheckFormula["检查是否需要计算"]
CheckFormula --> NeedCalc{"需要计算?"}
NeedCalc --> |否| DirectCompare["直接值比较"]
NeedCalc --> |是| CalcResult["执行计算公式"]
CalcResult --> CompareWithLimit["与限制值比较"]
DirectCompare --> CompareWithLimit
CompareWithLimit --> GenerateConclusion["生成结论"]
GenerateConclusion --> End([结束])
ReturnError --> End
```

**图示来源**  
- [RequirmentService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/RequirmentService.java#L3097-L3153)
- [WorksheetTemplateService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/WorksheetTemplateService.java#L1784-L1808)

**本节来源**  
- [RequirmentService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/RequirmentService.java#L3097-L3153)
- [WorksheetTemplateService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/WorksheetTemplateService.java#L1784-L1808)

## 数据验证与结论生成中的限制应用
限制在数据验证和结论生成过程中发挥关键作用。

### 数据验证流程
1. **获取限制**：根据测试项版本、分析物版本和测试条件获取相关限制
2. **单位检查**：验证测试结果单位与限制单位的一致性
3. **值比较**：将测试结果与限制值进行比较
4. **异常标记**：标记超出限制范围的测试结果

### 结论生成规则
- **单值测试**：直接根据限制判断结果是否正常
- **矩阵测试**：综合多个限制条件生成结论
- **条件依赖**：根据测试条件（Parent/Child）应用不同的限制规则

```mermaid
sequenceDiagram
participant TestSystem as 测试系统
participant LimitService as 限制服务
participant Validation as 验证模块
participant Conclusion as 结论生成
TestSystem->>LimitService : 获取限制配置
LimitService-->>TestSystem : 返回限制列表
TestSystem->>Validation : 执行数据验证
Validation->>Validation : 检查单位一致性
Validation->>Validation : 比较测试值与限制
Validation-->>TestSystem : 返回验证结果
TestSystem->>Conclusion : 生成测试结论
Conclusion->>Conclusion : 应用结论规则
Conclusion-->>TestSystem : 返回最终结论
```

**图示来源**  
- [RequirmentService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/RequirmentService.java#L3097-L3153)
- [RequirmentService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/RequirmentService.java#L3437-L3451)

**本节来源**  
- [RequirmentService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/RequirmentService.java#L3097-L3153)
- [RequirmentService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/RequirmentService.java#L3437-L3451)

## MyBatis映射文件中的限制查询实现
MyBatis映射文件提供了灵活的限制查询功能。

### 核心查询方法
- **selectByExample**：支持条件查询，可用于多维度限制检索
- **selectByPrimaryKey**：根据主键精确查询
- **insertSelective**：选择性插入，支持部分字段更新
- **updateByExampleSelective**：基于条件的选择性更新

### 查询优化策略
1. **索引优化**：在`TestLineVersionId`、`TalVersionId`和`Status`字段上建立复合索引
2. **分页查询**：支持大数据量下的分页检索
3. **缓存集成**：结合Redis缓存减少数据库访问
4. **批量操作**：支持批量插入和更新，提高性能

```xml
<select id="selectByExample" resultMap="BaseResultMap" parameterType="TestLineAnalyteLimitRelInfoExample">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List"/>
    from tre_trims_testline_tal_relationship
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
</select>
```

**本节来源**  
- [TestLineAnalyteLimitRelInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/TestLineAnalyteLimitRelInfoMapper.xml#L1-L275)
- [TestLineAnalyteLimitRelInfoMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/TestLineAnalyteLimitRelInfoMapper.java)

## 限制配置的业务规则与验证逻辑
限制配置遵循严格的业务规则和验证逻辑。

### 配置规则
1. **必填字段验证**：确保关键字段（如版本ID、状态）不为空
2. **唯一性约束**：同一测试项版本和分析物版本的组合只能有一个有效关联
3. **状态一致性**：关联记录的状态必须与引用的版本状态一致
4. **版本兼容性**：确保关联的测试项版本和分析物版本属于同一业务版本

### 验证逻辑
- **前置验证**：在保存前验证所有必填字段和数据格式
- **冲突检测**：检查是否存在重复或冲突的限制配置
- **级联验证**：验证关联的测试项和分析物是否存在且有效
- **权限控制**：确保操作用户具有相应的配置权限

**本节来源**  
- [TestLineAnalyteLimitRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineAnalyteLimitRelInfoPO.java#L1-L221)
- [RequirmentService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/RequirmentService.java#L1608-L1630)

## 实际应用场景
### 场景一：客户特定限制配置
为特定客户配置专属的测试限制，如某客户要求所有重金属测试的限制值比标准值严格10%。

### 场景二：多条件限制应用
同一测试项在不同测试条件下应用不同的限制，如常温测试和高温测试采用不同的合格标准。

### 场景三：动态限制调整
根据产品属性动态调整限制值，如不同批次的产品可能有不同的质量要求。

### 场景四：限制继承与覆盖
新版本测试项继承旧版本的限制配置，同时允许对特定分析物进行差异化调整。

**本节来源**  
- [RequirmentService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/RequirmentService.java#L1608-L1630)
- [RequirmentService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/RequirmentService.java#L3130-L3153)

## 结论
本文档全面阐述了测试项限制数据模型的设计与实现。`TestLineAnalyteLimitRelInfo`表作为核心，有效管理了测试项版本、分析物版本与限制之间的复杂关系。系统通过灵活的限制类型、严格的业务规则和高效的查询实现，确保了测试数据的准确性和结论的可靠性。未来可进一步优化限制的动态计算能力和多维度查询性能。