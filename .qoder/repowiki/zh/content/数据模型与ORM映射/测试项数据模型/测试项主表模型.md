# 测试项主表模型

<cite>
**本文档引用文件**  
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java)
- [TestLine.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/trims/TestLine.java)
- [TestLineService.md](file://doc/api/TestLineService.md)
- [Constant.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/Constant.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心字段定义](#核心字段定义)
3. [分类字段与枚举值](#分类字段与枚举值)
4. [主键与索引设计](#主键与索引设计)
5. [外键关系](#外键关系)
6. [MyBatis CRUD操作](#mybatis-crud操作)
7. [状态流转与业务规则](#状态流转与业务规则)

## 引言
本文档详细描述了测试项主表（TestLine）的数据模型设计，涵盖核心字段、分类体系、索引策略、外键关联及MyBatis实现。该模型是测试管理系统的核心实体，支撑测试项的全生命周期管理。

**Section sources**
- [TestLineService.md](file://doc/api/TestLineService.md#L1-L10)

## 核心字段定义
测试项主表的核心字段定义如下：

| 字段名 | 数据类型 | 约束 | 说明 |
|-------|--------|------|------|
| id | VARCHAR(36) | 主键，非空 | 测试项实例唯一标识符 |
| test_line_code | VARCHAR(50) | 唯一索引，非空 | 测试项编码，遵循GPOTlBarcodeNo规则 |
| test_line_name | VARCHAR(200) | 非空 | 测试项名称 |
| status | VARCHAR(20) | 非空 | 状态标识（Active/Inactive等） |
| create_time | DATETIME | 非空 | 创建时间戳 |
| update_time | DATETIME | 非空 | 最后更新时间戳 |

**Section sources**
- [TestLine.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/trims/TestLine.java#L17-L561)
- [Constant.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/Constant.java#L77-L82)

## 分类字段与枚举值
### 分类字段业务含义
- **test_line_type**：测试项类型，区分常规测试、特殊测试等业务场景
- **test_line_module_type**：模块类型，标识测试项所属功能模块
- **test_line_pa_type**：PA类型，用于产品认证分类管理

### 枚举值定义
```java
public enum TestLineType {
    GENERAL(1, "常规测试"),
    SPECIAL(2, "特殊测试"),
    REGULATORY(3, "法规测试");
}
```

```java
public enum TestLineModuleType {
    PHYSICAL(1, "物理性能"),
    CHEMICAL(2, "化学分析"),
    RELIABILITY(3, "可靠性测试");
}
```

```java
public enum TestLinePaType {
    A(1, "A类"),
    B(2, "B类"),
    C(3, "C类");
}
```

**Section sources**
- [TestLine.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/trims/TestLine.java#L17-L561)

## 主键与索引设计
### 主键生成策略
采用UUID生成全局唯一标识符，确保分布式环境下的主键唯一性。

### 唯一索引设计
- **UK_test_line_code**：确保test_line_code字段的唯一性
- **UK_test_line_instance**：复合唯一索引，包含test_line_id和version_id

### 查询优化方案
```sql
-- 针对状态查询的复合索引
CREATE INDEX IDX_status_create_time ON tb_test_line_instance(status, create_time);

-- 针对订单关联查询的索引
CREATE INDEX IDX_order_id_status ON tb_test_line_instance(order_id, status);

-- 针对分页查询的覆盖索引
CREATE INDEX IDX_create_time_id ON tb_test_line_instance(create_time, id) 
INCLUDE (test_line_code, test_line_name, status);
```

**Section sources**
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java#L0-L577)

## 外键关系
测试项主表与其他实体的外键关系如下：

```mermaid
erDiagram
tb_test_line_instance ||--|| tb_pp : "关联协议包"
tb_test_line_instance ||--|| tb_lab_section : "归属实验室分区"
tb_test_line_instance ||--|| tb_equipment_type : "使用设备类型"
tb_test_line_instance ||--|| tb_test_method : "引用测试方法"
tb_test_line_instance ||--o{ tb_test_result : "产生测试结果"
```

**Diagram sources**
- [TestLine.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/trims/TestLine.java#L17-L561)
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java#L0-L577)

## MyBatis CRUD操作
### CRUD操作实现
```java
public interface TestLineMapper {
    /**
     * 根据ID获取测试项
     */
    TestLineInstancePO getTestLineInstanceById(String testLineInstanceId);
    
    /**
     * 批量插入测试项
     */
    int batchInsert(@Param("testLines") List<TestLineInstancePO> testLines);
    
    /**
     * 分页查询测试项
     */
    List<QueryTestLineByOrderNoRsp> queryTestLineByOrderNo(@Param("orderNo") String orderNo);
    
    /**
     * 条件过滤查询
     */
    List<TestLineInstancePO> getTestLineByOrderIdAndStatus(TestLineStatusReq testLineStatusReq);
}
```

### 性能优化技巧
1. **批量操作**：使用`batchInsert`和`batchUpdate`减少数据库交互次数
2. **延迟加载**：对关联对象采用延迟加载策略
3. **缓存机制**：结合Redis缓存频繁查询结果
4. **分页优化**：使用游标分页替代OFFSET分页

**Section sources**
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java#L0-L577)

## 状态流转与业务规则
### 状态流转机制
```mermaid
stateDiagram-v2
[*] --> Created : 创建
Created --> Active : 激活
Active --> Inactive : 停用
Inactive --> Active : 重新激活
Active --> Deleted : 删除
Inactive --> Deleted : 删除
```

**Diagram sources**
- [TestLineService.md](file://doc/api/TestLineService.md#L100-L110)

### 业务规则验证逻辑
1. **创建规则**：
   - 测试代码必须唯一
   - 关联的PP协议包必须存在且有效
   - 实验室分区必须处于激活状态

2. **更新规则**：
   - 已完成的测试项不允许修改核心参数
   - 状态变更需记录操作日志
   - 并发修改采用乐观锁控制

3. **删除规则**：
   - 存在关联测试结果的测试项禁止删除
   - 软删除标记替代物理删除

**Section sources**
- [TestLineService.md](file://doc/api/TestLineService.md#L50-L90)
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java#L0-L577)