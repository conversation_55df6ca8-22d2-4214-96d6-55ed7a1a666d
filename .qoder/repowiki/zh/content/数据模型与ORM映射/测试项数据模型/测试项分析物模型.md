# 测试项分析物模型

<cite>
**本文档引用文件**  
- [TestLineAnalyteRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineAnalyteRelInfoPO.java)
- [TestLineAnalyteRelInfoMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/TestLineAnalyteRelInfoMapper.java)
- [TestLineAnalyteRelInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/TestLineAnalyteRelInfoMapper.xml)
- [TestAnalyteRelMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/TestAnalyteRelMapper.xml)
- [TestLineAnalyteRelInfoTemp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/tal/TestLineAnalyteRelInfoTemp.java)
- [TestLineAnalyteRelInfoReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/tal/TestLineAnalyteRelInfoReq.java)
- [TestLineSyncService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/trims/TestLineSyncService.java)
</cite>

## 目录
1. [引言](#引言)
2. [数据模型设计](#数据模型设计)
3. [字段定义与约束](#字段定义与约束)
4. [分析物关联关系与排序规则](#分析物关联关系与排序规则)
5. [分析物配置业务规则](#分析物配置业务规则)
6. [MyBatis映射与查询实现](#mybatis映射与查询实现)
7. [数据录入与报告展示应用](#数据录入与报告展示应用)
8. [依赖关系分析](#依赖关系分析)

## 引言
本文档详细描述了测试项与分析物之间的关联数据模型，重点围绕`TestLineAnalyteRelInfo`表的结构设计、字段含义、业务规则及其实现机制。该模型用于管理测试项中包含的分析物信息，包括其顺序、选择类型、单位、结果录入权限等关键属性，是实验室信息系统中测试配置的核心组成部分。

## 数据模型设计
测试项分析物关联模型通过`TestLineAnalyteRelInfoPO`实体类与数据库表`tb_trims_testline_analyte_relationship`进行映射。该模型建立了测试项版本（`TestLineVersionId`）与分析物（`TestAnalyteId`）之间的多对多关系，并通过`TestAnalyteSeq`字段定义分析物在测试项中的显示顺序。

```mermaid
erDiagram
TEST_LINE_VERSION ||--o{ TEST_LINE_ANALYTE_REL : "包含"
ANALYTE ||--o{ TEST_LINE_ANALYTE_REL : "被包含"
UNIT ||--o{ TEST_LINE_ANALYTE_REL : "关联单位"
TEST_LINE_ANALYTE_REL {
BIGINT id PK
INTEGER testLineVersionId FK
INTEGER testAnalyteId FK
INTEGER unitId FK
INTEGER testAnalyteSeq
INTEGER selectionType
VARCHAR descriptionAlias
STRING resultEntry
CHAR bizVersionId
INTEGER status
TIMESTAMP createdDate
TIMESTAMP modifiedDate
}
TEST_LINE_VERSION {
INTEGER testLineVersionId PK
string testLineName
}
ANALYTE {
INTEGER testAnalyteId PK
string analyteName
}
UNIT {
INTEGER unitId PK
string unitName
}
```

**图示来源**  
- [TestLineAnalyteRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineAnalyteRelInfoPO.java)
- [TestAnalyteRelMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/TestAnalyteRelMapper.xml)

## 字段定义与约束
`TestLineAnalyteRelInfo`表的核心字段定义如下：

| 字段名 | 数据类型 | 必填 | 默认值 | 说明 |
| :--- | :--- | :--- | :--- | :--- |
| `id` | BIGINT(19) | 是 | - | 主键，唯一标识一条关联记录 |
| `testLineVersionId` | INTEGER(10) | 是 | 0 | 测试项版本ID，外键关联测试项 |
| `testAnalyteId` | INTEGER(10) | 是 | - | 分析物ID，外键关联分析物基础信息 |
| `unitId` | INTEGER(10) | 否 | - | 单位ID，外键关联单位信息表 |
| `testAnalyteSeq` | INTEGER(10) | 是 | 0 | 分析物在测试项中的顺序号，用于排序 |
| `selectionType` | INTEGER(10) | 是 | 0 | 选择类型：0-无、1-必选、2-预选 |
| `descriptionAlias` | VARCHAR(2048) | 否 | - | 分析物描述别名，用于报告展示 |
| `resultEntry` | STRING | 否 | - | 结果录入权限：'0'-不允许修改、'1'-允许修改 |
| `bizVersionId` | CHAR(32) | 是 | - | 业务版本ID，用于数据同步和幂等控制 |
| `status` | INTEGER(10) | 是 | 0 | 状态：0-禁用、1-启用 |
| `createdDate` | TIMESTAMP(19) | 是 | - | 创建时间 |
| `modifiedDate` | TIMESTAMP(19) | 否 | - | 修改时间 |

**字段来源**  
- [TestLineAnalyteRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineAnalyteRelInfoPO.java)

## 分析物关联关系与排序规则
分析物在测试项中的作用是定义该测试项所包含的具体检测项目。其核心逻辑如下：

1.  **关联关系**：一个测试项可以包含多个分析物，一个分析物也可以被多个测试项使用，通过`testLineVersionId`和`testAnalyteId`建立关联。
2.  **排序规则**：通过`testAnalyteSeq`字段的数值大小来确定分析物在测试项中的显示顺序，数值越小，排序越靠前。该字段在数据录入和报告生成时被用来组织分析物的展示顺序。
3.  **显示逻辑**：系统在加载测试项详情时，会根据`testLineVersionId`查询所有状态为启用（`status=1`）的关联分析物，并按照`testAnalyteSeq`升序排列，形成最终的分析物列表。

```mermaid
flowchart TD
Start([开始加载测试项]) --> Query["根据 testLineVersionId 查询<br/>tb_trims_testline_analyte_relationship"]
Query --> Filter["筛选 status = 1 的记录"]
Filter --> Sort["按 testAnalyteSeq 升序排序"]
Sort --> Display["在界面/报告中展示分析物列表"]
Display --> End([结束])
```

**图示来源**  
- [TestAnalyteRelMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/TestAnalyteRelMapper.xml)
- [TestLineAnalyteRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineAnalyteRelInfoPO.java)

## 分析物配置业务规则
分析物的配置遵循以下业务规则和验证逻辑：

1.  **幂等性处理**：通过`bizVersionId`字段（通常为MD5哈希值）来确保数据同步的幂等性。在`TestLineSyncService.refreshTestlineAnalyteRelInfo`方法中，会为每条记录生成唯一的`bizVersionId`，用于判断数据是否已存在或需要更新。
2.  **状态管理**：`status`字段控制关联关系的有效性。当测试项被停用时，其关联的分析物关系也会被批量禁用（`status=0`），如`resetTestlineAnalyteRelStatus` SQL语句所示。
3.  **数据完整性**：`testLineVersionId`、`testAnalyteId`、`testAnalyteSeq`等关键字段均为必填，确保了关联关系的完整性。
4.  **批量操作**：支持通过`batchInsert`操作批量插入或更新分析物关联信息，利用`ON DUPLICATE KEY UPDATE`语句实现“存在则更新，不存在则插入”的逻辑，提高数据同步效率。

**业务规则来源**  
- [TestLineSyncService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/trims/TestLineSyncService.java#L2320-L2350)
- [TestAnalyteRelMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/TestAnalyteRelMapper.xml#L50-L65)

## MyBatis映射与查询实现
MyBatis框架通过Mapper接口和XML配置文件实现了对`TestLineAnalyteRelInfo`表的CRUD操作。

### 核心Mapper接口
`TestLineAnalyteRelInfoMapper`接口定义了标准的增删改查方法，如`insertSelective`、`updateByPrimaryKeySelective`、`selectByExample`等。

```mermaid
classDiagram
class TestLineAnalyteRelInfoMapper {
+int insertSelective(TestLineAnalyteRelInfoPO record)
+int updateByPrimaryKeySelective(TestLineAnalyteRelInfoPO record)
+TestLineAnalyteRelInfoPO selectByPrimaryKey(Long id)
+List<TestLineAnalyteRelInfoPO> selectByExample(TestLineAnalyteRelInfoExample example)
+int deleteByExample(TestLineAnalyteRelInfoExample example)
}
class TestLineAnalyteRelInfoPO {
+Long id
+Integer testLineVersionId
+Integer testAnalyteId
+Integer unitId
+Integer testAnalyteSeq
+Integer selectionType
+String descriptionAlias
+String resultEntry
+String bizVersionId
+Integer status
+Date createdDate
+Date modifiedDate
}
class TestLineAnalyteRelInfoExample {
+List<Criteria> oredCriteria
+String orderByClause
}
TestLineAnalyteRelInfoMapper --> TestLineAnalyteRelInfoPO : "使用"
TestLineAnalyteRelInfoMapper --> TestLineAnalyteRelInfoExample : "使用"
```

**图示来源**  
- [TestLineAnalyteRelInfoMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/TestLineAnalyteRelInfoMapper.java)
- [TestLineAnalyteRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineAnalyteRelInfoPO.java)

### 多表关联查询优化
在`TestAnalyteRelMapper.xml`中定义了优化的多表关联查询：
- **`getAnalytesByTestLineVersionId`**：通过`JOIN`操作，一次性查询出测试项版本ID对应的所有启用的分析物基本信息（如`TestAnalyteDesc`），避免了N+1查询问题。
- **批量操作**：`batchInsert`和`batchInsertTestLineAnalyteLangInfo`方法支持批量处理，显著提升了数据同步性能。

```sql
<select id="getAnalytesByTestLineVersionId" resultType="AnalyteSelectRsp">
    SELECT
        base.id as analyteBaseId,
        base.TestAnalyteId,
        base.TestAnalyteDesc,
        ship.SelectionType
    FROM
        tb_trims_testline_analyte_relationship ship
    JOIN tb_trims_analyte_baseinfo base ON ship.TestAnalyteId = base.TestAnalyteId AND base.`Status` = 1
    WHERE
        ship.`Status` = 1
    AND ship.TestLineVersionId=#{testLineVersionID}
</select>
```

**查询实现来源**  
- [TestAnalyteRelMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/TestAnalyteRelMapper.xml)

## 数据录入与报告展示应用
在测试流程中，该模型的应用场景如下：

1.  **数据录入**：在数据录入界面，系统根据`testAnalyteSeq`顺序展示分析物列表。`resultEntry`字段决定了操作员是否可以修改该分析物的检测结果。
2.  **报告展示**：在生成报告时，系统同样依据`testAnalyteSeq`对分析物进行排序，并使用`descriptionAlias`作为分析物的显示名称，确保报告的可读性和一致性。
3.  **配置管理**：管理员在配置测试项时，通过`TestLineAnalyteRelInfoReq`和`TestLineAnalyteRelInfoTemp`等DTO对象，批量管理分析物的关联、排序和属性设置。

**应用来源**  
- [TestLineAnalyteRelInfoReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/tal/TestLineAnalyteRelInfoReq.java)
- [TestLineAnalyteRelInfoTemp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/tal/TestLineAnalyteRelInfoTemp.java)

## 依赖关系分析
该数据模型与系统中的多个模块紧密耦合。

```mermaid
graph TD
TestLineAnalyteRelInfoPO --> TestLineSyncService[TestLineSyncService]
TestLineAnalyteRelInfoPO --> TestAnalyteRelMapper[TestAnalyteRelMapper]
TestLineAnalyteRelInfoPO --> TestLineAnalyteRelInfoMapper[TestLineAnalyteRelInfoMapper]
TestLineAnalyteRelInfoReq --> TestLineSyncService
TestLineAnalyteRelInfoTemp --> TestLineAnalyteRelInfoReq
TestLineSyncService --> TestLineAnalyteRelMapper
TestAnalyteRelMapper --> tb_trims_testline_analyte_relationship[(数据库表)]
TestLineAnalyteRelInfoMapper --> tb_trims_testline_analyte_relationship
style TestLineAnalyteRelInfoPO fill:#f9f,stroke:#333
style TestLineAnalyteRelInfoReq fill:#f9f,stroke:#333
style TestLineAnalyteRelInfoTemp fill:#f9f,stroke:#333
```

**图示来源**  
- [TestLineAnalyteRelInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineAnalyteRelInfoPO.java)
- [TestLineAnalyteRelInfoReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/tal/TestLineAnalyteRelInfoReq.java)
- [TestLineSyncService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/trims/TestLineSyncService.java)
- [TestAnalyteRelMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/TestAnalyteRelMapper.xml)
- [TestLineAnalyteRelInfoMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/TestLineAnalyteRelInfoMapper.java)