# 数据模型与ORM映射

<cite>
**本文档引用的文件**  
- [GeneralOrderInstanceInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/GeneralOrderInstanceInfoPO.java)
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java)
- [SubReportPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubReportPO.java)
- [PPSampleRelationshipInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/PPSampleRelationshipInfoPO.java)
- [TestLineInstancePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineInstancePO.java)
- [CustomerConclusionMultipleLanguagePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CustomerConclusionMultipleLanguagePO.java)
- [mybatis-settings.xml](file://otsnotes-dbstorages/src/main/resources/spring/mybatis-settings.xml)
- [GeneralOrderInstanceInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/GeneralOrderInstanceInfoMapper.xml)
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml) - *在最近的提交中更新*
- [AccreditationAnalyteRelationshipInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/AccreditationAnalyteRelationshipInfoMapper.xml) - *与TRIMS配置同步*
- [AccreditationBaseInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/AccreditationBaseInfoMapper.xml) - *与TRIMS配置同步*
- [TestLineInstanceInfo.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmodel/worksheet/TestLineInstanceInfo.java) - *重构数据条目服务和比较逻辑*
</cite>

## 更新摘要
**变更内容**   
- 修正了`DataEntryMapper.xml`中关于`LabSectionCode`的模糊查询条件，从`tl.LabSectionCode`更正为`lab.LabSectionCode`。
- 更新了与TRIMS集成相关的自动生成的Mapper文件，包括`AccreditationAnalyteRelationshipInfoMapper.xml`和`AccreditationBaseInfoMapper.xml`。
- 在`TestLineInstanceInfo`扩展模型中新增了`labSectionCode`、`labSectionName`和`labSectionSeq`等属性，以支持更完整的测试项信息展示。
- 相应地更新了文档中涉及的查询逻辑描述、实体模型分析和文件引用。

## 目录
1. [引言](#引言)
2. [核心实体模型分析](#核心实体模型分析)
3. [实体关系与ER图](#实体关系与er图)
4. [MyBatis ORM映射实现](#mybatis-orm映射实现)
5. [数据访问模式与性能优化](#数据访问模式与性能优化)
6. [结论](#结论)

## 引言
本文档旨在全面解析otsnotes-service服务的核心数据模型与ORM映射机制。系统围绕订单（Order）、分包（SubContract）、报告（Report）、样本（Sample）、测试项（TestLine）和结论（Conclusion）等核心业务实体构建，采用MyBatis作为持久层框架，实现了Java对象与数据库表之间的映射。文档将深入剖析这些实体的属性、关系、约束以及MyBatis的配置与使用方式，为开发者提供清晰的架构视图和开发指导。

## 核心实体模型分析

### 订单 (Order)
订单实体是整个业务流程的核心，代表一个完整的检测服务请求。其数据模型由`GeneralOrderInstanceInfoPO`类定义。

**属性与约束**
- **ID**: `String`类型，主键，长度36，必填。
- **OrderNo**: `String`类型，订单编号，长度50，必填。
- **OrderStatus**: `Integer`类型，订单状态码。
- **CustomerCode/Name**: `String`类型，客户编码与名称，用于标识服务对象。
- **LabCode**: `String`类型，实验室编码，标识处理订单的实验室。
- **CreatedDate/ModifiedBy**: `Date/String`类型，记录创建和修改的时间与操作者。
- **ConclusionMode**: `Integer`类型，默认值0，定义结论生成方式（按矩阵或PP矩阵）。
- **ActiveIndicator**: `Boolean`类型，默认值1，表示订单是否有效（1为有效）。

该实体遵循典型的审计字段模式（创建、修改时间/人），并包含丰富的业务属性，如客户信息、实验室归属和结论模式。

**Section sources**
- [GeneralOrderInstanceInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/GeneralOrderInstanceInfoPO.java)

### 分包 (SubContract)
分包实体代表将订单中的部分或全部测试项委托给外部实验室（或内部特定部门）执行的业务。其数据模型由`SubContractPO`类定义。

**属性与约束**
- **ID**: `String`类型，主键，长度36，必填。
- **OrderNo**: `String`类型，关联的主订单编号，长度30，必填。
- **SubContractNo**: `String`类型，分包单号，长度50。
- **SubContractLabCode/Name**: `String`类型，分包实验室的编码和名称。
- **Status**: `Integer`类型，分包状态：0-新建，1-测试中，2-完成，3-取消。
- **SyncStatus**: `Integer`类型，同步状态，用于管理与外部系统（如HK）的数据同步流程。
- **StartDate/CompleteDate**: `Date`类型，记录分包的开始和完成时间。
- **SubContractOrder**: `Integer`类型，默认值0，定义分包类型（普通、内部分包、To Slim、To Starlims）。
- **ExtData**: `String`类型，扩展字段，以JSON格式存储非核心业务数据，提供良好的扩展性。

此实体不仅管理分包的基本信息，还通过状态机（Status, SyncStatus）精确跟踪分包的生命周期，并通过`ExtData`字段支持灵活的业务扩展。

**Section sources**
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java)

### 报告 (Report)
报告实体代表一次检测服务的最终输出成果，通常与分包或SlimJob关联。其数据模型由`SubReportPO`类定义。

**属性与约束**
- **ID**: `String`类型，主键，长度36，必填。
- **GeneralOrderInstanceID**: `String`类型，外键，关联到`GeneralOrderInstanceInfoPO`的ID。
- **ObjectType/ObjectNo**: `String`类型，组合字段，标识报告的来源类型（如subcontract）和具体编号（如分包单号）。
- **CloudID/Filename**: `String`类型，存储报告文件在阿里云上的ID和文件名。
- **LanguageCode/Id**: `String/Integer`类型，定义报告的语言（如ENG/CHI）。
- **Status**: `String`类型，报告状态。
- **ReportSource**: `String`类型，报告来源系统（如StarLIMS）。
- **ReportVersion**: `Integer`类型，报告版本号，支持报告的迭代更新。
- **IsLastReport**: `Integer`类型，标记是否为最终报告。
- **testMatrixMergeMode**: `String`类型，定义测试矩阵的合并模式（NA, Host, Light, Execute）。

该实体设计用于管理电子报告的元数据，支持多语言、多版本和多种来源，并通过`ObjectType/ObjectNo`的组合实现了对不同业务场景的统一建模。

**Section sources**
- [SubReportPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubReportPO.java)

### 样本 (Sample)
样本实体代表检测所用的具体物理样本。其数据模型由`PPSampleRelationshipInfoPO`类定义。

**属性与约束**
- **ID**: `String`类型，主键，长度36，必填。
- **PpInstanceID**: `String`类型，外键，关联到PP实例（Protocol Package）。
- **TestSampleID**: `String`类型，测试样本ID，长度50。
- **MatrixID**: `String`类型，所属矩阵ID。
- **SampleType**: `String`类型，样本类型。
- **SampleCategory**: `String`类型，样本类别。
- **SampleQuantity**: `Integer`类型，样本数量。
- **SampleUnit**: `String`类型，样本单位。
- **CreatedDate/ModifiedBy**: `Date/String`类型，标准审计字段。

此实体建立了样本与PP实例、测试项矩阵之间的关系，是连接订单、测试项和实际检测对象的关键纽带。

**Section sources**
- [PPSampleRelationshipInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/PPSampleRelationshipInfoPO.java)

### 测试项 (TestLine)
测试项实体代表订单中需要执行的具体检测项目。其数据模型由`TestLineInstancePO`类定义。

**属性与约束**
- **ID**: `String`类型，主键，长度36，必填。
- **PpInstanceID**: `String`类型，外键，关联到PP实例。
- **TestLineId**: `Integer`类型，测试项的ID。
- **TestLineInstanceId**: `String`类型，测试项实例ID。
- **Status**: `Integer`类型，测试项状态（如待分配、测试中、已完成）。
- **TAT**: `Integer`类型，测试周期（Turnaround Time）。
- **Priority**: `Integer`类型，优先级。
- **LabSection**: `String`类型，所属实验室部门。
- **CreatedDate/ModifiedBy**: `Date/String`类型，标准审计字段。

`TestLineInstancePO`是`TestLine`在具体订单实例中的体现，包含了执行状态、周期和优先级等运行时信息。

**Section sources**
- [TestLineInstancePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineInstancePO.java)

### 测试项实例扩展信息 (TestLineInstanceInfo)
为了支持更复杂的数据条目和工作表生成功能，系统定义了`TestLineInstanceInfo`扩展模型。该模型继承自`AbstractMutableEvaluationAlias`，并整合了来自多个表的字段。

**属性与约束**
- **ID**: `String`类型，主键，对应`TestLineInstancePO`的ID。
- **testLineID/testLineEvaluation**: `Integer/String`类型，测试项ID和评估名称。
- **testLineStatus**: `Integer`类型，测试项状态。
- **labSectionCode/labSectionName/labSectionSeq**: `String/String/Integer`类型，新增的实验室部门代码、名称和序列，用于精确标识测试项所属的实验室部门。
- **jobId**: `String`类型，关联的作业编号。
- **testLineBaseId/citationBaseId/labSectionBaseId**: `Long/Long/Long`类型，新增的长整型基础ID，用于支持表拆分迁移。
- **templateList**: `List<WorksheetTemplateConfigInfoPO>`类型，关联的工作表模板列表。

此扩展模型通过整合`tb_test_line_instance`、`tb_trims_testline_baseinfo`、`tb_trims_labsection_baseinfo`等多个表的数据，为前端提供了更丰富、更完整的测试项信息。

**Section sources**
- [TestLineInstanceInfo.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmodel/worksheet/TestLineInstanceInfo.java)

### 结论 (Conclusion)
结论实体代表对测试结果的最终判定或总结。其数据模型由`CustomerConclusionMultipleLanguagePO`类定义。

**属性与约束**
- **ID**: `String`类型，主键，长度36，必填。
- **ConclusionId**: `String`类型，结论ID，长度50。
- **LanguageCode**: `String`类型，语言编码（如EN, ZH）。
- **ConclusionText**: `String`类型，结论文本内容，长度2000。
- **ActiveIndicator**: `Integer`类型，默认值1，标识结论是否有效。
- **CreatedDate/ModifiedBy**: `Date/String`类型，标准审计字段。

该实体通过`LanguageCode`字段支持多语言结论的存储，满足国际化报告的需求。

**Section sources**
- [CustomerConclusionMultipleLanguagePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CustomerConclusionMultipleLanguagePO.java)

## 实体关系与ER图

```mermaid
erDiagram
GeneralOrderInstanceInfoPO {
string ID PK
string OrderNo UK
integer OrderStatus
string CustomerCode
string CustomerName
string LabCode
date CreatedDate
string CreatedBy
boolean ActiveIndicator
}
SubContractPO {
string ID PK
string OrderNo FK
string SubContractNo UK
string SubContractLabCode
integer Status
integer SubContractOrder
date StartDate
date CompleteDate
string ExtData
}
SubReportPO {
string ID PK
string GeneralOrderInstanceID FK
string ObjectType
string ObjectNo
string Filename
string LanguageCode
integer ReportVersion
integer IsLastReport
}
PPSampleRelationshipInfoPO {
string ID PK
string PpInstanceID FK
string TestSampleID
string MatrixID
string SampleType
string SampleCategory
integer SampleQuantity
}
TestLineInstancePO {
string ID PK
string PpInstanceID FK
integer TestLineId
string TestLineInstanceId
integer Status
integer TAT
string LabSection
}
CustomerConclusionMultipleLanguagePO {
string ID PK
string ConclusionId
string LanguageCode
string ConclusionText
integer ActiveIndicator
}
GeneralOrderInstanceInfoPO ||--o{ SubContractPO : "1个订单可有多个分包"
GeneralOrderInstanceInfoPO ||--o{ SubReportPO : "1个订单可有多个报告"
GeneralOrderInstanceInfoPO ||--o{ TestLineInstancePO : "1个订单包含多个测试项"
TestLineInstancePO ||--o{ PPSampleRelationshipInfoPO : "1个测试项可关联多个样本"
SubContractPO ||--o{ SubReportPO : "1个分包可产生多个报告"
GeneralOrderInstanceInfoPO ||--o{ CustomerConclusionMultipleLanguagePO : "1个订单可有多个结论"
```

**Diagram sources**
- [GeneralOrderInstanceInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/GeneralOrderInstanceInfoPO.java)
- [SubContractPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractPO.java)
- [SubReportPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubReportPO.java)
- [PPSampleRelationshipInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/PPSampleRelationshipInfoPO.java)
- [TestLineInstancePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/TestLineInstancePO.java)
- [CustomerConclusionMultipleLanguagePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/CustomerConclusionMultipleLanguagePO.java)

## MyBatis ORM映射实现

### XML映射配置
系统采用MyBatis的XML配置方式来定义SQL语句和结果映射。核心配置文件位于`otsnotes-dbstorages/src/main/resources/spring/mybatis-settings.xml`，其中配置了数据源、事务管理器和Mapper扫描路径。

每个实体都有对应的Mapper XML文件，例如`GeneralOrderInstanceInfoMapper.xml`，其结构如下：
```xml
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.GeneralOrderInstanceInfoMapper">
    <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.GeneralOrderInstanceInfoPO">
        <id column="ID" property="ID" jdbcType="VARCHAR"/>
        <result column="OrderNo" property="orderNo" jdbcType="VARCHAR"/>
        <!-- 其他字段映射 -->
    </resultMap>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        SELECT * FROM GeneralOrderInstanceInfo WHERE ID = #{ID,jdbcType=VARCHAR}
    </select>

    <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.GeneralOrderInstanceInfoPO">
        INSERT INTO GeneralOrderInstanceInfo (...) VALUES (...)
    </insert>
    <!-- update, delete 等操作 -->
</mapper>
```
`resultMap`元素定义了数据库列（column）与Java对象属性（property）之间的精确映射，`select`, `insert`, `update`, `delete`等元素则封装了具体的SQL操作。

**Section sources**
- [mybatis-settings.xml](file://otsnotes-dbstorages/src/main/resources/spring/mybatis-settings.xml)
- [GeneralOrderInstanceInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/GeneralOrderInstanceInfoMapper.xml)

### 注解使用
在本项目中，MyBatis的SQL映射主要依赖XML文件，而Java实体类（PO）本身不使用MyBatis注解（如`@Select`, `@Insert`）。这种纯XML的方式提供了更高的灵活性，便于管理复杂的SQL语句和动态SQL。

### 实现方式总结
1.  **分层清晰**：DAO层接口（Mapper）与SQL实现（XML）分离，便于维护。
2.  **强类型映射**：通过`resultMap`确保类型安全，减少运行时错误。
3.  **动态SQL支持**：XML中可使用`<if>`, `<choose>`, `<foreach>`等标签构建动态查询。
4.  **代码生成**：项目可能使用了MyBatis Generator工具，根据数据库表自动生成PO类和Mapper接口/文件，保证了代码的一致性和开发效率。

## 数据访问模式与性能优化

### 数据访问模式
系统遵循典型的分层架构，数据访问模式如下：
1.  **Service层**：`domain/service`包下的服务类（如`OrderService`, `SubContractService`）是业务逻辑的入口。
2.  **DAO层**：Service层通过依赖注入获取Mapper接口（如`orderMapper`, `subContractMapper`），调用其方法进行数据库操作。
3.  **Mapper层**：Mapper接口由MyBatis动态代理实现，执行XML中定义的SQL。

这种模式实现了业务逻辑与数据访问的解耦。

### 性能考虑与查询优化
1.  **主键查询**：对`ID`和`OrderNo`等字段建立了主键或唯一索引，确保`selectByPrimaryKey`等查询的高效性。
2.  **关联查询优化**：对于复杂的关联查询（如获取订单及其所有分包、报告），应避免在Mapper中使用`N+1`查询。推荐在Service层通过多次查询或在Mapper中编写高效的JOIN SQL来获取数据。
3.  **分页查询**：对于列表查询，使用`LIMIT`和`OFFSET`（或MyBatis的`RowBounds`）进行分页，防止一次性加载过多数据。
4.  **缓存策略**：虽然文档未直接体现，但结合项目中的`cache`包（如`RedisHelper`），系统很可能在Service层或应用层实现了Redis缓存，用于缓存热点数据（如配置信息、频繁查询的订单状态），以减轻数据库压力。
5.  **批量操作**：对于需要插入或更新大量数据的场景（如批量导入样本），应使用MyBatis的`<foreach>`标签或`ExecutorType.BATCH`模式进行批量处理，显著提升性能。
6.  **查询条件修正**：已修正`DataEntryMapper.xml`中的模糊查询条件，确保`labSectionCode`参数能正确匹配`tb_trims_labsection_baseinfo`表的`LabSectionCode`字段，避免因表别名错误导致的查询失败。
7.  **扩展模型优化**：`TestLineInstanceInfo`等扩展模型通过`resultMap`的`collection`和`association`标签，实现了对复杂对象图的懒加载和关联查询，提高了数据访问的灵活性。

**Section sources**
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml) - *在最近的提交中更新*
- [AccreditationAnalyteRelationshipInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/AccreditationAnalyteRelationshipInfoMapper.xml) - *与TRIMS配置同步*
- [AccreditationBaseInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/AccreditationBaseInfoMapper.xml) - *与TRIMS配置同步*

## 结论
otsnotes-service的数据模型设计围绕核心业务实体构建，结构清晰，关系明确。通过MyBatis框架，系统实现了Java对象与数据库表的高效映射。实体设计充分考虑了业务需求，如分包的状态机、报告的多版本和多语言支持、以及通过`ExtData`字段实现的扩展性。ORM实现上，采用XML配置方式，保证了SQL的灵活性和可维护性。在性能方面，应重点关注关联查询的优化、分页处理和合理利用缓存，以确保系统在高并发场景下的稳定性和响应速度。近期对`DataEntryMapper.xml`等文件的修正，以及`TestLineInstanceInfo`扩展模型的引入，进一步提升了数据查询的准确性和系统的健壮性。该文档为理解系统数据层和进行后续开发提供了坚实的基础。