# 测试项数据模型

<cite>
**本文档中引用的文件**  
- [TestLineInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/trims/TestLineInfo.java)
- [TestLineDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/TestLineDTO.java)
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java)
- [TestLineBaseMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/TestLineBaseMapper.xml)
- [TestLineLanguageMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/TestLineLanguageMapper.xml)
- [TestLineTatConfigExtMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/TestLineTatConfigExtMapper.xml)
</cite>

## 目录
1. [引言](#引言)
2. [核心数据模型](#核心数据模型)
3. [实体关系模型](#实体关系模型)
4. [持久层映射分析](#持久层映射分析)
5. [数据流转与分层架构](#数据流转与分层架构)
6. [结论](#结论)

## 引言
本文档旨在深入解析`TestLine`（测试项）在系统中的数据模型设计。重点阐述`TestLineInfo`实体的关键字段定义、技术规格与业务规则，通过ER图展示其与`Order`（订单）、`Analyte`（分析物）、`Conclusion`（结论）及`SubContract`（外包）等核心实体的复杂多对多关系。同时，深入分析`TestLineMapper`中关键方法的SQL实现与动态查询逻辑，并解释`TestLineInfo`在持久层、领域层和接口层之间的数据流转机制，以及如何通过`TestLineDTO`对外暴露。

## 核心数据模型

### TestLineInfo 实体详解
`TestLineInfo`是表示测试项的核心信息载体，主要位于`otsnotes-facade-model`模块中，其关键字段定义如下：

- **testLineId**: `Integer`类型，表示测试项的唯一标识ID。该字段是业务逻辑中的核心主键，用于关联订单、外包、结论等其他实体。
- **ppNo**: 该字段未在`TestLineInfo`中直接定义，但通过`TestLineInstancePO`等持久化对象与`TestLine`关联，代表测试项所属的PP（Product Plan）编号，是业务分组和查询的重要依据。
- **testLineStatus**: `String`类型，表示测试项的当前状态（如“Active”表示可用）。状态机驱动着测试项的生命周期，影响其在订单处理、报告生成等流程中的行为。
- **labSection**: 该字段未在`TestLineInfo`中直接定义，但通过`LaboratorySectionInfo`的`List`集合体现。`LaboratorySectionInfo`对象包含了实验室分部（Lab Section）的详细信息，实现了`TestLine`与`Lab`的多对多关联。

此外，`TestLineInfo`还包含`testItemId`（测试项目ID）、`testItemVersionId`（测试项目版本ID）、`testLineEvaluation`（测试项评估名称）、`productLineAbbr`（产品线缩写）等丰富属性，共同构成了测试项的完整业务视图。

**Section sources**
- [TestLineInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/trims/TestLineInfo.java#L15-L160)

## 实体关系模型

```mermaid
erDiagram
ORDER {
string orderId PK
string orderNo UK
datetime createdDate
string status
}
TESTLINE {
integer testLineId PK
string testLineInstanceId UK
integer testItemId
integer testItemVersionId
string testLineEvaluation
string status
string ppNo FK
}
ANALYTE {
integer analyteId PK
string analyteName
string unit
}
CONCLUSION {
string conclusionId PK
string testLineInstanceId FK
string conclusionText
string conclusionFlag
datetime generatedDate
}
SUBCONTRACT {
string subcontractId PK
string subcontractNo UK
string labCode
string status
datetime startDate
datetime endDate
}
TESTLINE_INSTANCE {
string testLineInstanceId PK
string orderId FK
integer testLineId FK
string status
datetime createdDate
datetime modifiedDate
}
TESTLINE_ANALYTE {
string id PK
string testLineInstanceId FK
integer analyteId FK
}
TESTLINE_SUBCONTRACT {
string id PK
string testLineInstanceId FK
string subcontractId FK
string subcontractStatus
}
ORDER ||--o{ TESTLINE_INSTANCE : "包含"
TESTLINE ||--o{ TESTLINE_INSTANCE : "实例化为"
TESTLINE_INSTANCE }o--|| TESTLINE : "属于"
TESTLINE_INSTANCE }o--|| ORDER : "属于"
TESTLINE }|--|{ ANALYTE : "包含"
TESTLINE_INSTANCE ||--o{ CONCLUSION : "生成"
TESTLINE_INSTANCE ||--o{ TESTLINE_ANALYTE : "关联"
TESTLINE_ANALYTE }o--|| ANALYTE : "引用"
TESTLINE_INSTANCE ||--o{ TESTLINE_SUBCONTRACT : "关联"
TESTLINE_SUBCONTRACT }o--|| SUBCONTRACT : "引用"
```

**Diagram sources**
- [TestLineInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/trims/TestLineInfo.java)
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java)

## 持久层映射分析

### TestLineMapper 核心方法解析
`TestLineMapper`是MyBatis框架下的数据访问接口，定义了与`TestLine`相关的所有数据库操作。

#### selectByOrderId 方法
该功能由`getTestLineListByOrderId`方法实现。它接收一个`orderId`作为参数，查询并返回该订单下所有的`OrderTestLineRsp`列表。此方法是订单详情页加载测试项数据的基础。其SQL逻辑通常涉及多表关联（如`tb_test_line_instance`, `tb_test_line_base`, `tb_order`等），通过`orderId`进行过滤，并可能根据状态等条件进行动态WHERE拼接。

#### updateStatus 方法
该功能由`batchUpdateTestLineStatus`和`updateTestLineStatus`等系列方法实现。以`batchUpdateTestLineStatus`为例，它接收一个`moduleType`和一个`List<TestLineStatusInfo>`作为参数，可以批量更新多个测试项实例的状态。其对应的SQL语句在XML映射文件中定义，使用`<foreach>`标签遍历列表，生成`UPDATE ... WHERE TestLineInstanceId IN (...)`的批量更新语句，极大地提升了状态变更的效率。

#### 动态查询逻辑
`TestLineMapper`中的许多方法都体现了动态SQL的强大。例如，在`getTestLineByOrderIdAndStatus`方法中，可以根据传入的`TestLineStatusReq`对象中的不同条件（如`orderId`、`statusList`、`testLineIds`等），动态地构建WHERE子句。MyBatis的`<if>`、`<choose>`、`<when>`、`<otherwise>`等标签被广泛使用，确保了查询的灵活性和性能。

**Section sources**
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java#L150-L578)

## 数据流转与分层架构

`TestLine`数据在系统中的流转遵循典型的分层架构模式：

1.  **持久层 (Persistence Layer)**: 数据最初存储在数据库中，通过`TestLineInstancePO`、`TestLineBaseInfoPO`等持久化对象（PO）与数据库表进行映射。`TestLineMapper`负责执行CRUD操作。
2.  **领域层 (Domain Layer)**: 业务逻辑服务（如`TestLineCmdService`）调用`TestLineMapper`获取PO对象，然后将其转换为更丰富的领域模型对象（如`TestLineInfo`），并在这一层执行核心业务规则（如状态变更校验、混测校验等）。
3.  **接口层 (Interface Layer)**: 当需要将数据暴露给外部系统（如前端或其他微服务）时，领域模型对象会被进一步转换为数据传输对象（DTO），即`TestLineDTO`。`TestLineDTO`结构更扁平，只包含外部系统关心的必要字段（如`orderNo`, `testLines`列表），并经过`@JsonIgnoreProperties`等注解处理，确保序列化安全。

这种分层设计实现了关注点分离，保证了数据的安全性和系统的可维护性。

```mermaid
flowchart TD
A[数据库] --> |读取/写入| B[TestLineMapper]
B --> |返回 PO| C[TestLineCmdService]
C --> |转换为| D[TestLineInfo]
C --> |执行业务逻辑| D
D --> |转换为| E[TestLineDTO]
E --> |JSON 序列化| F[外部系统/前端]
F --> |请求| C
```

**Diagram sources**
- [TestLineInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/trims/TestLineInfo.java)
- [TestLineDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/TestLineDTO.java)
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java)

**Section sources**
- [TestLineInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/trims/TestLineInfo.java)
- [TestLineDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/TestLineDTO.java)
- [TestLineMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/TestLineMapper.java)

## 结论
本文档全面解析了`TestLine`数据模型的核心要素。`TestLineInfo`实体通过`testLineId`、`testLineStatus`等关键字段定义了测试项的业务属性，并通过`LaboratorySectionInfo`等关联实现了与`Lab`等实体的复杂关系。`TestLineMapper`作为数据访问的枢纽，其`selectByOrderId`和`updateStatus`等方法通过MyBatis的动态SQL实现了高效、灵活的数据操作。整个数据模型遵循清晰的分层架构，从持久层的PO，到领域层的`TestLineInfo`，再到接口层的`TestLineDTO`，确保了数据在系统内部和外部的安全、有序流转。该模型设计为订单管理、外包协同、报告生成等核心业务功能提供了坚实的数据基础。