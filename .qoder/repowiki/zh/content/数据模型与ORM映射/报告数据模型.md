# 报告数据模型

<cite>
**本文档引用的文件**  
- [ReportInfoMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/ReportInfoMapper.java)
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)
- [ReportSignerExtMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportSignerExtMapper.java)
- [ReportFileExtMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportFileExtMapper.java)
- [ReportTemplateExtMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportTemplateExtMapper.java)
- [ReportDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/EmReportDTO.java)
- [ReportStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ReportStatus.java)
- [ReportService.md](file://doc/api/ReportService.md)
</cite>

## 目录
1. [引言](#引言)
2. [报告实体模型](#报告实体模型)
3. [核心字段详解](#核心字段详解)
4. [报告生命周期与状态转换](#报告生命周期与状态转换)
5. [实体关系图（ER图）](#实体关系图er图)
6. [报告数据访问层（Mapper）分析](#报告数据访问层mapper分析)
7. [报告数据转换（DTO）](#报告数据转换dto)
8. [报告服务协调逻辑](#报告服务协调逻辑)
9. [结论](#结论)

## 引言

本技术文档旨在深入剖析`ReportInfo`数据模型的设计与实现，涵盖其核心字段、生命周期、状态转换机制、与其他实体的关联关系，以及在系统中的数据流转过程。文档将结合数据库模型、MyBatis映射器、数据传输对象（DTO）和服务层逻辑，全面揭示报告模块的技术架构。

## 报告实体模型

`ReportInfo`是系统中报告数据的核心实体，代表一份完整的检测报告。该实体通过MyBatis持久化框架与数据库中的`report_info`表进行映射，是报告生成、审批、分发等业务流程的数据基石。

**Section sources**
- [ReportInfoMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/ReportInfoMapper.java)

## 核心字段详解

`ReportInfo`实体包含以下关键字段：

- **`reportId`**: 报告的唯一标识符，通常为数据库主键，用于系统内部的精确引用。
- **`reportNo`**: 报告编号，是面向用户的唯一业务标识，遵循特定的生成规则，用于报告的检索和识别。
- **`reportStatus`**: 报告状态，枚举类型，表示报告当前所处的生命周期阶段，如“草稿”、“待审批”、“已发布”等。
- **`generateTime`**: 报告生成时间，记录报告内容最终定稿的时间戳。
- **`signer`**: 签署人信息，记录最终批准并签署该报告的用户或角色。

这些字段共同构成了报告的核心元数据，支撑着系统的业务逻辑和用户交互。

**Section sources**
- [ReportInfoMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/ReportInfoMapper.java)
- [ReportStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ReportStatus.java)

## 报告生命周期与状态转换

报告的生命周期由其`reportStatus`字段驱动，经历从创建到归档的多个阶段。典型的状态转换流程如下：

1.  **创建（CREATED）**: 报告被系统创建，但内容尚未完成。
2.  **生成（GENERATED）**: 报告内容已根据检测数据生成，进入待审批状态。
3.  **待审批（PENDING_APPROVAL）**: 报告需要经过指定审批人（Signer）的审核。
4.  **已批准（APPROVED）**: 审批人确认无误，报告被正式批准。
5.  **已发布（PUBLISHED）**: 报告已对外发布，可供客户或相关方查阅。
6.  **已归档（ARCHIVED）**: 报告进入长期存储状态。

状态转换由`ReportService`协调，并通过调用`ReportMapper`中的更新方法来持久化状态变更。

**Section sources**
- [ReportStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ReportStatus.java)
- [ReportService.md](file://doc/api/ReportService.md)

## 实体关系图（ER图）

```mermaid
erDiagram
REPORT {
string reportId PK
string reportNo UK
string reportStatus
datetime generateTime
string signer
}
ORDER {
string orderId PK
string orderNo
}
TESTLINE {
string testLineId PK
string testLineNo
}
REPORTFILE {
string fileId PK
string fileName
string filePath
string fileStatus
}
REPORTSIGNER {
string signerId PK
string userId
string reportId FK
int signOrder
string signStatus
}
REPORT ||--|| ORDER : "属于"
REPORT ||--o{ TESTLINE : "包含"
REPORT ||--o{ REPORTFILE : "关联文件"
REPORT ||--o{ REPORTSIGNER : "签署人"
```

**Diagram sources**
- [ReportInfoMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/ReportInfoMapper.java)
- [ReportFileExtMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportFileExtMapper.java)
- [ReportSignerExtMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportSignerExtMapper.java)

## 报告数据访问层（Mapper）分析

`ReportMapper`是操作报告数据的核心接口，封装了复杂的SQL逻辑。

- **报告生成**: 通过`insertReport`方法将新生成的报告数据插入`report_info`表，并可能关联`test_line`数据。
- **审批流程**: 使用`updateReportStatus`和`insertReportSigner`方法来更新报告状态并记录签署人信息，实现多级审批。
- **报告查询**: 提供如`selectReportByOrderNo`、`selectReportList`等方法，支持根据订单号、状态、时间范围等条件进行复杂查询，SQL中常包含多表关联（JOIN）。

**Section sources**
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)

## 报告数据转换（DTO）

为了在服务层与表现层（或外部系统）之间安全、高效地传输数据，系统使用`ReportDTO`（如`EmReportDTO`）作为数据传输对象。

`ReportInfo`到`ReportDTO`的转换过程通常由一个专门的转换器（Converter）完成，该过程包括：
1.  从`ReportInfo`实体中提取必要字段。
2.  根据业务需求，从关联的`Order`、`TestLine`、`ReportFile`等实体中补充信息。
3.  对数据进行格式化（如日期格式）和脱敏处理。
4.  将结果封装成`ReportDTO`对象。

这种转换实现了领域模型与外部接口的解耦。

**Section sources**
- [ReportDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/EmReportDTO.java)
- [ReportInfoMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/ReportInfoMapper.java)

## 报告服务协调逻辑

`ReportService`是报告功能的核心协调者，它整合了`ReportMapper`、`OrderService`、`TestLineService`等组件，实现了报告的创建、审批和分发。

- **创建**: 接收订单和检测线数据，调用`ReportMapper`生成报告初稿。
- **审批**: 管理审批流程，调用`ReportSignerExtMapper`记录审批人，并在所有审批通过后更新`reportStatus`。
- **分发**: 在报告发布后，触发文件生成和分发逻辑，可能涉及调用`ReportFileExtMapper`来管理报告文件。

`ReportService`确保了整个报告流程的原子性和一致性。

**Section sources**
- [ReportService.md](file://doc/api/ReportService.md)
- [ReportMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/ReportMapper.java)

## 结论

`ReportInfo`数据模型是整个报告系统的核心。通过对其字段、生命周期、关联关系和访问逻辑的深入分析，可以清晰地理解报告从数据生成到最终发布的完整技术路径。该模型设计合理，通过`ReportMapper`实现了复杂的数据操作，通过`ReportDTO`实现了安全的数据传输，并由`ReportService`有效地协调了各组件，共同支撑了系统的报告业务。