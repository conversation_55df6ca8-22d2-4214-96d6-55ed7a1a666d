# 分包数据模型

<cite>
**本文档引用的文件**   
- [SubContractInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/subcontract/SubContractInfo.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心实体分析](#核心实体分析)
3. [分包数据模型ER图](#分包数据模型er图)
4. [SQL操作与数据持久化](#sql操作与数据持久化)
5. [与StarLIMS系统的集成](#与starlims系统的集成)
6. [分包流程的业务逻辑](#分包流程的业务逻辑)
7. [结论](#结论)

## 简介
分包（SubContract）数据模型是系统中用于管理外部实验室协作的核心组件。该模型通过`SubContractInfo`实体定义了分包单的详细信息，包括分包ID、外部实验室代码、同步状态和结果接收时间等关键字段。这些字段在跨系统协作中起着至关重要的作用，确保了数据的一致性和可追溯性。`SubContractMapper`提供了用于同步状态更新和结果接收的SQL操作，而`SubContractService`则实现了分包流程的业务逻辑，支持与StarLIMS系统的无缝集成。

**Section sources**
- [SubContractInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/subcontract/SubContractInfo.java#L8-L456)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java#L7-L35)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java#L177-L3978)

## 核心实体分析
`SubContractInfo`实体是分包数据模型的核心，它包含了多个关键字段，每个字段都有其特定的作用和意义。以下是该实体的主要字段及其描述：

- **subContractId**: 分包单的唯一标识符，用于在系统中唯一识别一个分包单。
- **externalLabCode**: 外部实验室代码，用于标识执行分包任务的外部实验室。
- **syncStatus**: 同步状态，表示分包单与外部系统的同步情况，具体值为0（pending）、1（trigger）、2（get master job）、3（get report）。
- **resultReceivedTime**: 结果接收时间，记录外部实验室返回测试结果的时间。

这些字段共同构成了分包单的基本信息，确保了分包流程的透明性和可控性。

```mermaid
classDiagram
class SubContractInfo {
+String subContractId
+String externalLabCode
+Integer syncStatus
+Date resultReceivedTime
+String orderNo
+String subContractNo
+String subContractLabName
+String subContractServiceType
+Date subContractExpectDueDate
+String additionalInfo
+String subContractRemark
+Date startDate
+Date completeDate
+Integer subContractOrder
+Integer dataLock
+String modifiedBy
+Date modifiedDate
+String reportRequirement
+String reportTemplate
+String orderLabCode
+String errorMsg
+Boolean enableToStarLims
+Boolean enableToSlim
+String subContractContract
+String subContractContractTel
+String subContractContractEmail
+Integer subcontractTAT
+String externalNo
+String subcontractFeeCurrency
+BigDecimal subcontractFee
+Date createdDate
+String createdby
+String csContact
+String csEmail
+Date orderDueDate
+String extData
+SubContractExtInfo subContractExt
}
```

**Diagram sources**
- [SubContractInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/subcontract/SubContractInfo.java#L8-L456)

**Section sources**
- [SubContractInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/subcontract/SubContractInfo.java#L8-L456)

## 分包数据模型ER图
分包数据模型通过ER图展示了`SubContract`与`Order`、`TestLine`、`Report`以及外部实验室信息之间的关联关系。这种关系确保了数据的完整性和一致性，使得分包流程中的每一个环节都能被准确追踪。

```mermaid
erDiagram
ORDER ||--o{ SUBCONTRACT : "包含"
SUBCONTRACT ||--o{ TESTLINE : "关联"
SUBCONTRACT ||--o{ REPORT : "生成"
SUBCONTRACT ||--|| EXTERNAL_LAB : "发送至"
ORDER {
string orderNo PK
string labCode
string buCode
timestamp orderDate
}
SUBCONTRACT {
string subContractId PK
string orderNo FK
string externalLabCode FK
integer syncStatus
timestamp resultReceivedTime
string subContractNo
string subContractLabName
string subContractServiceType
timestamp subContractExpectDueDate
string additionalInfo
string subContractRemark
timestamp startDate
timestamp completeDate
integer subContractOrder
integer dataLock
string modifiedBy
timestamp modifiedDate
string reportRequirement
string reportTemplate
string orderLabCode
string errorMsg
boolean enableToStarLims
boolean enableToSlim
string subContractContract
string subContractContractTel
string subContractContractEmail
integer subcontractTAT
string externalNo
string subcontractFeeCurrency
decimal subcontractFee
timestamp createdDate
string createdby
string csContact
string csEmail
timestamp orderDueDate
string extData
}
TESTLINE {
string testLineId PK
string subContractId FK
string testLineName
string testLineStatus
timestamp testLineDate
}
REPORT {
string reportId PK
string subContractId FK
string reportNo
string reportStatus
timestamp reportDate
}
EXTERNAL_LAB {
string externalLabCode PK
string labName
string labAddress
string labContact
string labEmail
}
```

**Diagram sources**
- [SubContractInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/subcontract/SubContractInfo.java#L8-L456)

## SQL操作与数据持久化
`SubContractMapper`接口定义了一系列用于操作分包数据的SQL方法，这些方法支持分包单的创建、更新、删除和查询。以下是主要的SQL操作：

- **insert**: 插入新的分包单记录。
- **updateByPrimaryKeySelective**: 根据主键选择性地更新分包单记录。
- **deleteByPrimaryKey**: 根据主键删除分包单记录。
- **selectByPrimaryKey**: 根据主键查询分包单记录。

这些操作确保了分包数据的持久化和一致性，支持分包流程中的各种业务需求。

```mermaid
classDiagram
class SubContractMapper {
+int insert(SubContractPO record)
+int updateByPrimaryKeySelective(SubContractPO record)
+int deleteByPrimaryKey(String ID)
+SubContractPO selectByPrimaryKey(String ID)
}
```

**Diagram sources**
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java#L7-L35)

**Section sources**
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java#L7-L35)

## 与StarLIMS系统的集成
分包数据模型通过`SubContractService`实现了与StarLIMS系统的集成。`SubContractService`中的业务逻辑确保了分包单的状态同步和数据交换。例如，当分包单的状态发生变化时，`SubContractService`会触发相应的事件，通知StarLIMS系统进行状态更新。此外，`SubContractService`还支持从StarLIMS系统接收测试结果，并将其存储在本地数据库中。

```mermaid
sequenceDiagram
participant User
participant SubContractService
participant StarLIMS
User->>SubContractService : 创建分包单
SubContractService->>StarLIMS : 发送分包请求
StarLIMS-->>SubContractService : 返回分包确认
SubContractService->>User : 显示分包单状态
User->>SubContractService : 更新分包单状态
SubContractService->>StarLIMS : 同步状态更新
StarLIMS-->>SubContractService : 确认状态更新
SubContractService->>User : 显示更新后的状态
StarLIMS->>SubContractService : 发送测试结果
SubContractService->>User : 显示测试结果
```

**Diagram sources**
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java#L177-L3978)

## 分包流程的业务逻辑
`SubContractService`实现了分包流程的完整业务逻辑，包括分包单的创建、状态更新、结果接收和数据同步。以下是主要的业务逻辑步骤：

1. **创建分包单**: 用户创建分包单时，`SubContractService`会验证输入参数，并生成唯一的分包单号。
2. **状态更新**: 当分包单的状态发生变化时，`SubContractService`会更新分包单的状态，并触发相应的事件。
3. **结果接收**: 当外部实验室返回测试结果时，`SubContractService`会接收结果并存储在本地数据库中。
4. **数据同步**: `SubContractService`会定期与StarLIMS系统同步分包单的状态和数据，确保数据的一致性。

这些业务逻辑确保了分包流程的高效和可靠，支持跨系统的协作。

```mermaid
flowchart TD
Start([开始]) --> CreateSubContract["创建分包单"]
CreateSubContract --> ValidateInput["验证输入参数"]
ValidateInput --> GenerateSubContractNo["生成分包单号"]
GenerateSubContractNo --> SaveSubContract["保存分包单"]
SaveSubContract --> UpdateStatus["更新分包单状态"]
UpdateStatus --> SyncWithStarLIMS["与StarLIMS系统同步"]
SyncWithStarLIMS --> ReceiveResults["接收测试结果"]
ReceiveResults --> StoreResults["存储测试结果"]
StoreResults --> End([结束])
```

**Diagram sources**
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java#L177-L3978)

**Section sources**
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java#L177-L3978)

## 结论
分包数据模型通过`SubContractInfo`实体、`SubContractMapper`和`SubContractService`三个核心组件，实现了分包流程的全面管理。`SubContractInfo`定义了分包单的关键字段，`SubContractMapper`提供了数据持久化的支持，而`SubContractService`则实现了与StarLIMS系统的集成和业务逻辑。这些组件共同确保了分包流程的透明性、一致性和可靠性，支持跨系统的高效协作。