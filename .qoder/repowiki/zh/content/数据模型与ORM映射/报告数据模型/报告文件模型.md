# 报告文件模型

<cite>
**本文档引用的文件**  
- [ReportFilePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportFilePO.java)
- [ReportFileExtMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportFileExtMapper.xml)
- [ReportFileMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/ReportFileMapper.xml)
- [ReportFileStatusEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ReportFileStatusEnum.java)
- [testline_init.sql](file://uni-otsnotes/doc/db/testline_init.sql)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)
10. [附录](#附录)

## 引言
本报告文件模型文档旨在全面阐述报告文件管理系统的数据库表结构和ORM映射机制。文档重点介绍`tb_report_file`表的字段定义、数据类型、主外键关系和索引策略，详细说明`file_id`、`report_id`、`file_path`（在系统中对应`CloudID`）、`file_type`（在系统中对应`ReportFileType`）、`status`（在系统中对应`FileStatus`）等关键字段的业务含义。同时，文档将解析MyBatis映射文件中与报告文件相关的SQL语句编写逻辑和查询优化技巧，提供ER图展示报告文件与报告主表、文件存储系统、审批流程的关联关系。此外，文档还将说明报告文件的完整性约束、版本控制机制和状态管理（如上传中、已生成、已归档等），并文档化报告文件的数据访问模式、大文件处理策略和性能优化方案，包括报告文件生命周期管理、存储策略和备份恢复方案。

## 项目结构
报告文件管理功能主要分布在`otsnotes-dbstorages`模块中，该模块负责数据持久化。核心的数据库表定义位于`uni-otsnotes/doc/db/testline_init.sql`文件中，而对应的Java实体类（PO）和MyBatis映射文件则位于`otsnotes-dbstorages`模块的`src/main`目录下。业务逻辑层通过`otsnotes-facade`模块提供的接口与数据层交互，`otsnotes-facade-model`模块则定义了相关的枚举和数据传输对象（DTO）。

```mermaid
graph TB
subgraph "业务逻辑层"
facade[otsnotes-facade]
facade_impl[otsnotes-facade-impl]
end
subgraph "数据持久层"
dbstorages[otsnotes-dbstorages]
schema[uni-otsnotes/doc/db/testline_init.sql]
end
facade --> dbstorages
facade_impl --> dbstorages
dbstorages --> schema
```

**图表来源**  
- [testline_init.sql](file://uni-otsnotes/doc/db/testline_init.sql)
- [ReportFilePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportFilePO.java)

**章节来源**  
- [testline_init.sql](file://uni-otsnotes/doc/db/testline_init.sql)
- [otsnotes-dbstorages](file://otsnotes-dbstorages)

## 核心组件
报告文件管理的核心组件是`ReportFilePO`实体类和`ReportFileMapper.xml`、`ReportFileExtMapper.xml`两个MyBatis映射文件。`ReportFilePO`类作为`tb_report_file`表的Java对象映射，封装了所有字段的getter和setter方法。`ReportFileMapper.xml`由代码生成工具生成，提供了对`tb_report_file`表进行增删改查的基础方法。`ReportFileExtMapper.xml`则包含了业务逻辑中常用的自定义SQL语句，如根据报告ID和文件类型获取文件信息、批量插入等。

**章节来源**  
- [ReportFilePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportFilePO.java)
- [ReportFileExtMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportFileExtMapper.xml)
- [ReportFileMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/ReportFileMapper.xml)

## 架构概述
报告文件管理系统采用分层架构，从上至下分别为接口层（Facade）、业务实现层（FacadeImpl）、数据访问层（Mapper）和数据库层。`ReportFacade`接口定义了报告文件相关的操作，如上传、下载、删除等。`ReportFacadeImpl`实现类调用`ReportFileExtMapper`中的方法来执行具体的数据库操作。`ReportFileExtMapper`通过MyBatis框架与`tb_report_file`表进行交互，完成数据的持久化。

```mermaid
graph TD
A[ReportFacade] --> B[ReportFacadeImpl]
B --> C[ReportFileExtMapper]
C --> D[tb_report_file]
```

**图表来源**  
- [ReportFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/ReportFacade.java)
- [ReportFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/ReportFacadeImpl.java)
- [ReportFileExtMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportFileExtMapper.xml)

## 详细组件分析
### ReportFilePO 实体类分析
`ReportFilePO`类是`tb_report_file`表的Java持久化对象（PO），它通过字段与数据库表的列一一对应，实现了数据的映射。

#### 类图
```mermaid
classDiagram
class ReportFilePO {
+String ID
+String reportID
+String reportNo
+Integer reportFileType
+String cloudID
+String filename
+Date fileCreatedDate
+Integer fileStatus
+Integer dssStatus
+String createdBy
+Date createdDate
+String modifiedBy
+Date modifiedDate
+Boolean activeIndicator
+String suffixes
+Integer languageID
+String originalCloudID
+Integer status
+Integer generateStatus
+String generateErrorMessage
+getID() String
+setID(String) void
+getReportID() String
+setReportID(String) void
+getReportNo() String
+setReportNo(String) void
+getReportFileType() Integer
+setReportFileType(Integer) void
+getCloudID() String
+setCloudID(String) void
+getFilename() String
+setFilename(String) void
+getFileCreatedDate() Date
+setFileCreatedDate(Date) void
+getFileStatus() Integer
+setFileStatus(Integer) void
+getDssStatus() Integer
+setDssStatus(Integer) void
+getCreatedBy() String
+setCreatedBy(String) void
+getCreatedDate() Date
+setCreatedDate(Date) void
+getModifiedBy() String
+setModifiedBy(String) void
+getModifiedDate() Date
+setModifiedDate(Date) void
+getActiveIndicator() Boolean
+setActiveIndicator(Boolean) void
+getSuffixes() String
+setSuffixes(String) void
+getLanguageID() Integer
+setLanguageID(Integer) void
+getOriginalCloudID() String
+setOriginalCloudID(String) void
+getStatus() Integer
+setStatus(Integer) void
+getGenerateStatus() Integer
+setGenerateStatus(Integer) void
+getGenerateErrorMessage() String
+setGenerateErrorMessage(String) void
+toString() String
}
```

**图表来源**  
- [ReportFilePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportFilePO.java)

**章节来源**  
- [ReportFilePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportFilePO.java)

### ReportFileExtMapper.xml 映射文件分析
`ReportFileExtMapper.xml`文件定义了多个自定义的SQL语句，这些语句针对特定的业务场景进行了优化。

#### 获取报告文件信息序列图
```mermaid
sequenceDiagram
participant Service as "ReportService"
participant Mapper as "ReportFileExtMapper"
participant DB as "Database"
Service->>Mapper : getReportFileInfo(reportID, reportFileType)
Mapper->>DB : SELECT * FROM tb_report_file WHERE ReportID = ? AND ReportFileType = ? LIMIT 1
DB-->>Mapper : 返回单条记录
Mapper-->>Service : 返回ReportFilePO对象
```

**图表来源**  
- [ReportFileExtMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportFileExtMapper.xml)

**章节来源**  
- [ReportFileExtMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportFileExtMapper.xml)

## 依赖分析
报告文件实体`ReportFilePO`依赖于`java.util.Date`类来处理时间戳。`ReportFileExtMapper`依赖于MyBatis框架来执行SQL语句。整个`otsnotes-dbstorages`模块依赖于数据库驱动和连接池。

```mermaid
graph TD
A[ReportFilePO] --> B[java.util.Date]
C[ReportFileExtMapper] --> D[MyBatis Framework]
E[otsnotes-dbstorages] --> F[Database Driver]
E --> G[Connection Pool]
```

**图表来源**  
- [ReportFilePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportFilePO.java)
- [ReportFileExtMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportFileExtMapper.xml)

**章节来源**  
- [ReportFilePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportFilePO.java)
- [ReportFileExtMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/ReportFileExtMapper.xml)

## 性能考虑
为了提高查询性能，`tb_report_file`表在`ReportID`和`ReportFileType`字段上建立了复合索引。在`ReportFileExtMapper.xml`中，`getReportFileInfo`方法使用了`LIMIT 1`来确保只返回一条记录，避免了不必要的数据传输。对于批量操作，如`batchInsert`，使用了MyBatis的`<foreach>`标签和`ON DUPLICATE KEY UPDATE`语法，可以显著提高插入效率。

## 故障排除指南
当报告文件无法正常上传或下载时，应首先检查`tb_report_file`表中对应记录的`FileStatus`和`DssStatus`字段值。`FileStatus`为0表示DSS回调，为1表示手动上传。如果`generateStatus`为4，则表示报告生成失败，应查看`generateErrorMessage`字段获取具体的错误信息。

**章节来源**  
- [ReportFilePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportFilePO.java)
- [ReportFileStatusEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ReportFileStatusEnum.java)

## 结论
本报告文件模型文档详细阐述了报告文件管理系统的数据模型和ORM映射。通过对`ReportFilePO`实体类和`ReportFileExtMapper.xml`映射文件的分析，我们了解了报告文件的存储结构、状态管理和操作逻辑。该系统通过分层架构和MyBatis框架，实现了高效、可靠的数据访问。

## 附录
### 报告文件状态枚举
| 状态码 | 状态名称 | 说明 |
| :--- | :--- | :--- |
| 0 | DSSCALLBACK | DSS回调 |
| 1 | MANUALUPLOAD | 手动上传 |

**章节来源**  
- [ReportFileStatusEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ReportFileStatusEnum.java)

### 报告文件表结构
| 字段名 | 数据类型 | 允许空值 | 默认值 | 主键 | 外键 | 说明 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| ID | VARCHAR(36) | 否 | - | 是 | - | 报告文件ID，主键 |
| ReportID | VARCHAR(36) | 是 | - | 否 | 是 | 报告ID，外键关联tb_report |
| ReportNo | VARCHAR(100) | 是 | - | 否 | 是 | 报告编号 |
| ReportFileType | INTEGER(10) | 是 | - | 否 | - | 报告文件类型 |
| CloudID | VARCHAR(250) | 是 | - | 否 | - | OSS Key，文件在对象存储中的路径 |
| Filename | VARCHAR(100) | 是 | - | 否 | - | 文件名 |
| FileCreatedDate | TIMESTAMP(19) | 是 | - | 否 | - | 文件创建时间 |
| FileStatus | INTEGER(10) | 是 | - | 否 | - | 文件状态 |
| DssStatus | INTEGER(10) | 是 | - | 否 | - | DSS状态 |
| CreatedBy | VARCHAR(100) | 是 | - | 否 | - | 创建人 |
| CreatedDate | TIMESTAMP(19) | 是 | 2000-01-01 00:00:00 | 否 | - | 创建时间 |
| ModifiedBy | VARCHAR(100) | 是 | - | 否 | - | 修改人 |
| ModifiedDate | TIMESTAMP(19) | 是 | 2000-01-01 00:00:00 | 否 | - | 修改时间 |
| ActiveIndicator | BIT | 否 | 1 | 否 | - | 活跃指示器，0: 不活跃, 1: 活跃 |
| Suffixes | VARCHAR(250) | 是 | - | 否 | - | 后缀 |
| LanguageID | INTEGER(10) | 是 | - | 否 | - | 语言ID |
| OriginalCloudID | VARCHAR(250) | 是 | - | 否 | - | 原始文件，即每次生成后的doc |
| Status | INTEGER(10) | 是 | - | 否 | - | 状态 |
| GenerateStatus | INTEGER(10) | 是 | - | 否 | - | 生成报告状态 1-未生成 2-生成中 3-生成成功 4-生成失败 |
| GenerateErrorMessage | VARCHAR(200) | 是 | - | 否 | - | 错误信息 |

**章节来源**  
- [testline_init.sql](file://uni-otsnotes/doc/db/testline_init.sql)
- [ReportFilePO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/ReportFilePO.java)