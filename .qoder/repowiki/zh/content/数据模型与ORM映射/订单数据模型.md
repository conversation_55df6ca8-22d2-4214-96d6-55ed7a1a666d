# 订单数据模型

<cite>
**本文档引用的文件**  
- [OrderInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/OrderInfo.java)
- [OrderMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/OrderMapper.xml)
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java)
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java)
</cite>

## 目录
1. [简介](#简介)
2. [订单数据模型详解](#订单数据模型详解)
3. [实体关系图（ER图）](#实体关系图er图)
4. [MyBatis映射与数据库操作](#mybatis映射与数据库操作)
5. [数据对象转换流程](#数据对象转换流程)
6. [订单服务业务逻辑](#订单服务业务逻辑)
7. [结论](#结论)

## 简介
本文档深入解析订单（Order）数据模型的设计与实现，涵盖`OrderInfo`实体的字段定义、数据库约束、业务语义，以及其与`TestLine`、`Sample`、`Report`等实体的关联关系。文档详细分析MyBatis的`OrderMapper`接口及其XML映射文件，阐明关键数据库操作的实现机制。同时，阐述了从数据库实体到领域对象再到数据传输对象的转换流程，并说明`OrderService`如何利用该模型处理核心业务逻辑。

## 订单数据模型详解

`OrderInfo`类是订单系统的核心数据传输对象（DTO），定义了订单的所有属性。该类位于`otsnotes-facade-model`模块中，继承自`PrintFriendliness`以支持打印友好输出。

### 核心字段说明

以下表格详细描述了`OrderInfo`类的关键字段，包括其数据类型、数据库约束和业务语义。

| 字段名 | 数据类型 | 数据库约束 | 业务语义 |
| :--- | :--- | :--- | :--- |
| `ID` | `String` | VARCHAR(36), 主键, 必填 | 订单的唯一标识符，使用UUID生成。 |
| `orderNo` | `String` | VARCHAR(50), 唯一索引 | 客户可见的订单编号，用于业务查询和外部系统交互。 |
| `orderStatus` | `Integer` | INTEGER(10) | 订单状态码，表示订单当前所处的生命周期阶段（如新建、进行中、已完成）。 |
| `customerCode` | `String` | VARCHAR(50) | 客户代码，关联客户主数据。 |
| `customerName` | `String` | VARCHAR(360) | 客户英文名称。 |
| `customerGroupCode` | `String` | VARCHAR(50) | 客户组代码，用于客户分类和权限控制。 |
| `labCode` | `String` | VARCHAR(50) | 实验室代码，标识处理该订单的实验室。 |
| `activeIndicator` | `Boolean` | BIT, 默认值[1], 必填 | 激活指示器，1表示订单有效，0表示订单已停用或作废。 |
| `createdDate` | `Date` | TIMESTAMP(19) | 订单创建时间。 |
| `createdBy` | `String` | VARCHAR(50) | 创建订单的用户。 |
| `modifiedDate` | `Date` | TIMESTAMP(19) | 订单最后修改时间。 |
| `modifiedBy` | `String` | VARCHAR(50) | 最后修改订单的用户。 |
| `lastModifiedTimestamp` | `Date` | TIMESTAMP(19), 默认值[CURRENT_TIMESTAMP(3)] | 记录行最后一次被修改的时间戳，用于乐观锁和数据同步。 |
| `conclusionMode` | `Integer` | INTEGER(10), 默认值[0] | 结论计算模式，0表示按测试矩阵（matrix）计算，1表示按PP矩阵（pp matrix）计算。 |
| `confirmMatrixDate` | `Date` | TIMESTAMP(19) | 确认测试矩阵的日期，标志着测试计划的最终确定。 |

**Section sources**
- [OrderInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/OrderInfo.java#L1-L645)

## 实体关系图（ER图）

下图展示了`Order`实体与`TestLine`、`Sample`、`Report`等核心实体之间的关联关系。

```mermaid
erDiagram
ORDER {
string ID PK
string orderNo UK
integer orderStatus
string customerCode
string customerName
string customerGroupCode
string labCode
integer orderLaboratoryID
boolean activeIndicator
timestamp createdDate
string createdBy
timestamp modifiedDate
string modifiedBy
timestamp lastModifiedTimestamp
integer conclusionMode
timestamp confirmMatrixDate
}
TEST_LINE_INSTANCE {
string ID PK
string generalOrderInstanceID FK
string testLineID
string testLineEvaluation
integer testLineVersionID
integer standardVersionID
string standardName
string orderTestLineRemark
}
TEST_SAMPLE {
string ID PK
string orderNo FK
string sampleNo
integer sampleType
string description
string composition
string color
string endUse
}
REPORT {
string ID PK
string orderNo FK
string reportNo
timestamp reportDueDate
string customerCode
string customerGroupCode
integer labId
string labCode
integer reportStatus
integer recalculationFlag
integer reportVersion
integer testMatrixMergeMode
string rootReportNo
}
ORDER ||--o{ TEST_LINE_INSTANCE : "包含"
ORDER ||--o{ TEST_SAMPLE : "包含"
ORDER ||--o{ REPORT : "生成"
```

**Diagram sources**
- [OrderInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/OrderInfo.java#L1-L645)
- [OrderMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/OrderMapper.xml#L1-L234)

## MyBatis映射与数据库操作

MyBatis框架用于实现`OrderInfo`实体与数据库表`tb_general_order_instance`之间的持久化操作。该操作通过`OrderMapper`接口和`OrderMapper.xml`映射文件共同定义。

### OrderMapper 接口

`OrderMapper`是一个Java接口，位于`otsnotes-dbstorages`模块中，定义了所有与订单相关的数据库操作方法。它使用MyBatis注解（如`@Param`）来指定SQL参数。

```java
public interface OrderMapper {
    int saveOrderInfo(GeneralOrderInstanceInfoPO order);
    int updateOrderInfo(GeneralOrderInstanceInfoPO order);
    GeneralOrderInstanceInfoPO getOrderInfoByOrderId(@Param("orderId") String orderId);
    // ... 其他方法
}
```

### OrderMapper.xml 映射文件

`OrderMapper.xml`文件包含了具体的SQL语句，与`OrderMapper`接口中的方法相对应。它定义了SQL的执行逻辑、参数映射和结果集映射。

#### 关键SQL操作分析

1.  **`saveOrderInfo` (插入)**: 该操作将一个新的订单记录插入到`tb_general_order_instance`表中。它使用`<insert>`标签，并通过`<include refid="Base_Column_List" />`复用列名列表，确保了代码的简洁性。参数`#{ID,jdbcType=VARCHAR}`等使用了预编译参数占位符，有效防止SQL注入。
2.  **`updateOrderInfo` (更新)**: 该操作根据`ID`更新订单的客户信息、实验室信息和结论模式等字段。它是一个部分更新操作，只修改了特定的列。
3.  **`getOrderInfoByOrderId` (查询)**: 该操作根据订单的`ID`从数据库中查询完整的订单信息。`resultType`指定了结果映射到`GeneralOrderInstanceInfoPO`对象。
4.  **`updateOrderStatus` (更新状态)**: 该操作专门用于更新订单的状态（`OrderStatus`），并同时更新`ModifiedBy`和`ModifiedDate`字段，确保了审计信息的准确性。

**Section sources**
- [OrderMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/OrderMapper.java#L1-L121)
- [OrderMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/OrderMapper.xml#L1-L234)

## 数据对象转换流程

系统在不同层次间使用不同的数据对象，以满足各层的特定需求。`OrderInfo`作为数据传输对象（DTO），在服务间传递数据。其转换流程如下：

1.  **数据库实体 (PO)**: `GeneralOrderInstanceInfoPO` 是持久化对象（PO），直接映射数据库表`tb_general_order_instance`的每一列。MyBatis负责将数据库记录自动转换为PO对象。
2.  **领域对象 (DO)**: 在`OrderService`中，业务逻辑操作通常在PO或其包装对象上进行。虽然没有显式的DO类，但`GeneralOrderInstanceInfoPO`在服务层扮演了DO的角色。
3.  **数据传输对象 (DTO)**: `OrderInfo`是最终暴露给外部系统（如Web层或其他微服务）的数据传输对象。当需要将订单信息返回给前端或调用方时，服务层会将`GeneralOrderInstanceInfoPO`对象的属性通过`BeanUtils.copyProperties()`等工具类复制到`OrderInfo`对象中。

此转换流程实现了数据访问层与业务逻辑层、表现层的解耦，保证了内部数据结构的稳定性和外部接口的灵活性。

**Section sources**
- [OrderInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/OrderInfo.java#L1-L645)
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java#L1-L799)

## 订单服务业务逻辑

`OrderService`是处理订单核心业务逻辑的服务类，位于`otsnotes-domain`模块。它协调`OrderMapper`、`ReportMapper`、`TestLineService`等多个组件，完成复杂的业务流程。

### 核心业务方法

1.  **`createOrderInfo(SyncOrderInfo reqObject)`**: 这是创建订单的入口方法。它首先调用`preOrdercheck`进行前置校验（如订单号、客户信息、样品信息的完整性），然后调用`createGeneralOrder`执行实际的创建或更新逻辑。
2.  **`createGeneralOrder(SyncOrderInfo preOrder, boolean noChangeFlag)`**: 该方法是创建/更新订单的核心。它在一个数据库事务中执行以下操作：
    *   调用`generalOrder`方法构建`GeneralOrderInstanceInfoPO`对象。
    *   调用`generalReport`方法构建`ReportInfoPO`对象。
    *   调用`generalSample`方法处理样品信息。
    *   使用`transactionTemplate`开启事务，确保订单、报告和样品的创建/更新操作具有原子性。如果任何一步失败，整个事务将回滚。
3.  **`generalTestLine(String orderId, List<SyncTestLineInfo> testLineInfos)`**: 在订单创建成功后，该方法负责根据`SyncTestLineInfo`列表创建关联的测试项（TestLine）。它通过调用`testLineClient`和`ppClient`等外部服务获取测试项的详细信息，并最终调用`testLineService.saveTestLineList`来保存。

该服务的设计体现了典型的分层架构和事务管理思想，确保了业务逻辑的健壮性和数据的一致性。

**Section sources**
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java#L1-L799)

## 结论
本文档全面解析了订单数据模型的各个方面。`OrderInfo`实体作为核心数据载体，其字段设计兼顾了业务需求和数据完整性。通过ER图清晰地展示了订单与测试项、样品、报告等实体的关联。MyBatis的`OrderMapper`提供了高效、安全的数据库访问能力。从PO到DTO的转换流程保证了系统的分层清晰。最后，`OrderService`通过协调多个组件和严格的事务控制，实现了创建订单这一复杂业务场景的可靠执行。整个模型设计合理，为系统的稳定运行奠定了坚实基础。