# 分包需求模型

<cite>
**本文档引用的文件**
- [SubcontractRequirementPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubcontractRequirementPO.java)
- [SubContractRequirementExtMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractRequirementExtMapper.xml)
- [SubcontractRequirementInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/subcontract/SubcontractRequirementInfo.java)
- [SubContractRequirementFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/SubContractRequirementFacade.java)
- [testline_init.sql](file://uni-otsnotes/doc/db/testline_init.sql)
</cite>

## 目录
1. [引言](#引言)
2. [数据库表结构](#数据库表结构)
3. [ORM映射分析](#orm映射分析)
4. [MyBatis SQL映射](#mybatis-sql映射)
5. [实体关系图](#实体关系图)
6. [数据完整性与业务规则](#数据完整性与业务规则)
7. [数据访问模式与性能优化](#数据访问模式与性能优化)
8. [最佳实践](#最佳实践)

## 引言
分包需求模型是OTSNotes系统中用于管理分包测试需求的核心数据模型。该模型通过`tb_subcontract_requirement`表存储分包相关的各项要求，包括报告要求、服务类型、资质要求、交付方式等关键信息。本文档详细阐述了分包需求实体的数据库表结构、ORM映射关系、MyBatis映射文件中的SQL语句逻辑，以及相关的数据完整性约束和业务规则。

**Section sources**
- [SubcontractRequirementPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubcontractRequirementPO.java)
- [testline_init.sql](file://uni-otsnotes/doc/db/testline_init.sql)

## 数据库表结构
分包需求实体对应的数据库表为`tb_subcontract_requirement`，该表存储了分包测试的所有需求信息。

### 表结构定义
| 字段名 | 数据类型 | 是否必填 | 说明 |
|--------|---------|---------|------|
| ID | VARCHAR(36) | 是 | 主键ID |
| GeneralOrderInstanceID | VARCHAR(36) | 是 | 关联的主订单实例ID |
| SubContractID | VARCHAR(36) | 是 | 分包ID |
| SubContractNo | VARCHAR(50) | 否 | 分包编号 |
| EvaluationBasis | VARCHAR(250) | 否 | 评判原则 |
| OtherRequirements | VARCHAR(500) | 否 | 其他要求 |
| Qualification | VARCHAR(250) | 否 | 资质要求 |
| ReoprtDate | TIMESTAMP(19) | 否 | 预期报告接收日期 |
| ReportLanguage | VARCHAR(50) | 否 | 报告语言 |
| ReportManner | INTEGER(10) | 否 | 报告方式 |
| ReportType | VARCHAR(50) | 否 | 报告类型 |
| ReportRequirement | VARCHAR(50) | 否 | 报告要求 |
| ReportTemplate | VARCHAR(250) | 否 | 报告模板 |
| ReportQty | INTEGER(10) | 否 | 报告数量 |
| ResultJudgingFlag | BIT | 否 | 结果判定标志 |
| ServiceType | INTEGER(10) | 否 | 服务类型 |
| DisplaySupplierFlag | BIT | 否 | 是否显示供应商 |
| CommentFlag | BIT | 否 | 备注标志 |
| HardCopyFlag | BIT | 否 | 硬拷贝标志 |
| ChineseReportFlag | BIT | 否 | 中文报告标志 |
| TakePhotoFlag | BIT | 否 | 拍照标志 |
| ConfirmCoverPageFlag | BIT | 否 | 确认封面页标志 |
| PackageIndicator | VARCHAR(10) | 否 | 包装指示符 |
| TakePhotoRemark | VARCHAR(500) | 否 | 拍照备注 |
| ReturnResidueSampleFlag | BIT | 否 | 是否返还残留样品 |
| ReturnTestedSampleFlag | BIT | 否 | 是否返还已测样品 |
| ReturnResidueSampleRemark | VARCHAR(500) | 否 | 返还残留样品备注 |
| ReturnTestedSampleRemark | VARCHAR(500) | 否 | 返还已测样品备注 |
| ReportAccreditationNeededFlag | BIT | 否 | 报告是否需要认证信息 |
| HardCopyToApplicantFlag | BIT | 否 | 是否将硬拷贝发送给申请人 |
| HardCopyToPayertFlag | BIT | 否 | 是否将硬拷贝发送给付款人 |
| HardCopyToOther | VARCHAR(500) | 否 | 其他硬拷贝发送地址 |
| SoftCopyToApplicantFlag | BIT | 否 | 是否将软拷贝发送给申请人 |
| SoftCopyToPayerFlag | BIT | 否 | 是否将软拷贝发送给付款人 |
| SoftCopyToOther | VARCHAR(500) | 否 | 其他软拷贝发送地址 |
| HtmlString | VARCHAR(10000) | 否 | HTML字符串 |
| PdfReportSecurity | BIT | 否 | PDF报告安全 |
| ActiveIndicator | BIT | 是 | 活动指示符（0:非活动, 1:活动） |
| CreatedBy | VARCHAR(50) | 否 | 创建者 |
| CreatedDate | TIMESTAMP(19) | 否 | 创建日期 |
| ModifiedBy | VARCHAR(50) | 否 | 修改者 |
| ModifiedDate | TIMESTAMP(19) | 否 | 修改日期 |
| PaymentRemark | VARCHAR(500) | 否 | 付款备注 |
| LastModifiedTimestamp | TIMESTAMP(19) | 是 | 最后修改时间戳 |
| QualificationType | VARCHAR(50) | 否 | 认证请求类型 |
| DraftReportRequired | TINYINT(3) | 否 | 是否需要草稿报告 |
| ProformaInvoice | TINYINT(3) | 否 | 形式发票 |
| LiquidTestSample | TINYINT(3) | 否 | 液体测试样品 |
| VatType | TINYINT(3) | 否 | 增值税类型 |
| PhotoRequest | VARCHAR(250) | 否 | 拍照请求 |
| NeedConclusion | BIT | 否 | 是否需要结论 |
| HardCopyReportDeliverWay | VARCHAR(500) | 否 | 硬拷贝报告交付方式 |
| InvoiceDeliverWay | VARCHAR(500) | 否 | 发票交付方式 |
| SubcontractFee | DECIMAL(18,5) | 否 | 分包费用 |
| SubcontractFeeCurrency | VARCHAR(50) | 否 | 分包费用货币 |
| QrcodeFlag | VARCHAR(500) | 否 | 二维码标志 |
| ConvertInHouseMethodFlag | INTEGER | 否 | 转换内部方法标志 |

### 主外键关系
- **主键**: `ID` 字段作为主键
- **外键**: 
  - `GeneralOrderInstanceID` 外键关联 `tb_general_order_instance` 表的 `ID` 字段
  - `SubContractID` 外键关联分包主表的 `ID` 字段

### 索引策略
- 主键索引: `ID`
- 普通索引: `SubContractNo`, `GeneralOrderInstanceID`
- 复合索引: 根据查询需求可能存在的组合索引

**Section sources**
- [testline_init.sql](file://uni-otsnotes/doc/db/testline_init.sql)
- [SubcontractRequirementPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubcontractRequirementPO.java)

## ORM映射分析
分包需求实体通过MyBatis框架进行ORM映射，实现了数据库表与Java对象之间的转换。

### PO类映射
`SubcontractRequirementPO`类是分包需求实体的持久化对象，与`tb_subcontract_requirement`表一一对应。

```java
public class SubcontractRequirementPO {
    private String ID;
    private String generalOrderInstanceID;
    private String subContractID;
    private String subContractNo;
    // 其他字段...
}
```

### Info类映射
`SubcontractRequirementInfo`类是分包需求实体的业务信息对象，用于在服务层和接口层之间传递数据。

```java
public class SubcontractRequirementInfo extends BaseProductLine {
    private String ID;
    private String generalOrderInstanceID;
    private String subContractID;
    private String subContractNo;
    // 其他字段...
}
```

### 映射关系特点
1. **字段类型映射**: 数据库字段与Java字段类型一一对应
2. **命名规范**: 数据库字段使用下划线命名法，Java字段使用驼峰命名法
3. **数据类型转换**: MyBatis自动处理基本数据类型和复杂数据类型的转换
4. **空值处理**: 所有字段都进行了空值检查和处理

**Section sources**
- [SubcontractRequirementPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubcontractRequirementPO.java)
- [SubcontractRequirementInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/subcontract/SubcontractRequirementInfo.java)

## MyBatis SQL映射
MyBatis映射文件定义了分包需求实体的CRUD操作和复杂查询。

### SQL映射文件结构
```xml
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.extmapper.SubContractRequirementExtMapper">
    <resultMap id="BaseResultMap" type="com.sgs.otsnotes.facade.model.info.subcontract.SubcontractRequirementInfo">
        <!-- 字段映射定义 -->
    </resultMap>
    
    <sql id="Base_Column_List">
        <!-- 基础字段列表 -->
    </sql>
    
    <insert id="batchInsert" parameterType="java.util.List">
        <!-- 批量插入SQL -->
    </insert>
    
    <select id="getSubRequirementBySubContractIds" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.SubcontractRequirementPO">
        <!-- 根据分包ID查询 -->
    </select>
    
    <select id="getSubcontractRequirementBySubcontractNo" resultMap="BaseResultMap">
        <!-- 根据分包编号查询 -->
    </select>
    
    <select id="getSubcontractRequirementByOrderNo" resultMap="BaseResultMap">
        <!-- 根据订单编号查询 -->
    </select>
</mapper>
```

### 查询优化技巧
1. **批量操作**: 使用`<foreach>`标签实现批量插入，提高性能
2. **结果映射**: 使用`resultMap`定义复杂的对象映射关系
3. **SQL重用**: 使用`<sql>`标签定义可重用的SQL片段
4. **条件查询**: 使用动态SQL实现灵活的查询条件
5. **分页查询**: 通过`LIMIT`关键字实现分页

### 核心SQL语句
- **批量插入**: 支持批量插入分包需求，并处理重复键更新
- **多表关联查询**: 通过LEFT JOIN关联多个表获取完整信息
- **条件查询**: 支持根据分包编号、订单编号等多种条件查询

**Section sources**
- [SubContractRequirementExtMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractRequirementExtMapper.xml)
- [SubcontractRequirementPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubcontractRequirementPO.java)

## 实体关系图
```mermaid
erDiagram
tb_subcontract_requirement {
string ID PK
string GeneralOrderInstanceID FK
string SubContractID FK
string SubContractNo
string EvaluationBasis
string OtherRequirements
string Qualification
timestamp ReoprtDate
string ReportLanguage
int ReportManner
string ReportType
string ReportRequirement
string ReportTemplate
int ReportQty
boolean ResultJudgingFlag
int ServiceType
boolean DisplaySupplierFlag
boolean CommentFlag
boolean HardCopyFlag
boolean ChineseReportFlag
boolean TakePhotoFlag
boolean ConfirmCoverPageFlag
string PackageIndicator
string TakePhotoRemark
boolean ReturnResidueSampleFlag
boolean ReturnTestedSampleFlag
string ReturnResidueSampleRemark
string ReturnTestedSampleRemark
boolean ReportAccreditationNeededFlag
boolean HardCopyToApplicantFlag
boolean HardCopyToPayertFlag
string HardCopyToOther
boolean SoftCopyToApplicantFlag
boolean SoftCopyToPayerFlag
string SoftCopyToOther
string HtmlString
boolean PdfReportSecurity
boolean ActiveIndicator
string CreatedBy
timestamp CreatedDate
string ModifiedBy
timestamp ModifiedDate
string PaymentRemark
timestamp LastModifiedTimestamp
string QualificationType
int DraftReportRequired
int ProformaInvoice
int LiquidTestSample
int VatType
string PhotoRequest
boolean NeedConclusion
string HardCopyReportDeliverWay
string InvoiceDeliverWay
decimal SubcontractFee
string SubcontractFeeCurrency
string QrcodeFlag
int ConvertInHouseMethodFlag
}
tb_general_order_instance {
string ID PK
string OrderNo
string CustomerID
timestamp CreatedDate
string CreatedBy
timestamp ModifiedDate
string ModifiedBy
}
tb_subcontract {
string ID PK
string SubContractNo
string LabCode
string Status
timestamp CreatedDate
string CreatedBy
timestamp ModifiedDate
string ModifiedBy
}
tb_test_line_instance {
string ID PK
string GeneralOrderInstanceID FK
int TestLineID
int TestLineStatus
timestamp CreatedDate
string CreatedBy
timestamp ModifiedDate
string ModifiedBy
}
tb_subcontract_requirement ||--o{ tb_general_order_instance : "关联"
tb_subcontract_requirement ||--o{ tb_subcontract : "关联"
tb_subcontract_requirement }o--|| tb_test_line_instance : "包含"
```

**Diagram sources**
- [testline_init.sql](file://uni-otsnotes/doc/db/testline_init.sql)
- [SubcontractRequirementPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubcontractRequirementPO.java)

## 数据完整性与业务规则
分包需求模型遵循严格的数据完整性约束和业务规则。

### 数据完整性约束
1. **主键约束**: `ID`字段必须唯一且非空
2. **外键约束**: 
   - `GeneralOrderInstanceID`必须存在于`tb_general_order_instance`表中
   - `SubContractID`必须存在于分包主表中
3. **非空约束**: `ID`, `GeneralOrderInstanceID`, `SubContractID`, `ActiveIndicator`等字段为必填项
4. **默认值约束**: 
   - `ActiveIndicator`默认值为1（活动）
   - `LastModifiedTimestamp`默认值为当前时间戳

### 业务规则
1. **创建规则**: 
   - 必须关联有效的主订单和分包
   - 创建时自动填充创建者和创建时间
   - 生成唯一的ID

2. **分配规则**:
   - 分包需求必须分配给特定的分包实验室
   - 分配时需要验证实验室的资质和能力
   - 分配后更新分包状态

3. **完成规则**:
   - 完成时需要验证所有必填字段是否完整
   - 生成最终报告并更新状态
   - 记录完成时间和完成者

4. **状态流转**:
   - 草稿 → 已提交 → 处理中 → 已完成 → 已关闭
   - 支持状态回退和重新处理

### 处理流程
1. **创建流程**: 用户创建分包需求 → 系统验证数据完整性 → 保存到数据库
2. **分配流程**: 管理员选择分包实验室 → 系统验证实验室资质 → 更新分包关系
3. **处理流程**: 分包实验室接收需求 → 执行测试 → 提交结果 → 生成报告
4. **完成流程**: 质量审核 → 报告确认 → 状态更新 → 通知相关方

**Section sources**
- [SubcontractRequirementPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubcontractRequirementPO.java)
- [SubContractRequirementExtMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractRequirementExtMapper.xml)

## 数据访问模式与性能优化
分包需求模型采用多种数据访问模式和性能优化策略。

### 数据访问模式
1. **直接查询**: 通过主键或唯一索引直接查询单个记录
2. **批量查询**: 根据分包ID列表批量查询多个记录
3. **关联查询**: 通过外键关联查询相关实体信息
4. **条件查询**: 支持多种条件组合的复杂查询

### 查询过滤
1. **按分包编号过滤**: 支持精确匹配和模糊匹配
2. **按订单编号过滤**: 通过关联查询实现
3. **按状态过滤**: 支持活动/非活动状态筛选
4. **按时间范围过滤**: 支持创建时间、修改时间等范围查询

### 性能优化方案
1. **索引优化**:
   - 为常用查询字段创建索引
   - 为外键字段创建索引
   - 定期分析和优化索引

2. **查询优化**:
   - 避免SELECT *，只查询必要字段
   - 使用分页查询处理大量数据
   - 优化JOIN操作，避免笛卡尔积

3. **缓存策略**:
   - 使用Redis缓存常用查询结果
   - 实现二级缓存减少数据库访问
   - 设置合理的缓存过期策略

4. **批量操作**:
   - 使用批量插入提高数据导入性能
   - 批量更新减少数据库交互次数
   - 批量删除提高删除效率

**Section sources**
- [SubContractRequirementExtMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractRequirementExtMapper.xml)
- [SubcontractRequirementPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubcontractRequirementPO.java)

## 最佳实践
为开发者提供分包需求模型使用和扩展的最佳实践建议。

### 开发最佳实践
1. **数据验证**: 在保存前进行完整的数据验证
2. **事务管理**: 确保相关操作在同一个事务中
3. **异常处理**: 捕获并处理可能出现的数据库异常
4. **日志记录**: 记录关键操作的日志信息

### 扩展建议
1. **字段扩展**: 新增字段时考虑向后兼容性
2. **索引优化**: 根据实际查询需求调整索引策略
3. **性能监控**: 定期监控查询性能并优化慢查询
4. **数据归档**: 对历史数据进行归档处理

### 接口使用规范
1. **参数校验**: 调用接口前验证参数合法性
2. **错误处理**: 正确处理接口返回的错误信息
3. **并发控制**: 在高并发场景下实现适当的锁机制
4. **资源释放**: 及时释放数据库连接等资源

**Section sources**
- [SubContractRequirementFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/SubContractRequirementFacade.java)
- [SubContractRequirementExtMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractRequirementExtMapper.xml)