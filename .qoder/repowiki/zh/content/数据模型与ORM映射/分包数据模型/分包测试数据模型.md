# 分包测试数据模型

<cite>
**本文档引用文件**  
- [SubContractTestDataInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractTestDataInfoPO.java)
- [SubContractTestDataInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/SubContractTestDataInfoMapper.xml)
- [SubContractTestDataMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractTestDataMapper.xml)
- [SubContractTestDataMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/extmapper/SubContractTestDataMapper.java)
- [SubContractTestDataInfoMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractTestDataInfoMapper.java)
</cite>

## 目录
1. [简介](#简介)
2. [数据库表结构与ORM映射](#数据库表结构与orm映射)
3. [核心字段说明](#核心字段说明)
4. [主外键关系与索引策略](#主外键关系与索引策略)
5. [MyBatis映射文件与SQL逻辑](#mybatis映射文件与sql逻辑)
6. [实体关系图（ER图）](#实体关系图er图)
7. [数据完整性约束与业务规则](#数据完整性约束与业务规则)
8. [数据访问模式与性能优化](#数据访问模式与性能优化)
9. [最佳实践建议](#最佳实践建议)

## 简介
本文档旨在全面描述分包测试数据模型的设计与实现，重点围绕`SubContractTestData`实体展开。该模型用于存储外部实验室返回的测试结果数据，涵盖测试项、检测值、单位、检测限等关键信息。文档详细说明其数据库表结构、ORM映射、SQL查询逻辑、数据完整性机制以及性能优化策略，为开发者提供集成与分析分包测试数据的权威参考。

## 数据库表结构与ORM映射

`SubContractTestData`实体通过MyBatis框架映射到数据库表`tb_subcontract_test_data`。其Java持久化对象（PO）为`SubContractTestDataInfoPO`，位于`otsnotes-dbstorages`模块中。

该实体采用标准的POJO（Plain Old Java Object）设计，包含私有字段和公共的getter/setter方法。所有字符串字段在setter方法中均进行了`trim()`处理，以确保数据一致性。时间字段使用`java.util.Date`类型。

**表结构概览：**

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
| :--- | :--- | :--- | :--- | :--- |
| `Id` | BIGINT(19) | 是 | - | 主键，自增 |
| `OrderId` | VARCHAR(36) | 是 | - | 关联的订单ID |
| `SubContractId` | VARCHAR(36) | 是 | - | 分包主记录ID |
| `ExternalNo` | VARCHAR(30) | 是 | - | 外部实验室样本编号 |
| `ExternalCode` | VARCHAR(256) | 否 | - | 外部实验室测试项代码 |
| `SampleNo` | VARCHAR(36) | 是 | - | 内部样本编号 |
| `AnalyteType` | INTEGER(10) | 是 | 0 | 分析物类型（0: 普通, 1: 结论） |
| `TestAnalyteName` | VARCHAR(255) | 否 | - | 测试项英文名称 |
| `TestAnalyteNameCN` | VARCHAR(255) | 否 | - | 测试项中文名称 |
| `ReportUnit` | VARCHAR(255) | 否 | - | 报告单位英文 |
| `ReportUnitCN` | VARCHAR(255) | 否 | - | 报告单位中文 |
| `TestValue` | VARCHAR(150) | 否 | - | 测试结果值 |
| `ActiveIndicator` | BIT | 是 | 1 | 活跃标识（0: 非活跃, 1: 活跃） |
| `CreatedDate` | TIMESTAMP(19) | 是 | - | 创建时间 |
| `ModifiedDate` | TIMESTAMP(19) | 否 | - | 修改时间 |

**Section sources**
- [SubContractTestDataInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractTestDataInfoPO.java#L4-L359)

## 核心字段说明

### 测试结果与测试方法
- **`TestValue`**: 存储测试的实际结果值，数据类型为VARCHAR(150)，可容纳数字、文本或范围值（如"<LOD"）。此设计提供了最大的灵活性以适应不同类型的测试结果。
- **`ExternalCode`**: 记录外部实验室使用的测试项编码，是与外部系统进行数据映射和校验的关键字段。

### 检测限与单位
- **`ReportUnit` / `ReportUnitCN`**: 分别存储测试结果的报告单位（英文和中文）。单位信息对于结果的正确解读至关重要。
- **检测限处理**: 虽然模型中没有显式的“检测限”字段，但`TestValue`字段的设计允许存储如"<LOD"（低于检测限）或">UOD"（高于检测限）等特殊值，这是一种常见的行业实践。

### 分析物与测试项
- **`TestAnalyteName` / `TestAnalyteNameCN`**: 存储测试项的名称，用于在系统内部展示。
- **`AnalyteType`**: 区分普通测试项和结论性测试项。值为`0`表示普通分析物，`1`表示结论项，这有助于在报告生成时进行分类处理。

### 样本与分包关联
- **`ExternalNo`**: 外部实验室的样本编号，是连接外部报告与内部订单的核心标识。
- **`SubContractId`**: 关联到分包主表（`tb_subcontract`）的ID，建立一对多的关系。
- **`OrderId`**: 关联到主订单表的ID，确保测试数据能追溯到原始订单。

## 主外键关系与索引策略

### 主键
- **`Id`**: 表的主键，为BIGINT类型，通常由数据库自动生成。

### 外键
- **`SubContractId`**: 外键，引用`tb_subcontract`表的主键，建立分包测试数据与分包主记录的关联。
- **`OrderId`**: 外键，引用主订单表的主键，建立与订单的直接关联。

### 索引策略
虽然文档中未直接提供索引定义，但根据查询模式可以推断出关键索引：
- **`idx_subcontract_id`**: 在`SubContractId`字段上建立索引，以优化`getSubContractTestDataInfoList`等按分包ID查询的性能。
- **`idx_external_no`**: 在`ExternalNo`字段上建立索引，以加速`getTestDataListByExternalNo`和`delSubContractTestData`等基于外部编号的操作。
- **`idx_order_id`**: 在`OrderId`字段上建立索引，以支持按订单查询所有分包测试数据。

## MyBatis映射文件与SQL逻辑

### 自动生成的Mapper (`SubContractTestDataInfoMapper.xml`)
此文件由MyBatis Generator生成，提供了对`tb_subcontract_test_data`表的CRUD（增删改查）基础操作。
- **`selectByExample`**: 支持复杂的条件查询，通过`SubContractTestDataInfoExample`对象构建动态WHERE子句。
- **`insertSelective`**: 只插入非空字段，避免覆盖已有数据。
- **`updateByExampleSelective` / `updateByPrimaryKeySelective`**: 支持选择性更新，仅更新指定的字段。

### 自定义Mapper (`SubContractTestDataMapper.xml`)
此文件包含为特定业务场景定制的SQL语句。
- **`batchInsert`**: 批量插入分包测试数据。使用`INSERT INTO ... VALUES ... ON DUPLICATE KEY UPDATE`语法，实现了“存在则更新，不存在则插入”的逻辑。这在处理外部实验室的完整报告时非常高效，可以一次性同步所有数据。
- **`getSubContractTestDataInfoList`**: 根据`SubContractId`查询所有相关的测试数据，是加载分包详情的核心查询。
- **`getTestDataListByExternalNo`**: 根据外部样本编号查询数据，用于数据校验和去重。
- **`updateBatchTestData`**: 批量更新操作，将指定`ExternalNo`的所有活跃记录（`ActiveIndicator=1`）标记为非活跃（`ActiveIndicator=0`），并更新修改时间。此操作常用于数据刷新前的“软删除”，为后续的`batchInsert`做准备，确保数据的最终一致性。

**Section sources**
- [SubContractTestDataInfoMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/autogenerated/SubContractTestDataInfoMapper.xml#L1-L371)
- [SubContractTestDataMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractTestDataMapper.xml#L1-L90)

## 实体关系图（ER图）

```mermaid
erDiagram
SUBCONTRACT ||--o{ SUBCONTRACT_TEST_DATA : "包含"
TEST_LINE ||--o{ SUBCONTRACT_TEST_DATA : "对应"
ANALYTE ||--o{ SUBCONTRACT_TEST_DATA : "关联"
SUBCONTRACT {
string SubContractId PK
string SubContractNo
string SubContractLabCode
datetime StartDate
datetime CompleteDate
string Status
}
SUBCONTRACT_TEST_DATA {
bigint Id PK
string OrderId FK
string SubContractId FK
string ExternalNo
string ExternalCode
string SampleNo
int AnalyteType
string TestAnalyteName
string TestAnalyteNameCN
string ReportUnit
string ReportUnitCN
string TestValue
bool ActiveIndicator
datetime CreatedDate
datetime ModifiedDate
}
TEST_LINE {
string TestLineId PK
string TestLineName
string AnalyteId FK
}
ANALYTE {
string AnalyteId PK
string AnalyteName
string Unit
}
```

**Diagram sources**
- [SubContractTestDataInfoPO.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model/SubContractTestDataInfoPO.java#L4-L359)
- [SubContractTestDataMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/SubContractTestDataMapper.xml#L1-L90)

## 数据完整性约束与业务规则

### 质量控制机制
1.  **数据校验**:
    -   通过`ExternalNo`和`ExternalCode`与外部报告进行比对，确保数据的准确性。
    -   利用`getTestDataListByExternalNo`查询来验证特定样本的数据是否已存在。
2.  **复测处理**:
    -   当收到同一`ExternalNo`的新数据时，系统会先调用`updateBatchTestData`将旧的活跃数据标记为非活跃。
    -   随后执行`batchInsert`，插入新的数据记录。
    -   这种“标记-插入”模式保证了历史数据的可追溯性（通过`ActiveIndicator`区分），同时确保了当前视图的准确性。

### 完整性约束
- **必填字段**: `Id`, `OrderId`, `SubContractId`, `ExternalNo`, `SampleNo`, `AnalyteType`, `ActiveIndicator`, `CreatedDate`等字段被标记为必填，确保了核心数据的完整性。
- **默认值**: `AnalyteType`默认为`0`（普通分析物），`ActiveIndicator`默认为`1`（活跃），减少了应用层的处理负担。
- **软删除**: 通过`ActiveIndicator`字段实现软删除，而非物理删除，满足了审计和数据恢复的需求。

## 数据访问模式与性能优化

### 访问模式
- **批量写入**: 使用`batchInsert`进行批量操作，显著减少数据库交互次数，提升数据导入效率。
- **按分包查询**: `getSubContractTestDataInfoList`是典型的按主表ID查询子表数据的模式，用于展示分包详情。
- **按外部编号查询**: `getTestDataListByExternalNo`用于快速定位特定样本的数据，支持数据校验流程。

### 性能优化方案
1.  **批量操作**: `batchInsert`和`updateBatchTestData`都是为批量处理设计的，避免了逐条操作的性能瓶颈。
2.  **索引优化**: 在`SubContractId`、`ExternalNo`和`OrderId`上建立索引，是查询性能的关键。
3.  **ON DUPLICATE KEY UPDATE**: 在`batchInsert`中使用此语法，将“查-改”或“查-增”操作合并为一步，极大地提高了数据同步的效率。
4.  **选择性更新**: 使用`updateByPrimaryKeySelective`等方法，只更新必要的字段，减少数据库I/O。

## 最佳实践建议
1.  **数据同步流程**: 在处理外部实验室数据时，应遵循“查询校验 -> 批量标记旧数据 -> 批量插入新数据”的标准流程，以保证数据一致性。
2.  **索引维护**: 定期审查和优化索引，确保`SubContractId`和`ExternalNo`等高频查询字段的索引有效性。
3.  **数据清理**: 制定策略定期归档或清理`ActiveIndicator=0`的历史数据，以控制表的大小。
4.  **异常处理**: 在调用`batchInsert`等关键方法时，应捕获并妥善处理数据库异常，确保事务的完整性。