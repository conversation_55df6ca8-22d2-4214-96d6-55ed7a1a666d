# 数据录入服务

<cite>
**本文档引用的文件**   
- [DataEntryService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataEntryService.java)
- [DataEntryNewService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataEntryNewService.java)
- [DataComparisonService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataComparisonService.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心方法分析](#核心方法分析)
3. [重构逻辑与设计考量](#重构逻辑与设计考量)
4. [异常处理机制](#异常处理机制)
5. [外部系统交互](#外部系统交互)
6. [性能与优化](#性能与优化)

## 简介
数据录入服务（DataEntryService）是OTSNotes系统中的核心模块，负责处理数据录入、结果比对、状态验证等关键业务逻辑。该服务通过与多个外部系统（如TRIMSLocal、Order、Customer等）交互，实现复杂的数据处理流程。本文档将深入分析其核心方法`getDataEntryList`和`compareAndRecordResults`的执行流程、输入输出、异常处理及与外部系统的交互方式，并详细说明重构前后的逻辑差异和设计考量。

## 核心方法分析

### getDataEntryList 方法

#### 方法职责
`getDataEntryList`方法是数据录入服务的核心入口，主要负责根据请求参数获取数据录入列表，并根据结论模式决定是否执行数据比对和记录结果。

#### 输入输出
- **输入**: `DataEntryConclusionReq`对象，包含订单号、报告ID、实验室代码、分页信息等。
- **输出**: `CustomResult<DataEntryRsp>`对象，包含数据录入列表、分页信息、订单状态等。

#### 执行流程
1. **参数验证与初始化**: 验证请求参数，获取当前用户信息和实验室代码。
2. **订单与报告信息获取**: 通过订单号获取订单结论模式和报告信息。
3. **操作类型检查**: 检查订单是否为翻译件，若是则禁止查看。
4. **结论模式判断**: 根据订单的结论模式（PP_MATRIX或其他）决定后续处理逻辑。
5. **数据查询**: 调用`dataEntryMapper.getDataEntryList`或`getDataEntryListWithoutPP`方法查询数据录入列表。
6. **异步数据比对**: 如果结论模式为PP_MATRIX，则异步调用`dataEntryNewService.compareAndRecordResults`方法进行数据比对。
7. **数据处理与排序**: 对查询结果进行排序、分页处理，并构建响应对象。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant DataEntryService as "DataEntryService"
participant DataEntryMapper as "DataEntryMapper"
participant DataEntryNewService as "DataEntryNewService"
Client->>DataEntryService : getDataEntryList(reqObject)
DataEntryService->>DataEntryService : 参数验证与初始化
DataEntryService->>DataEntryService : 获取订单与报告信息
DataEntryService->>DataEntryService : 检查操作类型
DataEntryService->>DataEntryService : 判断结论模式
alt 结论模式为PP_MATRIX
DataEntryService->>DataEntryMapper : getDataEntryList(reqObject)
DataEntryService->>DataEntryNewService : compareAndRecordResults(reqObject, conclusionMode)
else 其他结论模式
DataEntryService->>DataEntryMapper : getDataEntryListWithoutPP(reqObject)
end
DataEntryService->>DataEntryService : 数据处理与排序
DataEntryService-->>Client : 返回CustomResult<DataEntryRsp>
```

**图示来源**
- [DataEntryService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataEntryService.java#L100-L200)

**本节来源**
- [DataEntryService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataEntryService.java#L100-L200)

### compareAndRecordResults 方法

#### 方法职责
`compareAndRecordResults`方法负责比较新旧数据结果，并将比对结果生成报告。该方法是数据表迁移项目的核心部分，用于确保新旧数据的一致性。

#### 输入输出
- **输入**: `DataEntryConclusionReq`对象和结论模式。
- **输出**: 无直接输出，但会生成比对报告并记录日志。

#### 执行流程
1. **新流程开关检查**: 检查是否启用新流程，若未启用则跳过比对。
2. **获取旧方法结果**: 调用`dataEntryMapper.getDataEntryList`获取旧方法结果，并记录耗时。
3. **获取新方法结果**: 调用`getDataEntryListNew`获取新方法结果，并记录耗时。
4. **数据比对**: 使用`dataComparisonService.compareAndGenerateReport`方法比较新旧结果的MD5摘要。
5. **生成报告**: 将比对结果写入CSV文件，并记录日志。

```mermaid
sequenceDiagram
participant DataEntryNewService as "DataEntryNewService"
participant DataEntryMapper as "DataEntryMapper"
participant DataComparisonService as "DataComparisonService"
DataEntryNewService->>DataEntryNewService : 检查新流程开关
alt 新流程已启用
DataEntryNewService->>DataEntryMapper : getDataEntryList(reqObject)
DataEntryNewService->>DataEntryNewService : 记录旧方法耗时
DataEntryNewService->>DataEntryNewService : getDataEntryListNew(reqObject, conclusionMode)
DataEntryNewService->>DataEntryNewService : 记录新方法耗时
DataEntryNewService->>DataComparisonService : compareAndGenerateReport(...)
DataComparisonService->>DataComparisonService : 计算MD5摘要
DataComparisonService->>DataComparisonService : 比较结果一致性
DataComparisonService->>DataComparisonService : 写入报告文件
else 新流程未启用
DataEntryNewService->>DataEntryNewService : 跳过比对
end
```

**图示来源**
- [DataEntryNewService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataEntryNewService.java#L300-L380)

**本节来源**
- [DataEntryNewService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataEntryNewService.java#L300-L380)

## 重构逻辑与设计考量

### 重构前后的逻辑差异
1. **数据获取方式**:
   - **重构前**: 通过复杂的SQL查询从数据库直接获取所有数据。
   - **重构后**: 使用API调用替代数据库JOIN，通过`DataEntryNewService.getDataEntryListNew`方法分步获取数据。

2. **数据比对机制**:
   - **重构前**: 无系统化的数据比对机制。
   - **重构后**: 引入`DataComparisonService`，通过MD5摘要比较新旧数据结果，并生成详细的比对报告。

3. **性能优化**:
   - **重构前**: 复杂的SQL查询可能导致性能瓶颈。
   - **重构后**: 通过异步执行和API调用，提高了系统的响应速度和可扩展性。

### 设计考量
1. **解耦与模块化**: 将数据比对逻辑从`DataEntryService`中分离出来，放入独立的`DataEntryNewService`和`DataComparisonService`中，提高了代码的可维护性和可测试性。
2. **性能与可靠性**: 通过异步执行数据比对，避免了阻塞主业务流程，同时通过详细的日志记录和报告生成，确保了系统的可靠性。
3. **可扩展性**: 使用API调用替代数据库JOIN，使得系统更容易扩展到其他微服务架构中。

```mermaid
classDiagram
class DataEntryService {
+getDataEntryList(DataEntryConclusionReq) CustomResult<DataEntryRsp>
+validate(ValidateReq) CustomResult
+updateUsageType(UpdateUsageTypeReq) CustomResult
}
class DataEntryNewService {
+getDataEntryListNew(DataEntryConclusionReq, Integer) List<DataEntryInfo>
+compareAndRecordResults(DataEntryConclusionReq, Integer) void
}
class DataComparisonService {
+compareAndGenerateReport(String, List<T>, List<T>, long, long, String, String) void
-calculateMD5(List<T>) String
-writeReportEntry(ComparisonReportEntry) void
}
DataEntryService --> DataEntryNewService : "依赖"
DataEntryNewService --> DataComparisonService : "依赖"
```

**图示来源**
- [DataEntryService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataEntryService.java)
- [DataEntryNewService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataEntryNewService.java)
- [DataComparisonService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataComparisonService.java)

**本节来源**
- [DataEntryService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataEntryService.java)
- [DataEntryNewService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataEntryNewService.java)
- [DataComparisonService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataComparisonService.java)

## 异常处理机制
数据录入服务在多个关键环节都实现了完善的异常处理机制：
1. **参数验证**: 在方法入口处对请求参数进行严格验证，确保输入数据的合法性。
2. **业务逻辑异常**: 在业务逻辑处理过程中，对可能出现的异常情况进行捕获和处理，如订单不存在、测试线已取消等。
3. **外部系统调用异常**: 在调用外部系统API时，使用try-catch块捕获异常，并记录警告日志，避免因外部系统问题导致主业务流程中断。
4. **事务管理**: 使用`transactionTemplate.execute`方法确保数据库操作的原子性，若操作失败则回滚事务。

**本节来源**
- [DataEntryService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataEntryService.java)

## 外部系统交互
数据录入服务与多个外部系统进行交互，主要包括：
1. **TRIMSLocal系统**: 通过`analyteClient`、`usageTypePositionClient`、`testLineClient`等客户端调用TRIMSLocal提供的API，获取测试线、分析物、使用类型等基础信息。
2. **订单系统**: 通过`orderClient`获取订单信息，包括订单状态、客户组代码等。
3. **客户系统**: 通过`customerClient`获取客户账户信息。
4. **报告系统**: 通过`orderReportClient`获取报告语言设置等信息。

这些交互通过Spring的依赖注入机制实现，确保了服务的松耦合和高内聚。

**本节来源**
- [DataEntryService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataEntryService.java)

## 性能与优化
1. **异步处理**: 通过`CompletableFuture.runAsync`或`@Async`注解实现异步数据比对，避免阻塞主业务流程。
2. **缓存机制**: 使用`RedisHelper`进行数据缓存，减少数据库查询次数。
3. **批量操作**: 在数据库操作中使用批量插入、更新、删除方法，提高数据库操作效率。
4. **分页处理**: 使用`PageInfo`进行分页处理，避免一次性加载大量数据。

**本节来源**
- [DataEntryService.java](file://otsnotes-domain\src\main\java\com\sgs\otsnotes\domain\service\DataEntryService.java)