# 项目结构

<cite>
**本文档中引用的文件**  
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java)
- [TestLineService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineService.java)
- [OrderFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/OrderFacadeImpl.java)
- [TestLineFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/TestLineFacadeImpl.java)
- [OrderFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/OrderFacade.java)
- [TestLineFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/TestLineFacade.java)
- [mapper](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper)
- [model](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model)
</cite>

## 目录
1. [项目结构](#项目结构)
2. [核心模块职责与组织](#核心模块职责与组织)
3. [模块间依赖关系](#模块间依赖关系)
4. [代码导航示例：查找订单创建逻辑](#代码导航示例查找订单创建逻辑)
5. [总结](#总结)

## 核心模块职责与组织

本项目采用模块化设计，各模块职责清晰，组织结构合理。以下是主要模块的详细说明：

### otsnotes-core 模块
`otsnotes-core` 模块是整个项目的基础支撑模块，提供通用工具类、配置、常量、注解、缓存、Kafka消息处理、Redis操作、线程池管理等核心功能。该模块不包含业务逻辑，主要为其他模块提供基础服务。

- **annotation**: 自定义注解，如 `@ApiController`、`@NotVerifyToken` 等，用于简化开发和增强功能。
- **common**: 通用工具类，如 `UserHelper`、`MapHelper` 等。
- **config**: 项目配置类，如 `CacheConfig`、`RedisConfig`、`KafkaProducerConfig` 等。
- **constants**: 常量定义，如 `BizLogConstant`、`CacheConstants` 等。
- **enums**: 通用枚举类。
- **kafka**: Kafka消息生产者和消费者相关实现。
- **redis**: Redis配置和连接池相关实现。
- **thread**: 线程池配置和拒绝策略。
- **util**: 各种工具类，如 `StringUtil`、`DateUtils`、`HttpClientUtil` 等。
- **workbook**: Excel导出相关处理。

### otsnotes-dbstorages 模块
`otsnotes-dbstorages` 模块负责数据持久层，使用MyBatis作为ORM框架，封装了与数据库交互的所有操作。

- **comparator**: 自定义比较器，用于排序。
- **config**: 数据源配置，支持多数据源和动态数据源切换。
- **enums**: 数据库相关的枚举类型。
- **extmapper**: 扩展的MyBatis Mapper接口，包含复杂的SQL查询。
- **extmodel**: 扩展的MyBatis Model类，与 `extmapper` 对应。
- **mapper**: 标准的MyBatis Mapper接口。
- **model**: 标准的MyBatis Model类，与数据库表一一对应。

### otsnotes-domain 模块
`otsnotes-domain` 模块是项目的业务逻辑核心，实现了领域驱动设计（DDD）中的领域服务和业务规则。该模块包含了所有核心业务逻辑，是项目中最关键的部分。

- **convertor**: 数据转换器，用于在不同数据结构之间进行转换。
- **domainservice**: 领域服务，处理跨多个实体的复杂业务逻辑。
- **dto**: 数据传输对象，用于在不同层之间传递数据。
- **kafka**: Kafka消息消费者，处理来自Kafka的消息。
- **service**: 业务服务层，每个子包对应一个业务领域，如 `analyte`、`conclusion`、`copy`、`job`、`report`、`sample`、`testline` 等。
- **utils**: 业务相关的工具类。
- **xxljob**: XXL-JOB定时任务处理器。

**Section sources**
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java)
- [TestLineService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineService.java)

### otsnotes-facade 模块
`otsnotes-facade` 模块定义了服务接口，为 `otsnotes-facade-impl` 模块提供契约。该模块仅包含接口定义，不包含任何实现代码，确保了接口的稳定性和可维护性。

- **ConclusionFacade.java**: 结论相关服务接口。
- **CopyMatrixFacade.java**: 复制矩阵相关服务接口。
- **DataEntryFacade.java**: 数据录入相关服务接口。
- **OrderFacade.java**: 订单相关服务接口。
- **ReportFacade.java**: 报告相关服务接口。
- **TestLineFacade.java**: 测试线相关服务接口。
- **SubContractFacade.java**: 分包相关服务接口。

**Section sources**
- [OrderFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/OrderFacade.java)
- [TestLineFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/TestLineFacade.java)

### otsnotes-facade-impl 模块
`otsnotes-facade-impl` 模块实现了 `otsnotes-facade` 模块中定义的服务接口，是业务逻辑的具体实现。该模块依赖于 `otsnotes-domain` 模块，通过调用领域服务来完成业务功能。

- **ConclusionFacadeImpl.java**: 结论相关服务接口的实现。
- **CopyMatrixFacadeImpl.java**: 复制矩阵相关服务接口的实现。
- **DataEntryFacadeImpl.java**: 数据录入相关服务接口的实现。
- **OrderFacadeImpl.java**: 订单相关服务接口的实现。
- **ReportFacadeImpl.java**: 报告相关服务接口的实现。
- **TestLineFacadeImpl.java**: 测试线相关服务接口的实现。
- **SubContractFacadeImpl.java**: 分包相关服务接口的实现。

**Section sources**
- [OrderFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/OrderFacadeImpl.java)
- [TestLineFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/TestLineFacadeImpl.java)

### otsnotes-web 模块
`otsnotes-web` 模块是项目的Web层，负责处理HTTP请求，提供RESTful API接口。该模块依赖于 `otsnotes-facade` 模块，通过调用服务接口来响应客户端请求。

- **controllers**: 控制器类，处理HTTP请求。
- **aop**: AOP切面，用于日志记录、权限校验等。
- **config**: Web层配置，如Swagger、Redis、XXL-Job等。
- **OtsNotesApplication.java**: Spring Boot应用启动类。

## 模块间依赖关系

项目各模块之间的依赖关系清晰，遵循了分层架构的原则，确保了模块间的低耦合和高内聚。

```mermaid
graph TD
otsnotes-web --> otsnotes-facade-impl
otsnotes-facade-impl --> otsnotes-domain
otsnotes-domain --> otsnotes-dbstorages
otsnotes-domain --> otsnotes-core
otsnotes-facade-impl --> otsnotes-facade
otsnotes-web --> otsnotes-core
otsnotes-web --> otsnotes-facade
```

**Diagram sources**
- [OrderFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/OrderFacadeImpl.java)
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java)
- [mapper](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper)
- [model](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/model)

- `otsnotes-web` 模块依赖于 `otsnotes-facade-impl` 模块，通过调用服务接口来处理业务逻辑。
- `otsnotes-facade-impl` 模块依赖于 `otsnotes-domain` 模块，通过调用领域服务来实现业务功能。
- `otsnotes-domain` 模块依赖于 `otsnotes-dbstorages` 模块，通过MyBatis进行数据库操作。
- `otsnotes-domain` 模块依赖于 `otsnotes-core` 模块，使用其提供的通用工具和配置。
- `otsnotes-facade-impl` 模块依赖于 `otsnotes-facade` 模块，实现其定义的接口。
- `otsnotes-web` 模块依赖于 `otsnotes-core` 模块，使用其提供的Web层配置和工具。
- `otsnotes-web` 模块依赖于 `otsnotes-facade` 模块，通过接口调用服务。

## 代码导航示例：查找订单创建逻辑

为了帮助开发者快速定位特定功能的代码位置，以下以查找订单创建逻辑为例，演示如何在复杂的项目结构中高效地进行代码探索。

1. **从Web层开始**：首先，从 `otsnotes-web` 模块的控制器类 `OrderController.java` 开始，查找处理订单创建请求的方法。
2. **进入服务接口**：在 `OrderController.java` 中，找到调用 `OrderFacade` 接口的 `createOrderInfo` 方法。
3. **查看接口实现**：进入 `otsnotes-facade-impl` 模块的 `OrderFacadeImpl.java`，找到 `createOrderInfo` 方法的实现。
4. **深入业务逻辑**：在 `OrderFacadeImpl.java` 中，`createOrderInfo` 方法调用了 `OrderService` 的 `createOrderInfo` 方法。进入 `otsnotes-domain` 模块的 `OrderService.java`，查看具体的业务逻辑实现。
5. **查看数据操作**：在 `OrderService.java` 中，`createOrderInfo` 方法调用了 `orderMapper` 的 `saveOrderInfo` 方法。进入 `otsnotes-dbstorages` 模块的 `OrderMapper.java` 和 `OrderInfoPO.java`，查看数据库操作的具体实现。

通过以上步骤，开发者可以快速地从Web层一直追踪到数据层，全面了解订单创建逻辑的实现过程。

**Section sources**
- [OrderFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/OrderFacadeImpl.java)
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java)

## 总结

本文档详细介绍了项目的目录结构、各模块的职责和内部组织、模块间的依赖关系，并通过具体的例子演示了如何在复杂的项目结构中高效地进行代码探索。希望本文档能为开发者提供一个清晰的导航图，帮助他们快速定位特定功能的代码位置，提高开发效率。