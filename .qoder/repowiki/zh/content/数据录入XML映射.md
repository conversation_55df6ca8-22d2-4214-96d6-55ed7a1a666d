# 数据录入XML映射

<cite>
**本文档引用的文件**  
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml)
- [DataEntryInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/dataentry/DataEntryInfo.java)
- [DataEntryMatrixInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/dataentry/DataEntryMatrixInfo.java)
- [DataEntryConclusionReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/req/dataentry/DataEntryConclusionReq.java)
- [DataEntryMatrixReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/req/dataentry/DataEntryMatrixReq.java)
- [DataEntryFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/DataEntryFacade.java)
- [DataEntryFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/DataEntryFacadeImpl.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心映射关系](#核心映射关系)
3. [查询方法分析](#查询方法分析)
4. [新旧映射差异与迁移方案](#新旧映射差异与迁移方案)
5. [数据结构说明](#数据结构说明)

## 简介
本文档详细说明了数据录入模块中MyBatis XML映射文件 `DataEntryMapper.xml` 的字段映射关系、查询语句参数与返回结构，以及新旧映射的差异和迁移方案。该模块主要用于获取测试线实例的数据录入信息，支持结论录入和数据展示功能。

## 核心映射关系

### BaseResultMap 字段映射
`BaseResultMap` 是基础结果映射，用于 `getDataEntryList` 查询，映射到 `DataEntryInfo` 对象。

```mermaid
classDiagram
class DataEntryInfo {
+String orderId
+String orderNo
+String testLineInstanceId
+Integer testLineId
+Integer testLineStatus
+Date validateDate
+String validateBy
+Integer testLineVersionId
+String testLineEvaluation
+String evaluationAlias
+String standardName
+Integer citationSectionId
+String citationSectionName
+String jobNo
+String labSectionCode
+Integer labSectionSeq
+Integer testLineStyle
+String subContractLab
+String subContractNo
+String subContractName
+Integer citationBaseId
+List<PpTestLineRelCitationInfo> ppArtifactRelIds
+List<String> ppName
+List<DataEntryMatrixInfo> matrixs
}
```

**图示来源**  
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml#L10-L85)
- [DataEntryInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/dataentry/DataEntryInfo.java)

### DataEntryListResultMap 映射
该映射用于 `getDataEntryListWithoutPP` 查询，与 `BaseResultMap` 基本一致，但 `matrixs` 集合使用 `getDataEntryMatrixListWithoutPP` 子查询。

**本节来源**  
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml#L87-L155)

### DataEntryListNewResultMap 映射
新版本映射，用于 `getDataEntryListNew` 查询，适配表拆分后的结构。

```mermaid
classDiagram
class DataEntryListNewResultMap {
+String testLineInstanceId
+String orderId
+String orderNo
+Integer testLineId
+Integer testLineStatus
+Date validateDate
+String validateBy
+Integer citationBaseId
+Long testLineBaseId
+Long labSectionBaseId
+String jobNo
+Integer testLineStyle
+String subContractLab
+String subContractNo
+String subContractName
+List<Long> ppBaseIds
+List<PpTestLineRelCitationInfo> ppArtifactRelIds
+List<DataEntryMatrixInfo> matrixs
}
```

**图示来源**  
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml#L157-L210)

## 查询方法分析

### getDataEntryList 查询
获取数据录入列表的主查询方法。

**参数类型**  
- `DataEntryConclusionReq`：包含订单号、作业号、实验室分区代码、测试线状态等条件

**返回结构**  
- `BaseResponse<DataEntryRsp>`：封装分页结果的响应对象

**SQL 逻辑**  
- 关联 `tb_test_line_instance`、`tb_general_order_instance`、`tb_job` 等多张表
- 支持按作业号、实验室分区、测试线状态等条件过滤
- 排除测试线类型为1和状态为707的记录

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Facade as "DataEntryFacade"
participant Service as "DataEntryService"
participant Mapper as "DataEntryMapper"
Client->>Facade : getDataEntryList(req)
Facade->>Service : getDataEntryList(req)
Service->>Mapper : getDataEntryList(req)
Mapper-->>Service : List<DataEntryInfo>
Service-->>Facade : DataEntryRsp
Facade-->>Client : BaseResponse<DataEntryRsp>
```

**本节来源**  
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml#L212-L350)
- [DataEntryFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/DataEntryFacade.java#L18)
- [DataEntryFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/DataEntryFacadeImpl.java#L40-L43)

### getDataEntryListNew 查询
新的数据录入列表查询方法，通过API获取外部表数据。

**特点**  
- 简化了表关联，移除了对 `tb_trims_testline_baseinfo` 和 `tb_trims_labsection_baseinfo` 的直接依赖
- 使用 `testLineBaseId` 和 `labSectionBaseId` 替代旧的ID字段
- 为支持表拆分迁移而设计

**本节来源**  
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml#L352-L410)

### getDataEntryMatrixList 查询
获取测试矩阵列表，用于填充 `DataEntryInfo` 中的 `matrixs` 集合。

**参数类型**  
- `DataEntryMatrixReq`：包含订单号、报告ID、测试线实例ID、结论模式等

**返回结构**  
- `List<DataEntryMatrixInfo>`：测试矩阵信息列表

**本节来源**  
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml#L522-L580)

## 新旧映射差异与迁移方案

### 主要差异对比

| 字段/特性 | 旧映射 (BaseResultMap) | 新映射 (DataEntryListNewResultMap) |
|---------|----------------------|-------------------------------|
| **测试线基础ID** | testLineVersionId (Integer) | testLineBaseId (Long) |
| **实验室分区基础ID** | labSectionCode + labSectionSeq | labSectionBaseId (Long) |
| **PP基础信息** | ppName (String) | ppBaseIds (List<Long>) |
| **表关联** | 直接关联基础信息表 | 通过关系表间接获取 |
| **性能** | 多表JOIN，性能较低 | 简化关联，性能提升 |

### 迁移方案
1. **字段兼容**：在 `DataEntryInfo` 类中同时保留新旧字段，确保向后兼容
2. **双写模式**：在迁移期间同时维护新旧数据结构
3. **逐步切换**：先切换查询接口，再逐步迁移数据存储
4. **数据校验**：添加数据一致性校验机制
5. **监控告警**：监控新旧接口的性能和错误率

**本节来源**  
- [DataEntryMapper.xml](file://otsnotes-dbstorages/src/main/resources/sqlmap/userdefined/DataEntryMapper.xml)
- [DataEntryInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/dataentry/DataEntryInfo.java)

## 数据结构说明

### DataEntryInfo 结构
主数据录入信息对象，包含测试线实例的元数据和关联的矩阵信息。

**关键字段**  
- `testLineInstanceId`：测试线实例唯一标识
- `matrixs`：关联的测试矩阵列表
- `ppArtifactRelIds`：PP与测试线的关系ID列表
- `ppBaseIds`：PP基础信息ID列表（新字段）

### DataEntryMatrixInfo 结构
测试矩阵信息对象，表示测试样本与测试线的关系。

**关键字段**  
- `testMatrixId`：测试矩阵ID
- `sampleId`：样本ID
- `conclusionDesc`：结论描述
- `usageTypes`：使用类型列表
- `sampleReferDataFlag`：样本参考数据标志

**本节来源**  
- [DataEntryInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/dataentry/DataEntryInfo.java)
- [DataEntryMatrixInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/dataentry/DataEntryMatrixInfo.java)