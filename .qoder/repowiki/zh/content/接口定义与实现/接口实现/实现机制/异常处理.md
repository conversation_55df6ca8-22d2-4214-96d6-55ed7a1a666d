# 异常处理

<cite>
**本文档引用的文件**   
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)
- [S3_OldOtsNotesAspect.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/aop/S3_OldOtsNotesAspect.java)
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java)
- [CustomResult.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/CustomResult.java)
- [SubReportServiceImpl.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubReportServiceImpl.java)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)
- [SampleService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SampleService.java)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 简介
本文档详细阐述了系统中异常处理机制的设计与实现。该机制基于自定义异常类`BizException`和标准化响应码`ResponseCode`，通过全局异常处理器`GlobalExceptionHandler`和AOP切面`S3_OldOtsNotesAspect`实现统一的异常捕获、日志记录和响应格式化。文档深入分析了异常处理的完整流程，包括异常的抛出、捕获、处理和响应，以及在领域服务和facade实现中的具体应用。

## 项目结构
系统采用分层架构，异常处理相关的核心组件分布在`otsnotes-facade-model`、`otsnotes-web`和`otsnotes-domain`模块中。`otsnotes-facade-model`模块定义了`BizException`和`ResponseCode`等基础模型，`otsnotes-web`模块实现了全局异常处理和AOP切面，`otsnotes-domain`模块则在业务逻辑中抛出和处理异常。

```mermaid
graph TD
subgraph "otsnotes-facade-model"
BizException[BizException]
ResponseCode[ResponseCode]
BaseResponse[BaseResponse]
CustomResult[CustomResult]
end
subgraph "otsnotes-web"
GlobalExceptionHandler[GlobalExceptionHandler]
S3_OldOtsNotesAspect[S3_OldOtsNotesAspect]
end
subgraph "otsnotes-domain"
SubReportServiceImpl[SubReportServiceImpl]
SubContractService[SubContractService]
SampleService[SampleService]
end
BizException --> GlobalExceptionHandler
ResponseCode --> BizException
BaseResponse --> GlobalExceptionHandler
CustomResult --> S3_OldOtsNotesAspect
SubReportServiceImpl --> BizException
SubContractService --> BizException
SampleService --> BizException
```

**图示来源**
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)
- [S3_OldOtsNotesAspect.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/aop/S3_OldOtsNotesAspect.java)
- [SubReportServiceImpl.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubReportServiceImpl.java)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)
- [SampleService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SampleService.java)

**本节来源**
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)

## 核心组件
异常处理机制的核心组件包括`BizException`、`ResponseCode`、`GlobalExceptionHandler`和`S3_OldOtsNotesAspect`。`BizException`是自定义业务异常类，继承自`RuntimeException`，包含`ResponseCode`作为错误码。`ResponseCode`是一个枚举类，定义了系统中所有标准化的响应码和对应的消息。`GlobalExceptionHandler`是Spring MVC的全局异常处理器，负责捕获和处理未被业务代码捕获的异常。`S3_OldOtsNotesAspect`是一个AOP切面，用于在方法执行前后进行异常处理和日志记录。

**本节来源**
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)
- [S3_OldOtsNotesAspect.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/aop/S3_OldOtsNotesAspect.java)

## 架构概述
系统异常处理架构采用分层设计，从下到上依次为：业务逻辑层、AOP切面层、全局异常处理器层和响应层。业务逻辑层在发现业务规则违反或数据校验失败时，主动抛出`BizException`。AOP切面层在方法执行过程中捕获`BizException`和其他已知异常，进行日志记录和响应构建。全局异常处理器层捕获所有未被AOP切面处理的异常，进行统一的日志记录和响应返回。响应层使用`BaseResponse`和`CustomResult`类来封装响应数据，确保响应格式的标准化。

```mermaid
sequenceDiagram
participant 业务逻辑 as 业务逻辑层
participant AOP切面 as AOP切面层
participant 全局处理器 as 全局异常处理器
participant 响应 as 响应层
业务逻辑->>AOP切面 : 调用方法
AOP切面->>业务逻辑 : 执行业务逻辑
业务逻辑->>AOP切面 : 抛出BizException
AOP切面->>AOP切面 : 捕获BizException，记录日志
AOP切面->>响应 : 构建BaseResponse
响应-->>AOP切面 : 返回响应
AOP切面-->>业务逻辑 : 返回响应
业务逻辑->>AOP切面 : 调用方法
AOP切面->>业务逻辑 : 执行业务逻辑
业务逻辑->>AOP切面 : 抛出未知异常
AOP切面->>全局处理器 : 未捕获，传递异常
全局处理器->>全局处理器 : 捕获异常，记录日志
全局处理器->>响应 : 构建BaseResponse
响应-->>全局处理器 : 返回响应
全局处理器-->>AOP切面 : 返回响应
AOP切面-->>业务逻辑 : 返回响应
```

**图示来源**
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)
- [S3_OldOtsNotesAspect.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/aop/S3_OldOtsNotesAspect.java)
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java)

## 详细组件分析
### BizException类分析
`BizException`是系统中所有业务异常的基类，它继承自`RuntimeException`，允许在不强制捕获的情况下传播异常。该类包含一个`ResponseCode`类型的`errorCode`字段，用于指定异常的具体类型。`BizException`提供了多个构造函数，支持不同的异常创建方式，包括仅指定错误码、指定错误码和消息、以及指定错误码、消息和根本原因。

```mermaid
classDiagram
class BizException {
-ResponseCode errorCode
+BizException(ResponseCode, String)
+BizException(String)
+BizException(ResponseCode)
+BizException(String, Throwable)
+BizException(ResponseCode, String, Throwable)
+ResponseCode getErrorCode()
+static void throwBizException(ResponseCode, String)
+static void throwBizException(ResponseCode, String, Throwable)
}
class RuntimeException {
+RuntimeException(String)
+RuntimeException(String, Throwable)
}
BizException --|> RuntimeException
```

**图示来源**
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)

**本节来源**
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)

### ResponseCode枚举分析
`ResponseCode`枚举类定义了系统中所有标准化的响应码和对应的消息。每个枚举实例包含一个整数类型的`code`和一个字符串类型的`message`。系统通过`getByCode`方法可以根据响应码获取对应的枚举实例，实现了响应码的集中管理和标准化。

```mermaid
classDiagram
class ResponseCode {
+SUCCESS(200, "操作成功")
+ILLEGAL_ARGUMENT(100, "错误的请求参数")
+ErrDuplicateReq(197, "重复请求")
+FAIL(198, "请求处理失败")
+TokenExpire(201, "用户权限过期，请重新登录")
+UNKNOWN(500, "系统异常，请稍后重试")
-int code
-String message
+int getCode()
+String getMessage()
+static ResponseCode getByCode(int)
}
```

**图示来源**
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)

**本节来源**
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)

### GlobalExceptionHandler分析
`GlobalExceptionHandler`是Spring MVC的全局异常处理器，使用`@ControllerAdvice`注解标记，使其能够捕获整个应用中的异常。该类定义了多个`@ExceptionHandler`方法，分别处理不同类型的异常。例如，`handleClientAbortException`方法处理客户端主动关闭连接的异常，`handleClassCastException`方法处理类型转换异常，`handleDBException`方法处理数据库相关的异常。

```mermaid
classDiagram
class GlobalExceptionHandler {
+handleClientAbortException(ClientAbortException) void
+handleClassCastException(ClassCastException) Object
+handleDBException(Throwable) Object
+handleThrowable(Throwable) Object
}
class ClientAbortException {
}
class ClassCastException {
}
class CommunicationsException {
}
class RecoverableDataAccessException {
}
class DeadlockLoserDataAccessException {
}
class Throwable {
}
GlobalExceptionHandler --> ClientAbortException : 处理
GlobalExceptionHandler --> ClassCastException : 处理
GlobalExceptionHandler --> CommunicationsException : 处理
GlobalExceptionHandler --> RecoverableDataAccessException : 处理
GlobalExceptionHandler --> DeadlockLoserDataAccessException : 处理
GlobalExceptionHandler --> Throwable : 处理
```

**图示来源**
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)

**本节来源**
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)

### S3_OldOtsNotesAspect分析
`S3_OldOtsNotesAspect`是一个AOP切面，使用`@Around`注解定义了一个环绕通知，该通知在所有被`executeJoinPoint`切点表达式匹配的方法执行前后执行。在`around`方法中，切面捕获了`IllegalArgumentException`、`BizException`和`Throwable`三种异常。对于`BizException`，切面会根据其`errorCode`构建相应的错误响应，并记录INFO级别的日志；对于其他异常，则构建未知错误响应，并记录ERROR级别的日志。

**本节来源**
- [S3_OldOtsNotesAspect.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/aop/S3_OldOtsNotesAspect.java)

## 依赖分析
异常处理机制的各个组件之间存在紧密的依赖关系。`BizException`依赖于`ResponseCode`来定义错误码，`GlobalExceptionHandler`和`S3_OldOtsNotesAspect`都依赖于`BizException`来捕获业务异常，`GlobalExceptionHandler`还依赖于`BaseResponse`来构建响应。`SubReportServiceImpl`、`SubContractService`和`SampleService`等业务服务类则直接依赖于`BizException`来抛出业务异常。

```mermaid
graph TD
BizException --> ResponseCode
GlobalExceptionHandler --> BizException
GlobalExceptionHandler --> BaseResponse
S3_OldOtsNotesAspect --> BizException
S3_OldOtsNotesAspect --> BaseResponse
SubReportServiceImpl --> BizException
SubContractService --> BizException
SampleService --> BizException
```

**图示来源**
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)
- [S3_OldOtsNotesAspect.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/aop/S3_OldOtsNotesAspect.java)
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java)
- [SubReportServiceImpl.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubReportServiceImpl.java)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)
- [SampleService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SampleService.java)

**本节来源**
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)
- [S3_OldOtsNotesAspect.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/aop/S3_OldOtsNotesAspect.java)
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java)

## 性能考虑
异常处理机制对系统性能的影响主要体现在异常抛出和捕获的开销上。`BizException`的构造函数中包含了对`errorCode`的非空检查，这会增加少量的性能开销，但可以避免后续的空指针异常。`GlobalExceptionHandler`和`S3_OldOtsNotesAspect`中的日志记录操作也会影响性能，特别是在高并发场景下。建议在生产环境中将日志级别设置为适当的级别，以平衡调试信息和性能。

## 故障排除指南
当系统出现异常时，首先应检查日志文件，确定异常的类型和堆栈信息。如果异常是`BizException`，则可以根据`errorCode`快速定位问题。如果异常是其他类型，则需要根据堆栈信息分析根本原因。此外，还可以通过调试工具逐步执行代码，观察变量的值和程序的执行流程，以找出问题所在。

**本节来源**
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)
- [S3_OldOtsNotesAspect.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/aop/S3_OldOtsNotesAspect.java)

## 结论
本文档详细介绍了系统中异常处理机制的设计与实现。该机制通过`BizException`和`ResponseCode`实现了业务异常的标准化，通过`GlobalExceptionHandler`和`S3_OldOtsNotesAspect`实现了异常的统一捕获和处理。这种分层设计不仅提高了代码的可维护性和可读性，还确保了系统在出现异常时能够提供一致的、用户友好的响应。