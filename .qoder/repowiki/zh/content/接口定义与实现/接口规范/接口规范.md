# 接口规范

<cite>
**本文档引用的文件**  
- [OrderFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/OrderFacade.java)
- [ReportFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/ReportFacade.java)
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java)
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)
- [OrderDetailReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/req/order/OrderDetailReq.java)
- [OrderDetailCommonRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/order/OrderDetailCommonRsp.java)
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)
- [HealthController.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/controllers/HealthController.java)
- [DataEntryController.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/controllers/DataEntryController.java)
- [ReportService.md](file://doc/api/ReportService.md)
- [StarLimsCommonService.md](file://doc/api/StarLimsCommonService.md)
- [QuotationTestLineInfoRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/starlims/QuotationTestLineInfoRsp.java) - *新增于最近提交*
- [QuotationTestLineAnalyteInfoRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/starlims/QuotationTestLineAnalyteInfoRsp.java) - *新增于最近提交*
- [GpoAnalyteBuilderBizProcess.java](file://otsnotes-subcontract/otsnotes-subcontract-gpo/src/main/java/com/sgs/otsnotes/subcontract/app/tostarlims/gpo/bizprocess/GpoAnalyteBuilderBizProcess.java) - *新增功能实现*
- [StarLimsFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/StarLimsFacade.java)
- [StarLimsFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/StarLimsFacadeImpl.java)
</cite>

## 更新摘要
**变更内容**  
- 新增了与StarLims报价相关的响应数据模型文档说明
- 扩展了DTO设计原则分析部分，包含新的报价相关DTO结构
- 增加了StarLimsFacade接口的分析内容
- 更新了详细组件分析中的DTO设计原则部分
- 添加了新的数据模型类作为文档引用源

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概览](#架构概览)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考量](#性能考量)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 引言
本文档旨在全面阐述otsnotes-service的API设计规范，重点介绍facade层接口的设计原则、DTO结构、统一响应格式、错误处理机制以及接口版本管理策略。文档为初学者提供API设计的基本概念，同时为开发者提供具体的接口使用示例和最佳实践。通过分析代码结构和相关文档，本文档将揭示系统的接口契约和设计哲学。

## 项目结构
otsnotes-service项目采用典型的分层微服务架构，各模块职责清晰，便于维护和扩展。核心模块包括facade、domain、core等，其中facade模块定义了对外暴露的API契约。

```mermaid
graph TD
subgraph "API层"
facade[otsnotes-facade]
facade_impl[otsnotes-facade-impl]
facade_model[otsnotes-facade-model]
end
subgraph "业务层"
domain[otsnotes-domain]
core[otsnotes-core]
end
subgraph "基础设施层"
infra[otsnotes-infra]
dbstorages[otsnotes-dbstorages]
end
subgraph "集成层"
integration[otsnotes-integration]
subcontract[otsnotes-subcontract]
end
subgraph "Web层"
web[otsnotes-web]
end
web --> facade
facade --> facade_impl
facade_impl --> domain
domain --> core
domain --> infra
domain --> dbstorages
integration --> domain
subcontract --> domain
```

**图示来源**
- [项目结构](file://README.md)

**本节来源**
- [项目结构](file://doc/项目结构.md)

## 核心组件
核心组件包括facade接口、统一响应结构、DTO数据传输对象和错误处理机制。这些组件共同构成了系统的API契约基础。

**本节来源**
- [OrderFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/OrderFacade.java)
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java)

## 架构概览
系统采用分层架构，从上至下分别为Web层、Facade层、Domain层和基础设施层。Web层负责HTTP请求的接收和路由，Facade层定义了服务的API契约，Domain层包含核心业务逻辑，基础设施层提供数据访问和外部系统集成能力。

```mermaid
graph TD
Client[客户端] --> Web[Web层]
Web --> Facade[Facade层]
Facade --> Domain[Domain层]
Domain --> Infra[基础设施层]
Domain --> DB[(数据库)]
Domain --> External[外部系统]
style Web fill:#f9f,stroke:#333
style Facade fill:#bbf,stroke:#333
style Domain fill:#f96,stroke:#333
style Infra fill:#9f9,stroke:#333
```

**图示来源**
- [系统架构](file://doc/系统架构.md)

## 详细组件分析
### Facade层接口分析
Facade层接口定义了服务对外暴露的API契约，采用Java接口形式，遵循统一的设计规范。

#### 接口命名规范
接口名称以"Fascade"结尾，如`OrderFacade`、`ReportFacade`，清晰表明其作为服务门面的角色。方法命名采用动词+名词的形式，如`createOrderInfo`、`getReportSimplifyInfo`，直观表达操作意图。

```mermaid
classDiagram
class OrderFacade {
+BaseResponse createOrderInfo(SyncOrderInfo)
+BaseResponse syncOrderInfo(SyncOrderInfo)
+BaseResponse getOrderInfoForEM(OrderInfoForEMReq)
+BaseResponse~OrderInfo~ getOrderInfo(OrderInfoForEMReq)
+BaseResponse~QueryOrderNoByObjectRsp~ getOrderInfoByJobAndSubcontractNo(QueryOrderNoByObjectReq)
+BaseResponse getANTATestInfo(ANTAInfoReq)
+BaseResponse getSemirReport(SemirReportReq)
+BaseResponse deleteOriginalSample(DeleteOriginalSampleReq)
+BaseResponse getOrderLabCode(OrderReq)
+BaseResponse cancelOrder(CancelPreorderOrderReq)
+BaseResponse closeOrderAfterDelAnalyte(CloseOrderAfterDelAnalyte)
+BaseResponse syncCrossLabInfo(CrossLabReq)
+BaseResponse~OrderDetailCommonRsp~ getOrderDetail(OrderDetailReq)
}
class ReportFacade {
+BaseResponse~ReportSimplifyInfo~ getReportSimplifyInfo(ReportSimplifyInfoReq)
+BaseResponse~List~ getTestLineConclusion(ReportSimplifyInfoReq)
+BaseResponse~List~ getMatrixInfoForAmend(ReportSimplifyInfoReq)
+BaseResponse~GetReportInfo~ getReportinfoByOrderNo(ReportSimplifyInfoReq)
+BaseResponse~List~ queryReportFile(ReportSimplifyInfoReq)
+BaseResponse~List~ getParentReportByReportNo(ParentReportReq)
+BaseResponse~List~ getReportByReportNos(ReportSgsmartReq)
+BaseResponse~List~ getReportByReportNos(ReportNoListReq)
+BaseResponse uploadPDF(UploadReportReq)
+BaseResponse reportApprove(BaseReportReq)
+BaseResponse generateFrontCoverPage(GenerateReportReq)
+BaseResponse~ImportReportDataReq~ generateReportData(ReportDataReq)
+BaseResponse processFastReturnData(FastReturnDataInfo)
}
OrderFacade <|-- OrderFacadeImpl
ReportFacade <|-- ReportFacadeImpl
```

**图示来源**
- [OrderFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/OrderFacade.java)
- [ReportFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/ReportFacade.java)

**本节来源**
- [OrderFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/OrderFacade.java)
- [ReportFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/ReportFacade.java)

### 统一响应结构分析
系统采用统一的响应结构`BaseResponse<T>`，确保所有API的返回格式一致，便于客户端处理。

#### BaseResponse结构
```mermaid
classDiagram
class BaseResponse~T~ {
-int status
-String message
-T data
-long versionId
-String stackTrace
+int getStatus()
+void setStatus(int)
+T getData()
+void setData(T)
+String getMessage()
+void setMessage(String)
+long getVersionId()
+void setVersionId(long)
+String getStackTrace()
+void setStackTrace(String)
+BaseResponse()
+BaseResponse(int, String)
+static BaseResponse newSuccessInstance(T)
+static BaseResponse newInstance(T)
+static BaseResponse newFailInstance(ResponseCode)
+static BaseResponse newFailInstance(String)
+static BaseResponse newInstance(CustomResult)
}
class ResponseCode {
+SUCCESS(200, "操作成功")
+ILLEGAL_ARGUMENT(100, "错误的请求参数")
+ErrDuplicateReq(197, "重复请求")
+FAIL(198, "请求处理失败")
+TokenExpire(201, "用户权限过期，请重新登录")
+UNKNOWN(500, "系统异常，请稍后重试")
+int getCode()
+String getMessage()
+static ResponseCode getByCode(int)
}
BaseResponse --> ResponseCode : "使用"
```

**图示来源**
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java)
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)

**本节来源**
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java)
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)

### DTO设计原则分析
数据传输对象(DTO)设计遵循清晰、简洁的原则，通过继承`BaseRequest`实现统一的请求基础结构。

#### 请求DTO结构
以`OrderDetailReq`为例，展示了请求DTO的设计模式：
```mermaid
classDiagram
class BaseRequest {
+String getRequestId()
+void setRequestId(String)
+String getOperator()
+void setOperator(String)
}
class OrderDetailReq {
-String orderNo
-Boolean includeTestMatrix
-Boolean includeCustomer
+String getOrderNo()
+void setOrderNo(String)
+Boolean getIncludeTestMatrix()
+void setIncludeTestMatrix(Boolean)
+Boolean getIncludeCustomer()
+void setIncludeCustomer(Boolean)
}
BaseRequest <|-- OrderDetailReq
```

**图示来源**
- [OrderDetailReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/req/order/OrderDetailReq.java)

**本节来源**
- [OrderDetailReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/req/order/OrderDetailReq.java)

#### 响应DTO结构
以`OrderDetailCommonRsp`为例，展示了响应DTO的嵌套结构设计：
```mermaid
classDiagram
class OrderDetailCommonRsp {
-OrderDetailHeaderStandardRsp header
-OrderDetailCustomerStandardRsp[] customerList
-OrderDetailSampleStandardRsp[] testSampleList
-OrderDetailTestLineStandardRsp[] testLineList
-OrderDetailMatrixStandardRsp[] testMatrixList
}
class OrderDetailHeaderStandardRsp {
-String orderNo
-String orderStatus
-String customerName
}
class OrderDetailCustomerStandardRsp {
-String customerName
-String customerCode
-String contactPerson
}
class OrderDetailSampleStandardRsp {
-String sampleNo
-String sampleName
-String sampleType
}
class OrderDetailTestLineStandardRsp {
-String testLineId
-String testLineName
-String testResult
}
class OrderDetailMatrixStandardRsp {
-String matrixId
-String matrixName
-String matrixType
}
OrderDetailCommonRsp --> OrderDetailHeaderStandardRsp
OrderDetailCommonRsp --> OrderDetailCustomerStandardRsp
OrderDetailCommonRsp --> OrderDetailSampleStandardRsp
OrderDetailCommonRsp --> OrderDetailTestLineStandardRsp
OrderDetailCommonRsp --> OrderDetailMatrixStandardRsp
```

**图示来源**
- [OrderDetailCommonRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/order/OrderDetailCommonRsp.java)

**本节来源**
- [OrderDetailCommonRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/order/OrderDetailCommonRsp.java)

#### StarLims报价相关DTO结构
新增了与StarLims报价相关的响应数据模型，用于支持报价功能的数据传输。

```mermaid
classDiagram
class QuotationTestLineInfoRsp {
-String id
-Integer ppVersionId
-Integer testLineVersionId
-Integer testLineId
-Integer aid
-Long citationBaseId
-Long citationId
-Long citationVersionId
-String citationName
-String citationFullName
-String productLine
-String evaluationAlias
-Integer isToTest
-BigDecimal mainCurrencyFinalAmount
-String ppTlRelId
-String locationCode
-String costCode
-Integer tat
-Integer appFactorId
-String quotationServiceItemId
-List<QuotationTestLineAnalyteInfoRsp> quotationAnalyteInstanceList
+String getId()
+void setId(String)
+Integer getPpVersionId()
+void setPpVersionId(Integer)
+Integer getTestLineVersionId()
+void setTestLineVersionId(Integer)
+Integer getTestLineId()
+void setTestLineId(Integer)
+Integer getAid()
+void setAid(Integer)
+Long getCitationBaseId()
+void setCitationBaseId(Long)
+Long getCitationId()
+void setCitationId(Long)
+Long getCitationVersionId()
+void setCitationVersionId(Long)
+String getCitationName()
+void setCitationName(String)
+String getCitationFullName()
+void setCitationFullName(String)
+String getProductLine()
+void setProductLine(String)
+String getEvaluationAlias()
+void setEvaluationAlias(String)
+Integer getIsToTest()
+void setIsToTest(Integer)
+BigDecimal getMainCurrencyFinalAmount()
+void setMainCurrencyFinalAmount(BigDecimal)
+String getPpTlRelId()
+void setPpTlRelId(String)
+String getLocationCode()
+void setLocationCode(String)
+String getCostCode()
+void setCostCode(String)
+Integer getTat()
+void setTat(Integer)
+Integer getAppFactorId()
+void setAppFactorId(Integer)
+String getQuotationServiceItemId()
+void setQuotationServiceItemId(String)
+List<QuotationTestLineAnalyteInfoRsp> getQuotationAnalyteInstanceList()
+void setQuotationAnalyteInstanceList(List<QuotationTestLineAnalyteInfoRsp>)
}
class QuotationTestLineAnalyteInfoRsp {
-String testAnalyteId
-String testAnalyteName
+String getTestAnalyteId()
+void setTestAnalyteId(String)
+String getTestAnalyteName()
+void setTestAnalyteName(String)
}
QuotationTestLineInfoRsp --> QuotationTestLineAnalyteInfoRsp : "包含"
```

**图示来源**
- [QuotationTestLineInfoRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/starlims/QuotationTestLineInfoRsp.java)
- [QuotationTestLineAnalyteInfoRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/starlims/QuotationTestLineAnalyteInfoRsp.java)

**本节来源**
- [QuotationTestLineInfoRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/starlims/QuotationTestLineInfoRsp.java)
- [QuotationTestLineAnalyteInfoRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/starlims/QuotationTestLineAnalyteInfoRsp.java)
- [GpoAnalyteBuilderBizProcess.java](file://otsnotes-subcontract/otsnotes-subcontract-gpo/src/main/java/com/sgs/otsnotes/subcontract/app/tostarlims/gpo/bizprocess/GpoAnalyteBuilderBizProcess.java)

### API端点分析
通过分析Web层控制器，可以确定HTTP方法和URL路径的定义规范。

#### HTTP方法与路径映射
```mermaid
sequenceDiagram
participant Client as 客户端
participant Controller as Web控制器
participant Facade as Facade接口
participant Impl as 实现类
Client->>Controller : GET /healthApi/checkHealth
Controller->>Facade : checkHealth()
Facade->>Impl : checkHealth()
Impl-->>Facade : 返回结果
Facade-->>Controller : 返回结果
Controller-->>Client : BaseResponse
Client->>Controller : POST /dataEntry/getConditions
Controller->>Facade : getConditions(req)
Facade->>Impl : getConditions(req)
Impl-->>Facade : 返回结果
Facade-->>Controller : 返回结果
Controller-->>Client : BaseResponse
```

**图示来源**
- [HealthController.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/controllers/HealthController.java)
- [DataEntryController.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/controllers/DataEntryController.java)

**本节来源**
- [HealthController.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/controllers/HealthController.java)
- [DataEntryController.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/controllers/DataEntryController.java)

### 错误处理规范分析
系统采用分层的错误处理机制，包括业务异常和系统异常的统一处理。

#### 异常处理体系
```mermaid
classDiagram
class BizException {
-ResponseCode errorCode
+BizException(ResponseCode, String)
+BizException(String)
+BizException(ResponseCode)
+BizException(String, Throwable)
+BizException(ResponseCode, String, Throwable)
+ResponseCode getErrorCode()
+static void throwBizException(ResponseCode, String)
+static void throwBizException(ResponseCode, String, Throwable)
}
class GlobalExceptionHandler {
+void handleClientAbortException(ClientAbortException)
+Object handleClassCastException(ClassCastException)
+Object handleDBException(Throwable)
+Object handleThrowable(Throwable)
}
class ResponseCode {
+SUCCESS(200, "操作成功")
+ILLEGAL_ARGUMENT(100, "错误的请求参数")
+ErrDuplicateReq(197, "重复请求")
+FAIL(198, "请求处理失败")
+TokenExpire(201, "用户权限过期，请重新登录")
+UNKNOWN(500, "系统异常，请稍后重试")
}
GlobalExceptionHandler --> BizException : "捕获"
GlobalExceptionHandler --> ResponseCode : "使用"
BizException --> ResponseCode : "引用"
```

**图示来源**
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)

**本节来源**
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)

## 依赖分析
系统各模块之间存在清晰的依赖关系，遵循依赖倒置原则，高层模块依赖于抽象而非具体实现。

```mermaid
graph TD
web[otsnotes-web] --> facade[otsnotes-facade]
facade --> facade_impl[otsnotes-facade-impl]
facade_impl --> domain[otsnotes-domain]
domain --> core[otsnotes-core]
domain --> infra[otsnotes-infra]
domain --> dbstorages[otsnotes-dbstorages]
integration[otsnotes-integration] --> domain
subcontract[otsnotes-subcontract] --> domain
style web fill:#f9f,stroke:#333
style facade fill:#bbf,stroke:#333
style facade_impl fill:#bbf,stroke:#333
style domain fill:#f96,stroke:#333
style core fill:#9f9,stroke:#333
style infra fill:#9f9,stroke:#333
style dbstorages fill:#9f9,stroke:#333
style integration fill:#ff9,stroke:#333
style subcontract fill:#ff9,stroke:#333
```

**图示来源**
- [项目结构](file://README.md)

**本节来源**
- [pom.xml](file://otsnotes-web/pom.xml)
- [pom.xml](file://otsnotes-facade/pom.xml)
- [pom.xml](file://otsnotes-facade-impl/pom.xml)
- [pom.xml](file://otsnotes-domain/pom.xml)

## 性能考量
系统在设计时考虑了性能因素，通过缓存、异步处理和数据库优化等手段提升性能。

- **缓存机制**：使用Guava Cache和Redis缓存频繁访问的数据
- **异步处理**：通过Kafka实现异步消息处理，解耦系统组件
- **数据库优化**：使用MyBatis进行数据库操作，支持批量处理和连接池
- **线程池**：配置专用线程池处理耗时操作，避免阻塞主线程

## 故障排除指南
### 常见错误场景及处理建议
1. **参数验证失败 (状态码100)**
   - 检查请求参数是否符合接口定义
   - 确保必填字段已提供
   - 验证数据类型和格式是否正确

2. **用户权限过期 (状态码201)**
   - 重新获取认证令牌
   - 检查令牌有效期
   - 确保认证信息正确传递

3. **系统异常 (状态码500)**
   - 检查服务日志获取详细错误信息
   - 验证数据库连接是否正常
   - 检查外部系统依赖是否可用

4. **重复请求 (状态码197)**
   - 实现请求去重机制
   - 使用唯一请求ID避免重复处理
   - 检查客户端是否重复发送请求

**本节来源**
- [GlobalExceptionHandler.java](file://otsnotes-web/src/main/java/com/sgs/otsnotes/web/config/GlobalExceptionHandler.java)
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)

## 结论
otsnotes-service的API设计遵循清晰、一致的原则，通过facade层定义了明确的接口契约。系统采用统一的响应结构和错误处理机制，提高了API的可用性和可维护性。DTO设计合理，支持复杂的数据结构传输。通过分析代码和文档，可以看出系统架构设计良好，各组件职责清晰，为系统的稳定运行和持续发展奠定了基础。最近的更新增加了与StarLims报价相关的数据模型，扩展了系统的对外接口能力，为报价功能提供了数据支持。