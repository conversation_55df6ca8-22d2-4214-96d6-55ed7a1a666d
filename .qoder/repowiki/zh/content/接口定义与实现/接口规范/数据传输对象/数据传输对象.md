# 数据传输对象

<cite>
**本文档引用的文件**   
- [JobDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/JobDTO.java)
- [TestLineDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/TestLineDTO.java)
- [TestLineRequirmentListDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/TestLineRequirmentListDTO.java)
- [PrintFriendliness.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/PrintFriendliness.java)
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java)
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)
- [架构文档.md](file://uni-otsnotes/doc/架构文档.md)
- [QuotationTestLineInfoRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/starlims/QuotationTestLineInfoRsp.java) - *新增于最近提交*
- [QuotationTestLineAnalyteInfoRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/starlims/QuotationTestLineAnalyteInfoRsp.java) - *新增于最近提交*
- [GpoAnalyteBuilderBizProcess.java](file://otsnotes-subcontract/otsnotes-subcontract-gpo/src/main/java/com/sgs/otsnotes/subcontract/app/tostarlims/gpo/bizprocess/GpoAnalyteBuilderBizProcess.java) - *新增于最近提交*
</cite>

## 更新摘要
**变更内容**   
- 新增了与StarLims报价相关的DTO结构说明，包括`QuotationTestLineInfoRsp`和`QuotationTestLineAnalyteInfoRsp`
- 扩展了核心DTO结构分析部分，增加对新增DTO的详细描述
- 更新了文档引用文件列表，包含新增的响应数据模型类
- 保持原有文档结构和设计规范部分不变

## 目录
1. [引言](#引言)
2. [DTO设计规范](#dto设计规范)
3. [核心DTO结构分析](#核心dto结构分析)
4. [DTO生命周期管理](#dto生命周期管理)
5. [DTO与领域模型映射](#dto与领域模型映射)
6. [DTO使用示例与最佳实践](#dto使用示例与最佳实践)
7. [性能考虑](#性能考虑)
8. [总结](#总结)

## 引言
数据传输对象（DTO）是otsnotes-service系统中用于在不同层之间传输数据的关键组件。本文档详细介绍了系统的DTO设计规范、核心DTO结构、生命周期管理、与领域模型的映射关系以及使用最佳实践。通过本文档，开发者可以全面了解如何在系统中正确使用DTO，确保数据传输的一致性和高效性。

## DTO设计规范

### 字段命名规范
在otsnotes-service系统中，DTO的字段命名遵循Java驼峰命名法（camelCase），确保代码的可读性和一致性。例如，`generalOrderInstanceID`和`testLineInstanceId`等字段名都采用了驼峰命名法。此外，字段名应具有描述性，能够清晰地表达其用途。

### 数据类型选择
DTO中的数据类型选择基于实际业务需求和数据的特性。常见的数据类型包括：
- **String**: 用于表示文本信息，如订单号、作业号等。
- **Integer**: 用于表示整数，如状态码、实验室部门ID等。
- **Date**: 用于表示日期和时间，如实验室入库日期、实验室出库日期等。
- **List**: 用于表示集合，如测试线列表、测试条件列表等。

### 嵌套结构处理
DTO支持嵌套结构，以表示复杂的数据关系。例如，`TestLineDTO`包含一个`List<TestLineRequirmentListDTO>`类型的字段`testLines`，用于表示多个测试线的需求列表。这种嵌套结构使得数据传输更加灵活和高效。

### 序列化配置
为了确保DTO在传输过程中的正确性和效率，系统使用了Jackson库进行序列化和反序列化。通过`@JsonIgnoreProperties`注解，可以忽略某些不需要序列化的属性，如`handler`。此外，`PrintFriendliness`类提供了统一的`toString`方法，便于调试和日志记录。

### 验证注解的使用
虽然当前系统中没有广泛使用验证注解，但可以通过`javax.validation`包中的注解（如`@NotNull`、`@Size`等）来增强DTO的验证能力。这些注解可以在运行时检查数据的有效性，防止无效数据进入系统。

**本节来源**
- [JobDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/JobDTO.java)
- [TestLineDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/TestLineDTO.java)
- [TestLineRequirmentListDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/TestLineRequirmentListDTO.java)
- [PrintFriendliness.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/PrintFriendliness.java)

## 核心DTO结构分析

### JobDTO
`JobDTO`是用于表示作业信息的数据传输对象。它继承自`BaseRequest`，并包含以下主要字段：

```java
public class JobDTO extends BaseRequest {
    private static final long serialVersionUID = -1945502540416984359L;

    private String generalOrderInstanceID;
    private String orderNo;
    private String jobNo;
    private Integer jobStatus;
    private Date labInDate;
    private Date labOutDate;
    private String remark;
    private Integer labSectionId;
    private String labSectionName;
    private String testLineInstanceId;
    private String testLineId;
    private String testItem;
    private Integer testLineStatus;

    // Getters and Setters
}
```

- **generalOrderInstanceID**: 通用订单实例ID
- **orderNo**: 订单号
- **jobNo**: 作业号
- **jobStatus**: 作业状态
- **labInDate**: 实验室入库日期
- **labOutDate**: 实验室出库日期
- **remark**: 备注
- **labSectionId**: 实验室部门ID
- **labSectionName**: 实验室部门名称
- **testLineInstanceId**: 测试线实例ID
- **testLineId**: 测试线ID
- **testItem**: 测试项目
- **testLineStatus**: 测试线状态

### TestLineDTO
`TestLineDTO`是用于表示测试线信息的数据传输对象。它继承自`PrintFriendliness`，并包含以下主要字段：

```java
@JsonIgnoreProperties(value = {"handler"})
public class TestLineDTO extends PrintFriendliness {
    private String orderNo;
    private Integer activeIndicator;
    private List<TestLineRequirmentListDTO> testLines;

    // Getters and Setters
}
```

- **orderNo**: 订单号
- **activeIndicator**: 活动指示器
- **testLines**: 测试线需求列表

### TestLineRequirmentListDTO
`TestLineRequirmentListDTO`是用于表示测试线需求列表的数据传输对象。它继承自`PrintFriendliness`，并包含以下主要字段：

```java
@JsonIgnoreProperties(value = {"handler"})
public class TestLineRequirmentListDTO extends PrintFriendliness {
    @DeclaredField
    private String testLineInstanceId;
    private Integer testLineId;
    @DeclaredField
    private String evaluationAlias;
    private String citationName;
    private String sampleNos;
    private String manualStr;
    private Long citationBaseId;

    // Getters and Setters
}
```

- **testLineInstanceId**: 测试线实例ID
- **testLineId**: 测试线ID
- **evaluationAlias**: 评估别名
- **citationName**: 引用名称
- **sampleNos**: 样品编号
- **manualStr**: 手动修改标志
- **citationBaseId**: 引用基础ID

### QuotationTestLineInfoRsp
`QuotationTestLineInfoRsp`是用于表示StarLims报价测试线信息的响应数据传输对象。它实现了`Serializable`接口，并包含以下主要字段：

```java
@Data
public class QuotationTestLineInfoRsp implements Serializable {
    private String id;
    private Integer ppVersionId;
    private Integer testLineVersionId;
    private Integer testLineId;
    private Integer aid;
    private Long citationBaseId;
    private Long citationId;
    private Long citationVersionId;
    private String citationName;
    private String citationFullName;
    private String productLine;
    private String evaluationAlias;
    private Integer isToTest;
    private BigDecimal mainCurrencyFinalAmount;
    private String ppTlRelId;
    private String locationCode;
    private String costCode;
    private Integer tat;
    private Integer appFactorId;
    private String quotationServiceItemId;
    private List<QuotationTestLineAnalyteInfoRsp> quotationAnalyteInstanceList;
}
```

- **id**: 唯一标识符
- **ppVersionId**: PP版本ID
- **testLineVersionId**: 测试线版本ID
- **testLineId**: 测试线ID
- **aid**: AID标识
- **citationBaseId**: 引用基础ID
- **citationId**: 引用ID
- **citationVersionId**: 引用版本ID
- **citationName**: 引用名称
- **citationFullName**: 引用全名
- **productLine**: 产品线
- **evaluationAlias**: 评估别名
- **isToTest**: 是否测试标志
- **mainCurrencyFinalAmount**: 主货币最终金额
- **ppTlRelId**: PP测试线关系ID
- **locationCode**: 位置代码
- **costCode**: 成本代码
- **tat**: 周转时间(TAT)
- **appFactorId**: 应用因子ID
- **quotationServiceItemId**: 报价服务项目ID
- **quotationAnalyteInstanceList**: 报价分析物实例列表

### QuotationTestLineAnalyteInfoRsp
`QuotationTestLineAnalyteInfoRsp`是用于表示报价测试线分析物信息的数据传输对象。它包含以下主要字段：

```java
@Data
public class QuotationTestLineAnalyteInfoRsp {
    private String testAnalyteId;
    private String testAnalyteName;
}
```

- **testAnalyteId**: 测试分析物ID
- **testAnalyteName**: 测试分析物名称

**本节来源**
- [JobDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/JobDTO.java)
- [TestLineDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/TestLineDTO.java)
- [TestLineRequirmentListDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/TestLineRequirmentListDTO.java)
- [QuotationTestLineInfoRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/starlims/QuotationTestLineInfoRsp.java)
- [QuotationTestLineAnalyteInfoRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/starlims/QuotationTestLineAnalyteInfoRsp.java)

## DTO生命周期管理

### 创建
DTO的创建通常发生在业务逻辑层或服务层。当需要将数据从数据库或其他数据源传输到前端或其他服务时，会创建相应的DTO对象，并填充其字段。

### 转换
DTO的转换通常涉及将领域模型对象转换为DTO对象，或将DTO对象转换为领域模型对象。这一过程可以通过手动映射或使用工具（如MapStruct）来实现。例如，`TestlineInstanceConverter`类负责将`TestlineInstance`对象转换为`TestlineInstanceDTO`对象。

### 序列化
DTO在传输过程中需要进行序列化。系统使用Jackson库进行JSON序列化，确保数据能够在网络上传输。通过`@JsonIgnoreProperties`注解，可以忽略某些不需要序列化的属性，提高传输效率。

**本节来源**
- [架构文档.md](file://uni-otsnotes/doc/架构文档.md)

## DTO与领域模型映射

### 映射关系说明
DTO与领域模型之间的映射关系是通过转换器（Converter）实现的。每个DTO都有一个对应的转换器，负责将领域模型对象转换为DTO对象，反之亦然。例如，`TestlineInstanceConverter`类负责将`TestlineInstance`对象转换为`TestlineInstanceDTO`对象。

### 数据转换规则和策略
数据转换的规则和策略主要包括：
- **字段映射**: 将领域模型中的字段映射到DTO中的相应字段。
- **类型转换**: 在必要时进行类型转换，如将`LocalDateTime`转换为`Date`。
- **嵌套对象处理**: 处理嵌套对象的转换，确保所有子对象都被正确映射。
- **默认值处理**: 为某些字段设置默认值，确保DTO对象的完整性。

**本节来源**
- [架构文档.md](file://uni-otsnotes/doc/架构文档.md)

## DTO使用示例与最佳实践

### 使用示例
以下是一个使用`JobDTO`的示例：

```java
@RestController
@RequestMapping("/jobs")
public class JobController {

    @Autowired
    private JobService jobService;

    @PostMapping("/create")
    public BaseResponse<JobDTO> createJob(@RequestBody JobDTO jobDTO) {
        JobDTO createdJob = jobService.createJob(jobDTO);
        return BaseResponse.newSuccessInstance(createdJob);
    }

    @GetMapping("/{jobNo}")
    public BaseResponse<JobDTO> getJob(@PathVariable String jobNo) {
        JobDTO jobDTO = jobService.getJobByJobNo(jobNo);
        return BaseResponse.newInstance(jobDTO);
    }
}
```

### 最佳实践
- **保持DTO简单**: DTO应仅包含必要的字段，避免过度复杂化。
- **使用继承**: 对于具有共同字段的DTO，可以使用继承来减少重复代码。
- **使用转换器**: 使用转换器（如MapStruct）来自动化领域模型与DTO之间的转换，减少手动映射的错误。
- **序列化优化**: 通过`@JsonIgnoreProperties`注解忽略不必要的字段，提高序列化效率。
- **验证数据**: 在接收DTO时进行数据验证，确保数据的有效性。

**本节来源**
- [JobDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/JobDTO.java)
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java)

## 性能考虑

### 序列化优化
- **忽略不必要的字段**: 使用`@JsonIgnoreProperties`注解忽略不需要序列化的字段，减少传输数据量。
- **使用高效的序列化库**: Jackson库提供了高效的JSON序列化和反序列化功能，确保数据传输的高效性。

### 内存使用建议
- **避免大对象**: 尽量避免创建过大的DTO对象，特别是在高并发场景下。
- **分页处理**: 对于大量数据的传输，使用分页机制，分批传输数据，减少内存占用。
- **缓存**: 对于频繁访问的数据，可以使用缓存机制，减少数据库查询次数，提高响应速度。

**本节来源**
- [PrintFriendliness.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/PrintFriendliness.java)
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java)

## 总结
本文档详细介绍了otsnotes-service系统中数据传输对象（DTO）的设计规范、核心结构、生命周期管理、与领域模型的映射关系以及使用最佳实践。通过遵循这些规范和最佳实践，开发者可以确保数据传输的一致性和高效性，提升系统的整体性能和可维护性。