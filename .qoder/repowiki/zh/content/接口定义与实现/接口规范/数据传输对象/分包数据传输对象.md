# 分包数据传输对象

<cite>
**本文档引用的文件**   
- [SubcontractDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/SubcontractDTO.java)
- [SubContractFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/SubContractFacade.java)
- [SubContractFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/SubContractFacadeImpl.java)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)
- [SubContractStatusEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/SubContractStatusEnum.java)
- [SubcontractRequirementInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/subcontract/SubcontractRequirementInfo.java)
- [SubcontractRequirementContactsInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/subcontract/SubcontractRequirementContactsInfo.java)
</cite>

## 目录
1. [分包数据传输对象概述](#分包数据传输对象概述)
2. [SubcontractDTO结构详解](#subcontractdto结构详解)
3. [分包状态管理](#分包状态管理)
4. [分包要求数据结构](#分包要求数据结构)
5. [分包DTO使用场景](#分包dto使用场景)
6. [分包系统架构与流程](#分包系统架构与流程)
7. [分包DTO与其他核心DTO的关系](#分包dto与其他核心dto的关系)
8. [性能优化与序列化建议](#性能优化与序列化建议)

## 分包数据传输对象概述

分包数据传输对象（DTO）是otsnotes-service系统中用于分包业务的核心数据结构，主要负责在不同系统组件之间传输分包相关的业务数据。该对象设计遵循了数据传输对象模式，旨在减少网络调用次数并封装复杂的业务数据。

分包DTO系统主要包含`SubcontractDTO`、`SubcontractRequirementInfo`和`SubcontractRequirementContactsInfo`等核心类，这些类共同构成了完整的分包信息模型。系统通过门面模式（Facade Pattern）提供统一的接口，使得外部系统可以方便地访问和操作分包数据。

分包功能在系统中扮演着重要角色，主要用于管理外部实验室的测试任务分配、状态跟踪和结果回传。通过分包DTO，系统能够实现订单与外部实验室之间的数据同步，确保测试流程的完整性和可追溯性。

**分包DTO的主要应用场景包括：**
- 分包单的创建和管理
- 分包状态的跟踪和更新
- 分包要求的配置和传递
- 外部系统（如StarLims）的集成
- 分包测试结果的接收和处理

```mermaid
graph TD
A[客户端] --> B[SubContractFacade]
B --> C[SubContractService]
C --> D[数据库]
C --> E[外部系统]
D --> F[SubcontractDTO]
E --> G[SubcontractRequirementInfo]
F --> H[分包管理]
G --> H
```

**图示来源**
- [SubContractFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/SubContractFacade.java)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)

## SubcontractDTO结构详解

`SubcontractDTO`是分包系统中最核心的数据传输对象，包含了分包单的基本信息、测试信息和状态信息。该类位于`otsnotes-facade-model`模块中，作为服务间通信的标准数据格式。

### 核心字段说明

**分包基本信息**
- `id`: 分包记录的唯一标识符，用于系统内部引用
- `subContractNo`: 分包编号，对外显示的分包单号
- `subContractId`: 分包ID，与外部系统的对应标识
- `subContractName`: 分包名称，描述分包的业务含义

**测试信息**
- `testLineId`: 测试线ID，关联具体的测试项目
- `testLineBaseId`: 测试线基础ID，用于版本控制
- `testItem`: 测试项目名称，描述具体的测试内容
- `testStandard`: 测试标准，执行测试所依据的标准文档
- `standardSectionName`: 标准章节名称，标准文档中的具体章节

**组织与分类信息**
- `labSection`: 实验室部门，执行测试的实验室部门
- `labSectionBaseId`: 实验室部门基础ID，用于版本控制
- `citationBaseId`: 引用基础ID，关联测试引用的标准
- `productLineAbbr`: 产品线缩写，标识所属的产品线

**状态与控制信息**
- `subContractStatus`: 分包状态，表示分包单的当前状态
- `flag`: 标记位，指示此测试线是否有job或分包
- `selectFlag`: 选择标记，指示此测试线是否被选中
- `longTatMsg`: 长TAT消息，描述测试周期较长的原因

**关联信息**
- `ppArtifactRelIds`: 产品部件关系ID列表，关联的产品部件
- `generalOrderInstanceID`: 通用订单实例ID，关联的订单实例
- `citationId`: 引用ID，具体的引用标识
- `citationVersionId`: 引用版本ID，引用的版本信息
- `aId`: 分析ID，关联的分析标识
- `ppBaseId`: 产品部件基础ID，产品部件的版本标识

```mermaid
classDiagram
class SubcontractDTO {
+String id
+String testLineInstanceID
+String subContractNo
+String testLineId
+Long testLineBaseId
+Long labSectionBaseId
+Long citationBaseId
+String testItem
+String testStandard
+String standardSectionName
+String labSection
+Integer subContractId
+String subContractName
+Integer subContractStatus
+String productLineAbbr
+boolean flag
+boolean selectFlag
+String longTatMsg
+List<Long> ppArtifactRelIds
+String generalOrderInstanceID
+Integer citationId
+Integer citationVersionId
+Long aId
+Long ppBaseId
+String getLongTatMsg()
+void setLongTatMsg(String)
+boolean isSelectFlag()
+void setSelectFlag(boolean)
+boolean isFlag()
+void setFlag(boolean)
+String getId()
+void setId(String)
+String getSubContractNo()
+void setSubContractNo(String)
+String getTestLineId()
+void setTestLineId(String)
+Long getTestLineBaseId()
+void setTestLineBaseId(Long)
+Long getLabSectionBaseId()
+void setLabSectionBaseId(Long)
+Long getCitationBaseId()
+void setCitationBaseId(Long)
+String getTestItem()
+void setTestItem(String)
+String getTestStandard()
+void setTestStandard(String)
+String getStandardSectionName()
+void setStandardSectionName(String)
+String getLabSection()
+void setLabSection(String)
+Integer getSubContractId()
+void setSubContractId(Integer)
+String getSubContractName()
+void setSubContractName(String)
+String getTestLineInstanceID()
+void setTestLineInstanceID(String)
+Integer getSubContractStatus()
+void setSubContractStatus(Integer)
+String getProductLineAbbr()
+void setProductLineAbbr(String)
+List<Long> getPpArtifactRelIds()
+void setPpArtifactRelIds(List<Long>)
+Integer getCitationId()
+void setCitationId(Integer)
+Integer getCitationVersionId()
+void setCitationVersionId(Integer)
+Long getaId()
+void setaId(Long)
+Long getPpBaseId()
+void setPpBaseId(Long)
+String getGeneralOrderInstanceID()
+void setGeneralOrderInstanceID(String)
}
```

**图示来源**
- [SubcontractDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/SubcontractDTO.java)

**本节来源**
- [SubcontractDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/SubcontractDTO.java)

## 分包状态管理

分包状态管理是分包系统的核心功能之一，通过`SubContractStatusEnum`枚举类定义了分包单的完整生命周期。该枚举类位于`otsnotes-facade-model`模块中，为整个系统提供了统一的状态定义。

### 分包状态枚举

`SubContractStatusEnum`定义了五种分包状态，每种状态都有对应的数字代码和名称：

- **Created (0, "New")**: 新建状态，分包单刚创建但尚未开始测试
- **Testing (1, "Testing")**: 测试中状态，分包单已分配给外部实验室并开始测试
- **Complete (2, "Completed")**: 完成状态，外部实验室已完成测试并返回结果
- **Cancelled (3, "Cancelled")**: 取消状态，分包单被取消，不再进行测试
- **Pend (4, "Pending")**: 挂起状态，分包单暂时搁置，等待进一步处理

### 状态转换规则

系统通过严格的状态转换规则确保分包流程的正确性。主要的转换规则包括：

1. **新建到测试中**: 当分包单被确认并发送给外部实验室时，状态从"New"变为"Testing"
2. **测试中到完成**: 当外部实验室完成测试并返回结果时，状态从"Testing"变为"Completed"
3. **任何状态到取消**: 在任何状态下，分包单都可以被取消，进入"Cancelled"状态
4. **完成到挂起**: 在特殊情况下，已完成的分包单可以被挂起，等待进一步处理

### 状态验证逻辑

系统在关键操作时会进行状态验证，确保操作的合法性：

- **接收业务编号**: 只有状态为"New"的分包单才能接收业务编号
- **更新时间**: 状态必须为"New"或"Testing"才能更新时间信息
- **接收报告**: 状态不能为"New"或"Complete"才能接收报告，避免重复接收
- **取消分包**: 任何非"Cancelled"状态的分包单都可以被取消

```mermaid
stateDiagram-v2
[*] --> New
New --> Testing : 确认分包
Testing --> Completed : 完成测试
Testing --> Pending : 暂停测试
Pending --> Testing : 恢复测试
New --> Cancelled : 取消分包
Testing --> Cancelled : 取消分包
Completed --> Cancelled : 取消分包
Pending --> Cancelled : 取消分包
Completed --> Pending : 重新评估
Pending --> Completed : 完成评估
```

**图示来源**
- [SubContractStatusEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/SubContractStatusEnum.java)

**本节来源**
- [SubContractStatusEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/SubContractStatusEnum.java)

## 分包要求数据结构

分包要求数据结构用于定义和管理分包测试的具体要求，包括报告要求、服务要求和样品处理要求等。这些要求确保外部实验室按照客户的需求执行测试并提供相应的报告。

### SubcontractRequirementInfo结构

`SubcontractRequirementInfo`类包含了分包要求的主要信息，其核心字段包括：

**报告要求**
- `evaluationBasis`: 评判原则，定义结果评判的标准
- `reportLanguage`: 报告语言，指定报告的语言版本
- `reportManner`: 报告方式，单样品报告或多样品报告
- `reportType`: 报告类型，电子报告或纸质报告
- `reportRequirement`: 报告要求，PDF、Word或两者都需要
- `reportTemplate`: 报告模板，指定使用的报告模板
- `reportQty`: 报告数量，需要生成的报告份数

**服务要求**
- `serviceType`: 服务类型，常规测试或紧急服务
- `resultJudgingFlag`: 结果评判标志，是否需要结果评判
- `needConclusion`: 需要结论，是否需要提供测试结论
- `draftReportRequired`: 需要草稿报告，是否需要提供草稿报告

**样品处理要求**
- `returnResidueSampleFlag`: 返回剩余样品标志，是否需要返回剩余样品
- `returnTestedSampleFlag`: 返回已测样品标志，是否需要返回已测样品
- `liquidTestSample`: 液体测试样品，是否为液体样品
- `takePhotoFlag`: 需要拍照标志，是否需要对样品拍照

**费用与资质要求**
- `subcontractFee`: 分包费用，分包测试的费用
- `subcontractFeeCurrency`: 分包费用货币，费用的货币单位
- `qualification`: 资质标志，实验室需要具备的资质
- `qualificationType`: 资质类型，具体的资质要求

### SubcontractRequirementContactsInfo结构

`SubcontractRequirementContactsInfo`类用于管理分包要求的联系人信息，其主要字段包括：

- `contactsType`: 联系人类型，包括软拷贝、硬拷贝、报告、样品返还、发票等
- `deliverTo`: 交付对象，申请人、付款人、买家、代理等
- `deliverOthers`: 其他交付信息，当交付对象为"Others"时的具体信息

### 分包要求的使用场景

分包要求在以下场景中被使用：

1. **分包创建时**: 系统根据订单信息和客户要求生成分包要求
2. **外部系统集成**: 将分包要求传递给外部实验室系统（如StarLims）
3. **报告生成**: 根据分包要求生成符合客户需求的报告
4. **费用计算**: 根据服务类型和报告要求计算分包费用

```mermaid
classDiagram
class SubcontractRequirementInfo {
+String ID
+String generalOrderInstanceID
+String subContractNo
+String subContractID
+String evaluationBasis
+String otherRequirements
+String qualification
+Date reoprtDate
+String reportLanguage
+Integer reportManner
+String reportType
+String reportRequirement
+String reportTemplate
+Integer reportQty
+Integer resultJudgingFlag
+Integer serviceType
+Integer displaySupplierFlag
+Integer commentFlag
+Integer hardCopyFlag
+Integer chineseReportFlag
+Integer takePhotoFlag
+Integer confirmCoverPageFlag
+String packageIndicator
+String takePhotoRemark
+Integer returnResidueSampleFlag
+Integer returnTestedSampleFlag
+String returnResidueSampleRemark
+String returnTestedSampleRemark
+Integer reportAccreditationNeededFlag
+Integer hardCopyToApplicantFlag
+Integer hardCopyToPayertFlag
+String hardCopyToOther
+Integer softCopyToApplicantFlag
+Integer softCopyToPayerFlag
+String softCopyToOther
+String htmlString
+Integer pdfReportSecurity
+Integer activeIndicator
+String createdBy
+Date createdDate
+String modifiedBy
+Date modifiedDate
+String paymentRemark
+Date lastModifiedTimestamp
+String qualificationType
+Integer draftReportRequired
+Integer proformaInvoice
+Integer liquidTestSample
+Integer vatType
+String photoRequest
+Integer needConclusion
+String hardCopyReportDeliverWay
+String invoiceDeliverWay
+BigDecimal subcontractFee
+String subcontractFeeCurrency
+String qrcodeFlag
+Integer convertInHouseMethodFlag
+List<SubcontractRequirementContactsInfo> subcontractRequirementContactsInfos
}
class SubcontractRequirementContactsInfo {
+String id
+String orderId
+Integer contactsType
+String deliverTo
+String deliverOthers
+Date createdDate
+String createdBy
+Date modifiedDate
+String modifiedBy
}
SubcontractRequirementInfo --> SubcontractRequirementContactsInfo : "包含"
```

**图示来源**
- [SubcontractRequirementInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/subcontract/SubcontractRequirementInfo.java)
- [SubcontractRequirementContactsInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/subcontract/SubcontractRequirementContactsInfo.java)

**本节来源**
- [SubcontractRequirementInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/subcontract/SubcontractRequirementInfo.java)
- [SubcontractRequirementContactsInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/subcontract/SubcontractRequirementContactsInfo.java)

## 分包DTO使用场景

分包DTO在系统中有着广泛的应用场景，主要涉及分包管理、订单同步和外部系统集成等方面。通过分析门面接口和实现类，可以全面了解分包DTO的实际使用方式。

### 分包管理场景

分包管理是分包DTO最直接的应用场景，主要通过`SubContractFacade`接口提供的一系列方法实现：

**分包信息查询**
- `getSubContractInfo()`: 根据查询条件获取分包信息
- `querySubContractList()`: 查询分包列表，支持分页和过滤
- `getExternalSubContractInfo()`: 获取外部分包信息

**分包操作**
- `saveSubContract()`: 保存分包信息，创建或更新分包单
- `bindSubContract()`: 绑定分包，将测试线与分包单关联
- `unBindSubContract()`: 解绑分包，解除测试线与分包单的关联
- `cancelSubcontract()`: 取消分包，终止分包流程

**分包状态更新**
- `updateTimeTrack()`: 更新时间跟踪信息
- `receiveReportDoc()`: 接收外部实验室的报告文档
- `startSubContract()`: 启动分包流程

### 订单同步场景

分包DTO在订单同步过程中扮演着重要角色，确保分包信息与订单信息的一致性：

**订单状态同步**
- 当订单状态发生变化时，通过`updateSubContractData()`方法更新相关分包数据
- 取消订单时，自动取消关联的分包单，保持数据一致性

**分包信息同步**
- 通过`syncSubContractInfo()`方法实现分包信息的同步
- 确保分包单与订单的测试项目、样品信息等保持同步

### 外部系统集成场景

分包DTO是与外部系统（如StarLims）集成的关键数据格式：

**StarLims集成**
- `receiveReportDoc()`: 接收StarLims系统发送的报告文档
- `updateTimeTrack()`: 接收StarLims系统发送的时间跟踪信息
- `getSubContractTestLineInfo()`: 获取分包测试线信息，用于与StarLims系统同步

**数据回传**
- `saveSubTestData()`: 保存从外部系统回传的测试数据
- `querySubTestData()`: 查询外部系统回传的测试数据

```mermaid
sequenceDiagram
participant 前端 as 前端应用
participant 门面 as SubContractFacade
participant 服务 as SubContractService
participant 数据库 as 数据库
participant 外部系统 as 外部系统(StarLims)
前端->>门面 : 创建分包请求
门面->>服务 : 调用saveSubContract()
服务->>数据库 : 保存分包信息
数据库-->>服务 : 返回结果
服务-->>门面 : 返回结果
门面-->>前端 : 返回响应
外部系统->>门面 : 发送报告文档
门面->>服务 : 调用receiveReportDoc()
服务->>数据库 : 更新分包状态
服务->>数据库 : 保存报告文档
数据库-->>服务 : 返回结果
服务-->>门面 : 返回结果
门面-->>外部系统 : 确认接收
前端->>门面 : 查询分包列表
门面->>服务 : 调用querySubContractList()
服务->>数据库 : 查询分包数据
数据库-->>服务 : 返回分包列表
服务-->>门面 : 返回结果
门面-->>前端 : 返回分包列表
```

**图示来源**
- [SubContractFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/SubContractFacade.java)
- [SubContractFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/SubContractFacadeImpl.java)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)

**本节来源**
- [SubContractFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/SubContractFacade.java)
- [SubContractFacadeImpl.java](file://otsnotes-facade-impl/src/main/java/com/sgs/otsnotes/facade/impl/SubContractFacadeImpl.java)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)

## 分包系统架构与流程

分包系统采用分层架构设计，各组件职责明确，通过清晰的接口进行通信。系统架构主要包括门面层、服务层、数据访问层和外部集成层。

### 系统架构

**门面层 (Facade Layer)**
- `SubContractFacade`: 提供统一的API接口，封装复杂的业务逻辑
- `SubContractRequirementFacade`: 专门处理分包要求相关的操作

**服务层 (Service Layer)**
- `SubContractService`: 核心业务逻辑实现，处理分包的创建、更新、查询等操作
- `StarLimsCommonService`: 处理与StarLims系统的通用业务逻辑
- `SlimService`: 处理与SLIM系统的集成逻辑

**数据访问层 (Data Access Layer)**
- MyBatis Mapper接口: 提供数据库访问能力
- `SubContractMapper`: 分包数据访问
- `SubContractExtMapper`: 分包扩展数据访问
- `SubReportMapper`: 分包报告数据访问

**外部集成层 (Integration Layer)**
- 与StarLims系统的集成
- 与外部实验室系统的数据交换
- Kafka消息队列用于异步通信

### 主要业务流程

**分包创建流程**
1. 前端发送创建分包请求
2. 门面层接收请求并进行参数验证
3. 服务层处理业务逻辑，包括状态检查、数据验证等
4. 数据访问层将分包信息保存到数据库
5. 返回创建结果给前端

**分包状态更新流程**
1. 外部系统发送状态更新请求
2. 门面层接收请求并进行安全验证
3. 服务层检查当前状态是否允许更新
4. 更新分包状态并记录操作日志
5. 通知相关系统（如订单系统）状态变更

**报告接收流程**
1. 外部实验室发送报告文档
2. 系统验证报告格式和完整性
3. 解析报告内容并提取关键信息
4. 更新分包状态为"Completed"
5. 将报告信息与测试数据关联

```mermaid
graph TD
A[前端应用] --> B[SubContractFacade]
B --> C[SubContractService]
C --> D[SubContractMapper]
D --> E[数据库]
C --> F[StarLimsCommonService]
F --> G[外部系统]
C --> H[SubReportMapper]
H --> E
C --> I[SubContractExtMapper]
I --> E
J[Kafka] --> C
C --> J
```

**图示来源**
- [SubContractFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/SubContractFacade.java)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)
- [SubContractMapper.java](file://otsnotes-dbstorages/src/main/java/com/sgs/otsnotes/dbstorages/mybatis/mapper/SubContractMapper.java)

**本节来源**
- [SubContractFacade.java](file://otsnotes-facade/src/main/java/com/sgs/otsnotes/facade/SubContractFacade.java)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)

## 分包DTO与其他核心DTO的关系

分包DTO与系统中的其他核心DTO有着密切的关系，这些关系构成了完整的业务数据模型。通过分析这些关系，可以更好地理解分包系统在整个业务流程中的位置和作用。

### 与OrderDTO的关系

分包DTO与订单DTO（OrderDTO）通过`generalOrderInstanceID`字段建立关联。每个分包单都关联到一个具体的订单实例，这种关系确保了分包测试与订单的对应性。

**关联方式：**
- 一个订单可以有多个分包单
- 每个分包单只能属于一个订单
- 通过订单状态变化触发分包状态的相应更新

### 与TestLineDTO的关系

分包DTO与测试线DTO（TestLineDTO）通过`testLineId`和`testLineInstanceID`字段建立关联。这种关系将具体的测试项目与分包单联系起来。

**关联方式：**
- 一个分包单可以包含多个测试线
- 每个测试线只能属于一个分包单（或直接在内部实验室测试）
- 测试线的状态变化会影响分包单的状态

### 与ReportDTO的关系

分包DTO与报告DTO（ReportDTO）通过分包报告（SubReportDTO）建立间接关联。当外部实验室完成测试后，会生成分包报告，这些报告与分包单相关联。

**关联方式：**
- 一个分包单可以有多个分包报告
- 分包报告包含测试结果和结论
- 分包报告的状态影响分包单的最终状态

### 数据关联示例

```mermaid
erDiagram
ORDER ||--o{ SUBCONTRACT : "1:N"
TEST_LINE ||--o{ SUBCONTRACT : "1:N"
SUBCONTRACT ||--o{ SUB_REPORT : "1:N"
SUBCONTRACT }|--|| LAB_SECTION : "N:1"
SUBCONTRACT }|--|| CITATION : "N:1"
ORDER {
string orderNo PK
string customerInfo
date orderDate
}
SUBCONTRACT {
string subContractNo PK
integer subContractStatus
string testItem
string testStandard
string labSection
}
TEST_LINE {
string testLineId PK
string testItem
string testMethod
}
SUB_REPORT {
string reportId PK
string reportContent
date reportDate
string reportStatus
}
LAB_SECTION {
string labSectionId PK
string labSectionName
}
CITATION {
string citationId PK
string citationName
string citationStandard
}
```

**图示来源**
- [SubcontractDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/SubcontractDTO.java)
- [OrderDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/OrderDTO.java)
- [TestLineDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/TestLineDTO.java)
- [SubReportDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/subreport/SubReportDTO.java)

**本节来源**
- [SubcontractDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/SubcontractDTO.java)

## 性能优化与序列化建议

分包DTO在实际使用中需要考虑性能和序列化效率，特别是在高并发场景下。以下是一些优化建议和最佳实践。

### 序列化优化

**字段精简**
- 只传输必要的字段，避免传输大量空值或默认值
- 对于大型文本字段（如`htmlString`），考虑延迟加载或按需加载
- 使用`transient`关键字标记不需要序列化的临时字段

**数据类型选择**
- 使用基本数据类型（如`int`、`boolean`）而不是包装类型（如`Integer`、`Boolean`），减少内存开销
- 对于日期时间字段，使用`long`（时间戳）而不是`Date`对象，提高序列化效率
- 对于枚举类型，使用整数代码而不是字符串名称，减少数据大小

### 内存使用建议

**对象池化**
- 对于频繁创建和销毁的分包DTO实例，考虑使用对象池模式
- 重用对象实例，减少垃圾回收压力
- 实现`reset()`方法，快速重置对象状态

**集合优化**
- 对于`List<Long> ppArtifactRelIds`等集合字段，预先设置合理的初始容量
- 使用`Collections.emptyList()`代替`null`值，避免空指针异常
- 对于大型集合，考虑分页加载或流式处理

### 缓存策略

**本地缓存**
- 使用Guava Cache等本地缓存框架缓存频繁访问的分包DTO
- 设置合理的过期策略，平衡缓存命中率和数据新鲜度
- 实现缓存穿透和雪崩的防护机制

**分布式缓存**
- 对于跨服务共享的分包DTO，使用Redis等分布式缓存
- 使用合理的键命名策略，便于缓存管理和监控
- 实现缓存一致性机制，确保缓存数据与数据库数据同步

### 最佳实践

**DTO设计原则**
- 保持DTO的简单性，避免在DTO中添加业务逻辑
- 使用Builder模式创建复杂的DTO实例，提高代码可读性
- 实现`equals()`和`hashCode()`方法，支持对象比较和集合操作

**性能监控**
- 添加性能监控点，跟踪DTO的序列化和反序列化时间
- 监控内存使用情况，及时发现内存泄漏
- 记录慢查询日志，优化数据库访问性能

```mermaid
flowchart TD
A[DTO创建] --> B[字段初始化]
B --> C{是否需要缓存?}
C --> |是| D[检查本地缓存]
C --> |否| E[直接创建]
D --> F{缓存命中?}
F --> |是| G[返回缓存对象]
F --> |否| H[创建新对象]
H --> I[存入缓存]
I --> J[返回对象]
E --> J
J --> K[使用DTO]
K --> L{操作完成?}
L --> |否| K
L --> |是| M[清理资源]
M --> N[结束]
```

**图示来源**
- [SubcontractDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/SubcontractDTO.java)

**本节来源**
- [SubcontractDTO.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/dto/SubcontractDTO.java)