# 枚举契约

<cite>
**本文档中引用的文件**  
- [ActionEnums.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ActionEnums.java)
- [JobStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/JobStatus.java)
- [OrderStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/OrderStatus.java)
- [ReportStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ReportStatus.java)
- [TestLineStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java)
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/JobService.java)
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)
- [TestLineService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineService.java)
- [DataEntryService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/DataEntryService.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心枚举类型设计](#核心枚举类型设计)
3. [ActionEnums详解](#actionenums详解)
4. [JobStatus详解](#jobstatus详解)
5. [OrderStatus详解](#orderstatus详解)
6. [ReportStatus详解](#reportstatus详解)
7. [TestLineStatus详解](#testlinestatus详解)
8. [枚举在API契约中的使用](#枚举在api契约中的使用)
9. [枚举版本管理策略](#枚举版本管理策略)
10. [枚举与业务逻辑映射](#枚举与业务逻辑映射)
11. [序列化与反序列化考虑](#序列化与反序列化考虑)
12. [最佳实践与使用示例](#最佳实践与使用示例)

## 引言
枚举契约是otsnotes-service系统中用于定义和管理业务状态的核心机制。通过枚举类型，系统实现了对各种业务实体状态的标准化定义和统一管理，确保了数据的一致性和可维护性。本文档旨在全面介绍系统中的核心枚举类型设计规范，包括其定义、取值范围、业务含义以及在系统中的使用方式。文档将重点分析ActionEnums、JobStatus、OrderStatus、ReportStatus和TestLineStatus等关键枚举类型，并详细说明它们在API契约中的应用场景、版本管理策略以及与业务逻辑的映射关系。通过本文档，开发者可以深入了解枚举类型的设计原则和使用方法，为系统的开发和维护提供指导。

## 核心枚举类型设计
otsnotes-service系统中的枚举类型设计遵循统一的规范，确保了代码的可读性和可维护性。核心枚举类型主要位于`otsnotes-facade-model`模块的`enums`包中，这些枚举类型被设计为跨服务共享的数据契约，确保了不同服务间状态定义的一致性。

枚举类型的设计特点包括：
- **状态码与描述分离**：每个枚举值都包含一个整数状态码和一个字符串描述，便于在数据库存储和用户界面显示之间进行转换。
- **静态映射表**：大多数枚举类型都包含一个静态的`Map<Integer, EnumType>`映射表，用于快速通过状态码查找对应的枚举实例，提高了查询效率。
- **状态检查方法**：提供了静态的`check`方法，用于验证给定的状态码是否匹配指定的枚举值，简化了状态判断逻辑。
- **注解支持**：部分枚举类型使用了`@Dict`和`@DictCodeField`等注解，支持与字典服务的集成，便于在前端界面中进行状态显示。

枚举类型的设计充分考虑了扩展性和兼容性，通过统一的访问接口和静态方法，确保了在不同业务场景下的稳定使用。

**本文档中引用的文件**
- [ActionEnums.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ActionEnums.java)
- [JobStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/JobStatus.java)
- [OrderStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/OrderStatus.java)
- [ReportStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ReportStatus.java)
- [TestLineStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java)

## ActionEnums详解
ActionEnums枚举类型定义了系统中的各种操作动作，主要用于标识用户在系统中执行的具体操作。该枚举类型位于`otsnotes-facade-model`模块中，作为跨服务共享的操作类型契约。

### 枚举值定义
```java
public enum ActionEnums {
    JOB_VALIDATE("Job Validate"),
    RETEST("Lab Retest"),
    SAVE_SUBMIT("Submit Test Data"),
    VALIDATE("Valiate Test Data"),
    RETURN("Return Test Data"),
    SAVE("Save Conclusion"),
    REJECT("Reject Report"),
    CONFIRM_MATRIX("Confirm Matrix");
}
```

### 业务含义
- **JOB_VALIDATE**：作业验证，表示对作业进行验证操作。
- **RETEST**：实验室复测，表示启动复测流程。
- **SAVE_SUBMIT**：提交测试数据，表示用户提交测试数据。
- **VALIDATE**：验证测试数据，表示对已提交的测试数据进行验证。
- **RETURN**：退回测试数据，表示将测试数据退回给用户进行修改。
- **SAVE**：保存结论，表示保存测试结论。
- **REJECT**：拒绝报告，表示拒绝生成报告。
- **CONFIRM_MATRIX**：确认矩阵，表示确认测试矩阵。

### 使用场景
ActionEnums主要用于日志记录、权限控制和业务流程判断。例如，在用户执行某个操作时，系统会记录对应的操作类型，便于后续的审计和追踪。同时，系统可以根据不同的操作类型执行相应的业务逻辑，如权限验证、状态转换等。

**本文档中引用的文件**
- [ActionEnums.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ActionEnums.java)

## JobStatus详解
JobStatus枚举类型定义了作业的生命周期状态，是系统中作业管理的核心状态机。该枚举类型使用整数状态码和字符串描述的组合，便于在数据库中存储和在界面上显示。

### 枚举值定义
```java
public enum JobStatus {
    New(1101, "New"),
    Testing(1102, "Testing"),
    Closed(1103, "Closed"),
    Cancelled(1104, "Cancelled"),
    Validated(1105,"Validated");
}
```

### 业务含义
- **New (1101)**：新建状态，表示作业刚刚创建，尚未开始测试。
- **Testing (1102)**：测试中状态，表示作业正在进行测试。
- **Closed (1103)**：已关闭状态，表示作业已完成所有测试流程。
- **Cancelled (1104)**：已取消状态，表示作业被取消，不再进行测试。
- **Validated (1105)**：已验证状态，表示作业的测试结果已经过验证。

### 状态转换
作业状态的转换遵循严格的业务规则，通常的转换路径为：New → Testing → Validated → Closed。系统通过状态机模式管理这些转换，确保状态变更的合法性和一致性。

### 使用场景
JobStatus在作业管理服务中被广泛使用，如`JobService`类中通过状态码查询作业列表、更新作业状态等。状态码的使用使得数据库查询更加高效，而描述字段则便于在用户界面中显示友好的状态信息。

```mermaid
stateDiagram-v2
[*] --> New
New --> Testing : 开始测试
Testing --> Validated : 完成验证
Validated --> Closed : 关闭作业
Testing --> Cancelled : 取消测试
New --> Cancelled : 取消作业
```

**图示来源**
- [JobStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/JobStatus.java#L8-L62)
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/JobService.java#L0-L199)

**本文档中引用的文件**
- [JobStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/JobStatus.java)
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/JobService.java)

## OrderStatus详解
OrderStatus枚举类型定义了订单的生命周期状态，是订单管理模块的核心状态定义。该枚举类型通过整数状态码和字符串描述的组合，实现了状态的标准化管理。

### 枚举值定义
```java
public enum OrderStatus {
    New(1601, "New"),
    Testing(1602, "Testing"),
    Completed(1603,"Completed");
}
```

### 业务含义
- **New (1601)**：新建状态，表示订单刚刚创建，尚未开始处理。
- **Testing (1602)**：测试中状态，表示订单正在进行测试流程。
- **Completed (1603)**：已完成状态，表示订单的所有测试流程已经完成。

### 状态管理
OrderStatus枚举提供了静态的`maps`映射表，用于快速通过状态码查找对应的枚举实例。同时，提供了`getOrderStatus`和`check`等静态方法，简化了状态判断和查询操作。

### 使用场景
OrderStatus在订单管理服务中被广泛使用，如订单查询、状态更新等操作。通过统一的状态定义，确保了不同服务间订单状态的一致性，避免了状态定义的混乱。

**本文档中引用的文件**
- [OrderStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/OrderStatus.java)

## ReportStatus详解
ReportStatus枚举类型定义了报告的生命周期状态，是报告管理模块的核心状态机。值得注意的是，该枚举类型被标记为`@Deprecated`，表明它可能已被新的实现替代或正在被逐步淘汰。

### 枚举值定义
```java
@Deprecated
public enum ReportStatus {
    New(201, "New"),
    Cancelled(202, "Cancelled"),
    Approved(203, "Approved"),
    Draft(204, "Draft"),
    Reworked(205, "Reworked"),
    Combined(206, "Combined"),
    Replaced(207, "Replaced"),
    Completed(208, "Completed");
}
```

### 业务含义
- **New (201)**：新建状态，表示报告刚刚创建。
- **Cancelled (202)**：已取消状态，表示报告被取消生成。
- **Approved (203)**：已批准状态，表示报告已经过审批。
- **Draft (204)**：草稿状态，表示报告处于草稿阶段。
- **Reworked (205)**：返工状态，表示报告需要返工修改。
- **Combined (206)**：已合并状态，表示报告已与其他报告合并。
- **Replaced (207)**：已替换状态，表示报告已被新版本替换。
- **Completed (208)**：已完成状态，表示报告生成流程已完成。

### 使用场景
尽管被标记为废弃，ReportStatus仍在一些遗留代码中使用。在新的开发中，建议使用更新的状态管理机制。系统通过`check`方法族提供了灵活的状态判断功能，支持单个状态检查和多个状态的批量检查。

**本文档中引用的文件**
- [ReportStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ReportStatus.java)

## TestLineStatus详解
TestLineStatus枚举类型定义了测试行的生命周期状态，是测试管理模块中最复杂的状态机之一。该枚举类型涵盖了测试过程中的各种状态，包括正常流程和特殊情况。

### 枚举值定义
```java
public enum TestLineStatus {
    Typing(701, "Typing"),
    Submit(702, "Submitted"),
    Completed(703, "Completed"),
    SubContracted(704, "Subcontracted"),
    Entered(705, "Entered"),
    Cancelled(706, "Cancelled"),
    NC(707, "Not Test"),
    DR(708, "Document Review"),
    NA(709, "NA");
}
```

### 业务含义
- **Typing (701)**：录入中状态，表示测试数据正在录入。
- **Submit (702)**：已提交状态，表示测试数据已提交等待验证。
- **Completed (703)**：已完成状态，表示测试流程已完成。
- **SubContracted (704)**：已分包状态，表示测试任务已分包给外部实验室。
- **Entered (705)**：已录入状态，表示测试数据已录入系统。
- **Cancelled (706)**：已取消状态，表示测试任务被取消。
- **NC (707)**：不测试状态，表示该测试项目不需要进行测试。
- **DR (708)**：文件审核状态，表示测试结果正在文件审核中。
- **NA (709)**：不适用状态，表示该测试项目不适用于当前样品。

### 状态转换
TestLineStatus的状态转换较为复杂，涉及多种业务场景。系统通过`check`方法提供了灵活的状态判断功能，支持单个状态和多个状态的批量检查。

```mermaid
stateDiagram-v2
[*] --> Typing
Typing --> Submit : 提交数据
Submit --> DR : 进入审核
DR --> Completed : 审核通过
DR --> Typing : 审核退回
Typing --> Entered : 数据录入
Entered --> Completed : 完成测试
Typing --> Cancelled : 取消测试
Typing --> NC : 标记不测试
Typing --> NA : 标记不适用
Submit --> Cancelled : 取消提交
```

**图示来源**
- [TestLineStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java#L14-L99)
- [TestLineService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineService.java#L0-L199)

**本文档中引用的文件**
- [TestLineStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java)
- [TestLineService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineService.java)

## 枚举在API契约中的使用
枚举类型在API契约中扮演着重要角色，作为请求参数、响应字段和状态标识的标准数据类型。通过统一的枚举定义，确保了前后端之间的数据一致性。

### 作为请求参数
在API请求中，枚举类型通常作为查询条件或操作类型的参数。例如，在查询作业列表时，可以通过`JobStatus`枚举值作为筛选条件：
```java
public CustomResult getWorkSheetJobList(WorkSheetJobReq reqObject) {
    // 使用JobStatus作为查询条件
    JobStatus status = JobStatus.getJobStatus(reqObject.getStatus());
    if (status != null) {
        // 根据状态查询作业
    }
}
```

### 作为响应字段
在API响应中，枚举类型的状态码和描述被用于返回业务实体的状态信息。例如，作业查询接口会返回作业的当前状态：
```java
JobInfoPO jobInfo = jobInfoMapper.selectByPrimaryKey(jobNo);
JobStatus status = JobStatus.getJobStatus(jobInfo.getStatus());
response.setStatus(status.getStatus());
response.setStatusMessage(status.getMessage());
```

### 作为状态标识
枚举类型作为状态标识贯穿整个业务流程，从数据存储到业务逻辑判断，再到用户界面显示，都依赖于统一的枚举定义。这种设计确保了状态管理的一致性和可维护性。

**本文档中引用的文件**
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/JobService.java)
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)
- [TestLineService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineService.java)

## 枚举版本管理策略
枚举类型的版本管理是确保系统稳定性和兼容性的关键。otsnotes-service系统采用以下策略进行枚举版本管理：

### 新增枚举值
当需要新增业务状态时，应在枚举类型中添加新的枚举值，并确保状态码的唯一性。新增的枚举值应遵循现有的命名规范，并提供清晰的业务含义描述。

### 修改枚举值
原则上不允许修改已存在的枚举值的业务含义，以避免影响现有业务逻辑。如果必须修改，应通过添加新枚举值并标记旧值为废弃的方式来实现。

### 废弃枚举值
对于不再使用的枚举值，应使用`@Deprecated`注解进行标记，并在文档中说明废弃原因和替代方案。废弃的枚举值不应立即删除，而应保留一段时间以确保平滑过渡。

### 兼容性考虑
在进行枚举类型变更时，必须考虑向后兼容性。系统应能够正确处理未知的枚举值，避免因枚举值不存在而导致系统异常。

**本文档中引用的文件**
- [ReportStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/ReportStatus.java)

## 枚举与业务逻辑映射
枚举类型与业务逻辑之间存在紧密的映射关系，不同的枚举值对应着不同的业务行为和状态转换。

### 状态转换逻辑
每个枚举值都对应着特定的状态转换逻辑。例如，当`TestLineStatus`从`Typing`变为`Submit`时，系统会触发数据验证流程；当`JobStatus`变为`Validated`时，系统会启动报告生成流程。

### 业务行为触发
枚举值的变化往往伴随着业务行为的触发。例如，当订单状态从`New`变为`Testing`时，系统会分配测试资源；当报告状态变为`Approved`时，系统会通知相关方。

### 权限控制
枚举值也用于权限控制，不同的状态可能对应不同的操作权限。例如，只有在`TestLineStatus`为`Typing`时，用户才能编辑测试数据；在`Completed`状态下，数据将被锁定。

**本文档中引用的文件**
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/JobService.java)
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)
- [TestLineService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineService.java)

## 序列化与反序列化考虑
枚举类型的序列化和反序列化是确保数据在不同系统组件间正确传输的关键。

### JSON序列化
在JSON序列化时，枚举类型通常被序列化为状态码或枚举名称。系统通过Jackson等序列化框架的配置，确保枚举值能够正确地转换为JSON格式。

### 反序列化处理
在反序列化时，系统需要处理未知的枚举值。通常的做法是提供默认值或抛出特定的异常，避免因枚举值不存在而导致系统崩溃。

### 数据库存储
在数据库存储时，枚举类型的状态码被存储为整数，提高了查询效率。系统通过DAO层的转换逻辑，实现数据库值与枚举实例之间的相互转换。

**本文档中引用的文件**
- [JobStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/JobStatus.java)
- [TestLineStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java)

## 最佳实践与使用示例
### 使用静态方法进行状态检查
```java
// 推荐使用静态check方法进行状态检查
if (JobStatus.check(job.getStatus(), JobStatus.Testing)) {
    // 处理测试中状态的逻辑
}
```

### 避免直接比较状态码
```java
// 不推荐直接比较状态码
if (job.getStatus() == 1102) {
    // 逻辑
}

// 推荐使用枚举实例比较
if (JobStatus.getJobStatus(job.getStatus()) == JobStatus.Testing) {
    // 逻辑
}
```

### 处理未知状态
```java
// 在处理枚举值时，应考虑未知状态的情况
JobStatus status = JobStatus.getJobStatus(job.getStatus());
if (status == null) {
    // 处理未知状态
    logger.warn("未知的作业状态: " + job.getStatus());
    return;
}
```

通过遵循这些最佳实践，可以确保枚举类型的正确使用，提高代码的可读性和可维护性。

**本文档中引用的文件**
- [JobStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/JobStatus.java)
- [TestLineStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java)