# 响应代码枚举

<cite>
**本文档引用的文件**   
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java)
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java)
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java)
- [LanguageType.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/LanguageType.java)
- [LanguageConsts.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/constants/LanguageConsts.java)
</cite>

## 目录
1. [引言](#引言)
2. [响应代码设计与实现](#响应代码设计与实现)
3. [响应代码分类体系](#响应代码分类体系)
4. [响应代码在API响应中的应用](#响应代码在api响应中的应用)
5. [错误处理与异常管理](#错误处理与异常管理)
6. [响应代码层级结构与扩展规则](#响应代码层级结构与扩展规则)
7. [响应代码对照表](#响应代码对照表)
8. [初学者指南](#初学者指南)
9. [开发者最佳实践](#开发者最佳实践)
10. [国际化支持策略](#国际化支持策略)

## 引言
响应代码枚举是系统中用于标准化API响应状态的核心组件。它提供了一套统一的错误和成功状态码，确保客户端能够一致地理解和处理服务端的响应。本文档详细介绍了`ResponseCode`枚举的设计、使用方式、分类体系以及在系统中的实际应用，为开发人员提供全面的参考。

## 响应代码设计与实现

### 响应代码枚举结构
`ResponseCode`是一个Java枚举类，定义了系统中所有可能的响应状态。每个枚举项包含一个整数代码和对应的中文消息。

```java
public enum ResponseCode {
    SUCCESS(200, "操作成功"),
    ILLEGAL_ARGUMENT(100, "错误的请求参数"),
    ErrDuplicateReq(197, "重复请求"),
    FAIL(198, "请求处理失败"),
    TokenExpire(201, "用户权限过期，请重新登录"),
    UNKNOWN(500, "系统异常，请稍后重试");

    private int code;
    private String message;

    ResponseCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static ResponseCode getByCode(int code) {
        for (ResponseCode errorCode : ResponseCode.values()) {
            if (errorCode.getCode() == code) {
                return errorCode;
            }
        }
        return null;
    }
}
```

**Section sources**
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java#L1-L52)

### 响应代码与响应对象的关系
`ResponseCode`与`BaseResponse`类紧密配合，共同构建完整的API响应。`BaseResponse`作为标准响应包装类，包含状态码、消息和数据三个核心字段。

```mermaid
classDiagram
class ResponseCode {
+int code
+String message
+ResponseCode(int code, String message)
+int getCode()
+String getMessage()
+static ResponseCode getByCode(int code)
}
class BaseResponse {
+int status
+String message
+T data
+long versionId
+String stackTrace
+BaseResponse()
+static <T> BaseResponse newSuccessInstance(T data)
+static BaseResponse newFailInstance(ResponseCode errorCode)
+static BaseResponse newFailInstance(String errMessage)
}
BaseResponse --> ResponseCode : "引用"
```

**Diagram sources**
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java#L1-L52)
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java#L1-L175)

**Section sources**
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java#L1-L52)
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java#L1-L175)

## 响应代码分类体系

### 成功响应
成功响应表示请求被正确处理，客户端可以继续后续操作。

| 代码 | 枚举名称 | 消息内容 | 说明 |
|------|----------|----------|------|
| 200 | SUCCESS | 操作成功 | 通用成功状态码 |

### 客户端错误
客户端错误表示请求存在问题，需要客户端修改请求后重新发送。

| 代码 | 枚举名称 | 消息内容 | 说明 |
|------|----------|----------|------|
| 100 | ILLEGAL_ARGUMENT | 错误的请求参数 | 请求参数验证失败 |
| 197 | ErrDuplicateReq | 重复请求 | 防止重复提交 |
| 198 | FAIL | 请求处理失败 | 明确知道失败原因但客户端不关心的具体错误 |

### 服务器错误
服务器错误表示服务端在处理请求时发生异常。

| 代码 | 枚举名称 | 消息内容 | 说明 |
|------|----------|----------|------|
| 201 | TokenExpire | 用户权限过期，请重新登录 | 认证令牌过期 |
| 500 | UNKNOWN | 系统异常，请稍后重试 | 未预期的系统异常 |

**Section sources**
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java#L1-L52)

## 响应代码在API响应中的应用

### 成功响应构建
当业务逻辑成功执行时，使用`BaseResponse`的静态工厂方法创建成功响应。

```java
public static <T> BaseResponse newInstance(T data) {
    BaseResponse baseResponse = new BaseResponse();
    if (data == null){
        baseResponse.setStatus(ResponseCode.UNKNOWN.getCode());
        baseResponse.setMessage(ResponseCode.UNKNOWN.getMessage());
        return baseResponse;
    }
    baseResponse.setStatus(ResponseCode.SUCCESS.getCode());
    baseResponse.setMessage(ResponseCode.SUCCESS.getMessage());
    baseResponse.setData(data);
    return baseResponse;
}
```

### 失败响应构建
根据不同的错误情况，构建相应的失败响应。

```java
public static BaseResponse newFailInstance(ResponseCode errorCode) {
    BaseResponse result = new BaseResponse();
    result.setStatus(errorCode.getCode());
    result.setMessage(errorCode.getMessage());
    return result;
}

public static BaseResponse newFailInstance(String errMessage) {
    BaseResponse result = new BaseResponse();
    result.setStatus(ResponseCode.ILLEGAL_ARGUMENT.getCode());
    result.setMessage(errMessage);
    return result;
}
```

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Controller as "控制器"
participant Service as "服务层"
participant ResponseBuilder as "响应构建器"
Client->>Controller : 发送请求
Controller->>Service : 调用业务逻辑
alt 业务成功
Service->>ResponseBuilder : 调用BaseResponse.newInstance(data)
ResponseBuilder->>Service : 返回成功响应
Service->>Controller : 返回成功结果
Controller->>Client : 返回{status : 200, message : "操作成功", data : ...}
else 业务失败
Service->>ResponseBuilder : 调用BaseResponse.newFailInstance(errorCode)
ResponseBuilder->>Service : 返回失败响应
Service->>Controller : 返回错误结果
Controller->>Client : 返回{status : 100, message : "错误的请求参数"}
end
```

**Diagram sources**
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java#L1-L175)

**Section sources**
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java#L1-L175)

## 错误处理与异常管理

### 业务异常设计
`BizException`是系统中的业务异常基类，它封装了`ResponseCode`，使得异常与响应代码直接关联。

```java
public class BizException extends RuntimeException {
    private ResponseCode errorCode;

    public BizException(ResponseCode errorCode, String msg) {
        this(errorCode, msg, null);
    }

    public BizException(String msg) {
        this(ResponseCode.UNKNOWN, msg);
    }

    public BizException(ResponseCode errorCode) {
        this(errorCode, errorCode.getMessage(), null);
    }

    public BizException(String msg, Throwable cause) {
        this(ResponseCode.FAIL, msg, cause);
    }

    public BizException(ResponseCode errorCode, String msg, Throwable cause) {
        super(msg, cause);
        if (errorCode == null) {
            throw new IllegalArgumentException("errorCode is null");
        }
        this.errorCode = errorCode;
    }

    public ResponseCode getErrorCode() {
        return errorCode;
    }

    public static void throwBizException(ResponseCode errorCode, String message, Throwable cause) {
        throw new BizException(errorCode, message, cause);
    }

    public static void throwBizException(ResponseCode errorCode, String message) {
        throwBizException(errorCode, message, null);
    }
}
```

### 异常处理流程
系统通过统一的异常处理机制，将`BizException`转换为标准的API响应。

```mermaid
flowchart TD
Start([开始处理请求]) --> BusinessLogic["执行业务逻辑"]
BusinessLogic --> HasException{"发生异常?"}
HasException --> |是| IsBizException{"是BizException?"}
IsBizException --> |是| GetErrorCode["获取ResponseCode"]
GetErrorCode --> BuildResponse["构建BaseResponse"]
BuildResponse --> ReturnResponse["返回响应"]
IsBizException --> |否| WrapAsUnknown["包装为UNKNOWN错误"]
WrapAsUnknown --> BuildResponse
HasException --> |否| BuildSuccess["构建成功响应"]
BuildSuccess --> ReturnResponse
ReturnResponse --> End([结束])
```

**Diagram sources**
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java#L1-L49)
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java#L1-L175)

**Section sources**
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java#L1-L49)

## 响应代码层级结构与扩展规则

### 层级结构
响应代码采用扁平化的枚举结构，所有代码在同一层级定义。这种设计简化了代码管理和查找。

```mermaid
graph TD
A[ResponseCode] --> B[SUCCESS]
A --> C[ILLEGAL_ARGUMENT]
A --> D[ErrDuplicateReq]
A --> E[FAIL]
A --> F[TokenExpire]
A --> G[UNKNOWN]
```

### 扩展规则
当需要添加新的响应代码时，应遵循以下规则：
1. 选择合适的代码范围：100-199用于客户端错误，200-299用于成功响应，500-599用于服务器错误
2. 提供清晰的中文消息
3. 在`ResponseCode`枚举中添加新的枚举项
4. 更新相关文档

**Section sources**
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java#L1-L52)

## 响应代码对照表

| 代码 | 枚举名称 | 消息内容 | 错误级别 | 处理建议 |
|------|----------|----------|----------|----------|
| 200 | SUCCESS | 操作成功 | 成功 | 继续正常流程 |
| 100 | ILLEGAL_ARGUMENT | 错误的请求参数 | 客户端错误 | 检查请求参数并修正 |
| 197 | ErrDuplicateReq | 重复请求 | 客户端错误 | 避免重复提交相同请求 |
| 198 | FAIL | 请求处理失败 | 客户端错误 | 检查业务逻辑和数据 |
| 201 | TokenExpire | 用户权限过期，请重新登录 | 客户端错误 | 重新登录获取新令牌 |
| 500 | UNKNOWN | 系统异常，请稍后重试 | 服务器错误 | 稍后重试或联系技术支持 |

**Section sources**
- [ResponseCode.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/ResponseCode.java#L1-L52)

## 初学者指南

### 基本概念
响应代码是API通信中的状态指示器，类似于HTTP状态码。它告诉客户端请求的处理结果。

### 常见场景
- **成功处理**：返回`SUCCESS(200)`，表示请求被正确处理
- **参数错误**：返回`ILLEGAL_ARGUMENT(100)`，表示客户端发送的参数有问题
- **系统异常**：返回`UNKNOWN(500)`，表示服务端发生未预期的错误

### 快速上手
```java
// 创建成功响应
BaseResponse successResponse = BaseResponse.newInstance(resultData);

// 创建失败响应
BaseResponse failResponse = BaseResponse.newFailInstance(ResponseCode.ILLEGAL_ARGUMENT);
```

## 开发者最佳实践

### 使用静态工厂方法
优先使用`BaseResponse`提供的静态工厂方法来创建响应，而不是手动设置字段。

```java
// 推荐做法
return BaseResponse.newInstance(data);

// 不推荐做法
BaseResponse response = new BaseResponse();
response.setStatus(200);
response.setMessage("操作成功");
response.setData(data);
return response;
```

### 异常处理
使用`BizException`来抛出业务异常，并指定相应的`ResponseCode`。

```java
// 正确的异常处理
if (StringUtils.isEmpty(orderNo)) {
    throw new BizException(ResponseCode.ILLEGAL_ARGUMENT, "订单号不能为空");
}
```

### 代码可读性
在代码中直接使用枚举常量，提高代码的可读性和可维护性。

```java
// 好的代码风格
if (response.getStatus() == ResponseCode.SUCCESS.getCode()) {
    // 处理成功逻辑
}

// 避免直接使用魔法数字
if (response.getStatus() == 200) {
    // 处理成功逻辑
}
```

**Section sources**
- [BaseResponse.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BaseResponse.java#L1-L175)
- [BizException.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/common/BizException.java#L1-L49)

## 国际化支持策略

### 语言支持
系统支持多种语言，通过`LanguageType`枚举定义。

```java
public enum LanguageType {
    EN(1, "EN","ENG","English Report"),
    CHI(2, "CHI","ZHS","Chinese Report");
}
```

### 多语言实现
虽然当前响应消息是中文，但系统具备多语言支持的基础架构。可以通过以下方式实现国际化：

1. **消息资源文件**：为不同语言创建消息资源文件
2. **语言解析器**：根据请求头中的语言偏好选择合适的语言
3. **动态消息获取**：在构建响应时根据客户端语言偏好获取对应语言的消息

```java
// 伪代码示例
public String getMessageByLocale(ResponseCode code, String locale) {
    // 根据locale从资源文件中获取对应语言的消息
    return messageSource.getMessage(code.name(), null, locale);
}
```

### 当前状态
目前系统中的响应消息均为中文，但架构设计考虑了未来的国际化需求。`LanguageConsts`类定义了系统支持的语言类型。

```java
public class LanguageConsts {
    public static final String LANGUAGE_ENGLISH = "English Report";
    public static final String LANGUAGE_GB_CHINESE = "GB Market Chinese Report";
    public static final String LANGUAGE_GB_CHINESE_ENGLISH = "GB Market Chinese/English Report";
    public static final String LANGUAGE_CHINESE_TRANSLATION = "Report in Chinese Translation";
}
```

**Section sources**
- [LanguageType.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/LanguageType.java#L1-L98)
- [LanguageConsts.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/constants/LanguageConsts.java#L1-L33)