# 业务代码枚举

<cite>
**本文档中引用的文件**   
- [BizCodeEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/BizCodeEnum.java#L0-L40)
</cite>

## 目录
1. [简介](#简介)
2. [业务代码枚举设计](#业务代码枚举设计)
3. [枚举定义与结构](#枚举定义与结构)
4. [业务含义与应用场景](#业务含义与应用场景)
5. [使用方式与最佳实践](#使用方式与最佳实践)
6. [状态管理与流转](#状态管理与流转)
7. [扩展规则与维护](#扩展规则与维护)
8. [国际化考虑](#国际化考虑)

## 简介
本文档详细介绍了`BizCodeEnum`业务代码枚举的设计、实现和使用方式。该枚举是系统中用于表示业务状态和操作结果的核心组件，通过标准化的代码值来标识不同的业务场景。文档将全面解析其分类体系、编码规则和在订单、分包、报告、样品、测试线等核心业务领域的应用。

## 业务代码枚举设计
`BizCodeEnum`枚举的设计遵循了简洁性和可扩展性的原则，旨在为系统提供统一的状态标识机制。该枚举位于`otsnotes-facade-model`模块中，作为跨服务通信的数据契约的一部分，确保了不同服务间对业务状态的理解一致性。

**Section sources**
- [BizCodeEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/BizCodeEnum.java#L0-L40)

## 枚举定义与结构
`BizCodeEnum`枚举定义了两个基本的业务代码值，每个值包含一个整型代码和对应的描述信息。

```mermaid
classDiagram
class BizCodeEnum {
+None(0, "None")
+Exist(1, "Exist")
+getCode() int
+message() String
+getByCode(Integer) BizCodeEnum
}
```

**Diagram sources**
- [BizCodeEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/BizCodeEnum.java#L0-L40)

**Section sources**
- [BizCodeEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/BizCodeEnum.java#L0-L40)

## 业务含义与应用场景
### None 枚举值
- **代码值**: 0
- **业务含义**: 表示"无"或"不存在"的状态
- **适用场景**: 
  - 用于标识某个资源或记录不存在
  - 在查询操作中表示未找到匹配项
  - 作为默认状态值，表示尚未设置或初始化

### Exist 枚举值
- **代码值**: 1
- **业务含义**: 表示"存在"或"已存在"的状态
- **适用场景**: 
  - 用于标识某个资源或记录已存在
  - 在数据校验中表示对象已创建
  - 作为激活或启用状态的标识

**Section sources**
- [BizCodeEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/BizCodeEnum.java#L0-L40)

## 使用方式与最佳实践
### 基本使用方法
```java
// 获取枚举实例
BizCodeEnum code = BizCodeEnum.getByCode(0);

// 获取代码值
int codeValue = BizCodeEnum.None.getCode();

// 获取描述信息
String message = BizCodeEnum.Exist.message();
```

### 最佳实践
1. **统一使用枚举**: 在业务逻辑中应始终使用`BizCodeEnum`枚举而非硬编码的数字值
2. **空值处理**: 使用`getByCode`方法时需注意处理null输入，该方法会返回null
3. **类型安全**: 利用枚举的类型安全性，避免错误的代码值使用

**Section sources**
- [BizCodeEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/BizCodeEnum.java#L0-L40)

## 状态管理与流转
`BizCodeEnum`在系统中主要用于简单的二元状态管理，其状态流转相对直接：

```mermaid
stateDiagram-v2
[*] --> None
None --> Exist : "创建/激活"
Exist --> None : "删除/禁用"
None --> None : "保持不存在"
Exist --> Exist : "保持存在"
```

**Diagram sources**
- [BizCodeEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/BizCodeEnum.java#L0-L40)

**Section sources**
- [BizCodeEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/BizCodeEnum.java#L0-L40)

## 扩展规则与维护
虽然当前`BizCodeEnum`只定义了两个基本状态，但其设计支持未来的扩展。如果需要添加新的业务代码，应遵循以下规则：
1. **保持语义清晰**: 新增的枚举值应有明确的业务含义
2. **避免冲突**: 确保代码值不重复
3. **文档同步**: 更新相关文档以反映新的枚举值
4. **向后兼容**: 考虑现有代码对新枚举值的处理

**Section sources**
- [BizCodeEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/BizCodeEnum.java#L0-L40)

## 国际化考虑
`BizCodeEnum`通过`@DictLabelField`注解支持国际化。系统可以根据用户的语言设置返回相应的描述信息。当前实现中，"None"和"Exist"作为基础标识，其国际化主要体现在上层业务逻辑的展示层，而非枚举本身的多语言支持。

**Section sources**
- [BizCodeEnum.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/BizCodeEnum.java#L0-L40)