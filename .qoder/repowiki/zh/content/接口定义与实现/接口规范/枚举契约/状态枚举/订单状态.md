# 订单状态

<cite>
**本文档引用的文件**   
- [OrderStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/OrderStatus.java)
- [OrderStatusRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/OrderStatusRsp.java)
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/JobService.java)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java)
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java)
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java)
- [TestLineService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineService.java)
- [OrderStatusValidator.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/fast/processor/validator/OrderStatusValidator.java)
</cite>

## 目录
1. [订单状态枚举定义](#订单状态枚举定义)
2. [订单状态取值范围与业务含义](#订单状态取值范围与业务含义)
3. [状态转换规则与触发条件](#状态转换规则与触发条件)
4. [订单状态转换图](#订单状态转换图)
5. [订单状态在业务流程中的应用](#订单状态在业务流程中的应用)
6. [状态校验与转换代码示例](#状态校验与转换代码示例)
7. [版本管理与向后兼容性](#版本管理与向后兼容性)

## 订单状态枚举定义

订单状态枚举 `OrderStatus` 定义在 `otsnotes-facade-model` 模块中，位于 `com.sgs.otsnotes.facade.model.enums` 包下。该枚举实现了订单状态的集中管理，通过整数状态码和字符串消息来表示不同的订单状态。

```java
@Dict(name = "NotesOrderStatus")
public enum OrderStatus {
    New(1601, "New"),
    Testing(1602, "Testing"),
    Completed(1603,"Completed");

    @DictCodeField
    private Integer status;
    @DictLabelField
    private String message;

    OrderStatus(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, OrderStatus> maps = new HashMap<Integer, OrderStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (OrderStatus enu : OrderStatus.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static OrderStatus getOrderStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    public static boolean check(Integer status, OrderStatus orderStatus) {
        if (status == null || !maps.containsKey(status)){
            return false;
        }
        return maps.get(status) == orderStatus;
    }
}
```

该枚举具有以下特点：
- 使用 `@Dict` 注解标记，表明这是一个字典枚举，名称为 "NotesOrderStatus"
- 每个状态值都有对应的 `@DictCodeField`（状态码）和 `@DictLabelField`（状态消息）
- 提供了静态 `maps` 映射，便于通过状态码快速查找对应的状态枚举值
- 提供了 `getOrderStatus` 静态方法，用于根据状态码获取对应的状态枚举
- 提供了 `check` 静态方法，用于验证给定的状态码是否匹配指定的状态枚举

**节来源**
- [OrderStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/OrderStatus.java)

## 订单状态取值范围与业务含义

订单状态枚举目前定义了三个主要状态值，每个状态都有其特定的业务语义：

### 新建 (New)
- **状态码**: 1601
- **英文标识**: "New"
- **业务含义**: 订单刚刚创建，处于初始状态。此时订单信息已录入系统，但尚未开始任何处理流程。这是订单生命周期的起点。

### 测试中 (Testing)
- **状态码**: 1602
- **英文标识**: "Testing"
- **业务含义**: 订单已进入测试阶段，相关的测试任务已经开始执行。此状态表明订单正在被处理，实验室已经开始对样品进行分析和测试。

### 已完成 (Completed)
- **状态码**: 1603
- **英文标识**: "Completed"
- **业务含义**: 订单的所有测试任务都已成功完成。此状态表示订单的测试阶段已经结束，可以进入报告生成等后续流程。

这些状态值构成了订单的核心生命周期，从创建到测试再到完成，反映了订单在系统中的主要流转过程。

**节来源**
- [OrderStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/OrderStatus.java)

## 状态转换规则与触发条件

订单状态的转换遵循特定的业务规则，这些规则确保了订单状态的合理流转。通过分析代码库中的实现，可以确定以下状态转换规则：

### 从新建到测试中
当订单从 "新建" 状态转换到 "测试中" 状态时，通常发生在以下场景：
- 订单确认后，实验室开始接收样品（LabIn）
- 系统检测到订单状态为 "新建" 或 "已确认"，并且有测试任务需要执行

在 `JobService.java` 文件中，可以看到相关的实现逻辑：
```java
if(oldStatus == 3 || oldStatus==1 ){
    //call preOrder changeOrderStatus
    logger.info("orderNo:{} 第一次labIn时，调用preorder接口更新状态，oldStatus:{}-newStatus:{}",orderNo,oldStatus,8);

    // POSL-6448 修改为通用的调用逻辑
    SysStatusReq statusReq = new SysStatusReq();
    statusReq.setObjectNo(orderNo);
    statusReq.setOldStatus(oldStatus);
    statusReq.setNewStatus(OrderStatus.Testing.getStatus());
    statusReq.setUserName(user.getRegionAccount());
    CustomResult statusResult = statusClient.insertStatusInfo(statusReq);
}
```

### 从测试中到已完成
当所有测试任务完成后，订单状态会从 "测试中" 转换到 "已完成"。在 `TestLineService.java` 中，`orderStatusValidate` 方法实现了这一逻辑：
```java
public void orderStatusValidate(String orderId) {
    GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(orderId);
    int orderStatusByOrderNo = orderClient.getOrderStatusByOrderNo(order.getOrderNo());
    if (com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(orderStatusByOrderNo, com.sgs.preorder.facade.model.enums.OrderStatus.Completed)) {
        return;
    }

    List<TestLineInstancePO> testLineInstancePOS = testLineRepository.getTestLineByOrderId(orderId);
    //过滤出非Pretreatment TL
    testLineInstancePOS = testLineInstancePOS.stream().filter(tl->!TestLineType.check(tl.getTestLineType(), TestLineType.Pretreatment)).collect(Collectors.toList());
    //存在测试TL 非 NC,DR,Cancelled
    long count = testLineInstancePOS.stream()
            .filter(testline -> !TestLineStatus.check(testline.getTestLineStatus(),TestLineStatus.NC,TestLineStatus.DR,TestLineStatus.Cancelled))
            .count();
    //没有TL了 不做处理
    if(count==0){
        return;
    }
    //找出所有completed的TL
    long validCount = testLineInstancePOS.stream()
            .filter(testline ->TestLineStatus.check(testline.getTestLineStatus(),TestLineStatus.Completed))
            .count();
    //没有completed TL 不做处理
    if(validCount==0){
        return;
    }
    //所有tl 都是complete 可以修改状态了
    if (count == validCount) {
        UserInfo localUser = UserHelper.getLocalUser();
        String user = localUser == null ?"System":localUser.getRegionAccount();
        order.setOrderStatus(OrderStatus.Completed.getStatus());
        order.setModifiedBy(user);
        order.setModifiedDate(new Date());
        orderMapper.updateOrderStatus(order);
        SysStatusReq reqStatus = new SysStatusReq();
        reqStatus.setObjectNo(order.getOrderNo());
        reqStatus.setIgnoreOldStatus(true);
        reqStatus.setNewStatus(com.sgs.preorder.facade.model.enums.OrderStatus.Reporting.getStatus());
        reqStatus.setUserName(user);
        statusClient.insertStatusInfo(reqStatus);
    }
}
```

此方法的逻辑是：当订单中所有非预处理的测试任务都已完成时，将订单状态更新为 "已完成"。

**节来源**
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/JobService.java#L329-L346)
- [TestLineService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineService.java#L2705-L2750)

## 订单状态转换图

```mermaid
stateDiagram-v2
[*] --> New
New --> Testing : 第一次LabIn
Testing --> Completed : 所有测试任务完成
Completed --> [*]
```

**图来源**
- [OrderStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/OrderStatus.java)
- [JobService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/JobService.java#L329-L346)
- [TestLineService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineService.java#L2705-L2750)

## 订单状态在业务流程中的应用

订单状态在系统的多个业务流程中发挥着关键作用，主要用于订单生命周期管理、业务流程控制和状态验证。

### 订单生命周期管理
订单状态是订单生命周期的核心标识，系统通过状态来跟踪订单的当前阶段。例如，在 `OrderService.java` 中，创建新订单时会将其状态设置为 "新建"：
```java
order.setOrderStatus(OrderStatus.New.getStatus());
```

### 业务流程控制
订单状态用于控制业务流程的执行。例如，在 `ReportService.java` 中，系统会根据订单状态来决定是否允许执行某些操作：
```java
Integer orderStatus = data.getOrderStatus();
if(com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(orderStatus,com.sgs.preorder.facade.model.enums.OrderStatus.Closed)){
    result.setMsg(reportNo + ":The Order is already closed!!");
    return result;
}
if(com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(orderStatus,com.sgs.preorder.facade.model.enums.OrderStatus.Pending)){
    result.setMsg(reportNo + ":The Order is already pending!!");
    return result;
}
if(com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(orderStatus,com.sgs.preorder.facade.model.enums.OrderStatus.Cancelled)){
    result.setMsg(reportNo + ":The Order is already cancelled!!");
    return result;
}
if(com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(orderStatus,com.sgs.preorder.facade.model.enums.OrderStatus.Completed)){
    result.setMsg(reportNo + ":The Order is already completed!!");
    return result;
}
```

### 状态验证
订单状态验证是确保业务逻辑正确执行的重要手段。在 `OrderStatusValidator.java` 中，实现了订单状态的验证逻辑：
```java
@Component
public class OrderStatusValidator implements BaseStepProcessor {
    
    @Override
    public CustomResult process(FastReturnContext context) {
        String orderNo = context.getOrderNo();
        logger.info("执行订单状态验证 - OrderNo: {}", orderNo);
        
        CustomResult result = new CustomResult();

        // DIG-9329 Fast TL 需要先操作LabIn
        CustomResult<Boolean> statusResult = statusClient.getOrderStatus(orderNo,-1, OrderStatusEnum.Testing);
        Boolean isCompleted = statusResult.getData();
        if (!statusResult.isSuccess() || isCompleted == null || !isCompleted){
            return statusResult.fail("Fail to send back for Order Status not being Testing!");
        }

        result.setSuccess(Boolean.TRUE);
        result.setMsg("订单状态验证通过");
        return result;
    }
}
```

**节来源**
- [OrderService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/OrderService.java#L265)
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java#L1206-L1218)
- [OrderStatusValidator.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/fast/processor/validator/OrderStatusValidator.java#L0-L61)

## 状态校验与转换代码示例

### 状态校验
系统提供了多种方式来校验订单状态。最常用的是通过 `OrderStatus.check` 静态方法：

```java
// 检查订单状态是否为"已完成"
if (OrderStatus.check(orderStatus, OrderStatus.Completed)) {
    // 执行相关操作
}

// 在ReportService中检查多个状态
if (!OrderStatus.checkStatus(orderStatus, OrderStatus.New, OrderStatus.Confirmed, OrderStatus.Testing, OrderStatus.Reporting) ||
    ReportStatus.check(reportStatusByOrderNo, ReportStatus.Approved)) {
    rspResult.setMsg("Can't Copy Limit! Only can Copy Limit when Order Status was New/Confirmed/Testing/Reporting. (And the Report Status was not Approved.)");
    return rspResult;
}
```

### 状态转换
状态转换通常通过调用状态服务来实现。在 `SubContractService.java` 中，可以看到状态转换的示例：

```java
public void syncOrderTestingToPreOrder(String subContractNo, String currentOrderNo) {
    logger.info("分包：{} 触发order：{} 变更Testing开始", subContractNo, currentOrderNo);
    SysStatusReq sysStatusReq = new SysStatusReq();
    sysStatusReq.setUserName("System");
    sysStatusReq.setIgnoreOldStatus(true);
    sysStatusReq.setNewStatus(com.sgs.preorder.facade.model.enums.OrderStatus.Testing.getStatus());
    sysStatusReq.setObjectNo(currentOrderNo);
    statusClient.insertStatusInfo(sysStatusReq);
    logger.info("分包：{} 触发order：{} 变更Testing结束", subContractNo, currentOrderNo);
}
```

此代码展示了如何通过 `SysStatusReq` 请求对象来触发订单状态的变更。

**节来源**
- [ReportService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/ReportService.java#L2713-L2715)
- [SubContractService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/SubContractService.java#L3862-L3870)

## 版本管理与向后兼容性

订单状态枚举的版本管理策略主要体现在以下几个方面：

### 静态映射维护
通过 `maps` 静态字段维护状态码与枚举值的映射关系，确保了向后兼容性：
```java
public static final Map<Integer, OrderStatus> maps = new HashMap<Integer, OrderStatus>() {
    private static final long serialVersionUID = -8986866330615001847L;
    {
        for (OrderStatus enu : OrderStatus.values()) {
            put(enu.getStatus(), enu);
        }
    }
};
```

### 安全的状态获取
`getOrderStatus` 方法在获取状态时会进行空值和存在性检查，避免了因无效状态码导致的异常：
```java
public static OrderStatus getOrderStatus(Integer status) {
    if (status == null || !maps.containsKey(status)) {
        return null;
    }
    return maps.get(status);
}
```

### 灵活的状态检查
`check` 方法提供了灵活的状态验证机制，支持单个或多个状态的检查，便于业务逻辑的扩展：
```java
public static boolean check(Integer status, OrderStatus orderStatus) {
    if (status == null || !maps.containsKey(status)){
        return false;
    }
    return maps.get(status) == orderStatus;
}
```

这种设计使得在新增状态值时，只需在枚举中添加新的状态定义，而不会影响现有代码的正常运行，从而保证了系统的向后兼容性。

**节来源**
- [OrderStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/OrderStatus.java)