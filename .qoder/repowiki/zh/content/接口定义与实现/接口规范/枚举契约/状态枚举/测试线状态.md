# 测试线状态

<cite>
**本文档引用的文件**
- [TestLineStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java)
- [TestLineStatusManager.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineStatusManager.java)
- [TestLineStatusInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/testline/TestLineStatusInfo.java)
- [UpdateTestLineStatusReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/req/testLine/UpdateTestLineStatusReq.java)
- [TestLineStatusRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/testLine/TestLineStatusRsp.java)
</cite>

## 目录
1. [引言](#引言)
2. [测试线状态枚举定义](#测试线状态枚举定义)
3. [状态值业务含义](#状态值业务含义)
4. [状态转换规则](#状态转换规则)
5. [状态管理器实现](#状态管理器实现)
6. [状态转换流程图](#状态转换流程图)
7. [应用示例](#应用示例)
8. [版本管理策略](#版本管理策略)

## 引言
本文档详细介绍了测试线（TestLine）状态管理系统的设计与实现。测试线状态是实验流程管理中的核心概念，用于跟踪和控制测试任务在其生命周期中的各个阶段。本文档全面解释了TestLineStatus枚举的定义、取值范围、业务语义以及状态转换规则，并提供了状态管理器的实现细节和应用示例，旨在为开发人员和业务用户提供完整的参考。

## 测试线状态枚举定义
测试线状态通过`TestLineStatus`枚举类进行定义，该枚举位于`otsnotes-facade-model`模块中，是系统中所有服务共享的状态定义。

```java
public enum TestLineStatus {
    Typing(701, "Typing"),
    Submit(702, "Submitted"),
    Completed(703, "Completed"),
    SubContracted(704, "Subcontracted"),
    Entered(705, "Entered"),
    Cancelled(706, "Cancelled"),
    NC(707, "Not Test"),
    DR(708, "Document Review"),
    NA(709, "NA");
    
    private int status;
    private String message;
    
    // 枚举构造函数、getter方法和静态工具方法
}
```

该枚举为每个状态值分配了唯一的整数代码和描述性消息，通过静态`maps`字段实现了状态码到枚举实例的快速查找。

**Section sources**
- [TestLineStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java#L1-L101)

## 状态值业务含义
每个测试线状态值都代表了测试任务生命周期中的一个特定阶段，具有明确的业务语义。

### 状态值列表
以下表格详细列出了所有测试线状态值及其业务含义：

| 状态码 | 状态值 | 描述 | 业务含义 |
|--------|--------|------|----------|
| 701 | Typing | Typing | 测试线处于数据录入阶段，实验数据正在被输入系统 |
| 702 | Submit | Submitted | 测试线数据已提交，等待进一步处理或审核 |
| 703 | Completed | Completed | 测试线已全部完成，所有实验和验证工作已结束 |
| 704 | SubContracted | Subcontracted | 测试线已外包给第三方实验室进行处理 |
| 705 | Entered | Entered | 测试线数据已录入系统，但尚未提交 |
| 706 | Cancelled | Cancelled | 测试线已被取消，不再进行后续处理 |
| 707 | NC | Not Test | 测试线被标记为"不测试"，通常用于排除某些测试项目 |
| 708 | DR | Document Review | 测试线处于文档审核阶段，等待质量审查 |
| 709 | NA | NA | 测试线状态为"不适用"，用于特殊情况 |

**Section sources**
- [TestLineStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java#L1-L101)

## 状态转换规则
测试线状态的转换遵循严格的业务规则，确保实验流程的完整性和数据的一致性。

### 状态转换约束
状态转换由`TestLineStatusManager`类中的`checkTestLineStatus`方法进行验证。该方法接收一个操作类型（`ActionTestLineStatusMatrixEnum`）和一组测试线实例，检查当前状态是否允许执行指定操作。

```java
public static CustomResult checkTestLineStatus(
    ActionTestLineStatusMatrixEnum actionTestLineStatusMatrixEnum, 
    List<TestLineInstancePO> testLineInstancePOS) {
    
    CustomResult customResult = new CustomResult();
    customResult.setSuccess(true);

    if (CollectionUtils.isEmpty(testLineInstancePOS)) {
        customResult.setMsg("参数testLineInstancePOS为空");
        return customResult;
    }

    // 检查每个测试线实例的当前状态是否在允许的状态列表中
    testLineInstancePOS.stream().filter(testLineInstancePO -> {
        List<Integer> vals = Lists.newArrayList();
        for (TestLineStatus allowTestLineStatus : 
             actionTestLineStatusMatrixEnum.getAllowTestLineStatus()) {
            vals.add(allowTestLineStatus.getStatus());
        }
        return !vals.contains(testLineInstancePO.getTestLineStatus());
    }).findAny().ifPresent(c -> {
        TestLineStatus[] allowTestLineStatus = 
            actionTestLineStatusMatrixEnum.getAllowTestLineStatus();
        String msg = Joiner.on(",").join(Arrays.stream(allowTestLineStatus)
            .map(m -> m.getMessage()).collect(Collectors.toList()));
        customResult.setMsg(msg);
        customResult.setSuccess(false);
    });

    return customResult;
}
```

### 转换触发条件
状态转换通常由特定的业务操作触发，这些操作通过`UpdateTestLineStatusReq`请求对象进行封装：

```java
public class UpdateTestLineStatusReq extends BaseRequest {
    private TestLineModuleType moduleType; // 操作类型
    private List<TestLineStatusInfo> testLines; // 要更新的测试线列表
}
```

`moduleType`字段定义了触发状态转换的具体操作类型，如数据录入、提交、验证等。

**Section sources**
- [TestLineStatusManager.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineStatusManager.java#L1-L51)
- [UpdateTestLineStatusReq.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/req/testLine/UpdateTestLineStatusReq.java#L1-L33)

## 状态管理器实现
`TestLineStatusManager`是测试线状态管理的核心组件，负责状态检查和验证。

### 核心功能
状态管理器提供了以下核心功能：

1. **状态检查**: 验证一组测试线实例是否处于允许执行特定操作的状态
2. **状态查找**: 通过状态码快速查找对应的枚举实例
3. **状态验证**: 检查当前状态是否符合预期状态之一

### 工具方法
`TestLineStatus`枚举提供了多个静态工具方法来简化状态操作：

- `getMessage(Integer status)`: 根据状态码获取状态描述
- `findStatus(Integer status)`: 根据状态码查找枚举实例
- `check(Integer status, TestLineStatus... testLineStatus)`: 检查状态码是否匹配任意一个指定状态

```java
public static boolean check(Integer status, TestLineStatus... testLineStatus) {
    if (status == null || !maps.containsKey(status) || 
        testLineStatus == null || testLineStatus.length <= 0){
        return false;
    }
    for (TestLineStatus tlStatus: testLineStatus){
        if (status != null && status == tlStatus.getStatus()){
            return true;
        }
    }
    return false;
}
```

**Section sources**
- [TestLineStatusManager.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineStatusManager.java#L1-L51)
- [TestLineStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java#L52-L99)

## 状态转换流程图
以下状态图可视化展示了测试线状态之间的转换路径和约束条件。

```mermaid
stateDiagram-v2
[*] --> Typing
Typing --> Entered : 数据录入完成
Typing --> Cancelled : 取消测试
Entered --> Submit : 提交数据
Entered --> Typing : 继续编辑
Entered --> Cancelled : 取消测试
Submit --> Completed : 验证通过
Submit --> Entered : 数据验证失败
Submit --> DR : 需要文档审核
Submit --> Cancelled : 取消测试
DR --> Completed : 审核通过
DR --> Submit : 审核不通过
Completed --> [*]
Cancelled --> [*]
NC --> [*]
NA --> [*]
SubContracted --> Entered : 外包结果返回
SubContracted --> Cancelled : 外包取消
state "Typing" as Typing
state "Submitted" as Submit
state "Completed" as Completed
state "Subcontracted" as SubContracted
state "Entered" as Entered
state "Cancelled" as Cancelled
state "Not Test" as NC
state "Document Review" as DR
state "NA" as NA
```

**Diagram sources**
- [TestLineStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java#L1-L101)
- [TestLineStatusManager.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineStatusManager.java#L1-L51)

## 应用示例
本节提供测试线状态管理的实际应用示例。

### 状态检查示例
```java
// 检查测试线是否处于允许提交的状态
CustomResult result = TestLineStatusManager.checkTestLineStatus(
    ActionTestLineStatusMatrixEnum.DATA_ENTRY_SUBMIT, 
    testLineInstances
);

if (!result.isSuccess()) {
    throw new BizException("测试线状态不允许提交: " + result.getMsg());
}
```

### 状态信息传输对象
`TestLineStatusInfo`类用于在服务间传递状态更新信息：

```java
public class TestLineStatusInfo extends PrintFriendliness {
    private String testLineInstanceId;
    private int testLineType;
    private int testLineStatus;
    private int oldTestLineStatus;
    private Set<String> testMatrixIds;
    private String regionAccount;
    private Date updateTime;
    // getter和setter方法
}
```

### 响应对象
`TestLineStatusRsp`用于返回状态查询结果：

```java
public class TestLineStatusRsp {
    private String testLineInstanceId;
    private Integer testLineId;
    private Integer testLineType;
    private Integer testLineStatus;
    // getter和setter方法
}
```

**Section sources**
- [TestLineStatusInfo.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/info/testline/TestLineStatusInfo.java#L1-L67)
- [TestLineStatusRsp.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/rsp/testline/TestLineStatusRsp.java#L1-L51)

## 版本管理策略
测试线状态系统的版本管理遵循以下策略：

### 新增状态值
当需要新增状态值时，应遵循以下步骤：
1. 在`TestLineStatus`枚举中添加新的状态常量
2. 分配唯一的、不与现有状态冲突的状态码
3. 提供清晰的状态描述
4. 更新相关文档和状态转换图
5. 确保向后兼容性，避免影响现有业务流程

### 向后兼容性
系统设计时充分考虑了向后兼容性：
- 使用静态`maps`字段确保状态码到枚举实例的快速查找
- `findStatus`和`getMessage`方法对无效状态码返回`null`而非抛出异常
- 状态检查方法对空值和无效输入进行优雅处理

### 状态值弃用
当某个状态值不再使用时，不应直接删除，而应：
1. 在代码中添加`@Deprecated`注解
2. 在文档中明确标记为已弃用
3. 提供替代状态值的建议
4. 在后续版本中逐步移除相关逻辑

**Section sources**
- [TestLineStatus.java](file://otsnotes-facade-model/src/main/java/com/sgs/otsnotes/facade/model/enums/TestLineStatus.java#L1-L101)
- [TestLineStatusManager.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/TestLineStatusManager.java#L1-L51)