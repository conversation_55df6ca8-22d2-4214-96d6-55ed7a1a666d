# 部署与运维

<cite>
**本文档引用文件**   
- [部署视角.md](file://doc/部署视角.md)
- [系统架构.md](file://doc/系统架构.md)
- [DisConf.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/DisConf.java)
- [SysConstants.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/SysConstants.java)
- [RedisConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/RedisConfig.java)
- [KafkaProducerConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/KafkaProducerConfig.java)
- [HealthService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/HealthService.java)
- [ReportConsumer.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/kafka/ReportConsumer.java)
- [XXLJobConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/XXLJobConfig.java)
- [start_prod.sh](file://otsnotes-web/script/start_prod.sh)
- [start_uat.sh](file://otsnotes-web/script/start_uat.sh)
- [start_test.sh](file://otsnotes-web/script/start_test.sh)
- [start_dev.sh](file://otsnotes-web/script/start_dev.sh)
- [start_local.sh](file://otsnotes-web/script/start_local.sh)
- [start_stg.sh](file://otsnotes-web/script/start_stg.sh)
</cite>

## 目录
1. [部署与运维](#部署与运维)
2. [部署架构](#部署架构)
3. [环境管理](#环境管理)
4. [健康检查机制](#健康检查机制)
5. [日志管理策略](#日志管理策略)
6. [监控告警设置](#监控告警设置)
7. [部署检查清单](#部署检查清单)
8. [故障排除指南](#故障排除指南)
9. [备份与恢复策略](#备份与恢复策略)
10. [灾难恢复计划](#灾难恢复计划)

## 部署架构

根据系统架构文档，otsnotes-service 采用分层微服务架构，各模块职责明确，支持分布式部署和横向扩展。

```mermaid
graph TD
A[otsnotes-web] --> |REST| B[otsnotes-facade]
B --> |调用| C[otsnotes-domain]
C --> |数据访问| D[otsnotes-dbstorages]
C --> |基础能力| E[otsnotes-core]
C --> |集成| F[otsnotes-integration]
D --> |MyBatis| G[(DB)]
F --> |HTTP/Dubbo| H[外部系统]
```

**图示来源**
- [系统架构.md](file://doc/系统架构.md)

**本节来源**
- [系统架构.md](file://doc/系统架构.md)

## 环境管理

### 环境配置差异

系统通过Spring Profiles机制管理不同环境的配置，主要环境包括：开发(dev)、测试(test)、UAT(uat)、预生产(stg)和生产(prod)。

#### 核心配置文件

**DisConf.java** 包含了多个环境开关和功能配置：
- `fast.testLineIds`: 快速检测线ID列表
- `rtf.jobHostIp`: RTF作业主机IP
- `isEnableDBRule`: 是否启用数据库规则
- `tLWorkingInstructionApi.Enable`: TL工作指导API开关
- `trims.queryAnalyteFlag`: TRIMS分析物查询标志
- `trims.updateEventSync`: TRIMS更新事件同步开关
- `trims.oldSyncEvent`: TRIMS旧同步事件开关
- `switch.newStarlimsReceiveReport`: 新StarLims接收报告开关
- `switch.newUpdateBreakDown`: 新更新故障开关

**SysConstants.java** 定义了系统常量：
- 报告类型常量：`REPORT_TYPE_ORIGINAL`, `REPORT_TYPE_NULL`
- 系统标识常量：`BUID`, `SYSTEMID`, `GPO_SYSTEMID`
- 数据条目样式常量：`DATAENTRYSTYLE`, `DATATYPE`
- 字典键常量：`DICT_KEY_SEAL_CODE`, `DICT_KEY_REPORT_WITHOUT_DSS`

#### 环境部署配置

不同环境的JVM参数和监控配置存在显著差异：

**生产环境配置 (start_prod.sh)**
- 堆内存：4GB (-Xms4096m -Xmx4096m)
- 元空间：512MB (-XX:MaxMetaspaceSize=512m)
- 监控：Pinpoint APM
- 活动配置文件：production

**UAT环境配置 (start_uat.sh)**
- 堆内存：4GB (-Xms4096m -Xmx4096m)
- 元空间：512MB (-XX:MaxMetaspaceSize=512m)
- 监控：SkyWalking APM
- 活动配置文件：uat

**预生产环境配置 (start_stg.sh)**
- 堆内存：4GB (-Xms4096m -Xmx4096m)
- 元空间：512MB (-XX:MaxMetaspaceSize=512m)
- 监控：Pinpoint APM
- 活动配置文件：uat

**测试环境配置 (start_test.sh)**
- 堆内存：1GB (-Xms1024m -Xmx1024m)
- 元空间：512MB (-XX:MaxMetaspaceSize=512m)
- 监控：无
- 活动配置文件：test

**开发环境配置 (start_dev.sh)**
- 堆内存：1GB (-Xms1024m -Xmx1024m)
- 元空间：512MB (-XX:MaxMetaspaceSize=512m)
- 监控：SkyWalking APM
- 活动配置文件：dev

**本地环境配置 (start_local.sh)**
- 堆内存：1GB (-Xms1024m -Xmx1024m)
- 元空间：512MB (-XX:MaxMetaspaceSize=512m)
- 监控：无
- 活动配置文件：local

### 部署流程

1. **代码拉取与编译打包**
   - 使用Maven进行项目编译和打包
   - 生成可部署的JAR/WAR文件

2. **环境配置**
   - 配置数据库连接参数 (DB_URL)
   - 配置Redis连接参数 (REDIS_HOST)
   - 配置Dubbo注册中心 (DUBBO_REGISTRY)
   - 配置Kafka集群 (KAFKA_SERVERS，可选)
   - 设置JVM参数 (JAVA_OPTS)

3. **服务部署**
   - 将打包文件部署至目标服务器或容器
   - 配置启动脚本和环境变量

4. **服务启动**
   - 执行相应的启动脚本 (start_*.sh)
   - 检查服务进程是否正常启动

5. **负载均衡配置**
   - 配置Nginx等负载均衡器（如需要）
   - 设置健康检查端点

**本节来源**
- [部署视角.md](file://doc/部署视角.md)
- [DisConf.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/DisConf.java)
- [SysConstants.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/SysConstants.java)
- [start_prod.sh](file://otsnotes-web/script/start_prod.sh)
- [start_uat.sh](file://otsnotes-web/script/start_uat.sh)
- [start_test.sh](file://otsnotes-web/script/start_test.sh)
- [start_dev.sh](file://otsnotes-web/script/start_dev.sh)
- [start_local.sh](file://otsnotes-web/script/start_local.sh)
- [start_stg.sh](file://otsnotes-web/script/start_stg.sh)

## 健康检查机制

系统提供了完善的健康检查机制，通过`HealthService`类实现，确保服务的可用性。

### 健康检查实现

```java
@Service
public class HealthService {
    @Autowired
    OrderMapper orderMapper;
    @Autowired
    RedisHelper redisHelper;

    public CustomResult<HealthRsp> checkHealth(){
        CustomResult rspResult = new CustomResult();
        HealthRsp healthRsp = new HealthRsp();
        //redis系统当前时间
        final String redisKey = "SL:PREORDER:HEALTH";
        redisHelper.set(redisKey,DateUtils.format(DateUtils.getCurrentDate()),10);
        String currentDate = redisHelper.get(redisKey);
        healthRsp.setRedis(currentDate);

        //db中对应表的值
        String orderNo = orderMapper.getOrderInfoByCreatedDate();
        healthRsp.setDb(orderNo);
        rspResult.setSuccess(StringUtils.isNotBlank(orderNo) && StringUtils.isNotBlank(currentDate));
        rspResult.setData(healthRsp);

        return rspResult;
    }
}
```

### 健康检查流程

1. **Redis健康检查**
   - 写入一个带有TTL的测试键值对到Redis
   - 立即读取该键值，验证Redis读写能力
   - 测试键：`SL:PREORDER:HEALTH`
   - TTL：10秒

2. **数据库健康检查**
   - 调用`OrderMapper.getOrderInfoByCreatedDate()`方法
   - 查询订单表中最近创建的订单号
   - 验证数据库连接和查询能力

3. **综合健康状态**
   - 只有当Redis和数据库检查都成功时，服务才被认为是健康的
   - 返回包含Redis和数据库状态的详细响应

### 健康检查指标

- **Redis状态**: 显示从Redis读取的当前日期时间
- **数据库状态**: 显示从数据库查询到的订单号
- **整体状态**: 布尔值，表示服务是否健康

**本节来源**
- [HealthService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/HealthService.java)

## 日志管理策略

### 日志配置

系统使用Logback作为日志框架，通过`logback-spring.xml`文件进行配置（文件未找到，但可通过启动脚本推断配置）。

### 日志级别

- **生产环境**: ERROR级别，减少日志量
- **UAT/预生产环境**: WARN级别，平衡调试和性能
- **测试/开发环境**: DEBUG级别，便于问题排查

### 日志文件

1. **应用日志**
   - 路径：`/usr/local/applogs/new-otsnotes-service/`
   - 包含业务日志、错误日志和调试信息

2. **GC日志**
   - 路径：`/usr/local/applogs/new-otsnotes-service/gc.log`
   - 启用GC日志轮转
   - 最多保留10个文件，每个100MB
   - 记录详细的GC信息，包括时间戳和GC详情

3. **堆转储文件**
   - 路径：`/usr/local/applogs/new-otsnotes-service/heapdump.hprof`
   - 当发生OutOfMemoryError时自动生成
   - 用于内存泄漏分析

### 日志轮转

- GC日志启用轮转机制
- 保留最近10个日志文件
- 每个日志文件最大100MB
- 自动清理旧日志文件

**本节来源**
- [start_prod.sh](file://otsnotes-web/script/start_prod.sh)
- [start_uat.sh](file://otsnotes-web/script/start_uat.sh)
- [start_test.sh](file://otsnotes-web/script/start_test.sh)
- [start_dev.sh](file://otsnotes-web/script/start_dev.sh)
- [start_local.sh](file://otsnotes-web/script/start_local.sh)
- [start_stg.sh](file://otsnotes-web/script/start_stg.sh)

## 监控告警设置

### JVM监控

通过JMX远程监控JVM状态：
- JMX端口：8977
- JMX主机：当前节点IP
- 认证：禁用
- SSL：禁用

### APM监控

不同环境使用不同的APM工具：
- **生产环境**: Pinpoint
  - 应用名称：new-otsnotes-PROD
  - 代理ID：new-otsnotes-PROD{节点号}
- **UAT/开发环境**: SkyWalking
  - 应用名称：now-otsnotes-uat
  - 收集器地址：**************:11800
  - 启用SQL参数追踪
  - 启用Dubbo消费者和提供者参数收集

### Kafka消息监控

#### 生产者配置
- Kafka集群地址：通过`kafka.bootstrap-servers`配置
- 序列化器：StringSerializer
- 生产者工厂：DefaultKafkaProducerFactory

#### 消费者配置
以`ReportConsumer`为例：
- 消费主题：`KafkaTopicConsts.TOPIC_OTSNOTES_RECORD`
- 消费组ID：`com.sgs.ec.otsnotes.kafka.ReportConsumer`
- 消息处理：`processMessage`方法
- 异常处理：捕获异常并记录错误日志
- 手动确认：处理成功后调用`acknowledgment.acknowledge()`

### 定时任务监控

通过XXL-Job管理定时任务：
- 管理中心地址：`xxl.job.admin.addresses`
- 执行器名称：`xxl.job.executor.appname`
- 执行器IP：`xxl.job.executor.ip`
- 执行器端口：`xxl.job.executor.port`
- 访问令牌：`xxl.job.accessToken`
- 日志路径：`xxl.job.executor.logpath`
- 日志保留天数：`xxl.job.executor.logretentiondays`

### Redis监控

#### 连接配置
- 集群节点：`spring.redis.nodes`
- 超时时间：`spring.redis.timeout` (默认10000ms)
- 密码：`spring.redis.password`
- 最大重定向次数：`spring.redis.max-redirects`

#### 连接池配置
- 最小空闲连接：`spring.redis.pool.min-idle` (默认2)
- 最大空闲连接：`spring.redis.pool.max-idle` (默认10)
- 最大活跃连接：`spring.redis.pool.max-active` (默认200)
- 最大等待时间：`spring.redis.pool.max-wait` (默认10000ms)
- 借用时验证：`spring.redis.pool.testOnBorrow` (默认true)

**本节来源**
- [KafkaProducerConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/KafkaProducerConfig.java)
- [ReportConsumer.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/kafka/ReportConsumer.java)
- [XXLJobConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/XXLJobConfig.java)
- [RedisConfig.java](file://otsnotes-core/src/main/java/com/sgs/otsnotes/core/config/RedisConfig.java)
- [start_prod.sh](file://otsnotes-web/script/start_prod.sh)
- [start_uat.sh](file://otsnotes-web/script/start_uat.sh)
- [start_dev.sh](file://otsnotes-web/script/start_dev.sh)

## 部署检查清单

### 部署前检查

- [ ] 确认目标服务器资源满足要求（CPU、内存、磁盘）
- [ ] 确认JDK版本为1.8或更高
- [ ] 确认MySQL版本为5.7或更高
- [ ] 确认Redis版本为5或更高
- [ ] 确认Dubbo注册中心（如Zookeeper）正常运行
- [ ] 确认Kafka集群（如使用）正常运行
- [ ] 确认网络配置，确保各组件间网络连通
- [ ] 备份现有配置文件和数据

### 部署中检查

- [ ] 拉取最新代码并使用Maven编译打包
- [ ] 根据目标环境选择正确的配置文件
- [ ] 配置环境变量：
  - [ ] DB_URL
  - [ ] REDIS_HOST
  - [ ] DUBBO_REGISTRY
  - [ ] KAFKA_SERVERS（如使用）
  - [ ] JAVA_OPTS
- [ ] 将打包文件部署到目标服务器
- [ ] 配置启动脚本权限（chmod +x）
- [ ] 执行启动脚本

### 部署后检查

- [ ] 检查服务进程是否正常启动
- [ ] 检查应用日志是否有错误信息
- [ ] 检查GC日志是否正常生成
- [ ] 调用健康检查接口，确认服务健康
- [ ] 验证核心功能是否正常
- [ ] 确认监控系统能够采集到指标
- [ ] 记录部署版本和时间

**本节来源**
- [部署视角.md](file://doc/部署视角.md)
- [start_prod.sh](file://otsnotes-web/script/start_prod.sh)

## 故障排除指南

### 服务无法启动

**症状**: 服务启动脚本执行后，进程不存在

**排查步骤**:
1. 检查日志文件 `/usr/local/applogs/new-otsnotes-service/` 中的错误信息
2. 确认JVM参数是否正确，特别是内存设置
3. 检查依赖服务（数据库、Redis、Dubbo注册中心）是否可达
4. 确认端口8977未被占用（JMX端口）
5. 检查类路径配置，确认所有依赖库存在

### 健康检查失败

**症状**: 健康检查接口返回不健康状态

**排查步骤**:
1. 检查Redis连接：
   - 使用redis-cli连接Redis服务器
   - 手动执行SET和GET命令测试读写能力
2. 检查数据库连接：
   - 使用数据库客户端连接数据库
   - 执行`SELECT order_no FROM order_info ORDER BY created_date DESC LIMIT 1;`测试查询
3. 检查网络连通性：
   - 使用ping和telnet测试到Redis和数据库的连接
4. 检查防火墙设置，确保相关端口开放

### Kafka消息积压

**症状**: Kafka主题中消息数量持续增长，消费者处理速度慢

**排查步骤**:
1. 检查消费者日志，确认是否有异常
2. 监控消费者处理时间，确认是否存在性能瓶颈
3. 检查数据库性能，确认SQL执行效率
4. 考虑增加消费者实例或优化消息处理逻辑
5. 检查网络延迟，确认Kafka集群性能

### 内存溢出

**症状**: 服务崩溃，生成heapdump.hprof文件

**排查步骤**:
1. 分析heapdump.hprof文件，使用MAT等工具定位内存泄漏
2. 检查大对象创建，特别是缓存使用
3. 调整JVM参数，增加堆内存或优化GC策略
4. 检查是否有未关闭的资源（如数据库连接、文件句柄）
5. 监控内存使用趋势，设置合理的告警阈值

### 性能下降

**症状**: 接口响应时间变长，系统负载升高

**排查步骤**:
1. 检查GC日志，确认是否有频繁的Full GC
2. 使用APM工具（Pinpoint/SkyWalking）分析调用链路，定位性能瓶颈
3. 监控数据库性能，优化慢查询
4. 检查缓存命中率，优化缓存策略
5. 分析线程dump，确认是否有线程阻塞

**本节来源**
- [HealthService.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/service/HealthService.java)
- [ReportConsumer.java](file://otsnotes-domain/src/main/java/com/sgs/otsnotes/domain/kafka/ReportConsumer.java)
- [start_prod.sh](file://otsnotes-web/script/start_prod.sh)

## 备份与恢复策略

### 数据库备份

- **备份频率**: 每日全量备份，每小时增量备份
- **备份保留**: 保留最近7天的备份
- **备份方式**: 使用mysqldump或类似工具
- **备份存储**: 异地存储，确保数据安全
- **备份验证**: 定期恢复测试，确保备份可用

### 配置文件备份

- **备份内容**: 所有环境的配置文件
- **备份频率**: 每次变更后立即备份
- **备份存储**: 版本控制系统（如Git）和独立存储
- **恢复流程**: 从版本控制系统拉取历史版本或从备份存储恢复

### 日志文件备份

- **重要日志**: 保留关键时期的日志文件
- **备份策略**: 根据磁盘空间和合规要求确定保留策略
- **归档**: 将旧日志文件压缩归档

### 恢复流程

1. **数据库恢复**
   - 停止应用服务
   - 使用备份文件恢复数据库
   - 验证数据完整性
   - 启动应用服务

2. **配置恢复**
   - 从备份中获取正确的配置文件
   - 替换当前配置文件
   - 重启服务使配置生效

3. **全量恢复**
   - 准备干净的服务器环境
   - 恢复数据库备份
   - 部署应用并配置
   - 验证系统功能

**本节来源**
- [部署视角.md](file://doc/部署视角.md)

## 灾难恢复计划

### 灾难场景

1. **服务器故障**: 物理服务器或虚拟机故障
2. **数据中心故障**: 整个数据中心不可用
3. **数据损坏**: 数据库或文件系统损坏
4. **网络中断**: 与外部系统的网络连接中断

### 恢复策略

#### 服务器故障
- **RTO (恢复时间目标)**: 30分钟
- **RPO (恢复点目标)**: 1小时
- **恢复步骤**:
  1. 在备用服务器上部署应用
  2. 从最近的备份恢复数据库
  3. 切换DNS或负载均衡指向新服务器
  4. 验证服务功能

#### 数据中心故障
- **RTO**: 2小时
- **RPO**: 1小时
- **恢复步骤**:
  1. 在备用数据中心启动服务
  2. 恢复数据库和配置
  3. 更新DNS记录指向新数据中心
  4. 通知相关方服务已恢复

#### 数据损坏
- **RTO**: 1小时
- **RPO**: 1小时
- **恢复步骤**:
  1. 停止写入操作
  2. 从备份中恢复数据
  3. 验证数据完整性
  4. 恢复服务

### 高可用架构

- **多实例部署**: 关键服务部署多个实例
- **负载均衡**: 使用Nginx等负载均衡器分发流量
- **数据复制**: 数据库主从复制，Redis集群
- **异地备份**: 备份数据存储在不同地理位置

### 演练计划

- **季度演练**: 每季度进行一次完整的灾难恢复演练
- **演练内容**: 模拟服务器故障、数据中心故障等场景
- **评估改进**: 记录演练过程，评估RTO和RPO，持续改进恢复流程

**本节来源**
- [部署视角.md](file://doc/部署视角.md)