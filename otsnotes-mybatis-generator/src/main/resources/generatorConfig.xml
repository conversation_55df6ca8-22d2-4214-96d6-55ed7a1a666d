<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
  PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
  "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="SqlServerTables" targetRuntime="MyBatis3" defaultModelType="flat">
        <!-- 自动识别数据库关键字，默认false，如果设置为true，根据SqlReservedWords中定义的关键字列表；
        一般保留默认值，遇到数据库关键字（Java关键字），使用columnOverride覆盖-->
        <property name="autoDelimitKeywords" value="true"/>
        <!--beginningDelimiter、endingDelimiter默认为（"），但Mysql中不能这么写，所以要将这两个默认值改为“单反引号（'）”-->
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
        <plugin type="com.sgs.otsnotes.mybatis.generator.RenameModelPlugin">
            <property name="prefixes2Remove" value="tb,sgs"/>
            <property name="suffix2Append" value=""/>
        </plugin>
        <!--自定义批量插入-->
        <plugin type="com.sgs.otsnotes.mybatis.generator.BatchInsertPlugin"></plugin>
        <!--自定义批量更新-->
        <plugin type="com.sgs.otsnotes.mybatis.generator.BatchUpdatePlugin"></plugin>

        <plugin type="com.sgs.otsnotes.mybatis.generator.ForceCreateUpdateTimePlugin">
            <property name="insertTimeColumns" value="create_time"/>
            <property name="lastUpdateTimeColumns" value="modify_time"/>
            <property name="dbCurrentTimeExpr" value="now()"/>
        </plugin>


        <commentGenerator type="com.sgs.otsnotes.mybatis.generator.DBCommentGenerator">
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
          connectionURL="*************************************************************************************************************************************************************************"
          userId="otsnotes_read"
          password="*6&amp;lv!6%E4KFXB2yp4^m">
            <property name="remarksReporting" value="true"/>
        </jdbcConnection>

        <javaTypeResolver type="com.sgs.otsnotes.mybatis.generator.MyJavaTypeResolver">
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.sgs.soda.otsnotes.infrastructure.database.testsample.dataobject.auto"
          targetProject="uni-otsnotes/uni-otsnotes-infrastructure/src/main/java">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="sqlmap.autogenerated" targetProject="uni-otsnotes/uni-otsnotes-infrastructure/src/main/resources">
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sgs.soda.otsnotes.infrastructure.database.testsample.mapper"
          targetProject="uni-otsnotes/uni-otsnotes-infrastructure/src/main/java">
        </javaClientGenerator>

        <table tableName="tb_test_sample" domainObjectName="TestSampleDO">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_test_sample_ext" domainObjectName="TestSampleFieldDO">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_test_sample_group" domainObjectName="TestSampleGroupDO">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_test_sample_language" domainObjectName="TestSampleMaterialLanguageDO">
            <property name="useActualColumnNames" value="true"/>
        </table>
    </context>

</generatorConfiguration>
