package com.sgs.otsnotes.facade.model.trims.info;

import com.alibaba.fastjson.annotation.JSONField;
import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/1 16:44
 */
public class TestAnalyteLimitSyncInfo extends PrintFriendliness {

    private Integer versionIndentifier;
    private Integer talId;
    private Integer testAnalyteId;
    private Integer testLimitGroupId;
    private String talValue1;
    private String talValue2;
    private Integer versionNo;

    private String analyteLimitDataType;
    private String analyteLimitType;
    private String resolveTestAnalyte;
    private String talReportDescription;
    private String status;

    @JSONField(name = "isNeedFormula")
    private Integer formulaType;

    private TestLimitOpreatorInfo testLimitOpreator;
    private List<Integer> customerAccountIds;
    private List<TestLimitMutipleUnitsInfo> mutipleUnits;
    private List<TestLimitApplicabilityInfo> applicabilities;
    private List<TestLimitLanguageInfo> otherLanguageItems;

    /**
     * {
     *     "status": "OK",
     *     "code": "200",
     *     "message": "Success!",
     *     "data": [{
     *         "versionIndentifier": "11029",
     *         "talId":"345",
     *         "testAnalyteId": "16423",
     *         "testLimitGroupId": "2915",
     *         "talValue1": "4",
     *         "talValue2": "",
     *         "resolveTestAnalyte":"Change In Shade [ >= 4]",
     *         "versionNo":"1",
     *         "talReportDescription": "Min.4",
     *         "testLimitOpreator": {
     *             "testLimitOperatorDepiction": ">=",
     *             "testLimitOperandCount": "1",
     *             "testLimitOperatorName": ">=",
     *             "testLimitOperatorId": "3"
     *         },
     *         "mutipleUnits": [{
     *             "sequence": 1,
     *             "testingUnitId": 60,
     *             "reportingUnitId": 60,
     *             "standardVersionidentifiers": [60]
     *         }],
     *         "applicabilities":[{
     *             "applicablityId":90,
     *             "productAttributeIds":[1,2,3],
     *             "testVersionIdentifiers":[1,2,3],
     *             "standardVersionidentifiers":[1,2,3],
     *             "regulationVersionIdentifiers":[1,2,3],
     *             "conditionIds":[1,2,3]
     *         }],
     *         "otherLanguageItems": [{
     *             "languageId": 2,
     *             "reportDescription": "阴影变化"
     *         }]
     *     }]
     * }
     */
    public Integer getVersionIndentifier() {
        return versionIndentifier;
    }

    public void setVersionIndentifier(Integer versionIndentifier) {
        this.versionIndentifier = versionIndentifier;
    }

    public Integer getTalId() {
        return talId;
    }

    public void setTalId(Integer talId) {
        this.talId = talId;
    }

    public Integer getTestAnalyteId() {
        return testAnalyteId;
    }

    public void setTestAnalyteId(Integer testAnalyteId) {
        this.testAnalyteId = testAnalyteId;
    }

    public Integer getTestLimitGroupId() {
        return testLimitGroupId;
    }

    public void setTestLimitGroupId(Integer testLimitGroupId) {
        this.testLimitGroupId = testLimitGroupId;
    }

    public String getTalValue1() {
        return talValue1;
    }

    public void setTalValue1(String talValue1) {
        this.talValue1 = talValue1;
    }

    public String getTalValue2() {
        return talValue2;
    }

    public void setTalValue2(String talValue2) {
        this.talValue2 = talValue2;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getAnalyteLimitDataType() {
        return analyteLimitDataType;
    }

    public void setAnalyteLimitDataType(String analyteLimitDataType) {
        this.analyteLimitDataType = analyteLimitDataType;
    }

    public String getAnalyteLimitType() {
        return analyteLimitType;
    }

    public void setAnalyteLimitType(String analyteLimitType) {
        this.analyteLimitType = analyteLimitType;
    }

    public String getResolveTestAnalyte() {
        return resolveTestAnalyte;
    }

    public void setResolveTestAnalyte(String resolveTestAnalyte) {
        this.resolveTestAnalyte = resolveTestAnalyte;
    }

    public String getTalReportDescription() {
        return talReportDescription;
    }

    public void setTalReportDescription(String talReportDescription) {
        this.talReportDescription = talReportDescription;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getFormulaType() {
        return formulaType;
    }

    public void setFormulaType(Integer formulaType) {
        this.formulaType = formulaType;
    }

    public TestLimitOpreatorInfo getTestLimitOpreator() {
        return testLimitOpreator;
    }

    public void setTestLimitOpreator(TestLimitOpreatorInfo testLimitOpreator) {
        this.testLimitOpreator = testLimitOpreator;
    }

    public List<Integer> getCustomerAccountIds() {
        return customerAccountIds;
    }

    public void setCustomerAccountIds(List<Integer> customerAccountIds) {
        this.customerAccountIds = customerAccountIds;
    }

    public List<TestLimitMutipleUnitsInfo> getMutipleUnits() {
        return mutipleUnits;
    }

    public void setMutipleUnits(List<TestLimitMutipleUnitsInfo> mutipleUnits) {
        this.mutipleUnits = mutipleUnits;
    }

    public List<TestLimitApplicabilityInfo> getApplicabilities() {
        return applicabilities;
    }

    public void setApplicabilities(List<TestLimitApplicabilityInfo> applicabilities) {
        this.applicabilities = applicabilities;
    }

    public List<TestLimitLanguageInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<TestLimitLanguageInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }
}
