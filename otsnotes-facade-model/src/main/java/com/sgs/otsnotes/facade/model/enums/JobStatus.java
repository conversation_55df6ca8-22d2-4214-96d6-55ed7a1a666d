package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum JobStatus {
    New(1101, "New"),
    Testing(1102, "Testing"),
    Closed(1103, "Closed"),
    Cancelled(1104, "Cancelled"),
    Validated(1105,"Validated");

    @DictCodeField
    private Integer status;
    @DictLabelField
    private String message;

    JobStatus(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, JobStatus> maps = new HashMap<Integer, JobStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (JobStatus enu : JobStatus.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static JobStatus getJobStatus(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }

    /**
     *
     * @param status
     * @param orderStatus
     * @return
     */
    public static boolean check(Integer status, JobStatus orderStatus) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        return maps.get(status.intValue()) == orderStatus;
    }
}