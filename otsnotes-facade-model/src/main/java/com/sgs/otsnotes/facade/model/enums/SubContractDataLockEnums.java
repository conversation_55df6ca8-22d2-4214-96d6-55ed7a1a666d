package com.sgs.otsnotes.facade.model.enums;

public enum SubContractDataLockEnums {
    lock(1),
    unlock(0);
    Integer code;

    SubContractDataLockEnums(Integer code){
        this.code = code;

    }

    public Integer getCode() {
        return code;
    }

    public static boolean check(Integer code,SubContractDataLockEnums ... enums){
        if(code==null){
            return false;
        }
        for (SubContractDataLockEnums anEnum : enums) {
            if(anEnum.getCode().compareTo(code)==0){
                return true;
            }
        }
        return false;
    }
}
