package com.sgs.otsnotes.facade.model.kafka;

import java.io.Serializable;


public class UpdatePreOrderStatusMessage implements Serializable{

	private static final long serialVersionUID = 361058472897233574L;

	private String orderNo;

	private String sgsToken;

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getSgsToken() {
		return sgsToken;
	}

	public void setSgsToken(String sgsToken) {
		this.sgsToken = sgsToken;
	}
}
