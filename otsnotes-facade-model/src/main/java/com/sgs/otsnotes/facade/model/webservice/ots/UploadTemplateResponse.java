/**
 * UploadTemplateResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class UploadTemplateResponse  implements java.io.Serializable {
    private CommandResult uploadTemplateResult;

    public UploadTemplateResponse() {
    }

    public UploadTemplateResponse(
           CommandResult uploadTemplateResult) {
           this.uploadTemplateResult = uploadTemplateResult;
    }


    /**
     * Gets the uploadTemplateResult value for this UploadTemplateResponse.
     * 
     * @return uploadTemplateResult
     */
    public CommandResult getUploadTemplateResult() {
        return uploadTemplateResult;
    }


    /**
     * Sets the uploadTemplateResult value for this UploadTemplateResponse.
     * 
     * @param uploadTemplateResult
     */
    public void setUploadTemplateResult(CommandResult uploadTemplateResult) {
        this.uploadTemplateResult = uploadTemplateResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof UploadTemplateResponse)) {
            return false;
        }
        UploadTemplateResponse other = (UploadTemplateResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.uploadTemplateResult==null && other.getUploadTemplateResult()==null) || 
             (this.uploadTemplateResult!=null &&
              this.uploadTemplateResult.equals(other.getUploadTemplateResult())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getUploadTemplateResult() != null) {
            _hashCode += getUploadTemplateResult().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(UploadTemplateResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">UploadTemplateResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("uploadTemplateResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "UploadTemplateResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "CommandResult"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
