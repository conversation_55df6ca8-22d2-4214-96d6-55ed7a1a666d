package com.sgs.otsnotes.facade.model.localize;

/**
 * 可本地化Condition Type
 * <AUTHOR>
 * @date 2020/12/30 16:44
 */
public interface ConditionTypeLocalizable {
    /**
     * 主键
     * @return ConditionTypeBaseId
     */
    Integer getConditionTypeBaseId();

    /**
     * ConditionTypeName
     * @param testConditionTypeName 名称
     */
    void setTestConditionTypeName(String testConditionTypeName);

    /**
     * 获取ConditionTypeName名称
     * @return 返回名称
     */
    String getTestConditionTypeName();
}
