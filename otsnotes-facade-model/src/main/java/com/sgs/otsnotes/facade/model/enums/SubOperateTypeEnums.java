package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum SubOperateTypeEnums {
    None(0, "None"),
    CanOperateMainContract(1, "订单为 内部分包单主单 可以操作"),
    CanOperateMainNewContract(2, "订单为 新版内部分包单主单 可以操作"),
    CanOperateSubContract(4, "订单为 内部分包单子单 可以操作"),
    CanOperateNewSubContract(8, "订单为 新版内部分包单子单 可以操作"),
    ;

    private int type;
    private String message;

    SubOperateTypeEnums(int type, String message) {
        this.type = type;
        this.message = message;
    }

    public int getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }


    public static final Map<Integer, SubOperateTypeEnums> maps = new HashMap<Integer, SubOperateTypeEnums>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (SubOperateTypeEnums type : SubOperateTypeEnums.values()) {
                put(type.getType(), type);
            }
        }
    };

    public static SubOperateTypeEnums findType(Integer type) {
        if (type == null || type.intValue() <= 0) {
            return SubOperateTypeEnums.None;
        }
        for (SubOperateTypeEnums subContractType : SubOperateTypeEnums.values()) {
            if ((subContractType.getType() & type.intValue()) > 0) {
                return subContractType;
            }
        }
        return null;
    }

    /**
     * 位运算 计算是否可以操作
     *
     * @param type
     * @param subOperateTypeEnums
     * @return
     */
    public static boolean checkOperate(Integer type, SubOperateTypeEnums subOperateTypeEnums) {
        if (type == null || subOperateTypeEnums == null) {
            return false;
        }
        return (type & subOperateTypeEnums.getType()) > 0;
    }

    /**
     * @param type
     * @param subContractType
     * @return
     */
    public static boolean check(Integer type, SubOperateTypeEnums subContractType) {
        if (subContractType == null || !maps.containsKey(type)) {
            return false;
        }
        return maps.get(type) == subContractType;
    }
}



