package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.MarkerMasterDataSyncInfo;
import com.sgs.otsnotes.facade.model.trims.info.PPTLLabSectionSyncInfo;

import java.util.List;

public class PPTLLabSectionSyncRsp extends TrimsSyncBaseRsp {
    /**
     *
     */
    private List<PPTLLabSectionSyncInfo> data;

    public List<PPTLLabSectionSyncInfo> getData() {
        return data;
    }

    public void setData(List<PPTLLabSectionSyncInfo> data) {
        this.data = data;
    }
}
