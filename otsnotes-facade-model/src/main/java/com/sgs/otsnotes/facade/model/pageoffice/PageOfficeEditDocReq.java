package com.sgs.otsnotes.facade.model.pageoffice;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sgs.framework.core.base.BaseRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class PageOfficeEditDocReq extends BaseRequest {
    /**
     *
     */
    @JsonIgnore
    private HttpServletRequest request;
    /**
     *
     */
    @JsonIgnore
    private HttpServletResponse response;
    /**
     *
     */
    private String testLineInstanceId;

    private Long attBaseId;

    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getAttBaseId() {
        return attBaseId;
    }

    public void setAttBaseId(Long attBaseId) {
        this.attBaseId = attBaseId;
    }

    public HttpServletRequest getRequest() {
        return request;
    }

    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    public HttpServletResponse getResponse() {
        return response;
    }

    public void setResponse(HttpServletResponse response) {
        this.response = response;
    }

    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId;
    }
}
