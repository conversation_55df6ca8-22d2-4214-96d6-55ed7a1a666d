package com.sgs.otsnotes.facade.model.kafka;

public class KafkaTopicConsts {
    public static final String TOPIC_OTSNOTES_RECORD = "com.sgs.tracking.domain.service.ProcessRecordServiceImpl";

    public static final String TOPIC_OTSNOTES_SYNCSTATUS = "com.sgs.ec.otsnotes.service.ReportServiceImpl";

    public static final String TOPIC_TRACKING_RECORD = "com.sgs.tracking.domain.kafka.Record";

    /**
     * 转单topic
     */
    public static final String TOPIC_ORDER_CROSS= "com.sgs.preorder.service.OrderCrossLabServiceImpl";


    /**
     * 物理删除数据
     */
    public static final String TOPIC_PHYCAL_DELETE= "com.sgs.central.domain.service.PhycalDeleteServiceImpl";

    /**
     * to DM topic
     */
    public static final String SL_DM_TOPIC = "com.sgs.ec.slpreorder.service.dm.DMServiceImpl";

    /**
     * 测试专用！！！
     */
    public static final String TOPIC_TEST = "test";

    /**
     * 内部分包创建更新修改原单相关信息
     */
    public static final String TOPIC_UPDATE_SUBCONTRACT = "com.sgs.otsnotes.kafka.updateSubContractInfo";
    /**
     * 分包报告回传逻辑处理
     */
    public static final String TOPIC_UPDATE_BACK_SUBCONTRACT_INFO = "com.sgs.otsnotes.kafka.updateBackSubInfo";


    /**
     * GPO TODO_LIST TOPIC
     */
//    public static final String TOPIC_TODO = "com.sgs.gpo.kafka.todo";
    public static final String TOPIC_TODO = "com.sgs.preorder.kafka.todolist";
}
