package com.sgs.otsnotes.facade.model.enums;

/**
 * <AUTHOR>
 */

public enum DigitalReportReportTypeEnums {
    Front(1),
    Subcontract(1617),
    ;
    private Integer reportType;
    DigitalReportReportTypeEnums(Integer reportType){
        this.reportType = reportType;
    }

    public Integer getReportType() {
        return reportType;
    }

    public static boolean check(Integer reportType,DigitalReportReportTypeEnums ...enums){
        if(reportType == null || enums == null || enums.length == 0){
            return false;
        }
        for (DigitalReportReportTypeEnums anEnum : enums) {
            if(anEnum.reportType.compareTo(reportType)==0){
                return true;
            }
        }
        return false;
    }
}
