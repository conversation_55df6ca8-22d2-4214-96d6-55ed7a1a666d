package com.sgs.otsnotes.facade.model.localize;

/**
 * Condition Instance多语言
 * <AUTHOR>
 * @date 2020/12/30 18:51
 */
public interface ConditionInstanceLocalizable {

    /**
     * 获取TestConditionInstanceId
     * @return TestConditionInstanceId
     */
    String getTestConditionInstanceId();

    /**
     * 设置condition name
     * @param testConditionName condition name
     */
    void setTestConditionName(String testConditionName);

    /**
     * 设置condition type name
     * @param testConditionTypeName condition type name
     */
    void setTestConditionTypeName(String testConditionTypeName);
}
