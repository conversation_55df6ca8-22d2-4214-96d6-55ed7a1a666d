package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;
import com.sgs.otsnotes.facade.model.trims.rsp.CustomerTestCategorySyncInfo;

import java.util.List;

/**
 * @Author: Joke.wang
 * @Date: 2022/2/22 13:26
 */
public class WorkInstructionSyncInfo extends PrintFriendliness {
    private static final long serialVersionUID = -6970635948247465195L;

    private Integer workingInstructionId;

    private Integer productLineId;

    private String workingInstructionName;

    private String workingInstructionShortDesc;

    private String workingInstructionText;

    private Integer workingInstructionCategoryId;

    private String workingInstructionCategoryName;

    private Integer customerAccountId;

    private List<Integer> testStandardVersionIdentifiers;

    private List<WorkInstructionLanguageSyncInfo> otherLanguageItems;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getWorkingInstructionId() {
        return workingInstructionId;
    }

    public void setWorkingInstructionId(Integer workingInstructionId) {
        this.workingInstructionId = workingInstructionId;
    }

    public Integer getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }

    public String getWorkingInstructionName() {
        return workingInstructionName;
    }

    public void setWorkingInstructionName(String workingInstructionName) {
        this.workingInstructionName = workingInstructionName;
    }

    public String getWorkingInstructionShortDesc() {
        return workingInstructionShortDesc;
    }

    public void setWorkingInstructionShortDesc(String workingInstructionShortDesc) {
        this.workingInstructionShortDesc = workingInstructionShortDesc;
    }

    public String getWorkingInstructionText() {
        return workingInstructionText;
    }

    public void setWorkingInstructionText(String workingInstructionText) {
        this.workingInstructionText = workingInstructionText;
    }

    public Integer getWorkingInstructionCategoryId() {
        return workingInstructionCategoryId;
    }

    public void setWorkingInstructionCategoryId(Integer workingInstructionCategoryId) {
        this.workingInstructionCategoryId = workingInstructionCategoryId;
    }

    public String getWorkingInstructionCategoryName() {
        return workingInstructionCategoryName;
    }

    public void setWorkingInstructionCategoryName(String workingInstructionCategoryName) {
        this.workingInstructionCategoryName = workingInstructionCategoryName;
    }

    public List<Integer> getTestStandardVersionIdentifiers() {
        return testStandardVersionIdentifiers;
    }

    public void setTestStandardVersionIdentifiers(List<Integer> testStandardVersionIdentifiers) {
        this.testStandardVersionIdentifiers = testStandardVersionIdentifiers;
    }

    public List<WorkInstructionLanguageSyncInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<WorkInstructionLanguageSyncInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }

    public Integer getCustomerAccountId() {
        return customerAccountId;
    }

    public void setCustomerAccountId(Integer customerAccountId) {
        this.customerAccountId = customerAccountId;
    }
}
