package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/3 10:42
 */
public class TestLimitLanguageInfo extends PrintFriendliness {

    private Integer languageId;
    private String reportDescription;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getReportDescription() {
        return reportDescription;
    }

    public void setReportDescription(String reportDescription) {
        this.reportDescription = reportDescription;
    }
}
