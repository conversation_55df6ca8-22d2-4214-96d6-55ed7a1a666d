package com.sgs.otsnotes.facade.model.common;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class BaseAopTestLineIdRequest extends OtsNotesRequest {
    /**
     *
     */
    @ApiModelProperty(required = true,notes = "testline主键")
    private String testLineInstanceId;

    private List<String> testLineInstanceIds;


    public List<String> getTestLineInstanceIds() {
        return testLineInstanceIds;
    }

    public void setTestLineInstanceIds(List<String> testLineInstanceIds) {
        this.testLineInstanceIds = testLineInstanceIds;
    }

    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId;
    }
}
