package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class TestStandardLanguageSyncRsp extends PrintFriendliness {
    /**
     *
     */
    private Integer versionIdentifier;
    /**
     *
     */
    private String evaluationAlias;
    /**
     *
     */
    private String methodAlias;
    /**
     *
     */
    private String ppNotesAlias;

    public Integer getVersionIdentifier() {
        return versionIdentifier;
    }

    public void setVersionIdentifier(Integer versionIdentifier) {
        this.versionIdentifier = versionIdentifier;
    }

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }

    public String getMethodAlias() {
        return methodAlias;
    }

    public void setMethodAlias(String methodAlias) {
        this.methodAlias = methodAlias;
    }

    public String getPpNotesAlias() {
        return ppNotesAlias;
    }

    public void setPpNotesAlias(String ppNotesAlias) {
        this.ppNotesAlias = ppNotesAlias;
    }
}
