package com.sgs.otsnotes.facade.model.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.annotation.Documented;

/**
 * 用于标记需要进行HTML转换的字段或类
 * 被此注解标记的String类型字段将会进行HTML转义处理
 * 如果标记在类上，则该类的所有String类型字段都会进行处理
 */
@Documented
@Target({ElementType.FIELD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface TransHtmlField {
    /**
     * 是否启用HTML转换，默认为true
     */
    boolean enabled() default true;
} 