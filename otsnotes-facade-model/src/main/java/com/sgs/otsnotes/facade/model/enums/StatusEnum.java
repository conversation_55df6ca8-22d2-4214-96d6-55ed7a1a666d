package com.sgs.otsnotes.facade.model.enums;

public enum StatusEnum {
    INVALID(0,"Inactive", "禁用"),
    VALID(1, "Active","启用");

    private int code;

    private String name;
    private String desc;

    StatusEnum(int code,String name, String desc) {
        this.code = code;
        this.desc = desc;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getName() {
        return name;
    }

    public static boolean checkName(String name,StatusEnum...enums){
        if(name == null || name.length()==0){
            return false;
        }
        for (StatusEnum value : enums) {
            if(value.name.equalsIgnoreCase(name)){
                return true;
            }
        }
        return false;
    }

}
