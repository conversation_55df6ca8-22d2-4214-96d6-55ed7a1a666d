package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

public enum UnitStatus {
    Inactive(0, "Inactive"),
    Active(1, "Active");
    @DictCodeField
    private final int status;
    @DictLabelField
    private final String code;

    UnitStatus(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return this.code;
    }

    public static final Map<String, UnitStatus> maps = new HashMap<String, UnitStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (UnitStatus enu : UnitStatus.values()) {
                put(enu.getCode().toLowerCase(), enu);
            }
        }
    };

    public static UnitStatus getCode(String code) {
        if (code == null || !maps.containsKey(code.toLowerCase())) {
            return null;
        }
        return maps.get(code.toLowerCase());
    }
}
