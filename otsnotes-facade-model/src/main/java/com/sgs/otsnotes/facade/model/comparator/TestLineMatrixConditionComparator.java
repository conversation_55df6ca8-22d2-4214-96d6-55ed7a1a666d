package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.dto.TestLineMatrixConditionDTO;

import java.util.Comparator;

public class TestLineMatrixConditionComparator implements Comparator<TestLineMatrixConditionDTO> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public TestLineMatrixConditionComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param c1
     * @param c2
     * @return
     */
    @Override
    public int compare(TestLineMatrixConditionDTO c1, TestLineMatrixConditionDTO c2) {
        //ORDER BY ct.TestConditionTypeID,c.TestConditionID;
        Integer testConditionTypeSeq1 = c1.getTestConditionTypeSeq();
        if (testConditionTypeSeq1 == null){
            testConditionTypeSeq1 = 0;
        }
        Integer testConditionTypeSeq2 = c2.getTestConditionTypeSeq();
        if (testConditionTypeSeq2 == null){
            testConditionTypeSeq2 = 0;
        }
        int index = Integer.compare(testConditionTypeSeq1, testConditionTypeSeq2);
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        Integer testConditionSeq1 = c1.getTestConditionSeq();
        if (testConditionSeq1 == null){
            testConditionSeq1 = 0;
        }
        Integer testConditionSeq2 = c2.getTestConditionSeq();
        if (testConditionSeq2 == null){
            testConditionSeq2 = 0;
        }
        index = Integer.compare(testConditionSeq1, testConditionSeq2);
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
