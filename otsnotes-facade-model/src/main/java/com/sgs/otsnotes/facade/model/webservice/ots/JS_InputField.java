/**
 * JS_InputField.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_InputField  extends Element implements java.io.Serializable {
    private String resultElementId;

    private ListBoxField listBoxProperties;

    private FieldTypeEnum fieldType;

    private String format;

    private String defaultValue;

    private String tooltip;

    private String remark;

    private String[] usedBy;

    private DataTypeEnum dataType;

    private TemplateDefineFieldFormulaType formulaType;

    private boolean isReadOnly;

    private boolean isSync;

    private int variableId;

    private String variableName;

    private FixedVariableNameEnum fixedVariableName;

    private ReportConclusionScopeEnum conclusionScope;

    private ConclusionValueEnum conclusionCellType;

    private ConclusionMarkFlagEnum conclusionMarkFlag;

    private FieldBindInfo bindInfo;

    private FactDatasourceTypeEnum factType;

    private Dim[] dims;

    private DataRowField dataRowFieldProperties;

    public JS_InputField() {
    }

    public JS_InputField(
           String id,
           String name,
           String resultElementId,
           ListBoxField listBoxProperties,
           FieldTypeEnum fieldType,
           String format,
           String defaultValue,
           String tooltip,
           String remark,
           String[] usedBy,
           DataTypeEnum dataType,
           TemplateDefineFieldFormulaType formulaType,
           boolean isReadOnly,
           boolean isSync,
           int variableId,
           String variableName,
           FixedVariableNameEnum fixedVariableName,
           ReportConclusionScopeEnum conclusionScope,
           ConclusionValueEnum conclusionCellType,
           ConclusionMarkFlagEnum conclusionMarkFlag,
           FieldBindInfo bindInfo,
           FactDatasourceTypeEnum factType,
           Dim[] dims,
           DataRowField dataRowFieldProperties) {
        super(
            id,
            name);
        this.resultElementId = resultElementId;
        this.listBoxProperties = listBoxProperties;
        this.fieldType = fieldType;
        this.format = format;
        this.defaultValue = defaultValue;
        this.tooltip = tooltip;
        this.remark = remark;
        this.usedBy = usedBy;
        this.dataType = dataType;
        this.formulaType = formulaType;
        this.isReadOnly = isReadOnly;
        this.isSync = isSync;
        this.variableId = variableId;
        this.variableName = variableName;
        this.fixedVariableName = fixedVariableName;
        this.conclusionScope = conclusionScope;
        this.conclusionCellType = conclusionCellType;
        this.conclusionMarkFlag = conclusionMarkFlag;
        this.bindInfo = bindInfo;
        this.factType = factType;
        this.dims = dims;
        this.dataRowFieldProperties = dataRowFieldProperties;
    }


    /**
     * Gets the resultElementId value for this JS_InputField.
     * 
     * @return resultElementId
     */
    public String getResultElementId() {
        return resultElementId;
    }


    /**
     * Sets the resultElementId value for this JS_InputField.
     * 
     * @param resultElementId
     */
    public void setResultElementId(String resultElementId) {
        this.resultElementId = resultElementId;
    }


    /**
     * Gets the listBoxProperties value for this JS_InputField.
     * 
     * @return listBoxProperties
     */
    public ListBoxField getListBoxProperties() {
        return listBoxProperties;
    }


    /**
     * Sets the listBoxProperties value for this JS_InputField.
     * 
     * @param listBoxProperties
     */
    public void setListBoxProperties(ListBoxField listBoxProperties) {
        this.listBoxProperties = listBoxProperties;
    }


    /**
     * Gets the fieldType value for this JS_InputField.
     * 
     * @return fieldType
     */
    public FieldTypeEnum getFieldType() {
        return fieldType;
    }


    /**
     * Sets the fieldType value for this JS_InputField.
     * 
     * @param fieldType
     */
    public void setFieldType(FieldTypeEnum fieldType) {
        this.fieldType = fieldType;
    }


    /**
     * Gets the format value for this JS_InputField.
     * 
     * @return format
     */
    public String getFormat() {
        return format;
    }


    /**
     * Sets the format value for this JS_InputField.
     * 
     * @param format
     */
    public void setFormat(String format) {
        this.format = format;
    }


    /**
     * Gets the defaultValue value for this JS_InputField.
     * 
     * @return defaultValue
     */
    public String getDefaultValue() {
        return defaultValue;
    }


    /**
     * Sets the defaultValue value for this JS_InputField.
     * 
     * @param defaultValue
     */
    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }


    /**
     * Gets the tooltip value for this JS_InputField.
     * 
     * @return tooltip
     */
    public String getTooltip() {
        return tooltip;
    }


    /**
     * Sets the tooltip value for this JS_InputField.
     * 
     * @param tooltip
     */
    public void setTooltip(String tooltip) {
        this.tooltip = tooltip;
    }


    /**
     * Gets the remark value for this JS_InputField.
     * 
     * @return remark
     */
    public String getRemark() {
        return remark;
    }


    /**
     * Sets the remark value for this JS_InputField.
     * 
     * @param remark
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }


    /**
     * Gets the usedBy value for this JS_InputField.
     * 
     * @return usedBy
     */
    public String[] getUsedBy() {
        return usedBy;
    }


    /**
     * Sets the usedBy value for this JS_InputField.
     * 
     * @param usedBy
     */
    public void setUsedBy(String[] usedBy) {
        this.usedBy = usedBy;
    }


    /**
     * Gets the dataType value for this JS_InputField.
     * 
     * @return dataType
     */
    public DataTypeEnum getDataType() {
        return dataType;
    }


    /**
     * Sets the dataType value for this JS_InputField.
     * 
     * @param dataType
     */
    public void setDataType(DataTypeEnum dataType) {
        this.dataType = dataType;
    }


    /**
     * Gets the formulaType value for this JS_InputField.
     * 
     * @return formulaType
     */
    public TemplateDefineFieldFormulaType getFormulaType() {
        return formulaType;
    }


    /**
     * Sets the formulaType value for this JS_InputField.
     * 
     * @param formulaType
     */
    public void setFormulaType(TemplateDefineFieldFormulaType formulaType) {
        this.formulaType = formulaType;
    }


    /**
     * Gets the isReadOnly value for this JS_InputField.
     * 
     * @return isReadOnly
     */
    public boolean isIsReadOnly() {
        return isReadOnly;
    }


    /**
     * Sets the isReadOnly value for this JS_InputField.
     * 
     * @param isReadOnly
     */
    public void setIsReadOnly(boolean isReadOnly) {
        this.isReadOnly = isReadOnly;
    }


    /**
     * Gets the isSync value for this JS_InputField.
     * 
     * @return isSync
     */
    public boolean isIsSync() {
        return isSync;
    }


    /**
     * Sets the isSync value for this JS_InputField.
     * 
     * @param isSync
     */
    public void setIsSync(boolean isSync) {
        this.isSync = isSync;
    }


    /**
     * Gets the variableId value for this JS_InputField.
     * 
     * @return variableId
     */
    public int getVariableId() {
        return variableId;
    }


    /**
     * Sets the variableId value for this JS_InputField.
     * 
     * @param variableId
     */
    public void setVariableId(int variableId) {
        this.variableId = variableId;
    }


    /**
     * Gets the variableName value for this JS_InputField.
     * 
     * @return variableName
     */
    public String getVariableName() {
        return variableName;
    }


    /**
     * Sets the variableName value for this JS_InputField.
     * 
     * @param variableName
     */
    public void setVariableName(String variableName) {
        this.variableName = variableName;
    }


    /**
     * Gets the fixedVariableName value for this JS_InputField.
     * 
     * @return fixedVariableName
     */
    public FixedVariableNameEnum getFixedVariableName() {
        return fixedVariableName;
    }


    /**
     * Sets the fixedVariableName value for this JS_InputField.
     * 
     * @param fixedVariableName
     */
    public void setFixedVariableName(FixedVariableNameEnum fixedVariableName) {
        this.fixedVariableName = fixedVariableName;
    }


    /**
     * Gets the conclusionScope value for this JS_InputField.
     * 
     * @return conclusionScope
     */
    public ReportConclusionScopeEnum getConclusionScope() {
        return conclusionScope;
    }


    /**
     * Sets the conclusionScope value for this JS_InputField.
     * 
     * @param conclusionScope
     */
    public void setConclusionScope(ReportConclusionScopeEnum conclusionScope) {
        this.conclusionScope = conclusionScope;
    }


    /**
     * Gets the conclusionCellType value for this JS_InputField.
     * 
     * @return conclusionCellType
     */
    public ConclusionValueEnum getConclusionCellType() {
        return conclusionCellType;
    }


    /**
     * Sets the conclusionCellType value for this JS_InputField.
     * 
     * @param conclusionCellType
     */
    public void setConclusionCellType(ConclusionValueEnum conclusionCellType) {
        this.conclusionCellType = conclusionCellType;
    }


    /**
     * Gets the conclusionMarkFlag value for this JS_InputField.
     * 
     * @return conclusionMarkFlag
     */
    public ConclusionMarkFlagEnum getConclusionMarkFlag() {
        return conclusionMarkFlag;
    }


    /**
     * Sets the conclusionMarkFlag value for this JS_InputField.
     * 
     * @param conclusionMarkFlag
     */
    public void setConclusionMarkFlag(ConclusionMarkFlagEnum conclusionMarkFlag) {
        this.conclusionMarkFlag = conclusionMarkFlag;
    }


    /**
     * Gets the bindInfo value for this JS_InputField.
     * 
     * @return bindInfo
     */
    public FieldBindInfo getBindInfo() {
        return bindInfo;
    }


    /**
     * Sets the bindInfo value for this JS_InputField.
     * 
     * @param bindInfo
     */
    public void setBindInfo(FieldBindInfo bindInfo) {
        this.bindInfo = bindInfo;
    }


    /**
     * Gets the factType value for this JS_InputField.
     * 
     * @return factType
     */
    public FactDatasourceTypeEnum getFactType() {
        return factType;
    }


    /**
     * Sets the factType value for this JS_InputField.
     * 
     * @param factType
     */
    public void setFactType(FactDatasourceTypeEnum factType) {
        this.factType = factType;
    }


    /**
     * Gets the dims value for this JS_InputField.
     * 
     * @return dims
     */
    public Dim[] getDims() {
        return dims;
    }


    /**
     * Sets the dims value for this JS_InputField.
     * 
     * @param dims
     */
    public void setDims(Dim[] dims) {
        this.dims = dims;
    }


    /**
     * Gets the dataRowFieldProperties value for this JS_InputField.
     * 
     * @return dataRowFieldProperties
     */
    public DataRowField getDataRowFieldProperties() {
        return dataRowFieldProperties;
    }


    /**
     * Sets the dataRowFieldProperties value for this JS_InputField.
     * 
     * @param dataRowFieldProperties
     */
    public void setDataRowFieldProperties(DataRowField dataRowFieldProperties) {
        this.dataRowFieldProperties = dataRowFieldProperties;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_InputField)) {
            return false;
        }
        JS_InputField other = (JS_InputField) obj;
        if (obj == null) {
            return false;
        }
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.resultElementId==null && other.getResultElementId()==null) || 
             (this.resultElementId!=null &&
              this.resultElementId.equals(other.getResultElementId()))) &&
            ((this.listBoxProperties==null && other.getListBoxProperties()==null) || 
             (this.listBoxProperties!=null &&
              this.listBoxProperties.equals(other.getListBoxProperties()))) &&
            ((this.fieldType==null && other.getFieldType()==null) || 
             (this.fieldType!=null &&
              this.fieldType.equals(other.getFieldType()))) &&
            ((this.format==null && other.getFormat()==null) || 
             (this.format!=null &&
              this.format.equals(other.getFormat()))) &&
            ((this.defaultValue==null && other.getDefaultValue()==null) || 
             (this.defaultValue!=null &&
              this.defaultValue.equals(other.getDefaultValue()))) &&
            ((this.tooltip==null && other.getTooltip()==null) || 
             (this.tooltip!=null &&
              this.tooltip.equals(other.getTooltip()))) &&
            ((this.remark==null && other.getRemark()==null) || 
             (this.remark!=null &&
              this.remark.equals(other.getRemark()))) &&
            ((this.usedBy==null && other.getUsedBy()==null) || 
             (this.usedBy!=null &&
              java.util.Arrays.equals(this.usedBy, other.getUsedBy()))) &&
            ((this.dataType==null && other.getDataType()==null) || 
             (this.dataType!=null &&
              this.dataType.equals(other.getDataType()))) &&
            ((this.formulaType==null && other.getFormulaType()==null) || 
             (this.formulaType!=null &&
              this.formulaType.equals(other.getFormulaType()))) &&
            this.isReadOnly == other.isIsReadOnly() &&
            this.isSync == other.isIsSync() &&
            this.variableId == other.getVariableId() &&
            ((this.variableName==null && other.getVariableName()==null) || 
             (this.variableName!=null &&
              this.variableName.equals(other.getVariableName()))) &&
            ((this.fixedVariableName==null && other.getFixedVariableName()==null) || 
             (this.fixedVariableName!=null &&
              this.fixedVariableName.equals(other.getFixedVariableName()))) &&
            ((this.conclusionScope==null && other.getConclusionScope()==null) || 
             (this.conclusionScope!=null &&
              this.conclusionScope.equals(other.getConclusionScope()))) &&
            ((this.conclusionCellType==null && other.getConclusionCellType()==null) || 
             (this.conclusionCellType!=null &&
              this.conclusionCellType.equals(other.getConclusionCellType()))) &&
            ((this.conclusionMarkFlag==null && other.getConclusionMarkFlag()==null) || 
             (this.conclusionMarkFlag!=null &&
              this.conclusionMarkFlag.equals(other.getConclusionMarkFlag()))) &&
            ((this.bindInfo==null && other.getBindInfo()==null) || 
             (this.bindInfo!=null &&
              this.bindInfo.equals(other.getBindInfo()))) &&
            ((this.factType==null && other.getFactType()==null) || 
             (this.factType!=null &&
              this.factType.equals(other.getFactType()))) &&
            ((this.dims==null && other.getDims()==null) || 
             (this.dims!=null &&
              java.util.Arrays.equals(this.dims, other.getDims()))) &&
            ((this.dataRowFieldProperties==null && other.getDataRowFieldProperties()==null) || 
             (this.dataRowFieldProperties!=null &&
              this.dataRowFieldProperties.equals(other.getDataRowFieldProperties())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getResultElementId() != null) {
            _hashCode += getResultElementId().hashCode();
        }
        if (getListBoxProperties() != null) {
            _hashCode += getListBoxProperties().hashCode();
        }
        if (getFieldType() != null) {
            _hashCode += getFieldType().hashCode();
        }
        if (getFormat() != null) {
            _hashCode += getFormat().hashCode();
        }
        if (getDefaultValue() != null) {
            _hashCode += getDefaultValue().hashCode();
        }
        if (getTooltip() != null) {
            _hashCode += getTooltip().hashCode();
        }
        if (getRemark() != null) {
            _hashCode += getRemark().hashCode();
        }
        if (getUsedBy() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getUsedBy());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getUsedBy(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getDataType() != null) {
            _hashCode += getDataType().hashCode();
        }
        if (getFormulaType() != null) {
            _hashCode += getFormulaType().hashCode();
        }
        _hashCode += (isIsReadOnly() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += (isIsSync() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += getVariableId();
        if (getVariableName() != null) {
            _hashCode += getVariableName().hashCode();
        }
        if (getFixedVariableName() != null) {
            _hashCode += getFixedVariableName().hashCode();
        }
        if (getConclusionScope() != null) {
            _hashCode += getConclusionScope().hashCode();
        }
        if (getConclusionCellType() != null) {
            _hashCode += getConclusionCellType().hashCode();
        }
        if (getConclusionMarkFlag() != null) {
            _hashCode += getConclusionMarkFlag().hashCode();
        }
        if (getBindInfo() != null) {
            _hashCode += getBindInfo().hashCode();
        }
        if (getFactType() != null) {
            _hashCode += getFactType().hashCode();
        }
        if (getDims() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getDims());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getDims(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getDataRowFieldProperties() != null) {
            _hashCode += getDataRowFieldProperties().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_InputField.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_InputField"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("resultElementId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ResultElementId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("listBoxProperties");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ListBoxProperties"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ListBoxField"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("fieldType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FieldType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FieldTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("format");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Format"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("defaultValue");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DefaultValue"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tooltip");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Tooltip"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("remark");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Remark"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("usedBy");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "UsedBy"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "UsedByEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("dataType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("formulaType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FormulaType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateDefineFieldFormulaType"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isReadOnly");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsReadOnly"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isSync");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsSync"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("variableId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "VariableId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("variableName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "VariableName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("fixedVariableName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FixedVariableName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FixedVariableNameEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("conclusionScope");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ConclusionScope"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ReportConclusionScopeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("conclusionCellType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ConclusionCellType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ConclusionValueEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("conclusionMarkFlag");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ConclusionMarkFlag"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ConclusionMarkFlagEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("bindInfo");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "BindInfo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FieldBindInfo"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("factType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FactType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FactDatasourceTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("dims");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Dims"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "Dim"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Dim"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("dataRowFieldProperties");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataRowFieldProperties"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataRowField"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
