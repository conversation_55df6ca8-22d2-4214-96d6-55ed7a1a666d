package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/1 16:44
 */
public class EquipmentTypeLanguageSyncInfo extends PrintFriendliness {

    private Integer languageId;

    private String equipmentTypeName;

    private String equipmentTypeShortName;

    private String equipmentTypeDesc;



    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getEquipmentTypeName() {
        return equipmentTypeName;
    }

    public void setEquipmentTypeName(String equipmentTypeName) {
        this.equipmentTypeName = equipmentTypeName;
    }

    public String getEquipmentTypeShortName() {
        return equipmentTypeShortName;
    }

    public void setEquipmentTypeShortName(String equipmentTypeShortName) {
        this.equipmentTypeShortName = equipmentTypeShortName;
    }

    public String getEquipmentTypeDesc() {
        return equipmentTypeDesc;
    }

    public void setEquipmentTypeDesc(String equipmentTypeDesc) {
        this.equipmentTypeDesc = equipmentTypeDesc;
    }
}
