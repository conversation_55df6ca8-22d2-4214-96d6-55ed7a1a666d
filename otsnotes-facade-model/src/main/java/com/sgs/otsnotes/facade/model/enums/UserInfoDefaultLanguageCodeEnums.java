package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import org.springframework.util.StringUtils;

/**
 * @ClassName UserInfoDefaultLanguageCodeEnums
 * @Description TODO
 * <AUTHOR>
 * @Date 1/22/2021
 */
public enum  UserInfoDefaultLanguageCodeEnums {
    en_us("EN"),
    zh_cn("CHI");
    private String code;

    UserInfoDefaultLanguageCodeEnums(String code){
        this.code =code;
    }

    public String getCode() {
        return code;
    }

    public static boolean check(String code,UserInfoDefaultLanguageCodeEnums ...enums){
        if(StringUtils.isEmpty(code)){
            return false;
        }
        for (UserInfoDefaultLanguageCodeEnums anEnum : enums) {
            if(anEnum.code.equals(code)){
                return true;
            }
        }
        return false;
    }
}
