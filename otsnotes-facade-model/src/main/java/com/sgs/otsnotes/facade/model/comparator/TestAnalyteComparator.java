package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.common.NumberUtils;
import com.sgs.otsnotes.facade.model.info.trims.Analyte;

import java.util.Comparator;

public class TestAnalyteComparator implements Comparator<Analyte> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public TestAnalyteComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(Analyte o1, Analyte o2) {
        int index = NumberUtils.toInt(o1.getSequence()) - NumberUtils.toInt(o2.getSequence());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
