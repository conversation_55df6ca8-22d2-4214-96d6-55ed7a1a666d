/**
 * TServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class TServiceLocator extends org.apache.axis.client.Service implements TService {

    public TServiceLocator() {
    }


    public TServiceLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public TServiceLocator(String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for TServiceSoap
    private String TServiceSoap_address = "http://**************:8090/TService.asmx";

    public String getTServiceSoapAddress() {
        return TServiceSoap_address;
    }

    // The WSDD service name defaults to the port name.
    private String TServiceSoapWSDDServiceName = "TServiceSoap";

    public String getTServiceSoapWSDDServiceName() {
        return TServiceSoapWSDDServiceName;
    }

    public void setTServiceSoapWSDDServiceName(String name) {
        TServiceSoapWSDDServiceName = name;
    }

    public TServiceSoap getTServiceSoap() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(TServiceSoap_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getTServiceSoap(endpoint);
    }

    public TServiceSoap getTServiceSoap(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            TServiceSoapStub _stub = new TServiceSoapStub(portAddress, this);
            _stub.setPortName(getTServiceSoapWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setTServiceSoapEndpointAddress(String address) {
        TServiceSoap_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (TServiceSoap.class.isAssignableFrom(serviceEndpointInterface)) {
                TServiceSoapStub _stub = new TServiceSoapStub(new java.net.URL(TServiceSoap_address), this);
                _stub.setPortName(getTServiceSoapWSDDServiceName());
                return _stub;
            }
        }
        catch (Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        String inputPortName = portName.getLocalPart();
        if ("TServiceSoap".equals(inputPortName)) {
            return getTServiceSoap();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://ots.sgs.com/", "TService");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://ots.sgs.com/", "TServiceSoap"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(String portName, String address) throws javax.xml.rpc.ServiceException {
        
if ("TServiceSoap".equals(portName)) {
            setTServiceSoapEndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
