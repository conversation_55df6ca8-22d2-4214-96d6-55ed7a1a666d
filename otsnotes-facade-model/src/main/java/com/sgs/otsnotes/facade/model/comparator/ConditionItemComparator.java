package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.req.condition.ConditionItemReq;

import java.util.Comparator;

public class ConditionItemComparator implements Comparator<ConditionItemReq> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public ConditionItemComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(ConditionItemReq o1, ConditionItemReq o2) {
        Integer testConditionSeq1 = o1.getTestConditionSeq();
        if (testConditionSeq1 == null){
            testConditionSeq1 = 0;
        }
        Integer testConditionSeq2 = o2.getTestConditionSeq();
        if (testConditionSeq2 == null){
            testConditionSeq2 = 0;
        }
        int index = Integer.compare(testConditionSeq1, testConditionSeq2);
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }

}
