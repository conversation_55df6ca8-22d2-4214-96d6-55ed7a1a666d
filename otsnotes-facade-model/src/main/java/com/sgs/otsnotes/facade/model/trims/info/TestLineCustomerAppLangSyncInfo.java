package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class TestLineCustomerAppLangSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer customerAccountId;
    /**
     *
     */
    private Integer workInstructionId;

    /**
     *
     */
    private Integer workingInstructionCategoryId;
    /**
     *
     */
    private String reportReferenceNote;
    /**
     *
     */
    private String workingInstructionText;
    /**
     *
     */
    private String workingInstructionCategoryName;
    /**
     *
     */
    private String conditionInstructions;

    public Integer getCustomerAccountId() {
        return customerAccountId;
    }

    public void setCustomerAccountId(Integer customerAccountId) {
        this.customerAccountId = customerAccountId;
    }

    public Integer getWorkInstructionId() {
        return workInstructionId;
    }

    public void setWorkInstructionId(Integer workInstructionId) {
        this.workInstructionId = workInstructionId;
    }

    public Integer getWorkingInstructionCategoryId() {
        return workingInstructionCategoryId;
    }

    public void setWorkingInstructionCategoryId(Integer workingInstructionCategoryId) {
        this.workingInstructionCategoryId = workingInstructionCategoryId;
    }

    public String getReportReferenceNote() {
        return reportReferenceNote;
    }

    public void setReportReferenceNote(String reportReferenceNote) {
        this.reportReferenceNote = reportReferenceNote;
    }

    public String getWorkingInstructionText() {
        return workingInstructionText;
    }

    public void setWorkingInstructionText(String workingInstructionText) {
        this.workingInstructionText = workingInstructionText;
    }

    public String getWorkingInstructionCategoryName() {
        return workingInstructionCategoryName;
    }

    public void setWorkingInstructionCategoryName(String workingInstructionCategoryName) {
        this.workingInstructionCategoryName = workingInstructionCategoryName;
    }

    public String getConditionInstructions() {
        return conditionInstructions;
    }

    public void setConditionInstructions(String conditionInstructions) {
        this.conditionInstructions = conditionInstructions;
    }
}
