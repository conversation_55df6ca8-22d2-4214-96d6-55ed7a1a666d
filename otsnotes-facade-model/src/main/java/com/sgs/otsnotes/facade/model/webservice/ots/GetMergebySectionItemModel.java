/**
 * GetMergebySectionItemModel.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GetMergebySectionItemModel  implements java.io.Serializable {
    private TemplateInstanceInfo reportInfo;

    private int seqencing;

    private ReportDatasource datasource;

    public GetMergebySectionItemModel() {
    }

    public GetMergebySectionItemModel(
           TemplateInstanceInfo reportInfo,
           int seqencing,
           ReportDatasource datasource) {
           this.reportInfo = reportInfo;
           this.seqencing = seqencing;
           this.datasource = datasource;
    }


    /**
     * Gets the reportInfo value for this GetMergebySectionItemModel.
     * 
     * @return reportInfo
     */
    public TemplateInstanceInfo getReportInfo() {
        return reportInfo;
    }


    /**
     * Sets the reportInfo value for this GetMergebySectionItemModel.
     * 
     * @param reportInfo
     */
    public void setReportInfo(TemplateInstanceInfo reportInfo) {
        this.reportInfo = reportInfo;
    }


    /**
     * Gets the seqencing value for this GetMergebySectionItemModel.
     * 
     * @return seqencing
     */
    public int getSeqencing() {
        return seqencing;
    }


    /**
     * Sets the seqencing value for this GetMergebySectionItemModel.
     * 
     * @param seqencing
     */
    public void setSeqencing(int seqencing) {
        this.seqencing = seqencing;
    }


    /**
     * Gets the datasource value for this GetMergebySectionItemModel.
     * 
     * @return datasource
     */
    public ReportDatasource getDatasource() {
        return datasource;
    }


    /**
     * Sets the datasource value for this GetMergebySectionItemModel.
     * 
     * @param datasource
     */
    public void setDatasource(ReportDatasource datasource) {
        this.datasource = datasource;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GetMergebySectionItemModel)) {
            return false;
        }
        GetMergebySectionItemModel other = (GetMergebySectionItemModel) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.reportInfo==null && other.getReportInfo()==null) || 
             (this.reportInfo!=null &&
              this.reportInfo.equals(other.getReportInfo()))) &&
            this.seqencing == other.getSeqencing() &&
            ((this.datasource==null && other.getDatasource()==null) || 
             (this.datasource!=null &&
              this.datasource.equals(other.getDatasource())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getReportInfo() != null) {
            _hashCode += getReportInfo().hashCode();
        }
        _hashCode += getSeqencing();
        if (getDatasource() != null) {
            _hashCode += getDatasource().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GetMergebySectionItemModel.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "GetMergebySectionItemModel"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("reportInfo");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "reportInfo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInstanceInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("seqencing");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "seqencing"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("datasource");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "datasource"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ReportDatasource"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
