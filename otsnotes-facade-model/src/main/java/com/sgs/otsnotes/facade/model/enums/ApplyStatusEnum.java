package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum ApplyStatusEnum {
    None(0, "None"),
    Apply(1, "申请中"),
    Approval(2, "Approve"),
    Reject(3, "Reject");
    /*申请状态：
    1、申请中，申请人提交之后的状态；
    2、通过，主管审批通过；
    3、拒绝，主管审批拒绝；*/
    @DictCodeField
    private final int code;
    @DictLabelField
    private final String message;

    ApplyStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String message() {
        return this.message;
    }

    public static ApplyStatusEnum getCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ApplyStatusEnum item : ApplyStatusEnum.values()) {
            if (code.equals(item.getCode())) {
                return item;
            }
        }
        return null;
    }
}
