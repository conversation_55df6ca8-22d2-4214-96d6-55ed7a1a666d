/**
 * DatasourceTypeEnum.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class DatasourceTypeEnum implements java.io.Serializable {
    private String _value_;
    private static java.util.HashMap _table_ = new java.util.HashMap();

    // Constructor
    protected DatasourceTypeEnum(String value) {
        _value_ = value;
        _table_.put(_value_,this);
    }

    public static final String _NA = "NA";
    public static final String _TestItem = "TestItem";
    public static final String _Sample = "Sample";
    public static final String _ChemicElement = "ChemicElement";
    public static final String _WashTimes = "WashTimes";
    public static final String _Approver = "Approver";
    public static final DatasourceTypeEnum NA = new DatasourceTypeEnum(_NA);
    public static final DatasourceTypeEnum TestItem = new DatasourceTypeEnum(_TestItem);
    public static final DatasourceTypeEnum Sample = new DatasourceTypeEnum(_Sample);
    public static final DatasourceTypeEnum ChemicElement = new DatasourceTypeEnum(_ChemicElement);
    public static final DatasourceTypeEnum WashTimes = new DatasourceTypeEnum(_WashTimes);
    public static final DatasourceTypeEnum Approver = new DatasourceTypeEnum(_Approver);
    public String getValue() { return _value_;}
    public static DatasourceTypeEnum fromValue(String value)
          throws IllegalArgumentException {
        DatasourceTypeEnum enumeration = (DatasourceTypeEnum)
            _table_.get(value);
        if (enumeration==null) {
            throw new IllegalArgumentException();
        }
        return enumeration;
    }
    public static DatasourceTypeEnum fromString(String value)
          throws IllegalArgumentException {
        return fromValue(value);
    }
    public boolean equals(Object obj) {return (obj == this);}
    public int hashCode() { return toString().hashCode();}
    public String toString() { return _value_;}
    public Object readResolve() throws java.io.ObjectStreamException { return fromValue(_value_);}
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumSerializer(
            _javaType, _xmlType);
    }
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumDeserializer(
            _javaType, _xmlType);
    }
    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(DatasourceTypeEnum.class);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DatasourceTypeEnum"));
    }
    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

}
