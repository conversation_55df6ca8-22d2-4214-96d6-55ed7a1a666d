package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class ApplicationFactorSyncInfo extends PrintFriendliness {

    private Integer applicationFactorId;

    private String applicationFactorName;

    private Integer applicationFactorTypeId;

    private String applicationFactorTypeName;

    private String status;

    public Integer getApplicationFactorId() {
        return applicationFactorId;
    }

    public void setApplicationFactorId(Integer applicationFactorId) {
        this.applicationFactorId = applicationFactorId;
    }

    public String getApplicationFactorName() {
        return applicationFactorName;
    }

    public void setApplicationFactorName(String applicationFactorName) {
        this.applicationFactorName = applicationFactorName;
    }

    public Integer getApplicationFactorTypeId() {
        return applicationFactorTypeId;
    }

    public void setApplicationFactorTypeId(Integer applicationFactorTypeId) {
        this.applicationFactorTypeId = applicationFactorTypeId;
    }

    public String getApplicationFactorTypeName() {
        return applicationFactorTypeName;
    }

    public void setApplicationFactorTypeName(String applicationFactorTypeName) {
        this.applicationFactorTypeName = applicationFactorTypeName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
