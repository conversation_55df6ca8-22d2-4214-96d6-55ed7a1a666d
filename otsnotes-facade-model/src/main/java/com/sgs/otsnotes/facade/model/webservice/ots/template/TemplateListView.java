/**
 * TemplateListView.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots.template;

public class TemplateListView  extends com.sgs.otsnotes.facade.model.webservice.ots.template.EntityObject  implements java.io.Serializable {
    private int templateID;

    private java.lang.String templateName;

    private int templateTypeID;

    private boolean isActived;

    private java.util.Calendar createdDate;

    private java.lang.Integer appliedBy;

    private java.lang.String customerName;

    private int userAccountID;

    private java.lang.String userName;

    private short BUID;

    private java.lang.Boolean templateForLab;

    private java.lang.Boolean templateForReport;

    private java.lang.String BUSubTypeName;

    private java.lang.String customerNumber;

    private java.lang.Integer subBUID;

    private java.lang.String templatePackageID;

    private java.lang.String templatePackageName;

    private int languageID;

    private java.lang.Boolean isCurrentTemplate;

    private java.lang.Boolean isCurrentChineseTemplate;

    private java.lang.String BUSubTypeCode;

    private java.lang.String BUCode;

    private java.lang.String modifiedByName;

    private java.lang.String templateFilePath;

    private java.util.Calendar modifiedDate;

    private boolean isForCN;

    private boolean isForHK;

    private java.lang.String templateLanguage;

    private short applicationID;

    private java.lang.String applicationName;

    private boolean newVersion;

    private boolean newMapping;

    private java.lang.String PPTemplateID;

    public TemplateListView() {
    }

    public TemplateListView(
            com.sgs.otsnotes.facade.model.webservice.ots.template.EntityKey entityKey,
           int templateID,
           java.lang.String templateName,
           int templateTypeID,
           boolean isActived,
           java.util.Calendar createdDate,
           java.lang.Integer appliedBy,
           java.lang.String customerName,
           int userAccountID,
           java.lang.String userName,
           short BUID,
           java.lang.Boolean templateForLab,
           java.lang.Boolean templateForReport,
           java.lang.String BUSubTypeName,
           java.lang.String customerNumber,
           java.lang.Integer subBUID,
           java.lang.String templatePackageID,
           java.lang.String templatePackageName,
           int languageID,
           java.lang.Boolean isCurrentTemplate,
           java.lang.Boolean isCurrentChineseTemplate,
           java.lang.String BUSubTypeCode,
           java.lang.String BUCode,
           java.lang.String modifiedByName,
           java.lang.String templateFilePath,
           java.util.Calendar modifiedDate,
           boolean isForCN,
           boolean isForHK,
           java.lang.String templateLanguage,
           short applicationID,
           java.lang.String applicationName,
           boolean newVersion,
           boolean newMapping,
           java.lang.String PPTemplateID) {
        super(
            entityKey);
        this.templateID = templateID;
        this.templateName = templateName;
        this.templateTypeID = templateTypeID;
        this.isActived = isActived;
        this.createdDate = createdDate;
        this.appliedBy = appliedBy;
        this.customerName = customerName;
        this.userAccountID = userAccountID;
        this.userName = userName;
        this.BUID = BUID;
        this.templateForLab = templateForLab;
        this.templateForReport = templateForReport;
        this.BUSubTypeName = BUSubTypeName;
        this.customerNumber = customerNumber;
        this.subBUID = subBUID;
        this.templatePackageID = templatePackageID;
        this.templatePackageName = templatePackageName;
        this.languageID = languageID;
        this.isCurrentTemplate = isCurrentTemplate;
        this.isCurrentChineseTemplate = isCurrentChineseTemplate;
        this.BUSubTypeCode = BUSubTypeCode;
        this.BUCode = BUCode;
        this.modifiedByName = modifiedByName;
        this.templateFilePath = templateFilePath;
        this.modifiedDate = modifiedDate;
        this.isForCN = isForCN;
        this.isForHK = isForHK;
        this.templateLanguage = templateLanguage;
        this.applicationID = applicationID;
        this.applicationName = applicationName;
        this.newVersion = newVersion;
        this.newMapping = newMapping;
        this.PPTemplateID = PPTemplateID;
    }


    /**
     * Gets the templateID value for this TemplateListView.
     * 
     * @return templateID
     */
    public int getTemplateID() {
        return templateID;
    }


    /**
     * Sets the templateID value for this TemplateListView.
     * 
     * @param templateID
     */
    public void setTemplateID(int templateID) {
        this.templateID = templateID;
    }


    /**
     * Gets the templateName value for this TemplateListView.
     * 
     * @return templateName
     */
    public java.lang.String getTemplateName() {
        return templateName;
    }


    /**
     * Sets the templateName value for this TemplateListView.
     * 
     * @param templateName
     */
    public void setTemplateName(java.lang.String templateName) {
        this.templateName = templateName;
    }


    /**
     * Gets the templateTypeID value for this TemplateListView.
     * 
     * @return templateTypeID
     */
    public int getTemplateTypeID() {
        return templateTypeID;
    }


    /**
     * Sets the templateTypeID value for this TemplateListView.
     * 
     * @param templateTypeID
     */
    public void setTemplateTypeID(int templateTypeID) {
        this.templateTypeID = templateTypeID;
    }


    /**
     * Gets the isActived value for this TemplateListView.
     * 
     * @return isActived
     */
    public boolean isIsActived() {
        return isActived;
    }


    /**
     * Sets the isActived value for this TemplateListView.
     * 
     * @param isActived
     */
    public void setIsActived(boolean isActived) {
        this.isActived = isActived;
    }


    /**
     * Gets the createdDate value for this TemplateListView.
     * 
     * @return createdDate
     */
    public java.util.Calendar getCreatedDate() {
        return createdDate;
    }


    /**
     * Sets the createdDate value for this TemplateListView.
     * 
     * @param createdDate
     */
    public void setCreatedDate(java.util.Calendar createdDate) {
        this.createdDate = createdDate;
    }


    /**
     * Gets the appliedBy value for this TemplateListView.
     * 
     * @return appliedBy
     */
    public java.lang.Integer getAppliedBy() {
        return appliedBy;
    }


    /**
     * Sets the appliedBy value for this TemplateListView.
     * 
     * @param appliedBy
     */
    public void setAppliedBy(java.lang.Integer appliedBy) {
        this.appliedBy = appliedBy;
    }


    /**
     * Gets the customerName value for this TemplateListView.
     * 
     * @return customerName
     */
    public java.lang.String getCustomerName() {
        return customerName;
    }


    /**
     * Sets the customerName value for this TemplateListView.
     * 
     * @param customerName
     */
    public void setCustomerName(java.lang.String customerName) {
        this.customerName = customerName;
    }


    /**
     * Gets the userAccountID value for this TemplateListView.
     * 
     * @return userAccountID
     */
    public int getUserAccountID() {
        return userAccountID;
    }


    /**
     * Sets the userAccountID value for this TemplateListView.
     * 
     * @param userAccountID
     */
    public void setUserAccountID(int userAccountID) {
        this.userAccountID = userAccountID;
    }


    /**
     * Gets the userName value for this TemplateListView.
     * 
     * @return userName
     */
    public java.lang.String getUserName() {
        return userName;
    }


    /**
     * Sets the userName value for this TemplateListView.
     * 
     * @param userName
     */
    public void setUserName(java.lang.String userName) {
        this.userName = userName;
    }


    /**
     * Gets the BUID value for this TemplateListView.
     * 
     * @return BUID
     */
    public short getBUID() {
        return BUID;
    }


    /**
     * Sets the BUID value for this TemplateListView.
     * 
     * @param BUID
     */
    public void setBUID(short BUID) {
        this.BUID = BUID;
    }


    /**
     * Gets the templateForLab value for this TemplateListView.
     * 
     * @return templateForLab
     */
    public java.lang.Boolean getTemplateForLab() {
        return templateForLab;
    }


    /**
     * Sets the templateForLab value for this TemplateListView.
     * 
     * @param templateForLab
     */
    public void setTemplateForLab(java.lang.Boolean templateForLab) {
        this.templateForLab = templateForLab;
    }


    /**
     * Gets the templateForReport value for this TemplateListView.
     * 
     * @return templateForReport
     */
    public java.lang.Boolean getTemplateForReport() {
        return templateForReport;
    }


    /**
     * Sets the templateForReport value for this TemplateListView.
     * 
     * @param templateForReport
     */
    public void setTemplateForReport(java.lang.Boolean templateForReport) {
        this.templateForReport = templateForReport;
    }


    /**
     * Gets the BUSubTypeName value for this TemplateListView.
     * 
     * @return BUSubTypeName
     */
    public java.lang.String getBUSubTypeName() {
        return BUSubTypeName;
    }


    /**
     * Sets the BUSubTypeName value for this TemplateListView.
     * 
     * @param BUSubTypeName
     */
    public void setBUSubTypeName(java.lang.String BUSubTypeName) {
        this.BUSubTypeName = BUSubTypeName;
    }


    /**
     * Gets the customerNumber value for this TemplateListView.
     * 
     * @return customerNumber
     */
    public java.lang.String getCustomerNumber() {
        return customerNumber;
    }


    /**
     * Sets the customerNumber value for this TemplateListView.
     * 
     * @param customerNumber
     */
    public void setCustomerNumber(java.lang.String customerNumber) {
        this.customerNumber = customerNumber;
    }


    /**
     * Gets the subBUID value for this TemplateListView.
     * 
     * @return subBUID
     */
    public java.lang.Integer getSubBUID() {
        return subBUID;
    }


    /**
     * Sets the subBUID value for this TemplateListView.
     * 
     * @param subBUID
     */
    public void setSubBUID(java.lang.Integer subBUID) {
        this.subBUID = subBUID;
    }


    /**
     * Gets the templatePackageID value for this TemplateListView.
     * 
     * @return templatePackageID
     */
    public java.lang.String getTemplatePackageID() {
        return templatePackageID;
    }


    /**
     * Sets the templatePackageID value for this TemplateListView.
     * 
     * @param templatePackageID
     */
    public void setTemplatePackageID(java.lang.String templatePackageID) {
        this.templatePackageID = templatePackageID;
    }


    /**
     * Gets the templatePackageName value for this TemplateListView.
     * 
     * @return templatePackageName
     */
    public java.lang.String getTemplatePackageName() {
        return templatePackageName;
    }


    /**
     * Sets the templatePackageName value for this TemplateListView.
     * 
     * @param templatePackageName
     */
    public void setTemplatePackageName(java.lang.String templatePackageName) {
        this.templatePackageName = templatePackageName;
    }


    /**
     * Gets the languageID value for this TemplateListView.
     * 
     * @return languageID
     */
    public int getLanguageID() {
        return languageID;
    }


    /**
     * Sets the languageID value for this TemplateListView.
     * 
     * @param languageID
     */
    public void setLanguageID(int languageID) {
        this.languageID = languageID;
    }


    /**
     * Gets the isCurrentTemplate value for this TemplateListView.
     * 
     * @return isCurrentTemplate
     */
    public java.lang.Boolean getIsCurrentTemplate() {
        return isCurrentTemplate;
    }


    /**
     * Sets the isCurrentTemplate value for this TemplateListView.
     * 
     * @param isCurrentTemplate
     */
    public void setIsCurrentTemplate(java.lang.Boolean isCurrentTemplate) {
        this.isCurrentTemplate = isCurrentTemplate;
    }


    /**
     * Gets the isCurrentChineseTemplate value for this TemplateListView.
     * 
     * @return isCurrentChineseTemplate
     */
    public java.lang.Boolean getIsCurrentChineseTemplate() {
        return isCurrentChineseTemplate;
    }


    /**
     * Sets the isCurrentChineseTemplate value for this TemplateListView.
     * 
     * @param isCurrentChineseTemplate
     */
    public void setIsCurrentChineseTemplate(java.lang.Boolean isCurrentChineseTemplate) {
        this.isCurrentChineseTemplate = isCurrentChineseTemplate;
    }


    /**
     * Gets the BUSubTypeCode value for this TemplateListView.
     * 
     * @return BUSubTypeCode
     */
    public java.lang.String getBUSubTypeCode() {
        return BUSubTypeCode;
    }


    /**
     * Sets the BUSubTypeCode value for this TemplateListView.
     * 
     * @param BUSubTypeCode
     */
    public void setBUSubTypeCode(java.lang.String BUSubTypeCode) {
        this.BUSubTypeCode = BUSubTypeCode;
    }


    /**
     * Gets the BUCode value for this TemplateListView.
     * 
     * @return BUCode
     */
    public java.lang.String getBUCode() {
        return BUCode;
    }


    /**
     * Sets the BUCode value for this TemplateListView.
     * 
     * @param BUCode
     */
    public void setBUCode(java.lang.String BUCode) {
        this.BUCode = BUCode;
    }


    /**
     * Gets the modifiedByName value for this TemplateListView.
     * 
     * @return modifiedByName
     */
    public java.lang.String getModifiedByName() {
        return modifiedByName;
    }


    /**
     * Sets the modifiedByName value for this TemplateListView.
     * 
     * @param modifiedByName
     */
    public void setModifiedByName(java.lang.String modifiedByName) {
        this.modifiedByName = modifiedByName;
    }


    /**
     * Gets the templateFilePath value for this TemplateListView.
     * 
     * @return templateFilePath
     */
    public java.lang.String getTemplateFilePath() {
        return templateFilePath;
    }


    /**
     * Sets the templateFilePath value for this TemplateListView.
     * 
     * @param templateFilePath
     */
    public void setTemplateFilePath(java.lang.String templateFilePath) {
        this.templateFilePath = templateFilePath;
    }


    /**
     * Gets the modifiedDate value for this TemplateListView.
     * 
     * @return modifiedDate
     */
    public java.util.Calendar getModifiedDate() {
        return modifiedDate;
    }


    /**
     * Sets the modifiedDate value for this TemplateListView.
     * 
     * @param modifiedDate
     */
    public void setModifiedDate(java.util.Calendar modifiedDate) {
        this.modifiedDate = modifiedDate;
    }


    /**
     * Gets the isForCN value for this TemplateListView.
     * 
     * @return isForCN
     */
    public boolean isIsForCN() {
        return isForCN;
    }


    /**
     * Sets the isForCN value for this TemplateListView.
     * 
     * @param isForCN
     */
    public void setIsForCN(boolean isForCN) {
        this.isForCN = isForCN;
    }


    /**
     * Gets the isForHK value for this TemplateListView.
     * 
     * @return isForHK
     */
    public boolean isIsForHK() {
        return isForHK;
    }


    /**
     * Sets the isForHK value for this TemplateListView.
     * 
     * @param isForHK
     */
    public void setIsForHK(boolean isForHK) {
        this.isForHK = isForHK;
    }


    /**
     * Gets the templateLanguage value for this TemplateListView.
     * 
     * @return templateLanguage
     */
    public java.lang.String getTemplateLanguage() {
        return templateLanguage;
    }


    /**
     * Sets the templateLanguage value for this TemplateListView.
     * 
     * @param templateLanguage
     */
    public void setTemplateLanguage(java.lang.String templateLanguage) {
        this.templateLanguage = templateLanguage;
    }


    /**
     * Gets the applicationID value for this TemplateListView.
     * 
     * @return applicationID
     */
    public short getApplicationID() {
        return applicationID;
    }


    /**
     * Sets the applicationID value for this TemplateListView.
     * 
     * @param applicationID
     */
    public void setApplicationID(short applicationID) {
        this.applicationID = applicationID;
    }


    /**
     * Gets the applicationName value for this TemplateListView.
     * 
     * @return applicationName
     */
    public java.lang.String getApplicationName() {
        return applicationName;
    }


    /**
     * Sets the applicationName value for this TemplateListView.
     * 
     * @param applicationName
     */
    public void setApplicationName(java.lang.String applicationName) {
        this.applicationName = applicationName;
    }


    /**
     * Gets the newVersion value for this TemplateListView.
     * 
     * @return newVersion
     */
    public boolean isNewVersion() {
        return newVersion;
    }


    /**
     * Sets the newVersion value for this TemplateListView.
     * 
     * @param newVersion
     */
    public void setNewVersion(boolean newVersion) {
        this.newVersion = newVersion;
    }


    /**
     * Gets the newMapping value for this TemplateListView.
     * 
     * @return newMapping
     */
    public boolean isNewMapping() {
        return newMapping;
    }


    /**
     * Sets the newMapping value for this TemplateListView.
     * 
     * @param newMapping
     */
    public void setNewMapping(boolean newMapping) {
        this.newMapping = newMapping;
    }


    /**
     * Gets the PPTemplateID value for this TemplateListView.
     * 
     * @return PPTemplateID
     */
    public java.lang.String getPPTemplateID() {
        return PPTemplateID;
    }


    /**
     * Sets the PPTemplateID value for this TemplateListView.
     * 
     * @param PPTemplateID
     */
    public void setPPTemplateID(java.lang.String PPTemplateID) {
        this.PPTemplateID = PPTemplateID;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof TemplateListView)) {
            return false;
        }
        TemplateListView other = (TemplateListView) obj;
        if (obj == null) {
            return false;
        }
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            this.templateID == other.getTemplateID() &&
            ((this.templateName==null && other.getTemplateName()==null) || 
             (this.templateName!=null &&
              this.templateName.equals(other.getTemplateName()))) &&
            this.templateTypeID == other.getTemplateTypeID() &&
            this.isActived == other.isIsActived() &&
            ((this.createdDate==null && other.getCreatedDate()==null) || 
             (this.createdDate!=null &&
              this.createdDate.equals(other.getCreatedDate()))) &&
            ((this.appliedBy==null && other.getAppliedBy()==null) || 
             (this.appliedBy!=null &&
              this.appliedBy.equals(other.getAppliedBy()))) &&
            ((this.customerName==null && other.getCustomerName()==null) || 
             (this.customerName!=null &&
              this.customerName.equals(other.getCustomerName()))) &&
            this.userAccountID == other.getUserAccountID() &&
            ((this.userName==null && other.getUserName()==null) || 
             (this.userName!=null &&
              this.userName.equals(other.getUserName()))) &&
            this.BUID == other.getBUID() &&
            ((this.templateForLab==null && other.getTemplateForLab()==null) || 
             (this.templateForLab!=null &&
              this.templateForLab.equals(other.getTemplateForLab()))) &&
            ((this.templateForReport==null && other.getTemplateForReport()==null) || 
             (this.templateForReport!=null &&
              this.templateForReport.equals(other.getTemplateForReport()))) &&
            ((this.BUSubTypeName==null && other.getBUSubTypeName()==null) || 
             (this.BUSubTypeName!=null &&
              this.BUSubTypeName.equals(other.getBUSubTypeName()))) &&
            ((this.customerNumber==null && other.getCustomerNumber()==null) || 
             (this.customerNumber!=null &&
              this.customerNumber.equals(other.getCustomerNumber()))) &&
            ((this.subBUID==null && other.getSubBUID()==null) || 
             (this.subBUID!=null &&
              this.subBUID.equals(other.getSubBUID()))) &&
            ((this.templatePackageID==null && other.getTemplatePackageID()==null) || 
             (this.templatePackageID!=null &&
              this.templatePackageID.equals(other.getTemplatePackageID()))) &&
            ((this.templatePackageName==null && other.getTemplatePackageName()==null) || 
             (this.templatePackageName!=null &&
              this.templatePackageName.equals(other.getTemplatePackageName()))) &&
            this.languageID == other.getLanguageID() &&
            ((this.isCurrentTemplate==null && other.getIsCurrentTemplate()==null) || 
             (this.isCurrentTemplate!=null &&
              this.isCurrentTemplate.equals(other.getIsCurrentTemplate()))) &&
            ((this.isCurrentChineseTemplate==null && other.getIsCurrentChineseTemplate()==null) || 
             (this.isCurrentChineseTemplate!=null &&
              this.isCurrentChineseTemplate.equals(other.getIsCurrentChineseTemplate()))) &&
            ((this.BUSubTypeCode==null && other.getBUSubTypeCode()==null) || 
             (this.BUSubTypeCode!=null &&
              this.BUSubTypeCode.equals(other.getBUSubTypeCode()))) &&
            ((this.BUCode==null && other.getBUCode()==null) || 
             (this.BUCode!=null &&
              this.BUCode.equals(other.getBUCode()))) &&
            ((this.modifiedByName==null && other.getModifiedByName()==null) || 
             (this.modifiedByName!=null &&
              this.modifiedByName.equals(other.getModifiedByName()))) &&
            ((this.templateFilePath==null && other.getTemplateFilePath()==null) || 
             (this.templateFilePath!=null &&
              this.templateFilePath.equals(other.getTemplateFilePath()))) &&
            ((this.modifiedDate==null && other.getModifiedDate()==null) || 
             (this.modifiedDate!=null &&
              this.modifiedDate.equals(other.getModifiedDate()))) &&
            this.isForCN == other.isIsForCN() &&
            this.isForHK == other.isIsForHK() &&
            ((this.templateLanguage==null && other.getTemplateLanguage()==null) || 
             (this.templateLanguage!=null &&
              this.templateLanguage.equals(other.getTemplateLanguage()))) &&
            this.applicationID == other.getApplicationID() &&
            ((this.applicationName==null && other.getApplicationName()==null) || 
             (this.applicationName!=null &&
              this.applicationName.equals(other.getApplicationName()))) &&
            this.newVersion == other.isNewVersion() &&
            this.newMapping == other.isNewMapping() &&
            ((this.PPTemplateID==null && other.getPPTemplateID()==null) || 
             (this.PPTemplateID!=null &&
              this.PPTemplateID.equals(other.getPPTemplateID())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        _hashCode += getTemplateID();
        if (getTemplateName() != null) {
            _hashCode += getTemplateName().hashCode();
        }
        _hashCode += getTemplateTypeID();
        _hashCode += (isIsActived() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        if (getCreatedDate() != null) {
            _hashCode += getCreatedDate().hashCode();
        }
        if (getAppliedBy() != null) {
            _hashCode += getAppliedBy().hashCode();
        }
        if (getCustomerName() != null) {
            _hashCode += getCustomerName().hashCode();
        }
        _hashCode += getUserAccountID();
        if (getUserName() != null) {
            _hashCode += getUserName().hashCode();
        }
        _hashCode += getBUID();
        if (getTemplateForLab() != null) {
            _hashCode += getTemplateForLab().hashCode();
        }
        if (getTemplateForReport() != null) {
            _hashCode += getTemplateForReport().hashCode();
        }
        if (getBUSubTypeName() != null) {
            _hashCode += getBUSubTypeName().hashCode();
        }
        if (getCustomerNumber() != null) {
            _hashCode += getCustomerNumber().hashCode();
        }
        if (getSubBUID() != null) {
            _hashCode += getSubBUID().hashCode();
        }
        if (getTemplatePackageID() != null) {
            _hashCode += getTemplatePackageID().hashCode();
        }
        if (getTemplatePackageName() != null) {
            _hashCode += getTemplatePackageName().hashCode();
        }
        _hashCode += getLanguageID();
        if (getIsCurrentTemplate() != null) {
            _hashCode += getIsCurrentTemplate().hashCode();
        }
        if (getIsCurrentChineseTemplate() != null) {
            _hashCode += getIsCurrentChineseTemplate().hashCode();
        }
        if (getBUSubTypeCode() != null) {
            _hashCode += getBUSubTypeCode().hashCode();
        }
        if (getBUCode() != null) {
            _hashCode += getBUCode().hashCode();
        }
        if (getModifiedByName() != null) {
            _hashCode += getModifiedByName().hashCode();
        }
        if (getTemplateFilePath() != null) {
            _hashCode += getTemplateFilePath().hashCode();
        }
        if (getModifiedDate() != null) {
            _hashCode += getModifiedDate().hashCode();
        }
        _hashCode += (isIsForCN() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += (isIsForHK() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        if (getTemplateLanguage() != null) {
            _hashCode += getTemplateLanguage().hashCode();
        }
        _hashCode += getApplicationID();
        if (getApplicationName() != null) {
            _hashCode += getApplicationName().hashCode();
        }
        _hashCode += (isNewVersion() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += (isNewMapping() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        if (getPPTemplateID() != null) {
            _hashCode += getPPTemplateID().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(TemplateListView.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", "TemplateListView"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "TemplateID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "TemplateName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateTypeID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "TemplateTypeID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isActived");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "IsActived"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("createdDate");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "CreatedDate"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("appliedBy");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "AppliedBy"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("customerName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "CustomerName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("userAccountID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "UserAccountID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("userName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "UserName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("BUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "BUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "short"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateForLab");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "TemplateForLab"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateForReport");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "TemplateForReport"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("BUSubTypeName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "BUSubTypeName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("customerNumber");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "CustomerNumber"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("subBUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "SubBUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templatePackageID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "TemplatePackageID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templatePackageName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "TemplatePackageName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("languageID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "LanguageID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isCurrentTemplate");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "IsCurrentTemplate"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isCurrentChineseTemplate");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "IsCurrentChineseTemplate"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("BUSubTypeCode");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "BUSubTypeCode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("BUCode");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "BUCode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("modifiedByName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "ModifiedByName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "TemplateFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("modifiedDate");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "ModifiedDate"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isForCN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "IsForCN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isForHK");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "IsForHK"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateLanguage");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "TemplateLanguage"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("applicationID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "ApplicationID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "short"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("applicationName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "ApplicationName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("newVersion");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "NewVersion"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("newMapping");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "NewMapping"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PPTemplateID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "PPTemplateID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
