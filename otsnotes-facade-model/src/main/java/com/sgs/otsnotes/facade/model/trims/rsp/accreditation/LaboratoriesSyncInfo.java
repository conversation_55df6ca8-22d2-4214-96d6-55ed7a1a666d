package com.sgs.otsnotes.facade.model.trims.rsp.accreditation;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class LaboratoriesSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer laboratoryId;
    /**
     *
     */
    private List<LaboratorySectionsSyncInfo> laboratorySections;

    public Integer getLaboratoryId() {
        return laboratoryId;
    }

    public void setLaboratoryId(Integer laboratoryId) {
        this.laboratoryId = laboratoryId;
    }

    public List<LaboratorySectionsSyncInfo> getLaboratorySections() {
        return laboratorySections;
    }

    public void setLaboratorySections(List<LaboratorySectionsSyncInfo> laboratorySections) {
        this.laboratorySections = laboratorySections;
    }
}