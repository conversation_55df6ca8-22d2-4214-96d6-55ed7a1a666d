package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class StandardSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private String standardShortName;
    /**
     *
     */
    private Integer versionIdentifier;
    /**
     *
     */
    private Integer standardId;
    /**
     *
     */
    private String standardName;
    /**
     *
     */
    private Integer versionNo;
    /**
     *
     */
    private Integer status;
    /**
     *
     *
     */
    private Integer isCancelled;
    /**
     *
     */
    private List<SectionSyncInfo> sectionItems;
    /**
     *
     *//*
    private List<CountrieSyncInfo> countries;
    *//**
     *
     *//*
    private List<StateSyncInfo> states;
    *//**
     *
     *//*
    private List<RegionSyncInfo> regions;*/
    /**
     *
     */
    private List<StandardLangSyncInfo> otherLanguageItems;

    public String getStandardShortName() {
        return standardShortName;
    }

    public void setStandardShortName(String standardShortName) {
        this.standardShortName = standardShortName;
    }

    public Integer getVersionIdentifier() {
        return versionIdentifier;
    }

    public void setVersionIdentifier(Integer versionIdentifier) {
        this.versionIdentifier = versionIdentifier;
    }

    public Integer getStandardId() {
        return standardId;
    }

    public void setStandardId(Integer standardId) {
        this.standardId = standardId;
    }

    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getIsCancelled() {
        return isCancelled;
    }

    public void setIsCancelled(Integer isCancelled) {
        this.isCancelled = isCancelled;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<SectionSyncInfo> getSectionItems() {
        return sectionItems;
    }

    public void setSectionItems(List<SectionSyncInfo> sectionItems) {
        this.sectionItems = sectionItems;
    }

/*    public List<CountrieSyncInfo> getCountries() {
        return countries;
    }

    public void setCountries(List<CountrieSyncInfo> countries) {
        this.countries = countries;
    }

    public List<StateSyncInfo> getStates() {
        return states;
    }

    public void setStates(List<StateSyncInfo> states) {
        this.states = states;
    }

    public List<RegionSyncInfo> getRegions() {
        return regions;
    }

    public void setRegions(List<RegionSyncInfo> regions) {
        this.regions = regions;
    }*/

    public List<StandardLangSyncInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<StandardLangSyncInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }
}
