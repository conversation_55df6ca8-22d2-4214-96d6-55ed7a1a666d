package com.sgs.otsnotes.facade.model.req.subcontract;

import com.sgs.framework.core.base.BaseProductLine;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 使分包数据失效请求
 * 用于将指定报告下的数据置为无效状态
 */
@ApiModel("使分包数据失效请求")
@Data
public class InvalidateSubcontractDataReq extends BaseProductLine {

    @ApiModelProperty(value = "旧报告编号", required = true, notes = "需要失效数据的报告编号")
    private String reportNo;

    @ApiModelProperty(value = "操作原因", notes = "失效操作的原因说明")
    private String labCode;

    @ApiModelProperty(value = "操作人", required = true)
    private String objectNo;

    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;

    private Boolean activeIndicator;

}