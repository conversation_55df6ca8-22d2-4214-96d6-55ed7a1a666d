package com.sgs.otsnotes.facade.model.enums;
/**
 * 提示类型枚举
 */
public enum TipsTypeEnum {

    /**
     * 支持TestLine,Sample等tips的提示类型
     */
    /**
     * 测试线提示
     */
    TEST_LINE("TEST_LINE", "测试线提示"),

    /**
     * 样品提示
     */
    SAMPLE("SAMPLE", "样品提示"),

    /**
     * 通用提示
     */
    COMMON("COMMON", "通用提示");
    
    /**
     * 提示类型编码
     */
    private String code;

    /**
     * 提示类型描述
     */
    private String desc;

    TipsTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
