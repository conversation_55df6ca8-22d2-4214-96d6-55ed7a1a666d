package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class ConditionSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer testConditionId;
    /**
     *
     */
    private Integer testConditionTypeId;
    /**
     *
     */
    private String testConditionType;
    /**
     *
     */
    private String testConditionName;
    /**
     *
     */
    private String testConditionDesc;
    /**
     *
     */
    private String module;
    /**
     *
     */
    private Integer isCancelled;
    /**
     *
     */
    private String status;
    /**
     *
     */
    private List<ConditionLangSyncInfo> otherLanguageItems;

    /**
     * add by vincent DIG-4833
     */
    private String testConditionValue;

    public String getTestConditionValue() {
        return testConditionValue;
    }

    public void setTestConditionValue(String testConditionValue) {
        this.testConditionValue = testConditionValue;
    }

    public Integer getTestConditionId() {
        return testConditionId;
    }

    public void setTestConditionId(Integer testConditionId) {
        this.testConditionId = testConditionId;
    }

    public Integer getTestConditionTypeId() {
        return testConditionTypeId;
    }

    public void setTestConditionTypeId(Integer testConditionTypeId) {
        this.testConditionTypeId = testConditionTypeId;
    }

    public String getTestConditionType() {
        return testConditionType;
    }

    public void setTestConditionType(String testConditionType) {
        this.testConditionType = testConditionType;
    }

    public String getTestConditionName() {
        return testConditionName;
    }

    public void setTestConditionName(String testConditionName) {
        this.testConditionName = testConditionName;
    }

    public String getTestConditionDesc() {
        return testConditionDesc;
    }

    public void setTestConditionDesc(String testConditionDesc) {
        this.testConditionDesc = testConditionDesc;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public Integer getIsCancelled() {
        return isCancelled;
    }

    public void setIsCancelled(Integer isCancelled) {
        this.isCancelled = isCancelled;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<ConditionLangSyncInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<ConditionLangSyncInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }
}
