package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

/**
 * @ClassName AnalyteSectionTypeEnums
 * @Description Analyte selection type 枚举
 * <AUTHOR>
 * @Date 1/14/2021
 */
@Dict
public enum  AnalyteSectionTypeEnums {

    None(0),
    /**
     * 必选
     * */
    Mandatory(1),
    /**
     * 可选*/
    Preselection(2);
    @DictCodeField
    private Integer type;
    AnalyteSectionTypeEnums(Integer type){
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public static boolean check(Integer type,AnalyteSectionTypeEnums ...enums){
        if(type==null){
            return false;
        }
        for (AnalyteSectionTypeEnums anEnum : enums) {
            if(anEnum.type.compareTo(type)==0){
                return true;
            }
        }

        return false;
    }
}
