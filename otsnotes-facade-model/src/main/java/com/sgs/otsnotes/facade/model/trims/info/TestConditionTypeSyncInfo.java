package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class TestConditionTypeSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Boolean testConditionTypeBlock;
    /**
     *
     */
    private Boolean testConditionTypeFixed;
    /**
     *
     */
    private Integer testConditionTypeId;
    /**
     *
     */
    private Integer testConditionTypeSeq;
    /**
     *
     */
    private List<TestConditonSyncInfo> conditions;

    public Boolean getTestConditionTypeBlock() {
        return testConditionTypeBlock;
    }

    public void setTestConditionTypeBlock(Boolean testConditionTypeBlock) {
        this.testConditionTypeBlock = testConditionTypeBlock;
    }

    public Boolean getTestConditionTypeFixed() {
        return testConditionTypeFixed;
    }

    public void setTestConditionTypeFixed(Boolean testConditionTypeFixed) {
        this.testConditionTypeFixed = testConditionTypeFixed;
    }

    public Integer getTestConditionTypeId() {
        return testConditionTypeId;
    }

    public void setTestConditionTypeId(Integer testConditionTypeId) {
        this.testConditionTypeId = testConditionTypeId;
    }

    public Integer getTestConditionTypeSeq() {
        return testConditionTypeSeq;
    }

    public void setTestConditionTypeSeq(Integer testConditionTypeSeq) {
        this.testConditionTypeSeq = testConditionTypeSeq;
    }

    public List<TestConditonSyncInfo> getConditions() {
        return conditions;
    }

    public void setConditions(List<TestConditonSyncInfo> conditions) {
        this.conditions = conditions;
    }
}
