package com.sgs.otsnotes.facade.model.enums;

import org.apache.axis.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

/**
 * <AUTHOR>
 * 默认显示qrcode的 定义枚举
 */

public enum QRCodeResponsibleTeamEnums {

    LAB_CODE_NJ("NJ SL") {
        @Override
        public boolean isContainsResponsible(String responsible) {
            return ResponsibleEnums.check(responsible,ResponsibleEnums.GB);
        }
    },
    LAB_CODE_GZ("GZ SL") {
        @Override
        public boolean isContainsResponsible(String responsible) {
            return ResponsibleEnums.check(responsible,ResponsibleEnums.GB,ResponsibleEnums.GB1,ResponsibleEnums.GBFW,ResponsibleEnums.GBFW1);
        }
    },

    LAB_CODE_QD("QD SL") {
        @Override
        public boolean isContainsResponsible(String responsible) {
            return ResponsibleEnums.check(responsible,ResponsibleEnums.GB);
        }
    },
    LAB_CODE_XM("XM SL") {
        @Override
        public boolean isContainsResponsible(String responsible) {
            return ResponsibleEnums.check(responsible,ResponsibleEnums.GB);
        }
    },
    LAB_CODE_SH("SH SL") {
        @Override
        public boolean isContainsResponsible(String responsible) {
            return ResponsibleEnums.check(responsible,ResponsibleEnums.GB_Subcontract, ResponsibleEnums.GB_SHLab);
        }
    },
    LAB_CODE_HZ("HZ SL") {
        @Override
        public boolean isContainsResponsible(String responsible) {
            return ResponsibleEnums.check(responsible,ResponsibleEnums.GB,ResponsibleEnums.GBFW,ResponsibleEnums.GBZG);
        }
    },
    LAB_CODE_CZ("CZ SL") {
        @Override
        public boolean isContainsResponsible(String responsible) {
            return ResponsibleEnums.check(responsible,ResponsibleEnums.GB);
        }
    },
    LAB_CODE_NB("NB SL") {
        @Override
        public boolean isContainsResponsible(String responsible) {
            return ResponsibleEnums.check(responsible,ResponsibleEnums.GB);
        }
    },
    LAB_CODE_TJ("TJ SL") {
        @Override
        public boolean isContainsResponsible(String responsible) {
            return ResponsibleEnums.check(responsible,ResponsibleEnums.GB);
        }
    },
    LAB_CODE_HK("HK SL") {
        @Override
        public boolean isContainsResponsible(String responsible) {
            return ResponsibleEnums.check(responsible);
        }
    };

    private String labCode;
    private boolean containsResponsible;


    QRCodeResponsibleTeamEnums(String labCode) {
        this.labCode = labCode;
    }

    public String getLabCode() {
        return labCode;
    }

    public boolean isContainsResponsible(String responsible) {
        return false;
    }

    public static final Map<String, QRCodeResponsibleTeamEnums> maps = new HashMap<String, QRCodeResponsibleTeamEnums>() {
        private static final long serialVersionUID = -1L;

        {
            for (QRCodeResponsibleTeamEnums enu : QRCodeResponsibleTeamEnums.values()) {
                put(enu.getLabCode(), enu);
            }
        }
    };


    public static boolean checkQrCode(String labCode, String responsibleCode) {
        if (StringUtils.isEmpty(labCode) || StringUtils.isEmpty(responsibleCode)) {
            return false;
        }
        if (!maps.containsKey(labCode.toUpperCase())) {
            return false;
        }
        responsibleCode = responsibleCode.toUpperCase();
        QRCodeResponsibleTeamEnums qrCodeResponsibleTeamEnums = maps.get(labCode.toUpperCase());

        return qrCodeResponsibleTeamEnums.isContainsResponsible(responsibleCode);
    }

}
