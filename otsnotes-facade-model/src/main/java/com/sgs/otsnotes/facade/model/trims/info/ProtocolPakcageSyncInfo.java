package com.sgs.otsnotes.facade.model.trims.info;

import com.alibaba.fastjson.annotation.JSONField;
import com.sgs.otsnotes.facade.model.common.PrintFriendliness;
import com.sgs.otsnotes.facade.model.trims.rsp.RegionCountryStateLocationResp;

import java.util.List;

public class ProtocolPakcageSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer versionIdentifier;
    /**
     *
     */
    private String status;
    /**
     *
     */
    private String internalDocumentRefNo;
    /**
     *
     */
    private List<Integer> specialChemicalRelationPp;
    /**
     *
     */
    private Integer testLimitGroupId;
    /**
     *
     */
    private Integer ppNumber;
    /**
     *
     */
    private Integer versionNo;
    /**
     *
     */
    private String productLineName;
    /**
     *
     */
    private Integer productLineId;
    /**
     *
     */
    private String productLineAbbr;
    /**
     *
     */
    @JSONField(name = "CSPP")
    private Integer ppType;
    /**
     *
     */
    private String ppReferenceNote;
    /**
     *
     */
    private String reportReferenceNote;
    /**
     * add by vincent 2020年12月17日 同步数据，增加pp desc
     */
    private String protocolPackageDescription;
    /**
     *
     */
    private Integer customerAccountId;
    /**
     *
     */
    private Integer artifactTypeId;
    /**
     *
     */
    private String artifactTypeName;
    /**
     *
     */
    @Deprecated
    private List<ProductTaxonomySyncInfo> productTaxonomyItems;
    /**
     *
     */
    private List<Integer> testStandardVersionIdentifiers;
    /**
     *
     */
    // 241029 trims端通知不再维护
    @Deprecated
    private List<Integer> regulationVersionIdentifiers;
    /**
     *
     */
    private List<ProtocolPakcageSectionSyncInfo> sections;
    /**
     *
     */
    private List<PpConstructSyncInfo> ppConstruct;
    /**
     *
     */
    private List<ProtocolPakcageLangSyncInfo> otherLanguageItems;

    private List<RegionCountryStateLocationResp> regionCountryStateLocation;

    public List<RegionCountryStateLocationResp> getRegionCountryStateLocation() {
        return regionCountryStateLocation;
    }

    public void setRegionCountryStateLocation(List<RegionCountryStateLocationResp> regionCountryStateLocation) {
        this.regionCountryStateLocation = regionCountryStateLocation;
    }

    public String getProtocolPackageDescription() {
        return protocolPackageDescription;
    }

    public void setProtocolPackageDescription(String protocolPackageDescription) {
        this.protocolPackageDescription = protocolPackageDescription;
    }

    public Integer getVersionIdentifier() {
        return versionIdentifier;
    }

    public void setVersionIdentifier(Integer versionIdentifier) {
        this.versionIdentifier = versionIdentifier;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getInternalDocumentRefNo() {
        return internalDocumentRefNo;
    }

    public void setInternalDocumentRefNo(String internalDocumentRefNo) {
        this.internalDocumentRefNo = internalDocumentRefNo;
    }

    public Integer getPpNumber() {
        return ppNumber;
    }

    public void setPpNumber(Integer ppNumber) {
        this.ppNumber = ppNumber;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getProductLineName() {
        return productLineName;
    }

    public void setProductLineName(String productLineName) {
        this.productLineName = productLineName;
    }

    public Integer getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }

    public String getProductLineAbbr() {
        return productLineAbbr;
    }

    public void setProductLineAbbr(String productLineAbbr) {
        this.productLineAbbr = productLineAbbr;
    }

    public Integer getPpType() {
        return ppType;
    }

    public void setPpType(Integer ppType) {
        this.ppType = ppType;
    }

    public String getPpReferenceNote() {
        return ppReferenceNote;
    }

    public void setPpReferenceNote(String ppReferenceNote) {
        this.ppReferenceNote = ppReferenceNote;
    }

    public String getReportReferenceNote() {
        return reportReferenceNote;
    }

    public void setReportReferenceNote(String reportReferenceNote) {
        this.reportReferenceNote = reportReferenceNote;
    }

    public Integer getCustomerAccountId() {
        return customerAccountId;
    }

    public void setCustomerAccountId(Integer customerAccountId) {
        this.customerAccountId = customerAccountId;
    }

    public Integer getArtifactTypeId() {
        return artifactTypeId;
    }

    public void setArtifactTypeId(Integer artifactTypeId) {
        this.artifactTypeId = artifactTypeId;
    }

    public String getArtifactTypeName() {
        return artifactTypeName;
    }

    public void setArtifactTypeName(String artifactTypeName) {
        this.artifactTypeName = artifactTypeName;
    }

    public List<ProductTaxonomySyncInfo> getProductTaxonomyItems() {
        return productTaxonomyItems;
    }

    public void setProductTaxonomyItems(List<ProductTaxonomySyncInfo> productTaxonomyItems) {
        this.productTaxonomyItems = productTaxonomyItems;
    }

    public List<Integer> getTestStandardVersionIdentifiers() {
        return testStandardVersionIdentifiers;
    }

    public void setTestStandardVersionIdentifiers(List<Integer> testStandardVersionIdentifiers) {
        this.testStandardVersionIdentifiers = testStandardVersionIdentifiers;
    }

    public List<Integer> getRegulationVersionIdentifiers() {
        return regulationVersionIdentifiers;
    }

    public void setRegulationVersionIdentifiers(List<Integer> regulationVersionIdentifiers) {
        this.regulationVersionIdentifiers = regulationVersionIdentifiers;
    }

    public List<ProtocolPakcageSectionSyncInfo> getSections() {
        return sections;
    }

    public void setSections(List<ProtocolPakcageSectionSyncInfo> sections) {
        this.sections = sections;
    }

    public List<PpConstructSyncInfo> getPpConstruct() {
        return ppConstruct;
    }

    public void setPpConstruct(List<PpConstructSyncInfo> ppConstruct) {
        this.ppConstruct = ppConstruct;
    }

    public List<ProtocolPakcageLangSyncInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<ProtocolPakcageLangSyncInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }

    public Integer getTestLimitGroupId() {
        return testLimitGroupId;
    }

    public void setTestLimitGroupId(Integer testLimitGroupId) {
        this.testLimitGroupId = testLimitGroupId;
    }

    public List<Integer> getSpecialChemicalRelationPp() {
        return specialChemicalRelationPp;
    }

    public void setSpecialChemicalRelationPp(List<Integer> specialChemicalRelationPp) {
        this.specialChemicalRelationPp = specialChemicalRelationPp;
    }
}
