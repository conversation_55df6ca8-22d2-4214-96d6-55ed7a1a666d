/**
 * CommandResult.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class CommandResult  implements java.io.Serializable {
    private CommandTypeEnum commandType;

    private Object commandResultData;

    public CommandResult() {
    }

    public CommandResult(
           CommandTypeEnum commandType,
           Object commandResultData) {
           this.commandType = commandType;
           this.commandResultData = commandResultData;
    }


    /**
     * Gets the commandType value for this CommandResult.
     * 
     * @return commandType
     */
    public CommandTypeEnum getCommandType() {
        return commandType;
    }


    /**
     * Sets the commandType value for this CommandResult.
     * 
     * @param commandType
     */
    public void setCommandType(CommandTypeEnum commandType) {
        this.commandType = commandType;
    }


    /**
     * Gets the commandResultData value for this CommandResult.
     * 
     * @return commandResultData
     */
    public Object getCommandResultData() {
        return commandResultData;
    }


    /**
     * Sets the commandResultData value for this CommandResult.
     * 
     * @param commandResultData
     */
    public void setCommandResultData(Object commandResultData) {
        this.commandResultData = commandResultData;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof CommandResult)) {
            return false;
        }
        CommandResult other = (CommandResult) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.commandType==null && other.getCommandType()==null) || 
             (this.commandType!=null &&
              this.commandType.equals(other.getCommandType()))) &&
            ((this.commandResultData==null && other.getCommandResultData()==null) || 
             (this.commandResultData!=null &&
              this.commandResultData.equals(other.getCommandResultData())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getCommandType() != null) {
            _hashCode += getCommandType().hashCode();
        }
        if (getCommandResultData() != null) {
            _hashCode += getCommandResultData().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(CommandResult.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "CommandResult"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("commandType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CommandType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "CommandTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("commandResultData");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CommandResultData"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "anyType"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
