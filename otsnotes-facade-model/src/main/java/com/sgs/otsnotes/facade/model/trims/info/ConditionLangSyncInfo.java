package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class ConditionLangSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer languageId;
    /**
     *
     */
    private String multiTestConditionTypeName;
    /**
     *
     */
    private String multiTestConditionName;
    /**
     *
     */
    private String multiTestConditionDesc;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getMultiTestConditionTypeName() {
        return multiTestConditionTypeName;
    }

    public void setMultiTestConditionTypeName(String multiTestConditionTypeName) {
        this.multiTestConditionTypeName = multiTestConditionTypeName;
    }

    public String getMultiTestConditionName() {
        return multiTestConditionName;
    }

    public void setMultiTestConditionName(String multiTestConditionName) {
        this.multiTestConditionName = multiTestConditionName;
    }

    public String getMultiTestConditionDesc() {
        return multiTestConditionDesc;
    }

    public void setMultiTestConditionDesc(String multiTestConditionDesc) {
        this.multiTestConditionDesc = multiTestConditionDesc;
    }
}
