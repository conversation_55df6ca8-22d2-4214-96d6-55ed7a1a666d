package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

/**
 * <AUTHOR> jiling<PERSON>
 * @version V1.0
 * @Project: sgs-otsnotes
 * @Package com.sgs.otsnotes.facade.model.enums
 * @Description: Watermark的状态枚举
 * @date Date : 2020年01月20日
 */
public enum Watermark {
	SELFREFERENCE(101, "SELF REFERENCE");
	private int code;
	private String watermark;

	Watermark(int code, String watermark) {
		this.code = code;
		this.watermark = watermark;
	}

	public String getWatermark() {
		return watermark;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public static Map<Integer, Watermark> getMaps() {
		return maps;
	}

	public static final Map<Integer, Watermark> maps = new HashMap<Integer, Watermark>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (Watermark enu : Watermark.values()) {
				put(enu.getCode(), enu);
			}
		}
	};

	public static String getWatermarkText(Integer code) {
		Watermark wm = findCode(code);
		return wm == null ? null : wm.getWatermark();
	}

	public static Watermark findCode(Integer code) {
		return maps.get(code);
	}

	/**
	 *
	 * @param code
	 * @param watermark
	 * @return
	 */
	public static boolean check(Integer code, Watermark... watermark) {
		if (code == null || !maps.containsKey(code.intValue()) || watermark == null || watermark.length <= 0){
			return false;
		}
		for (Watermark tlWatermark: watermark){
			if (code.intValue() == tlWatermark.getCode()){
				return true;
			}
		}
		return false;
	}

	/**
	 *
	 * @param watermark
	 * @return
	 */
	public boolean check(Watermark... watermark){
		if (watermark == null || watermark.length <= 0){
			return false;
		}
		for (Watermark watermarkItem: watermark){
			if (this.getCode() == watermarkItem.getCode()){
				return true;
			}
		}
		return false;
	}
}
