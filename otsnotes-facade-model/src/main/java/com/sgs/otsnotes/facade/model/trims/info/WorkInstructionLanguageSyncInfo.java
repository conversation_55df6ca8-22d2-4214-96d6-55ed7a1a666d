package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

/**
 * @Author: Joke.wang
 * @Date: 2022/2/22 13:26
 */
public class WorkInstructionLanguageSyncInfo extends PrintFriendliness {

    private static final long serialVersionUID = 4290104970501651534L;
    private Integer languageId;
    private String workingInstructionName;
    private String workingInstructionShortDesc;
    private String workingInstructionText;
    private String workingInstructionCategoryName;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getWorkingInstructionName() {
        return workingInstructionName;
    }

    public void setWorkingInstructionName(String workingInstructionName) {
        this.workingInstructionName = workingInstructionName;
    }

    public String getWorkingInstructionShortDesc() {
        return workingInstructionShortDesc;
    }

    public void setWorkingInstructionShortDesc(String workingInstructionShortDesc) {
        this.workingInstructionShortDesc = workingInstructionShortDesc;
    }

    public String getWorkingInstructionText() {
        return workingInstructionText;
    }

    public void setWorkingInstructionText(String workingInstructionText) {
        this.workingInstructionText = workingInstructionText;
    }

    public String getWorkingInstructionCategoryName() {
        return workingInstructionCategoryName;
    }

    public void setWorkingInstructionCategoryName(String workingInstructionCategoryName) {
        this.workingInstructionCategoryName = workingInstructionCategoryName;
    }
}
