package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

/**
 * {@link com.sgs.framework.model.enums.ReportStatus}
 */
@Deprecated
public enum ReportStatus {
    New(201, "New"),
    Cancelled(202, "Cancelled"),
    Approved(203, "Approved"),
    Draft(204, "Draft"),
    Reworked(205, "Reworked"),
    Combined(206, "Combined"),
    Replaced(207, "Replaced"),
    Completed(208, "Completed");

    private int code;
    private String message;

    ReportStatus(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, ReportStatus> maps = new HashMap<Integer, ReportStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ReportStatus reportStatus : ReportStatus.values()) {
                put(reportStatus.getCode(), reportStatus);
            }
        }
    };

    public static ReportStatus getCode(Integer code) {
        if (code == null || !maps.containsKey(code)){
            return null;
        }
        return maps.get(code);
    }

    public boolean check(ReportStatus... reportStatus){
        if (reportStatus == null || reportStatus.length <= 0){
            return false;
        }
        for (ReportStatus status: reportStatus){
            if (this.getCode() == status.getCode()){
                return true;
            }
        }
        return false;
    }

    public static boolean checkStatus(ReportStatus status, ReportStatus... reportStatus){
        if (status == null || reportStatus == null || reportStatus.length <= 0) {
            return false;
        }
        return check(status.getCode(), reportStatus);
    }

    public static boolean check(Integer status, ReportStatus... reportStatus){
        if (status == null || !maps.containsKey(status) || reportStatus == null || reportStatus.length <= 0) {
            return false;
        }
        for (ReportStatus reportStatu: reportStatus){
            if (status != null && status == reportStatu.getCode()){
                return true;
            }
        }
        return false;
    }
}
