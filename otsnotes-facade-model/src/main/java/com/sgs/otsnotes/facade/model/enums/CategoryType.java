package com.sgs.otsnotes.facade.model.enums;


import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;
import org.springframework.util.StringUtils;

/**
 * @Author: mingyang.chen
 * @Date: 2021/1/15 17:52
 */
@Dict
public enum CategoryType {
    Original("Original","O"),<PERSON><PERSON>("Chem","C"),<PERSON><PERSON>("Phy","P")
    ;
    @DictLabelField
    private final String name;
    @DictCodeField
    private final String code;

    CategoryType(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public static boolean check(String code,CategoryType ... types){
        if(StringUtils.isEmpty(code)){
            return false;
        }
        for (CategoryType type : types) {
            if(type.code.equalsIgnoreCase(code)){
                return true;
            }
        }
        return false;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

}
