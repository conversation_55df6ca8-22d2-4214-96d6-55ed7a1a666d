package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class TestLineLangSyncInfo extends PrintFriendliness{
    /**
     *
     */
    private Integer languageId;
    /**
     *
     */
    private Integer versionIdentifier;
    /**
     *
     */
    private String reportReferenceNote;
    /**
     *
     */
    private String testLineEvaluation;

    private String testItemName;
    /**
     *
     */
    private String conditionInstructions;
    /**
     *
     */
    private String ppNotes;
    /**
     *
     */
    private List<TestStandardLangSyncInfo> testStandardItems;
    /**
     *
     */
    private List<TestRegulationLangSyncInfo> regulationItems;
    /**
     *
     */
    private List<TestLineCustomerAppLangSyncInfo> customersApplicabilityItems;
    /**
     *
     */
    private List<TestLineCustomerAppLangSyncInfo> workInstructionItems;
    /**
     *
     */
    private List<TestLineAnalyteLangSyncInfo> testAnalyteItems;


    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public Integer getVersionIdentifier() {
        return versionIdentifier;
    }

    public void setVersionIdentifier(Integer versionIdentifier) {
        this.versionIdentifier = versionIdentifier;
    }

    public String getReportReferenceNote() {
        return reportReferenceNote;
    }

    public void setReportReferenceNote(String reportReferenceNote) {
        this.reportReferenceNote = reportReferenceNote;
    }

    public String getTestLineEvaluation() {
        return testLineEvaluation;
    }

    public void setTestLineEvaluation(String testLineEvaluation) {
        this.testLineEvaluation = testLineEvaluation;
    }

    public String getTestItemName() {
        return testItemName;
    }

    public void setTestItemName(String testItemName) {
        this.testItemName = testItemName;
    }

    public String getConditionInstructions() {
        return conditionInstructions;
    }

    public void setConditionInstructions(String conditionInstructions) {
        this.conditionInstructions = conditionInstructions;
    }

    public String getPpNotes() {
        return ppNotes;
    }

    public void setPpNotes(String ppNotes) {
        this.ppNotes = ppNotes;
    }

    public List<TestStandardLangSyncInfo> getTestStandardItems() {
        return testStandardItems;
    }

    public void setTestStandardItems(List<TestStandardLangSyncInfo> testStandardItems) {
        this.testStandardItems = testStandardItems;
    }

    public List<TestRegulationLangSyncInfo> getRegulationItems() {
        return regulationItems;
    }

    public void setRegulationItems(List<TestRegulationLangSyncInfo> regulationItems) {
        this.regulationItems = regulationItems;
    }

    public List<TestLineCustomerAppLangSyncInfo> getCustomersApplicabilityItems() {
        return customersApplicabilityItems;
    }

    public void setCustomersApplicabilityItems(List<TestLineCustomerAppLangSyncInfo> customersApplicabilityItems) {
        this.customersApplicabilityItems = customersApplicabilityItems;
    }

    public List<TestLineCustomerAppLangSyncInfo> getWorkInstructionItems() {
        return workInstructionItems;
    }

    public void setWorkInstructionItems(List<TestLineCustomerAppLangSyncInfo> workInstructionItems) {
        this.workInstructionItems = workInstructionItems;
    }

    public List<TestLineAnalyteLangSyncInfo> getTestAnalyteItems() {
        return testAnalyteItems;
    }

    public void setTestAnalyteItems(List<TestLineAnalyteLangSyncInfo> testAnalyteItems) {
        this.testAnalyteItems = testAnalyteItems;
    }
}
