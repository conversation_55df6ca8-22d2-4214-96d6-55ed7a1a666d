package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

/**
 * <AUTHOR> joke.wang
 * @date : 2022-01-13 17:37
 */
@Dict
public enum BomTestLineStatus {
    NEW(1, "New"),
    TESTING(2, "Testing"),
    COMPLETED(3, "Completed");
    @DictCodeField
    private int code;
    @DictLabelField
    private String message;

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    BomTestLineStatus(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
