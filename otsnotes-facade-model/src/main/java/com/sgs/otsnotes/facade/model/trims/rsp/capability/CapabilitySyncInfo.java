package com.sgs.otsnotes.facade.model.trims.rsp.capability;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;
import lombok.Data;

import java.util.List;

@Data
public class CapabilitySyncInfo extends PrintFriendliness {

    /**
     * TestLineVersionId+BU的唯一键，暂时对于应用没什么作用，加这个只是想关后续数据排查比较可能有用
     */
    private Integer caId;

    /**
     * trims端主键ID,相当于之前的 AccreditationDetailId
     */
    private Integer capId;

    /**
     * 1代表数据已经删除，以下内容不会输出
     */
    private Integer isDeleted;

    private Integer tlVersionIdentifier;

    /**
     * 暂时不使用
     */
    private Integer ppVersionIdentifier;

    private Integer productLineId;

    private Integer laboratoryId;

    private List<Integer> laboratorySectionIds;

    private String performCapability;

    private String remark;

    // 暂不处理
    private List<CapabilitySyncLangInfo> otherLanguageItems;

}
