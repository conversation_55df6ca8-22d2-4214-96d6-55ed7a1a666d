package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/3 10:32
 */
public class TestLimitOpreatorInfo extends PrintFriendliness {

     private String testLimitOperatorDepiction;
     private Integer testLimitOperandCount;
     private String testLimitOperatorName;
     private Integer testLimitOperatorId;

    public String getTestLimitOperatorDepiction() {
        return testLimitOperatorDepiction;
    }

    public void setTestLimitOperatorDepiction(String testLimitOperatorDepiction) {
        this.testLimitOperatorDepiction = testLimitOperatorDepiction;
    }

    public Integer getTestLimitOperandCount() {
        return testLimitOperandCount;
    }

    public void setTestLimitOperandCount(Integer testLimitOperandCount) {
        this.testLimitOperandCount = testLimitOperandCount;
    }

    public String getTestLimitOperatorName() {
        return testLimitOperatorName;
    }

    public void setTestLimitOperatorName(String testLimitOperatorName) {
        this.testLimitOperatorName = testLimitOperatorName;
    }

    public Integer getTestLimitOperatorId() {
        return testLimitOperatorId;
    }

    public void setTestLimitOperatorId(Integer testLimitOperatorId) {
        this.testLimitOperatorId = testLimitOperatorId;
    }
}
