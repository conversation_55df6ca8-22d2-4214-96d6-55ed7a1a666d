/**
 * ListBoxField.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ListBoxField  implements java.io.Serializable {
    private ListBoxDatasourceType dataSourceType;

    private boolean isMultiSelect;

    private String multiSeparator;

    private String dataSource;

    public ListBoxField() {
    }

    public ListBoxField(
           ListBoxDatasourceType dataSourceType,
           boolean isMultiSelect,
           String multiSeparator,
           String dataSource) {
           this.dataSourceType = dataSourceType;
           this.isMultiSelect = isMultiSelect;
           this.multiSeparator = multiSeparator;
           this.dataSource = dataSource;
    }


    /**
     * Gets the dataSourceType value for this ListBoxField.
     * 
     * @return dataSourceType
     */
    public ListBoxDatasourceType getDataSourceType() {
        return dataSourceType;
    }


    /**
     * Sets the dataSourceType value for this ListBoxField.
     * 
     * @param dataSourceType
     */
    public void setDataSourceType(ListBoxDatasourceType dataSourceType) {
        this.dataSourceType = dataSourceType;
    }


    /**
     * Gets the isMultiSelect value for this ListBoxField.
     * 
     * @return isMultiSelect
     */
    public boolean isIsMultiSelect() {
        return isMultiSelect;
    }


    /**
     * Sets the isMultiSelect value for this ListBoxField.
     * 
     * @param isMultiSelect
     */
    public void setIsMultiSelect(boolean isMultiSelect) {
        this.isMultiSelect = isMultiSelect;
    }


    /**
     * Gets the multiSeparator value for this ListBoxField.
     * 
     * @return multiSeparator
     */
    public String getMultiSeparator() {
        return multiSeparator;
    }


    /**
     * Sets the multiSeparator value for this ListBoxField.
     * 
     * @param multiSeparator
     */
    public void setMultiSeparator(String multiSeparator) {
        this.multiSeparator = multiSeparator;
    }


    /**
     * Gets the dataSource value for this ListBoxField.
     * 
     * @return dataSource
     */
    public String getDataSource() {
        return dataSource;
    }


    /**
     * Sets the dataSource value for this ListBoxField.
     * 
     * @param dataSource
     */
    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ListBoxField)) {
            return false;
        }
        ListBoxField other = (ListBoxField) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.dataSourceType==null && other.getDataSourceType()==null) || 
             (this.dataSourceType!=null &&
              this.dataSourceType.equals(other.getDataSourceType()))) &&
            this.isMultiSelect == other.isIsMultiSelect() &&
            ((this.multiSeparator==null && other.getMultiSeparator()==null) || 
             (this.multiSeparator!=null &&
              this.multiSeparator.equals(other.getMultiSeparator()))) &&
            ((this.dataSource==null && other.getDataSource()==null) || 
             (this.dataSource!=null &&
              this.dataSource.equals(other.getDataSource())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getDataSourceType() != null) {
            _hashCode += getDataSourceType().hashCode();
        }
        _hashCode += (isIsMultiSelect() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        if (getMultiSeparator() != null) {
            _hashCode += getMultiSeparator().hashCode();
        }
        if (getDataSource() != null) {
            _hashCode += getDataSource().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ListBoxField.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ListBoxField"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("dataSourceType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataSourceType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ListBoxDatasourceType"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isMultiSelect");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsMultiSelect"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("multiSeparator");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "MultiSeparator"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("dataSource");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataSource"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
