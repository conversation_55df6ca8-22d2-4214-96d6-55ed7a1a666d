/**
 * UploadTemplate.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class UploadTemplate  implements java.io.Serializable {
    private int templateId;

    private String templateFilePath;

    private String templateConfigFilePath;

    private int templateType;

    public UploadTemplate() {
    }

    public UploadTemplate(
           int templateId,
           String templateFilePath,
           String templateConfigFilePath,
           int templateType) {
           this.templateId = templateId;
           this.templateFilePath = templateFilePath;
           this.templateConfigFilePath = templateConfigFilePath;
           this.templateType = templateType;
    }


    /**
     * Gets the templateId value for this UploadTemplate.
     * 
     * @return templateId
     */
    public int getTemplateId() {
        return templateId;
    }


    /**
     * Sets the templateId value for this UploadTemplate.
     * 
     * @param templateId
     */
    public void setTemplateId(int templateId) {
        this.templateId = templateId;
    }


    /**
     * Gets the templateFilePath value for this UploadTemplate.
     * 
     * @return templateFilePath
     */
    public String getTemplateFilePath() {
        return templateFilePath;
    }


    /**
     * Sets the templateFilePath value for this UploadTemplate.
     * 
     * @param templateFilePath
     */
    public void setTemplateFilePath(String templateFilePath) {
        this.templateFilePath = templateFilePath;
    }


    /**
     * Gets the templateConfigFilePath value for this UploadTemplate.
     * 
     * @return templateConfigFilePath
     */
    public String getTemplateConfigFilePath() {
        return templateConfigFilePath;
    }


    /**
     * Sets the templateConfigFilePath value for this UploadTemplate.
     * 
     * @param templateConfigFilePath
     */
    public void setTemplateConfigFilePath(String templateConfigFilePath) {
        this.templateConfigFilePath = templateConfigFilePath;
    }


    /**
     * Gets the templateType value for this UploadTemplate.
     * 
     * @return templateType
     */
    public int getTemplateType() {
        return templateType;
    }


    /**
     * Sets the templateType value for this UploadTemplate.
     * 
     * @param templateType
     */
    public void setTemplateType(int templateType) {
        this.templateType = templateType;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof UploadTemplate)) {
            return false;
        }
        UploadTemplate other = (UploadTemplate) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.templateId == other.getTemplateId() &&
            ((this.templateFilePath==null && other.getTemplateFilePath()==null) || 
             (this.templateFilePath!=null &&
              this.templateFilePath.equals(other.getTemplateFilePath()))) &&
            ((this.templateConfigFilePath==null && other.getTemplateConfigFilePath()==null) || 
             (this.templateConfigFilePath!=null &&
              this.templateConfigFilePath.equals(other.getTemplateConfigFilePath()))) &&
            this.templateType == other.getTemplateType();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += getTemplateId();
        if (getTemplateFilePath() != null) {
            _hashCode += getTemplateFilePath().hashCode();
        }
        if (getTemplateConfigFilePath() != null) {
            _hashCode += getTemplateConfigFilePath().hashCode();
        }
        _hashCode += getTemplateType();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(UploadTemplate.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">UploadTemplate"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "templateId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "templateFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateConfigFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "templateConfigFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "templateType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
