package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

/**
 * <AUTHOR> vincent
 * @version V1.0
 * @Project: sgs-otsnotes
 * @Package com.sgs.otsnotes.facade.model.enums
 * @Description: TestLine的状态枚举
 * @date Date : 2019年06月14日 15:03
 */
public enum TestLineStatus {
	Typing(701, "Typing"),
	Submit(702, "Submitted"),
	Completed(703, "Completed"),
	SubContracted(704, "Subcontracted"),
	Entered(705, "Entered"),
	Cancelled(706, "Cancelled"),
	// Not Test、NC
	NC(707, "Not Test"),
	DR(708, "Document Review"),
	NA(709, "NA");

	private int status;
	private String message;

	TestLineStatus(int status, String message) {
		this.status = status;
		this.message = message;
	}

	public String getMessage() {
		return message;
	}

	public int getStatus() {
		return status;
	}

	public static final Map<Integer, TestLineStatus> maps = new HashMap<Integer, TestLineStatus>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (TestLineStatus enu : TestLineStatus.values()) {
				put(enu.getStatus(), enu);
			}
		}
	};

	public static String getMessage(Integer status) {
		if (status == null || !maps.containsKey(status)) {
			return null;
		}
		return maps.get(status).getMessage();
	}

	public static TestLineStatus findStatus(Integer status) {
		if (status == null || !maps.containsKey(status)) {
			return null;
		}
		return maps.get(status);
	}

	/**
	 *
	 * @param status
	 * @param testLineStatus
	 * @return
	 */
	public static boolean check(Integer status, TestLineStatus... testLineStatus) {
		if (status == null || !maps.containsKey(status) || testLineStatus == null || testLineStatus.length <= 0){
			return false;
		}
		for (TestLineStatus tlStatus: testLineStatus){
			if (status != null && status == tlStatus.getStatus()){
				return true;
			}
		}
		return false;
	}

	/**
	 *
	 * @param testLineStatus
	 * @return
	 */
	public boolean check(TestLineStatus... testLineStatus){
		if (testLineStatus == null || testLineStatus.length <= 0){
			return false;
		}
		for (TestLineStatus status: testLineStatus){
			if (this.getStatus() == status.getStatus()){
				return true;
			}
		}
		return false;
	}
}
