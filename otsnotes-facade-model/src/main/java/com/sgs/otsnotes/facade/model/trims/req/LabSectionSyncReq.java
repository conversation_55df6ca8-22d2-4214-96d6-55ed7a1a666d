package com.sgs.otsnotes.facade.model.trims.req;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class LabSectionSyncReq extends PrintFriendliness {
    /**
     *
     */
    public LabSectionSyncReq(){
        this.caller = 1;
        this.correlationId = 2016;
    }
    /**
     *
     */
    private Integer caller;
    /**
     *
     */
    private Integer correlationId;

    public Integer getCaller() {
        return caller;
    }

    public void setCaller(Integer caller) {
        this.caller = caller;
    }

    public Integer getCorrelationId() {
        return correlationId;
    }

    public void setCorrelationId(Integer correlationId) {
        this.correlationId = correlationId;
    }
}
