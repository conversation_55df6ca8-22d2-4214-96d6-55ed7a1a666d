package com.sgs.otsnotes.facade.model.trims.req;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseReq;

import java.util.List;

public class PpSgsMartSyncReq extends TrimsSyncBaseReq {
    /**
     *
     */
    public PpSgsMartSyncReq(){
        this.setCaller("SgsMart");
        this.setSecurityCode("DE3F385611E41");
    }

    /**
     *
     */
    private List<Long> ids;

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }
}
