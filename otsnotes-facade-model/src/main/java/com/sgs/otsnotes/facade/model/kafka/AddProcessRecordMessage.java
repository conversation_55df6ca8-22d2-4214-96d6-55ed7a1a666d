package com.sgs.otsnotes.facade.model.kafka;


import java.io.Serializable;
import java.util.List;


public class AddProcessRecordMessage implements Serializable{
	
	private static final long serialVersionUID = -4916194186308729001L;

	private List<String> paperConsolidationOrderNos;//纸质结果整合
	
	private List<CopyDeliveredMessage> copyDeliveredMessages;//softCopydeliver HardCopyDeliver 
	
	private List<String> updateOrderStatusOrderNos;//preorder更新订单状态信息
	
	private List<UpdateJobMessage> updateJobMessages;//updateJobStatus

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public List<String> getPaperConsolidationOrderNos() {
		return paperConsolidationOrderNos;
	}

	public void setPaperConsolidationOrderNos(List<String> paperConsolidationOrderNos) {
		this.paperConsolidationOrderNos = paperConsolidationOrderNos;
	}

	public List<CopyDeliveredMessage> getCopyDeliveredMessages() {
		return copyDeliveredMessages;
	}

	public void setCopyDeliveredMessages(List<CopyDeliveredMessage> copyDeliveredMessages) {
		this.copyDeliveredMessages = copyDeliveredMessages;
	}

	public List<String> getUpdateOrderStatusOrderNos() {
		return updateOrderStatusOrderNos;
	}

	public void setUpdateOrderStatusOrderNos(List<String> updateOrderStatusOrderNos) {
		this.updateOrderStatusOrderNos = updateOrderStatusOrderNos;
	}

	public List<UpdateJobMessage> getUpdateJobMessages() {
		return updateJobMessages;
	}

	public void setUpdateJobMessages(List<UpdateJobMessage> updateJobMessages) {
		this.updateJobMessages = updateJobMessages;
	}


}
