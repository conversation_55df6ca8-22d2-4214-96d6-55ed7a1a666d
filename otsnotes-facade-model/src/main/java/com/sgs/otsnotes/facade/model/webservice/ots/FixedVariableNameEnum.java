/**
 * FixedVariableNameEnum.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class FixedVariableNameEnum implements java.io.Serializable {
    private String _value_;
    private static java.util.HashMap _table_ = new java.util.HashMap();

    // Constructor
    protected FixedVariableNameEnum(String value) {
        _value_ = value;
        _table_.put(_value_,this);
    }

    public static final String _NA = "NA";
    public static final String _ID = "ID";
    public static final String _Name = "Name";
    public static final String _Description = "Description";
    public static final String _TestItemName = "TestItemName";
    public static final String _TestCondition = "TestCondition";
    public static final String _TestMethodName = "TestMethodName";
    public static final String _EquipmentNo = "EquipmentNo";
    public static final String _WashProcedure = "WashProcedure";
    public static final String _ComponentType = "ComponentType";
    public static final String _ComponentNoForToyA = "ComponentNoForToyA";
    public static final String _TaskAliasNoForToyA = "TaskAliasNoForToyA";
    public static final String _ProductType = "ProductType";
    public static final String _CASNo = "CASNo";
    public static final String _ChemicalElementName = "ChemicalElementName";
    public static final String _WashCycleName = "WashCycleName";
    public static final FixedVariableNameEnum NA = new FixedVariableNameEnum(_NA);
    public static final FixedVariableNameEnum ID = new FixedVariableNameEnum(_ID);
    public static final FixedVariableNameEnum Name = new FixedVariableNameEnum(_Name);
    public static final FixedVariableNameEnum Description = new FixedVariableNameEnum(_Description);
    public static final FixedVariableNameEnum TestItemName = new FixedVariableNameEnum(_TestItemName);
    public static final FixedVariableNameEnum TestCondition = new FixedVariableNameEnum(_TestCondition);
    public static final FixedVariableNameEnum TestMethodName = new FixedVariableNameEnum(_TestMethodName);
    public static final FixedVariableNameEnum EquipmentNo = new FixedVariableNameEnum(_EquipmentNo);
    public static final FixedVariableNameEnum WashProcedure = new FixedVariableNameEnum(_WashProcedure);
    public static final FixedVariableNameEnum ComponentType = new FixedVariableNameEnum(_ComponentType);
    public static final FixedVariableNameEnum ComponentNoForToyA = new FixedVariableNameEnum(_ComponentNoForToyA);
    public static final FixedVariableNameEnum TaskAliasNoForToyA = new FixedVariableNameEnum(_TaskAliasNoForToyA);
    public static final FixedVariableNameEnum ProductType = new FixedVariableNameEnum(_ProductType);
    public static final FixedVariableNameEnum CASNo = new FixedVariableNameEnum(_CASNo);
    public static final FixedVariableNameEnum ChemicalElementName = new FixedVariableNameEnum(_ChemicalElementName);
    public static final FixedVariableNameEnum WashCycleName = new FixedVariableNameEnum(_WashCycleName);
    public String getValue() { return _value_;}
    public static FixedVariableNameEnum fromValue(String value)
          throws IllegalArgumentException {
        FixedVariableNameEnum enumeration = (FixedVariableNameEnum)
            _table_.get(value);
        if (enumeration==null) {
            throw new IllegalArgumentException();
        }
        return enumeration;
    }
    public static FixedVariableNameEnum fromString(String value)
          throws IllegalArgumentException {
        return fromValue(value);
    }
    public boolean equals(Object obj) {return (obj == this);}
    public int hashCode() { return toString().hashCode();}
    public String toString() { return _value_;}
    public Object readResolve() throws java.io.ObjectStreamException { return fromValue(_value_);}
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumSerializer(
            _javaType, _xmlType);
    }
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumDeserializer(
            _javaType, _xmlType);
    }
    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(FixedVariableNameEnum.class);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FixedVariableNameEnum"));
    }
    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

}
