package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.ApplicationFactorSyncInfo;

import java.util.List;

public class ApplicationFactorSyncRsp extends TrimsSyncBaseRsp {

    private List<ApplicationFactorSyncInfo> data;

    public List<ApplicationFactorSyncInfo> getData() {
        return data;
    }

    public void setData(List<ApplicationFactorSyncInfo> data) {
        this.data = data;
    }
}
