package com.sgs.otsnotes.facade.model.enums;

/**
 * <AUTHOR>
 */


public enum DigitalAppIDEnums {
    SL(5),
    ;
    private Integer appId;

    DigitalAppIDEnums(Integer appId) {
        this.appId = appId;
    }

    public Integer getAppId() {
        return appId;
    }

    public static boolean check(Integer appId, DigitalAppIDEnums... enums) {
        if (appId == null || enums == null || enums.length == 0) {
            return false;
        }
        for (DigitalAppIDEnums anEnum : enums) {
            if (anEnum.appId.compareTo(appId) == 0) {
                return true;
            }
        }
        return false;
    }
}
