package com.sgs.otsnotes.facade.model.trims.info;

/**
 *
 */
public final class DataMarkerSyncResInfo extends BaseSyncInfo{
    /**
     *
     */
    private long id;

    /**
     * 数据（关系）表主键
     */
    private Long dataMarkerId;

    /**
     * 数据主键
     */
    private Long itemId;

    /**
     * 数据类型（TestLine、ProtocolPackage）
     */
    private String itemType;

    /**
     * 1代表有效数据，0代表无效数据
     */
    private int status;

    /**
     * 标签ID
     */
    private Long markerId;

    /**
     * 标签名称
     */
    private String markerName;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Long getDataMarkerId() {
        return dataMarkerId;
    }

    public void setDataMarkerId(Long dataMarkerId) {
        this.dataMarkerId = dataMarkerId;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Long getMarkerId() {
        return markerId;
    }

    public void setMarkerId(Long markerId) {
        this.markerId = markerId;
    }

    public String getMarkerName() {
        return markerName;
    }

    public void setMarkerName(String markerName) {
        this.markerName = markerName;
    }
}
