package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.DataMarkerSyncResInfo;

import java.util.List;

public class DataMarkerSyncRsp extends TrimsSyncBaseRsp {
    /**
     *
     */
    private List<DataMarkerSyncResInfo> data;

    public List<DataMarkerSyncResInfo> getData() {
        return data;
    }

    public void setData(List<DataMarkerSyncResInfo> data) {
        this.data = data;
    }
}
