package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.reportdata.NotesTestResultDTO;

import java.util.Comparator;

public class NotesTestResultComparator implements Comparator<NotesTestResultDTO> {
    /**
     * 是否为升序
     */
    private boolean isAsc;

    /**
     * @param isAsc
     */
    public NotesTestResultComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * upSpecimenNo, SpecimenNo, TestConditionSeq, AnalyteSeq, PositionSeq
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(NotesTestResultDTO o1, NotesTestResultDTO o2) {
        //
        this.resetNull(o1);
        //
        this.resetNull(o2);

        int index = Integer.compare(o1.getUpSpecimenNo(), o2.getUpSpecimenNo());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        index = Integer.compare(o1.getSpecimenNo(), o2.getSpecimenNo());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

//        index = Integer.compare(o1.get(), o2.getConditionParentSeq());
//        if (index < 0) {
//            return isAsc ? -1 : 1;
//        }
//        if (index > 0) {
//            return isAsc ? 1 : -1;
//        }


        index = Integer.compare(o1.getConditionParentSeq(), o2.getConditionParentSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        index = Integer.compare(o1.getConditionSeq(), o2.getConditionSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }


        index = Integer.compare(o1.getAnalyteSeq(), o2.getAnalyteSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        index = Integer.compare(o1.getPositionSeq(), o2.getPositionSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }

    /**
     * @param td
     */
    private void resetNull(NotesTestResultDTO td) {
        final Integer empty = 0;
        if (td.getUpSpecimenNo() == null) {
            td.setUpSpecimenNo(empty);
        }
        if (td.getSpecimenNo() == null) {
            td.setSpecimenNo(empty);
        }
        if (td.getConditionSeq() == null) {
            td.setConditionSeq(0);
        }
        if (td.getConditionParentSeq() == null) {
            td.setConditionParentSeq(0);
        }
        if (td.getAnalyteSeq() == null) {
            td.setAnalyteSeq(0);
        }
        if (td.getPositionSeq() == null) {
            td.setPositionSeq(0);
        }

    }
}
