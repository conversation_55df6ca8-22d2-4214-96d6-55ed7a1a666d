package com.sgs.otsnotes.facade.model.trims.info;

import com.alibaba.fastjson.annotation.JSONField;
import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class PpSgsMartInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer id;
    /**
     *
     */
    @JSONField(name = "versionIdentifier")
    private Integer ppVersionId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getPpVersionId() {
        return ppVersionId;
    }

    public void setPpVersionId(Integer ppVersionId) {
        this.ppVersionId = ppVersionId;
    }
}
