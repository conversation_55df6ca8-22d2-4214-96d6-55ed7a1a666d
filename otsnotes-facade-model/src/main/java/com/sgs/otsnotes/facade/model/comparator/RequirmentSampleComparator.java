package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.info.limitgroup.RequirmentSampleInfo;

import java.util.Comparator;

public class RequirmentSampleComparator implements Comparator<RequirmentSampleInfo> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public RequirmentSampleComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(RequirmentSampleInfo o1, RequirmentSampleInfo o2) {
        int index = Integer.compare(o1.getSampleSeq(), o2.getSampleSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
