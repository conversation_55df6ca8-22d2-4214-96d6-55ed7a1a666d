package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

/**
 * 对应OrderSubcontractRel中的type
 * <AUTHOR>
 * @date 2021/8/6 14:32
 */
@Dict
public enum OrderSubcontractRelTypeEnum {
    /**
     * tb_test_sample
     */
    Sample(3),
    TestLine(5),
    ;

    OrderSubcontractRelTypeEnum(Integer relType) {
		this.relType = relType;
    };

    @DictCodeField
    private Integer relType;

    public Integer getRelType() {
        return relType;
    }
}
