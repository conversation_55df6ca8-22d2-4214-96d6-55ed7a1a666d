/**
 * SubReportInfo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class SubReportInfo  implements java.io.Serializable {
    private TemplateSectionTypeEnum subReportType;

    private String reportFilePath;

    private String reportConfigFilePath;

    private int sequenceNo;

    private boolean isChemicalWorksheet;

    public SubReportInfo() {
    }

    public SubReportInfo(
           TemplateSectionTypeEnum subReportType,
           String reportFilePath,
           String reportConfigFilePath,
           int sequenceNo,
           boolean isChemicalWorksheet) {
           this.subReportType = subReportType;
           this.reportFilePath = reportFilePath;
           this.reportConfigFilePath = reportConfigFilePath;
           this.sequenceNo = sequenceNo;
           this.isChemicalWorksheet = isChemicalWorksheet;
    }


    /**
     * Gets the subReportType value for this SubReportInfo.
     * 
     * @return subReportType
     */
    public TemplateSectionTypeEnum getSubReportType() {
        return subReportType;
    }


    /**
     * Sets the subReportType value for this SubReportInfo.
     * 
     * @param subReportType
     */
    public void setSubReportType(TemplateSectionTypeEnum subReportType) {
        this.subReportType = subReportType;
    }


    /**
     * Gets the reportFilePath value for this SubReportInfo.
     * 
     * @return reportFilePath
     */
    public String getReportFilePath() {
        return reportFilePath;
    }


    /**
     * Sets the reportFilePath value for this SubReportInfo.
     * 
     * @param reportFilePath
     */
    public void setReportFilePath(String reportFilePath) {
        this.reportFilePath = reportFilePath;
    }


    /**
     * Gets the reportConfigFilePath value for this SubReportInfo.
     * 
     * @return reportConfigFilePath
     */
    public String getReportConfigFilePath() {
        return reportConfigFilePath;
    }


    /**
     * Sets the reportConfigFilePath value for this SubReportInfo.
     * 
     * @param reportConfigFilePath
     */
    public void setReportConfigFilePath(String reportConfigFilePath) {
        this.reportConfigFilePath = reportConfigFilePath;
    }


    /**
     * Gets the sequenceNo value for this SubReportInfo.
     * 
     * @return sequenceNo
     */
    public int getSequenceNo() {
        return sequenceNo;
    }


    /**
     * Sets the sequenceNo value for this SubReportInfo.
     * 
     * @param sequenceNo
     */
    public void setSequenceNo(int sequenceNo) {
        this.sequenceNo = sequenceNo;
    }


    /**
     * Gets the isChemicalWorksheet value for this SubReportInfo.
     * 
     * @return isChemicalWorksheet
     */
    public boolean isIsChemicalWorksheet() {
        return isChemicalWorksheet;
    }


    /**
     * Sets the isChemicalWorksheet value for this SubReportInfo.
     * 
     * @param isChemicalWorksheet
     */
    public void setIsChemicalWorksheet(boolean isChemicalWorksheet) {
        this.isChemicalWorksheet = isChemicalWorksheet;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof SubReportInfo)) {
            return false;
        }
        SubReportInfo other = (SubReportInfo) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.subReportType==null && other.getSubReportType()==null) || 
             (this.subReportType!=null &&
              this.subReportType.equals(other.getSubReportType()))) &&
            ((this.reportFilePath==null && other.getReportFilePath()==null) || 
             (this.reportFilePath!=null &&
              this.reportFilePath.equals(other.getReportFilePath()))) &&
            ((this.reportConfigFilePath==null && other.getReportConfigFilePath()==null) || 
             (this.reportConfigFilePath!=null &&
              this.reportConfigFilePath.equals(other.getReportConfigFilePath()))) &&
            this.sequenceNo == other.getSequenceNo() &&
            this.isChemicalWorksheet == other.isIsChemicalWorksheet();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getSubReportType() != null) {
            _hashCode += getSubReportType().hashCode();
        }
        if (getReportFilePath() != null) {
            _hashCode += getReportFilePath().hashCode();
        }
        if (getReportConfigFilePath() != null) {
            _hashCode += getReportConfigFilePath().hashCode();
        }
        _hashCode += getSequenceNo();
        _hashCode += (isIsChemicalWorksheet() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(SubReportInfo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubReportInfo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("subReportType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubReportType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateSectionTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("reportFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ReportFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("reportConfigFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ReportConfigFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sequenceNo");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SequenceNo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isChemicalWorksheet");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsChemicalWorksheet"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
