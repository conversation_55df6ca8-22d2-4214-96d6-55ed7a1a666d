
package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;

/**
 * @description   :  LogOperationTypeEnums
 * <AUTHOR>  Killian.Sun  Sun Hengyuan
 * @createDate    :  2020/7/2 3:43 PM
 * @updateUser    :  Killian.Sun  Sun Hengyuan
 * @updateDate    :  2020/7/2 3:43 PM
 * @updateRemark  :
 * @version       :  1.0
 */

@Dict
public enum LogOperationTypeEnums {
	/** sample 1*/
	sample(1),
	/** testline 2*/
	testline(2),
	/** condition 3*/
	condition(3),
	/** matrix 4*/
	matrix(4),

	retest(5),

	report(6),

	reportMD5(7),

	copyLimit(8);

	private Integer code;

	LogOperationTypeEnums(Integer code) {
		this.code = code;
	}

	public Integer getCode() {

		return code;
	}

}
