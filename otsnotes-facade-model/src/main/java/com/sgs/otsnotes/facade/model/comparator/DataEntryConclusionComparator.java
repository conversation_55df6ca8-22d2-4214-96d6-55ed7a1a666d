package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.info.dataentry.DataEntryConclusionInfo;

import java.util.Comparator;

public class DataEntryConclusionComparator implements Comparator<DataEntryConclusionInfo> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public DataEntryConclusionComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(DataEntryConclusionInfo o1, DataEntryConclusionInfo o2) {
        int index = Integer.compare(o1.getSampleSeq(), o2.getSampleSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}