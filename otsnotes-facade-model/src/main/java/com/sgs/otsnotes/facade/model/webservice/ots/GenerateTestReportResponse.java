/**
 * GenerateTestReportResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GenerateTestReportResponse  implements java.io.Serializable {
    private boolean generateTestReportResult;

    private Object rlt;

    public GenerateTestReportResponse() {
    }

    public GenerateTestReportResponse(
           boolean generateTestReportResult,
           Object rlt) {
           this.generateTestReportResult = generateTestReportResult;
           this.rlt = rlt;
    }


    /**
     * Gets the generateTestReportResult value for this GenerateTestReportResponse.
     * 
     * @return generateTestReportResult
     */
    public boolean isGenerateTestReportResult() {
        return generateTestReportResult;
    }


    /**
     * Sets the generateTestReportResult value for this GenerateTestReportResponse.
     * 
     * @param generateTestReportResult
     */
    public void setGenerateTestReportResult(boolean generateTestReportResult) {
        this.generateTestReportResult = generateTestReportResult;
    }


    /**
     * Gets the rlt value for this GenerateTestReportResponse.
     * 
     * @return rlt
     */
    public Object getRlt() {
        return rlt;
    }


    /**
     * Sets the rlt value for this GenerateTestReportResponse.
     * 
     * @param rlt
     */
    public void setRlt(Object rlt) {
        this.rlt = rlt;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GenerateTestReportResponse)) {
            return false;
        }
        GenerateTestReportResponse other = (GenerateTestReportResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.generateTestReportResult == other.isGenerateTestReportResult() &&
            ((this.rlt==null && other.getRlt()==null) || 
             (this.rlt!=null &&
              this.rlt.equals(other.getRlt())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += (isGenerateTestReportResult() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        if (getRlt() != null) {
            _hashCode += getRlt().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GenerateTestReportResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GenerateTestReportResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("generateTestReportResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "GenerateTestReportResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rlt");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "rlt"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "anyType"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
