package com.sgs.otsnotes.facade.model.trims.rsp.customizeddata;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class CustomizedDataSyncRsp extends PrintFriendliness {

    private List<CustomizedDataDetailInfo> data;

    public List<CustomizedDataDetailInfo> getData() {
        return data;
    }

    public void setData(List<CustomizedDataDetailInfo> data) {
        this.data = data;
    }
}
