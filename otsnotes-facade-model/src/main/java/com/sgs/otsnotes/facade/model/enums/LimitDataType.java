package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/10 14:27
 */
@Dict
public enum LimitDataType {
    Data(0,"Data"),
    Numeric(1,"Numeric"),

    ; @DictCodeField
    private final int type;
    @DictLabelField
    private final String message;

    public static LimitDataType getType(String key) {
        for (LimitDataType item : LimitDataType.values()) {
            if (item.name().equals(key) || item.message.equals(key)){
                return item;
            }
        }
        return Data;
    }

    LimitDataType(int type, String message) {
        this.type = type;
        this.message = message;
    }

    public int getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }

}
