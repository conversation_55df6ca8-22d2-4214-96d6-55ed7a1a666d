/**
 * ProcessJasperReport.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ProcessJasperReport  implements java.io.Serializable {
    private byte[] fileStream;

    private String targetFilePath;

    public ProcessJasperReport() {
    }

    public ProcessJasperReport(
           byte[] fileStream,
           String targetFilePath) {
           this.fileStream = fileStream;
           this.targetFilePath = targetFilePath;
    }


    /**
     * Gets the fileStream value for this ProcessJasperReport.
     * 
     * @return fileStream
     */
    public byte[] getFileStream() {
        return fileStream;
    }


    /**
     * Sets the fileStream value for this ProcessJasperReport.
     * 
     * @param fileStream
     */
    public void setFileStream(byte[] fileStream) {
        this.fileStream = fileStream;
    }


    /**
     * Gets the targetFilePath value for this ProcessJasperReport.
     * 
     * @return targetFilePath
     */
    public String getTargetFilePath() {
        return targetFilePath;
    }


    /**
     * Sets the targetFilePath value for this ProcessJasperReport.
     * 
     * @param targetFilePath
     */
    public void setTargetFilePath(String targetFilePath) {
        this.targetFilePath = targetFilePath;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ProcessJasperReport)) {
            return false;
        }
        ProcessJasperReport other = (ProcessJasperReport) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.fileStream==null && other.getFileStream()==null) || 
             (this.fileStream!=null &&
              java.util.Arrays.equals(this.fileStream, other.getFileStream()))) &&
            ((this.targetFilePath==null && other.getTargetFilePath()==null) || 
             (this.targetFilePath!=null &&
              this.targetFilePath.equals(other.getTargetFilePath())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getFileStream() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getFileStream());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getFileStream(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getTargetFilePath() != null) {
            _hashCode += getTargetFilePath().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ProcessJasperReport.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">ProcessJasperReport"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("fileStream");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "fileStream"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "base64Binary"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("targetFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "targetFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
