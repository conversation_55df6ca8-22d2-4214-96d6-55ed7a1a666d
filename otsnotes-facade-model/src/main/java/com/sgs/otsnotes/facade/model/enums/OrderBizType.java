package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum OrderBizType {
    Pre(0, "Copy 预处理"),
    Order(1, "Order"),//
    Report(2, "Report"),
    <PERSON><PERSON>(3, "Sample"),
    SampleExt(4,"Sample Ext"),
    TestLine(5, "TestLine"),
    PP(6, "PP"),
    PpTestLineRel(7, "PP"),
    PpSampleRel(8, "PP"),
    PreMatrix(9, "PreMatrix"),
    TestCondition(10, "TestCondition"),
    TestConditionGroup(11, "TestConditionGroup"),
    TestPpConditionGroup(12, "TestPpConditionGroup"),
    Analyte(13, "Analyte"),
    Matrix(14, "Matrix"),
    TestLineRemark(15, "TestLineRemark"),
    LabSection(16, "LabSection"),
    Limit(17, "Limit"),
    LimitGroup(18, "LimitGroup"),
    ProductAttr(19, "ProductAttr"),
    TestPosition(20, "TestPosition"),
    TestSpecimen(21, "TestSpecimen"),
    TestData(22, "TestData"),
    TestDataFail(23, "TestDataFail"),
    ReportMatrixRel(24, "ReportMatrixRel"),
    Conclusion(25, "Conclusion"),
    CrossLab(26, "CrossLab"),
    OrderCitationRel(27, "OrderCitationRel"),
    OrderLangRel(28, "OrderLangRel"),
    SubContract(29, "SubContract"),
    OrderSubcontractRel(30, "OrderSubcontractRel"),
    SampleClaimRel(31, "SampleClaimRel"),
    ReportFile(32, "ReportFile"),
    UpdateTestLine(33, "UpdateTestLine"),
    PreOrderSync(34, "PreOrder Sync"),
    ReportTestData(35, "ReportTestData"),
    ;

    private final long status;

    @DictLabelField
    private final String message;

    OrderBizType(long status, String message) {
        this.status = status;
        this.message = message;
    }

    public long getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

}
