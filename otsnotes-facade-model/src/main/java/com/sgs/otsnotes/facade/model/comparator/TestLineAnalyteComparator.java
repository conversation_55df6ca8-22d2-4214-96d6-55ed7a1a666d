package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.info.trims.Analyte;
import com.sgs.otsnotes.facade.model.info.trims.TestLineAnalyte;

import java.util.Comparator;
import java.util.List;

public class TestLineAnalyteComparator implements Comparator<TestLineAnalyte> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public TestLineAnalyteComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(TestLineAnalyte o1, TestLineAnalyte o2) {
        List<Analyte> testAnalytes = o1.getTestAnalyteItems();
        if (testAnalytes != null){
            testAnalytes.sort(new TestAnalyteComparator(true));
        }
        testAnalytes = o2.getTestAnalyteItems();
        if (testAnalytes != null){
            testAnalytes.sort(new TestAnalyteComparator(true));
        }
        int index = o1.getTlVersionIdentifier() - o2.getTlVersionIdentifier();
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
