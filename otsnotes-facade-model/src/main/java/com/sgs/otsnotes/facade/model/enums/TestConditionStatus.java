package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum TestConditionStatus {
    Inactive(0, "Inactive"),
    Active(1, "Active");

    private final int status;
    private final String code;

    TestConditionStatus(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return this.code;
    }

    public static final Map<String, TestConditionStatus> maps = new HashMap<String, TestConditionStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TestConditionStatus enu : TestConditionStatus.values()) {
                put(enu.getCode().toLowerCase(), enu);
            }
        }
    };

    public static TestConditionStatus getCode(String code) {
        if (code == null || !maps.containsKey(code.toLowerCase())) {
            return null;
        }
        return maps.get(code.toLowerCase());
    }
}
