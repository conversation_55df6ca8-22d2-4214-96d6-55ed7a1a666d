package com.sgs.otsnotes.facade.model.trims.info;

public class PricingSampleSizesSyncInfo {
    /**
     *
     */
    private String unit;
    /**
     *
     */
    private Integer unitId;
    /**
     *
     */
    private Integer sampleSizeId;
    /**
     *
     */
    private String suggestedSampleSize;
    /**
     *
     */
    private String suggestedSampleSizeRemark;

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getSampleSizeId() {
        return sampleSizeId;
    }

    public void setSampleSizeId(Integer sampleSizeId) {
        this.sampleSizeId = sampleSizeId;
    }

    public String getSuggestedSampleSize() {
        return suggestedSampleSize;
    }

    public void setSuggestedSampleSize(String suggestedSampleSize) {
        this.suggestedSampleSize = suggestedSampleSize;
    }

    public String getSuggestedSampleSizeRemark() {
        return suggestedSampleSizeRemark;
    }

    public void setSuggestedSampleSizeRemark(String suggestedSampleSizeRemark) {
        this.suggestedSampleSizeRemark = suggestedSampleSizeRemark;
    }
}
