package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

/**
 * <AUTHOR>
 * @date 2023/5/29 8:31
 */
public enum DataDeleteType {

    DeleteFlag(1, ActiveIndicatorEnum.Inactive, "无效"),
    NotDeleteFlag(0, ActiveIndicatorEnum.Active, "有效"),
    ;
    private int code;
    private ActiveIndicatorEnum activeIndicator;
    private String message;

    DataDeleteType(int code, ActiveIndicatorEnum activeIndicator, String message) {
        this.code = code;
        this.activeIndicator = activeIndicator;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public ActiveIndicatorEnum getActiveIndicator() {
        return activeIndicator;
    }

    public String getMessage() {
        return message;
    }

    static Map<Integer, DataDeleteType> maps = new HashMap<>();

    static {
        for (DataDeleteType type : DataDeleteType.values()) {
            maps.put(type.getCode(), type);
        }
    }

    public static DataDeleteType findCode(Integer code) {
        if (code == null || !maps.containsKey(code)) {
            // 默认为 有效数据
            return DataDeleteType.NotDeleteFlag;
        }
        return maps.get(code);
    }


    public static boolean checkCode(Integer code, DataDeleteType... dataDeleteTypes){
        if (code == null || !maps.containsKey(code) || dataDeleteTypes == null || dataDeleteTypes.length <= 0) {
            return false;
        }
        for (DataDeleteType dataDeleteType : dataDeleteTypes){
            if (code != null && code == dataDeleteType.getCode()){
                return true;
            }
        }
        return false;
    }


}
