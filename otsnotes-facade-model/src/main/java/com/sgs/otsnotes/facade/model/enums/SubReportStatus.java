package com.sgs.otsnotes.facade.model.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum SubReportStatus {
    New(201, "New"),
    Cancelled(202, "Cancelled"),
    Approved(203, "Approved"),
    Draft(204, "Draft"),
    Reworked(205, "Reworked"),
    Combined(206, "Combined"),
    Replaced(207, "Replaced"),
    Completed(208, "Completed"),
    Pending(209, "Pending"),
    Confirmed(210, "Confirmed"),
    Deleted(211, "Deleted")
    ;
    @DictCodeField
    private int code;
    @DictLabelField
    private String message;


    SubReportStatus(int code, String message) {
        this.code = code;
        this.message = message;
    }


    public static List<Integer>  validStatus(){
        return Arrays.asList(New.code,Approved.code, Draft.code, Combined.code,Completed.code,Pending.code,Confirmed.code);
    }
    public static List<String>  validStrStatus(){
        return Arrays.asList(String.valueOf(New.code),String.valueOf(Approved.code),
                String.valueOf(Draft.code), String.valueOf(Combined.code),String.valueOf(Completed.code),String.valueOf(Pending.code),String.valueOf(Confirmed.code));
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, SubReportStatus> maps = new HashMap<Integer, SubReportStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (SubReportStatus reportStatus : SubReportStatus.values()) {
                put(reportStatus.getCode(), reportStatus);
            }
        }
    };

    public static SubReportStatus getCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code.intValue());
    }

    public boolean check(SubReportStatus... reportStatus){
        if (reportStatus == null || reportStatus.length <= 0){
            return false;
        }
        for (SubReportStatus status: reportStatus){
            if (this.getCode() == status.getCode()){
                return true;
            }
        }
        return false;
    }

    public static boolean checkStatus(SubReportStatus status, SubReportStatus... reportStatus){
        if (status == null || reportStatus == null || reportStatus.length <= 0) {
            return false;
        }
        return check(status.getCode(), reportStatus);
    }

    public static boolean check(Integer status, SubReportStatus... reportStatus){
        if (status == null || !maps.containsKey(status.intValue()) || reportStatus == null || reportStatus.length <= 0) {
            return false;
        }
        for (SubReportStatus reportStatu: reportStatus){
            if (status.intValue() == reportStatu.getCode()){
                return true;
            }
        }
        return false;
    }

    public static SubReportStatus enumOf(int code) {
        for (SubReportStatus type : SubReportStatus.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

}
