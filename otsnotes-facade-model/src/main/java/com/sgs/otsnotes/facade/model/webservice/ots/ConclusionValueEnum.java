/**
 * ConclusionValueEnum.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ConclusionValueEnum implements java.io.Serializable {
    private String _value_;
    private static java.util.HashMap _table_ = new java.util.HashMap();

    // Constructor
    protected ConclusionValueEnum(String value) {
        _value_ = value;
        _table_.put(_value_,this);
    }

    public static final String _BlankInput = "BlankInput";
    public static final String _NA = "NA";
    public static final String _Pass = "Pass";
    public static final String _Fail = "Fail";
    public static final String _ConditionalPass = "ConditionalPass";
    public static final String _SeeComments = "SeeComments";
    public static final String _Tolerant = "Tolerant";
    public static final String _NC = "NC";
    public static final String _ComplyWith = "ComplyWith";
    public static final String _MeetRequirement = "MeetRequirement";
    public static final String _SeeAnnex = "SeeAnnex";
    public static final String _CorrecitveActionRequired = "CorrecitveActionRequired";
    public static final String _Inconclusive = "Inconclusive";
    public static final String _RegulatoryFail = "RegulatoryFail";
    public static final String _InterimFail = "InterimFail";
    public static final String _PerformanceFail = "PerformanceFail";
    public static final String _Marginal = "Marginal";
    public static final String _passxcept7Dot1 = "passxcept7Dot1";
    public static final String _passexceptCDot4 = "passexceptCDot4";
    public static final String _passexceptremark = "passexceptremark";
    public static final String _pending = "pending";
    public static final String _PASSEXCEPT7Dot1Up = "PASSEXCEPT7Dot1Up";
    public static final String _PASSEXCEPTCDot4Up = "PASSEXCEPTCDot4Up";
    public static final String _PASSexceptBDot4Up = "PASSexceptBDot4Up";
    public static final String _PASSEXCEPT7Up = "PASSEXCEPT7Up";
    public static final String _MeetClientRequirement = "MeetClientRequirement";
    public static final String _SeeSummaryTable = "SeeSummaryTable";
    public static final String _DataReportOnly = "DataReportOnly";
    public static final String _Waitonfirmation = "Waitonfirmation";
    public static final String _Merges = "Merges";
    public static final ConclusionValueEnum BlankInput = new ConclusionValueEnum(_BlankInput);
    public static final ConclusionValueEnum NA = new ConclusionValueEnum(_NA);
    public static final ConclusionValueEnum Pass = new ConclusionValueEnum(_Pass);
    public static final ConclusionValueEnum Fail = new ConclusionValueEnum(_Fail);
    public static final ConclusionValueEnum ConditionalPass = new ConclusionValueEnum(_ConditionalPass);
    public static final ConclusionValueEnum SeeComments = new ConclusionValueEnum(_SeeComments);
    public static final ConclusionValueEnum Tolerant = new ConclusionValueEnum(_Tolerant);
    public static final ConclusionValueEnum NC = new ConclusionValueEnum(_NC);
    public static final ConclusionValueEnum ComplyWith = new ConclusionValueEnum(_ComplyWith);
    public static final ConclusionValueEnum MeetRequirement = new ConclusionValueEnum(_MeetRequirement);
    public static final ConclusionValueEnum SeeAnnex = new ConclusionValueEnum(_SeeAnnex);
    public static final ConclusionValueEnum CorrecitveActionRequired = new ConclusionValueEnum(_CorrecitveActionRequired);
    public static final ConclusionValueEnum Inconclusive = new ConclusionValueEnum(_Inconclusive);
    public static final ConclusionValueEnum RegulatoryFail = new ConclusionValueEnum(_RegulatoryFail);
    public static final ConclusionValueEnum InterimFail = new ConclusionValueEnum(_InterimFail);
    public static final ConclusionValueEnum PerformanceFail = new ConclusionValueEnum(_PerformanceFail);
    public static final ConclusionValueEnum Marginal = new ConclusionValueEnum(_Marginal);
    public static final ConclusionValueEnum passxcept7Dot1 = new ConclusionValueEnum(_passxcept7Dot1);
    public static final ConclusionValueEnum passexceptCDot4 = new ConclusionValueEnum(_passexceptCDot4);
    public static final ConclusionValueEnum passexceptremark = new ConclusionValueEnum(_passexceptremark);
    public static final ConclusionValueEnum pending = new ConclusionValueEnum(_pending);
    public static final ConclusionValueEnum PASSEXCEPT7Dot1Up = new ConclusionValueEnum(_PASSEXCEPT7Dot1Up);
    public static final ConclusionValueEnum PASSEXCEPTCDot4Up = new ConclusionValueEnum(_PASSEXCEPTCDot4Up);
    public static final ConclusionValueEnum PASSexceptBDot4Up = new ConclusionValueEnum(_PASSexceptBDot4Up);
    public static final ConclusionValueEnum PASSEXCEPT7Up = new ConclusionValueEnum(_PASSEXCEPT7Up);
    public static final ConclusionValueEnum MeetClientRequirement = new ConclusionValueEnum(_MeetClientRequirement);
    public static final ConclusionValueEnum SeeSummaryTable = new ConclusionValueEnum(_SeeSummaryTable);
    public static final ConclusionValueEnum DataReportOnly = new ConclusionValueEnum(_DataReportOnly);
    public static final ConclusionValueEnum Waitonfirmation = new ConclusionValueEnum(_Waitonfirmation);
    public static final ConclusionValueEnum Merges = new ConclusionValueEnum(_Merges);
    public String getValue() { return _value_;}
    public static ConclusionValueEnum fromValue(String value)
          throws IllegalArgumentException {
        ConclusionValueEnum enumeration = (ConclusionValueEnum)
            _table_.get(value);
        if (enumeration==null) {
            throw new IllegalArgumentException();
        }
        return enumeration;
    }
    public static ConclusionValueEnum fromString(String value)
          throws IllegalArgumentException {
        return fromValue(value);
    }
    public boolean equals(Object obj) {return (obj == this);}
    public int hashCode() { return toString().hashCode();}
    public String toString() { return _value_;}
    public Object readResolve() throws java.io.ObjectStreamException { return fromValue(_value_);}
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumSerializer(
            _javaType, _xmlType);
    }
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumDeserializer(
            _javaType, _xmlType);
    }
    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ConclusionValueEnum.class);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ConclusionValueEnum"));
    }
    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

}
