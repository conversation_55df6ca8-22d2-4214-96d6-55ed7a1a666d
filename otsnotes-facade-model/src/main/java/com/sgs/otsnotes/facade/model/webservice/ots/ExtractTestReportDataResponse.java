/**
 * ExtractTestReportDataResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ExtractTestReportDataResponse  implements java.io.Serializable {
    private DataItem[] extractTestReportDataResult;

    private String errMessage;

    public ExtractTestReportDataResponse() {
    }

    public ExtractTestReportDataResponse(
           DataItem[] extractTestReportDataResult,
           String errMessage) {
           this.extractTestReportDataResult = extractTestReportDataResult;
           this.errMessage = errMessage;
    }


    /**
     * Gets the extractTestReportDataResult value for this ExtractTestReportDataResponse.
     * 
     * @return extractTestReportDataResult
     */
    public DataItem[] getExtractTestReportDataResult() {
        return extractTestReportDataResult;
    }


    /**
     * Sets the extractTestReportDataResult value for this ExtractTestReportDataResponse.
     * 
     * @param extractTestReportDataResult
     */
    public void setExtractTestReportDataResult(DataItem[] extractTestReportDataResult) {
        this.extractTestReportDataResult = extractTestReportDataResult;
    }


    /**
     * Gets the errMessage value for this ExtractTestReportDataResponse.
     * 
     * @return errMessage
     */
    public String getErrMessage() {
        return errMessage;
    }


    /**
     * Sets the errMessage value for this ExtractTestReportDataResponse.
     * 
     * @param errMessage
     */
    public void setErrMessage(String errMessage) {
        this.errMessage = errMessage;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ExtractTestReportDataResponse)) {
            return false;
        }
        ExtractTestReportDataResponse other = (ExtractTestReportDataResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.extractTestReportDataResult==null && other.getExtractTestReportDataResult()==null) || 
             (this.extractTestReportDataResult!=null &&
              java.util.Arrays.equals(this.extractTestReportDataResult, other.getExtractTestReportDataResult()))) &&
            ((this.errMessage==null && other.getErrMessage()==null) || 
             (this.errMessage!=null &&
              this.errMessage.equals(other.getErrMessage())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getExtractTestReportDataResult() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getExtractTestReportDataResult());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getExtractTestReportDataResult(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getErrMessage() != null) {
            _hashCode += getErrMessage().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ExtractTestReportDataResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">ExtractTestReportDataResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("extractTestReportDataResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ExtractTestReportDataResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("errMessage");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "errMessage"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
