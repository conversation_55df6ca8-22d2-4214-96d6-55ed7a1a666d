package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

/**
 * 请使用 com.sgs.framework.model.enums.ProductLineType;
 */
@Deprecated
@Dict
public enum ProductLineType {
    ALL(0,false,"ALL"),
    SL(1, "SL", "Softlines"),
    HL(2,"HL","Hardlines"),
    EE(3,"E&E","Electrical & Electronic"),
    CPCH(4,"CPCH","Cosmetics, Personal Care & Household"),
    CChemLab(5,"CChemLab","Central Chemical Laboratory"),

    INDMetal(6,"IND-ML","IND-Metal"),
    INDPolymer(7,"IND-PL","IND-Polymer"),
    EEC(8,"EEC","Electrical & Electronic Consumer"),
    AFL(9,"AFL","Agriculture, Food and Life"),

    AUTO(10,"AUTO","Automotive"),
    TY(11,"TY","Toys"),
    EHS(12,"EHS","Environment, Health & Safety"),
    FTS(13,"FTS","Fire Technology"),
    RS(14,"RS","Rolling Stock"),

    MR(15,"MR","Material and Reliability"),
    LSS(16,"LSS","Life Science Services");

    private final int productLineId;
    private boolean sync = true;
    private final String productLineAbbr;
    private final String productLineName;

    /**
     *
     * @param productLineId
     * @param sync
     * @param productLineAbbr
     */
    ProductLineType(int productLineId, boolean sync, String productLineAbbr) {
        this(productLineId, productLineAbbr, null);
        this.sync = sync;
    }

    /**
     *
     * @param productLineId
     * @param productLineAbbr
     * @param productLineName
     */
    ProductLineType(int productLineId, String productLineAbbr, String productLineName) {
        this.productLineId = productLineId;
        this.productLineAbbr = productLineAbbr;
        this.productLineName = productLineName;
    }

    /**
     *
     * @param productLineId
     * @param sync
     * @param productLineAbbr
     * @param productLineName
     */
    ProductLineType(int productLineId, boolean sync, String productLineAbbr, String productLineName) {
        this(productLineId, productLineAbbr, productLineName);
        this.sync = sync;
    }

    public int getProductLineId() {
        return productLineId;
    }

    public boolean isSync() {
        return sync;
    }

    public String getProductLineAbbr() {
        return productLineAbbr;
    }

    public String getProductLineName() {
        return productLineName;
    }

    public static final Map<Integer, ProductLineType> maps = new HashMap<Integer, ProductLineType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ProductLineType enu : ProductLineType.values()) {
                put(enu.getProductLineId(), enu);
            }
        }
    };

    public static final Map<String, ProductLineType> productLineAbbrMaps = new HashMap<String, ProductLineType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ProductLineType enu : ProductLineType.values()) {
                put(enu.getProductLineAbbr().toUpperCase(), enu);
            }
        }
    };

    /**
     *
     * @param productLineId
     * @return
     */
    public static ProductLineType findType(Integer productLineId) {
        if (productLineId == null || !maps.containsKey(productLineId)) {
            return ProductLineType.ALL;
        }
        return maps.get(productLineId);
    }

    /**
     *
     * @param productLineAbbr
     * @return
     */
    public static ProductLineType findProductLineAbbr(String productLineAbbr) {
        if (productLineAbbr == null || !productLineAbbrMaps.containsKey(productLineAbbr.toUpperCase())) {
            return ProductLineType.ALL;
        }
        return productLineAbbrMaps.get(productLineAbbr.toUpperCase());
    }

    /**
     *
     * @param productLineAbbr
     * @return
     */
    public static boolean check(String productLineAbbr) {
        if (productLineAbbr == null) {
            return false;
        }
        return productLineAbbrMaps.containsKey(productLineAbbr.toUpperCase());
    }

    /**
     *
     * @param productLineId
     * @return
     */
    public static boolean checkSync(Integer productLineId){
        if (productLineId == null || !maps.containsKey(productLineId)) {
            return false;
        }
        ProductLineType productLineType = maps.get(productLineId);
        return productLineType.isSync();
    }

    public static boolean check(Integer productLineId, ProductLineType... productLineTypes){
        if (productLineId == null || productLineId <= 0 || productLineTypes == null || productLineTypes.length <= 0){
            return false;
        }
        for (ProductLineType pl: productLineTypes){
            if (productLineId != null && productLineId == pl.getProductLineId()){
                return true;
            }
        }
        return false;
    }

}
