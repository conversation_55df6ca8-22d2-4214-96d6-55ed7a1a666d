package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

/**
 * @Author: Joke.wang
 * @Date: 2022/4/2 13:47
 */

public enum ReferenceIDEnum {

    portalCustomerNumber(1,"portalCustomerNumber"),
    SGSMart(2,"sgsMart"),
    OLB(3,"OLB"),
    SEMIR(4,"SEMIR"),
    ANTA(5,"ANTA"),
    InspectionTool(6,"InspectionTool"),
    SubContract(8, "SubContract"),
    ExternalSubContract(9,"ExternalSubContract"),
    OTS(10,"OTSSubContract"),
    ;

    private Integer code;
    private String name;
    ReferenceIDEnum(Integer code,String name){
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static final Map<Integer, ReferenceIDEnum> maps = new HashMap<Integer, ReferenceIDEnum>() {
        private static final long serialVersionUID = -5362445229658442135L;

        {
            for (ReferenceIDEnum refId : ReferenceIDEnum.values()) {
                put(refId.getCode(), refId);
            }
        }
    };

    public static boolean check(Integer code, ReferenceIDEnum refId) {
        if (code == null || !maps.containsKey(code)){
            return false;
        }
        return maps.get(code) == refId;
    }

    public static boolean check(Integer code,ReferenceIDEnum ... refIds){
        if(code==null || !maps.containsKey(code)){
            return false;
        }
        for (ReferenceIDEnum refId : refIds) {
            if(code.equals(refId.getCode())){
                return true;
            }
        }
        return false;
    }
}
