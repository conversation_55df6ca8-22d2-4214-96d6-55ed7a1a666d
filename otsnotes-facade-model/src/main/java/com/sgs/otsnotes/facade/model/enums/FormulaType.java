package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

@Dict
public enum FormulaType {
    None(0, "None"),
    Calc(1, "Calc");

    private int type;
    private String message;

    FormulaType(int type, String message) {
        this.type = type;
        this.message = message;
    }

    public int getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, FormulaType> maps = new HashMap<Integer, FormulaType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (FormulaType enu : FormulaType.values()) {
                put(enu.getType(), enu);
            }
        }
    };

    /**
     *
     * @param status
     * @return
     */
    public static FormulaType findType(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())){
            return FormulaType.None;
        }
        return maps.get(status);
    }

    /**
     *
     * @param code
     * @return
     */
    public static FormulaType findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return FormulaType.None;
        }
        return maps.get(code);
    }

    /**
     *
     * @param status
     * @return
     */
    public static boolean check(Integer status) {
        if (status == null){
            return false;
        }
        return maps.containsKey(status.intValue());
    }

    /**
     *
     * @param status
     * @param type
     * @return
     */
    public static boolean check(Integer status, FormulaType type) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        return maps.get(status.intValue()) == type;
    }
}
