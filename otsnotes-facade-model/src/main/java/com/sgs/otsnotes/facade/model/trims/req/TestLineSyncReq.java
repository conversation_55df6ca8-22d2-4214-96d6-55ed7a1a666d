package com.sgs.otsnotes.facade.model.trims.req;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseReq;

import java.util.List;

public class TestLineSyncReq extends TrimsSyncBaseReq {
    /**
     *
     */
    private List<Long> ids;
    /**
     *
     */
    private String lastModifyFrom;

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public String getLastModifyFrom() {
        return lastModifyFrom;
    }

    public void setLastModifyFrom(String lastModifyFrom) {
        this.lastModifyFrom = lastModifyFrom;
    }
}
