package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class EquipmentTypeSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer equipmentTypeId;
    /**
     *
     */
    private String equipmentTypeName;
    /**
     *
     */
    private String equipmentTypeShortName;
    /**
     *
     */
    private String equipmentTypeDescription;

    public Integer getEquipmentTypeId() {
        return equipmentTypeId;
    }

    public void setEquipmentTypeId(Integer equipmentTypeId) {
        this.equipmentTypeId = equipmentTypeId;
    }

    public String getEquipmentTypeName() {
        return equipmentTypeName;
    }

    public void setEquipmentTypeName(String equipmentTypeName) {
        this.equipmentTypeName = equipmentTypeName;
    }

    public String getEquipmentTypeShortName() {
        return equipmentTypeShortName;
    }

    public void setEquipmentTypeShortName(String equipmentTypeShortName) {
        this.equipmentTypeShortName = equipmentTypeShortName;
    }

    public String getEquipmentTypeDescription() {
        return equipmentTypeDescription;
    }

    public void setEquipmentTypeDescription(String equipmentTypeDescription) {
        this.equipmentTypeDescription = equipmentTypeDescription;
    }
}
