package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;

/**
 * @description   :   DIG-2167 OTSNotesInterfaces
 * <AUTHOR>  Killian.Sun  Sun He<PERSON>yuan
 * @createDate    :  2020/7/3 10:51 AM
 * @updateUser    :  Killian.Sun  Sun Hengyuan
 * @updateDate    :  2020/7/3 10:51 AM
 * @updateRemark  :
 * @version       :  1.0
 */
@Dict
public enum LogOperationEnums {
	ins("INS"), del("DEL"), upd("UPD"),can("CAN");
	
	private String desc;

	LogOperationEnums(String desc) {
		this.desc = desc;
	}

	public String getDesc() {

		return desc;
	}

}
