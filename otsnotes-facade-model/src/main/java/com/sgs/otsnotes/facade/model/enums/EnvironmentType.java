package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum EnvironmentType {
    Local((1 << 0),"local", "Local"),
    Dev((1 << 1),"dev", false, "Dev"),
    Test((1 << 2),"test", "Test"),
    Uat((1 << 3),"uat", "Uat"),
    Prod((1 << 4),"production", "Prod");

    private int envType;
    private String code;
    private boolean isWrite;
    private String message;

    EnvironmentType(int envType, String code, String message) {
        this.envType = envType;
        this.code = code;
        this.isWrite = true;
        this.message = message;
    }

    EnvironmentType(int envType, String code, boolean isWrite, String message) {
        this(envType, code, message);
        this.isWrite = isWrite;
    }

    public String getCode() {
        return code;
    }

    public boolean isWrite() {
        return isWrite;
    }

    public String getMessage() {
        return message;
    }

    public int getEnvType() {
        return envType;
    }

    static Map<String, EnvironmentType> maps = new HashMap<>();

    static {
        for (EnvironmentType type : EnvironmentType.values()) {
            maps.put(type.getCode(), type);
        }
    }

    public static EnvironmentType findCode(String code) {
        if (code == null){
            return null;
        }
        return maps.get(code.toLowerCase());
    }

    public static boolean check(EnvironmentType envType, EnvironmentType... envTypes) {
        if (envType == null || envTypes == null || envTypes.length <= 0){
            return false;
        }
        for (EnvironmentType env: envTypes) {
            if ((env.getEnvType() & envType.getEnvType()) > 0){
                return true;
            }
        }
        return false;
    }

    public boolean check(EnvironmentType envType) {
        if (envType == null){
            return false;
        }
        return this == envType;
    }
}
