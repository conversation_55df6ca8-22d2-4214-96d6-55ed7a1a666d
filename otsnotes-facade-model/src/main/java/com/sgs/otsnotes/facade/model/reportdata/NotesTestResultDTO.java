package com.sgs.otsnotes.facade.model.reportdata;

import com.sgs.extsystem.facade.model.reportdata.RdTestResultDTO;
import lombok.Data;

@Data
public class NotesTestResultDTO extends RdTestResultDTO {

    // 新增一个主键Id 用于排序
    private String testResultId;

    // 实例化相关的排序字段 upSpecimenNo, SpecimenNo, TestConditionSeq, AnalyteSeq, PositionSeq
    private Integer upSpecimenNo;
    private Integer specimenNo;
    private Integer conditionParentSeq;
    private Integer conditionSeq;
    private Integer analyteSeq;
    private Integer positionSeq;


}
