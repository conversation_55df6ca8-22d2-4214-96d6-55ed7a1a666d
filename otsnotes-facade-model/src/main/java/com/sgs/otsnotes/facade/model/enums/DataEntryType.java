package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

public enum DataEntryType {
    None(0, ""),
    Lab(1, "Lab"),
    Sub(2, "Sub");
    private int status;
    private String code;

    DataEntryType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return this.code;
    }

    static Map<Integer, DataEntryType> maps = new HashMap<>();

    static {
        for (DataEntryType type : DataEntryType.values()) {
            maps.put(type.getStatus(), type);
        }
    }

    public static DataEntryType findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code);
    }

    public static boolean check(Integer status) {
        if (status == null){
            return false;
        }
        return maps.containsKey(status.intValue());
    }
}