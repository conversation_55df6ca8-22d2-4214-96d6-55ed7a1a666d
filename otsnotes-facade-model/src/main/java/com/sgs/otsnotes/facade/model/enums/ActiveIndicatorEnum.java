package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum ActiveIndicatorEnum {
    Inactive(0, "Inactive"),
    Active(1, "Active");

    @DictCodeField
    private int status;
    @DictLabelField
    private String code;

    ActiveIndicatorEnum(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    static Map<Integer, AccreditationStatus> maps = new HashMap<>();
    static Map<String, AccreditationStatus> codeMaps = new HashMap<>();

    static {
        for (AccreditationStatus status : AccreditationStatus.values()) {
            maps.put(status.getStatus(), status);
            codeMaps.put(status.getCode().toLowerCase(), status);
        }
    };

    public static AccreditationStatus getStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }
}
