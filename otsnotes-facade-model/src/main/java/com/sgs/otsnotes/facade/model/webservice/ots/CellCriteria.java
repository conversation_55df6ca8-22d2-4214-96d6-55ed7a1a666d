/**
 * CellCriteria.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class CellCriteria  implements java.io.Serializable {
    private String ID;

    private int PGT;

    private int PG;

    private DatasourceTypeEnum tableDatasourceType;

    private DatasourceTypeEnum columnDatasourceType;

    private DatasourceTypeEnum rowDatasourceType;

    private int TT;

    private int tableDatasourceItemPrimaryKey;

    private String tableDatasourceItemID;

    private int CT;

    private int columnDatasourceItemPrimaryKey;

    private String columnDatasourceItemID;

    private int RT;

    private int rowDatasourceItemPrimaryKey;

    private String rowDatasourceItemID;

    private String regionType;

    public CellCriteria() {
    }

    public CellCriteria(
           String ID,
           int PGT,
           int PG,
           DatasourceTypeEnum tableDatasourceType,
           DatasourceTypeEnum columnDatasourceType,
           DatasourceTypeEnum rowDatasourceType,
           int TT,
           int tableDatasourceItemPrimaryKey,
           String tableDatasourceItemID,
           int CT,
           int columnDatasourceItemPrimaryKey,
           String columnDatasourceItemID,
           int RT,
           int rowDatasourceItemPrimaryKey,
           String rowDatasourceItemID,
           String regionType) {
           this.ID = ID;
           this.PGT = PGT;
           this.PG = PG;
           this.tableDatasourceType = tableDatasourceType;
           this.columnDatasourceType = columnDatasourceType;
           this.rowDatasourceType = rowDatasourceType;
           this.TT = TT;
           this.tableDatasourceItemPrimaryKey = tableDatasourceItemPrimaryKey;
           this.tableDatasourceItemID = tableDatasourceItemID;
           this.CT = CT;
           this.columnDatasourceItemPrimaryKey = columnDatasourceItemPrimaryKey;
           this.columnDatasourceItemID = columnDatasourceItemID;
           this.RT = RT;
           this.rowDatasourceItemPrimaryKey = rowDatasourceItemPrimaryKey;
           this.rowDatasourceItemID = rowDatasourceItemID;
           this.regionType = regionType;
    }


    /**
     * Gets the ID value for this CellCriteria.
     * 
     * @return ID
     */
    public String getID() {
        return ID;
    }


    /**
     * Sets the ID value for this CellCriteria.
     * 
     * @param ID
     */
    public void setID(String ID) {
        this.ID = ID;
    }


    /**
     * Gets the PGT value for this CellCriteria.
     * 
     * @return PGT
     */
    public int getPGT() {
        return PGT;
    }


    /**
     * Sets the PGT value for this CellCriteria.
     * 
     * @param PGT
     */
    public void setPGT(int PGT) {
        this.PGT = PGT;
    }


    /**
     * Gets the PG value for this CellCriteria.
     * 
     * @return PG
     */
    public int getPG() {
        return PG;
    }


    /**
     * Sets the PG value for this CellCriteria.
     * 
     * @param PG
     */
    public void setPG(int PG) {
        this.PG = PG;
    }


    /**
     * Gets the tableDatasourceType value for this CellCriteria.
     * 
     * @return tableDatasourceType
     */
    public DatasourceTypeEnum getTableDatasourceType() {
        return tableDatasourceType;
    }


    /**
     * Sets the tableDatasourceType value for this CellCriteria.
     * 
     * @param tableDatasourceType
     */
    public void setTableDatasourceType(DatasourceTypeEnum tableDatasourceType) {
        this.tableDatasourceType = tableDatasourceType;
    }


    /**
     * Gets the columnDatasourceType value for this CellCriteria.
     * 
     * @return columnDatasourceType
     */
    public DatasourceTypeEnum getColumnDatasourceType() {
        return columnDatasourceType;
    }


    /**
     * Sets the columnDatasourceType value for this CellCriteria.
     * 
     * @param columnDatasourceType
     */
    public void setColumnDatasourceType(DatasourceTypeEnum columnDatasourceType) {
        this.columnDatasourceType = columnDatasourceType;
    }


    /**
     * Gets the rowDatasourceType value for this CellCriteria.
     * 
     * @return rowDatasourceType
     */
    public DatasourceTypeEnum getRowDatasourceType() {
        return rowDatasourceType;
    }


    /**
     * Sets the rowDatasourceType value for this CellCriteria.
     * 
     * @param rowDatasourceType
     */
    public void setRowDatasourceType(DatasourceTypeEnum rowDatasourceType) {
        this.rowDatasourceType = rowDatasourceType;
    }


    /**
     * Gets the TT value for this CellCriteria.
     * 
     * @return TT
     */
    public int getTT() {
        return TT;
    }


    /**
     * Sets the TT value for this CellCriteria.
     * 
     * @param TT
     */
    public void setTT(int TT) {
        this.TT = TT;
    }


    /**
     * Gets the tableDatasourceItemPrimaryKey value for this CellCriteria.
     * 
     * @return tableDatasourceItemPrimaryKey
     */
    public int getTableDatasourceItemPrimaryKey() {
        return tableDatasourceItemPrimaryKey;
    }


    /**
     * Sets the tableDatasourceItemPrimaryKey value for this CellCriteria.
     * 
     * @param tableDatasourceItemPrimaryKey
     */
    public void setTableDatasourceItemPrimaryKey(int tableDatasourceItemPrimaryKey) {
        this.tableDatasourceItemPrimaryKey = tableDatasourceItemPrimaryKey;
    }


    /**
     * Gets the tableDatasourceItemID value for this CellCriteria.
     * 
     * @return tableDatasourceItemID
     */
    public String getTableDatasourceItemID() {
        return tableDatasourceItemID;
    }


    /**
     * Sets the tableDatasourceItemID value for this CellCriteria.
     * 
     * @param tableDatasourceItemID
     */
    public void setTableDatasourceItemID(String tableDatasourceItemID) {
        this.tableDatasourceItemID = tableDatasourceItemID;
    }


    /**
     * Gets the CT value for this CellCriteria.
     * 
     * @return CT
     */
    public int getCT() {
        return CT;
    }


    /**
     * Sets the CT value for this CellCriteria.
     * 
     * @param CT
     */
    public void setCT(int CT) {
        this.CT = CT;
    }


    /**
     * Gets the columnDatasourceItemPrimaryKey value for this CellCriteria.
     * 
     * @return columnDatasourceItemPrimaryKey
     */
    public int getColumnDatasourceItemPrimaryKey() {
        return columnDatasourceItemPrimaryKey;
    }


    /**
     * Sets the columnDatasourceItemPrimaryKey value for this CellCriteria.
     * 
     * @param columnDatasourceItemPrimaryKey
     */
    public void setColumnDatasourceItemPrimaryKey(int columnDatasourceItemPrimaryKey) {
        this.columnDatasourceItemPrimaryKey = columnDatasourceItemPrimaryKey;
    }


    /**
     * Gets the columnDatasourceItemID value for this CellCriteria.
     * 
     * @return columnDatasourceItemID
     */
    public String getColumnDatasourceItemID() {
        return columnDatasourceItemID;
    }


    /**
     * Sets the columnDatasourceItemID value for this CellCriteria.
     * 
     * @param columnDatasourceItemID
     */
    public void setColumnDatasourceItemID(String columnDatasourceItemID) {
        this.columnDatasourceItemID = columnDatasourceItemID;
    }


    /**
     * Gets the RT value for this CellCriteria.
     * 
     * @return RT
     */
    public int getRT() {
        return RT;
    }


    /**
     * Sets the RT value for this CellCriteria.
     * 
     * @param RT
     */
    public void setRT(int RT) {
        this.RT = RT;
    }


    /**
     * Gets the rowDatasourceItemPrimaryKey value for this CellCriteria.
     * 
     * @return rowDatasourceItemPrimaryKey
     */
    public int getRowDatasourceItemPrimaryKey() {
        return rowDatasourceItemPrimaryKey;
    }


    /**
     * Sets the rowDatasourceItemPrimaryKey value for this CellCriteria.
     * 
     * @param rowDatasourceItemPrimaryKey
     */
    public void setRowDatasourceItemPrimaryKey(int rowDatasourceItemPrimaryKey) {
        this.rowDatasourceItemPrimaryKey = rowDatasourceItemPrimaryKey;
    }


    /**
     * Gets the rowDatasourceItemID value for this CellCriteria.
     * 
     * @return rowDatasourceItemID
     */
    public String getRowDatasourceItemID() {
        return rowDatasourceItemID;
    }


    /**
     * Sets the rowDatasourceItemID value for this CellCriteria.
     * 
     * @param rowDatasourceItemID
     */
    public void setRowDatasourceItemID(String rowDatasourceItemID) {
        this.rowDatasourceItemID = rowDatasourceItemID;
    }


    /**
     * Gets the regionType value for this CellCriteria.
     * 
     * @return regionType
     */
    public String getRegionType() {
        return regionType;
    }


    /**
     * Sets the regionType value for this CellCriteria.
     * 
     * @param regionType
     */
    public void setRegionType(String regionType) {
        this.regionType = regionType;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof CellCriteria)) {
            return false;
        }
        CellCriteria other = (CellCriteria) obj;
        if (obj == null) {
            return false;
        }
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.ID==null && other.getID()==null) || 
             (this.ID!=null &&
              this.ID.equals(other.getID()))) &&
            this.PGT == other.getPGT() &&
            this.PG == other.getPG() &&
            ((this.tableDatasourceType==null && other.getTableDatasourceType()==null) || 
             (this.tableDatasourceType!=null &&
              this.tableDatasourceType.equals(other.getTableDatasourceType()))) &&
            ((this.columnDatasourceType==null && other.getColumnDatasourceType()==null) || 
             (this.columnDatasourceType!=null &&
              this.columnDatasourceType.equals(other.getColumnDatasourceType()))) &&
            ((this.rowDatasourceType==null && other.getRowDatasourceType()==null) || 
             (this.rowDatasourceType!=null &&
              this.rowDatasourceType.equals(other.getRowDatasourceType()))) &&
            this.TT == other.getTT() &&
            this.tableDatasourceItemPrimaryKey == other.getTableDatasourceItemPrimaryKey() &&
            ((this.tableDatasourceItemID==null && other.getTableDatasourceItemID()==null) || 
             (this.tableDatasourceItemID!=null &&
              this.tableDatasourceItemID.equals(other.getTableDatasourceItemID()))) &&
            this.CT == other.getCT() &&
            this.columnDatasourceItemPrimaryKey == other.getColumnDatasourceItemPrimaryKey() &&
            ((this.columnDatasourceItemID==null && other.getColumnDatasourceItemID()==null) || 
             (this.columnDatasourceItemID!=null &&
              this.columnDatasourceItemID.equals(other.getColumnDatasourceItemID()))) &&
            this.RT == other.getRT() &&
            this.rowDatasourceItemPrimaryKey == other.getRowDatasourceItemPrimaryKey() &&
            ((this.rowDatasourceItemID==null && other.getRowDatasourceItemID()==null) || 
             (this.rowDatasourceItemID!=null &&
              this.rowDatasourceItemID.equals(other.getRowDatasourceItemID()))) &&
            ((this.regionType==null && other.getRegionType()==null) || 
             (this.regionType!=null &&
              this.regionType.equals(other.getRegionType())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getID() != null) {
            _hashCode += getID().hashCode();
        }
        _hashCode += getPGT();
        _hashCode += getPG();
        if (getTableDatasourceType() != null) {
            _hashCode += getTableDatasourceType().hashCode();
        }
        if (getColumnDatasourceType() != null) {
            _hashCode += getColumnDatasourceType().hashCode();
        }
        if (getRowDatasourceType() != null) {
            _hashCode += getRowDatasourceType().hashCode();
        }
        _hashCode += getTT();
        _hashCode += getTableDatasourceItemPrimaryKey();
        if (getTableDatasourceItemID() != null) {
            _hashCode += getTableDatasourceItemID().hashCode();
        }
        _hashCode += getCT();
        _hashCode += getColumnDatasourceItemPrimaryKey();
        if (getColumnDatasourceItemID() != null) {
            _hashCode += getColumnDatasourceItemID().hashCode();
        }
        _hashCode += getRT();
        _hashCode += getRowDatasourceItemPrimaryKey();
        if (getRowDatasourceItemID() != null) {
            _hashCode += getRowDatasourceItemID().hashCode();
        }
        if (getRegionType() != null) {
            _hashCode += getRegionType().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(CellCriteria.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "CellCriteria"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PGT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PGT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tableDatasourceType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TableDatasourceType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DatasourceTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("columnDatasourceType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ColumnDatasourceType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DatasourceTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rowDatasourceType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RowDatasourceType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DatasourceTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("TT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tableDatasourceItemPrimaryKey");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TableDatasourceItemPrimaryKey"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tableDatasourceItemID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TableDatasourceItemID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("columnDatasourceItemPrimaryKey");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ColumnDatasourceItemPrimaryKey"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("columnDatasourceItemID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ColumnDatasourceItemID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("RT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rowDatasourceItemPrimaryKey");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RowDatasourceItemPrimaryKey"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rowDatasourceItemID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RowDatasourceItemID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("regionType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RegionType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
