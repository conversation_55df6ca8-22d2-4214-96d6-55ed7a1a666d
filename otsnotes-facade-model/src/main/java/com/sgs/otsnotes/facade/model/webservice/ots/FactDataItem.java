/**
 * FactDataItem.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class FactDataItem  implements java.io.Serializable {
    private String bookmarkName;

    private FactDatasourceTypeEnum factType;

    private Dim[] dims;

    private Object value;

    public FactDataItem() {
    }

    public FactDataItem(
           String bookmarkName,
           FactDatasourceTypeEnum factType,
           Dim[] dims,
           Object value) {
           this.bookmarkName = bookmarkName;
           this.factType = factType;
           this.dims = dims;
           this.value = value;
    }


    /**
     * Gets the bookmarkName value for this FactDataItem.
     * 
     * @return bookmarkName
     */
    public String getBookmarkName() {
        return bookmarkName;
    }


    /**
     * Sets the bookmarkName value for this FactDataItem.
     * 
     * @param bookmarkName
     */
    public void setBookmarkName(String bookmarkName) {
        this.bookmarkName = bookmarkName;
    }


    /**
     * Gets the factType value for this FactDataItem.
     * 
     * @return factType
     */
    public FactDatasourceTypeEnum getFactType() {
        return factType;
    }


    /**
     * Sets the factType value for this FactDataItem.
     * 
     * @param factType
     */
    public void setFactType(FactDatasourceTypeEnum factType) {
        this.factType = factType;
    }


    /**
     * Gets the dims value for this FactDataItem.
     * 
     * @return dims
     */
    public Dim[] getDims() {
        return dims;
    }


    /**
     * Sets the dims value for this FactDataItem.
     * 
     * @param dims
     */
    public void setDims(Dim[] dims) {
        this.dims = dims;
    }


    /**
     * Gets the value value for this FactDataItem.
     * 
     * @return value
     */
    public Object getValue() {
        return value;
    }


    /**
     * Sets the value value for this FactDataItem.
     * 
     * @param value
     */
    public void setValue(Object value) {
        this.value = value;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof FactDataItem)) {
            return false;
        }
        FactDataItem other = (FactDataItem) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.bookmarkName==null && other.getBookmarkName()==null) || 
             (this.bookmarkName!=null &&
              this.bookmarkName.equals(other.getBookmarkName()))) &&
            ((this.factType==null && other.getFactType()==null) || 
             (this.factType!=null &&
              this.factType.equals(other.getFactType()))) &&
            ((this.dims==null && other.getDims()==null) || 
             (this.dims!=null &&
              java.util.Arrays.equals(this.dims, other.getDims()))) &&
            ((this.value==null && other.getValue()==null) || 
             (this.value!=null &&
              this.value.equals(other.getValue())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getBookmarkName() != null) {
            _hashCode += getBookmarkName().hashCode();
        }
        if (getFactType() != null) {
            _hashCode += getFactType().hashCode();
        }
        if (getDims() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getDims());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getDims(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getValue() != null) {
            _hashCode += getValue().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(FactDataItem.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FactDataItem"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("bookmarkName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "BookmarkName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("factType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FactType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FactDatasourceTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("dims");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Dims"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "Dim"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Dim"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("value");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Value"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "anyType"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
