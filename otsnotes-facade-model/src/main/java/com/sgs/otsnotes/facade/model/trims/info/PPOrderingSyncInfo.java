package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName PPOrderingSyncInfo
 * @Description pp ordering 同步类
 * <AUTHOR>
 * @Date 2024/1/23
 */
public class PPOrderingSyncInfo extends PrintFriendliness {

    private Long id;
    private String status;
    private Integer customerAccountId;
    private Integer ppNumber;
    private Integer sequence;
    private Integer productLineId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getCustomerAccountId() {
        return customerAccountId;
    }

    public void setCustomerAccountId(Integer customerAccountId) {
        this.customerAccountId = customerAccountId;
    }

    public Integer getPpNumber() {
        return ppNumber;
    }

    public void setPpNumber(Integer ppNumber) {
        this.ppNumber = ppNumber;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Integer getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }
}
