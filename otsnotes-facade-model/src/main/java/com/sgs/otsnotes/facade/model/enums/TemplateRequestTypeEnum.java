package com.sgs.otsnotes.facade.model.enums;

/**
 * 定义模板的类型
 * <AUTHOR>
 * @date 2020/7/17 13:57
 */
public enum TemplateRequestTypeEnum {
    ByCustomer("by customer", 0),
    ByTemplate("by template", 1),
            ;

    private String text;
    private Integer value;

    TemplateRequestTypeEnum(String text, Integer value){
        this.text = text;
        this.value = value;
    }

    public String getText() {
        return text;
    }

    public Integer getValue() {
        return value;
    }

    public static boolean check(Integer value,TemplateRequestTypeEnum ... enums){
        if(value==null){
            return false;
        }
        for (TemplateRequestTypeEnum anEnum : enums) {
            if(value.compareTo(anEnum.value)==0){
                return true;
            }
        }
        return false;
    }
}
