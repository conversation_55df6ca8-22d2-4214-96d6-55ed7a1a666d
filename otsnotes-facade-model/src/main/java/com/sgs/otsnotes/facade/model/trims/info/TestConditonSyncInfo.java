package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class TestConditonSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer testConditonId;
    /**
     *
     */
    private Integer testConditionSeq;

    private Integer isDefault;

    /**
     * 标识Product Condition
     */
    private Integer isProductCondition;

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setTestConditonId(Integer testConditonId) {
        this.testConditonId = testConditonId;
    }

    public Integer getTestConditionSeq() {
        return testConditionSeq;
    }

    public void setTestConditionSeq(Integer testConditionSeq) {
        this.testConditionSeq = testConditionSeq;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public Integer getTestConditonId() {
        return testConditonId;
    }

    public Integer getIsProductCondition() {
        return isProductCondition;
    }

    public void setIsProductCondition(Integer isProductCondition) {
        this.isProductCondition = isProductCondition;
    }
}
