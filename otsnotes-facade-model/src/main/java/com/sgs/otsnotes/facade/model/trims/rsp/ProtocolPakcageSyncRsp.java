package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.ProtocolPakcageSyncInfo;

import java.util.List;

public class ProtocolPakcageSyncRsp extends TrimsSyncBaseRsp {
    /**
     *
     */
    private List<ProtocolPakcageSyncInfo> data;

    public List<ProtocolPakcageSyncInfo> getData() {
        return data;
    }

    public void setData(List<ProtocolPakcageSyncInfo> data) {
        this.data = data;
    }
}
