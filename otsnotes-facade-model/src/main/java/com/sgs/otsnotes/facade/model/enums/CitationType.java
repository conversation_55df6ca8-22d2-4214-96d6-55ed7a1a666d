package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum CitationType {
    None(0, "None"),
    Method(1, TrimsCacheType.CitationMethodInfo,"Method"),
    Regulation(2, TrimsCacheType.CitationRegulationInfo,"Regulation"),
    Standard(3, TrimsCacheType.CitationStandardInfo,"Standard"),
    ProtocolPackage(4,"ProtocolPackage");
    @DictCodeField
    private int type;
    private TrimsCacheType cacheType;
    @DictLabelField
    private String message;

    CitationType(int type, String message) {
        this.type = type;
        this.message = message;
    }

    CitationType(int type, TrimsCacheType cacheType, String message) {
        this(type, message);
        this.cacheType = cacheType;
    }

    public int getType() {
        return type;
    }

    public TrimsCacheType getCacheType() {
        return cacheType;
    }

    public String getMessage() {
        return message;
    }

    static Map<Integer, CitationType> maps = new HashMap<>();
    static Map<String, CitationType> nameMaps = new HashMap<>();

    static {
        for (CitationType type : CitationType.values()) {
            if (type.getType() <= 0){
                continue;
            }
            maps.put(type.getType(), type);
            nameMaps.put(type.getMessage(), type);
        }
    }

    public static CitationType findType(Integer type) {
        if (type == null || !maps.containsKey(type)){
            return null;
        }
        return maps.get(type);
    }

    public static CitationType findCitationType(String typeStr) {
        if (typeStr == null || !nameMaps.containsKey(typeStr)){
            return null;
        }
        return nameMaps.get(typeStr);
    }


    public static boolean check(Integer type, CitationType citationType) {
        if (type == null || !maps.containsKey(type)) {
            return false;
        }
        return maps.get(type) == citationType;
    }

    public static boolean check(Integer code, CitationType ...citationTypes ) {
        if (code == null || !maps.containsKey(code)){
            return false;
        }
        for (CitationType citationType : citationTypes) {
            if(code.compareTo(citationType.type)==0){
                return true;
            }
        }
        return false;
    }

}
