package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

/**
 * @Author: mingyang.chen
 * @Date: 2021/3/31 17:42
 */
public enum UsageTypeClient {
    /**
     * 默认配置0
     */
    TRIMS(0),
    /**
     * 1.用户手动添加
     */
    MANUAL(1),
    ;
    @DictCodeField
    private final int code;

    UsageTypeClient(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
