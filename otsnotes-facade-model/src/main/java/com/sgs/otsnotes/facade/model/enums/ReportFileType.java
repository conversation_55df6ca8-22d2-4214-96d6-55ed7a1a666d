package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum ReportFileType {
    Generate(1501, "Generate"), // final Word
    PDF(1502, "生成最终报告PDF"),  // final PDF
    DSS(1503, "生成PDF 传给DSS"),
    DraftPDF(1504, "Draft PDF"),   // Draft PDF
    TL_TEMPLATE_DOC(1505, "TL Tempalte doc"),
    SHOW_ALL_PICTURE(1506, "显示所有图片的DOC");

    @DictCodeField
    private final int code;
    @DictLabelField
    private final String message;

    ReportFileType(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String message() {
        return this.message;
    }

    public static ReportFileType getCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReportFileType fileType : ReportFileType.values()) {
            if (code.equals(fileType.getCode())) {
                return fileType;
            }
        }
        return null;
    }
    public static boolean check(Integer type,ReportFileType ... types){
        if(type==null || types ==null || types.length==0){
            return false;
        }
        for (ReportFileType reportFileType : types) {
            if(type.compareTo(reportFileType.code)==0){
                return true;
            }
        }
        return false;
    }
}
