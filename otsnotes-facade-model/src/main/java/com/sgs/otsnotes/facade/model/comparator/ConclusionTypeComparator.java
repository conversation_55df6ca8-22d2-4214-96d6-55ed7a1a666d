package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.rsp.conclusion.ConclusionTypeInfoRsp;

import java.util.Comparator;

public class ConclusionTypeComparator implements Comparator<ConclusionTypeInfoRsp> {
    /**
     * 是否为升序
     */
    private boolean isAsc;

    /**
     * @param isAsc
     */
    public ConclusionTypeComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(ConclusionTypeInfoRsp o1, ConclusionTypeInfoRsp o2) {
        Integer conclusionType1 = o1.getConclusionType();
        if (conclusionType1 == null) {
            conclusionType1 = 0;
        }
        Integer conclusionType2 = o2.getConclusionType();
        if (conclusionType2 == null) {
            conclusionType2 = 0;
        }
        int index = Integer.compare(conclusionType1, conclusionType2);
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }

}
