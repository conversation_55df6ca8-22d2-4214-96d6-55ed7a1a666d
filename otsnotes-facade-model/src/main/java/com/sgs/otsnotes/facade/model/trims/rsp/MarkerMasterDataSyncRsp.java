package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.MarkerMasterDataSyncInfo;

import java.util.List;

public class MarkerMasterDataSyncRsp extends TrimsSyncBaseRsp {
    /**
     *
     */
    private List<MarkerMasterDataSyncInfo> data;

    public List<MarkerMasterDataSyncInfo> getData() {
        return data;
    }

    public void setData(List<MarkerMasterDataSyncInfo> data) {
        this.data = data;
    }
}
