package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.ChkChemicalPPSyncInfo;
import com.sgs.otsnotes.facade.model.trims.info.DataMarkerSyncResInfo;

import java.util.List;

public class ChkChemicalPPSyncRsp extends TrimsSyncBaseRsp {
    /**
     *
     */
    private List<ChkChemicalPPSyncInfo> data;

    public List<ChkChemicalPPSyncInfo> getData() {
        return data;
    }

    public void setData(List<ChkChemicalPPSyncInfo> data) {
        this.data = data;
    }
}
