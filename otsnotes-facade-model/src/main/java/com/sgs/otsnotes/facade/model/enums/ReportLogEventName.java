package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum ReportLogEventName {
    Generate(1, "Generate"),
    Generate_Callback(2, "Generate_Callback"),
    Approve(3, "Approve"),
    Approve_Callback(4, "Approve_Callback"),
    Approve_ShowImage_Before(5, "Approve_ShowImage_Before"),
    Approve_ShowImage_After(6, "Approve_ShowImage_After"),
    Generate_Draft(7, "Generate_Draft"),
    Generate_Draft_Callback(8, "Generate_Draft_Callback"),
    Generate_Front_CoverPage(9, "Generate_Front_CoverPage"),
    ;

    private int code;
    private String message;

    ReportLogEventName(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, ReportLogEventName> maps = new HashMap<Integer, ReportLogEventName>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ReportLogEventName reportStatus : ReportLogEventName.values()) {
                put(reportStatus.getCode(), reportStatus);
            }
        }
    };

    public static ReportLogEventName getCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code.intValue());
    }

    public boolean check(ReportLogEventName... reportStatus){
        if (reportStatus == null || reportStatus.length <= 0){
            return false;
        }
        for (ReportLogEventName status: reportStatus){
            if (this.getCode() == status.getCode()){
                return true;
            }
        }
        return false;
    }

    public static boolean checkStatus(ReportLogEventName status, ReportLogEventName... reportStatus){
        if (status == null || reportStatus == null || reportStatus.length <= 0) {
            return false;
        }
        return check(status.getCode(), reportStatus);
    }

    public static boolean check(Integer status, ReportLogEventName... reportStatus){
        if (status == null || !maps.containsKey(status.intValue()) || reportStatus == null || reportStatus.length <= 0) {
            return false;
        }
        for (ReportLogEventName reportStatu: reportStatus){
            if (status.intValue() == reportStatu.getCode()){
                return true;
            }
        }
        return false;
    }
}
