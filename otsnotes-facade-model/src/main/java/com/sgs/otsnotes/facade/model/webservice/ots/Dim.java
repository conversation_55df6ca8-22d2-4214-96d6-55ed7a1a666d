/**
 * Dim.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class Dim  implements java.io.Serializable {
    private DimTypeEnum dimType;

    private int valueType;

    private String value;

    private String unit;

    public Dim() {
    }

    public Dim(
           DimTypeEnum dimType,
           int valueType,
           String value,
           String unit) {
           this.dimType = dimType;
           this.valueType = valueType;
           this.value = value;
           this.unit = unit;
    }


    /**
     * Gets the dimType value for this Dim.
     * 
     * @return dimType
     */
    public DimTypeEnum getDimType() {
        return dimType;
    }


    /**
     * Sets the dimType value for this Dim.
     * 
     * @param dimType
     */
    public void setDimType(DimTypeEnum dimType) {
        this.dimType = dimType;
    }


    /**
     * Gets the valueType value for this Dim.
     * 
     * @return valueType
     */
    public int getValueType() {
        return valueType;
    }


    /**
     * Sets the valueType value for this Dim.
     * 
     * @param valueType
     */
    public void setValueType(int valueType) {
        this.valueType = valueType;
    }


    /**
     * Gets the value value for this Dim.
     * 
     * @return value
     */
    public String getValue() {
        return value;
    }


    /**
     * Sets the value value for this Dim.
     * 
     * @param value
     */
    public void setValue(String value) {
        this.value = value;
    }


    /**
     * Gets the unit value for this Dim.
     * 
     * @return unit
     */
    public String getUnit() {
        return unit;
    }


    /**
     * Sets the unit value for this Dim.
     * 
     * @param unit
     */
    public void setUnit(String unit) {
        this.unit = unit;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof Dim)) {
            return false;
        }
        Dim other = (Dim) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.dimType==null && other.getDimType()==null) || 
             (this.dimType!=null &&
              this.dimType.equals(other.getDimType()))) &&
            this.valueType == other.getValueType() &&
            ((this.value==null && other.getValue()==null) || 
             (this.value!=null &&
              this.value.equals(other.getValue()))) &&
            ((this.unit==null && other.getUnit()==null) || 
             (this.unit!=null &&
              this.unit.equals(other.getUnit())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getDimType() != null) {
            _hashCode += getDimType().hashCode();
        }
        _hashCode += getValueType();
        if (getValue() != null) {
            _hashCode += getValue().hashCode();
        }
        if (getUnit() != null) {
            _hashCode += getUnit().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(Dim.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "Dim"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("dimType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DimType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DimTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("valueType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ValueType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("value");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Value"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("unit");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Unit"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
