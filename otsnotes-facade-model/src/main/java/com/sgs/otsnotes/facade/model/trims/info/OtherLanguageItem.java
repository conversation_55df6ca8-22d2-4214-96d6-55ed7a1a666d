package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class OtherLanguageItem extends PrintFriendliness {

    private Integer languageId;

    private String multiLaboratorySectionName;

    private String multiLaboratorySectionCode;

    private String multiLaboratorySectionOtherCode;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getMultiLaboratorySectionName() {
        return multiLaboratorySectionName;
    }

    public void setMultiLaboratorySectionName(String multiLaboratorySectionName) {
        this.multiLaboratorySectionName = multiLaboratorySectionName;
    }

    public String getMultiLaboratorySectionCode() {
        return multiLaboratorySectionCode;
    }

    public void setMultiLaboratorySectionCode(String multiLaboratorySectionCode) {
        this.multiLaboratorySectionCode = multiLaboratorySectionCode;
    }

    public String getMultiLaboratorySectionOtherCode() {
        return multiLaboratorySectionOtherCode;
    }

    public void setMultiLaboratorySectionOtherCode(String multiLaboratorySectionOtherCode) {
        this.multiLaboratorySectionOtherCode = multiLaboratorySectionOtherCode;
    }
}
