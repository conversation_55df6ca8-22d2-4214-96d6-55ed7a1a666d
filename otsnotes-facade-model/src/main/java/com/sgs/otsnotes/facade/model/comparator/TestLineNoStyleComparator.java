package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.common.NumberUtils;
import com.sgs.otsnotes.facade.model.info.testline.TestLinePpInfo;

import java.util.Comparator;

public class TestLineNoStyleComparator implements Comparator<TestLinePpInfo> {

    private boolean isAsc;

    public TestLineNoStyleComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }
    @Override
    public int compare(TestLinePpInfo o1, TestLinePpInfo o2) {
        if (o1.getIfNotPP() == null){
            o1.setIfNotPP(0);
        }
        if (o2.getIfNotPP() == null){
            o2.setIfNotPP(0);
        }
        if (o1.getPpNo() == null){
            o1.setPpNo(0);
        }
        if (o2.getPpNo() == null){
            o2.setPpNo(0);
        }
        if (o1.getEvaluationAlias() == null){
            o1.setEvaluationAlias("");
        }
        if (o2.getEvaluationAlias() == null){
            o2.setEvaluationAlias("");
        }
        if (o1.getPpOrderingSeq() == null){
            o1.setPpOrderingSeq(Integer.MAX_VALUE);
        }
        if (o2.getPpOrderingSeq() == null){
            o2.setPpOrderingSeq(Integer.MAX_VALUE);
        }
        int index = 0;

        index = o1.getIfNotPP().compareTo(o2.getIfNotPP());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        index = o1.getPpOrderingSeq().compareTo(o2.getPpOrderingSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        index = o1.getPpNo().compareTo(o2.getPpNo());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        index = o1.getEvaluationAlias().compareTo(o2.getEvaluationAlias());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        index = o1.getTestLineId().compareTo(o2.getTestLineId());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        return 0;
    }
}
