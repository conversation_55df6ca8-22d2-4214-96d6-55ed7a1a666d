package com.sgs.otsnotes.facade.model.common;

/**
 * 重试业务异常
 */
public class RetryBizException extends BizException{
    public RetryBizException(ResponseCode errorCode, String msg) {
        super(errorCode, msg);
    }

    public RetryBizException(String msg) {
        super(msg);
    }

    public RetryBizException(ResponseCode errorCode) {
        super(errorCode);
    }

    public RetryBizException(String msg, Throwable cause) {
        super(msg, cause);
    }

    public RetryBizException(ResponseCode errorCode, String msg, Throwable cause) {
        super(errorCode, msg, cause);
    }
}
