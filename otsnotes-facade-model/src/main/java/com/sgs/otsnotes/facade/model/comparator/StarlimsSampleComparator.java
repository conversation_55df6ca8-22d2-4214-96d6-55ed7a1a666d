package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.req.starlims.SubContractSampleInfo;

import java.util.Comparator;

public class StarlimsSampleComparator implements Comparator<SubContractSampleInfo> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public StarlimsSampleComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(SubContractSampleInfo o1, SubContractSampleInfo o2) {
        if (o1.getSampleSeq() == null){
            o1.setSampleSeq(0);
        }
        if (o2.getSampleSeq() == null){
            o2.setSampleSeq(0);
        }
        int index = Integer.compare(o1.getSampleSeq(), o2.getSampleSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
