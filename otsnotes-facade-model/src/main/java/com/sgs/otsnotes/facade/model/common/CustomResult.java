package com.sgs.otsnotes.facade.model.common;

/**
 * Created by <PERSON> on 2019/05/09.
 */
public class CustomResult<T> extends PrintFriendliness {
    private int status;
    private String resultMsg;
    private String message;

    public CustomResult(){

    }
    /**
     *
     * @param isSuccess
     */
    public CustomResult(boolean isSuccess){
        this.success = isSuccess;
    }

    public CustomResult(T data){
        this.data = data;
    }

    private boolean success;
    /**
     *
     */
    private T data;
    /**
     *
     */
    private boolean ignore;
    /**
     *
     */
    private int count;
    /**
     *
     */
    private long versionId;
    /**
     *
     */
    private String msg;
    /**
     *
     */
    private String stackTrace;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public boolean isIgnore() {
        return ignore;
    }

    public void setIgnore(boolean ignore) {
        this.ignore = ignore;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public long getVersionId() {
        return versionId;
    }

    public void setVersionId(long versionId) {
        this.versionId = versionId;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(String stackTrace) {
        this.stackTrace = stackTrace;
    }

    public static <T> CustomResult newSuccessInstance(){
        CustomResult result = new CustomResult<T>();
        result.setSuccess(true);
        return result;
    }

    public static <T> CustomResult newSuccessInstance(T data){
        CustomResult result = new CustomResult<T>();
        result.setSuccess(true);
        result.setData(data);
        return result;
    }

    public static <T> CustomResult failure(String message, Object ... args){
        CustomResult result = new CustomResult<T>();
        result.setSuccess(false);
        result.setMsg(String.format(message, args));
        return result;
    }

    public CustomResult data(T data) {
        this.setData(data);
        return this;
    }

    public CustomResult fail() {
        this.setSuccess(false);
        return this;
    }

    public CustomResult success() {
        this.setSuccess(true);
        return this;
    }

    public CustomResult fail(String message) {
        this.setMsg(message);
        this.setSuccess(false);
        return this;
    }
}
