package com.sgs.otsnotes.facade.model.trims.rsp.result;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public final class AnalyteResultLangInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer languageId;

    /**
     *
     */
    private String analyteResultType;

    /**
     *
     */
    private List<AnalyteResultOptionLangInfo> analyteResultOptions;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getAnalyteResultType() {
        return analyteResultType;
    }

    public void setAnalyteResultType(String analyteResultType) {
        this.analyteResultType = analyteResultType;
    }

    public List<AnalyteResultOptionLangInfo> getAnalyteResultOptions() {
        return analyteResultOptions;
    }

    public void setAnalyteResultOptions(List<AnalyteResultOptionLangInfo> analyteResultOptions) {
        this.analyteResultOptions = analyteResultOptions;
    }
}
