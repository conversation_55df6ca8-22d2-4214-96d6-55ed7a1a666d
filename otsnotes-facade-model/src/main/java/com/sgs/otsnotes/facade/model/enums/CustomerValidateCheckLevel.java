package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

public enum CustomerValidateCheckLevel {
    checkFail(1, "接口直接报错，前端表格形式提示客户"),
    checkRemind(2, "校验非终止，不影响数据操作,报错信息以表格形式提示，前端点击Close 后，页面刷新"),
    checkFilterPass(3, "根据校验接口中返回的数据 对入参的数据进行筛选，将符合条件的数据入库");
    private int type;
    private String desc;

    CustomerValidateCheckLevel(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getType() {
        return type;
    }

    static Map<Integer, CustomerValidateCheckLevel> typeMaps = new HashMap();
    static {
        for (CustomerValidateCheckLevel analyteType : CustomerValidateCheckLevel.values()) {
            typeMaps.put(analyteType.getType(), analyteType);
        }
    }

    public static CustomerValidateCheckLevel findType(Integer type) {
        if (type == null || !typeMaps.containsKey(type)) {
            return null;
        }
        return typeMaps.get(type);
    }

    /**
     *
     * @param type
     * @param analyteType
     * @return
     */
    public static boolean check(Integer type, CustomerValidateCheckLevel analyteType) {
        if (type == null || !typeMaps.containsKey(type)){
            return false;
        }
        return typeMaps.get(type) == analyteType;
    }

    public static boolean check(Integer analyteType) {
        if (analyteType == null){
            return false;
        }
        return typeMaps.containsKey(analyteType);
    }

    public static boolean checkLevel(CustomerValidateCheckLevel checkLevel) {
        if (checkLevel == null){
            return false;
        }
        return check(checkLevel.getType());
    }

}
