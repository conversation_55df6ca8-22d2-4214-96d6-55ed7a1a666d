package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum AnalyteType {
    Analyte(0, "Analyte"),
    LimitAnalyte(1, "Limit Analyte");
    @DictCodeField
    private int type;
    @DictLabelField
    private String code;

    AnalyteType(int type, String code) {
        this.type = type;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getType() {
        return type;
    }

    static Map<Integer, AnalyteType> typeMaps = new HashMap();
    static {
        for (AnalyteType analyteType : AnalyteType.values()) {
            typeMaps.put(analyteType.getType(), analyteType);
        }
    }

    public static AnalyteType findType(Integer type) {
        if (type == null || !typeMaps.containsKey(type)) {
            return null;
        }
        return typeMaps.get(type);
    }

    /**
     *
     * @param type
     * @param analyteType
     * @return
     */
    public static boolean check(Integer type, AnalyteType analyteType) {
        if (type == null || !typeMaps.containsKey(type)){
            return false;
        }
        return typeMaps.get(type) == analyteType;
    }

    public static boolean check(Integer analyteType) {
        if (analyteType == null){
            return false;
        }
        return typeMaps.containsKey(analyteType);
    }

}
