/**
 * DatasourceItem.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class DatasourceItem  implements java.io.Serializable {
    private int PKT;

    private int primaryKey;

    private int PGT;

    private int PG;

    private int parentPrimaryKey;

    private int parentSubPrimaryKey;

    private String sampleIDString;

    private String ID;

    private String name;

    private String description;

    private int sequence;

    private DataItem[] extendProperties;

    private long extendObjectId;

    public DatasourceItem() {
    }

    public DatasourceItem(
           int PKT,
           int primaryKey,
           int PGT,
           int PG,
           int parentPrimaryKey,
           int parentSubPrimaryKey,
           String sampleIDString,
           String ID,
           String name,
           String description,
           int sequence,
           DataItem[] extendProperties,
           long extendObjectId) {
           this.PKT = PKT;
           this.primaryKey = primaryKey;
           this.PGT = PGT;
           this.PG = PG;
           this.parentPrimaryKey = parentPrimaryKey;
           this.parentSubPrimaryKey = parentSubPrimaryKey;
           this.sampleIDString = sampleIDString;
           this.ID = ID;
           this.name = name;
           this.description = description;
           this.sequence = sequence;
           this.extendProperties = extendProperties;
           this.extendObjectId = extendObjectId;
    }


    /**
     * Gets the PKT value for this DatasourceItem.
     * 
     * @return PKT
     */
    public int getPKT() {
        return PKT;
    }


    /**
     * Sets the PKT value for this DatasourceItem.
     * 
     * @param PKT
     */
    public void setPKT(int PKT) {
        this.PKT = PKT;
    }


    /**
     * Gets the primaryKey value for this DatasourceItem.
     * 
     * @return primaryKey
     */
    public int getPrimaryKey() {
        return primaryKey;
    }


    /**
     * Sets the primaryKey value for this DatasourceItem.
     * 
     * @param primaryKey
     */
    public void setPrimaryKey(int primaryKey) {
        this.primaryKey = primaryKey;
    }


    /**
     * Gets the PGT value for this DatasourceItem.
     * 
     * @return PGT
     */
    public int getPGT() {
        return PGT;
    }


    /**
     * Sets the PGT value for this DatasourceItem.
     * 
     * @param PGT
     */
    public void setPGT(int PGT) {
        this.PGT = PGT;
    }


    /**
     * Gets the PG value for this DatasourceItem.
     * 
     * @return PG
     */
    public int getPG() {
        return PG;
    }


    /**
     * Sets the PG value for this DatasourceItem.
     * 
     * @param PG
     */
    public void setPG(int PG) {
        this.PG = PG;
    }


    /**
     * Gets the parentPrimaryKey value for this DatasourceItem.
     * 
     * @return parentPrimaryKey
     */
    public int getParentPrimaryKey() {
        return parentPrimaryKey;
    }


    /**
     * Sets the parentPrimaryKey value for this DatasourceItem.
     * 
     * @param parentPrimaryKey
     */
    public void setParentPrimaryKey(int parentPrimaryKey) {
        this.parentPrimaryKey = parentPrimaryKey;
    }


    /**
     * Gets the parentSubPrimaryKey value for this DatasourceItem.
     * 
     * @return parentSubPrimaryKey
     */
    public int getParentSubPrimaryKey() {
        return parentSubPrimaryKey;
    }


    /**
     * Sets the parentSubPrimaryKey value for this DatasourceItem.
     * 
     * @param parentSubPrimaryKey
     */
    public void setParentSubPrimaryKey(int parentSubPrimaryKey) {
        this.parentSubPrimaryKey = parentSubPrimaryKey;
    }


    /**
     * Gets the sampleIDString value for this DatasourceItem.
     * 
     * @return sampleIDString
     */
    public String getSampleIDString() {
        return sampleIDString;
    }


    /**
     * Sets the sampleIDString value for this DatasourceItem.
     * 
     * @param sampleIDString
     */
    public void setSampleIDString(String sampleIDString) {
        this.sampleIDString = sampleIDString;
    }


    /**
     * Gets the ID value for this DatasourceItem.
     * 
     * @return ID
     */
    public String getID() {
        return ID;
    }


    /**
     * Sets the ID value for this DatasourceItem.
     * 
     * @param ID
     */
    public void setID(String ID) {
        this.ID = ID;
    }


    /**
     * Gets the name value for this DatasourceItem.
     * 
     * @return name
     */
    public String getName() {
        return name;
    }


    /**
     * Sets the name value for this DatasourceItem.
     * 
     * @param name
     */
    public void setName(String name) {
        this.name = name;
    }


    /**
     * Gets the description value for this DatasourceItem.
     * 
     * @return description
     */
    public String getDescription() {
        return description;
    }


    /**
     * Sets the description value for this DatasourceItem.
     * 
     * @param description
     */
    public void setDescription(String description) {
        this.description = description;
    }


    /**
     * Gets the sequence value for this DatasourceItem.
     * 
     * @return sequence
     */
    public int getSequence() {
        return sequence;
    }


    /**
     * Sets the sequence value for this DatasourceItem.
     * 
     * @param sequence
     */
    public void setSequence(int sequence) {
        this.sequence = sequence;
    }


    /**
     * Gets the extendProperties value for this DatasourceItem.
     * 
     * @return extendProperties
     */
    public DataItem[] getExtendProperties() {
        return extendProperties;
    }


    /**
     * Sets the extendProperties value for this DatasourceItem.
     * 
     * @param extendProperties
     */
    public void setExtendProperties(DataItem[] extendProperties) {
        this.extendProperties = extendProperties;
    }


    /**
     * Gets the extendObjectId value for this DatasourceItem.
     * 
     * @return extendObjectId
     */
    public long getExtendObjectId() {
        return extendObjectId;
    }


    /**
     * Sets the extendObjectId value for this DatasourceItem.
     * 
     * @param extendObjectId
     */
    public void setExtendObjectId(long extendObjectId) {
        this.extendObjectId = extendObjectId;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof DatasourceItem)) {
            return false;
        }
        DatasourceItem other = (DatasourceItem) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.PKT == other.getPKT() &&
            this.primaryKey == other.getPrimaryKey() &&
            this.PGT == other.getPGT() &&
            this.PG == other.getPG() &&
            this.parentPrimaryKey == other.getParentPrimaryKey() &&
            this.parentSubPrimaryKey == other.getParentSubPrimaryKey() &&
            ((this.sampleIDString==null && other.getSampleIDString()==null) || 
             (this.sampleIDString!=null &&
              this.sampleIDString.equals(other.getSampleIDString()))) &&
            ((this.ID==null && other.getID()==null) || 
             (this.ID!=null &&
              this.ID.equals(other.getID()))) &&
            ((this.name==null && other.getName()==null) || 
             (this.name!=null &&
              this.name.equals(other.getName()))) &&
            ((this.description==null && other.getDescription()==null) || 
             (this.description!=null &&
              this.description.equals(other.getDescription()))) &&
            this.sequence == other.getSequence() &&
            ((this.extendProperties==null && other.getExtendProperties()==null) || 
             (this.extendProperties!=null &&
              java.util.Arrays.equals(this.extendProperties, other.getExtendProperties()))) &&
            this.extendObjectId == other.getExtendObjectId();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += getPKT();
        _hashCode += getPrimaryKey();
        _hashCode += getPGT();
        _hashCode += getPG();
        _hashCode += getParentPrimaryKey();
        _hashCode += getParentSubPrimaryKey();
        if (getSampleIDString() != null) {
            _hashCode += getSampleIDString().hashCode();
        }
        if (getID() != null) {
            _hashCode += getID().hashCode();
        }
        if (getName() != null) {
            _hashCode += getName().hashCode();
        }
        if (getDescription() != null) {
            _hashCode += getDescription().hashCode();
        }
        _hashCode += getSequence();
        if (getExtendProperties() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getExtendProperties());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getExtendProperties(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        _hashCode += new Long(getExtendObjectId()).hashCode();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(DatasourceItem.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DatasourceItem"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PKT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PKT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("primaryKey");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PrimaryKey"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PGT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PGT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("parentPrimaryKey");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ParentPrimaryKey"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("parentSubPrimaryKey");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ParentSubPrimaryKey"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sampleIDString");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SampleIDString"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("name");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Name"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("description");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Description"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sequence");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Sequence"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("extendProperties");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ExtendProperties"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("extendObjectId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ExtendObjectId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "long"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
