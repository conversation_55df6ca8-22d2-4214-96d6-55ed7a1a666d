package com.sgs.otsnotes.facade.model.ordercopy;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class NoCopyTestLineInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer testLineId;
    /**
     *
     */
    private String testLineName;
    /**
     *
     */
    private String type;
    /**
     *
     */
    private String reason;

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public String getTestLineName() {
        return testLineName;
    }

    public void setTestLineName(String testLineName) {
        this.testLineName = testLineName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
