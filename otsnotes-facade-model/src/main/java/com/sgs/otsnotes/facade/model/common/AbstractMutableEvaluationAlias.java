package com.sgs.otsnotes.facade.model.common;

import com.sgs.otsnotes.facade.model.annotation.DeclaredField;
import com.sgs.otsnotes.facade.model.annotation.TransHtmlField;

/**
 * 用于构造EvaluationAlias + PP_Notes
 * <AUTHOR>
 * @date 2020/9/27 19:02
 */
public abstract class AbstractMutableEvaluationAlias {
    private Long ppArtifactRelId;
    @DeclaredField
    private String testLineInstanceId;
    @DeclaredField
    private String evaluationAlias;
    @TransHtmlField
    private String ppNotes;

    public Long getPpArtifactRelId() {
        return ppArtifactRelId;
    }

    public void setPpArtifactRelId(Long ppArtifactRelId) {
        this.ppArtifactRelId = ppArtifactRelId;
    }

    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId;
    }

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }

    public String getPpNotes() {
        return ppNotes;
    }

    public void setPpNotes(String ppNotes) {
        this.ppNotes = ppNotes;
    }
}
