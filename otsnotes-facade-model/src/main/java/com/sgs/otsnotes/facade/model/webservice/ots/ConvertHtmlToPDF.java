/**
 * ConvertHtmlToPDF.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ConvertHtmlToPDF  implements java.io.Serializable {
    private String html;

    private String pdfFilePath;

    public ConvertHtmlToPDF() {
    }

    public ConvertHtmlToPDF(
           String html,
           String pdfFilePath) {
           this.html = html;
           this.pdfFilePath = pdfFilePath;
    }


    /**
     * Gets the html value for this ConvertHtmlToPDF.
     * 
     * @return html
     */
    public String getHtml() {
        return html;
    }


    /**
     * Sets the html value for this ConvertHtmlToPDF.
     * 
     * @param html
     */
    public void setHtml(String html) {
        this.html = html;
    }


    /**
     * Gets the pdfFilePath value for this ConvertHtmlToPDF.
     * 
     * @return pdfFilePath
     */
    public String getPdfFilePath() {
        return pdfFilePath;
    }


    /**
     * Sets the pdfFilePath value for this ConvertHtmlToPDF.
     * 
     * @param pdfFilePath
     */
    public void setPdfFilePath(String pdfFilePath) {
        this.pdfFilePath = pdfFilePath;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ConvertHtmlToPDF)) {
            return false;
        }
        ConvertHtmlToPDF other = (ConvertHtmlToPDF) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.html==null && other.getHtml()==null) || 
             (this.html!=null &&
              this.html.equals(other.getHtml()))) &&
            ((this.pdfFilePath==null && other.getPdfFilePath()==null) || 
             (this.pdfFilePath!=null &&
              this.pdfFilePath.equals(other.getPdfFilePath())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getHtml() != null) {
            _hashCode += getHtml().hashCode();
        }
        if (getPdfFilePath() != null) {
            _hashCode += getPdfFilePath().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ConvertHtmlToPDF.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">ConvertHtmlToPDF"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("html");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "html"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("pdfFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "pdfFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
