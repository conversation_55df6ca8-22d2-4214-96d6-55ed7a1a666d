/**
 * TemplateInfo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class TemplateInfo  implements java.io.Serializable {
    private int templateId;

    private int templateType;

    private String templateFilePath;

    private String configFilePath;

    private String WIFilePath;

    public TemplateInfo() {
    }

    public TemplateInfo(
           int templateId,
           int templateType,
           String templateFilePath,
           String configFilePath,
           String WIFilePath) {
           this.templateId = templateId;
           this.templateType = templateType;
           this.templateFilePath = templateFilePath;
           this.configFilePath = configFilePath;
           this.WIFilePath = WIFilePath;
    }


    /**
     * Gets the templateId value for this TemplateInfo.
     * 
     * @return templateId
     */
    public int getTemplateId() {
        return templateId;
    }


    /**
     * Sets the templateId value for this TemplateInfo.
     * 
     * @param templateId
     */
    public void setTemplateId(int templateId) {
        this.templateId = templateId;
    }


    /**
     * Gets the templateType value for this TemplateInfo.
     * 
     * @return templateType
     */
    public int getTemplateType() {
        return templateType;
    }


    /**
     * Sets the templateType value for this TemplateInfo.
     * 
     * @param templateType
     */
    public void setTemplateType(int templateType) {
        this.templateType = templateType;
    }


    /**
     * Gets the templateFilePath value for this TemplateInfo.
     * 
     * @return templateFilePath
     */
    public String getTemplateFilePath() {
        return templateFilePath;
    }


    /**
     * Sets the templateFilePath value for this TemplateInfo.
     * 
     * @param templateFilePath
     */
    public void setTemplateFilePath(String templateFilePath) {
        this.templateFilePath = templateFilePath;
    }


    /**
     * Gets the configFilePath value for this TemplateInfo.
     * 
     * @return configFilePath
     */
    public String getConfigFilePath() {
        return configFilePath;
    }


    /**
     * Sets the configFilePath value for this TemplateInfo.
     * 
     * @param configFilePath
     */
    public void setConfigFilePath(String configFilePath) {
        this.configFilePath = configFilePath;
    }


    /**
     * Gets the WIFilePath value for this TemplateInfo.
     * 
     * @return WIFilePath
     */
    public String getWIFilePath() {
        return WIFilePath;
    }


    /**
     * Sets the WIFilePath value for this TemplateInfo.
     * 
     * @param WIFilePath
     */
    public void setWIFilePath(String WIFilePath) {
        this.WIFilePath = WIFilePath;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof TemplateInfo)) {
            return false;
        }
        TemplateInfo other = (TemplateInfo) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.templateId == other.getTemplateId() &&
            this.templateType == other.getTemplateType() &&
            ((this.templateFilePath==null && other.getTemplateFilePath()==null) || 
             (this.templateFilePath!=null &&
              this.templateFilePath.equals(other.getTemplateFilePath()))) &&
            ((this.configFilePath==null && other.getConfigFilePath()==null) || 
             (this.configFilePath!=null &&
              this.configFilePath.equals(other.getConfigFilePath()))) &&
            ((this.WIFilePath==null && other.getWIFilePath()==null) || 
             (this.WIFilePath!=null &&
              this.WIFilePath.equals(other.getWIFilePath())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += getTemplateId();
        _hashCode += getTemplateType();
        if (getTemplateFilePath() != null) {
            _hashCode += getTemplateFilePath().hashCode();
        }
        if (getConfigFilePath() != null) {
            _hashCode += getConfigFilePath().hashCode();
        }
        if (getWIFilePath() != null) {
            _hashCode += getWIFilePath().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(TemplateInfo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInfo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("configFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ConfigFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("WIFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "WIFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
