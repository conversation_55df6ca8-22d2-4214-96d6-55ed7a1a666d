package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.dto.TestLineLimitsDTO;

public class TestLineLimitComparator extends AbstractComparator<TestLineLimitsDTO> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public TestLineLimitComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(TestLineLimitsDTO o1, TestLineLimitsDTO o2) {
        int index = Integer.compare(this.toInt(o1.getPpNoHide()), this.toInt(o2.getPpNoHide()));
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        index = Integer.compare(this.toInt(o1.getTestConditionSeq()), this.toInt(o2.getTestConditionSeq()));
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        index = Integer.compare(this.toInt(o1.getTestAnalyteSeq()), this.toInt(o2.getTestAnalyteSeq()));
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
