/**
 * ExtractTestReportData.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ExtractTestReportData  implements java.io.Serializable {
    private TemplateInstanceInfo reportInfo;

    public ExtractTestReportData() {
    }

    public ExtractTestReportData(
           TemplateInstanceInfo reportInfo) {
           this.reportInfo = reportInfo;
    }


    /**
     * Gets the reportInfo value for this ExtractTestReportData.
     * 
     * @return reportInfo
     */
    public TemplateInstanceInfo getReportInfo() {
        return reportInfo;
    }


    /**
     * Sets the reportInfo value for this ExtractTestReportData.
     * 
     * @param reportInfo
     */
    public void setReportInfo(TemplateInstanceInfo reportInfo) {
        this.reportInfo = reportInfo;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ExtractTestReportData)) {
            return false;
        }
        ExtractTestReportData other = (ExtractTestReportData) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.reportInfo==null && other.getReportInfo()==null) || 
             (this.reportInfo!=null &&
              this.reportInfo.equals(other.getReportInfo())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getReportInfo() != null) {
            _hashCode += getReportInfo().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ExtractTestReportData.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">ExtractTestReportData"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("reportInfo");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "reportInfo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInstanceInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
