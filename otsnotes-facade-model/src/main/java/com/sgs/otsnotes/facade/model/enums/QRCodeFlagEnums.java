package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

/**
 * <AUTHOR>
 * qrcode的 定义枚举
 */

@Dict
public enum QRCodeFlagEnums {
    inactive(0),
    active(1);
    @DictCodeField
    private Integer flag;
    QRCodeFlagEnums(Integer flag){
        this.flag = flag;
    }

    public Integer getFlag() {
        return flag;
    }

    public static boolean check(Integer flag,QRCodeFlagEnums ... enmus){
        if(flag == null){
            return  false;
        }
        for (QRCodeFlagEnums qrCodeFlagEnums : enmus) {
            if(qrCodeFlagEnums.flag.compareTo(flag)==0){
                return true;
            }
        }
        return false;
    }
}
