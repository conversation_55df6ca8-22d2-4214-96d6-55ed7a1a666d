package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.rsp.trims.TrimsLimitGroupRsp;

import java.util.Comparator;

public class LimitGroupComparator implements Comparator<TrimsLimitGroupRsp> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public LimitGroupComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(TrimsLimitGroupRsp o1, TrimsLimitGroupRsp o2) {
        int index = Integer.compare(o1.getTestLimitGroupTypeId(), o2.getTestLimitGroupTypeId());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        if (o1.getTestLimitGroupId() == null){
            o1.setTestLimitGroupId(0);
        }
        if (o2.getTestLimitGroupId() == null){
            o2.setTestLimitGroupId(0);
        }
        index = Integer.compare(o1.getTestLimitGroupId(), o2.getTestLimitGroupId());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
