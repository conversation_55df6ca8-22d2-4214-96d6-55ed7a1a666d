package com.sgs.otsnotes.facade.model.trims.info;

public class PricingTestLineAndProtocolsSyncInfo {

    /**
     *
     */
    private Integer aid;
    /**
     *
     */
    private String artifactType;
    /**
     *
     */
    private Integer ppVersionIdentifier;

    /**
     *
     */
    private Integer artifactVersionIdentifier;
    /**
     *
     */
    private Integer sequence;
    /**
     *
     */
    private Integer priceLimit;
    /**
     *
     */
    private String remark;

    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }

    public String getArtifactType() {
        return artifactType;
    }

    public void setArtifactType(String artifactType) {
        this.artifactType = artifactType;
    }

    public Integer getPpVersionIdentifier() {
        return ppVersionIdentifier;
    }

    public void setPpVersionIdentifier(Integer ppVersionIdentifier) {
        this.ppVersionIdentifier = ppVersionIdentifier;
    }

    public Integer getArtifactVersionIdentifier() {
        return artifactVersionIdentifier;
    }

    public void setArtifactVersionIdentifier(Integer artifactVersionIdentifier) {
        this.artifactVersionIdentifier = artifactVersionIdentifier;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Integer getPriceLimit() {
        return priceLimit;
    }

    public void setPriceLimit(Integer priceLimit) {
        this.priceLimit = priceLimit;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
