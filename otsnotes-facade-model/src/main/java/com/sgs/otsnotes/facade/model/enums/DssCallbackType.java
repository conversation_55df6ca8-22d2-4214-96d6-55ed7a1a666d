package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum DssCallbackType {
    NotResponse(0, "请求未响应."),
    Fail(1, "失败."),
    Success(2, "成功.");

    private int status;
    private String message;

    DssCallbackType(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public int getStatus() {
        return status;
    }

    public String getMessage() {
        return this.message;
    }

    static Map<Integer, DssCallbackType> maps = new HashMap<>();

    static {
        for (DssCallbackType type : DssCallbackType.values()) {
            maps.put(type.getStatus(), type);
        }
    }

    public static DssCallbackType findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code);
    }

    public static boolean check(Integer status) {
        if (status == null){
            return false;
        }
        return maps.containsKey(status.intValue());
    }
}
