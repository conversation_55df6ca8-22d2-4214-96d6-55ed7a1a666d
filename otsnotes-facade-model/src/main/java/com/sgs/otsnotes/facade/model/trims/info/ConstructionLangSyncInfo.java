package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class ConstructionLangSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Long aid;
    /**
     *
     */
    private String citationName;
    /**
     *
     */
    private String citationItemName;
    /**
     *
     */
    private String citationSectionName;
    /**
     *
     *
     */
    private String evaluationAlias;
    /**
     *
     */
    private String specificNotes;
    /**
     *
     */
    private String reportReferenceNoteAlias;
    /**
     *
     */
    private String ppNotesAlias;
    /**
     *
     */
    private String conditionInstruction;

    /**
     * 没有计入MD5的计算
     */
    private String citationPpStandardSection;

    /**
     *
     */
    private List<PpTestlineAnalyteLangSyncInfo> testAnalytes;

    public Long getAid() {
        return aid;
    }

    public void setAid(Long aid) {
        this.aid = aid;
    }

    public String getCitationName() {
        return citationName;
    }

    public void setCitationName(String citationName) {
        this.citationName = citationName;
    }

    public String getCitationItemName() {
        return citationItemName;
    }

    public void setCitationItemName(String citationItemName) {
        this.citationItemName = citationItemName;
    }

    public String getCitationSectionName() {
        return citationSectionName;
    }

    public void setCitationSectionName(String citationSectionName) {
        this.citationSectionName = citationSectionName;
    }

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }

    public String getSpecificNotes() {
        return specificNotes;
    }

    public void setSpecificNotes(String specificNotes) {
        this.specificNotes = specificNotes;
    }

    public String getReportReferenceNoteAlias() {
        return reportReferenceNoteAlias;
    }

    public void setReportReferenceNoteAlias(String reportReferenceNoteAlias) {
        this.reportReferenceNoteAlias = reportReferenceNoteAlias;
    }

    public String getPpNotesAlias() {
        return ppNotesAlias;
    }

    public void setPpNotesAlias(String ppNotesAlias) {
        this.ppNotesAlias = ppNotesAlias;
    }

    public String getConditionInstruction() {
        return conditionInstruction;
    }

    public void setConditionInstruction(String conditionInstruction) {
        this.conditionInstruction = conditionInstruction;
    }

    public String getCitationPpStandardSection() {
        return citationPpStandardSection;
    }

    public void setCitationPpStandardSection(String citationPpStandardSection) {
        this.citationPpStandardSection = citationPpStandardSection;
    }

    public List<PpTestlineAnalyteLangSyncInfo> getTestAnalytes() {
        return testAnalytes;
    }

    public void setTestAnalytes(List<PpTestlineAnalyteLangSyncInfo> testAnalytes) {
        this.testAnalytes = testAnalytes;
    }
}

