package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.enums.ConclusionType;
import com.sgs.otsnotes.facade.model.info.matrix.TestLineMatrixRelInfo;

import java.util.Comparator;

public class TestLineMatrixRelComparator implements Comparator<TestLineMatrixRelInfo> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     * 是否为升序
     */
    private ConclusionType conclusionType;
    /**
     *
     */
    public TestLineMatrixRelComparator() {
        this.isAsc = true;
    }
    /**
     *
     * @param conclusionType
     */
    public TestLineMatrixRelComparator(ConclusionType conclusionType) {
        this();
        this.conclusionType = conclusionType;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(TestLineMatrixRelInfo o1, TestLineMatrixRelInfo o2) {
        if (this.conclusionType != null && this.conclusionType.check(ConclusionType.Report, ConclusionType.Section)){
            if (o1.getTestLineId() == null){
                o1.setTestLineId(0);
            }
            if (o2.getTestLineId() == null){
                o2.setTestLineId(0);
            }
            int index = Integer.compare(o1.getTestLineId(), o2.getTestLineId());
            if (index < 0) {
                return isAsc ? -1 : 1;
            }
            if (index > 0) {
                return isAsc ? 1 : -1;
            }
        }
        if (o1.getSampleSeq() == null){
            o1.setSampleSeq(Integer.MAX_VALUE-1);
        }
        if (o2.getSampleSeq() == null){
            o2.setSampleSeq(Integer.MAX_VALUE-1);
        }
        int index = Integer.compare(o1.getSampleSeq(), o2.getSampleSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
