package com.sgs.otsnotes.facade.model.common;

public class NumberUtils {
    /**
     *
     * @param intValue
     * @return
     */
    public static int toInt(Integer intValue) {
        return intValue == null ? 0 : intValue.intValue();
    }

    /**
     *
     * @param str
     * @return
     */
    public static int toInt(String str) {
        return toInt(str,0);
    }

    /**
     *
     * @param intValue
     * @param defValue
     * @return
     */
    public static int toInt(Integer intValue, Integer defValue) {
        if (intValue == null || intValue.intValue() <= 0) {
            return toInt(defValue);
        }
        return intValue.intValue();
    }

    /**
     *
     * @param str
     * @param defValue
     * @return
     */
    public static int toInt(String str, int defValue) {
        if (str == null) {
            return defValue;
        }
        try {
            return Integer.parseInt(str);
        } catch (NumberFormatException var3) {
            return defValue;
        }
    }

    /**
     *
     * @param bool
     * @return
     */
    public static int toInt(Boolean bool) {
        if (bool == null) {
            return 0;
        }
        return bool ? 1 : 0;
    }
}
