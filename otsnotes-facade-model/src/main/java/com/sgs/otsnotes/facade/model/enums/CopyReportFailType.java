package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum CopyReportFailType {
    Limit(0, "Sample %s 关联的 TL %s 已经有Limit记录"),
    Analyte(1, "Sample %s 关联的 TL %s Analyte 不一致"),
    Conclusion(2, "Sample %s 关联的 TL %s 已经有Conclusion记录"),
    SourceTLStatus(3, "Source Sample %s 关联的 TL %s 状态为%s，无法Copy"),
    ReferTLStatus(4, "Refer Sample %s 关联的 TL %s 状态为%s，无法Copy"),
    SourceTLRepeat(5, "Source Sample %s 关联的 TL %s 状态为%s 重复，无法Copy");
    @DictCodeField
    private int status;
    private String format;

    CopyReportFailType(int status, String format) {
        this.status = status;
        this.format = format;
    }

    public int getStatus() {
        return status;
    }

    public String getFormat() {
        return format;
    }

    static Map<Integer, CopyReportFailType> maps = new HashMap<>();
    static {
        for (CopyReportFailType type : CopyReportFailType.values()) {
            maps.put(type.getStatus(), type);
        }
    }

    public static CopyReportFailType findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code);
    }

    public static boolean check(Integer status) {
        if (status == null){
            return false;
        }
        return maps.containsKey(status.intValue());
    }

}
