package com.sgs.otsnotes.facade.model.kafka;

import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.otsnotes.facade.model.info.SubContractExternalRelationshipInfo;
import com.sgs.otsnotes.facade.model.info.SubContractTestLineInfo;
import com.sgs.otsnotes.facade.model.info.SubContractTestLineMappingInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.po.SubcontractRelInfoPO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/6 18:03
 */
public class UpdateSubcontractInfoMessage extends BaseProductLine {
    private static final long serialVersionUID = 932346385704794908L;

    private SubContractTestLineInfo testLineInfo;

    private SubContractInfo subContractPO;

    private SubContractExternalRelationshipInfo subContractExternalRelationshipInfo;

    private List<SubContractTestLineMappingInfo> subContractTestLineMappingInfos;

    private List<SubcontractRelInfoPO> subcontractRelInfoPOS;

    private List<SubcontractRelInfoPO> needUpdateRels;

    private List<Long> delSubcontractRelMappingIds;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public SubContractTestLineInfo getTestLineInfo() {
        return testLineInfo;
    }

    public void setTestLineInfo(SubContractTestLineInfo testLineInfo) {
        this.testLineInfo = testLineInfo;
    }

    public SubContractInfo getSubContractPO() {
        return subContractPO;
    }

    public void setSubContractPO(SubContractInfo subContractPO) {
        this.subContractPO = subContractPO;
    }

    public SubContractExternalRelationshipInfo getSubContractExternalRelationshipInfo() {
        return subContractExternalRelationshipInfo;
    }

    public void setSubContractExternalRelationshipInfo(SubContractExternalRelationshipInfo subContractExternalRelationshipInfo) {
        this.subContractExternalRelationshipInfo = subContractExternalRelationshipInfo;
    }

    public List<SubContractTestLineMappingInfo> getSubContractTestLineMappingInfos() {
        return subContractTestLineMappingInfos;
    }

    public void setSubContractTestLineMappingInfos(List<SubContractTestLineMappingInfo> subContractTestLineMappingInfos) {
        this.subContractTestLineMappingInfos = subContractTestLineMappingInfos;
    }

    public List<SubcontractRelInfoPO> getSubcontractRelInfoPOS() {
        return subcontractRelInfoPOS;
    }

    public void setSubcontractRelInfoPOS(List<SubcontractRelInfoPO> subcontractRelInfoPOS) {
        this.subcontractRelInfoPOS = subcontractRelInfoPOS;
    }

    public List<SubcontractRelInfoPO> getNeedUpdateRels() {
        return needUpdateRels;
    }

    public void setNeedUpdateRels(List<SubcontractRelInfoPO> needUpdateRels) {
        this.needUpdateRels = needUpdateRels;
    }

    public List<Long> getDelSubcontractRelMappingIds() {
        return delSubcontractRelMappingIds;
    }

    public void setDelSubcontractRelMappingIds(List<Long> delSubcontractRelMappingIds) {
        this.delSubcontractRelMappingIds = delSubcontractRelMappingIds;
    }
}
