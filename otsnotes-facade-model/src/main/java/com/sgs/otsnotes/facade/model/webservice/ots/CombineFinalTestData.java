/**
 * CombineFinalTestData.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class CombineFinalTestData  implements java.io.Serializable {
    private TemplateInstanceInfo worksheetInfo;

    private String[] sourceServerFiles;

    private String targetServerFile;

    public CombineFinalTestData() {
    }

    public CombineFinalTestData(
           TemplateInstanceInfo worksheetInfo,
           String[] sourceServerFiles,
           String targetServerFile) {
           this.worksheetInfo = worksheetInfo;
           this.sourceServerFiles = sourceServerFiles;
           this.targetServerFile = targetServerFile;
    }


    /**
     * Gets the worksheetInfo value for this CombineFinalTestData.
     * 
     * @return worksheetInfo
     */
    public TemplateInstanceInfo getWorksheetInfo() {
        return worksheetInfo;
    }


    /**
     * Sets the worksheetInfo value for this CombineFinalTestData.
     * 
     * @param worksheetInfo
     */
    public void setWorksheetInfo(TemplateInstanceInfo worksheetInfo) {
        this.worksheetInfo = worksheetInfo;
    }


    /**
     * Gets the sourceServerFiles value for this CombineFinalTestData.
     * 
     * @return sourceServerFiles
     */
    public String[] getSourceServerFiles() {
        return sourceServerFiles;
    }


    /**
     * Sets the sourceServerFiles value for this CombineFinalTestData.
     * 
     * @param sourceServerFiles
     */
    public void setSourceServerFiles(String[] sourceServerFiles) {
        this.sourceServerFiles = sourceServerFiles;
    }


    /**
     * Gets the targetServerFile value for this CombineFinalTestData.
     * 
     * @return targetServerFile
     */
    public String getTargetServerFile() {
        return targetServerFile;
    }


    /**
     * Sets the targetServerFile value for this CombineFinalTestData.
     * 
     * @param targetServerFile
     */
    public void setTargetServerFile(String targetServerFile) {
        this.targetServerFile = targetServerFile;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof CombineFinalTestData)) {
            return false;
        }
        CombineFinalTestData other = (CombineFinalTestData) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.worksheetInfo==null && other.getWorksheetInfo()==null) || 
             (this.worksheetInfo!=null &&
              this.worksheetInfo.equals(other.getWorksheetInfo()))) &&
            ((this.sourceServerFiles==null && other.getSourceServerFiles()==null) || 
             (this.sourceServerFiles!=null &&
              java.util.Arrays.equals(this.sourceServerFiles, other.getSourceServerFiles()))) &&
            ((this.targetServerFile==null && other.getTargetServerFile()==null) || 
             (this.targetServerFile!=null &&
              this.targetServerFile.equals(other.getTargetServerFile())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getWorksheetInfo() != null) {
            _hashCode += getWorksheetInfo().hashCode();
        }
        if (getSourceServerFiles() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getSourceServerFiles());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getSourceServerFiles(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getTargetServerFile() != null) {
            _hashCode += getTargetServerFile().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(CombineFinalTestData.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">CombineFinalTestData"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("worksheetInfo");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "worksheetInfo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInstanceInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sourceServerFiles");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "sourceServerFiles"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "string"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("targetServerFile");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "targetServerFile"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
