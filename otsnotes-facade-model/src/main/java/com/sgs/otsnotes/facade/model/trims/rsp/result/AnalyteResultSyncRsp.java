package com.sgs.otsnotes.facade.model.trims.rsp.result;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public final class AnalyteResultSyncRsp extends PrintFriendliness {

    /**
     *
     */
    private List<AnalyteResultInfo> data;

    public List<AnalyteResultInfo> getData() {
        return data;
    }

    public void setData(List<AnalyteResultInfo> data) {
        this.data = data;
    }
}
