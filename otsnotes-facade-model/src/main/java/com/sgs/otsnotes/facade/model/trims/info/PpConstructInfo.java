package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class PpConstructInfo extends PrintFriendliness {
    /**
     *
     */
    private Long ppBaseId;
    /**
     *
     */
    private Integer ppVersionId;

    /**
     *
     */
    private String ppName;

    /**
     * 对应 Trims字段：CSPP
     */
    private Integer ppType;

    /**
     *
     */
    private List<Integer> testStandardVersionIds;
    /**
     *
     */
    private List<Integer> regulationVersionIds;
    /**
     *
     */
    private List<PpConstructSyncInfo> ppConstructs;
    /**
     *
     */
    private List<ProtocolPakcageLangSyncInfo> languages;

    public Long getPpBaseId() {
        return ppBaseId;
    }

    public void setPpBaseId(Long ppBaseId) {
        this.ppBaseId = ppBaseId;
    }

    public Integer getPpVersionId() {
        return ppVersionId;
    }

    public void setPpVersionId(Integer ppVersionId) {
        this.ppVersionId = ppVersionId;
    }

    public String getPpName() {
        return ppName;
    }

    public void setPpName(String ppName) {
        this.ppName = ppName;
    }

    public Integer getPpType() {
        return ppType;
    }

    public void setPpType(Integer ppType) {
        this.ppType = ppType;
    }

    public List<Integer> getTestStandardVersionIds() {
        return testStandardVersionIds;
    }

    public void setTestStandardVersionIds(List<Integer> testStandardVersionIds) {
        this.testStandardVersionIds = testStandardVersionIds;
    }

    public List<Integer> getRegulationVersionIds() {
        return regulationVersionIds;
    }

    public void setRegulationVersionIds(List<Integer> regulationVersionIds) {
        this.regulationVersionIds = regulationVersionIds;
    }

    public List<PpConstructSyncInfo> getPpConstructs() {
        return ppConstructs;
    }

    public void setPpConstructs(List<PpConstructSyncInfo> ppConstructs) {
        this.ppConstructs = ppConstructs;
    }

    public List<ProtocolPakcageLangSyncInfo> getLanguages() {
        return languages;
    }

    public void setLanguages(List<ProtocolPakcageLangSyncInfo> languages) {
        this.languages = languages;
    }
}
