package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum ConclusionColumnType {

    Text(0,"Text"),
    Select(1,"Select"),
    Input(2, "InputRemark");
    @DictCodeField
    private int type;
    @DictLabelField
    private String name;

    ConclusionColumnType(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static final Map<Integer, ConclusionColumnType> maps = new HashMap<Integer, ConclusionColumnType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ConclusionColumnType headType : ConclusionColumnType.values()) {
                put(headType.getType(), headType);
            }
        }
    };

    public static ConclusionColumnType findType(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())) {
            return ConclusionColumnType.Text;
        }
        return maps.get(code);
    }

    public static boolean check(Integer code, ConclusionColumnType conclusionMode) {
        if (code == null || !maps.containsKey(code.intValue())){
            return false;
        }
        return maps.get(code.intValue()) == conclusionMode;
    }

    public boolean check(ConclusionColumnType... conclusionHeadTypes) {
        if (conclusionHeadTypes == null || conclusionHeadTypes.length <= 0){
            return false;
        }
        for (ConclusionColumnType columnType : conclusionHeadTypes) {
            if (this.getType() == columnType.getType()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param code
     * @param conclusionMode
     * @return
     */
    public static boolean check(Integer code, Integer conclusionMode) {
        return check(code, ConclusionColumnType.findType(conclusionMode));
    }

}
