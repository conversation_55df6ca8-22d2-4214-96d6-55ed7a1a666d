package com.sgs.otsnotes.facade.model.trims.rsp.accreditation;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

/**
 * @ClassName AccreditationSyncInfo
 * @Description 同步labSection
 * <AUTHOR>
 * @Date 12/24/2020
 */

public class AccreditationsSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer accId;
    /**
     *
     */
    private Integer isDeleted;
    /**
     *
     */
    private Integer tlVersionIdentifier;
    /**
     *
     */
    private Integer laboratoryId;

    /**
     *
     */
    private List<Integer> testAnalyteIds;

}
