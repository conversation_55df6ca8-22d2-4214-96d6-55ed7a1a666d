package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class RegulationLangSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer languageId;
    /**
     *
     */
    private String regulationName;
    /**
     *
     */
    private String regulationShortName;
    /**
     *
     */
    private List<SectionSyncInfo> sectionItems;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getRegulationName() {
        return regulationName;
    }

    public void setRegulationName(String regulationName) {
        this.regulationName = regulationName;
    }

    public String getRegulationShortName() {
        return regulationShortName;
    }

    public void setRegulationShortName(String regulationShortName) {
        this.regulationShortName = regulationShortName;
    }

    public List<SectionSyncInfo> getSectionItems() {
        return sectionItems;
    }

    public void setSectionItems(List<SectionSyncInfo> sectionItems) {
        this.sectionItems = sectionItems;
    }
}
