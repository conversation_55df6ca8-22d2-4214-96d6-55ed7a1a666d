/**
 * JS_ChapterTableRow.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_ChapterTableRow  extends Element implements java.io.Serializable {
    private String chapterSerial;

    private PhysicalChapterTableRowType rowType;

    private String clauseRowFlag;

    public JS_ChapterTableRow() {
    }

    public JS_ChapterTableRow(
           String id,
           String name,
           String chapterSerial,
           PhysicalChapterTableRowType rowType,
           String clauseRowFlag) {
        super(
            id,
            name);
        this.chapterSerial = chapterSerial;
        this.rowType = rowType;
        this.clauseRowFlag = clauseRowFlag;
    }


    /**
     * Gets the chapterSerial value for this JS_ChapterTableRow.
     * 
     * @return chapterSerial
     */
    public String getChapterSerial() {
        return chapterSerial;
    }


    /**
     * Sets the chapterSerial value for this JS_ChapterTableRow.
     * 
     * @param chapterSerial
     */
    public void setChapterSerial(String chapterSerial) {
        this.chapterSerial = chapterSerial;
    }


    /**
     * Gets the rowType value for this JS_ChapterTableRow.
     * 
     * @return rowType
     */
    public PhysicalChapterTableRowType getRowType() {
        return rowType;
    }


    /**
     * Sets the rowType value for this JS_ChapterTableRow.
     * 
     * @param rowType
     */
    public void setRowType(PhysicalChapterTableRowType rowType) {
        this.rowType = rowType;
    }


    /**
     * Gets the clauseRowFlag value for this JS_ChapterTableRow.
     * 
     * @return clauseRowFlag
     */
    public String getClauseRowFlag() {
        return clauseRowFlag;
    }


    /**
     * Sets the clauseRowFlag value for this JS_ChapterTableRow.
     * 
     * @param clauseRowFlag
     */
    public void setClauseRowFlag(String clauseRowFlag) {
        this.clauseRowFlag = clauseRowFlag;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_ChapterTableRow)) {
            return false;
        }
        JS_ChapterTableRow other = (JS_ChapterTableRow) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.chapterSerial==null && other.getChapterSerial()==null) || 
             (this.chapterSerial!=null &&
              this.chapterSerial.equals(other.getChapterSerial()))) &&
            ((this.rowType==null && other.getRowType()==null) || 
             (this.rowType!=null &&
              this.rowType.equals(other.getRowType()))) &&
            ((this.clauseRowFlag==null && other.getClauseRowFlag()==null) || 
             (this.clauseRowFlag!=null &&
              this.clauseRowFlag.equals(other.getClauseRowFlag())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getChapterSerial() != null) {
            _hashCode += getChapterSerial().hashCode();
        }
        if (getRowType() != null) {
            _hashCode += getRowType().hashCode();
        }
        if (getClauseRowFlag() != null) {
            _hashCode += getClauseRowFlag().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_ChapterTableRow.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_ChapterTableRow"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("chapterSerial");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ChapterSerial"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rowType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RowType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "PhysicalChapterTableRowType"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("clauseRowFlag");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ClauseRowFlag"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
