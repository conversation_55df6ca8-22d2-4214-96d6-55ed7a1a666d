package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum TrimsCacheType {
    PPBaseInfo(100,"PPBaseInfo"),
    PPLanguageInfo(101, 100,"PPLanguageInfo"),
    PPSectionBaseInfo(120, "PPSectionBaseInfo"),
    PPSectionLanguageInfo(121,"PPSectionLanguageInfo"),
    TestLineBaseInfo(130,"TestLineBaseInfo"),
    TestLineLanguageInfo(131,20, "TestLineLanguageInfo"),

    ArtifactCitationRelInfo(140, "ArtifactCitationRelInfo"),
    ArtifactCitationLangInfo(141,"ArtifactCitationLangInfo"),

    PPArtifactRelInfo(142, "PPArtifactRelInfo"),
    PPArtifactLangInfo(143,"PPArtifactLangInfo"),

    PPTestLineRelInfo(150, "PPTestLineRelInfo"),
    PPTestLineRelationshipInfo(151, "PPTestLineRelationshipInfo"),
    PPTestLineAfRelationshipInfo(152,"PPTestLineAfRelationshipInfo"),

    LabSectionBaseInfo(160,"LabSectionBaseInfo"),
    TestLineLabSectionRelInfo(161,100,"TestLineLabSectionRelInfo"),
    TestLineEquipmentTypeRelInfo(165,100, "TestLineEquipmentTypeRelInfo"),
    TestLineAnalyteRelInfo(166,"TestLineAnalyteRelInfo"),
    TestLineAnalyteLangInfo(167,"TestLineAnalyteRelInfo"),
    TestLineAnalyteLimitRelInfo(168,"TestLineAnalyteLimitRelInfo"),
    AccreditationAnalyteRelInfo(169,"AccreditationAnalyteRelInfo"),

    CitationLanguageInfo(170, "CitationLanguageInfo"),
    ConditionTypeBaseInfo(180, 100,"ConditionTypeBaseInfo"),
    ConditionTypeLanguageInfo(181, 100,"ConditionTypeLanguageInfo"),

    ConditionBaseInfo(190,"ConditionBaseInfo"),
    ConditionLanguageInfo(191, "ConditionLanguageInfo"),

    PPTestLineConditionRelInfo(200, "PPTestLineConditionRelInfo"),
    TestLineConditionRelInfo(201, 100, "TestLineConditionRelInfo"),
    PpTestLineTalRelInfo(202, "PpTestLineTalRelInfo"),
    PpTestLineAnalyteRel(203, "PpTestLineAnalyteRelSyncInfo"),
    PpTestLineAnalyteLang(203, "PpTestlineAnalyteLangSyncInfo"),

    UsageTypeBaseInfo(210, "UsageTypeBaseInfo"),
    PositionBaseInfo(220,"PositionBaseInfo"),
    TestLineUsageTypeRelInfo(221, 100, "TestLineUsageTypeRelInfo"),
    UsageTypePositionRelInfo(222, "UsageTypePositionRelInfo"),
    CustomTestCategoryInfoPO(223, "CustomTestCategoryInfoPO"),
    CustomTestCategoryLangInfoPO(224, "CustomTestCategoryLangInfoPO"),
    CustomTestCategoryTestItemRelInfoPO(225, "CustomTestCategoryTestItemRelInfoPO"),
    UsageTypeLangInfo(227, "UsageTypeLangInfo"),
    PositionLanguageInfo(228, "PositionLanguageInfo"),
    TestLineAfRelInfo(229,100,"TestLineAfRelInfo"),


    ProductAttrBaseInfo(230,"ProductAttrBaseInfo"),
    TestLineProductAttrRelInfo(231, 100, "TestLineProductAttrRelInfo"),
    TestLineCustomerAppRelInfo(240, "TestLineCustomerAppRelInfo"),
    TestLineCustomerAppLangInfo(241, 100, "TestLineCustomerAppLangInfo"),

    TestLineWorkInstructionRelInfo(250, "TestLineWorkInstructionRelInfo"),
    TestLineWorkInstructionLangInfo(251, 100, "TestLineWorkInstructionLangInfo"),
    TestLineWorkInstructionStandardInfo(252, 100, "TestLineWorkInstructionStandardInfo"),

    OrderCitationRelInfo(260, "OrderCitationRelInfo"),

    ProductTaxOnomyBaseInfo(270,100,"ProductTaxOnomyBaseInfo"),
    PpProductTaxOnomyRelInfo(271, 100, "PpProductTaxOnomyRelInfo"),
    PpSpecialChemicalRelInfo(272, 100, "PpSpecialChemicalRelInfo"),

    CitationStandardInfo(280, "ArtifactCitationStandardInfo"),
    CitationStandardLangInfo(281,"CitationStandardLangInfo"),

    CitationRegulationInfo(282, "ArtifactCitationRegulationInfo"),
    CitationRegulationLangInfo(283, "CitationRegulationLangInfo"),

    CitationMethodInfo(284, "CitationMethodInfo"),
    CitationMethodLangInfo(285, "CitationMethodLangInfo"),

    CitationBaseInfo(290, "CitationBaseInfo"),
    CitationLangInfo(291, "CitationLangInfo"),
    CitationSectionRelInfo(292, "CitationSectionRelInfo"),
    CitationSectionLangInfo(293, "CitationSectionLangInfo"),

    LimitGroupBaseInfo(300, "LimitGroupBaseInfo"),
    LimitGroupTypeBaseInfo(301, "LimitGroupTypeBaseInfo"),
    UnitBaseInfo(310, "UnitBaseInfo"),
    UnitLanguageInfo(311, "UnitLanguageInfo"),

    TestAnalyteBaseInfo(320, "TestAnalyteBaseInfo"),
    TestAnalyteLangInfo(321, "TestAnalyteLangInfo"),

    TestAnalyteLimitBaseInfo(330, "TestAnalyteLimitBaseInfo"),
    TestAnalyteLimitLanguageInfo(331, "TestAnalyteLimitLanguageInfo"),
    TestAnalyteLimitApplicabilityInfo(332, "TestAnalyteLimitApplicabilityInfo"),
    TestAnalyteLimitOperatorInfo(333, "TestAnalyteLimitOperatorInfo"),
    TestAnalyteLimitUnitRelationInfo(334, "TestAnalyteLimitUnitRelationInfo"),
    TalCustomerRelBaseInfo(335, "TalCustomerRelBaseInfo"),
    PpSgsMart(336, "TalCustomerRelBaseInfo"),

    ServiceItemDetailMappingInfo(341, "ServiceItemDetailMappingInfo"),
    ServiceItemBaseInfo(350, "ServiceItemBaseInfo"),
    ServiceItemLanguage(351, "ServiceItemLanguage"),
    ServiceItemConditionRel(352, "ServiceItemConditionRel"),
    ServiceItemArtifactRel(353, "ServiceItemArtifactRel"),
    ServiceItemAnalyteInfo(354, "ServiceItemAnalyteInfo"),
    ServiceItemAciRelInfo(355, "ServiceItemAciRelInfo"),
    ServiceItemSampleSizeInfo(356, "ServiceItemSampleSizeInfo"),

    WorkInstructionInfoPO(357, "WorkInstructionInfoPO"),
    WorkInstructionLanguageInfoPO(358, "WorkInstructionLanguageInfoPO"),
    WorkInstructionStandardRelInfoPO(359, "WorkInstructionStandardRelInfoPO"),

    TbTestLineWorkInstructionRelInfoPO(360, "TbTestLineWorkInstructionRelInfoPO"),

    EquipmenttypeBaseinfoPO(361, "EquipmenttypeBaseinfoPO"),
    EquipmenttypeLanguagePO(362, "EquipmenttypeLanguagePO"),

    TestLineCategoryIdRelInfo(363, "TestLineCategoryIdRelInfo"),

    TestLineEquipmentTypeIdRelInfo(364, "TestLineEquipmentTypeIdRelInfo"),

    AnalyteResultBaseInfoPO(400, "AnalyteResultBaseInfoPO"),
    AnalyteResultLangPO(401, "AnalyteResultLangPO"),
    AnalyteResultOptionBaseInfoPO(402, "AnalyteResultOptionBaseInfoPO"),
    AnalyteResultOptionLangPO(403, "AnalyteResultOptionLangPO"),
    AnalyteResultOptionRelInfoPO(404, "TestLineAnalyteResultOptionRelInfoPO"),


    UpgradeBizVersionIdMd5(500, "UpgradeBizVersionIdMd5"),

    ApplicationFactorInfoPo(501,"ApplicationFactorInfoPo"),

    AccreditationBaseInfo(600, "AccreditationBaseInfo"),
    AccreditationAnalyteInfo(601, "AccreditationAnalyteInfo"),
    AccreditationProductAttributeInfo(602, "AccreditationProductAttributeInfo"),
    MDLBaseInfo(610, "MDLBaseInfo"),
    MDLAnalyteRelInfo(611, "MDLAnalyteRelInfo"),
    MDLEquipmentRelInfo(612, "MDLEquipmentRelInfo"),
    MDLProductAttributeRelInfo(613, "MDLProductAttributeRelInfo"),

    ;

    private int cacheType;
    private int limit = 100;
    private boolean redisCache = false;
    private String message;

    TrimsCacheType(int cacheType, String message) {
        this.cacheType = cacheType;
        this.message = message;
    }

    TrimsCacheType(int cacheType, int limit, String message) {
        this(cacheType, message);
        this.limit = limit;
    }

    public int getCacheType() {
        return cacheType;
    }

    public int getLimit() {
        return limit;
    }

    public boolean isRedisCache() {
        return redisCache;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, TrimsCacheType> maps = new HashMap<Integer, TrimsCacheType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TrimsCacheType cacheType : TrimsCacheType.values()) {
                put(cacheType.getCacheType(), cacheType);
            }
        }
    };

    public TrimsCacheType findType(Integer cacheType) {
        if (cacheType == null || !maps.containsKey(cacheType.intValue())) {
            return null;
        }
        return maps.get(cacheType.intValue());
    }

    public String getBizVersionKey(String bizVersionId){
        TrimsCacheType tmp = this.findType(cacheType);
        String str = tmp != null ?tmp.toString().toLowerCase() : "";
        return String.format("trimslocal:%s:bizversionid:%s", str, bizVersionId);
    }

    public String getBizKey(String prefix, Integer id){
        if (prefix != null && prefix.length() > 0){
            prefix = String.format(":%s", prefix);
        }
        TrimsCacheType tmp = this.findType(cacheType);
        String str = tmp != null ? tmp.toString().toLowerCase() : "";
        return String.format("trimslocal:%s%s:bizid:%s", str, prefix, id);
    }

    public String getArtifactKey(ArtifactType artifactType, CitationType citationType, Integer citationVersionId){
        TrimsCacheType tmp = this.findType(cacheType);
        String str = tmp != null ? tmp.toString().toLowerCase() : "";
        return String.format("trimslocal:%s:%s:%s:citationversionid:%s", str, artifactType.getCode().toLowerCase(), citationType.getMessage().toLowerCase(), citationVersionId);
    }

    public String getCacheTypeKey(){
        TrimsCacheType tmp = this.findType(cacheType);
        String str = tmp != null ? tmp.toString().toLowerCase() : "";
        return String.format("trimslocal:%s:*", str);
    }
}
