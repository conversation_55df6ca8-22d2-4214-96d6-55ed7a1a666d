/**
 * MergeDocumentBySectionBreak.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class MergeDocumentBySectionBreak  implements java.io.Serializable {
    private String[] filePaths;

    private String finalFullPath;

    private int uiViewType;

    public MergeDocumentBySectionBreak() {
    }

    public MergeDocumentBySectionBreak(
           String[] filePaths,
           String finalFullPath,
           int uiViewType) {
           this.filePaths = filePaths;
           this.finalFullPath = finalFullPath;
           this.uiViewType = uiViewType;
    }


    /**
     * Gets the filePaths value for this MergeDocumentBySectionBreak.
     * 
     * @return filePaths
     */
    public String[] getFilePaths() {
        return filePaths;
    }


    /**
     * Sets the filePaths value for this MergeDocumentBySectionBreak.
     * 
     * @param filePaths
     */
    public void setFilePaths(String[] filePaths) {
        this.filePaths = filePaths;
    }


    /**
     * Gets the finalFullPath value for this MergeDocumentBySectionBreak.
     * 
     * @return finalFullPath
     */
    public String getFinalFullPath() {
        return finalFullPath;
    }


    /**
     * Sets the finalFullPath value for this MergeDocumentBySectionBreak.
     * 
     * @param finalFullPath
     */
    public void setFinalFullPath(String finalFullPath) {
        this.finalFullPath = finalFullPath;
    }


    /**
     * Gets the uiViewType value for this MergeDocumentBySectionBreak.
     * 
     * @return uiViewType
     */
    public int getUiViewType() {
        return uiViewType;
    }


    /**
     * Sets the uiViewType value for this MergeDocumentBySectionBreak.
     * 
     * @param uiViewType
     */
    public void setUiViewType(int uiViewType) {
        this.uiViewType = uiViewType;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof MergeDocumentBySectionBreak)) {
            return false;
        }
        MergeDocumentBySectionBreak other = (MergeDocumentBySectionBreak) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.filePaths==null && other.getFilePaths()==null) || 
             (this.filePaths!=null &&
              java.util.Arrays.equals(this.filePaths, other.getFilePaths()))) &&
            ((this.finalFullPath==null && other.getFinalFullPath()==null) || 
             (this.finalFullPath!=null &&
              this.finalFullPath.equals(other.getFinalFullPath()))) &&
            this.uiViewType == other.getUiViewType();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getFilePaths() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getFilePaths());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getFilePaths(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getFinalFullPath() != null) {
            _hashCode += getFinalFullPath().hashCode();
        }
        _hashCode += getUiViewType();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(MergeDocumentBySectionBreak.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">MergeDocumentBySectionBreak"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("filePaths");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "filePaths"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "string"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("finalFullPath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "finalFullPath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("uiViewType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "uiViewType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
