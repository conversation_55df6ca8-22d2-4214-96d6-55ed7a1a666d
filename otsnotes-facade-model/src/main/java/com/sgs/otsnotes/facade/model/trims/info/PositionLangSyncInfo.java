package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class PositionLangSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer languageId;
    /**
     *
     */
    private String languageCode;
    /**
     *
     */
    private String languageName;
    /**
     *
     */
    private String multiPositionName;
    /**
     *
     */
    private String bossLanguageCode;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    public String getLanguageName() {
        return languageName;
    }

    public void setLanguageName(String languageName) {
        this.languageName = languageName;
    }

    public String getMultiPositionName() {
        return multiPositionName;
    }

    public void setMultiPositionName(String multiPositionName) {
        this.multiPositionName = multiPositionName;
    }

    public String getBossLanguageCode() {
        return bossLanguageCode;
    }

    public void setBossLanguageCode(String bossLanguageCode) {
        this.bossLanguageCode = bossLanguageCode;
    }
}
