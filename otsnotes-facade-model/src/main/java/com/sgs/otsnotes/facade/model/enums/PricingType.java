package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum PricingType {
    GlobalPricing(0, "Global Pricing"),
    RegionPricing(1, "Region Pricing"),
    CountryPricing(2, "Country Pricing"),
    LocationPricing(3, "Location Pricing");
    @DictCodeField
    private int status;
    @DictLabelField
    private String code;


    PricingType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    static Map<Integer, PricingType> maps = new HashMap<>();
    static Map<String, PricingType> codeMaps = new HashMap<>();

    static {
        for (PricingType status : PricingType.values()) {
            maps.put(status.getStatus(), status);
            codeMaps.put(status.getCode().toLowerCase(), status);
        }
    }

    ;

    public static PricingType getStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    public static PricingType getCode(String code) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase())) {
            return null;
        }
        return codeMaps.get(code.toLowerCase());
    }

    public static boolean checkStatus(PricingType type, PricingType... pricingTypes) {
        if (type == null || pricingTypes == null || pricingTypes.length <= 0) {
            return false;
        }
        return check(type.getStatus(), pricingTypes);
    }

    /**
     * @param type
     * @param pricingTypes
     * @return
     */
    public static boolean check(Integer type, PricingType... pricingTypes) {
        if (type == null || !maps.containsKey(type) || pricingTypes == null || pricingTypes.length <= 0) {
            return false;
        }
        for (PricingType pricingType : pricingTypes) {
            if (type == pricingType.getStatus()) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param code
     * @param pricingTypes
     * @return
     */
    public static boolean check(String code, PricingType... pricingTypes) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase()) || pricingTypes == null || pricingTypes.length <= 0) {
            return false;
        }
        int status = codeMaps.get(code.toLowerCase()).getStatus();
        for (PricingType tls : pricingTypes) {
            if (tls.getStatus() == status) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param pricingTypes
     * @return
     */
    public boolean check(PricingType... pricingTypes) {
        if (pricingTypes == null || pricingTypes.length <= 0) {
            return false;
        }
        for (PricingType tlType : pricingTypes) {
            if (this.getStatus() == tlType.getStatus()) {
                return true;
            }
        }
        return false;
    }
}
