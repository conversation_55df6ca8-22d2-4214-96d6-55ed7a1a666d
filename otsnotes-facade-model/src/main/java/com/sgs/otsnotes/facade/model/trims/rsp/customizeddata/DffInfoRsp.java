package com.sgs.otsnotes.facade.model.trims.rsp.customizeddata;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class DffInfoRsp extends PrintFriendliness {


    /**
     *
     */
    private String dffTitle;
    /**
     *
     */
    private String dffValue;

    public String getDffTitle() {
        return dffTitle;
    }

    public void setDffTitle(String dffTitle) {
        this.dffTitle = dffTitle;
    }

    public String getDffValue() {
        return dffValue;
    }

    public void setDffValue(String dffValue) {
        this.dffValue = dffValue;
    }
}
