package com.sgs.otsnotes.facade.model.req;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;
import io.swagger.annotations.ApiModelProperty;

public class TestMatrixReq extends PrintFriendliness {
    /**
     * tre_pp_test_line_relationship.ID
     */
    @ApiModelProperty(value = "ppId", dataType = "String", required = true)
    private String ppId;
    /**
     *
     */
    @ApiModelProperty(value = "sampleId", dataType = "String", required = false)
    private String sampleId;
    /**
     * 不允许为空，根绝 testLineInstanceId 所以确认哪个testLine做assign和unassign
     */
    @ApiModelProperty(value = "testLineInstanceId", dataType = "String", required = true)
    private String testLineInstanceId;

    public String getPpId() {
        return ppId;
    }

    public void setPpId(String ppId) {
        this.ppId = ppId;
    }

    public String getSampleId() {
        return sampleId;
    }

    public void setSampleId(String sampleId) {
        this.sampleId = sampleId;
    }

    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId;
    }
}
