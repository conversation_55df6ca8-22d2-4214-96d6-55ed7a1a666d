package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

/**
 * 10：Fail、20：Data Only、30：Inconclusive、40：Pass、50：Exempt、60：NA
 * {@link com.sgs.framework.model.enums.PriorityLevel}
 */
@Deprecated
public enum PriorityLevel {
    Fail(10, "Fail", "Fail"),
    DataOnly(20, "Data Only", "Pending"),
    Inconclusive(30, "Inconclusive", "Pending"),
    Pass(40, "Pass", "Pass"),
    Exempt(50, "Exempt", "Pass"),
    NA(60, "NA", "Pass");

    private final int level;
    private final String message;
    private final String resultMsg;


    PriorityLevel(int level, String message, String resultMsg) {
        this.level = level;
        this.message = message;
        this.resultMsg = resultMsg;
    }

    public int getLevel() {
        return level;
    }

    public String getMessage() {
        return message;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public static final Map<Integer, PriorityLevel> maps = new HashMap<Integer, PriorityLevel>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (PriorityLevel enu : PriorityLevel.values()) {
                put(enu.getLevel(), enu);
            }
        }
    };

    public static final Map<String, PriorityLevel> priorityMessageMaps = new HashMap<String, PriorityLevel>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (PriorityLevel enu : PriorityLevel.values()) {
                put(enu.getMessage(), enu);
            }
        }
    };

    public static PriorityLevel getLevel(Integer code) {
        if (code == null || !maps.containsKey(code)) {
            return null;
        }
        return maps.get(code);
    }

    public static PriorityLevel getLevelFromMessage(String message) {
        if (message == null || !priorityMessageMaps.containsKey(message)) {
            return null;
        }
        return priorityMessageMaps.get(message);
    }

    public static boolean check(Integer level, PriorityLevel... priorityLevels){
        if (level == null || level <= 0 || priorityLevels == null || priorityLevels.length <= 0){
            return false;
        }
        for (PriorityLevel st: priorityLevels){
            if (level != null && level == st.getLevel()){
                return true;
            }
        }
        return false;
    }

}
