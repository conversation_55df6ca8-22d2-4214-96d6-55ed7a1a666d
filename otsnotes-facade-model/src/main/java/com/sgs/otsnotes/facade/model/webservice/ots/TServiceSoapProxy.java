package com.sgs.otsnotes.facade.model.webservice.ots;

public class TServiceSoapProxy implements TServiceSoap {
  private String _endpoint = null;
  private TServiceSoap tServiceSoap = null;
  
  public TServiceSoapProxy() {
    _initTServiceSoapProxy();
  }
  
  public TServiceSoapProxy(String endpoint) {
    _endpoint = endpoint;
    _initTServiceSoapProxy();
  }
  
  private void _initTServiceSoapProxy() {
    try {
      tServiceSoap = (new TServiceLocator()).getTServiceSoap();
      if (tServiceSoap != null) {
        if (_endpoint != null) {
          ((javax.xml.rpc.Stub) tServiceSoap)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
        }else {
            _endpoint = (String)((javax.xml.rpc.Stub)tServiceSoap)._getProperty("javax.xml.rpc.service.endpoint.address");
        }
      }

    }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  
  public String getEndpoint() {
    return _endpoint;
  }
  
  public void setEndpoint(String endpoint) {
    _endpoint = endpoint;
    if (tServiceSoap != null) {
        ((javax.xml.rpc.Stub)tServiceSoap)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    }
    
  }
  
  public TServiceSoap getTServiceSoap() {
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap;
  }
  
  public void clearTemporaryFolder() throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    tServiceSoap.clearTemporaryFolder();
  }
  
  public String doCommand(String command, String args) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.doCommand(command, args);
  }
  
  public String[] getAllFilesInTemporaryFolder() throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.getAllFilesInTemporaryFolder();
  }
  
  public String[] getAllXmlFilesInDebugDataFolder() throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.getAllXmlFilesInDebugDataFolder();
  }
  
  public String[] getSubDirAndFiles(String folderPath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.getSubDirAndFiles(folderPath);
  }
  
  public byte[] readTemporaryFile(String physicalPath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.readTemporaryFile(physicalPath);
  }
  
  public byte[] downloadTemplateServicesFile(String filePath, javax.xml.rpc.holders.BooleanHolder isExists) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.downloadTemplateServicesFile(filePath, isExists);
  }
  
  public boolean fileExistInFileServer(String serverFilePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.fileExistInFileServer(serverFilePath);
  }
  
  public CommandResult uploadTemplate(int templateId, String templateFilePath, String templateConfigFilePath, int templateType) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.uploadTemplate(templateId, templateFilePath, templateConfigFilePath, templateType);
  }
  
  public CommandResult uploadApplicationFormTemplate(int templateId, String templateFilePath, String templateConfigFilePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.uploadApplicationFormTemplate(templateId, templateFilePath, templateConfigFilePath);
  }
  
  public String getTemplateHtml(int templateId, String templateFilePath, String templateConfigFilePath, String currentSheetName) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.getTemplateHtml(templateId, templateFilePath, templateConfigFilePath, currentSheetName);
  }
  
  public String getPreviewTemplateHtml(int templateId, String templateFilePath, String templateConfigFilePath, String currentSheetName) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.getPreviewTemplateHtml(templateId, templateFilePath, templateConfigFilePath, currentSheetName);
  }
  
  public String[] getTemplatePreviewImages(int templateId, String templateFilePath, String templateConfigFilePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.getTemplatePreviewImages(templateId, templateFilePath, templateConfigFilePath);
  }
  
  public boolean copyTemplate(TemplateInfo source, TemplateInfo target) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.copyTemplate(source, target);
  }
  
  public boolean removeTemplateConfigurationCache(int templateId) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.removeTemplateConfigurationCache(templateId);
  }
  
  public TemplateConfigEntity getTemplateConfig(TemplateInstanceInfo info) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.getTemplateConfig(info);
  }
  
  public TemplateConfigEntity getWorksheetConfig(TemplateInstanceInfo worksheetInfo) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.getWorksheetConfig(worksheetInfo);
  }
  
  public boolean generateWorksheet(TemplateInstanceInfo worksheetInfo, ReportDatasource datasource) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.generateWorksheet(worksheetInfo, datasource);
  }
  
  public boolean updateWorksheetPhysicalTestClause(TemplateInstanceInfo worksheetInfo, ReportDatasource rd, javax.xml.rpc.holders.StringHolder errMsg) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.updateWorksheetPhysicalTestClause(worksheetInfo, rd, errMsg);
  }
  
  public String getWorksheetPhysicalTestClauseHtml(TemplateInstanceInfo[] worksheetInfos, javax.xml.rpc.holders.StringHolder errMsg) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.getWorksheetPhysicalTestClauseHtml(worksheetInfos, errMsg);
  }
  
  public boolean saveWorksheetDataIntoFile(TemplateInstanceInfo worksheetInfo, ReportDatasource datasource) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.saveWorksheetDataIntoFile(worksheetInfo, datasource);
  }
  
  public FactDataItem[] extractWorksheetChemicData(TemplateInstanceInfo worksheetInfo) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.extractWorksheetChemicData(worksheetInfo);
  }
  
  public boolean combineFinalTestData(TemplateInstanceInfo worksheetInfo, String[] sourceServerFiles, String targetServerFile) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.combineFinalTestData(worksheetInfo, sourceServerFiles, targetServerFile);
  }
  
  public String[] getTestReportPreviewImages(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.getTestReportPreviewImages(reportInfo);
  }
  
  public TemplateConfigEntity getTestReportConfig(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.getTestReportConfig(reportInfo);
  }
  
  public boolean generateTestReport(TemplateInstanceInfo reportInfo, ReportDatasource datasource, javax.xml.rpc.holders.ObjectHolder rlt) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.generateTestReport(reportInfo, datasource, rlt);
  }
  
  public byte[] mergeTestReportBinary(TemplateInstanceInfo reportInfo, ReportDatasource datasource, javax.xml.rpc.holders.StringHolder errMsg) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.mergeTestReportBinary(reportInfo, datasource, errMsg);
  }
  
  public boolean generateMixedTemplateTestReport(TemplateInstanceInfo reportInfo, ReportDatasource datasource, String bodyTemplateFilePath, String bodyTemplateConfigFilePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.generateMixedTemplateTestReport(reportInfo, datasource, bodyTemplateFilePath, bodyTemplateConfigFilePath);
  }
  
  public boolean generateMixedPrelimReport(TemplateInstanceInfo prelimReportInfo, ReportDatasource prelimDatasource, String testReportFilePath, String testReportConfigFilePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.generateMixedPrelimReport(prelimReportInfo, prelimDatasource, testReportFilePath, testReportConfigFilePath);
  }
  
  public boolean replaceUnknownCharAtReport(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.replaceUnknownCharAtReport(reportInfo);
  }
  
  public boolean saveTestReportDataIntoFile(TemplateInstanceInfo reportInfo, ReportDatasource datasource) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.saveTestReportDataIntoFile(reportInfo, datasource);
  }
  
  public boolean approveTestReport(TemplateInstanceInfo reportInfo, ApproverInfo[] approverInfos, boolean isShowESignature) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.approveTestReport(reportInfo, approverInfos, isShowESignature);
  }
  
  public String resetPDFPrivilege(String pdfFilePath, int privilege, boolean isRegSignature) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.resetPDFPrivilege(pdfFilePath, privilege, isRegSignature);
  }
  
  public String resetPDFPrivilegeAndDSS(String pdfFilePath, int privilege) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.resetPDFPrivilegeAndDSS(pdfFilePath, privilege);
  }
  
  public boolean resetElectronicSignature(TemplateInstanceInfo reportInfo, boolean isOnlyLogo) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.resetElectronicSignature(reportInfo, isOnlyLogo);
  }
  
  public boolean fillTestReportApproverInformation(TemplateInstanceInfo reportInfo, ApproverInfo[] approverInfos) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.fillTestReportApproverInformation(reportInfo, approverInfos);
  }
  
  public String generateTestReportPDF(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.generateTestReportPDF(reportInfo);
  }
  
  public void generateTestReportDraftSoftCopyPDF(TemplateInstanceInfo reportInfo, String outputPdfFilePath, String waterMarkText, javax.xml.rpc.holders.BooleanHolder generateTestReportDraftSoftCopyPDFResult, com.sgs.otsnotes.facade.model.webservice.ots.holders.ArrayOfStringHolder errMsgs) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    tServiceSoap.generateTestReportDraftSoftCopyPDF(reportInfo, outputPdfFilePath, waterMarkText, generateTestReportDraftSoftCopyPDFResult, errMsgs);
  }
  
  public String preGenerateTestReportPDF(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.preGenerateTestReportPDF(reportInfo);
  }
  
  public boolean isTestReportPDFExist(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.isTestReportPDFExist(reportInfo);
  }
  
  public boolean isTestReportPDFConverting(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.isTestReportPDFConverting(reportInfo);
  }
  
  public void extractTestReportData(TemplateInstanceInfo reportInfo, com.sgs.otsnotes.facade.model.webservice.ots.holders.ArrayOfDataItemHolder extractTestReportDataResult, javax.xml.rpc.holders.StringHolder errMessage) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    tServiceSoap.extractTestReportData(reportInfo, extractTestReportDataResult, errMessage);
  }
  
  public void deleteFilesAfterUploadTestReport(TemplateInstanceInfo reportInfo, javax.xml.rpc.holders.BooleanHolder deleteFilesAfterUploadTestReportResult, javax.xml.rpc.holders.StringHolder errMessage) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    tServiceSoap.deleteFilesAfterUploadTestReport(reportInfo, deleteFilesAfterUploadTestReportResult, errMessage);
  }
  
  public boolean deleteTBDElements(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.deleteTBDElements(reportInfo);
  }
  
  public ReportDatasource getMockReportDatasource() throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.getMockReportDatasource();
  }
  
  public boolean deleteMockReportDatasourceFile() throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.deleteMockReportDatasourceFile();
  }
  
  public boolean mergeReports(SubReportInfo[] reports, TemplateInstanceInfo targetReport) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.mergeReports(reports, targetReport);
  }
  
  public void mergeDocumentBySectionBreak(String[] filePaths, String finalFullPath, int uiViewType, javax.xml.rpc.holders.StringHolder mergeDocumentBySectionBreakResult, javax.xml.rpc.holders.StringHolder errMsg) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    tServiceSoap.mergeDocumentBySectionBreak(filePaths, finalFullPath, uiViewType, mergeDocumentBySectionBreakResult, errMsg);
  }
  
  public byte[] mergeDocumentContent(String[] filePaths, String targetFilePath, javax.xml.rpc.holders.StringHolder errMsg) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.mergeDocumentContent(filePaths, targetFilePath, errMsg);
  }
  
  public void getMergedReportbySection(GetMergebySectionModel model, javax.xml.rpc.holders.StringHolder getMergedReportbySectionResult, javax.xml.rpc.holders.StringHolder errMsg, Boolean downSheet, Integer timeOut) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    tServiceSoap.getMergedReportbySection(model, getMergedReportbySectionResult, errMsg, downSheet, timeOut);
  }
  
  public boolean generateConfirmation(String confirmationId, String confirmationTemplateFilePath, String confirmationFilePath, String testReportTemplateFilePath, String testReportTemplateConfigFilePath, ReportDatasource datasource) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.generateConfirmation(confirmationId, confirmationTemplateFilePath, confirmationFilePath, testReportTemplateFilePath, testReportTemplateConfigFilePath, datasource);
  }
  
  public boolean generateConfirmationByMockDatasource(String confirmationId, String confirmationTemplateFilePath, String confirmationFilePath, String testReportTemplateFilePath, String testReportTemplateConfigFilePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.generateConfirmationByMockDatasource(confirmationId, confirmationTemplateFilePath, confirmationFilePath, testReportTemplateFilePath, testReportTemplateConfigFilePath);
  }
  
  public boolean convertHtmlToPDF(String html, String pdfFilePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.convertHtmlToPDF(html, pdfFilePath);
  }
  
  public boolean generateEquipmentReport(String equipmentReportId, String equipmentTemplateFilePath, String equipmentFilePath, DataItem[] flatDataList) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.generateEquipmentReport(equipmentReportId, equipmentTemplateFilePath, equipmentFilePath, flatDataList);
  }
  
  public boolean generateFlatReport(String reportTypeName, String instanceId, String templateFilePath, String instanceFilePath, DataItem[] flatDataList) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.generateFlatReport(reportTypeName, instanceId, templateFilePath, instanceFilePath, flatDataList);
  }
  
  public boolean convertWordWithExternalConverter(String htmlString, String pdfFilePath, int waitMilliSeconds) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.convertWordWithExternalConverter(htmlString, pdfFilePath, waitMilliSeconds);
  }
  
  public boolean generateApplicationForm(TemplateInstanceInfo reportInfo, ReportDatasource datasource) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.generateApplicationForm(reportInfo, datasource);
  }
  
  public boolean saveApplicationFormDataIntoFile(TemplateInstanceInfo reportInfo, ReportDatasource datasource) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.saveApplicationFormDataIntoFile(reportInfo, datasource);
  }
  
  public boolean convertWordToPDF(String wordFilePath, String pdfFilePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.convertWordToPDF(wordFilePath, pdfFilePath);
  }
  
  public boolean addWatermarkForPDF(String pdfFilePath, String outputPdfFilePath, String watermarkText) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.addWatermarkForPDF(pdfFilePath, outputPdfFilePath, watermarkText);
  }
  
  public boolean convertDSSToolFileToFileService(String documentName, String filePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.convertDSSToolFileToFileService(documentName, filePath);
  }
  
  public boolean convertFileServiceFileToDSSTool(String documentName, String filePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.convertFileServiceFileToDSSTool(documentName, filePath);
  }
  
  public boolean convertDSSToolFailFileToQueue(String documentName, String filePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.convertDSSToolFailFileToQueue(documentName, filePath);
  }
  
  public boolean convertDSSToolFailFileToFileService(String documentName, String filePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.convertDSSToolFailFileToFileService(documentName, filePath);
  }
  
  public void deleteDSSToolOutputFile(String documentName, String filePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    tServiceSoap.deleteDSSToolOutputFile(documentName, filePath);
  }
  
  public String getProtocolReport(InTemplateInformation[] list, String fileName, Boolean downSheet, Integer timeOut, String callbackUri) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.getProtocolReport(list, fileName, downSheet, timeOut, callbackUri);
  }
  
  public String processJasperReport(byte[] fileStream, String targetFilePath) throws java.rmi.RemoteException{
    if (tServiceSoap == null) {
        _initTServiceSoapProxy();
    }
    return tServiceSoap.processJasperReport(fileStream, targetFilePath);
  }
  
  
}