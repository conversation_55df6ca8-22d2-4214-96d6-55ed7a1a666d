package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

/**
 * Test Result定义
 * <AUTHOR>
 * @date 2020/7/22 8:44
 */
@Dict
public enum ReportTemplateTestResultEnum {
    TESTLINE(1327),
    PP(1328),
    SECTION(1329);

    @DictCodeField
    private Integer code;
    ReportTemplateTestResultEnum(Integer code){
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }
}
