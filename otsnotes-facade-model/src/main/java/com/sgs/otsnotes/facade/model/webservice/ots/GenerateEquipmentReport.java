/**
 * GenerateEquipmentReport.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GenerateEquipmentReport  implements java.io.Serializable {
    private String equipmentReportId;

    private String equipmentTemplateFilePath;

    private String equipmentFilePath;

    private DataItem[] flatDataList;

    public GenerateEquipmentReport() {
    }

    public GenerateEquipmentReport(
           String equipmentReportId,
           String equipmentTemplateFilePath,
           String equipmentFilePath,
           DataItem[] flatDataList) {
           this.equipmentReportId = equipmentReportId;
           this.equipmentTemplateFilePath = equipmentTemplateFilePath;
           this.equipmentFilePath = equipmentFilePath;
           this.flatDataList = flatDataList;
    }


    /**
     * Gets the equipmentReportId value for this GenerateEquipmentReport.
     * 
     * @return equipmentReportId
     */
    public String getEquipmentReportId() {
        return equipmentReportId;
    }


    /**
     * Sets the equipmentReportId value for this GenerateEquipmentReport.
     * 
     * @param equipmentReportId
     */
    public void setEquipmentReportId(String equipmentReportId) {
        this.equipmentReportId = equipmentReportId;
    }


    /**
     * Gets the equipmentTemplateFilePath value for this GenerateEquipmentReport.
     * 
     * @return equipmentTemplateFilePath
     */
    public String getEquipmentTemplateFilePath() {
        return equipmentTemplateFilePath;
    }


    /**
     * Sets the equipmentTemplateFilePath value for this GenerateEquipmentReport.
     * 
     * @param equipmentTemplateFilePath
     */
    public void setEquipmentTemplateFilePath(String equipmentTemplateFilePath) {
        this.equipmentTemplateFilePath = equipmentTemplateFilePath;
    }


    /**
     * Gets the equipmentFilePath value for this GenerateEquipmentReport.
     * 
     * @return equipmentFilePath
     */
    public String getEquipmentFilePath() {
        return equipmentFilePath;
    }


    /**
     * Sets the equipmentFilePath value for this GenerateEquipmentReport.
     * 
     * @param equipmentFilePath
     */
    public void setEquipmentFilePath(String equipmentFilePath) {
        this.equipmentFilePath = equipmentFilePath;
    }


    /**
     * Gets the flatDataList value for this GenerateEquipmentReport.
     * 
     * @return flatDataList
     */
    public DataItem[] getFlatDataList() {
        return flatDataList;
    }


    /**
     * Sets the flatDataList value for this GenerateEquipmentReport.
     * 
     * @param flatDataList
     */
    public void setFlatDataList(DataItem[] flatDataList) {
        this.flatDataList = flatDataList;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GenerateEquipmentReport)) {
            return false;
        }
        GenerateEquipmentReport other = (GenerateEquipmentReport) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.equipmentReportId==null && other.getEquipmentReportId()==null) || 
             (this.equipmentReportId!=null &&
              this.equipmentReportId.equals(other.getEquipmentReportId()))) &&
            ((this.equipmentTemplateFilePath==null && other.getEquipmentTemplateFilePath()==null) || 
             (this.equipmentTemplateFilePath!=null &&
              this.equipmentTemplateFilePath.equals(other.getEquipmentTemplateFilePath()))) &&
            ((this.equipmentFilePath==null && other.getEquipmentFilePath()==null) || 
             (this.equipmentFilePath!=null &&
              this.equipmentFilePath.equals(other.getEquipmentFilePath()))) &&
            ((this.flatDataList==null && other.getFlatDataList()==null) || 
             (this.flatDataList!=null &&
              java.util.Arrays.equals(this.flatDataList, other.getFlatDataList())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getEquipmentReportId() != null) {
            _hashCode += getEquipmentReportId().hashCode();
        }
        if (getEquipmentTemplateFilePath() != null) {
            _hashCode += getEquipmentTemplateFilePath().hashCode();
        }
        if (getEquipmentFilePath() != null) {
            _hashCode += getEquipmentFilePath().hashCode();
        }
        if (getFlatDataList() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getFlatDataList());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getFlatDataList(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GenerateEquipmentReport.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GenerateEquipmentReport"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("equipmentReportId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "equipmentReportId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("equipmentTemplateFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "equipmentTemplateFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("equipmentFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "equipmentFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("flatDataList");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "flatDataList"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
