package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum BizCodeEnum {
    None(0, "None"),
    Exist(1, "Exist");
    @DictCodeField
    private final int code;
    @DictLabelField
    private final String message;

    BizCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String message() {
        return this.message;
    }

    public static BizCodeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BizCodeEnum item : BizCodeEnum.values()) {
            if (code.equals(item.getCode())) {
                return item;
            }
        }
        return null;
    }
}
