package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class StandardLangSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer languageId;
    /**
     *
     */
    private String standardName;
    /**
     *
     */
    private String standardShortName;
    /**
     *
     */
    private List<SectionSyncInfo> sectionItems;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    public String getStandardShortName() {
        return standardShortName;
    }

    public void setStandardShortName(String standardShortName) {
        this.standardShortName = standardShortName;
    }

    public List<SectionSyncInfo> getSectionItems() {
        return sectionItems;
    }

    public void setSectionItems(List<SectionSyncInfo> sectionItems) {
        this.sectionItems = sectionItems;
    }
}
