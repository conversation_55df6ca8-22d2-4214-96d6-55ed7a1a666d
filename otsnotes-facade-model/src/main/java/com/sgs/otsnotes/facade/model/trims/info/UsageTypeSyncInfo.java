package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class UsageTypeSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer usageTypeId;
    /**
     *
     */
    private String usageTypeName;
    /**
     *
     */
    private String usageTypeValue;
    /**
     *
     */
    private Integer status;
    /**
     *
     */
    private Integer customerAccountId;
    /**
     *
     */
    private String type;
    /**
     *
     */
    private List<PositionSyncInfo> positions;
    /**
     *
     */
    private List<UsageTypeLangSyncInfo> otherLanguageItems;

    public Integer getUsageTypeId() {
        return usageTypeId;
    }

    public void setUsageTypeId(Integer usageTypeId) {
        this.usageTypeId = usageTypeId;
    }

    public String getUsageTypeName() {
        return usageTypeName;
    }

    public void setUsageTypeName(String usageTypeName) {
        this.usageTypeName = usageTypeName;
    }

    public String getUsageTypeValue() {
        return usageTypeValue;
    }

    public void setUsageTypeValue(String usageTypeValue) {
        this.usageTypeValue = usageTypeValue;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCustomerAccountId() {
        return customerAccountId;
    }

    public void setCustomerAccountId(Integer customerAccountId) {
        this.customerAccountId = customerAccountId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<PositionSyncInfo> getPositions() {
        return positions;
    }

    public void setPositions(List<PositionSyncInfo> positions) {
        this.positions = positions;
    }

    public List<UsageTypeLangSyncInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<UsageTypeLangSyncInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }
}
