package com.sgs.otsnotes.facade.model.enums;

/**
 * @Author: Jo<PERSON>.wang
 * @Date: 2022/2/28 13:18
 */
public enum TodoStatus {
    Todo("10","Todo"),
    Ongoing("20","Ongoing"),
    Complated("30","Complated"),
    Canceled("40","Canceled");

    private final String code;
    private final String name;

    TodoStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static TodoStatus getCode(String code) {
        TodoStatus[] statuses = values();
        for(TodoStatus status : statuses){
            if(status.getCode().equals(code)){
                return status;
            }
        }
        return null;
    }
}
