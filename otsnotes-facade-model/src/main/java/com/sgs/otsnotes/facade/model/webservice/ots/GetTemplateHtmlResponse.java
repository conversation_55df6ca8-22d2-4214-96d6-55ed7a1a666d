/**
 * GetTemplateHtmlResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GetTemplateHtmlResponse  implements java.io.Serializable {
    private String getTemplateHtmlResult;

    public GetTemplateHtmlResponse() {
    }

    public GetTemplateHtmlResponse(
           String getTemplateHtmlResult) {
           this.getTemplateHtmlResult = getTemplateHtmlResult;
    }


    /**
     * Gets the getTemplateHtmlResult value for this GetTemplateHtmlResponse.
     * 
     * @return getTemplateHtmlResult
     */
    public String getGetTemplateHtmlResult() {
        return getTemplateHtmlResult;
    }


    /**
     * Sets the getTemplateHtmlResult value for this GetTemplateHtmlResponse.
     * 
     * @param getTemplateHtmlResult
     */
    public void setGetTemplateHtmlResult(String getTemplateHtmlResult) {
        this.getTemplateHtmlResult = getTemplateHtmlResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GetTemplateHtmlResponse)) {
            return false;
        }
        GetTemplateHtmlResponse other = (GetTemplateHtmlResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.getTemplateHtmlResult==null && other.getGetTemplateHtmlResult()==null) || 
             (this.getTemplateHtmlResult!=null &&
              this.getTemplateHtmlResult.equals(other.getGetTemplateHtmlResult())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getGetTemplateHtmlResult() != null) {
            _hashCode += getGetTemplateHtmlResult().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GetTemplateHtmlResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GetTemplateHtmlResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("getTemplateHtmlResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "GetTemplateHtmlResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
