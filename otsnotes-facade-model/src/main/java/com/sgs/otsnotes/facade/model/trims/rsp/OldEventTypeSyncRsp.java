package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.old.OldEventSyncInfo;

import java.util.List;

public class OldEventTypeSyncRsp extends TrimsSyncBaseRsp {
    /**
     *
     */
    private List<OldEventSyncInfo> data;

    public List<OldEventSyncInfo> getData() {
        return data;
    }

    public void setData(List<OldEventSyncInfo> data) {
        this.data = data;
    }
}