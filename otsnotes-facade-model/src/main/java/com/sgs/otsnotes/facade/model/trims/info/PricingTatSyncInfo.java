package com.sgs.otsnotes.facade.model.trims.info;

import java.math.BigDecimal;

public class PricingTatSyncInfo {
    /**
     *
     */
    private Integer internalTat;
    /**
     *
     */
    private BigDecimal doubleExpressTat;
    /**
     *
     */
    private Integer regularTat;
    /**
     *
     */
    private Integer expressTat;
    /**
     *
     */
    private Integer emergencyTat;

    public Integer getInternalTat() {
        return internalTat;
    }

    public void setInternalTat(Integer internalTat) {
        this.internalTat = internalTat;
    }

    public BigDecimal getDoubleExpressTat() {
        return doubleExpressTat;
    }

    public void setDoubleExpressTat(BigDecimal doubleExpressTat) {
        this.doubleExpressTat = doubleExpressTat;
    }

    public Integer getRegularTat() {
        return regularTat;
    }

    public void setRegularTat(Integer regularTat) {
        this.regularTat = regularTat;
    }

    public Integer getExpressTat() {
        return expressTat;
    }

    public void setExpressTat(Integer expressTat) {
        this.expressTat = expressTat;
    }

    public Integer getEmergencyTat() {
        return emergencyTat;
    }

    public void setEmergencyTat(Integer emergencyTat) {
        this.emergencyTat = emergencyTat;
    }
}
