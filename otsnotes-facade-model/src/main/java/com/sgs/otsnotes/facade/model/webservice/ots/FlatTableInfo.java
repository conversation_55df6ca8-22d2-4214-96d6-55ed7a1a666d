/**
 * FlatTableInfo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class FlatTableInfo  implements java.io.Serializable {
    private FlatTableTypeEnum flatTableType;

    private String flatTableTemplateFilePath;

    private String datasourceTableName;

    public FlatTableInfo() {
    }

    public FlatTableInfo(
           FlatTableTypeEnum flatTableType,
           String flatTableTemplateFilePath,
           String datasourceTableName) {
           this.flatTableType = flatTableType;
           this.flatTableTemplateFilePath = flatTableTemplateFilePath;
           this.datasourceTableName = datasourceTableName;
    }


    /**
     * Gets the flatTableType value for this FlatTableInfo.
     * 
     * @return flatTableType
     */
    public FlatTableTypeEnum getFlatTableType() {
        return flatTableType;
    }


    /**
     * Sets the flatTableType value for this FlatTableInfo.
     * 
     * @param flatTableType
     */
    public void setFlatTableType(FlatTableTypeEnum flatTableType) {
        this.flatTableType = flatTableType;
    }


    /**
     * Gets the flatTableTemplateFilePath value for this FlatTableInfo.
     * 
     * @return flatTableTemplateFilePath
     */
    public String getFlatTableTemplateFilePath() {
        return flatTableTemplateFilePath;
    }


    /**
     * Sets the flatTableTemplateFilePath value for this FlatTableInfo.
     * 
     * @param flatTableTemplateFilePath
     */
    public void setFlatTableTemplateFilePath(String flatTableTemplateFilePath) {
        this.flatTableTemplateFilePath = flatTableTemplateFilePath;
    }


    /**
     * Gets the datasourceTableName value for this FlatTableInfo.
     * 
     * @return datasourceTableName
     */
    public String getDatasourceTableName() {
        return datasourceTableName;
    }


    /**
     * Sets the datasourceTableName value for this FlatTableInfo.
     * 
     * @param datasourceTableName
     */
    public void setDatasourceTableName(String datasourceTableName) {
        this.datasourceTableName = datasourceTableName;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof FlatTableInfo)) {
            return false;
        }
        FlatTableInfo other = (FlatTableInfo) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.flatTableType==null && other.getFlatTableType()==null) || 
             (this.flatTableType!=null &&
              this.flatTableType.equals(other.getFlatTableType()))) &&
            ((this.flatTableTemplateFilePath==null && other.getFlatTableTemplateFilePath()==null) || 
             (this.flatTableTemplateFilePath!=null &&
              this.flatTableTemplateFilePath.equals(other.getFlatTableTemplateFilePath()))) &&
            ((this.datasourceTableName==null && other.getDatasourceTableName()==null) || 
             (this.datasourceTableName!=null &&
              this.datasourceTableName.equals(other.getDatasourceTableName())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getFlatTableType() != null) {
            _hashCode += getFlatTableType().hashCode();
        }
        if (getFlatTableTemplateFilePath() != null) {
            _hashCode += getFlatTableTemplateFilePath().hashCode();
        }
        if (getDatasourceTableName() != null) {
            _hashCode += getDatasourceTableName().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(FlatTableInfo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FlatTableInfo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("flatTableType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FlatTableType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FlatTableTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("flatTableTemplateFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FlatTableTemplateFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("datasourceTableName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DatasourceTableName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
