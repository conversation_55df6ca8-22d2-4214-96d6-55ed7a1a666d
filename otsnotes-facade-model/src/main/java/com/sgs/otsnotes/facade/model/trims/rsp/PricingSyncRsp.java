package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.PricingSyncInfo;

import java.util.List;

public class PricingSyncRsp extends TrimsSyncBaseRsp {
    /**
     *
     */
    private List<PricingSyncInfo> data;

    public List<PricingSyncInfo> getData() {
        return data;
    }

    public void setData(List<PricingSyncInfo> data) {
        this.data = data;
    }
}
