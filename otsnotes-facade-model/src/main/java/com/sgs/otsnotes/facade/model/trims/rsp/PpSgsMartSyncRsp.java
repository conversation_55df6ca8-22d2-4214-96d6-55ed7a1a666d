package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.PpSgsMartInfo;

import java.util.List;

public class PpSgsMartSyncRsp extends TrimsSyncBaseRsp {
    /**
     *
     */
    private List<PpSgsMartInfo> data;

    public List<PpSgsMartInfo> getData() {
        return data;
    }

    public void setData(List<PpSgsMartInfo> data) {
        this.data = data;
    }
}
