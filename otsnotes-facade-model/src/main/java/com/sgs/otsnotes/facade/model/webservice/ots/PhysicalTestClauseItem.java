/**
 * PhysicalTestClauseItem.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class PhysicalTestClauseItem  implements java.io.Serializable {
    private String serialNo;

    private boolean isDeleted;

    private boolean isTestClause;

    private String clauseRowFlag;

    private String conclusionValue;

    private String chapterTableName;

    public PhysicalTestClauseItem() {
    }

    public PhysicalTestClauseItem(
           String serialNo,
           boolean isDeleted,
           boolean isTestClause,
           String clauseRowFlag,
           String conclusionValue,
           String chapterTableName) {
           this.serialNo = serialNo;
           this.isDeleted = isDeleted;
           this.isTestClause = isTestClause;
           this.clauseRowFlag = clauseRowFlag;
           this.conclusionValue = conclusionValue;
           this.chapterTableName = chapterTableName;
    }


    /**
     * Gets the serialNo value for this PhysicalTestClauseItem.
     * 
     * @return serialNo
     */
    public String getSerialNo() {
        return serialNo;
    }


    /**
     * Sets the serialNo value for this PhysicalTestClauseItem.
     * 
     * @param serialNo
     */
    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }


    /**
     * Gets the isDeleted value for this PhysicalTestClauseItem.
     * 
     * @return isDeleted
     */
    public boolean isIsDeleted() {
        return isDeleted;
    }


    /**
     * Sets the isDeleted value for this PhysicalTestClauseItem.
     * 
     * @param isDeleted
     */
    public void setIsDeleted(boolean isDeleted) {
        this.isDeleted = isDeleted;
    }


    /**
     * Gets the isTestClause value for this PhysicalTestClauseItem.
     * 
     * @return isTestClause
     */
    public boolean isIsTestClause() {
        return isTestClause;
    }


    /**
     * Sets the isTestClause value for this PhysicalTestClauseItem.
     * 
     * @param isTestClause
     */
    public void setIsTestClause(boolean isTestClause) {
        this.isTestClause = isTestClause;
    }


    /**
     * Gets the clauseRowFlag value for this PhysicalTestClauseItem.
     * 
     * @return clauseRowFlag
     */
    public String getClauseRowFlag() {
        return clauseRowFlag;
    }


    /**
     * Sets the clauseRowFlag value for this PhysicalTestClauseItem.
     * 
     * @param clauseRowFlag
     */
    public void setClauseRowFlag(String clauseRowFlag) {
        this.clauseRowFlag = clauseRowFlag;
    }


    /**
     * Gets the conclusionValue value for this PhysicalTestClauseItem.
     * 
     * @return conclusionValue
     */
    public String getConclusionValue() {
        return conclusionValue;
    }


    /**
     * Sets the conclusionValue value for this PhysicalTestClauseItem.
     * 
     * @param conclusionValue
     */
    public void setConclusionValue(String conclusionValue) {
        this.conclusionValue = conclusionValue;
    }


    /**
     * Gets the chapterTableName value for this PhysicalTestClauseItem.
     * 
     * @return chapterTableName
     */
    public String getChapterTableName() {
        return chapterTableName;
    }


    /**
     * Sets the chapterTableName value for this PhysicalTestClauseItem.
     * 
     * @param chapterTableName
     */
    public void setChapterTableName(String chapterTableName) {
        this.chapterTableName = chapterTableName;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof PhysicalTestClauseItem)) {
            return false;
        }
        PhysicalTestClauseItem other = (PhysicalTestClauseItem) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.serialNo==null && other.getSerialNo()==null) || 
             (this.serialNo!=null &&
              this.serialNo.equals(other.getSerialNo()))) &&
            this.isDeleted == other.isIsDeleted() &&
            this.isTestClause == other.isIsTestClause() &&
            ((this.clauseRowFlag==null && other.getClauseRowFlag()==null) || 
             (this.clauseRowFlag!=null &&
              this.clauseRowFlag.equals(other.getClauseRowFlag()))) &&
            ((this.conclusionValue==null && other.getConclusionValue()==null) || 
             (this.conclusionValue!=null &&
              this.conclusionValue.equals(other.getConclusionValue()))) &&
            ((this.chapterTableName==null && other.getChapterTableName()==null) || 
             (this.chapterTableName!=null &&
              this.chapterTableName.equals(other.getChapterTableName())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getSerialNo() != null) {
            _hashCode += getSerialNo().hashCode();
        }
        _hashCode += (isIsDeleted() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += (isIsTestClause() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        if (getClauseRowFlag() != null) {
            _hashCode += getClauseRowFlag().hashCode();
        }
        if (getConclusionValue() != null) {
            _hashCode += getConclusionValue().hashCode();
        }
        if (getChapterTableName() != null) {
            _hashCode += getChapterTableName().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(PhysicalTestClauseItem.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "PhysicalTestClauseItem"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("serialNo");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SerialNo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isDeleted");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsDeleted"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isTestClause");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsTestClause"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("clauseRowFlag");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ClauseRowFlag"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("conclusionValue");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ConclusionValue"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("chapterTableName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ChapterTableName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
