//package com.sgs.otsnotes.facade.model.common;
//
//import com.fasterxml.jackson.annotation.JsonInclude;
//import com.fasterxml.jackson.annotation.JsonProperty;
//import io.swagger.annotations.ApiModelProperty;
//
//import javax.validation.ConstraintViolation;
//import javax.validation.Validation;
//import javax.validation.Validator;
//import java.util.Set;
//
///**
// *
// * <AUTHOR> on 2019/05/09.
// *
// */
//public abstract class BaseRequest extends PrintFriendliness {
//
//    private static final long serialVersionUID = -7140385409591586152L;
//
//    private static Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();
//
//    /**
//     *
//     */
//    @JsonProperty("sgsToken")
//    @ApiModelProperty(hidden = true)
//    private String token;
//
//    /**
//     * 请求ID
//     */
//    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
//    @ApiModelProperty(hidden = true)
//    private String requestId;
//
//    /**
//     *
//     */
//    private String productLineCode;
//
//    public String getToken() {
//        return token;
//    }
//
//    public void setToken(String token) {
//        this.token = token;
//    }
//    public String getRequestId() {
//        return requestId;
//    }
//
//    public void setRequestId(String requestId) {
//        this.requestId = requestId;
//    }
//
//    public void validate() {
//        StringBuilder errorMsgs = new StringBuilder();
//        Set<ConstraintViolation<BaseRequest>> violations = VALIDATOR.validate(this);
//        if (violations != null && violations.size() > 0) {
//            for (ConstraintViolation<BaseRequest> violation : violations) {
//                errorMsgs.append(violation.getPropertyPath()).append(":").append(violation.getMessage()).append("|");
//            }
//            throw new IllegalArgumentException(errorMsgs.substring(0, errorMsgs.length() - 1));
//        }
//    }
//
//    public String getProductLineCode() {
//        return productLineCode;
//    }
//
//    public void setProductLineCode(String productLineCode) {
//        this.productLineCode = productLineCode;
//    }
//
//    /**
//     * 一般请求，requestId不强制必填
//     * @return
//     */
//    public boolean requireRequestId( ) {
//        return false;
//    }
//
//    /**
//     * 老的.net系统的请求都沒有appId
//     * @return
//     */
//    public boolean requireAppId( ) {
//        return false;
//    }
//}
