package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictLabelField;

/**
 * @description   :  ActionEnums
 * <AUTHOR>  <PERSON>ian.<PERSON>  Sun <PERSON>
 * @createDate    :  2020/7/2 3:41 PM
 * @updateUser    :  <PERSON><PERSON>.Sun  Sun He<PERSON>yuan
 * @updateDate    :  2020/7/2 3:41 PM
 * @updateRemark  :
 * @version       :  1.0
 */
@Dict
public enum ActionEnums {
    JOB_VALIDATE("Job Validate"),
    RETEST("Lab Retest"),
    SAVE_SUBMIT("Submit Test Data"),
    VALIDATE("Valiate Test Data"),
    RETURN("Return Test Data"),
    SAVE("Save Conclusion"),
    REJECT("Reject Report"),
    CONFIRM_MATRIX("Confirm Matrix");

    @DictLabelField
    private String action;
    ActionEnums(String action){
        this.action = action;
    }

    public String getAction() {
        return action;
    }
}
