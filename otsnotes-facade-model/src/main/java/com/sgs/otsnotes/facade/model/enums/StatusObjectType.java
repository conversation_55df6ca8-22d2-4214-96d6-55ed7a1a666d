package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum StatusObjectType {
    Order("order", "order"),
    Job("job", "job");

    private final String code;
    private final String message;

    StatusObjectType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<String, StatusObjectType> maps = new HashMap<String, StatusObjectType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (StatusObjectType type : StatusObjectType.values()) {
                put(type.getCode(), type);
            }
        }
    };

    public static StatusObjectType getCode(String code) {
        if (code == null || !maps.containsKey(code.toLowerCase())) {
            return null;
        }
        return maps.get(code.toLowerCase());
    }

    public static boolean check(String code, StatusObjectType StatusObjectType) {
        if (code == null || StatusObjectType == null || !maps.containsKey(code.toLowerCase())){
            return false;
        }
        return maps.get(code.toLowerCase()) == StatusObjectType;
    }
}
