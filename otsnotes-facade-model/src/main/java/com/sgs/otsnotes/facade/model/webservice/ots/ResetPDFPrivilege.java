/**
 * ResetPDFPrivilege.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ResetPDFPrivilege  implements java.io.Serializable {
    private String pdfFilePath;

    private int privilege;

    private boolean isRegSignature;

    public ResetPDFPrivilege() {
    }

    public ResetPDFPrivilege(
           String pdfFilePath,
           int privilege,
           boolean isRegSignature) {
           this.pdfFilePath = pdfFilePath;
           this.privilege = privilege;
           this.isRegSignature = isRegSignature;
    }


    /**
     * Gets the pdfFilePath value for this ResetPDFPrivilege.
     * 
     * @return pdfFilePath
     */
    public String getPdfFilePath() {
        return pdfFilePath;
    }


    /**
     * Sets the pdfFilePath value for this ResetPDFPrivilege.
     * 
     * @param pdfFilePath
     */
    public void setPdfFilePath(String pdfFilePath) {
        this.pdfFilePath = pdfFilePath;
    }


    /**
     * Gets the privilege value for this ResetPDFPrivilege.
     * 
     * @return privilege
     */
    public int getPrivilege() {
        return privilege;
    }


    /**
     * Sets the privilege value for this ResetPDFPrivilege.
     * 
     * @param privilege
     */
    public void setPrivilege(int privilege) {
        this.privilege = privilege;
    }


    /**
     * Gets the isRegSignature value for this ResetPDFPrivilege.
     * 
     * @return isRegSignature
     */
    public boolean isIsRegSignature() {
        return isRegSignature;
    }


    /**
     * Sets the isRegSignature value for this ResetPDFPrivilege.
     * 
     * @param isRegSignature
     */
    public void setIsRegSignature(boolean isRegSignature) {
        this.isRegSignature = isRegSignature;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ResetPDFPrivilege)) {
            return false;
        }
        ResetPDFPrivilege other = (ResetPDFPrivilege) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.pdfFilePath==null && other.getPdfFilePath()==null) || 
             (this.pdfFilePath!=null &&
              this.pdfFilePath.equals(other.getPdfFilePath()))) &&
            this.privilege == other.getPrivilege() &&
            this.isRegSignature == other.isIsRegSignature();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getPdfFilePath() != null) {
            _hashCode += getPdfFilePath().hashCode();
        }
        _hashCode += getPrivilege();
        _hashCode += (isIsRegSignature() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ResetPDFPrivilege.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">ResetPDFPrivilege"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("pdfFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "pdfFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("privilege");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "privilege"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isRegSignature");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "isRegSignature"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
