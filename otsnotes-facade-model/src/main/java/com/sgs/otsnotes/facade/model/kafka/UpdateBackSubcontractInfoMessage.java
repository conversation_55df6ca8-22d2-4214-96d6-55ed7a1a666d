package com.sgs.otsnotes.facade.model.kafka;

import com.sgs.framework.core.base.BaseRequest;

public class UpdateBackSubcontractInfoMessage extends BaseRequest {
    private static final long serialVersionUID = 932346385704794908L;

    private String orderNo;
    private String reportId;
    private String subContractNo;
    private Integer reportQty;
    private String userAccount;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public String getSubContractNo() {
        return subContractNo;
    }

    public void setSubContractNo(String subContractNo) {
        this.subContractNo = subContractNo;
    }

    public Integer getReportQty() {
        return reportQty;
    }

    public void setReportQty(Integer reportQty) {
        this.reportQty = reportQty;
    }

    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }
}
