package com.sgs.otsnotes.facade.model.enums;

/**
 * 这里是样式七的condition
 * <AUTHOR>
 * @date 2021/7/16 11:36
 */
public enum Style7ConditionEnum {
    DirectReduction(1, "Direct Reduction"),
    ColorantExtraction(2, "Colorant Extraction"),
    Both(3, "Direct Reduction & Colorant Extraction")
    ;

    private final int id;
    private final String name;

    Style7ConditionEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static Style7ConditionEnum getCondition(Integer id) {
        if (id == null) {
            return null;
        }
        for (Style7ConditionEnum item : Style7ConditionEnum.values()) {
            if (item.getId() == id) {
                return item;
            }
        }
        return null;
    }
}
