package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

/**
 *
 */
public enum TestLineModuleType {
    AddTestLine(1, "Add TL"),
    AddPPTestLine(2, "Add PP TL"),
    Change(3, "Change"),
    ReTest(4, "Re Test"),
    SyncSubContract(5, "SubContract Complete Sync"),
    DataEntrySave(6, "Data Entry Save"),
    DataEntrySumbit(7, "Data Entry Sumbit"),
    ReturnTestLine(8, "Return TestLine"),
    Validate(9, "Validate"),
    UnLock(10, "unlock"),
    NCTestLine(11, "NC TestLine"),
    UpdateRemarkTestLine(12, "Update Remark TestLine"),
    CancelTestLine(13, "Cancel TestLine"),
    SubContract(14, "SubContract update old TestLine"),
    SubContractSync(15, "SubContractSync"),
    NewSubContract(16, "NewSubContract"),
    CancelOrderSubContract(17, "Cancel Order For Subcontract parent"),
    CopyReport(18, "Copy Report"),
    UploadTestLineReport(19, "Upload TestLine Report"),
    SlimJob(20, "acceptSlimJobFile"),
    ReferData(21, "Data Entry Refer Data"),
    TestDataFileDelete(22, "Test Data File Delete"),
    CancelOrder(23, "Cancel Order"),
    StarLims(24, "StarLims"),
    TestDataFileSave(25, "Test Data File Save"),
    CreatSubContract(26, "CreatSubContract"),
    SaveSubContractReport(27, "Save SubContract Report"),
    CancelSample(28, "Cancel Sample"),
    ChangeCancelMatrix(29, "Cancel Cancel Matrix"),
    ChangeUpdateCondition(30, "Change Update Condition"),
    ChangeUpdateStandard(31, "Change Update Standard"),
    ChangeAddMatrix(32, "Change Add Matrix"),
    SubcontractToTesting(33, "Subcontract To Testing"),
    PPSummaryDataEntry(34, "PP Summary DataEntry"),
    ReviseSubcontract(35,"Revise Subcontract"),
    ;

    @DictCodeField
    private final int moduleType;
    @DictLabelField
    private final String message;

    TestLineModuleType(int moduleType, String message) {
        this.moduleType = moduleType;
        this.message = message;
    }

    public int getModuleType() {
        return moduleType;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, TestLineModuleType> maps = new HashMap<Integer, TestLineModuleType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TestLineModuleType enu : TestLineModuleType.values()) {
                put(enu.getModuleType(), enu);
            }
        }
    };

    public static TestLineModuleType getModuleType(Integer moduleType) {
        if (moduleType == null || !maps.containsKey(moduleType.intValue())) {
            return null;
        }
        return maps.get(moduleType.intValue());
    }

    /**
     *
     * @param type
     * @param testLineModuleTypes
     * @return
     */
    public static boolean check(Integer type, TestLineModuleType... testLineModuleTypes) {
        if (type == null || !maps.containsKey(type) || testLineModuleTypes == null || testLineModuleTypes.length <= 0){
            return false;
        }
        for (TestLineModuleType moduleType : testLineModuleTypes){
            if (type == moduleType.getModuleType()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param testLineModuleTypes
     * @return
     */
    public boolean check(TestLineModuleType... testLineModuleTypes){
        if (testLineModuleTypes == null || testLineModuleTypes.length <= 0){
            return false;
        }
        for (TestLineModuleType moduleType: testLineModuleTypes){
            if (this.getModuleType() == moduleType.getModuleType()){
                return true;
            }
        }
        return false;
    }

}
