/**
 * JS_ExclusiveRemark.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_ExclusiveRemark  extends Element implements java.io.Serializable {
    private String endElementId;

    private String groupName;

    private boolean removeStartBookmarkArea;

    private boolean removeEndBookmarkArea;

    private boolean keepTheFirst;

    public JS_ExclusiveRemark() {
    }

    public JS_ExclusiveRemark(
           String id,
           String name,
           String endElementId,
           String groupName,
           boolean removeStartBookmarkArea,
           boolean removeEndBookmarkArea,
           boolean keepTheFirst) {
        super(
            id,
            name);
        this.endElementId = endElementId;
        this.groupName = groupName;
        this.removeStartBookmarkArea = removeStartBookmarkArea;
        this.removeEndBookmarkArea = removeEndBookmarkArea;
        this.keepTheFirst = keepTheFirst;
    }


    /**
     * Gets the endElementId value for this JS_ExclusiveRemark.
     * 
     * @return endElementId
     */
    public String getEndElementId() {
        return endElementId;
    }


    /**
     * Sets the endElementId value for this JS_ExclusiveRemark.
     * 
     * @param endElementId
     */
    public void setEndElementId(String endElementId) {
        this.endElementId = endElementId;
    }


    /**
     * Gets the groupName value for this JS_ExclusiveRemark.
     * 
     * @return groupName
     */
    public String getGroupName() {
        return groupName;
    }


    /**
     * Sets the groupName value for this JS_ExclusiveRemark.
     * 
     * @param groupName
     */
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }


    /**
     * Gets the removeStartBookmarkArea value for this JS_ExclusiveRemark.
     * 
     * @return removeStartBookmarkArea
     */
    public boolean isRemoveStartBookmarkArea() {
        return removeStartBookmarkArea;
    }


    /**
     * Sets the removeStartBookmarkArea value for this JS_ExclusiveRemark.
     * 
     * @param removeStartBookmarkArea
     */
    public void setRemoveStartBookmarkArea(boolean removeStartBookmarkArea) {
        this.removeStartBookmarkArea = removeStartBookmarkArea;
    }


    /**
     * Gets the removeEndBookmarkArea value for this JS_ExclusiveRemark.
     * 
     * @return removeEndBookmarkArea
     */
    public boolean isRemoveEndBookmarkArea() {
        return removeEndBookmarkArea;
    }


    /**
     * Sets the removeEndBookmarkArea value for this JS_ExclusiveRemark.
     * 
     * @param removeEndBookmarkArea
     */
    public void setRemoveEndBookmarkArea(boolean removeEndBookmarkArea) {
        this.removeEndBookmarkArea = removeEndBookmarkArea;
    }


    /**
     * Gets the keepTheFirst value for this JS_ExclusiveRemark.
     * 
     * @return keepTheFirst
     */
    public boolean isKeepTheFirst() {
        return keepTheFirst;
    }


    /**
     * Sets the keepTheFirst value for this JS_ExclusiveRemark.
     * 
     * @param keepTheFirst
     */
    public void setKeepTheFirst(boolean keepTheFirst) {
        this.keepTheFirst = keepTheFirst;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_ExclusiveRemark)) {
            return false;
        }
        JS_ExclusiveRemark other = (JS_ExclusiveRemark) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.endElementId==null && other.getEndElementId()==null) || 
             (this.endElementId!=null &&
              this.endElementId.equals(other.getEndElementId()))) &&
            ((this.groupName==null && other.getGroupName()==null) || 
             (this.groupName!=null &&
              this.groupName.equals(other.getGroupName()))) &&
            this.removeStartBookmarkArea == other.isRemoveStartBookmarkArea() &&
            this.removeEndBookmarkArea == other.isRemoveEndBookmarkArea() &&
            this.keepTheFirst == other.isKeepTheFirst();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getEndElementId() != null) {
            _hashCode += getEndElementId().hashCode();
        }
        if (getGroupName() != null) {
            _hashCode += getGroupName().hashCode();
        }
        _hashCode += (isRemoveStartBookmarkArea() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += (isRemoveEndBookmarkArea() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += (isKeepTheFirst() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_ExclusiveRemark.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_ExclusiveRemark"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("endElementId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "EndElementId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("groupName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "GroupName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("removeStartBookmarkArea");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RemoveStartBookmarkArea"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("removeEndBookmarkArea");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RemoveEndBookmarkArea"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("keepTheFirst");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "KeepTheFirst"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
