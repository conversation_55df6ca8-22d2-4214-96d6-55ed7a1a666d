package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum StandardType {
    StdVersionId(0, "StdVersionId"),
    RegVersionId(1, "RegVersionId");

    private int status;
    private String code;

    StandardType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return this.code;
    }

    static Map<Integer, StandardType> maps = new HashMap<>();

    static {
        for (StandardType type : StandardType.values()) {
            maps.put(type.getStatus(), type);
        }
    }

    /**
     *
     * @param status
     * @return
     */
    public static StandardType findStatus(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())){
            return null;
        }
        return maps.get(status);
    }

    /**
     *
     * @param code
     * @return
     */
    public static StandardType findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code);
    }

    /**
     *
     * @param status
     * @return
     */
    public static boolean check(Integer status) {
        if (status == null){
            return false;
        }
        return maps.containsKey(status.intValue());
    }

    /**
     *
     * @param status
     * @param type
     * @return
     */
    public static boolean check(Integer status, StandardType type) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        return maps.get(status.intValue()) == type;
    }
}
