package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;
import com.sgs.otsnotes.facade.model.trims.info.PositionLangSyncInfo;

import java.util.List;

public class PositionSyncInfo extends PrintFriendliness {
    private Integer positionId;
    /**
     *
     */
    private Integer positionNumber;
    /**
     *
     */
    private String positionName;
    /**
     *
     */
    private String positionDescription;
    /**
     *
     */
    private Integer languageId;

    public Integer getPositionId() {
        return positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public Integer getPositionNumber() {
        return positionNumber;
    }

    public void setPositionNumber(Integer positionNumber) {
        this.positionNumber = positionNumber;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getPositionDescription() {
        return positionDescription;
    }

    public void setPositionDescription(String positionDescription) {
        this.positionDescription = positionDescription;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }
}
