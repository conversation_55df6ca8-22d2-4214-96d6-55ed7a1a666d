/**
 * GenerateWorksheet.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GenerateWorksheet  implements java.io.Serializable {
    private TemplateInstanceInfo worksheetInfo;

    private ReportDatasource datasource;

    public GenerateWorksheet() {
    }

    public GenerateWorksheet(
           TemplateInstanceInfo worksheetInfo,
           ReportDatasource datasource) {
           this.worksheetInfo = worksheetInfo;
           this.datasource = datasource;
    }


    /**
     * Gets the worksheetInfo value for this GenerateWorksheet.
     * 
     * @return worksheetInfo
     */
    public TemplateInstanceInfo getWorksheetInfo() {
        return worksheetInfo;
    }


    /**
     * Sets the worksheetInfo value for this GenerateWorksheet.
     * 
     * @param worksheetInfo
     */
    public void setWorksheetInfo(TemplateInstanceInfo worksheetInfo) {
        this.worksheetInfo = worksheetInfo;
    }


    /**
     * Gets the datasource value for this GenerateWorksheet.
     * 
     * @return datasource
     */
    public ReportDatasource getDatasource() {
        return datasource;
    }


    /**
     * Sets the datasource value for this GenerateWorksheet.
     * 
     * @param datasource
     */
    public void setDatasource(ReportDatasource datasource) {
        this.datasource = datasource;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GenerateWorksheet)) {
            return false;
        }
        GenerateWorksheet other = (GenerateWorksheet) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.worksheetInfo==null && other.getWorksheetInfo()==null) || 
             (this.worksheetInfo!=null &&
              this.worksheetInfo.equals(other.getWorksheetInfo()))) &&
            ((this.datasource==null && other.getDatasource()==null) || 
             (this.datasource!=null &&
              this.datasource.equals(other.getDatasource())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getWorksheetInfo() != null) {
            _hashCode += getWorksheetInfo().hashCode();
        }
        if (getDatasource() != null) {
            _hashCode += getDatasource().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GenerateWorksheet.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GenerateWorksheet"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("worksheetInfo");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "worksheetInfo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInstanceInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("datasource");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "datasource"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ReportDatasource"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
