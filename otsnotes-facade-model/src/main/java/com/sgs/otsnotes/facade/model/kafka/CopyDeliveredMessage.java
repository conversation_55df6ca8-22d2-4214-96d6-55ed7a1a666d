package com.sgs.otsnotes.facade.model.kafka;


import java.io.Serializable;


public class CopyDeliveredMessage implements Serializable{
	
	private static final long serialVersionUID = -5019741015211675147L;

	private String orderNo;
	
	private String deliveryRemark;
	
	private String softCopyDeliveryDate;
	
	private String hardCopyDeliveryDate;


	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getDeliveryRemark() {
		return deliveryRemark;
	}

	public void setDeliveryRemark(String deliveryRemark) {
		this.deliveryRemark = deliveryRemark;
	}

	public String getSoftCopyDeliveryDate() {
		return softCopyDeliveryDate;
	}

	public void setSoftCopyDeliveryDate(String softCopyDeliveryDate) {
		this.softCopyDeliveryDate = softCopyDeliveryDate;
	}

	public String getHardCopyDeliveryDate() {
		return hardCopyDeliveryDate;
	}

	public void setHardCopyDeliveryDate(String hardCopyDeliveryDate) {
		this.hardCopyDeliveryDate = hardCopyDeliveryDate;
	}
}
