package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

/**
 * @ClassName NoStyleTemplateType
 * @Description TODO
 * <AUTHOR>
 * @Date 4/20/2021
 */
@Dict
public enum  NoStyleTemplateType {
    NoStyle(0),
    WI(1);
    @DictCodeField
    private Integer type;
    NoStyleTemplateType(Integer type){
        this.type = type;
    }

    public Integer getType() {
        return type;
    }
}
