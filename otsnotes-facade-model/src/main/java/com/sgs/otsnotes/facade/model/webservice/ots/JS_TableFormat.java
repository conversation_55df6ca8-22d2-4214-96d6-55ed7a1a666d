/**
 * JS_TableFormat.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_TableFormat  implements java.io.Serializable {
    private double leftFixedWidth;

    private double rightFixedWidth;

    public JS_TableFormat() {
    }

    public JS_TableFormat(
           double leftFixedWidth,
           double rightFixedWidth) {
           this.leftFixedWidth = leftFixedWidth;
           this.rightFixedWidth = rightFixedWidth;
    }


    /**
     * Gets the leftFixedWidth value for this JS_TableFormat.
     * 
     * @return leftFixedWidth
     */
    public double getLeftFixedWidth() {
        return leftFixedWidth;
    }


    /**
     * Sets the leftFixedWidth value for this JS_TableFormat.
     * 
     * @param leftFixedWidth
     */
    public void setLeftFixedWidth(double leftFixedWidth) {
        this.leftFixedWidth = leftFixedWidth;
    }


    /**
     * Gets the rightFixedWidth value for this JS_TableFormat.
     * 
     * @return rightFixedWidth
     */
    public double getRightFixedWidth() {
        return rightFixedWidth;
    }


    /**
     * Sets the rightFixedWidth value for this JS_TableFormat.
     * 
     * @param rightFixedWidth
     */
    public void setRightFixedWidth(double rightFixedWidth) {
        this.rightFixedWidth = rightFixedWidth;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_TableFormat)) {
            return false;
        }
        JS_TableFormat other = (JS_TableFormat) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.leftFixedWidth == other.getLeftFixedWidth() &&
            this.rightFixedWidth == other.getRightFixedWidth();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += new Double(getLeftFixedWidth()).hashCode();
        _hashCode += new Double(getRightFixedWidth()).hashCode();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_TableFormat.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_TableFormat"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("leftFixedWidth");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "LeftFixedWidth"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rightFixedWidth");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RightFixedWidth"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
