package com.sgs.otsnotes.facade.model.starLims.temp;

import com.sgs.framework.core.common.PrintFriendliness;
import com.sgs.testdatabiz.facade.model.req.starlims.ReceiveStarLimsReportReq;

import java.util.List;

public class ReportInfoTempRsp extends PrintFriendliness {
    /**
     *
     */
    private String status;

    /**
     *
     */
    private String details;

    /**
     *
     */
    private List<ReceiveStarLimsReportReq> report;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public List<ReceiveStarLimsReportReq> getReport() {
        return report;
    }

    public void setReport(List<ReceiveStarLimsReportReq> report) {
        this.report = report;
    }
}
