package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum ConclusionTabsEnums {
    Test(0, "Test"),
    DR(1, "DR"),
    Subcontract(2, "Subcontract");
    @DictCodeField
    private Integer tabType;
    @DictLabelField
    private String name;

    ConclusionTabsEnums(Integer tabType, String name) {
        this.tabType = tabType;
        this.name = name;
    }

    public Integer getTabType() {
        return tabType;
    }

    public String getName() {
        return name;
    }

    public static boolean check(Integer tabType, ConclusionTabsEnums... enums) {
        if (tabType == null) {
            return false;
        }
        for (ConclusionTabsEnums anEnum : enums) {
            if (anEnum.tabType.compareTo(tabType) == 0) {
                return true;
            }
        }
        return false;
    }

    public static ConclusionTabsEnums getTabType(Integer tabType) {
        if (tabType == null) {
            return null;
        }
        for (ConclusionTabsEnums value : ConclusionTabsEnums.values()) {
            if (value.tabType.compareTo(tabType) == 0) {
                return value;
            }
        }
        return null;
    }
}
