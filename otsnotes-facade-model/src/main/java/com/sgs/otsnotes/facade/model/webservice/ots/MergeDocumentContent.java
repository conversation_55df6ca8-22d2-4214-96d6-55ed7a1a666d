/**
 * MergeDocumentContent.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class MergeDocumentContent  implements java.io.Serializable {
    private String[] filePaths;

    private String targetFilePath;

    private String errMsg;

    public MergeDocumentContent() {
    }

    public MergeDocumentContent(
           String[] filePaths,
           String targetFilePath,
           String errMsg) {
           this.filePaths = filePaths;
           this.targetFilePath = targetFilePath;
           this.errMsg = errMsg;
    }


    /**
     * Gets the filePaths value for this MergeDocumentContent.
     * 
     * @return filePaths
     */
    public String[] getFilePaths() {
        return filePaths;
    }


    /**
     * Sets the filePaths value for this MergeDocumentContent.
     * 
     * @param filePaths
     */
    public void setFilePaths(String[] filePaths) {
        this.filePaths = filePaths;
    }


    /**
     * Gets the targetFilePath value for this MergeDocumentContent.
     * 
     * @return targetFilePath
     */
    public String getTargetFilePath() {
        return targetFilePath;
    }


    /**
     * Sets the targetFilePath value for this MergeDocumentContent.
     * 
     * @param targetFilePath
     */
    public void setTargetFilePath(String targetFilePath) {
        this.targetFilePath = targetFilePath;
    }


    /**
     * Gets the errMsg value for this MergeDocumentContent.
     * 
     * @return errMsg
     */
    public String getErrMsg() {
        return errMsg;
    }


    /**
     * Sets the errMsg value for this MergeDocumentContent.
     * 
     * @param errMsg
     */
    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof MergeDocumentContent)) {
            return false;
        }
        MergeDocumentContent other = (MergeDocumentContent) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.filePaths==null && other.getFilePaths()==null) || 
             (this.filePaths!=null &&
              java.util.Arrays.equals(this.filePaths, other.getFilePaths()))) &&
            ((this.targetFilePath==null && other.getTargetFilePath()==null) || 
             (this.targetFilePath!=null &&
              this.targetFilePath.equals(other.getTargetFilePath()))) &&
            ((this.errMsg==null && other.getErrMsg()==null) || 
             (this.errMsg!=null &&
              this.errMsg.equals(other.getErrMsg())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getFilePaths() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getFilePaths());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getFilePaths(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getTargetFilePath() != null) {
            _hashCode += getTargetFilePath().hashCode();
        }
        if (getErrMsg() != null) {
            _hashCode += getErrMsg().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(MergeDocumentContent.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">MergeDocumentContent"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("filePaths");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "filePaths"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "string"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("targetFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "targetFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("errMsg");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "errMsg"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
