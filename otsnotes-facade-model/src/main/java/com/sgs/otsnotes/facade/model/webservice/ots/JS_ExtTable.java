/**
 * JS_ExtTable.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_ExtTable  extends Element implements java.io.Serializable {
    private String[] RBlockType;

    private String[] BBlockType;

    private String LTID;

    private String RBID;

    private EnumExcelRepeatType rowRT;

    private EnumExcelRepeatType colRT;

    private int FRCount;

    private int FCCount;

    private String[] settingType;

    private DatasourceTypeEnum tbRDatasrc;

    private DatasourceTypeEnum colRDatasrc;

    private DatasourceTypeEnum rowRDatasrc;

    private String[] rowRepeatCells;

    private String[] columnRepeatCells;

    private int maxColumnCount;

    public JS_ExtTable() {
    }

    public JS_ExtTable(
           String id,
           String name,
           String[] RBlockType,
           String[] BBlockType,
           String LTID,
           String RBID,
           EnumExcelRepeatType rowRT,
           EnumExcelRepeatType colRT,
           int FRCount,
           int FCCount,
           String[] settingType,
           DatasourceTypeEnum tbRDatasrc,
           DatasourceTypeEnum colRDatasrc,
           DatasourceTypeEnum rowRDatasrc,
           String[] rowRepeatCells,
           String[] columnRepeatCells,
           int maxColumnCount) {
        super(
            id,
            name);
        this.RBlockType = RBlockType;
        this.BBlockType = BBlockType;
        this.LTID = LTID;
        this.RBID = RBID;
        this.rowRT = rowRT;
        this.colRT = colRT;
        this.FRCount = FRCount;
        this.FCCount = FCCount;
        this.settingType = settingType;
        this.tbRDatasrc = tbRDatasrc;
        this.colRDatasrc = colRDatasrc;
        this.rowRDatasrc = rowRDatasrc;
        this.rowRepeatCells = rowRepeatCells;
        this.columnRepeatCells = columnRepeatCells;
        this.maxColumnCount = maxColumnCount;
    }


    /**
     * Gets the RBlockType value for this JS_ExtTable.
     * 
     * @return RBlockType
     */
    public String[] getRBlockType() {
        return RBlockType;
    }


    /**
     * Sets the RBlockType value for this JS_ExtTable.
     * 
     * @param RBlockType
     */
    public void setRBlockType(String[] RBlockType) {
        this.RBlockType = RBlockType;
    }


    /**
     * Gets the BBlockType value for this JS_ExtTable.
     * 
     * @return BBlockType
     */
    public String[] getBBlockType() {
        return BBlockType;
    }


    /**
     * Sets the BBlockType value for this JS_ExtTable.
     * 
     * @param BBlockType
     */
    public void setBBlockType(String[] BBlockType) {
        this.BBlockType = BBlockType;
    }


    /**
     * Gets the LTID value for this JS_ExtTable.
     * 
     * @return LTID
     */
    public String getLTID() {
        return LTID;
    }


    /**
     * Sets the LTID value for this JS_ExtTable.
     * 
     * @param LTID
     */
    public void setLTID(String LTID) {
        this.LTID = LTID;
    }


    /**
     * Gets the RBID value for this JS_ExtTable.
     * 
     * @return RBID
     */
    public String getRBID() {
        return RBID;
    }


    /**
     * Sets the RBID value for this JS_ExtTable.
     * 
     * @param RBID
     */
    public void setRBID(String RBID) {
        this.RBID = RBID;
    }


    /**
     * Gets the rowRT value for this JS_ExtTable.
     * 
     * @return rowRT
     */
    public EnumExcelRepeatType getRowRT() {
        return rowRT;
    }


    /**
     * Sets the rowRT value for this JS_ExtTable.
     * 
     * @param rowRT
     */
    public void setRowRT(EnumExcelRepeatType rowRT) {
        this.rowRT = rowRT;
    }


    /**
     * Gets the colRT value for this JS_ExtTable.
     * 
     * @return colRT
     */
    public EnumExcelRepeatType getColRT() {
        return colRT;
    }


    /**
     * Sets the colRT value for this JS_ExtTable.
     * 
     * @param colRT
     */
    public void setColRT(EnumExcelRepeatType colRT) {
        this.colRT = colRT;
    }


    /**
     * Gets the FRCount value for this JS_ExtTable.
     * 
     * @return FRCount
     */
    public int getFRCount() {
        return FRCount;
    }


    /**
     * Sets the FRCount value for this JS_ExtTable.
     * 
     * @param FRCount
     */
    public void setFRCount(int FRCount) {
        this.FRCount = FRCount;
    }


    /**
     * Gets the FCCount value for this JS_ExtTable.
     * 
     * @return FCCount
     */
    public int getFCCount() {
        return FCCount;
    }


    /**
     * Sets the FCCount value for this JS_ExtTable.
     * 
     * @param FCCount
     */
    public void setFCCount(int FCCount) {
        this.FCCount = FCCount;
    }


    /**
     * Gets the settingType value for this JS_ExtTable.
     * 
     * @return settingType
     */
    public String[] getSettingType() {
        return settingType;
    }


    /**
     * Sets the settingType value for this JS_ExtTable.
     * 
     * @param settingType
     */
    public void setSettingType(String[] settingType) {
        this.settingType = settingType;
    }


    /**
     * Gets the tbRDatasrc value for this JS_ExtTable.
     * 
     * @return tbRDatasrc
     */
    public DatasourceTypeEnum getTbRDatasrc() {
        return tbRDatasrc;
    }


    /**
     * Sets the tbRDatasrc value for this JS_ExtTable.
     * 
     * @param tbRDatasrc
     */
    public void setTbRDatasrc(DatasourceTypeEnum tbRDatasrc) {
        this.tbRDatasrc = tbRDatasrc;
    }


    /**
     * Gets the colRDatasrc value for this JS_ExtTable.
     * 
     * @return colRDatasrc
     */
    public DatasourceTypeEnum getColRDatasrc() {
        return colRDatasrc;
    }


    /**
     * Sets the colRDatasrc value for this JS_ExtTable.
     * 
     * @param colRDatasrc
     */
    public void setColRDatasrc(DatasourceTypeEnum colRDatasrc) {
        this.colRDatasrc = colRDatasrc;
    }


    /**
     * Gets the rowRDatasrc value for this JS_ExtTable.
     * 
     * @return rowRDatasrc
     */
    public DatasourceTypeEnum getRowRDatasrc() {
        return rowRDatasrc;
    }


    /**
     * Sets the rowRDatasrc value for this JS_ExtTable.
     * 
     * @param rowRDatasrc
     */
    public void setRowRDatasrc(DatasourceTypeEnum rowRDatasrc) {
        this.rowRDatasrc = rowRDatasrc;
    }


    /**
     * Gets the rowRepeatCells value for this JS_ExtTable.
     * 
     * @return rowRepeatCells
     */
    public String[] getRowRepeatCells() {
        return rowRepeatCells;
    }


    /**
     * Sets the rowRepeatCells value for this JS_ExtTable.
     * 
     * @param rowRepeatCells
     */
    public void setRowRepeatCells(String[] rowRepeatCells) {
        this.rowRepeatCells = rowRepeatCells;
    }


    /**
     * Gets the columnRepeatCells value for this JS_ExtTable.
     * 
     * @return columnRepeatCells
     */
    public String[] getColumnRepeatCells() {
        return columnRepeatCells;
    }


    /**
     * Sets the columnRepeatCells value for this JS_ExtTable.
     * 
     * @param columnRepeatCells
     */
    public void setColumnRepeatCells(String[] columnRepeatCells) {
        this.columnRepeatCells = columnRepeatCells;
    }


    /**
     * Gets the maxColumnCount value for this JS_ExtTable.
     * 
     * @return maxColumnCount
     */
    public int getMaxColumnCount() {
        return maxColumnCount;
    }


    /**
     * Sets the maxColumnCount value for this JS_ExtTable.
     * 
     * @param maxColumnCount
     */
    public void setMaxColumnCount(int maxColumnCount) {
        this.maxColumnCount = maxColumnCount;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_ExtTable)) {
            return false;
        }
        JS_ExtTable other = (JS_ExtTable) obj;
        if (obj == null) {
            return false;
        }
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.RBlockType==null && other.getRBlockType()==null) || 
             (this.RBlockType!=null &&
              java.util.Arrays.equals(this.RBlockType, other.getRBlockType()))) &&
            ((this.BBlockType==null && other.getBBlockType()==null) || 
             (this.BBlockType!=null &&
              java.util.Arrays.equals(this.BBlockType, other.getBBlockType()))) &&
            ((this.LTID==null && other.getLTID()==null) || 
             (this.LTID!=null &&
              this.LTID.equals(other.getLTID()))) &&
            ((this.RBID==null && other.getRBID()==null) || 
             (this.RBID!=null &&
              this.RBID.equals(other.getRBID()))) &&
            ((this.rowRT==null && other.getRowRT()==null) || 
             (this.rowRT!=null &&
              this.rowRT.equals(other.getRowRT()))) &&
            ((this.colRT==null && other.getColRT()==null) || 
             (this.colRT!=null &&
              this.colRT.equals(other.getColRT()))) &&
            this.FRCount == other.getFRCount() &&
            this.FCCount == other.getFCCount() &&
            ((this.settingType==null && other.getSettingType()==null) || 
             (this.settingType!=null &&
              java.util.Arrays.equals(this.settingType, other.getSettingType()))) &&
            ((this.tbRDatasrc==null && other.getTbRDatasrc()==null) || 
             (this.tbRDatasrc!=null &&
              this.tbRDatasrc.equals(other.getTbRDatasrc()))) &&
            ((this.colRDatasrc==null && other.getColRDatasrc()==null) || 
             (this.colRDatasrc!=null &&
              this.colRDatasrc.equals(other.getColRDatasrc()))) &&
            ((this.rowRDatasrc==null && other.getRowRDatasrc()==null) || 
             (this.rowRDatasrc!=null &&
              this.rowRDatasrc.equals(other.getRowRDatasrc()))) &&
            ((this.rowRepeatCells==null && other.getRowRepeatCells()==null) || 
             (this.rowRepeatCells!=null &&
              java.util.Arrays.equals(this.rowRepeatCells, other.getRowRepeatCells()))) &&
            ((this.columnRepeatCells==null && other.getColumnRepeatCells()==null) || 
             (this.columnRepeatCells!=null &&
              java.util.Arrays.equals(this.columnRepeatCells, other.getColumnRepeatCells()))) &&
            this.maxColumnCount == other.getMaxColumnCount();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getRBlockType() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getRBlockType());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getRBlockType(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getBBlockType() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getBBlockType());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getBBlockType(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getLTID() != null) {
            _hashCode += getLTID().hashCode();
        }
        if (getRBID() != null) {
            _hashCode += getRBID().hashCode();
        }
        if (getRowRT() != null) {
            _hashCode += getRowRT().hashCode();
        }
        if (getColRT() != null) {
            _hashCode += getColRT().hashCode();
        }
        _hashCode += getFRCount();
        _hashCode += getFCCount();
        if (getSettingType() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getSettingType());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getSettingType(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getTbRDatasrc() != null) {
            _hashCode += getTbRDatasrc().hashCode();
        }
        if (getColRDatasrc() != null) {
            _hashCode += getColRDatasrc().hashCode();
        }
        if (getRowRDatasrc() != null) {
            _hashCode += getRowRDatasrc().hashCode();
        }
        if (getRowRepeatCells() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getRowRepeatCells());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getRowRepeatCells(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getColumnRepeatCells() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getColumnRepeatCells());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getColumnRepeatCells(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        _hashCode += getMaxColumnCount();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_ExtTable.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_ExtTable"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("RBlockType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RBlockType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ExcelTableBlockType"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("BBlockType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "BBlockType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ExcelTableBlockType"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("LTID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "LTID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("RBID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RBID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rowRT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RowRT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "EnumExcelRepeatType"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("colRT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ColRT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "EnumExcelRepeatType"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FRCount");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FRCount"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FCCount");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FCCount"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("settingType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SettingType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TableSettingTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tbRDatasrc");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TbRDatasrc"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DatasourceTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("colRDatasrc");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ColRDatasrc"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DatasourceTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rowRDatasrc");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RowRDatasrc"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DatasourceTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rowRepeatCells");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RowRepeatCells"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "string"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("columnRepeatCells");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ColumnRepeatCells"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "string"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("maxColumnCount");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "MaxColumnCount"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
