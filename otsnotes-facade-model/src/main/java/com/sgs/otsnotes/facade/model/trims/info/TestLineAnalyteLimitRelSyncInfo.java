package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/4 18:32
 */
public class TestLineAnalyteLimitRelSyncInfo extends PrintFriendliness {

    private Integer versionIndentifier;
    private Integer sequence;
    private String referenceMethod;

    public Integer getVersionIndentifier() {
        return versionIndentifier;
    }

    public void setVersionIndentifier(Integer versionIndentifier) {
        this.versionIndentifier = versionIndentifier;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getReferenceMethod() {
        return referenceMethod;
    }

    public void setReferenceMethod(String referenceMethod) {
        this.referenceMethod = referenceMethod;
    }
}
