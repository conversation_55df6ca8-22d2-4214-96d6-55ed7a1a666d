package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum TestLinePaType {
    None(0, "none"),
    Applicability(1, "Applicability");

    private final int status;
    private final String paType;

    TestLinePaType(int status, String paType) {
        this.status = status;
        this.paType = paType;
    }

    public int getStatus() {
        return status;
    }

    public String getPaType() {
        return this.paType;
    }

    public static final Map<Integer, TestLinePaType> maps = new HashMap<Integer, TestLinePaType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TestLinePaType enu : TestLinePaType.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static TestLinePaType getCode(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }
}
