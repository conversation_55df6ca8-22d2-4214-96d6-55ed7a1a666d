package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

/**
 * <AUTHOR> mingyang.chen
 * @date Date : 2021年03月09日
 */
@Dict
public enum AmendReportTypeEnums {
	CLIENT_INFORMATION(1,"UPDATE CLIENT INFORMATION.","更新客户提供的信息。"),
	SAMPLE_INFORMATION(2,"UPDATE SAMPLE INFORMATION.","更新样品信息。"),
	TEST_INFORMATION(3,"UPDATE TEST INFORMATION.","更新检测信息。"),
	;
	@DictCodeField
	private final int code;
	@DictLabelField
	private final String remarkEn;
	private final String remarkCn;

	AmendReportTypeEnums(int code, String remarkEn, String remarkCn) {
		this.code = code;
		this.remarkEn = remarkEn;
		this.remarkCn = remarkCn;
	}

	public int getCode() {
		return code;
	}

	public String getRemarkEn() {
		return remarkEn;
	}

	public String getRemarkCn() {
		return remarkCn;
	}

	public static Map<Integer, AmendReportTypeEnums> getMaps() {
		return maps;
	}

	public static final Map<Integer, AmendReportTypeEnums> maps = new HashMap<Integer, AmendReportTypeEnums>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (AmendReportTypeEnums enu : AmendReportTypeEnums.values()) {
				put(enu.getCode(), enu);
			}
		}
	};

	public static AmendReportTypeEnums findCode(Integer code) {
		return maps.get(code);
	}

	/**
	 *
	 * @param code
	 * @param remarks
	 * @return
	 */
	public static boolean check(Integer code, AmendReportTypeEnums... remarks) {
		if (code == null || !maps.containsKey(code) || remarks == null || remarks.length <= 0){
			return false;
		}
		for (AmendReportTypeEnums tlWatermark: remarks){
			if (code == tlWatermark.getCode()){
				return true;
			}
		}
		return false;
	}

	/**
	 *
	 * @param remarks
	 * @return
	 */
	public boolean check(AmendReportTypeEnums... remarks){
		if (remarks == null || remarks.length <= 0){
			return false;
		}
		for (AmendReportTypeEnums watermarkItem: remarks){
			if (this.getCode() == watermarkItem.getCode()){
				return true;
			}
		}
		return false;
	}
}
