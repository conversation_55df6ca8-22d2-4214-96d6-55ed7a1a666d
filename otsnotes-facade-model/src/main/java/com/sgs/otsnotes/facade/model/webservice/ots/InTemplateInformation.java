/**
 * InTemplateInformation.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class InTemplateInformation  implements java.io.Serializable {
    private String templateFilePath;

    private int seqencing;

    private TemplateDataItem[] AIDList;

    private boolean isDelete;

    public InTemplateInformation() {
    }

    public InTemplateInformation(
           String templateFilePath,
           int seqencing,
           TemplateDataItem[] AIDList,
           boolean isDelete) {
           this.templateFilePath = templateFilePath;
           this.seqencing = seqencing;
           this.AIDList = AIDList;
           this.isDelete = isDelete;
    }


    /**
     * Gets the templateFilePath value for this InTemplateInformation.
     * 
     * @return templateFilePath
     */
    public String getTemplateFilePath() {
        return templateFilePath;
    }


    /**
     * Sets the templateFilePath value for this InTemplateInformation.
     * 
     * @param templateFilePath
     */
    public void setTemplateFilePath(String templateFilePath) {
        this.templateFilePath = templateFilePath;
    }


    /**
     * Gets the seqencing value for this InTemplateInformation.
     * 
     * @return seqencing
     */
    public int getSeqencing() {
        return seqencing;
    }


    /**
     * Sets the seqencing value for this InTemplateInformation.
     * 
     * @param seqencing
     */
    public void setSeqencing(int seqencing) {
        this.seqencing = seqencing;
    }


    /**
     * Gets the AIDList value for this InTemplateInformation.
     * 
     * @return AIDList
     */
    public TemplateDataItem[] getAIDList() {
        return AIDList;
    }


    /**
     * Sets the AIDList value for this InTemplateInformation.
     * 
     * @param AIDList
     */
    public void setAIDList(TemplateDataItem[] AIDList) {
        this.AIDList = AIDList;
    }


    /**
     * Gets the isDelete value for this InTemplateInformation.
     * 
     * @return isDelete
     */
    public boolean isIsDelete() {
        return isDelete;
    }


    /**
     * Sets the isDelete value for this InTemplateInformation.
     * 
     * @param isDelete
     */
    public void setIsDelete(boolean isDelete) {
        this.isDelete = isDelete;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof InTemplateInformation)) {
            return false;
        }
        InTemplateInformation other = (InTemplateInformation) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.templateFilePath==null && other.getTemplateFilePath()==null) || 
             (this.templateFilePath!=null &&
              this.templateFilePath.equals(other.getTemplateFilePath()))) &&
            this.seqencing == other.getSeqencing() &&
            ((this.AIDList==null && other.getAIDList()==null) || 
             (this.AIDList!=null &&
              java.util.Arrays.equals(this.AIDList, other.getAIDList()))) &&
            this.isDelete == other.isIsDelete();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getTemplateFilePath() != null) {
            _hashCode += getTemplateFilePath().hashCode();
        }
        _hashCode += getSeqencing();
        if (getAIDList() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getAIDList());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getAIDList(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        _hashCode += (isIsDelete() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(InTemplateInformation.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "InTemplateInformation"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("seqencing");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "seqencing"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AIDList");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "AIDList"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateDataItem"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateDataItem"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isDelete");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsDelete"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
