package com.sgs.otsnotes.facade.model.kafka;


import com.sgs.framework.core.base.BaseRequest;

public class ToDoListMessage extends BaseRequest {

    /**
     * ID
     */
    private String id;

    /**
     * BU 编码
     */
    private String productLineCode;

    /**
     * Location 编码
     */
    private String locationCode;

    /**
     * 实验室编码
     */
    private String labCode;

    /**
     * 待办类型
     */
    private String type;

    /**
     * 对象ID
     */
    private String objectId;

    /**
     * 对象No
     */
    private String objectNo;

    /**
     * assignee VARCHAR(50)<br>
     * 责任人
     */
    private String assignee;

    /**
     * status VARCHAR(2) 必填<br>
     * 10:TODO;20:Complate;30:Cancel;
     */
    private String status;

    /**
     * data OTHER<br>
     * 待办数据
     */
    private Object data;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProductLineCode() {
        return productLineCode;
    }

    public void setProductLineCode(String productLineCode) {
        this.productLineCode = productLineCode;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getObjectNo() {
        return objectNo;
    }

    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
