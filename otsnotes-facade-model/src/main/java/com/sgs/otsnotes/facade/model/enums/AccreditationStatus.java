package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

@Dict
public enum AccreditationStatus {
    Inactive(0, "Inactive"),
    Active(1, "Active");

    private int status;
    private String code;

    AccreditationStatus(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    static Map<Integer, AccreditationStatus> maps = new HashMap<>();
    static Map<String, AccreditationStatus> codeMaps = new HashMap<>();

    static {
        for (AccreditationStatus status : AccreditationStatus.values()) {
            maps.put(status.getStatus(), status);
            codeMaps.put(status.getCode().toLowerCase(), status);
        }
    };

    public static AccreditationStatus getStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    /**
     *
     * @param status
     * @param aStatus
     * @return
     */
    public static boolean check(Integer status, AccreditationStatus... aStatus) {
        if (status == null || !maps.containsKey(status.intValue()) || aStatus == null || aStatus.length <= 0){
            return false;
        }
        for (AccreditationStatus aaStatus: aStatus){
            if (status == aaStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    public static AccreditationStatus getCode(String code) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase())) {
            return null;
        }
        return codeMaps.get(code.toLowerCase());
    }

    /**
     *
     * @param code
     * @param aStatus
     * @return
     */
    public static boolean check(String code, AccreditationStatus... aStatus) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase()) || aStatus == null || aStatus.length <= 0){
            return false;
        }
        AccreditationStatus accreditationStatus = codeMaps.get(code.toLowerCase());
        for (AccreditationStatus aaStatus: aStatus){
            if (aaStatus.getStatus() == accreditationStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param aStatus
     * @return
     */
    public boolean check(AccreditationStatus... aStatus){
        if (aStatus == null || aStatus.length <= 0){
            return false;
        }
        for (AccreditationStatus accreditationStatus: aStatus){
            if (this.getStatus() == accreditationStatus.getStatus()){
                return true;
            }
        }
        return false;
    }
}
