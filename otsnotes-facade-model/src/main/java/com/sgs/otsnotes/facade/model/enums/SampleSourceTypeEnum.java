package com.sgs.otsnotes.facade.model.enums;

import org.apache.commons.lang3.StringUtils;

public enum SampleSourceTypeEnum {
    SGS("sgs", "由sgs创建"),
    CUSTOMER("customer", "客户创建");

    private String code;
    private String desc;

    SampleSourceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    /**
     *
     * @param sampleSourceType
     * @param sampleSourceTypes
     * @return
     */
    public static boolean check(String sampleSourceType, SampleSourceTypeEnum... sampleSourceTypes){
        if (StringUtils.isEmpty(sampleSourceType) || sampleSourceTypes == null || sampleSourceTypes.length <= 0){
            return false;
        }
        for (SampleSourceTypeEnum sourceTypeEnum : sampleSourceTypes){
            if (StringUtils.equalsIgnoreCase(sampleSourceType, sourceTypeEnum.getCode())) {
                return true;
            }
        }
        return false;
    }

}
