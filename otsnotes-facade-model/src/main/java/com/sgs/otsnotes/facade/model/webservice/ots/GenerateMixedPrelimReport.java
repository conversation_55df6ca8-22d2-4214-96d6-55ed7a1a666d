/**
 * GenerateMixedPrelimReport.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GenerateMixedPrelimReport  implements java.io.Serializable {
    private TemplateInstanceInfo prelimReportInfo;

    private ReportDatasource prelimDatasource;

    private String testReportFilePath;

    private String testReportConfigFilePath;

    public GenerateMixedPrelimReport() {
    }

    public GenerateMixedPrelimReport(
           TemplateInstanceInfo prelimReportInfo,
           ReportDatasource prelimDatasource,
           String testReportFilePath,
           String testReportConfigFilePath) {
           this.prelimReportInfo = prelimReportInfo;
           this.prelimDatasource = prelimDatasource;
           this.testReportFilePath = testReportFilePath;
           this.testReportConfigFilePath = testReportConfigFilePath;
    }


    /**
     * Gets the prelimReportInfo value for this GenerateMixedPrelimReport.
     * 
     * @return prelimReportInfo
     */
    public TemplateInstanceInfo getPrelimReportInfo() {
        return prelimReportInfo;
    }


    /**
     * Sets the prelimReportInfo value for this GenerateMixedPrelimReport.
     * 
     * @param prelimReportInfo
     */
    public void setPrelimReportInfo(TemplateInstanceInfo prelimReportInfo) {
        this.prelimReportInfo = prelimReportInfo;
    }


    /**
     * Gets the prelimDatasource value for this GenerateMixedPrelimReport.
     * 
     * @return prelimDatasource
     */
    public ReportDatasource getPrelimDatasource() {
        return prelimDatasource;
    }


    /**
     * Sets the prelimDatasource value for this GenerateMixedPrelimReport.
     * 
     * @param prelimDatasource
     */
    public void setPrelimDatasource(ReportDatasource prelimDatasource) {
        this.prelimDatasource = prelimDatasource;
    }


    /**
     * Gets the testReportFilePath value for this GenerateMixedPrelimReport.
     * 
     * @return testReportFilePath
     */
    public String getTestReportFilePath() {
        return testReportFilePath;
    }


    /**
     * Sets the testReportFilePath value for this GenerateMixedPrelimReport.
     * 
     * @param testReportFilePath
     */
    public void setTestReportFilePath(String testReportFilePath) {
        this.testReportFilePath = testReportFilePath;
    }


    /**
     * Gets the testReportConfigFilePath value for this GenerateMixedPrelimReport.
     * 
     * @return testReportConfigFilePath
     */
    public String getTestReportConfigFilePath() {
        return testReportConfigFilePath;
    }


    /**
     * Sets the testReportConfigFilePath value for this GenerateMixedPrelimReport.
     * 
     * @param testReportConfigFilePath
     */
    public void setTestReportConfigFilePath(String testReportConfigFilePath) {
        this.testReportConfigFilePath = testReportConfigFilePath;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GenerateMixedPrelimReport)) {
            return false;
        }
        GenerateMixedPrelimReport other = (GenerateMixedPrelimReport) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.prelimReportInfo==null && other.getPrelimReportInfo()==null) || 
             (this.prelimReportInfo!=null &&
              this.prelimReportInfo.equals(other.getPrelimReportInfo()))) &&
            ((this.prelimDatasource==null && other.getPrelimDatasource()==null) || 
             (this.prelimDatasource!=null &&
              this.prelimDatasource.equals(other.getPrelimDatasource()))) &&
            ((this.testReportFilePath==null && other.getTestReportFilePath()==null) || 
             (this.testReportFilePath!=null &&
              this.testReportFilePath.equals(other.getTestReportFilePath()))) &&
            ((this.testReportConfigFilePath==null && other.getTestReportConfigFilePath()==null) || 
             (this.testReportConfigFilePath!=null &&
              this.testReportConfigFilePath.equals(other.getTestReportConfigFilePath())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getPrelimReportInfo() != null) {
            _hashCode += getPrelimReportInfo().hashCode();
        }
        if (getPrelimDatasource() != null) {
            _hashCode += getPrelimDatasource().hashCode();
        }
        if (getTestReportFilePath() != null) {
            _hashCode += getTestReportFilePath().hashCode();
        }
        if (getTestReportConfigFilePath() != null) {
            _hashCode += getTestReportConfigFilePath().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GenerateMixedPrelimReport.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GenerateMixedPrelimReport"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("prelimReportInfo");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "prelimReportInfo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInstanceInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("prelimDatasource");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "prelimDatasource"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ReportDatasource"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("testReportFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "testReportFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("testReportConfigFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "testReportConfigFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
