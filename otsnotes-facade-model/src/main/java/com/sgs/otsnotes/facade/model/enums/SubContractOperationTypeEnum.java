package com.sgs.otsnotes.facade.model.enums;

/**
 * 分包操作类型枚举
 *
 * @Author: Assistant
 * @Date: 2025-09-02
 * @Description: 定义分包操作的类型枚举值
 */
public enum SubContractOperationTypeEnum {

    SAVE("save", "保存"),
    START("start", "开始"),
    SAVE_REPORT("saveReport", "保存报告");

    private String code;
    private String desc;

    SubContractOperationTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 操作类型代码
     * @return 对应的枚举值，如果不存在返回null
     */
    public static SubContractOperationTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SubContractOperationTypeEnum operationType : values()) {
            if (operationType.getCode().equals(code)) {
                return operationType;
            }
        }
        return null;
    }

    /**
     * 验证操作类型是否有效
     *
     * @param code 操作类型代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 获取所有有效的操作类型代码
     *
     * @return 所有有效的操作类型代码字符串，用逗号分隔
     */
    public static String getAllValidCodes() {
        StringBuilder sb = new StringBuilder();
        for (SubContractOperationTypeEnum operationType : values()) {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            sb.append(operationType.getCode());
        }
        return sb.toString();
    }
}