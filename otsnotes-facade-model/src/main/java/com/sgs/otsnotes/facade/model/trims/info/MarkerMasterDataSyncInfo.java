package com.sgs.otsnotes.facade.model.trims.info;

/**
 *
 */
public final class MarkerMasterDataSyncInfo extends BaseSyncInfo{
    /**
     *
     */
    private long id;

    /**
     *
     */
    private Long markerId;

    /**
     *
     */
    private String markerName;

    /**
     *
     */
    private Long parentMarkerId;

    /**
     *
     */
    private String productLineId;

    /**
     *
     */
    private int customerAccountId;

    /**
     *
     */
    private String masterData;

    /**
     * 1代表有效数据，0代表无效数据
     */
    private int status;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Long getMarkerId() {
        return markerId;
    }

    public void setMarkerId(Long markerId) {
        this.markerId = markerId;
    }

    public String getMarkerName() {
        return markerName;
    }

    public void setMarkerName(String markerName) {
        this.markerName = markerName;
    }

    public Long getParentMarkerId() {
        return parentMarkerId;
    }

    public void setParentMarkerId(Long parentMarkerId) {
        this.parentMarkerId = parentMarkerId;
    }

    public String getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(String productLineId) {
        this.productLineId = productLineId;
    }

    public int getCustomerAccountId() {
        return customerAccountId;
    }

    public void setCustomerAccountId(int customerAccountId) {
        this.customerAccountId = customerAccountId;
    }

    public String getMasterData() {
        return masterData;
    }

    public void setMasterData(String masterData) {
        this.masterData = masterData;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
