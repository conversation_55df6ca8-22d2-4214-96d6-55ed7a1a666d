package com.sgs.otsnotes.facade.model.pageoffice;

import java.util.List;

public class Message
{
    public static String NOTICE = "notice";     //通知
    public static String MESSAGE = "message";   //消息
    private String type;
    private String content;
    private List<OnlineUser> onlineUserList;
    public String getType()
    {
        return type;
    }
    public void setType(String type)
    {
        this.type = type;
    }
    public String getContent()
    {
        return content;
    }
    public void setContent(String content)
    {
        this.content = content;
    }
    public List<OnlineUser> getOnlineUserList()
    {
        return onlineUserList;
    }
    public void setOnlineUserList(List<OnlineUser> onlineUserList)
    {
        this.onlineUserList = onlineUserList;
    }
    
}
