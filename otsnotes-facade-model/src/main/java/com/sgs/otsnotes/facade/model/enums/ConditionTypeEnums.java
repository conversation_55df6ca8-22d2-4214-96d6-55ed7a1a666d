package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

/**
 * <AUTHOR>
 */

@Dict
public enum ConditionTypeEnums {
    None(0, "None"),
    Parent(1, "Parent Condition"),
    Child(2, "Child Condition");
    @DictCodeField
    private int code;
    @DictLabelField
    private String message;

    ConditionTypeEnums(int value, String message) {
        this.code = value;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return message;
    }

    static Map<Integer, ConditionTypeEnums> maps = new HashMap<>();

    static {
        for (ConditionTypeEnums type : ConditionTypeEnums.values()) {
            maps.put(type.getCode(), type);
        }
    }

    public static ConditionTypeEnums findCode(Integer code) {
        if (code == null || !maps.containsKey(code)){
            return null;
        }
        ConditionTypeEnums type = maps.get(code);
        if (type == null) {
            /*throw new IllegalArgumentException("ConclusionType not found" + code);*/
            return null;
        }
        return type;
    }

    public static boolean check(Integer code) {
        if (code == null || !maps.containsKey(code)){
            return false;
        }
        return maps.get(code) != null;
    }

    public static boolean check(Integer code, ConditionTypeEnums conditionType) {
        if (code == null || !maps.containsKey(code)){
            return false;
        }
        return maps.get(code) != conditionType;
    }
}
