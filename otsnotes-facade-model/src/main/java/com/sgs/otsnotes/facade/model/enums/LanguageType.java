package com.sgs.otsnotes.facade.model.enums;

import com.google.common.collect.Lists;

import java.util.*;

public enum LanguageType {
    EN(1, "EN","ENG","English Report"),
    CHI(2, "CHI","ZHS","Chinese Report");

    private int languageId;
    private String code;
    private String starlimsCode;
    private String message;

    LanguageType(int languageId, String code,String starlimsCode , String message) {
        this.languageId = languageId;
        this.code = code;
        this.starlimsCode = starlimsCode;
        this.message = message;
    }

    public String getStarlimsCode() {
        return starlimsCode;
    }

    public void setStarlimsCode(String starlimsCode) {
        this.starlimsCode = starlimsCode;
    }

    public int getLanguageId() {
        return languageId;
    }

    public String getCode() {
        return this.code;
    }

    public String getMessage() {
        return message;
    }

    static Map<Integer, LanguageType> maps = new HashMap<>();
    static Map<String, LanguageType> starLimsMaps = new HashMap<>();

    static Map<String, LanguageType> languageMaps = new HashMap<>();

    static {
        for (LanguageType languageType : LanguageType.values()) {
            maps.put(languageType.getLanguageId(), languageType);
            starLimsMaps.put(languageType.getStarlimsCode(), languageType);
            languageMaps.put(languageType.getCode(), languageType);
        }
    }

    /**
     *
     * @param languageId
     * @return
     */
    public static LanguageType findCode(Integer languageId) {
        if (languageId == null || !maps.containsKey(languageId)){
            return null;
        }
        return maps.get(languageId);
    }

    public static LanguageType findStarLimsCode(String starlimsCode) {
        if (starlimsCode == null){
            return null;
        }
        return starLimsMaps.get(starlimsCode.toUpperCase());
    }

    public static LanguageType findStarLimsLangCode(String code) {
        if (code == null || !starLimsMaps.containsKey(code)){
            return EN;
        }
        return starLimsMaps.get(code);
    }

    /**
     *
     * @param code
     * @return
     */
    public static LanguageType findLangCode(String code) {
        if (code == null || !languageMaps.containsKey(code)){
            return EN;
        }
        return languageMaps.get(code);
    }

    /**
     *
     * @param languageId
     * @param type
     * @return
     */
    public static boolean check(Integer languageId, LanguageType type) {
        if (languageId == null || !maps.containsKey(languageId)){
            return false;
        }
        return maps.get(languageId) == type;
    }

    /**
     *
     * @param langId
     * @param langType
     * @return
     */
    public static boolean checkLang(Integer langId, LanguageType langType) {
        if (langId == null || langType == null){
            return false;
        }
        for (LanguageType languageType: LanguageType.values()) {
            if ((languageType.getLanguageId() & langId.intValue()) > 0 && languageType == langType){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param languageId
     * @return
     */
    public boolean check(Integer languageId) {
        if (languageId == null || !maps.containsKey(languageId)){
            return false;
        }
        return languageId != null && languageId.equals(this.getLanguageId());
    }


    /**
     * 根据传入的languageId 返回对应的集合
     * @param languageId
     * @return
     */
    public static List<Map> languageTypeList(int languageId){
        List<Map> list = new ArrayList<>();
        for (LanguageType type : LanguageType.values()) {
            if ((type.languageId & languageId) > 0){
                Map<String,Object> map = new HashMap<>();
                map.put("id",type.languageId);
                map.put("code",type.code);
                list.add(map);
            }
        }
        return list;
    }

    /**
     *
     * @param languageId
     * @return
     */
    public static List<LanguageType> getLanguageType(int languageId){
        List<LanguageType> languages = Lists.newArrayList();
        for (LanguageType languageType : LanguageType.values()) {
            if ((languageType.languageId & languageId) > 0){
                languages.add(languageType);
            }
        }
        return languages;
    }
}

