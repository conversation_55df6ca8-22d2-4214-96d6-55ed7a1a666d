/**
 * TestReportTemplateSoap.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots.template;

public interface TestReportTemplateSoap extends java.rmi.Remote {
    public com.sgs.otsnotes.facade.model.webservice.ots.template.TemplateListView[] queryBusinessTemplateList(int templateTypeID, int buSubTypeID, int templateID, java.lang.String templateName, java.lang.String customerName, java.lang.String customerNo, java.lang.String templatePackageName, java.lang.String groupCode, java.lang.String groupName, java.lang.String PPTemplateID) throws java.rmi.RemoteException;
    public com.sgs.otsnotes.facade.model.webservice.ots.template.TemplateListView[] queryBusinessTemplateList2(int applicationID, int templateTypeID, int buId, int templateID, java.lang.String templateName, java.lang.String customerName, java.lang.String customerNo, java.lang.String templatePackageName, java.lang.String groupCode, java.lang.String groupName) throws java.rmi.RemoteException;
}
