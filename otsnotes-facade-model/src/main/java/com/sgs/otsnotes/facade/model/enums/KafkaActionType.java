package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum KafkaActionType {
    SubContract(1,"saveSubContract","subContract"),
    TodoStatus(97,"updateTodoStatus", "todoStatus"),
    TodoList(98,"saveTodoList","createTodo");

    private int type;
    private String action;
    private String code;

    KafkaActionType(int type, String action, String code) {
        this.type = type;
        this.action = action;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getAction() {
        return action;
    }

    public int getType() {
        return type;
    }

    public static final Map<Integer, KafkaActionType> maps = new HashMap<Integer, KafkaActionType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (KafkaActionType enu : KafkaActionType.values()) {
                put(enu.getType(), enu);
            }
        }
    };

    public static String getMessage(Integer type) {
        if (type == null || !maps.containsKey(type)) {
            return null;
        }
        return maps.get(type).getCode();
    }

}
