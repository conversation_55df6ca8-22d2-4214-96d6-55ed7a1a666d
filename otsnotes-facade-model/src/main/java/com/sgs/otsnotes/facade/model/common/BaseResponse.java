package com.sgs.otsnotes.facade.model.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by <PERSON> on 2019/05/09.
 * Description:
 */
@ApiModel(value = "BaseResponse", description = "响应")
public class BaseResponse<T> extends PrintFriendliness {
    private static final long serialVersionUID = -5719901720924490738L;
    /**
     * @see com.sgs.otsnotes.facade.model.common.ResponseCode
     */
    @ApiModelProperty(notes = "响应状态： \n " +
            "200：操作成功  \n " +
            "201：用户权限过期，请重新登录  \n " +
            "500：系统异常，请稍后重试  \n ")
    private int status;
    /**
     *
     */
    @ApiModelProperty(notes = "消息（对应的响应消息）")
    private String message;
    /**
     *
     */
    @ApiModelProperty(notes = "响应数据对象")
    private T data;
    /**
     *
     */
    @ApiModelProperty(notes = "业务版本Id")
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private long versionId;

    /**
     *
     */
    @ApiModelProperty(hidden = true, notes = "异常堆栈信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String stackTrace;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public long getVersionId() {
        return versionId;
    }

    public void setVersionId(long versionId) {
        this.versionId = versionId;
    }

    public String getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(String stackTrace) {
        this.stackTrace = stackTrace;
    }

    /**
     * 默认构造成功的响应
     */
    public BaseResponse() {
        this.setStatus(ResponseCode.SUCCESS.getCode());
        this.setMessage(ResponseCode.SUCCESS.getMessage());
    }

    public BaseResponse(int code,String msg) {
        this.setStatus(code);
        this.setMessage(msg);
    }

    public static <T> BaseResponse newSuccessInstance(T data) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setStatus(200);
        baseResponse.setMessage(ResponseCode.SUCCESS.getMessage());
        baseResponse.setData(data);
        return baseResponse;
    }

    public static <T> BaseResponse newInstance(T data) {
        BaseResponse baseResponse = new BaseResponse();
        if (data == null){
            baseResponse.setStatus(ResponseCode.UNKNOWN.getCode());
            baseResponse.setMessage(ResponseCode.UNKNOWN.getMessage());
            return baseResponse;
        }
        baseResponse.setStatus(ResponseCode.SUCCESS.getCode());
        baseResponse.setMessage(ResponseCode.SUCCESS.getMessage());
        baseResponse.setData(data);
        return baseResponse;
    }

    public static BaseResponse newFailInstance(ResponseCode errorCode) {
        BaseResponse result = new BaseResponse();
        result.setStatus(errorCode.getCode());
        result.setMessage(errorCode.getMessage());
        return result;
    }

    public static BaseResponse newFailInstance(String errMessage) {
        BaseResponse result = new BaseResponse();
        result.setStatus(ResponseCode.ILLEGAL_ARGUMENT.getCode());
        result.setMessage(errMessage);
        return result;
    }

    public static BaseResponse newInstance(CustomResult result) {
        BaseResponse rspResult = new BaseResponse();
        rspResult.setData(result.getData());
        rspResult.setVersionId(result.getVersionId());
        rspResult.setMessage(result.getMsg());
        if (result.isSuccess() && result.getStatus() > 0){
            rspResult.setStatus(result.getStatus());
        }
        if (!result.isSuccess()){
            rspResult.setStatus(ResponseCode.UNKNOWN.getCode());
            rspResult.setStackTrace(result.getStackTrace());
            if (result.getMsg() == null || result.getMsg().length() <= 0){
                rspResult.setMessage(ResponseCode.UNKNOWN.getMessage());
            }
        }
        return rspResult;
    }

    /**
     * 增加
     * @param result
     * @return
     */
    public static BaseResponse newInstance(com.sgs.framework.core.base.CustomResult result) {
        BaseResponse rspResult = new BaseResponse();
        rspResult.setData(result.getData());
//        rspResult.setVersionId(result.getVersionId());
        rspResult.setMessage(result.getMsg());
        if (result.isSuccess() && result.getStatus() > 0){
            rspResult.setStatus(result.getStatus());
        }
        if (!result.isSuccess()){
            rspResult.setStatus(ResponseCode.UNKNOWN.getCode());
            rspResult.setStackTrace(result.getStackTrace());
            if (result.getMsg() == null || result.getMsg().length() <= 0){
                rspResult.setMessage(ResponseCode.UNKNOWN.getMessage());
            }
        }
        return rspResult;
    }
}
