package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum StarlimsItemType {
    EA(1, ArtifactType.TestLine, ConclusionType.TestLine, "EA"),
    PP(2, ArtifactType.PP, ConclusionType.PP, "PP"),
    TestLine(3, ArtifactType.TestLine, ConclusionType.TestLine, "TestLine"),
    TestGroup(4, ArtifactType.TestLine, ConclusionType.TestLine, "TestGroup"),
    TestLine_OriginalSample(5, ArtifactType.TestLine, ConclusionType.TestLine_OriginalSample,"TestLine_OriginalSample"),
    ;

    private int code;
    private ArtifactType artifactType;
    private ConclusionType conclusionType;
    private String desc;

    StarlimsItemType(int code, ArtifactType artifactType, ConclusionType conclusionType, String desc) {
        this.code = code;
        this.artifactType = artifactType;
        this.conclusionType = conclusionType;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public ArtifactType getArtifactType() {
        return artifactType;
    }

    public ConclusionType getConclusionType() {
        return conclusionType;
    }

    public String getDesc() {
        return desc;
    }

    public static final Map<Integer, StarlimsItemType> maps = new HashMap<Integer, StarlimsItemType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (StarlimsItemType reportStatus : StarlimsItemType.values()) {
                put(reportStatus.getCode(), reportStatus);
            }
        }
    };

    // 不区分大小写
    public static final Map<String, StarlimsItemType> descMaps = new HashMap<String, StarlimsItemType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (StarlimsItemType itemType : StarlimsItemType.values()) {
                put(itemType.getDesc().toLowerCase(Locale.ROOT), itemType);
            }
        }
    };

    public static StarlimsItemType getCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code.intValue());
    }

    public static StarlimsItemType getItemType(String itemTypeDesc) {
        if (itemTypeDesc == null || !descMaps.containsKey(itemTypeDesc.toLowerCase(Locale.ROOT))){
            return null;
        }
        return descMaps.get(itemTypeDesc.toLowerCase(Locale.ROOT));
    }

    public static ArtifactType getArtifactTypeByItemType(String itemTypeDesc) {
        if (itemTypeDesc == null || !descMaps.containsKey(itemTypeDesc.toLowerCase(Locale.ROOT))){
            return null;
        }
        StarlimsItemType itemType = descMaps.get(itemTypeDesc.toLowerCase(Locale.ROOT));
        if (itemType == null) {
            return null;
        }
        return itemType.getArtifactType();
    }

    public boolean check(StarlimsItemType... starlimsItemTypes){
        if (starlimsItemTypes == null || starlimsItemTypes.length <= 0){
            return false;
        }
        for (StarlimsItemType type: starlimsItemTypes){
            if (this.getCode() == type.getCode()){
                return true;
            }
        }
        return false;
    }

    public static boolean checkItemType(StarlimsItemType itemType, StarlimsItemType... starlimsItemTypes){
        if (itemType == null || starlimsItemTypes == null || starlimsItemTypes.length <= 0) {
            return false;
        }
        return check(itemType.getCode(), starlimsItemTypes);
    }

    public static boolean check(Integer status, StarlimsItemType... starlimsItemTypes){
        if (status == null || !maps.containsKey(status.intValue()) || starlimsItemTypes == null || starlimsItemTypes.length <= 0) {
            return false;
        }
        for (StarlimsItemType itemType : starlimsItemTypes){
            if (status.intValue() == itemType.getCode()){
                return true;
            }
        }
        return false;
    }



}
