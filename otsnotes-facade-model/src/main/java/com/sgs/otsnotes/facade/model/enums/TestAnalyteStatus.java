package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum TestAnalyteStatus {
    Inactive(0, "Inactive"),
    Active(1, "Active");

    private final int status;
    private final String code;

    TestAnalyteStatus(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return this.code;
    }

    public static final Map<String, TestAnalyteStatus> maps = new HashMap<String, TestAnalyteStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TestAnalyteStatus enu : TestAnalyteStatus.values()) {
                put(enu.getCode().toLowerCase(), enu);
            }
        }
    };

    public static TestAnalyteStatus getCode(String code) {
        if (code == null || !maps.containsKey(code.toLowerCase())) {
            return null;
        }
        return maps.get(code.toLowerCase());
    }
}
