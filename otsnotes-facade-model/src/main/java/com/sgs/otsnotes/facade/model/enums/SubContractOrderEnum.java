package com.sgs.otsnotes.facade.model.enums;

/**
 * package com.sgs.otsnotes.facade.model.enums;
 */
@Deprecated
public enum SubContractOrderEnum {
    COMMON(0, "普通"),
    INTERNAL_SUBCONTRACTING(1, "内部分包"),
    TO_SLIM(2, "To Slim"),
    TO_STARLIMS(4, "To Starlims");

    private int code;
    private String desc;

    SubContractOrderEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean check(Integer status, SubContractOrderEnum... enums) {
        if (status == null) {
            return false;
        }
        for (SubContractOrderEnum anEnum : enums) {
            if (anEnum.getCode() == status.intValue()) {
                return true;
            }
        }
        return false;
    }

    public static SubContractOrderEnum findStatus(Integer status) {
        if (status == null) {
            return null;
        }
        for (SubContractOrderEnum enu : values()) {
            if (enu.getCode() == status.intValue()) {
                return enu;
            }
        }
        return null;
    }
}
