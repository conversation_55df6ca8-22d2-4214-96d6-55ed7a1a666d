/**
 * TemplateInstanceInfo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class TemplateInstanceInfo  implements java.io.Serializable {
    private int instanceId;

    private String instanceNumber;

    private int templateId;

    private String templateFilePath;

    private String templateConfigFilePath;

    private String instanceFilePath;

    private String instanceConfigFilePath;

    private String PDFInstanceFilePath;

    private int templateType;

    private int UIViewType;

    private boolean isGenerateConclusionArea;

    private boolean isNeedDSS;

    private int reportLanguageId;

    private int pdfConverterNo;

    public TemplateInstanceInfo() {
    }

    public TemplateInstanceInfo(
           int instanceId,
           String instanceNumber,
           int templateId,
           String templateFilePath,
           String templateConfigFilePath,
           String instanceFilePath,
           String instanceConfigFilePath,
           String PDFInstanceFilePath,
           int templateType,
           int UIViewType,
           boolean isGenerateConclusionArea,
           boolean isNeedDSS,
           int reportLanguageId,
           int pdfConverterNo) {
           this.instanceId = instanceId;
           this.instanceNumber = instanceNumber;
           this.templateId = templateId;
           this.templateFilePath = templateFilePath;
           this.templateConfigFilePath = templateConfigFilePath;
           this.instanceFilePath = instanceFilePath;
           this.instanceConfigFilePath = instanceConfigFilePath;
           this.PDFInstanceFilePath = PDFInstanceFilePath;
           this.templateType = templateType;
           this.UIViewType = UIViewType;
           this.isGenerateConclusionArea = isGenerateConclusionArea;
           this.isNeedDSS = isNeedDSS;
           this.reportLanguageId = reportLanguageId;
           this.pdfConverterNo = pdfConverterNo;
    }


    /**
     * Gets the instanceId value for this TemplateInstanceInfo.
     * 
     * @return instanceId
     */
    public int getInstanceId() {
        return instanceId;
    }


    /**
     * Sets the instanceId value for this TemplateInstanceInfo.
     * 
     * @param instanceId
     */
    public void setInstanceId(int instanceId) {
        this.instanceId = instanceId;
    }


    /**
     * Gets the instanceNumber value for this TemplateInstanceInfo.
     * 
     * @return instanceNumber
     */
    public String getInstanceNumber() {
        return instanceNumber;
    }


    /**
     * Sets the instanceNumber value for this TemplateInstanceInfo.
     * 
     * @param instanceNumber
     */
    public void setInstanceNumber(String instanceNumber) {
        this.instanceNumber = instanceNumber;
    }


    /**
     * Gets the templateId value for this TemplateInstanceInfo.
     * 
     * @return templateId
     */
    public int getTemplateId() {
        return templateId;
    }


    /**
     * Sets the templateId value for this TemplateInstanceInfo.
     * 
     * @param templateId
     */
    public void setTemplateId(int templateId) {
        this.templateId = templateId;
    }


    /**
     * Gets the templateFilePath value for this TemplateInstanceInfo.
     * 
     * @return templateFilePath
     */
    public String getTemplateFilePath() {
        return templateFilePath;
    }


    /**
     * Sets the templateFilePath value for this TemplateInstanceInfo.
     * 
     * @param templateFilePath
     */
    public void setTemplateFilePath(String templateFilePath) {
        this.templateFilePath = templateFilePath;
    }


    /**
     * Gets the templateConfigFilePath value for this TemplateInstanceInfo.
     * 
     * @return templateConfigFilePath
     */
    public String getTemplateConfigFilePath() {
        return templateConfigFilePath;
    }


    /**
     * Sets the templateConfigFilePath value for this TemplateInstanceInfo.
     * 
     * @param templateConfigFilePath
     */
    public void setTemplateConfigFilePath(String templateConfigFilePath) {
        this.templateConfigFilePath = templateConfigFilePath;
    }


    /**
     * Gets the instanceFilePath value for this TemplateInstanceInfo.
     * 
     * @return instanceFilePath
     */
    public String getInstanceFilePath() {
        return instanceFilePath;
    }


    /**
     * Sets the instanceFilePath value for this TemplateInstanceInfo.
     * 
     * @param instanceFilePath
     */
    public void setInstanceFilePath(String instanceFilePath) {
        this.instanceFilePath = instanceFilePath;
    }


    /**
     * Gets the instanceConfigFilePath value for this TemplateInstanceInfo.
     * 
     * @return instanceConfigFilePath
     */
    public String getInstanceConfigFilePath() {
        return instanceConfigFilePath;
    }


    /**
     * Sets the instanceConfigFilePath value for this TemplateInstanceInfo.
     * 
     * @param instanceConfigFilePath
     */
    public void setInstanceConfigFilePath(String instanceConfigFilePath) {
        this.instanceConfigFilePath = instanceConfigFilePath;
    }


    /**
     * Gets the PDFInstanceFilePath value for this TemplateInstanceInfo.
     * 
     * @return PDFInstanceFilePath
     */
    public String getPDFInstanceFilePath() {
        return PDFInstanceFilePath;
    }


    /**
     * Sets the PDFInstanceFilePath value for this TemplateInstanceInfo.
     * 
     * @param PDFInstanceFilePath
     */
    public void setPDFInstanceFilePath(String PDFInstanceFilePath) {
        this.PDFInstanceFilePath = PDFInstanceFilePath;
    }


    /**
     * Gets the templateType value for this TemplateInstanceInfo.
     * 
     * @return templateType
     */
    public int getTemplateType() {
        return templateType;
    }


    /**
     * Sets the templateType value for this TemplateInstanceInfo.
     * 
     * @param templateType
     */
    public void setTemplateType(int templateType) {
        this.templateType = templateType;
    }


    /**
     * Gets the UIViewType value for this TemplateInstanceInfo.
     * 
     * @return UIViewType
     */
    public int getUIViewType() {
        return UIViewType;
    }


    /**
     * Sets the UIViewType value for this TemplateInstanceInfo.
     * 
     * @param UIViewType
     */
    public void setUIViewType(int UIViewType) {
        this.UIViewType = UIViewType;
    }


    /**
     * Gets the isGenerateConclusionArea value for this TemplateInstanceInfo.
     * 
     * @return isGenerateConclusionArea
     */
    public boolean isIsGenerateConclusionArea() {
        return isGenerateConclusionArea;
    }


    /**
     * Sets the isGenerateConclusionArea value for this TemplateInstanceInfo.
     * 
     * @param isGenerateConclusionArea
     */
    public void setIsGenerateConclusionArea(boolean isGenerateConclusionArea) {
        this.isGenerateConclusionArea = isGenerateConclusionArea;
    }


    /**
     * Gets the isNeedDSS value for this TemplateInstanceInfo.
     * 
     * @return isNeedDSS
     */
    public boolean isIsNeedDSS() {
        return isNeedDSS;
    }


    /**
     * Sets the isNeedDSS value for this TemplateInstanceInfo.
     * 
     * @param isNeedDSS
     */
    public void setIsNeedDSS(boolean isNeedDSS) {
        this.isNeedDSS = isNeedDSS;
    }


    /**
     * Gets the reportLanguageId value for this TemplateInstanceInfo.
     * 
     * @return reportLanguageId
     */
    public int getReportLanguageId() {
        return reportLanguageId;
    }


    /**
     * Sets the reportLanguageId value for this TemplateInstanceInfo.
     * 
     * @param reportLanguageId
     */
    public void setReportLanguageId(int reportLanguageId) {
        this.reportLanguageId = reportLanguageId;
    }


    /**
     * Gets the pdfConverterNo value for this TemplateInstanceInfo.
     * 
     * @return pdfConverterNo
     */
    public int getPdfConverterNo() {
        return pdfConverterNo;
    }


    /**
     * Sets the pdfConverterNo value for this TemplateInstanceInfo.
     * 
     * @param pdfConverterNo
     */
    public void setPdfConverterNo(int pdfConverterNo) {
        this.pdfConverterNo = pdfConverterNo;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof TemplateInstanceInfo)) {
            return false;
        }
        TemplateInstanceInfo other = (TemplateInstanceInfo) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.instanceId == other.getInstanceId() &&
            ((this.instanceNumber==null && other.getInstanceNumber()==null) || 
             (this.instanceNumber!=null &&
              this.instanceNumber.equals(other.getInstanceNumber()))) &&
            this.templateId == other.getTemplateId() &&
            ((this.templateFilePath==null && other.getTemplateFilePath()==null) || 
             (this.templateFilePath!=null &&
              this.templateFilePath.equals(other.getTemplateFilePath()))) &&
            ((this.templateConfigFilePath==null && other.getTemplateConfigFilePath()==null) || 
             (this.templateConfigFilePath!=null &&
              this.templateConfigFilePath.equals(other.getTemplateConfigFilePath()))) &&
            ((this.instanceFilePath==null && other.getInstanceFilePath()==null) || 
             (this.instanceFilePath!=null &&
              this.instanceFilePath.equals(other.getInstanceFilePath()))) &&
            ((this.instanceConfigFilePath==null && other.getInstanceConfigFilePath()==null) || 
             (this.instanceConfigFilePath!=null &&
              this.instanceConfigFilePath.equals(other.getInstanceConfigFilePath()))) &&
            ((this.PDFInstanceFilePath==null && other.getPDFInstanceFilePath()==null) || 
             (this.PDFInstanceFilePath!=null &&
              this.PDFInstanceFilePath.equals(other.getPDFInstanceFilePath()))) &&
            this.templateType == other.getTemplateType() &&
            this.UIViewType == other.getUIViewType() &&
            this.isGenerateConclusionArea == other.isIsGenerateConclusionArea() &&
            this.isNeedDSS == other.isIsNeedDSS() &&
            this.reportLanguageId == other.getReportLanguageId() &&
            this.pdfConverterNo == other.getPdfConverterNo();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += getInstanceId();
        if (getInstanceNumber() != null) {
            _hashCode += getInstanceNumber().hashCode();
        }
        _hashCode += getTemplateId();
        if (getTemplateFilePath() != null) {
            _hashCode += getTemplateFilePath().hashCode();
        }
        if (getTemplateConfigFilePath() != null) {
            _hashCode += getTemplateConfigFilePath().hashCode();
        }
        if (getInstanceFilePath() != null) {
            _hashCode += getInstanceFilePath().hashCode();
        }
        if (getInstanceConfigFilePath() != null) {
            _hashCode += getInstanceConfigFilePath().hashCode();
        }
        if (getPDFInstanceFilePath() != null) {
            _hashCode += getPDFInstanceFilePath().hashCode();
        }
        _hashCode += getTemplateType();
        _hashCode += getUIViewType();
        _hashCode += (isIsGenerateConclusionArea() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += (isIsNeedDSS() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += getReportLanguageId();
        _hashCode += getPdfConverterNo();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(TemplateInstanceInfo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInstanceInfo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("instanceId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "InstanceId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("instanceNumber");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "InstanceNumber"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateConfigFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateConfigFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("instanceFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "InstanceFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("instanceConfigFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "InstanceConfigFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PDFInstanceFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PDFInstanceFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UIViewType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "UIViewType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isGenerateConclusionArea");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsGenerateConclusionArea"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isNeedDSS");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsNeedDSS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("reportLanguageId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ReportLanguageId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("pdfConverterNo");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PdfConverterNo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
