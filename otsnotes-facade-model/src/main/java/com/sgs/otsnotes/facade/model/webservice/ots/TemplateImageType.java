/**
 * TemplateImageType.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class TemplateImageType implements java.io.Serializable {
    private String _value_;
    private static java.util.HashMap _table_ = new java.util.HashMap();

    // Constructor
    protected TemplateImageType(String value) {
        _value_ = value;
        _table_.put(_value_,this);
    }

    public static final String _FreeImage = "FreeImage";
    public static final String _CareInstruction = "CareInstruction";
    public static final String _ElectronicSignature = "ElectronicSignature";
    public static final String _HoklasLogo = "HoklasLogo";
    public static final String _ClientLogo = "ClientLogo";
    public static final String _DARLogo = "DARLogo";
    public static final String _CertificateImage = "CertificateImage";
    public static final String _LabAddressImage = "LabAddressImage";
    public static final String _TestReportBarCode = "TestReportBarCode";
    public static final TemplateImageType FreeImage = new TemplateImageType(_FreeImage);
    public static final TemplateImageType CareInstruction = new TemplateImageType(_CareInstruction);
    public static final TemplateImageType ElectronicSignature = new TemplateImageType(_ElectronicSignature);
    public static final TemplateImageType HoklasLogo = new TemplateImageType(_HoklasLogo);
    public static final TemplateImageType ClientLogo = new TemplateImageType(_ClientLogo);
    public static final TemplateImageType DARLogo = new TemplateImageType(_DARLogo);
    public static final TemplateImageType CertificateImage = new TemplateImageType(_CertificateImage);
    public static final TemplateImageType LabAddressImage = new TemplateImageType(_LabAddressImage);
    public static final TemplateImageType TestReportBarCode = new TemplateImageType(_TestReportBarCode);
    public String getValue() { return _value_;}
    public static TemplateImageType fromValue(String value)
          throws IllegalArgumentException {
        TemplateImageType enumeration = (TemplateImageType)
            _table_.get(value);
        if (enumeration==null) {
            throw new IllegalArgumentException();
        }
        return enumeration;
    }
    public static TemplateImageType fromString(String value)
          throws IllegalArgumentException {
        return fromValue(value);
    }
    public boolean equals(Object obj) {return (obj == this);}
    public int hashCode() { return toString().hashCode();}
    public String toString() { return _value_;}
    public Object readResolve() throws java.io.ObjectStreamException { return fromValue(_value_);}
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumSerializer(
            _javaType, _xmlType);
    }
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumDeserializer(
            _javaType, _xmlType);
    }
    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(TemplateImageType.class);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateImageType"));
    }
    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

}
