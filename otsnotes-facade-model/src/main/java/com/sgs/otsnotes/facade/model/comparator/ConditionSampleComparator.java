package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.info.condition.ConditionSampleInfo;

import java.util.Comparator;

public class ConditionSampleComparator implements Comparator<ConditionSampleInfo> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public ConditionSampleComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(ConditionSampleInfo o1, ConditionSampleInfo o2) {
        int index = Integer.compare(o1.getSampleSeq(), o2.getSampleSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}