package com.sgs.otsnotes.facade.model.trims;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class TrimsSyncBaseRsp extends PrintFriendliness {
    /**
     *
     */
    private String status;
    /**
     *
     */
    private String massage;
    /**
     *
     */
    private String code;
    /**
     * 200：成功、400：鉴权失败或Token失效、500：处理失败或业务异常
     */
    private Integer httpStatus;
    /**
     * Internal Server Error, The API program error
     */
    private String errorMessage;
    /**
     * ********
     */
    private String errorCode;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMassage() {
        return massage;
    }

    public void setMassage(String massage) {
        this.massage = massage;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getHttpStatus() {
        return httpStatus;
    }

    public void setHttpStatus(Integer httpStatus) {
        this.httpStatus = httpStatus;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
}
