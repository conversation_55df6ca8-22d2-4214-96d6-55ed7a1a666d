package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;
import java.util.Set;

public class CustomerAppSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer customerAccountId;
    /**
     *
     */
    private String reportReferenceNote;
    /**
     *
     */
    private String conditionInstructions;
    /**
     *
     */
    private Integer workInstructionId;

    private Integer workingInstructionId;
    /**
     *
     */
    private Integer productLineId;
    /**
     *
     */
    private String workingInstructionText;
    /**
     *
     */
    private Integer workingInstructionCategoryId;
    /**
     *
     */
    private String workingInstructionCategoryName;
    /**
     *
     */
    private List<Integer> productAttributeIds;
    /**
     *
     */
    private Set<Integer> testStandardVersionIdentifiers;

    private String workingInstructionName;

    private String workingInstructionShortDesc;

    public Set<Integer> getTestStandardVersionIdentifiers() {
        return testStandardVersionIdentifiers;
    }

    public void setTestStandardVersionIdentifiers(Set<Integer> testStandardVersionIdentifiers) {
        this.testStandardVersionIdentifiers = testStandardVersionIdentifiers;
    }

    public Integer getCustomerAccountId() {
        return customerAccountId;
    }

    public void setCustomerAccountId(Integer customerAccountId) {
        this.customerAccountId = customerAccountId;
    }

    public String getReportReferenceNote() {
        return reportReferenceNote;
    }

    public void setReportReferenceNote(String reportReferenceNote) {
        this.reportReferenceNote = reportReferenceNote;
    }

    public String getConditionInstructions() {
        return conditionInstructions;
    }

    public void setConditionInstructions(String conditionInstructions) {
        this.conditionInstructions = conditionInstructions;
    }

    public Integer getWorkInstructionId() {
        return workInstructionId;
    }

    public void setWorkInstructionId(Integer workInstructionId) {
        this.workInstructionId = workInstructionId;
    }

    public Integer getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }

    public String getWorkingInstructionText() {
        return workingInstructionText;
    }

    public void setWorkingInstructionText(String workingInstructionText) {
        this.workingInstructionText = workingInstructionText;
    }

    public Integer getWorkingInstructionCategoryId() {
        return workingInstructionCategoryId;
    }

    public void setWorkingInstructionCategoryId(Integer workingInstructionCategoryId) {
        this.workingInstructionCategoryId = workingInstructionCategoryId;
    }

    public String getWorkingInstructionCategoryName() {
        return workingInstructionCategoryName;
    }

    public void setWorkingInstructionCategoryName(String workingInstructionCategoryName) {
        this.workingInstructionCategoryName = workingInstructionCategoryName;
    }

    public List<Integer> getProductAttributeIds() {
        return productAttributeIds;
    }

    public void setProductAttributeIds(List<Integer> productAttributeIds) {
        this.productAttributeIds = productAttributeIds;
    }

    public Integer getWorkingInstructionId() {
        return workingInstructionId;
    }

    public void setWorkingInstructionId(Integer workingInstructionId) {
        this.workingInstructionId = workingInstructionId;
    }

    public String getWorkingInstructionName() {
        return workingInstructionName;
    }

    public void setWorkingInstructionName(String workingInstructionName) {
        this.workingInstructionName = workingInstructionName;
    }

    public String getWorkingInstructionShortDesc() {
        return workingInstructionShortDesc;
    }

    public void setWorkingInstructionShortDesc(String workingInstructionShortDesc) {
        this.workingInstructionShortDesc = workingInstructionShortDesc;
    }
}
