package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

public enum CustomerValidateObjectType {
    TestLine("TestLine", "校验维度：TestLine"),
    Analyte("Analyte", "校验维度：Analyte"),
    Limit("Limit", "校验维度：Limit");
    private String code;
    private String desc;

    CustomerValidateObjectType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    static Map<String, CustomerValidateObjectType> typeMaps = new HashMap();
    static {
        for (CustomerValidateObjectType objectType : CustomerValidateObjectType.values()) {
            typeMaps.put(objectType.getCode(), objectType);
        }
    }

    public static CustomerValidateObjectType findType(String type) {
        if (type == null || !typeMaps.containsKey(type)) {
            return null;
        }
        return typeMaps.get(type);
    }

    /**
     *
     * @param type
     * @param analyteType
     * @return
     */
    public static boolean check(String type, CustomerValidateObjectType analyteType) {
        if (type == null || !typeMaps.containsKey(type)){
            return false;
        }
        return typeMaps.get(type) == analyteType;
    }


}
