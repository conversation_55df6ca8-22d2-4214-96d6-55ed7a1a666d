package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.WorkInstructionSyncInfo;

import java.util.List;

/**
 * @Author: Joke.wang
 * @Date: 2022/2/22 13:31
 */
public class WorkInstructionSyncRsp extends TrimsSyncBaseRsp {
    private static final long serialVersionUID = -1819201279893649361L;

    private List<WorkInstructionSyncInfo> data;

    public List<WorkInstructionSyncInfo> getData() {
        return data;
    }

    public void setData(List<WorkInstructionSyncInfo> data) {
        this.data = data;
    }
}
