package com.sgs.otsnotes.facade.model.pageoffice;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sgs.framework.core.base.BaseRequest;
import org.springframework.ui.Model;

import javax.servlet.http.HttpServletRequest;

public class PageOfficeWordReq extends BaseRequest {
    /**
     *
     */
    @JsonIgnore
    private HttpServletRequest request;
    /**
     *
     */
    @JsonIgnore
    private Model map;
    /**
     *
     */
    private String templateId;
    /**
     *
     */
    private String type;
    /**
     *
     */
    private String testLineInstanceId;

    public HttpServletRequest getRequest() {
        return request;
    }

    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    public Model getMap() {
        return map;
    }

    public void setMap(Model map) {
        this.map = map;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId;
    }
}
