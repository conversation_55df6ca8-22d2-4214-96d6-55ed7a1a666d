package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum TestCategoryStatus {
    None(0, "None"),
    Active(1, "Active"),
    PhaseOut(2, "PhaseOut"),
    Inactive(3, "Inactive");

    private int status;
    private String code;

    TestCategoryStatus(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    static Map<Integer, TestCategoryStatus> maps = new HashMap<>();
    static Map<String, TestCategoryStatus> codeMaps = new HashMap<>();

    static {
        for (TestCategoryStatus status : TestCategoryStatus.values()) {
            maps.put(status.getStatus(), status);
            codeMaps.put(status.getCode().toLowerCase(), status);
        }
    };

    public static TestCategoryStatus getStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    /**
     *
     * @param status
     * @param testCategoryStatus
     * @return
     */
    public static boolean check(Integer status, TestCategoryStatus... testCategoryStatus) {
        if (status == null || !maps.containsKey(status.intValue()) || testCategoryStatus == null || testCategoryStatus.length <= 0){
            return false;
        }
        for (TestCategoryStatus tcStatus: testCategoryStatus){
            if (status.intValue() == tcStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    public static TestCategoryStatus getCode(String code) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase())) {
            return null;
        }
        return codeMaps.get(code.toLowerCase());
    }

    /**
     *
     * @param code
     * @param testCategoryStatus
     * @return
     */
    public static boolean check(String code, TestCategoryStatus... testCategoryStatus) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase()) || testCategoryStatus == null || testCategoryStatus.length <= 0){
            return false;
        }
        TestCategoryStatus trimsStatus = codeMaps.get(code.toLowerCase());
        for (TestCategoryStatus tcStatus: testCategoryStatus){
            if (tcStatus.getStatus() == trimsStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param testCategoryStatus
     * @return
     */
    public boolean check(TestCategoryStatus... testCategoryStatus){
        if (testCategoryStatus == null || testCategoryStatus.length <= 0){
            return false;
        }
        for (TestCategoryStatus tcStatus: testCategoryStatus){
            if (this.getStatus() == tcStatus.getStatus()){
                return true;
            }
        }
        return false;
    }
}
