package com.sgs.otsnotes.facade.model.trims.info;

import java.math.BigDecimal;

public class PricingAciPriceItemsSyncInfo {
    /**
     * 用于记录trims中aci和price的关联id
     */
    private Integer mappingId;

    /**
     *
     */
    private Integer chargeItemId;
    /**
     *
     */
    private String chargeItemName;
    /**
     *
     */
    private BigDecimal aciPrice;
    /**
     *
     */
    private String aciDescription;
    /**
     *
     */
    private String aciCondition;
    /**
     *
     */
     private Integer aciFormulaCode;
    /**
     *
     */
     private Integer aciNMeaningsCode;
    /**
     *
     */
     private String aciNEqual;
    /**
     *
     */
     private Integer aciQtyMeaningsCode;
    /**
     *
     */
     private String aciBasePriceEqual;
    /**
     *
     */
     private Integer nTestConditionTypeId;
    /**
     *
     */
     private Integer qtyTestConditionTypeId;

    public Integer getMappingId() {
        return mappingId;
    }

    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }

    public Integer getChargeItemId() {
        return chargeItemId;
    }

    public void setChargeItemId(Integer chargeItemId) {
        this.chargeItemId = chargeItemId;
    }

    public String getChargeItemName() {
        return chargeItemName;
    }

    public void setChargeItemName(String chargeItemName) {
        this.chargeItemName = chargeItemName;
    }

    public BigDecimal getAciPrice() {
        return aciPrice;
    }

    public void setAciPrice(BigDecimal aciPrice) {
        this.aciPrice = aciPrice;
    }

    public String getAciDescription() {
        return aciDescription;
    }

    public void setAciDescription(String aciDescription) {
        this.aciDescription = aciDescription;
    }

    public String getAciCondition() {
        return aciCondition;
    }

    public void setAciCondition(String aciCondition) {
        this.aciCondition = aciCondition;
    }

    public Integer getAciFormulaCode() {
        return aciFormulaCode;
    }

    public void setAciFormulaCode(Integer aciFormulaCode) {
        this.aciFormulaCode = aciFormulaCode;
    }

    public Integer getAciNMeaningsCode() {
        return aciNMeaningsCode;
    }

    public void setAciNMeaningsCode(Integer aciNMeaningsCode) {
        this.aciNMeaningsCode = aciNMeaningsCode;
    }

    public String getAciNEqual() {
        return aciNEqual;
    }

    public void setAciNEqual(String aciNEqual) {
        this.aciNEqual = aciNEqual;
    }

    public Integer getAciQtyMeaningsCode() {
        return aciQtyMeaningsCode;
    }

    public void setAciQtyMeaningsCode(Integer aciQtyMeaningsCode) {
        this.aciQtyMeaningsCode = aciQtyMeaningsCode;
    }

    public String getAciBasePriceEqual() {
        return aciBasePriceEqual;
    }

    public void setAciBasePriceEqual(String aciBasePriceEqual) {
        this.aciBasePriceEqual = aciBasePriceEqual;
    }

    public Integer getnTestConditionTypeId() {
        return nTestConditionTypeId;
    }

    public void setnTestConditionTypeId(Integer nTestConditionTypeId) {
        this.nTestConditionTypeId = nTestConditionTypeId;
    }

    public Integer getQtyTestConditionTypeId() {
        return qtyTestConditionTypeId;
    }

    public void setQtyTestConditionTypeId(Integer qtyTestConditionTypeId) {
        this.qtyTestConditionTypeId = qtyTestConditionTypeId;
    }
}
