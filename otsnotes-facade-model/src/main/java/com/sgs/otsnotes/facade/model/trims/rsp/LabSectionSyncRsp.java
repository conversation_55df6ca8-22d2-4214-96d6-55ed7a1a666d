package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.LabSectionSyncInfo;

import java.util.List;

public class LabSectionSyncRsp extends TrimsSyncBaseRsp {
    /**
     *
     */
    private List<LabSectionSyncInfo> data;

    public List<LabSectionSyncInfo> getData() {
        return data;
    }

    public void setData(List<LabSectionSyncInfo> data) {
        this.data = data;
    }
}
