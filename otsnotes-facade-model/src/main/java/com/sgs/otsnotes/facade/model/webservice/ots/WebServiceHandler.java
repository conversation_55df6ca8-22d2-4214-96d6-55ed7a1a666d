package com.sgs.otsnotes.facade.model.webservice.ots;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sgs.framework.core.util.SpringUtil;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;

public class WebServiceHandler {


    public static GetMergebySectionModel resetMergeBySectionModeWithTimeZone(GetMergebySectionModel mergebySectionModel) {
        if (mergebySectionModel == null || mergebySectionModel.getList() == null || CollectionUtils.isEmpty(Arrays.asList(mergebySectionModel.getList()))) {
            return mergebySectionModel;
        }
        for (GetMergebySectionItemModel itemModel : mergebySectionModel.getList()) {
            if (itemModel.getDatasource() == null || CollectionUtils.isEmpty(Arrays.asList(itemModel.getDatasource().getVariableDataList()))) {
                continue;
            }
            DataItem[] variableDataList = itemModel.getDatasource().getVariableDataList();
            ObjectMapper objectMapper = SpringUtil.getBean(ObjectMapper.class);
            try {
                String dataItems = objectMapper.writeValueAsString(variableDataList);
                itemModel.getDatasource().setVariableDataList(JSON.parseObject(dataItems, new TypeReference<DataItem[]>() {
                }));
            } catch (JsonProcessingException e) {

                throw new RuntimeException(e);
            }
        }
        return mergebySectionModel;

    }
}
