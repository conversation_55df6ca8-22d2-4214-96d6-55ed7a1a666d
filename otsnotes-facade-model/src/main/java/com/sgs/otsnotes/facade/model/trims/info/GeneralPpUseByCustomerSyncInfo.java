package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

/**
 *
 */
public class GeneralPpUseByCustomerSyncInfo extends PrintFriendliness {
    /**
     * {
     *     "httpStatus": 200,
     *     "message": "Success!",
     *     "data": [
     *         {
     *             "ppNumber": ********,
     *             "accountIds": [
     *                 1369897
     *             ],
     *             "id": 3936655,
     *             "productLineId": 2
     *        }
     *    ]
     * }
     */
    /**
     *
     */
    private Integer ppNumber;
    /**
     *
     */
    private Integer productLineId;
    /**
     *
     */
    private List<Integer> accountIds;
    /**
     *
     */
    private Long id;

    /**
     *
     */
    private String status;

    public Integer getPpNumber() {
        return ppNumber;
    }

    public void setPpNumber(Integer ppNumber) {
        this.ppNumber = ppNumber;
    }

    public Integer getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }

    public List<Integer> getAccountIds() {
        return accountIds;
    }

    public void setAccountIds(List<Integer> accountIds) {
        this.accountIds = accountIds;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
