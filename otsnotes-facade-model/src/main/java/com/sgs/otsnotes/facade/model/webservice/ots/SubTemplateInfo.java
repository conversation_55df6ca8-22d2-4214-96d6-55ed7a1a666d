/**
 * SubTemplateInfo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class SubTemplateInfo  implements java.io.Serializable {
    private TemplateTypeEnum templateType;

    private TemplateInfo templateInfomation;

    private int PGT;

    private int PG;

    private String instanceId;

    private String subInstanceId;

    private TestItemTaskStatus taskStatus;

    private DataItem[] extendProperties;

    private CustomerRemarkInfo[] customerRemarks;

    public SubTemplateInfo() {
    }

    public SubTemplateInfo(
           TemplateTypeEnum templateType,
           TemplateInfo templateInfomation,
           int PGT,
           int PG,
           String instanceId,
           String subInstanceId,
           TestItemTaskStatus taskStatus,
           DataItem[] extendProperties,
           CustomerRemarkInfo[] customerRemarks) {
           this.templateType = templateType;
           this.templateInfomation = templateInfomation;
           this.PGT = PGT;
           this.PG = PG;
           this.instanceId = instanceId;
           this.subInstanceId = subInstanceId;
           this.taskStatus = taskStatus;
           this.extendProperties = extendProperties;
           this.customerRemarks = customerRemarks;
    }


    /**
     * Gets the templateType value for this SubTemplateInfo.
     * 
     * @return templateType
     */
    public TemplateTypeEnum getTemplateType() {
        return templateType;
    }


    /**
     * Sets the templateType value for this SubTemplateInfo.
     * 
     * @param templateType
     */
    public void setTemplateType(TemplateTypeEnum templateType) {
        this.templateType = templateType;
    }


    /**
     * Gets the templateInfomation value for this SubTemplateInfo.
     * 
     * @return templateInfomation
     */
    public TemplateInfo getTemplateInfomation() {
        return templateInfomation;
    }


    /**
     * Sets the templateInfomation value for this SubTemplateInfo.
     * 
     * @param templateInfomation
     */
    public void setTemplateInfomation(TemplateInfo templateInfomation) {
        this.templateInfomation = templateInfomation;
    }


    /**
     * Gets the PGT value for this SubTemplateInfo.
     * 
     * @return PGT
     */
    public int getPGT() {
        return PGT;
    }


    /**
     * Sets the PGT value for this SubTemplateInfo.
     * 
     * @param PGT
     */
    public void setPGT(int PGT) {
        this.PGT = PGT;
    }


    /**
     * Gets the PG value for this SubTemplateInfo.
     * 
     * @return PG
     */
    public int getPG() {
        return PG;
    }


    /**
     * Sets the PG value for this SubTemplateInfo.
     * 
     * @param PG
     */
    public void setPG(int PG) {
        this.PG = PG;
    }


    /**
     * Gets the instanceId value for this SubTemplateInfo.
     * 
     * @return instanceId
     */
    public String getInstanceId() {
        return instanceId;
    }


    /**
     * Sets the instanceId value for this SubTemplateInfo.
     * 
     * @param instanceId
     */
    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }


    /**
     * Gets the subInstanceId value for this SubTemplateInfo.
     * 
     * @return subInstanceId
     */
    public String getSubInstanceId() {
        return subInstanceId;
    }


    /**
     * Sets the subInstanceId value for this SubTemplateInfo.
     * 
     * @param subInstanceId
     */
    public void setSubInstanceId(String subInstanceId) {
        this.subInstanceId = subInstanceId;
    }


    /**
     * Gets the taskStatus value for this SubTemplateInfo.
     * 
     * @return taskStatus
     */
    public TestItemTaskStatus getTaskStatus() {
        return taskStatus;
    }


    /**
     * Sets the taskStatus value for this SubTemplateInfo.
     * 
     * @param taskStatus
     */
    public void setTaskStatus(TestItemTaskStatus taskStatus) {
        this.taskStatus = taskStatus;
    }


    /**
     * Gets the extendProperties value for this SubTemplateInfo.
     * 
     * @return extendProperties
     */
    public DataItem[] getExtendProperties() {
        return extendProperties;
    }


    /**
     * Sets the extendProperties value for this SubTemplateInfo.
     * 
     * @param extendProperties
     */
    public void setExtendProperties(DataItem[] extendProperties) {
        this.extendProperties = extendProperties;
    }


    /**
     * Gets the customerRemarks value for this SubTemplateInfo.
     * 
     * @return customerRemarks
     */
    public CustomerRemarkInfo[] getCustomerRemarks() {
        return customerRemarks;
    }


    /**
     * Sets the customerRemarks value for this SubTemplateInfo.
     * 
     * @param customerRemarks
     */
    public void setCustomerRemarks(CustomerRemarkInfo[] customerRemarks) {
        this.customerRemarks = customerRemarks;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof SubTemplateInfo)) {
            return false;
        }
        SubTemplateInfo other = (SubTemplateInfo) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.templateType==null && other.getTemplateType()==null) || 
             (this.templateType!=null &&
              this.templateType.equals(other.getTemplateType()))) &&
            ((this.templateInfomation==null && other.getTemplateInfomation()==null) || 
             (this.templateInfomation!=null &&
              this.templateInfomation.equals(other.getTemplateInfomation()))) &&
            this.PGT == other.getPGT() &&
            this.PG == other.getPG() &&
            ((this.instanceId==null && other.getInstanceId()==null) || 
             (this.instanceId!=null &&
              this.instanceId.equals(other.getInstanceId()))) &&
            ((this.subInstanceId==null && other.getSubInstanceId()==null) || 
             (this.subInstanceId!=null &&
              this.subInstanceId.equals(other.getSubInstanceId()))) &&
            ((this.taskStatus==null && other.getTaskStatus()==null) || 
             (this.taskStatus!=null &&
              this.taskStatus.equals(other.getTaskStatus()))) &&
            ((this.extendProperties==null && other.getExtendProperties()==null) || 
             (this.extendProperties!=null &&
              java.util.Arrays.equals(this.extendProperties, other.getExtendProperties()))) &&
            ((this.customerRemarks==null && other.getCustomerRemarks()==null) || 
             (this.customerRemarks!=null &&
              java.util.Arrays.equals(this.customerRemarks, other.getCustomerRemarks())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getTemplateType() != null) {
            _hashCode += getTemplateType().hashCode();
        }
        if (getTemplateInfomation() != null) {
            _hashCode += getTemplateInfomation().hashCode();
        }
        _hashCode += getPGT();
        _hashCode += getPG();
        if (getInstanceId() != null) {
            _hashCode += getInstanceId().hashCode();
        }
        if (getSubInstanceId() != null) {
            _hashCode += getSubInstanceId().hashCode();
        }
        if (getTaskStatus() != null) {
            _hashCode += getTaskStatus().hashCode();
        }
        if (getExtendProperties() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getExtendProperties());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getExtendProperties(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getCustomerRemarks() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getCustomerRemarks());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getCustomerRemarks(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(SubTemplateInfo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubTemplateInfo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateInfomation");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInfomation"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PGT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PGT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("instanceId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "InstanceId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("subInstanceId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubInstanceId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("taskStatus");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TaskStatus"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TestItemTaskStatus"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("extendProperties");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ExtendProperties"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("customerRemarks");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CustomerRemarks"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "CustomerRemarkInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CustomerRemarkInfo"));
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
