package com.sgs.otsnotes.facade.model.enums;

/**
 * @ClassName NoStyleTemplateStyleEnums
 * @Description 无样式配置类型枚举
 * <AUTHOR>
 * @Date 11/10/2021
 */
public enum NoStyleTemplateStyleEnums {
    General(1),
    Combined(2);
    private Integer templateStyle;

    NoStyleTemplateStyleEnums(Integer templateStyle) {
        this.templateStyle = templateStyle;
    }

    public Integer getTemplateStyle() {
        return templateStyle;
    }

    public static boolean check(Integer templateStyle, NoStyleTemplateStyleEnums... enums) {
        if (templateStyle == null) {
            return false;
        }
        for (NoStyleTemplateStyleEnums anEnum : enums) {
            if (anEnum.templateStyle.compareTo(templateStyle) == 0) {
                return true;
            }
        }
        return false;
    }

}
