package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.rsp.dataentry.DataEntryStandardRsp;

import java.util.Comparator;

public class DataEntryStandardComparator implements Comparator<DataEntryStandardRsp> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public DataEntryStandardComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(DataEntryStandardRsp o1, DataEntryStandardRsp o2) {
        String standardName1 = o1.getStandardName();
        if (standardName1 == null){
            standardName1 = "";
        }
        String standardName2 = o2.getStandardName();
        if (standardName2 == null){
            standardName2 = "";
        }
        int index = standardName1.compareTo(standardName2);
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
