package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class CustomTestCategoryLangSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer languageId;
    /**
     *
     */
    private String customTestCategoryName;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getCustomTestCategoryName() {
        return customTestCategoryName;
    }

    public void setCustomTestCategoryName(String customTestCategoryName) {
        this.customTestCategoryName = customTestCategoryName;
    }
}
