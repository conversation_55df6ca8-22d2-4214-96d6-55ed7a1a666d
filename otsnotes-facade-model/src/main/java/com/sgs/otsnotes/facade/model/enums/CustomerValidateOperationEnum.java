package com.sgs.otsnotes.facade.model.enums;

import com.sgs.extsystem.facade.model.enums.CustomizedValidationEnum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

public enum CustomerValidateOperationEnum {
    ConfirmMatrix(1,  CustomizedValidationEnum.ConfirmMatrix, "Analyte", "根据校验接口中返回的数据 对入参的数据进行筛选，将符合条件的数据入库"),
    UpdateStandard(2,  CustomizedValidationEnum.ConfirmMatrix, "Analyte", "根据校验接口中返回的数据 对入参的数据进行筛选，将符合条件的数据入库"),
    SaveProductAttribute(3, CustomizedValidationEnum.ConfirmMatrix, "Limit","校验非终止，不影响数据操作,报错信息以表格形式提示，前端点击Close 后，页面刷新"),
    Approve(4, CustomizedValidationEnum.Approve, "Approve","接口直接报错，前端表格形式提示客户"),
    StarLimsDataCallback(5, CustomizedValidationEnum.ConfirmMatrix, "StarLimsDataCallback","接口直接报错，前端表格形式提示客户"),
    FastDataCallback(6, CustomizedValidationEnum.ConfirmMatrix, "FastDataCallback","接口直接报错，前端表格形式提示客户");
    private int type;
    private String checkGroupCode;
    private CustomizedValidationEnum validationEnum;
    private String desc;

    CustomerValidateOperationEnum(int type, CustomizedValidationEnum validationEnum, String checkGroupCode, String desc) {
        this.type = type;
        this.validationEnum = validationEnum;
        this.checkGroupCode = checkGroupCode;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public CustomizedValidationEnum getValidationEnum() {
        return validationEnum;
    }

    public String getCheckGroupCode() {
        return checkGroupCode;
    }

    public int getType() {
        return type;
    }

    static Map<Integer, CustomerValidateOperationEnum> typeMaps = new HashMap();
    static {
        for (CustomerValidateOperationEnum analyteType : CustomerValidateOperationEnum.values()) {
            typeMaps.put(analyteType.getType(), analyteType);
        }
    }

    public static CustomerValidateOperationEnum findType(Integer type) {
        if (type == null || !typeMaps.containsKey(type)) {
            return null;
        }
        return typeMaps.get(type);
    }

    /**
     *
     * @param type
     * @param analyteType
     * @return
     */
    public static boolean check(Integer type, CustomerValidateOperationEnum analyteType) {
        if (type == null || !typeMaps.containsKey(type)){
            return false;
        }
        return typeMaps.get(type) == analyteType;
    }

    public static boolean check(Integer analyteType) {
        if (analyteType == null){
            return false;
        }
        return typeMaps.containsKey(analyteType);
    }

}
