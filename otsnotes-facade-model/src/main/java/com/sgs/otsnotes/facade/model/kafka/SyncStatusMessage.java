package com.sgs.otsnotes.facade.model.kafka;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/6 18:03
 */
public class SyncStatusMessage implements Serializable {
    private static final long serialVersionUID = 932346385704794908L;

    private ReportApproveMessage reportApproveMessage;

    private SyncReportStatusMessage syncReportStatusMessage;

    private String sampleId;

    private List<String> combinedOrderNos;


    public ReportApproveMessage getReportApproveMessage() {
        return reportApproveMessage;
    }

    public void setReportApproveMessage(ReportApproveMessage reportApproveMessage) {
        this.reportApproveMessage = reportApproveMessage;
    }

    public SyncReportStatusMessage getSyncReportStatusMessage() {
        return syncReportStatusMessage;
    }

    public void setSyncReportStatusMessage(SyncReportStatusMessage syncReportStatusMessage) {
        this.syncReportStatusMessage = syncReportStatusMessage;
    }

    public String getSampleId() {
        return sampleId;
    }

    public void setSampleId(String sampleId) {
        this.sampleId = sampleId;
    }

    public List<String> getCombinedOrderNos() {
        return combinedOrderNos;
    }

    public void setCombinedOrderNos(List<String> combinedOrderNos) {
        this.combinedOrderNos = combinedOrderNos;
    }
}
