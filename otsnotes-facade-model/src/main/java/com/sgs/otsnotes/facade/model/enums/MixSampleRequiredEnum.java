package com.sgs.otsnotes.facade.model.enums;
/**
 * 混测要求枚举
 */
public enum MixSampleRequiredEnum {
    MIX_SAMPLE(1, "所有混测", "all mix sample"),
    MIX_SAMPLE_ONE(2, "三合一混测", "mix sample (3 in 1)"),
    NOT_CHECK(3, "不校验", "not check");

    private Integer code;
    private String  mixSampleRequiredValue;
    private String desc;

    MixSampleRequiredEnum(Integer code, String desc, String mixSampleRequiredValue) {
        this.code = code;
        this.desc = desc;
        this.mixSampleRequiredValue = mixSampleRequiredValue;
    }

    public static MixSampleRequiredEnum findByCode(Integer mixSampleRequired) {
        for (MixSampleRequiredEnum mixSampleRequiredEnum : MixSampleRequiredEnum.values()) {
            if (mixSampleRequiredEnum.getCode().equals(mixSampleRequired)) {
                return mixSampleRequiredEnum;
            }
        }
        return null;
    }
    public static String getDescByCode(Integer mixSampleRequired) {
        for (MixSampleRequiredEnum mixSampleRequiredEnum : MixSampleRequiredEnum.values()) {
            if (mixSampleRequiredEnum.getCode().equals(mixSampleRequired)) {
                return mixSampleRequiredEnum.getDesc();
            }
        }
        return null;
    }
    public static String getMixSampleRequiredValueByCode(Integer mixSampleRequired) {
        for (MixSampleRequiredEnum mixSampleRequiredEnum : MixSampleRequiredEnum.values()) {
            if (mixSampleRequiredEnum.getCode().equals(mixSampleRequired)) {
                return mixSampleRequiredEnum.getMixSampleRequiredValue();
            }
        }
        return null;
    }
    /**
     * 根据mixSampleRequiredValue获取对应的枚举
     * @param mixSampleRequiredValue
     * @return
     */
    public static MixSampleRequiredEnum findByValue(String mixSampleRequiredValue) {
        for (MixSampleRequiredEnum mixSampleRequiredEnum : MixSampleRequiredEnum.values()) {
            if (mixSampleRequiredEnum.getMixSampleRequiredValue().equals(mixSampleRequiredValue)) {
                return mixSampleRequiredEnum;
            }
        }
        return null;
    }

    /**
     * 是否需要混测
     * @param mixSampleRequired
     * @return
     */
    public static boolean isMixSampleConfig(Integer mixSampleRequired) {
        return findByCode(mixSampleRequired) == MIX_SAMPLE || findByCode(mixSampleRequired) == MIX_SAMPLE_ONE;    }

    /**
     * 是否 必须混测
     * @param mixSampleRequired
     * @return
     */
    public static boolean isMixSampleRequired(Integer mixSampleRequired) {
        return findByCode(mixSampleRequired) == MIX_SAMPLE_ONE;
    }
    /**
     * 判断mixSampleRequiredValue是否符合规则
     * 如果是MIX_SAMPLE_ONE类型，但是mixSampleRealValue小于3，则认为不符合规则
     * @param mixSampleRequiredEnum
     * @param mixSampleRealValue
     * @return
     */
    public static boolean isMixSampleRequiredValue(MixSampleRequiredEnum mixSampleRequiredEnum,Integer mixSampleRealValue) {
        //根据枚举获取对应的混测要求值，然后判断传入的mixSampleRealValue实际值是否一致
        if(mixSampleRequiredEnum == MIX_SAMPLE_ONE){
            if(mixSampleRealValue < 3){
                return false;
            }
        }
        //其他类型数值是0，则认为符合规则
        return true;
    }

    
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }   

    public String getMixSampleRequiredValue() {
        return mixSampleRequiredValue;
    }
    
}
