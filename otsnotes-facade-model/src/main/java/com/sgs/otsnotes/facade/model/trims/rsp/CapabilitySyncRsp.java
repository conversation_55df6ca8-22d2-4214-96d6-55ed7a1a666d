package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;
import com.sgs.otsnotes.facade.model.trims.rsp.accreditation.AccreditationSyncInfo;
import com.sgs.otsnotes.facade.model.trims.rsp.capability.CapabilitySyncInfo;
import lombok.Data;

import java.util.List;

@Data
public class CapabilitySyncRsp extends PrintFriendliness {

    private List<CapabilitySyncInfo> data;


}
