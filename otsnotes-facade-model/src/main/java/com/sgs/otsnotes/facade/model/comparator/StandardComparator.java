package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.rsp.testLine.StandardRsp;

import java.util.Comparator;

public class StandardComparator implements Comparator<StandardRsp> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public StandardComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(StandardRsp o1, StandardRsp o2) {
        Integer standardId1 = o1.getStandardId();
        if (standardId1 == null){
            standardId1 = 0;
        }
        Integer standardId2 = o2.getStandardId();
        if (standardId2 == null){
            standardId2 = 0;
        }
        int index = Integer.compare(standardId1, standardId2);
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }

}
