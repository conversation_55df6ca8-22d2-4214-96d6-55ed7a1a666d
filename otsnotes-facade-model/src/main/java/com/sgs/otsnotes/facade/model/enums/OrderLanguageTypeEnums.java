package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

/**
 * @ClassName OrderLanguageTypeEnums
 * @Description TODO
 * <AUTHOR>
 * @Date 7/29/2020
 * '0：PP
 * 1：Section
 * 2：TestLine
 * 3：Citation
 * 4：Analyte
 * 5：ConditionType
 * 6：Condition
 * 7：Position
 * 8：UsageType
 * 9：PPTestline',
 * 10：AnalyteUnit',
 */
@Dict
public enum OrderLanguageTypeEnums {
    PP(0),
    Section(1),
    TestLine(2),
    Citation(3),
    Analyte(4),
    ConditionType(5),
    Condition(6),
    Position(7),
    UsageType(8),
    PPTestline(9),
    AnalyteUnit(10);
    @DictCodeField
    private Integer type;

    OrderLanguageTypeEnums(Integer type){
        this.type = type;
    }

    public Integer getType() {
        return type;
    }


    public static final Map<Integer, OrderLanguageTypeEnums> maps;
    static {
        Map<Integer, OrderLanguageTypeEnums> tmp = new HashMap<>();
        for (OrderLanguageTypeEnums enu : OrderLanguageTypeEnums.values()) {
            tmp.put(enu.getType(), enu);
        }
        maps = java.util.Collections.unmodifiableMap(tmp);
    }

    public static OrderLanguageTypeEnums findType(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    /**
     *
     * @param status
     * @param type
     * @return
     */
    public static boolean check(Integer status, OrderLanguageTypeEnums ... type) {
        if (status == null || !maps.containsKey(status)){
            return false;
        }
        for (OrderLanguageTypeEnums enums : type) {
            if(status.equals(enums.getType())){
                return true;
            }
        }
        return false;
    }
}
