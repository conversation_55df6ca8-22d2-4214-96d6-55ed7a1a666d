package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

public enum TrimsTestLineStatus {
    None(0, "None"),
    Active(1, "Active"),
    PhaseOut(2, "PhaseOut"),
    Inactive(3, "Inactive");
    @DictCodeField
    private int status;
    @DictLabelField
    private String code;

    TrimsTestLineStatus(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    static Map<Integer, TrimsTestLineStatus> maps = new HashMap<>();
    static Map<String, TrimsTestLineStatus> codeMaps = new HashMap<>();

    static {
        for (TrimsTestLineStatus status : TrimsTestLineStatus.values()) {
            maps.put(status.getStatus(), status);
            codeMaps.put(status.getCode().toLowerCase(), status);
        }
    };

    public static TrimsTestLineStatus getStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    public static TrimsTestLineStatus getCode(String code) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase())) {
            return null;
        }
        return codeMaps.get(code.toLowerCase());
    }

    public static boolean checkStatus(TrimsTestLineStatus status, TrimsTestLineStatus... testLineStatus) {
        if (status == null || testLineStatus == null || testLineStatus.length <= 0){
            return false;
        }
        return check(status.getStatus(), testLineStatus);
    }

    /**
     *
     * @param status
     * @param testLineStatus
     * @return
     */
    public static boolean check(Integer status, TrimsTestLineStatus... testLineStatus) {
        if (status == null || !maps.containsKey(status.intValue()) || testLineStatus == null || testLineStatus.length <= 0){
            return false;
        }
        for (TrimsTestLineStatus tlStatus: testLineStatus){
            if (status.intValue() == tlStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param code
     * @param tlStatus
     * @return
     */
    public static boolean check(String code, TrimsTestLineStatus... tlStatus) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase()) || tlStatus == null || tlStatus.length <= 0){
            return false;
        }
        int status = codeMaps.get(code.toLowerCase()).getStatus();
        for (TrimsTestLineStatus tls: tlStatus){
            if (tls.getStatus() == status){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param testLineStatus
     * @return
     */
    public boolean check(TrimsTestLineStatus... testLineStatus){
        if (testLineStatus == null || testLineStatus.length <= 0){
            return false;
        }
        for (TrimsTestLineStatus tlStatus: testLineStatus){
            if (this.getStatus() == tlStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

}
