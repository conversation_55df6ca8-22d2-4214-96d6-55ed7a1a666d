package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.info.LabSectionSortInfo;

import java.util.Comparator;

public class LabSectionComparator implements Comparator<LabSectionSortInfo> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public LabSectionComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(LabSectionSortInfo o1, LabSectionSortInfo o2) {
        // to SectionSequence
        int index = 0;
        if (o1.getSectionSequence() != null && o2.getSectionSequence() != null) {
            index = Integer.compare(o1.getSectionSequence(), o2.getSectionSequence());
            if (index < 0) {
                return isAsc ? -1 : 1;
            }
            if (index > 0) {
                return isAsc ? 1 : -1;
            }
        }
        // to labSectionCode
        if (o1.getLabSectionCode() != null && o2.getLabSectionCode() != null) {
            index = o1.getLabSectionCode().compareTo(o2.getLabSectionCode());
            if (index < 0) {
                return isAsc ? -1 : 1;
            }
            if (index > 0) {
                return isAsc ? 1 : -1;
            }
        }

        // to subContractLabName
        if (o1.getSubContractLabName() != null && o2.getSubContractLabName() != null) {
            index = o1.getSubContractLabName().compareTo(o2.getSubContractLabName());
            if (index < 0) {
                return isAsc ? -1 : 1;
            }
            if (index > 0) {
                return isAsc ? 1 : -1;
            }
        }

        // to evaluationAlias
        if (o1.getEvaluationAlias() != null && o2.getEvaluationAlias() != null) {
            index = o1.getEvaluationAlias().compareTo(o2.getEvaluationAlias());
            if (index < 0) {
                return isAsc ? -1 : 1;
            }
            if (index > 0) {
                return isAsc ? 1 : -1;
            }
        }
        return index;
    }
}
