package com.sgs.otsnotes.facade.model.rsp.starlims;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class ReceiveStarLimsReportDocRsp extends PrintFriendliness {
    /**
     *
     */
    private String subcontractNo;

    /**
     *
     */
    private String externalNo;

    /**
     *
     */
    private String subReportNo;

    /**
     *
     */
    private String reportNo;

    /**
     *
     */
    private String reportId;

    public String getSubcontractNo() {
        return subcontractNo;
    }

    public void setSubcontractNo(String subcontractNo) {
        this.subcontractNo = subcontractNo;
    }

    public String getExternalNo() {
        return externalNo;
    }

    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo;
    }

    public String getSubReportNo() {
        return subReportNo;
    }

    public void setSubReportNo(String subReportNo) {
        this.subReportNo = subReportNo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }
}
