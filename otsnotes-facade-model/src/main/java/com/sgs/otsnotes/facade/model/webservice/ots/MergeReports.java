/**
 * MergeReports.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class MergeReports  implements java.io.Serializable {
    private SubReportInfo[] reports;

    private TemplateInstanceInfo targetReport;

    public MergeReports() {
    }

    public MergeReports(
           SubReportInfo[] reports,
           TemplateInstanceInfo targetReport) {
           this.reports = reports;
           this.targetReport = targetReport;
    }


    /**
     * Gets the reports value for this MergeReports.
     * 
     * @return reports
     */
    public SubReportInfo[] getReports() {
        return reports;
    }


    /**
     * Sets the reports value for this MergeReports.
     * 
     * @param reports
     */
    public void setReports(SubReportInfo[] reports) {
        this.reports = reports;
    }


    /**
     * Gets the targetReport value for this MergeReports.
     * 
     * @return targetReport
     */
    public TemplateInstanceInfo getTargetReport() {
        return targetReport;
    }


    /**
     * Sets the targetReport value for this MergeReports.
     * 
     * @param targetReport
     */
    public void setTargetReport(TemplateInstanceInfo targetReport) {
        this.targetReport = targetReport;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof MergeReports)) {
            return false;
        }
        MergeReports other = (MergeReports) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.reports==null && other.getReports()==null) || 
             (this.reports!=null &&
              java.util.Arrays.equals(this.reports, other.getReports()))) &&
            ((this.targetReport==null && other.getTargetReport()==null) || 
             (this.targetReport!=null &&
              this.targetReport.equals(other.getTargetReport())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getReports() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getReports());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getReports(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getTargetReport() != null) {
            _hashCode += getTargetReport().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(MergeReports.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">MergeReports"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("reports");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "reports"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubReportInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubReportInfo"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("targetReport");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "targetReport"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateInstanceInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
