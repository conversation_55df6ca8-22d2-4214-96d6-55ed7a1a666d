package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.LimitGroupSyncInfo;

import java.util.List;

public class LimitGroupSyncRsp extends TrimsSyncBaseRsp {
    /**
     *
     */
    private List<LimitGroupSyncInfo> data;

    public List<LimitGroupSyncInfo> getData() {
        return data;
    }

    public void setData(List<LimitGroupSyncInfo> data) {
        this.data = data;
    }
}
