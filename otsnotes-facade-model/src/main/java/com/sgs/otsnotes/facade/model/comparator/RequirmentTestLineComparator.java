package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.info.limitgroup.RequirmentTestLineInfo;
import com.sgs.otsnotes.facade.model.info.limitgroup.TestLineMatrixInfo;

import java.util.Comparator;
import java.util.List;

public class RequirmentTestLineComparator implements Comparator<RequirmentTestLineInfo> {
    /**
     * 是否为升序
     */
    private boolean isAsc;

    public RequirmentTestLineComparator() {
        this.isAsc = false;
    }

    public RequirmentTestLineComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     * @param testLine1
     * @param testLine2
     * @return
     */
    @Override
    public int compare(RequirmentTestLineInfo testLine1, RequirmentTestLineInfo testLine2) {
        String labSectionName1 = testLine1.getLabSectionName();
        if (labSectionName1 == null){
            labSectionName1 = "";
        }
        String labSectionName2 = testLine2.getLabSectionName();
        if (labSectionName2 == null){
            labSectionName2 = "";
        }
        int index = labSectionName1.compareToIgnoreCase(labSectionName2);

        List<TestLineMatrixInfo> matrixs = testLine1.getMatrixs();
        if (matrixs != null){
            matrixs.sort(new RequirmentMatrixComparator(true));
        }
        matrixs = testLine2.getMatrixs();
        if (matrixs != null){
            matrixs.sort(new RequirmentMatrixComparator(true));
        }
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
