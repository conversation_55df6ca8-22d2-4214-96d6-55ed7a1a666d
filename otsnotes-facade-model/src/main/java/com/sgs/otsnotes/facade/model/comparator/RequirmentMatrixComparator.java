package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.info.limitgroup.TestLineMatrixInfo;

import java.util.Comparator;

public class RequirmentMatrixComparator implements Comparator<TestLineMatrixInfo> {
    /**
     * 是否为升序
     */
    private boolean isAsc;

    public RequirmentMatrixComparator() {
        this.isAsc = false;
    }

    public RequirmentMatrixComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     * @param matrix1
     * @param matrix2
     * @return
     */
    @Override
    public int compare(TestLineMatrixInfo matrix1, TestLineMatrixInfo matrix2) {
        Integer sampleSeq1 = matrix1.getSampleSeq();
        if (sampleSeq1 == null){
            sampleSeq1 = 0;
        }
        Integer sampleSeq2 = matrix2.getSampleSeq();
        if (sampleSeq2 == null){
            sampleSeq2 = 0;
        }
        int index = sampleSeq1.compareTo(sampleSeq2);
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
