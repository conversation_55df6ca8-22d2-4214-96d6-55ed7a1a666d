package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class LabSectionSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer productLineId;
    /**
     *
     */
    private Integer laboratoryId;
    /**
     *
     */
    private Integer laboratorySectionId;
    /**
     *
     */
    private String laboratorySectionCode;
    /**
     *
     */
    private String laboratorySectionName;
    /**
     *
     */
    private String laboratorySectionOtherCode;
    /**
     *
     */
    private Integer laboratorySectionSequence;
    /**
     *
     */
    private String status;

    public Integer getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }

    public Integer getLaboratoryId() {
        return laboratoryId;
    }

    public void setLaboratoryId(Integer laboratoryId) {
        this.laboratoryId = laboratoryId;
    }

    public Integer getLaboratorySectionId() {
        return laboratorySectionId;
    }

    public void setLaboratorySectionId(Integer laboratorySectionId) {
        this.laboratorySectionId = laboratorySectionId;
    }

    public String getLaboratorySectionCode() {
        return laboratorySectionCode;
    }

    public void setLaboratorySectionCode(String laboratorySectionCode) {
        this.laboratorySectionCode = laboratorySectionCode;
    }

    public String getLaboratorySectionName() {
        return laboratorySectionName;
    }

    public void setLaboratorySectionName(String laboratorySectionName) {
        this.laboratorySectionName = laboratorySectionName;
    }

    public String getLaboratorySectionOtherCode() {
        return laboratorySectionOtherCode;
    }

    public void setLaboratorySectionOtherCode(String laboratorySectionOtherCode) {
        this.laboratorySectionOtherCode = laboratorySectionOtherCode;
    }

    public Integer getLaboratorySectionSequence() {
        return laboratorySectionSequence;
    }

    public void setLaboratorySectionSequence(Integer laboratorySectionSequence) {
        this.laboratorySectionSequence = laboratorySectionSequence;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
