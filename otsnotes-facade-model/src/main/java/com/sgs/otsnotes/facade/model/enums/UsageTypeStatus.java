package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

public enum UsageTypeStatus {
    Inactive(0,0, "Inactive"),
    Active(1,1, "Active");

    @DictCodeField
    private int status;
    private int trimsStatus;
    @DictLabelField
    private String code;

    UsageTypeStatus(int status, int trimsStatus, String code) {
        this.status = status;
        this.trimsStatus = trimsStatus;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    public int getTrimsStatus() {
        return trimsStatus;
    }

    static Map<Integer, UsageTypeStatus> statusMaps = new HashMap<>();
    static Map<Integer, UsageTypeStatus> trimsStatusMaps = new HashMap<>();

    static {
        for (UsageTypeStatus status : UsageTypeStatus.values()) {
            statusMaps.put(status.getStatus(), status);
            trimsStatusMaps.put(status.getTrimsStatus(), status);
        }
    };

    public static UsageTypeStatus getStatus(Integer status) {
        if (status == null || !statusMaps.containsKey(status)) {
            return null;
        }
        return statusMaps.get(status);
    }

    public static UsageTypeStatus getSyncStatus(Integer syncStatus) {
        if (syncStatus == null || !trimsStatusMaps.containsKey(syncStatus)) {
            return null;
        }
        return trimsStatusMaps.get(syncStatus);
    }
}
