/**
 * GetMergebySectionModel.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GetMergebySectionModel  implements java.io.Serializable {
    private GetMergebySectionItemModel[] list;

    private String finalFullPath;

    private int uiViewType;

    private int breakType;

    private CallSetting callbackSetting;

    public GetMergebySectionModel() {
    }

    public GetMergebySectionModel(
           GetMergebySectionItemModel[] list,
           String finalFullPath,
           int uiViewType,
           int breakType,
           CallSetting callbackSetting) {
           this.list = list;
           this.finalFullPath = finalFullPath;
           this.uiViewType = uiViewType;
           this.breakType = breakType;
           this.callbackSetting = callbackSetting;
    }


    /**
     * Gets the list value for this GetMergebySectionModel.
     * 
     * @return list
     */
    public GetMergebySectionItemModel[] getList() {
        return list;
    }


    /**
     * Sets the list value for this GetMergebySectionModel.
     * 
     * @param list
     */
    public void setList(GetMergebySectionItemModel[] list) {
        this.list = list;
    }


    /**
     * Gets the finalFullPath value for this GetMergebySectionModel.
     * 
     * @return finalFullPath
     */
    public String getFinalFullPath() {
        return finalFullPath;
    }


    /**
     * Sets the finalFullPath value for this GetMergebySectionModel.
     * 
     * @param finalFullPath
     */
    public void setFinalFullPath(String finalFullPath) {
        this.finalFullPath = finalFullPath;
    }


    /**
     * Gets the uiViewType value for this GetMergebySectionModel.
     * 
     * @return uiViewType
     */
    public int getUiViewType() {
        return uiViewType;
    }


    /**
     * Sets the uiViewType value for this GetMergebySectionModel.
     * 
     * @param uiViewType
     */
    public void setUiViewType(int uiViewType) {
        this.uiViewType = uiViewType;
    }


    /**
     * Gets the breakType value for this GetMergebySectionModel.
     * 
     * @return breakType
     */
    public int getBreakType() {
        return breakType;
    }


    /**
     * Sets the breakType value for this GetMergebySectionModel.
     * 
     * @param breakType
     */
    public void setBreakType(int breakType) {
        this.breakType = breakType;
    }

    public CallSetting getCallbackSetting() {
        return callbackSetting;
    }

    public void setCallbackSetting(CallSetting callbackSetting) {
        this.callbackSetting = callbackSetting;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GetMergebySectionModel)) {
            return false;
        }
        GetMergebySectionModel other = (GetMergebySectionModel) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.list==null && other.getList()==null) || 
             (this.list!=null &&
              java.util.Arrays.equals(this.list, other.getList()))) &&
            ((this.finalFullPath==null && other.getFinalFullPath()==null) || 
             (this.finalFullPath!=null &&
              this.finalFullPath.equals(other.getFinalFullPath()))) &&
            this.uiViewType == other.getUiViewType() &&
            this.breakType == other.getBreakType();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getList() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getList());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getList(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getFinalFullPath() != null) {
            _hashCode += getFinalFullPath().hashCode();
        }
        _hashCode += getUiViewType();
        _hashCode += getBreakType();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GetMergebySectionModel.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "GetMergebySectionModel"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("list");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "list"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "GetMergebySectionItemModel"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "GetMergebySectionItemModel"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("finalFullPath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "finalFullPath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("uiViewType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "uiViewType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("breakType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "BreakType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);


        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("callbackSetting");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CallbackSetting"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "CallSetting"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);

    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
