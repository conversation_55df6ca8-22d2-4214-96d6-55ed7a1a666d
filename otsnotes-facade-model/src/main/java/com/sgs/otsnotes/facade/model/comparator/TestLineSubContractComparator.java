package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.common.NumberUtils;
import com.sgs.otsnotes.facade.model.info.testline.TestLinePpInfo;

import java.util.Comparator;

public class TestLineSubContractComparator implements Comparator<TestLinePpInfo> {

    private boolean isAsc;

    public TestLineSubContractComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }
    @Override
    public int compare(TestLinePpInfo o1, TestLinePpInfo o2) {
        if (o1.getSubContractLabCode() == null){
            o1.setSubContractLabCode("");
        }
        if (o2.getSubContractLabCode() == null){
            o2.setSubContractLabCode("");
        }
        if(o1.getSlimTestLineSeq() == null ){
            o1.setSlimTestLineSeq(Long.MIN_VALUE);
        }
        if(o2.getSlimTestLineSeq() == null ){
            o2.setSlimTestLineSeq(Long.MIN_VALUE);
        }
        if (o1.getIfNotPP() == null){
            o1.setIfNotPP(0);
        }
        if (o2.getIfNotPP() == null){
            o2.setIfNotPP(0);
        }
        if (o1.getPpNo() == null){
            o1.setPpNo(0);
        }
        if (o2.getPpNo() == null){
            o2.setPpNo(0);
        }
        if (o1.getPpOrderingSeq() == null){
            o1.setPpOrderingSeq(Integer.MAX_VALUE);
        }
        if (o2.getPpOrderingSeq() == null){
            o2.setPpOrderingSeq(Integer.MAX_VALUE);
        }
        int index = 0;
        index = o1.getSubContractLabCode().compareTo(o2.getSubContractLabCode());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        index = o1.getSlimTestLineSeq().compareTo(o2.getSlimTestLineSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        index = o1.getIfNotPP().compareTo(o2.getIfNotPP());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        index = o1.getPpOrderingSeq().compareTo(o2.getPpOrderingSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        index = o1.getPpNo().compareTo(o2.getPpNo());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        index = o1.getTestLineId().compareTo(o2.getTestLineId());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        return 0;
    }
}
