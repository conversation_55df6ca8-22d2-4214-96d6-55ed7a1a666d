/**
 * ImageInfo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ImageInfo  implements java.io.Serializable {
    private TemplateImageType imageType;

    private int certificateImageType;

    private String imageFilePath;

    private String description;

    private String[] imageDisplayType;

    public ImageInfo() {
    }

    public ImageInfo(
           TemplateImageType imageType,
           int certificateImageType,
           String imageFilePath,
           String description,
           String[] imageDisplayType) {
           this.imageType = imageType;
           this.certificateImageType = certificateImageType;
           this.imageFilePath = imageFilePath;
           this.description = description;
           this.imageDisplayType = imageDisplayType;
    }


    /**
     * Gets the imageType value for this ImageInfo.
     * 
     * @return imageType
     */
    public TemplateImageType getImageType() {
        return imageType;
    }


    /**
     * Sets the imageType value for this ImageInfo.
     * 
     * @param imageType
     */
    public void setImageType(TemplateImageType imageType) {
        this.imageType = imageType;
    }


    /**
     * Gets the certificateImageType value for this ImageInfo.
     * 
     * @return certificateImageType
     */
    public int getCertificateImageType() {
        return certificateImageType;
    }


    /**
     * Sets the certificateImageType value for this ImageInfo.
     * 
     * @param certificateImageType
     */
    public void setCertificateImageType(int certificateImageType) {
        this.certificateImageType = certificateImageType;
    }


    /**
     * Gets the imageFilePath value for this ImageInfo.
     * 
     * @return imageFilePath
     */
    public String getImageFilePath() {
        return imageFilePath;
    }


    /**
     * Sets the imageFilePath value for this ImageInfo.
     * 
     * @param imageFilePath
     */
    public void setImageFilePath(String imageFilePath) {
        this.imageFilePath = imageFilePath;
    }


    /**
     * Gets the description value for this ImageInfo.
     * 
     * @return description
     */
    public String getDescription() {
        return description;
    }


    /**
     * Sets the description value for this ImageInfo.
     * 
     * @param description
     */
    public void setDescription(String description) {
        this.description = description;
    }


    /**
     * Gets the imageDisplayType value for this ImageInfo.
     * 
     * @return imageDisplayType
     */
    public String[] getImageDisplayType() {
        return imageDisplayType;
    }


    /**
     * Sets the imageDisplayType value for this ImageInfo.
     * 
     * @param imageDisplayType
     */
    public void setImageDisplayType(String[] imageDisplayType) {
        this.imageDisplayType = imageDisplayType;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ImageInfo)) {
            return false;
        }
        ImageInfo other = (ImageInfo) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.imageType==null && other.getImageType()==null) || 
             (this.imageType!=null &&
              this.imageType.equals(other.getImageType()))) &&
            this.certificateImageType == other.getCertificateImageType() &&
            ((this.imageFilePath==null && other.getImageFilePath()==null) || 
             (this.imageFilePath!=null &&
              this.imageFilePath.equals(other.getImageFilePath()))) &&
            ((this.description==null && other.getDescription()==null) || 
             (this.description!=null &&
              this.description.equals(other.getDescription()))) &&
            ((this.imageDisplayType==null && other.getImageDisplayType()==null) || 
             (this.imageDisplayType!=null &&
              java.util.Arrays.equals(this.imageDisplayType, other.getImageDisplayType())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getImageType() != null) {
            _hashCode += getImageType().hashCode();
        }
        _hashCode += getCertificateImageType();
        if (getImageFilePath() != null) {
            _hashCode += getImageFilePath().hashCode();
        }
        if (getDescription() != null) {
            _hashCode += getDescription().hashCode();
        }
        if (getImageDisplayType() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getImageDisplayType());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getImageDisplayType(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ImageInfo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ImageInfo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("imageType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ImageType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateImageType"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("certificateImageType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CertificateImageType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("imageFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ImageFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("description");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Description"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("imageDisplayType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ImageDisplayType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ImageDisplayOption"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
