package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.GeneralPpUseByCustomerSyncInfo;
import com.sgs.otsnotes.facade.model.trims.info.UnitSyncInfo;

import java.util.List;

public class GeneralPPUsedByCustomerSyncRsp extends TrimsSyncBaseRsp {
    /**
     *
     */
    private List<GeneralPpUseByCustomerSyncInfo> data;

    public List<GeneralPpUseByCustomerSyncInfo> getData() {
        return data;
    }

    public void setData(List<GeneralPpUseByCustomerSyncInfo> data) {
        this.data = data;
    }
}
