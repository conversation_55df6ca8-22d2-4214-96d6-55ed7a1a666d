package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.Set;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/4 18:32
 */
public class TestLineAnalyteRelSyncInfo extends PrintFriendliness {
    private Integer testAnalyteId;
    private Integer sequence;
    private Integer selectionType;
    private Integer unitId;
    /**
     * 0：代表不允许修改、1：代表允许修改
     */
    private Integer analyteResultModify;
    private String descriptionAlias;

    private Set<Integer> analyteResultOptionIds;

    private String resultEntry;

    public String getResultEntry() {
        return resultEntry;
    }

    public void setResultEntry(String resultEntry) {
        this.resultEntry = resultEntry;
    }

    public Integer getTestAnalyteId() {
        return testAnalyteId;
    }

    public void setTestAnalyteId(Integer testAnalyteId) {
        this.testAnalyteId = testAnalyteId;
    }

    public Integer getSelectionType() {
        return selectionType;
    }

    public void setSelectionType(Integer selectionType) {
        this.selectionType = selectionType;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getDescriptionAlias() {
        return descriptionAlias;
    }

    public void setDescriptionAlias(String descriptionAlias) {
        this.descriptionAlias = descriptionAlias;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getAnalyteResultModify() {
        return analyteResultModify;
    }

    public void setAnalyteResultModify(Integer analyteResultModify) {
        this.analyteResultModify = analyteResultModify;
    }

    public Set<Integer> getAnalyteResultOptionIds() {
        return analyteResultOptionIds;
    }

    public void setAnalyteResultOptionIds(Set<Integer> analyteResultOptionIds) {
        this.analyteResultOptionIds = analyteResultOptionIds;
    }
}
