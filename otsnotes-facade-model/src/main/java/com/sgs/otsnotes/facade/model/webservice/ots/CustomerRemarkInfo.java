/**
 * CustomerRemarkInfo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class CustomerRemarkInfo  implements java.io.Serializable {
    private String primaryKey;

    private CustomerRemarkTypeEnum customerRemarkType;

    private String parameter;

    public CustomerRemarkInfo() {
    }

    public CustomerRemarkInfo(
           String primaryKey,
           CustomerRemarkTypeEnum customerRemarkType,
           String parameter) {
           this.primaryKey = primaryKey;
           this.customerRemarkType = customerRemarkType;
           this.parameter = parameter;
    }


    /**
     * Gets the primaryKey value for this CustomerRemarkInfo.
     * 
     * @return primaryKey
     */
    public String getPrimaryKey() {
        return primaryKey;
    }


    /**
     * Sets the primaryKey value for this CustomerRemarkInfo.
     * 
     * @param primaryKey
     */
    public void setPrimaryKey(String primaryKey) {
        this.primaryKey = primaryKey;
    }


    /**
     * Gets the customerRemarkType value for this CustomerRemarkInfo.
     * 
     * @return customerRemarkType
     */
    public CustomerRemarkTypeEnum getCustomerRemarkType() {
        return customerRemarkType;
    }


    /**
     * Sets the customerRemarkType value for this CustomerRemarkInfo.
     * 
     * @param customerRemarkType
     */
    public void setCustomerRemarkType(CustomerRemarkTypeEnum customerRemarkType) {
        this.customerRemarkType = customerRemarkType;
    }


    /**
     * Gets the parameter value for this CustomerRemarkInfo.
     * 
     * @return parameter
     */
    public String getParameter() {
        return parameter;
    }


    /**
     * Sets the parameter value for this CustomerRemarkInfo.
     * 
     * @param parameter
     */
    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof CustomerRemarkInfo)) {
            return false;
        }
        CustomerRemarkInfo other = (CustomerRemarkInfo) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.primaryKey==null && other.getPrimaryKey()==null) || 
             (this.primaryKey!=null &&
              this.primaryKey.equals(other.getPrimaryKey()))) &&
            ((this.customerRemarkType==null && other.getCustomerRemarkType()==null) || 
             (this.customerRemarkType!=null &&
              this.customerRemarkType.equals(other.getCustomerRemarkType()))) &&
            ((this.parameter==null && other.getParameter()==null) || 
             (this.parameter!=null &&
              this.parameter.equals(other.getParameter())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getPrimaryKey() != null) {
            _hashCode += getPrimaryKey().hashCode();
        }
        if (getCustomerRemarkType() != null) {
            _hashCode += getCustomerRemarkType().hashCode();
        }
        if (getParameter() != null) {
            _hashCode += getParameter().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(CustomerRemarkInfo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "CustomerRemarkInfo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("primaryKey");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PrimaryKey"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("customerRemarkType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CustomerRemarkType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "CustomerRemarkTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("parameter");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Parameter"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
