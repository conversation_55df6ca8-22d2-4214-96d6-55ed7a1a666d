package com.sgs.otsnotes.facade.model.trims.info;

/**
 *
 */
public final class ChkChemicalPPSyncInfo extends BaseSyncInfo{
    /**
     *
     */
    private long id;

    /**
     * type= CHKChemicalPP CHKChemicalPPNumber
     */
    private int chemicalPPNo;

    /**
     * type= CHKChemicalPP EndusePPNumber
     */
    private int endUsePPNo;

    /**
     * 1代表有效数据，0代表无效数据
     */
    private int status;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getChemicalPPNo() {
        return chemicalPPNo;
    }

    public void setChemicalPPNo(int chemicalPPNo) {
        this.chemicalPPNo = chemicalPPNo;
    }

    public int getEndUsePPNo() {
        return endUsePPNo;
    }

    public void setEndUsePPNo(int endUsePPNo) {
        this.endUsePPNo = endUsePPNo;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
