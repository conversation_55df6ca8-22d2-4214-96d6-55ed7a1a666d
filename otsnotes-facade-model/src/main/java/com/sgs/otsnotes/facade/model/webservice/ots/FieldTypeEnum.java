/**
 * FieldTypeEnum.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class FieldTypeEnum implements java.io.Serializable {
    private String _value_;
    private static java.util.HashMap _table_ = new java.util.HashMap();

    // Constructor
    protected FieldTypeEnum(String value) {
        _value_ = value;
        _table_.put(_value_,this);
    }

    public static final String _Input = "Input";
    public static final String _Variable = "Variable";
    public static final String _Formula = "Formula";
    public static final String _Barcode = "Barcode";
    public static final String _Conclusion = "Conclusion";
    public static final String _ListBox = "ListBox";
    public static final String _DFF = "DFF";
    public static final String _DataRowField = "DataRowField";
    public static final FieldTypeEnum Input = new FieldTypeEnum(_Input);
    public static final FieldTypeEnum Variable = new FieldTypeEnum(_Variable);
    public static final FieldTypeEnum Formula = new FieldTypeEnum(_Formula);
    public static final FieldTypeEnum Barcode = new FieldTypeEnum(_Barcode);
    public static final FieldTypeEnum Conclusion = new FieldTypeEnum(_Conclusion);
    public static final FieldTypeEnum ListBox = new FieldTypeEnum(_ListBox);
    public static final FieldTypeEnum DFF = new FieldTypeEnum(_DFF);
    public static final FieldTypeEnum DataRowField = new FieldTypeEnum(_DataRowField);
    public String getValue() { return _value_;}
    public static FieldTypeEnum fromValue(String value)
          throws IllegalArgumentException {
        FieldTypeEnum enumeration = (FieldTypeEnum)
            _table_.get(value);
        if (enumeration==null) {
            throw new IllegalArgumentException();
        }
        return enumeration;
    }
    public static FieldTypeEnum fromString(String value)
          throws IllegalArgumentException {
        return fromValue(value);
    }
    public boolean equals(Object obj) {return (obj == this);}
    public int hashCode() { return toString().hashCode();}
    public String toString() { return _value_;}
    public Object readResolve() throws java.io.ObjectStreamException { return fromValue(_value_);}
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumSerializer(
            _javaType, _xmlType);
    }
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumDeserializer(
            _javaType, _xmlType);
    }
    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(FieldTypeEnum.class);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FieldTypeEnum"));
    }
    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

}
