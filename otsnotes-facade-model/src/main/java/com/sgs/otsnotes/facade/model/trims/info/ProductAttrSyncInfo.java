package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class ProductAttrSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer productAttributeId;
    /**
     *
     */
    private String productAttributeType;
    /**
     *
     */
    private Integer productAttributeTypeId;
    /**
     *
     */
    private String productAttributeValue;
    /**
     *
     */
    private String productAttributeDesc;
    /**
     *
     */
    private String status;

    public Integer getProductAttributeId() {
        return productAttributeId;
    }

    public void setProductAttributeId(Integer productAttributeId) {
        this.productAttributeId = productAttributeId;
    }

    public String getProductAttributeType() {
        return productAttributeType;
    }

    public void setProductAttributeType(String productAttributeType) {
        this.productAttributeType = productAttributeType;
    }

    public Integer getProductAttributeTypeId() {
        return productAttributeTypeId;
    }

    public void setProductAttributeTypeId(Integer productAttributeTypeId) {
        this.productAttributeTypeId = productAttributeTypeId;
    }

    public String getProductAttributeValue() {
        return productAttributeValue;
    }

    public void setProductAttributeValue(String productAttributeValue) {
        this.productAttributeValue = productAttributeValue;
    }

    public String getProductAttributeDesc() {
        return productAttributeDesc;
    }

    public void setProductAttributeDesc(String productAttributeDesc) {
        this.productAttributeDesc = productAttributeDesc;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
