package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.PPOrderingSyncInfo;

import java.util.List;

public class PPOrderingSyncRsp extends TrimsSyncBaseRsp {

    private List<PPOrderingSyncInfo> data;

    public List<PPOrderingSyncInfo> getData() {
        return data;
    }

    public void setData(List<PPOrderingSyncInfo> data) {
        this.data = data;
    }
}
