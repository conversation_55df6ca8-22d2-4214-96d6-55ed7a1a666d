package com.sgs.otsnotes.facade.model.trims;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class TrimsSyncBaseReq extends PrintFriendliness {
    /**
     *
     */
    public TrimsSyncBaseReq(){
        this.caller = "SODA";
    }
    /**
     *
     */
    private String caller;
    /**
     *
     */
    private String securityCode;

    public String getCaller() {
        return caller;
    }

    public void setCaller(String caller) {
        this.caller = caller;
    }

    public String getSecurityCode() {
        return securityCode;
    }

    public void setSecurityCode(String securityCode) {
        this.securityCode = securityCode;
    }
}
