/**
 * JS_GeneralArea.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_GeneralArea  extends Element implements java.io.Serializable {
    private String endElementId;

    private GeneralAreaTypeEnum generalAreaType;

    public JS_GeneralArea() {
    }

    public JS_GeneralArea(
           String id,
           String name,
           String endElementId,
           GeneralAreaTypeEnum generalAreaType) {
        super(
            id,
            name);
        this.endElementId = endElementId;
        this.generalAreaType = generalAreaType;
    }


    /**
     * Gets the endElementId value for this JS_GeneralArea.
     * 
     * @return endElementId
     */
    public String getEndElementId() {
        return endElementId;
    }


    /**
     * Sets the endElementId value for this JS_GeneralArea.
     * 
     * @param endElementId
     */
    public void setEndElementId(String endElementId) {
        this.endElementId = endElementId;
    }


    /**
     * Gets the generalAreaType value for this JS_GeneralArea.
     * 
     * @return generalAreaType
     */
    public GeneralAreaTypeEnum getGeneralAreaType() {
        return generalAreaType;
    }


    /**
     * Sets the generalAreaType value for this JS_GeneralArea.
     * 
     * @param generalAreaType
     */
    public void setGeneralAreaType(GeneralAreaTypeEnum generalAreaType) {
        this.generalAreaType = generalAreaType;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_GeneralArea)) {
            return false;
        }
        JS_GeneralArea other = (JS_GeneralArea) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.endElementId==null && other.getEndElementId()==null) || 
             (this.endElementId!=null &&
              this.endElementId.equals(other.getEndElementId()))) &&
            ((this.generalAreaType==null && other.getGeneralAreaType()==null) || 
             (this.generalAreaType!=null &&
              this.generalAreaType.equals(other.getGeneralAreaType())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getEndElementId() != null) {
            _hashCode += getEndElementId().hashCode();
        }
        if (getGeneralAreaType() != null) {
            _hashCode += getGeneralAreaType().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_GeneralArea.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_GeneralArea"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("endElementId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "EndElementId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("generalAreaType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "GeneralAreaType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "GeneralAreaTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
