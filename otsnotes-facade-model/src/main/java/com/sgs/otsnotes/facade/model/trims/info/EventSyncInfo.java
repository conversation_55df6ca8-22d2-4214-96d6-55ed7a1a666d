package com.sgs.otsnotes.facade.model.trims.info;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public final class EventSyncInfo extends PrintFriendliness {
    /**
     *
     */
    public EventSyncInfo(){
        this.masterData = Lists.newArrayList();
        this.dataMarker = Lists.newArrayList();
        this.relationShip = Lists.newArrayList();
    }

    /**
     *
     */
    @J<PERSON>NField(name = "MasterData")
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private List<MasterDataInfo> masterData;

    /**
     *
     */
    @JSONField(name = "DataMarker")
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private List<DataMarkerInfo> dataMarker;

    /**
     *
     */
    @JSONField(name = "RelationShip")
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private List<RelationShipInfo> relationShip;

    public List<MasterDataInfo> getMasterData() {
        return masterData;
    }

    public void setMasterData(List<MasterDataInfo> masterData) {
        this.masterData = masterData;
    }

    public List<DataMarkerInfo> getDataMarker() {
        return dataMarker;
    }

    public void setDataMarker(List<DataMarkerInfo> dataMarker) {
        this.dataMarker = dataMarker;
    }

    public List<RelationShipInfo> getRelationShip() {
        return relationShip;
    }

    public void setRelationShip(List<RelationShipInfo> relationShip) {
        this.relationShip = relationShip;
    }
}
