package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.rsp.dataentry.ProductAttribute;

import java.util.Comparator;

public class ProductAttributeComparator implements Comparator<ProductAttribute> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public ProductAttributeComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(ProductAttribute o1, ProductAttribute o2) {
        int index = Integer.compare(o1.getProductAttributeId(), o2.getProductAttributeId());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
