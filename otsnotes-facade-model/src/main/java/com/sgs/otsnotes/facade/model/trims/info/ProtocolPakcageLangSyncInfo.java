package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class ProtocolPakcageLangSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer versionIdentifier;
    /**
     *
     */
    private Integer languageId;
    /**
     *
     */
    private String internalDocumentRefNo;
    /**
     *
     */
    private String ppReferenceNote;
    /**
     *
     */
    private String reportReferenceNote;
    /**
     *
     */
    private String bossLanguageCode;
    /**
     *
     */
    private String protocolPackageDescription;
    /**
     *
     */
    private List<ConstructionLangSyncInfo> constructionItems;
    /**
     *
     */
    private List<SectionSyncInfo> sectionItems;

    public Integer getVersionIdentifier() {
        return versionIdentifier;
    }

    public void setVersionIdentifier(Integer versionIdentifier) {
        this.versionIdentifier = versionIdentifier;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getInternalDocumentRefNo() {
        return internalDocumentRefNo;
    }

    public void setInternalDocumentRefNo(String internalDocumentRefNo) {
        this.internalDocumentRefNo = internalDocumentRefNo;
    }

    public String getPpReferenceNote() {
        return ppReferenceNote;
    }

    public void setPpReferenceNote(String ppReferenceNote) {
        this.ppReferenceNote = ppReferenceNote;
    }

    public String getReportReferenceNote() {
        return reportReferenceNote;
    }

    public void setReportReferenceNote(String reportReferenceNote) {
        this.reportReferenceNote = reportReferenceNote;
    }

    public String getBossLanguageCode() {
        return bossLanguageCode;
    }

    public void setBossLanguageCode(String bossLanguageCode) {
        this.bossLanguageCode = bossLanguageCode;
    }

    public String getProtocolPackageDescription() {
        return protocolPackageDescription;
    }

    public void setProtocolPackageDescription(String protocolPackageDescription) {
        this.protocolPackageDescription = protocolPackageDescription;
    }

    public List<ConstructionLangSyncInfo> getConstructionItems() {
        return constructionItems;
    }

    public void setConstructionItems(List<ConstructionLangSyncInfo> constructionItems) {
        this.constructionItems = constructionItems;
    }

    public List<SectionSyncInfo> getSectionItems() {
        return sectionItems;
    }

    public void setSectionItems(List<SectionSyncInfo> sectionItems) {
        this.sectionItems = sectionItems;
    }
}
