package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

/**
 * conclusion计算模式
 * <AUTHOR>
 * @date 2020/1/16 19:20
 */
@Dict
public enum ConclusionMode {
    NoLimit(0,"NoLimit"),
    MATRIX(1,"matrix"),
    PP_MATRIX(2,"pp matrix");
    @DictCodeField
    private int id;
    @DictLabelField
    private String name;

    ConclusionMode(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static final Map<Integer, ConclusionMode> maps = new HashMap<Integer, ConclusionMode>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ConclusionMode conclusionMode : ConclusionMode.values()) {
                put(conclusionMode.id, conclusionMode);
            }
        }
    };

    public static ConclusionMode findCode(Integer id) {
        if (id == null || !maps.containsKey(id.intValue())) {
            return ConclusionMode.NoLimit;
        }
        return maps.get(id);
    }

    public static ConclusionMode findCode(Integer id, ConclusionMode defMode) {
        if (id == null || id.intValue() <= 0 || !maps.containsKey(id.intValue())) {
            return defMode;
        }
        return maps.get(id);
    }

    public static boolean check(Integer id, ConclusionMode conclusionMode) {
        if (id == null || !maps.containsKey(id.intValue())){
            return false;
        }
        return maps.get(id.intValue()) == conclusionMode;
    }

    /**
     *
     * @param id
     * @param conclusionMode
     * @return
     */
    public static boolean check(Integer id, Integer conclusionMode) {
        return check(id, ConclusionMode.findCode(conclusionMode));
    }
}
