package com.sgs.otsnotes.facade.model.trims.info;

public class PpTestLineAnalyteRelSyncInfo{
    /**
     *
     */
    private Integer testAnalyteId;
    /**
     *
     */
    private String testAnalyteDescAlias;
    /**
     *
     */
    private Integer sequence;

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Integer getTestAnalyteId() {
        return testAnalyteId;
    }

    public void setTestAnalyteId(Integer testAnalyteId) {
        this.testAnalyteId = testAnalyteId;
    }

    public String getTestAnalyteDescAlias() {
        return testAnalyteDescAlias;
    }

    public void setTestAnalyteDescAlias(String testAnalyteDescAlias) {
        this.testAnalyteDescAlias = testAnalyteDescAlias;
    }
}
