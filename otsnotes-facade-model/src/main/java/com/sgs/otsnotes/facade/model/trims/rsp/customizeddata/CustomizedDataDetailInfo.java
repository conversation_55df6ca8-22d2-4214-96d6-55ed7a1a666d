package com.sgs.otsnotes.facade.model.trims.rsp.customizeddata;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;
import com.sgs.otsnotes.facade.model.trims.rsp.accreditation.AccreditationDetailsSyncInfo;

import java.util.List;

public class CustomizedDataDetailInfo extends PrintFriendliness {
    /**
     *
     */
    private String dataType;
    /**
     *
     */
    private String itemId;

    /**
     *
     */
    private String dffList;
    /**
     * 处理好之后的dff列表
     */
    private List<DffInfoRsp> dealDffList;

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getDffList() {
        return dffList;
    }

    public void setDffList(String dffList) {
        this.dffList = dffList;
    }

    public List<DffInfoRsp> getDealDffList() {
        return dealDffList;
    }

    public void setDealDffList(List<DffInfoRsp> dealDffList) {
        this.dealDffList = dealDffList;
    }
}
