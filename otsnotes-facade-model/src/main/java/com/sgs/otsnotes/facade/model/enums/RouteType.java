package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum RouteType {
    None(0, "None"),
    Old(1, "Old"),
    New(2, "New"),
    Update(4, "Update"),
    ForceNewMethod(8, "ForceNewMethod"),
    SyncToOldData(16, "SyncToOldData");

    private int routeId;
    private String message;

    RouteType(int routeId, String message) {
        this.routeId = routeId;
        this.message = message;
    }

    public int getRouteId() {
        return routeId;
    }

    public String message() {
        return this.message;
    }

    public static final Map<Integer, RouteType> maps = new HashMap<Integer, RouteType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (RouteType routeType : RouteType.values()) {
                put(routeType.getRouteId(), routeType);
            }
        }
    };

    public static RouteType getRouteId(Integer routeId) {
        if (routeId == null || !maps.containsKey(routeId.intValue())){
            return null;
        }
        return maps.get(routeId.intValue());
    }

    public boolean check(RouteType... routeTypes){
        if (routeTypes == null || routeTypes.length <= 0){
            return false;
        }
        for (RouteType routeType: routeTypes){
            if (this.getRouteId() == routeType.getRouteId()){
                return true;
            }
        }
        return false;
    }

    public static boolean check(Integer routeId, RouteType routeType){
        if (routeId == null){
            routeId = 0;
        }
        if (routeType == RouteType.Old && routeId.intValue() <= 0) {
            return true;
        }
        if (routeType == null){
            return false;
        }
        return (routeId.intValue() & routeType.getRouteId()) > 0;
    }

}
