package com.sgs.otsnotes.facade.model.enums;

/**
 * @Author: mingyang.chen
 * @Date: 2021/3/31 17:42
 */
public enum TestPositionType {
    /**
     * 和原来trims进行比较 默认0没有变化，1.testPosition,2.trimsPosition
     */
    None(0,"default trims position"),
    TEST_POSITION_EXIST(1,"test position exist"),
    TRIMS_POSITION_EXIST(2,"trims position exist"),
    ;
    private final int code;
    private final String msg;

    TestPositionType(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

    public int getCode() {
        return code;
    }
}
