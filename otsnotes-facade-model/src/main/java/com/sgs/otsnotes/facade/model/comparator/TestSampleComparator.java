package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.enums.SampleType;
import com.sgs.otsnotes.facade.model.req.TestSampleReq;

import java.util.Comparator;

public class TestSampleComparator implements Comparator<TestSampleReq> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public TestSampleComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(TestSampleReq o1, TestSampleReq o2) {
        SampleType o1SampleType = SampleType.findType(o1.getSampleType());
        if (o1SampleType != null && o1SampleType == SampleType.OriginalSample){
            if (o1.getSampleSeq() == null){
                o1.setSampleSeq(Integer.MAX_VALUE);
            }
        }
        SampleType o2SampleType = SampleType.findType(o1.getSampleType());
        if (o2SampleType != null && o2SampleType == SampleType.OriginalSample){
            if (o2.getSampleSeq() == null){
                o2.setSampleSeq(Integer.MAX_VALUE);
            }
        }
        if (o1.getSampleSeq() == null){
            o1.setSampleSeq(Integer.MAX_VALUE-1);
        }
        if (o2.getSampleSeq() == null){
            o2.setSampleSeq(Integer.MAX_VALUE-1);
        }
        int index = Integer.compare(o1.getSampleSeq(), o2.getSampleSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return o1.getSampleNo().compareTo(o2.getSampleNo());
    }
}
