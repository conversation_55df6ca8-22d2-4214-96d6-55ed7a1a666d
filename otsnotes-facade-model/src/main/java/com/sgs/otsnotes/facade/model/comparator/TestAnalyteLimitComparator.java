package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.req.requirment.TestAnalyteLimitRsp;

import java.util.Comparator;

public class TestAnalyteLimitComparator implements Comparator<TestAnalyteLimitRsp> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public TestAnalyteLimitComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(TestAnalyteLimitRsp o1, TestAnalyteLimitRsp o2) {
        int index = Integer.compare(o1.getVersionIdentifier(), o2.getVersionIdentifier());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        index = Integer.compare(o1.getSequence(), o2.getSequence());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
