package com.sgs.otsnotes.facade.model.trims.info;

public final class TestLineTypeInfo {
    /**
     * 如果状态为Inactive则下面其它信息都不需要提供。
     */
    private int testLineId;

    /**
     * 默认0;1(testItemName等于Pretreatment);2(testItemName等于OOB Test)
     */
    private int testLineType;

    /**
     * 0：Inactive、1：Active
     */
    private int dataDeleted;

    /**
     * ModifiedDate BIGINT(19)<br>
     *
     */
    private Long modifiedDate;

    public int getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(int testLineId) {
        this.testLineId = testLineId;
    }

    public int getTestLineType() {
        return testLineType;
    }

    public void setTestLineType(int testLineType) {
        this.testLineType = testLineType;
    }

    public int getDataDeleted() {
        return dataDeleted;
    }

    public void setDataDeleted(int dataDeleted) {
        this.dataDeleted = dataDeleted;
    }

    public Long getModifiedDate() {
        return modifiedDate;
    }

    public void setModifiedDate(Long modifiedDate) {
        this.modifiedDate = modifiedDate;
    }
}
