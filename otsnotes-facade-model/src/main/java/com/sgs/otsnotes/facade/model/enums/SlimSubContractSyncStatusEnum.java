package com.sgs.otsnotes.facade.model.enums;

/**
 * @Author: Jinx
 * @Date: 2019-06-13 14:07
 * @Description:
 **/
public enum SlimSubContractSyncStatusEnum {

    pending(0),
    trigger(1),
    slimResponse(2)
    ;

    private int value = 1;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    SlimSubContractSyncStatusEnum(int value){
        this.value = value;
    }

}
