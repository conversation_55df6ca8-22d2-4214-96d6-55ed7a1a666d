package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.rsp.requirment.TestLineLimitItem;

import java.util.Comparator;

public class RequirmentTestLineLimitComparator implements Comparator<TestLineLimitItem> {
    /**
     * 是否为升序
     */
    private boolean isAsc;

    public RequirmentTestLineLimitComparator() {
        this.isAsc = false;
    }

    public RequirmentTestLineLimitComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param testLine1
     * @param testLine2
     * @return
     */
    @Override
    public int compare(TestLineLimitItem testLine1, TestLineLimitItem testLine2) {
        String labSectionName1 = testLine1.getLabSectionName();
        if (labSectionName1 == null) {
            labSectionName1 = "";
        }
        String labSectionName2 = testLine2.getLabSectionName();
        if (labSectionName2 == null) {
            labSectionName2 = "";
        }
        int index = labSectionName1.compareToIgnoreCase(labSectionName2);
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        Integer sampleSeq1 = testLine1.getSampleSeq();
        Integer sampleSeq2 = testLine2.getSampleSeq();
        if (sampleSeq1 == null) {
            sampleSeq1 = 0;
        }
        if (sampleSeq2 == null) {
            sampleSeq2 = 0;
        }
        index = Integer.compare(sampleSeq1, sampleSeq2);
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        Integer testLineId1 = testLine1.getTestLineId();
        Integer testLineId2 = testLine2.getTestLineId();
        if (testLineId1 == null) {
            testLineId1 = 0;
        }
        if (testLineId2 == null) {
            testLineId2 = 0;
        }
        index = Integer.compare(testLineId1, testLineId2);
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
