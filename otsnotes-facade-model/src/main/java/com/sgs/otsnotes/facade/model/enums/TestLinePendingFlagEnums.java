package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;

/**
 * @ClassName TestLinePendingFlagEnums
 * @Description TODO
 * <AUTHOR>
 * @Date 1/13/2021
 */
public enum TestLinePendingFlagEnums {
    Pending(true),
    UnPending(false);
    private Boolean flag;

    TestLinePendingFlagEnums(Boolean flag) {
        this.flag = flag;
    }

    public Boolean getFlag() {
        return flag;
    }

    public static boolean check(Boolean flag, TestLinePendingFlagEnums... enums) {
        if (flag == null) {
            return false;
        }
        for (TestLinePendingFlagEnums anEnum : enums) {
            if (anEnum.flag.compareTo(flag)== 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 代码中很多地方判断是否pending，然后提示语都一样，避免每个方法都要写一遍下面的返回语句，这里统一设置下
     *
     * @return
     */
    public static String getPendingMsg() {
        return "The testLine already pending ,Please UnPending first";
    }

}
