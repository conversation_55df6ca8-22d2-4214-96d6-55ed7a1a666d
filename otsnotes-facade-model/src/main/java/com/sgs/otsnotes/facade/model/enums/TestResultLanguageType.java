package com.sgs.otsnotes.facade.model.enums;

/**
 * <AUTHOR>
 */

public enum TestResultLanguageType {
    Follow_order_language(1),
    Use_EN_Report(2);

    private Integer type;

    TestResultLanguageType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public static boolean checkType(Integer type, TestResultLanguageType... types) {
        if (type == null || types == null || types.length == 0) {
            return false;
        }
        for (TestResultLanguageType testResultLanguageType : types) {
            if (testResultLanguageType.type.compareTo(type) == 0) {
                return true;
            }
        }
        return false;
    }
}
