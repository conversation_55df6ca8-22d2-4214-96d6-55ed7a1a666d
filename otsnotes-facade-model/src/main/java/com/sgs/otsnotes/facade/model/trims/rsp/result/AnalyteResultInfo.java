package com.sgs.otsnotes.facade.model.trims.rsp.result;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public final class AnalyteResultInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer analyteResultId;

    /**
     *
     */
    private String analyteResultType;

    /**
     *
     */
    private Integer productLineId;

    /**
     *
     */
    private Integer status;

    /**
     *
     */
    private List<AnalyteResultOptionInfo> analyteResultOptions;

    /**
     *
     */
    private List<AnalyteResultLangInfo> otherLanguageItems;

    public Integer getAnalyteResultId() {
        return analyteResultId;
    }

    public void setAnalyteResultId(Integer analyteResultId) {
        this.analyteResultId = analyteResultId;
    }

    public String getAnalyteResultType() {
        return analyteResultType;
    }

    public void setAnalyteResultType(String analyteResultType) {
        this.analyteResultType = analyteResultType;
    }

    public Integer getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<AnalyteResultOptionInfo> getAnalyteResultOptions() {
        return analyteResultOptions;
    }

    public void setAnalyteResultOptions(List<AnalyteResultOptionInfo> analyteResultOptions) {
        this.analyteResultOptions = analyteResultOptions;
    }

    public List<AnalyteResultLangInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<AnalyteResultLangInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }
}
