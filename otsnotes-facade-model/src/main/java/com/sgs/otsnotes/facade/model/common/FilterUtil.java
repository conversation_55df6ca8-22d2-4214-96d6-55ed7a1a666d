package com.sgs.otsnotes.facade.model.common;

import java.util.ArrayList;
import java.util.List;

public abstract class FilterUtil<T> {

    /**
     * 过滤list
     *
     * @param list
     * @return
     * <AUTHOR>
     * 2017年12月29日 上午10:03:45
     */
    public List<T> filter(List<T> list) {
        List<T> resultList = new ArrayList<>();
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                if (!remove(list.get(i))) {
                    resultList.add(list.get(i));
                }
            }
        }
        return resultList;
    }

    public abstract boolean remove(T t);
}
