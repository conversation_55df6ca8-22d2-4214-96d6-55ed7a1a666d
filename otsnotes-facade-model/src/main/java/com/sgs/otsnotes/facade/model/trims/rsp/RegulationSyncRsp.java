package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;
import com.sgs.otsnotes.facade.model.trims.info.RegulationSyncInfo;

import java.util.List;

public class RegulationSyncRsp extends TrimsSyncBaseRsp {
    /**
     *
     */
    private List<RegulationSyncInfo> data;

    public List<RegulationSyncInfo> getData() {
        return data;
    }

    public void setData(List<RegulationSyncInfo> data) {
        this.data = data;
    }
}
