package com.sgs.otsnotes.facade.model.trims.rsp.accreditation;

import com.alibaba.fastjson.annotation.JSONField;
import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class AccreditationDetailsSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer accreditationDetailId;

    /**
     *
     */
    private String mdl;
    /**
     *
     */
    private Integer mdlUnitId;
    /**
     *
     */
    private List<Integer> testAnalyteIds;
    /**
     *
     */
    private List<LaboratoriesSyncInfo> laboratories;
    /**
     * -- 1代表数据已经删除，以下内容不会输出
     */
    @JSONField(name = "isDeleted")
    private Integer del;

    public Integer getAccreditationDetailId() {
        return accreditationDetailId;
    }

    public void setAccreditationDetailId(Integer accreditationDetailId) {
        this.accreditationDetailId = accreditationDetailId;
    }

    public String getMdl() {
        return mdl;
    }

    public void setMdl(String mdl) {
        this.mdl = mdl;
    }

    public Integer getMdlUnitId() {
        return mdlUnitId;
    }

    public void setMdlUnitId(Integer mdlUnitId) {
        this.mdlUnitId = mdlUnitId;
    }

    public List<Integer> getTestAnalyteIds() {
        return testAnalyteIds;
    }

    public void setTestAnalyteIds(List<Integer> testAnalyteIds) {
        this.testAnalyteIds = testAnalyteIds;
    }

    public List<LaboratoriesSyncInfo> getLaboratories() {
        return laboratories;
    }

    public void setLaboratories(List<LaboratoriesSyncInfo> laboratories) {
        this.laboratories = laboratories;
    }

    public Integer getDel() {
        return del;
    }

    public void setDel(Integer del) {
        this.del = del;
    }
}