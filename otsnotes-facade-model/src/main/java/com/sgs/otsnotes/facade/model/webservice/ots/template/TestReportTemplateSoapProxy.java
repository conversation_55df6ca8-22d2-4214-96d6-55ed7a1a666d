package com.sgs.otsnotes.facade.model.webservice.ots.template;

public class TestReportTemplateSoapProxy implements com.sgs.otsnotes.facade.model.webservice.ots.template.TestReportTemplateSoap {
  private String _endpoint = null;
  private com.sgs.otsnotes.facade.model.webservice.ots.template.TestReportTemplateSoap testReportTemplateSoap = null;
  
  public TestReportTemplateSoapProxy() {
    _initTestReportTemplateSoapProxy();
  }
  
  public TestReportTemplateSoapProxy(String endpoint) {
    _endpoint = endpoint;
    _initTestReportTemplateSoapProxy();
  }
  
  private void _initTestReportTemplateSoapProxy() {
    try {
      testReportTemplateSoap = (new com.sgs.otsnotes.facade.model.webservice.ots.template.TestReportTemplateLocator()).getTestReportTemplateSoap();
      if (testReportTemplateSoap != null) {
        if (_endpoint != null) {
            ((javax.xml.rpc.Stub)testReportTemplateSoap)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
        } else {
            _endpoint = (String)((javax.xml.rpc.Stub)testReportTemplateSoap)._getProperty("javax.xml.rpc.service.endpoint.address");
        }
      }
      
    }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  
  public String getEndpoint() {
    return _endpoint;
  }
  
  public void setEndpoint(String endpoint) {
    _endpoint = endpoint;
    if (testReportTemplateSoap != null) {
        ((javax.xml.rpc.Stub)testReportTemplateSoap)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    }
    
  }
  
  public com.sgs.otsnotes.facade.model.webservice.ots.template.TestReportTemplateSoap getTestReportTemplateSoap() {
    if (testReportTemplateSoap == null) {
        _initTestReportTemplateSoapProxy();
    }
    return testReportTemplateSoap;
  }
  
  public com.sgs.otsnotes.facade.model.webservice.ots.template.TemplateListView[] queryBusinessTemplateList(int templateTypeID, int buSubTypeID, int templateID, java.lang.String templateName, java.lang.String customerName, java.lang.String customerNo, java.lang.String templatePackageName, java.lang.String groupCode, java.lang.String groupName, java.lang.String PPTemplateID) throws java.rmi.RemoteException{
    if (testReportTemplateSoap == null) {
        _initTestReportTemplateSoapProxy();
    }
    return testReportTemplateSoap.queryBusinessTemplateList(templateTypeID, buSubTypeID, templateID, templateName, customerName, customerNo, templatePackageName, groupCode, groupName, PPTemplateID);
  }
  
  public com.sgs.otsnotes.facade.model.webservice.ots.template.TemplateListView[] queryBusinessTemplateList2(int applicationID, int templateTypeID, int buId, int templateID, java.lang.String templateName, java.lang.String customerName, java.lang.String customerNo, java.lang.String templatePackageName, java.lang.String groupCode, java.lang.String groupName) throws java.rmi.RemoteException{
    if (testReportTemplateSoap == null) {
        _initTestReportTemplateSoapProxy();
    }
    return testReportTemplateSoap.queryBusinessTemplateList2(applicationID, templateTypeID, buId, templateID, templateName, customerName, customerNo, templatePackageName, groupCode, groupName);
  }
  
  
}