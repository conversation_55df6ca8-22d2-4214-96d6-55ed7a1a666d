package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.rsp.sample.TestSampleSimplifyInfo;

import java.util.Comparator;

public class TestSampleSimplifyMixComparator implements Comparator<TestSampleSimplifyInfo> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public TestSampleSimplifyMixComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(TestSampleSimplifyInfo o1, TestSampleSimplifyInfo o2) {
        if(o1.getSampleType() == null){
            o1.setSampleType(Integer.MAX_VALUE);
        }
        if(o2.getSampleType() == null){
            o2.setSampleType(Integer.MAX_VALUE);
        }
        if (o1.getSampleSeq() == null){
            o1.setSampleSeq(Integer.MAX_VALUE-1);
        }
        if (o2.getSampleSeq() == null){
            o2.setSampleSeq(Integer.MAX_VALUE-1);
        }
        int index = Integer.compare(o1.getSampleType(),o2.getSampleType());
        if(index < 0){
            return isAsc ? -1 : 1;
        }
        if(index > 0 ){
            return isAsc ? 1 : -1;
        }

        index = Integer.compare(o1.getSampleSeq(), o2.getSampleSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return o1.getSampleNo().compareTo(o2.getSampleNo());
    }
}
