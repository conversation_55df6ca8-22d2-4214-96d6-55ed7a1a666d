package com.sgs.otsnotes.facade.model.kafka;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/8/6 18:03
 */
public class ReportApproveMessage implements Serializable {
    private static final long serialVersionUID = 1154539273898711271L;

    private String orderNo;

    private String reportNo;

    private String reportApprovedDate;

    private String overAllConclusion;

    private String approvedBy;


    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getReportApprovedDate() {
        return reportApprovedDate;
    }

    public void setReportApprovedDate(String reportApprovedDate) {
        this.reportApprovedDate = reportApprovedDate;
    }

    public String getOverAllConclusion() {
        return overAllConclusion;
    }

    public void setOverAllConclusion(String overAllConclusion) {
        this.overAllConclusion = overAllConclusion;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }
}
