package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class ProductTaxonomySyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer productTaxonomyId;
    /**
     *
     */
    private String productTaxonomyName;

    public Integer getProductTaxonomyId() {
        return productTaxonomyId;
    }

    public void setProductTaxonomyId(Integer productTaxonomyId) {
        this.productTaxonomyId = productTaxonomyId;
    }

    public String getProductTaxonomyName() {
        return productTaxonomyName;
    }

    public void setProductTaxonomyName(String productTaxonomyName) {
        this.productTaxonomyName = productTaxonomyName;
    }
}
