package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;

/**
 * 报告文件类型枚举类
 * todo 系统中有好几个FileType的概念 后续需要推进统一
 */
public enum ReportFileTypeEnums {
    /**
     * 二进制文件
     */
    BINARY(1, "二进制文件"),
    /**
     * 阿里云文件标识
     */
    ALIYUN_CLOUD_ID(2, "阿里云文件标识"),
    /**
     * arzure云文件标识
     */
    ARZURE_CLOUD_ID(3, "azure云文件标识");

    private final int code;
    private final String desc;

    ReportFileTypeEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
