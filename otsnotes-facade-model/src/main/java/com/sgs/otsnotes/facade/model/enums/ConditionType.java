package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum ConditionType {
    Normal(1, "Normal"),
    BlockCondition(2, "BlockCondition"),
    ProcedureCondition(4, "ProcedureCondition");
    @DictCodeField
    private int code;
    @DictLabelField
    private String message;

    ConditionType(int value, String message) {
        this.code = value;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return message;
    }

    static Map<Integer, ConditionType> maps = new HashMap<>();

    static {
        for (ConditionType type : ConditionType.values()) {
            maps.put(type.getCode(), type);
        }
    }

    public static ConditionType findCode(Integer code) {
        if (code == null || !maps.containsKey(code)) {
            return null;
        }
        ConditionType type = maps.get(code);
        if (type == null) {
            /*throw new IllegalArgumentException("ConclusionType not found" + code);*/
            return null;
        }
        return type;
    }

    public static boolean check(Integer code) {
        if (code == null || !maps.containsKey(code)) {
            return false;
        }
        return maps.get(code) != null;
    }

    public static boolean check(Integer code, ConditionType conditionType) {
        if (code == null || !maps.containsKey(code)) {
            return false;
        }
        return maps.get(code) != conditionType;
    }
}
