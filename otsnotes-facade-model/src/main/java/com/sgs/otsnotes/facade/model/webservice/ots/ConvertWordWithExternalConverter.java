/**
 * ConvertWordWithExternalConverter.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ConvertWordWithExternalConverter  implements java.io.Serializable {
    private String htmlString;

    private String pdfFilePath;

    private int waitMilliSeconds;

    public ConvertWordWithExternalConverter() {
    }

    public ConvertWordWithExternalConverter(
           String htmlString,
           String pdfFilePath,
           int waitMilliSeconds) {
           this.htmlString = htmlString;
           this.pdfFilePath = pdfFilePath;
           this.waitMilliSeconds = waitMilliSeconds;
    }


    /**
     * Gets the htmlString value for this ConvertWordWithExternalConverter.
     * 
     * @return htmlString
     */
    public String getHtmlString() {
        return htmlString;
    }


    /**
     * Sets the htmlString value for this ConvertWordWithExternalConverter.
     * 
     * @param htmlString
     */
    public void setHtmlString(String htmlString) {
        this.htmlString = htmlString;
    }


    /**
     * Gets the pdfFilePath value for this ConvertWordWithExternalConverter.
     * 
     * @return pdfFilePath
     */
    public String getPdfFilePath() {
        return pdfFilePath;
    }


    /**
     * Sets the pdfFilePath value for this ConvertWordWithExternalConverter.
     * 
     * @param pdfFilePath
     */
    public void setPdfFilePath(String pdfFilePath) {
        this.pdfFilePath = pdfFilePath;
    }


    /**
     * Gets the waitMilliSeconds value for this ConvertWordWithExternalConverter.
     * 
     * @return waitMilliSeconds
     */
    public int getWaitMilliSeconds() {
        return waitMilliSeconds;
    }


    /**
     * Sets the waitMilliSeconds value for this ConvertWordWithExternalConverter.
     * 
     * @param waitMilliSeconds
     */
    public void setWaitMilliSeconds(int waitMilliSeconds) {
        this.waitMilliSeconds = waitMilliSeconds;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ConvertWordWithExternalConverter)) {
            return false;
        }
        ConvertWordWithExternalConverter other = (ConvertWordWithExternalConverter) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.htmlString==null && other.getHtmlString()==null) || 
             (this.htmlString!=null &&
              this.htmlString.equals(other.getHtmlString()))) &&
            ((this.pdfFilePath==null && other.getPdfFilePath()==null) || 
             (this.pdfFilePath!=null &&
              this.pdfFilePath.equals(other.getPdfFilePath()))) &&
            this.waitMilliSeconds == other.getWaitMilliSeconds();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getHtmlString() != null) {
            _hashCode += getHtmlString().hashCode();
        }
        if (getPdfFilePath() != null) {
            _hashCode += getPdfFilePath().hashCode();
        }
        _hashCode += getWaitMilliSeconds();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ConvertWordWithExternalConverter.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">ConvertWordWithExternalConverter"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("htmlString");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "htmlString"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("pdfFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "pdfFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("waitMilliSeconds");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "waitMilliSeconds"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
