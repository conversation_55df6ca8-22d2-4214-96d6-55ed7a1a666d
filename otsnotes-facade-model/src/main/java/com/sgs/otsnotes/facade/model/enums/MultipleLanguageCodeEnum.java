package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum MultipleLanguageCodeEnum {
	CN("CN",2),
	EN("EN",1);

	@DictCodeField
	@DictLabelField
	private String code;
	private Integer id;

	MultipleLanguageCodeEnum(String code, Integer id){
		this.code = code;
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public Integer getId() {
		return id;
	}
}
