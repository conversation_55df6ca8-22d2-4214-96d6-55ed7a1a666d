package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

/**
 *
 */
public enum TestLineSubToType {
    None(0, "普通【默认值】"),
    ToSubContract(1, "ToSubContract"),
    ToNewSubContract(2, "ToNewSubContract"),
    ;

    private int type;
    private String message;

    TestLineSubToType(int type, String message) {
        this.type = type;
        this.message = message;
    }

    public int getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }


    public static final Map<Integer, TestLineSubToType> maps = new HashMap<Integer, TestLineSubToType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TestLineSubToType type : TestLineSubToType.values()) {
                put(type.getType(), type);
            }
        }
    };

    public static TestLineSubToType findType(Integer type) {
        if (type == null || !maps.containsKey(type)) {
            return null;
        }
        return maps.get(type);
    }

    /**
     *
     * @param status
     * @param testLineSubToTypes
     * @return
     */
    public static boolean check(Integer status, TestLineSubToType... testLineSubToTypes) {
        if (status == null || !maps.containsKey(status) || testLineSubToTypes == null || testLineSubToTypes.length <= 0){
            return false;
        }
        for (TestLineSubToType tlStatus: testLineSubToTypes){
            if (status == tlStatus.getType()){
                return true;
            }
        }
        return false;
    }

}
