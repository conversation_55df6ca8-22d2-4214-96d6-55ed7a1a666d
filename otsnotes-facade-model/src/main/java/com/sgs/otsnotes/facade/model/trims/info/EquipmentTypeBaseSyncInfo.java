package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/1 16:44
 */
public class EquipmentTypeBaseSyncInfo extends PrintFriendliness {
    private Integer equipmentTypeId;

    private String equipmentTypeName;

    private String equipmentTypeShortName;

    private String equipmentTypeDesc;

    private String status;

    private List<EquipmentTypeLanguageSyncInfo> otherLanguageItems;


    public Integer getEquipmentTypeId() {
        return equipmentTypeId;
    }

    public void setEquipmentTypeId(Integer equipmentTypeId) {
        this.equipmentTypeId = equipmentTypeId;
    }

    public String getEquipmentTypeName() {
        return equipmentTypeName;
    }

    public void setEquipmentTypeName(String equipmentTypeName) {
        this.equipmentTypeName = equipmentTypeName;
    }

    public String getEquipmentTypeShortName() {
        return equipmentTypeShortName;
    }

    public void setEquipmentTypeShortName(String equipmentTypeShortName) {
        this.equipmentTypeShortName = equipmentTypeShortName;
    }

    public String getEquipmentTypeDesc() {
        return equipmentTypeDesc;
    }

    public void setEquipmentTypeDesc(String equipmentTypeDesc) {
        this.equipmentTypeDesc = equipmentTypeDesc;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<EquipmentTypeLanguageSyncInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<EquipmentTypeLanguageSyncInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }
}
