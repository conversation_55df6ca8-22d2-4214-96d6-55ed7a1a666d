/**
 * JS_Table.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_Table  extends Element implements java.io.Serializable {
    private String[] settingType;

    private DatasourceTypeEnum tableRepeatDataSource;

    private DatasourceTypeEnum columnRepeatDataSource;

    private DatasourceTypeEnum rowRepeatDataSource;

    private String[] rowRepeatCells;

    private String[] columnRepeatCells;

    private int maxColumnCount;

    private boolean copyColumnsWhole;

    private boolean copyRowsWhole;

    private TableFormatScope usedFormatScope;

    private JS_TableFormat tableFormat;

    private StaticTableDim[] staticDims;

    public JS_Table() {
    }

    public JS_Table(
           String id,
           String name,
           String[] settingType,
           DatasourceTypeEnum tableRepeatDataSource,
           DatasourceTypeEnum columnRepeatDataSource,
           DatasourceTypeEnum rowRepeatDataSource,
           String[] rowRepeatCells,
           String[] columnRepeatCells,
           int maxColumnCount,
           boolean copyColumnsWhole,
           boolean copyRowsWhole,
           TableFormatScope usedFormatScope,
           JS_TableFormat tableFormat,
           StaticTableDim[] staticDims) {
        super(
            id,
            name);
        this.settingType = settingType;
        this.tableRepeatDataSource = tableRepeatDataSource;
        this.columnRepeatDataSource = columnRepeatDataSource;
        this.rowRepeatDataSource = rowRepeatDataSource;
        this.rowRepeatCells = rowRepeatCells;
        this.columnRepeatCells = columnRepeatCells;
        this.maxColumnCount = maxColumnCount;
        this.copyColumnsWhole = copyColumnsWhole;
        this.copyRowsWhole = copyRowsWhole;
        this.usedFormatScope = usedFormatScope;
        this.tableFormat = tableFormat;
        this.staticDims = staticDims;
    }


    /**
     * Gets the settingType value for this JS_Table.
     * 
     * @return settingType
     */
    public String[] getSettingType() {
        return settingType;
    }


    /**
     * Sets the settingType value for this JS_Table.
     * 
     * @param settingType
     */
    public void setSettingType(String[] settingType) {
        this.settingType = settingType;
    }


    /**
     * Gets the tableRepeatDataSource value for this JS_Table.
     * 
     * @return tableRepeatDataSource
     */
    public DatasourceTypeEnum getTableRepeatDataSource() {
        return tableRepeatDataSource;
    }


    /**
     * Sets the tableRepeatDataSource value for this JS_Table.
     * 
     * @param tableRepeatDataSource
     */
    public void setTableRepeatDataSource(DatasourceTypeEnum tableRepeatDataSource) {
        this.tableRepeatDataSource = tableRepeatDataSource;
    }


    /**
     * Gets the columnRepeatDataSource value for this JS_Table.
     * 
     * @return columnRepeatDataSource
     */
    public DatasourceTypeEnum getColumnRepeatDataSource() {
        return columnRepeatDataSource;
    }


    /**
     * Sets the columnRepeatDataSource value for this JS_Table.
     * 
     * @param columnRepeatDataSource
     */
    public void setColumnRepeatDataSource(DatasourceTypeEnum columnRepeatDataSource) {
        this.columnRepeatDataSource = columnRepeatDataSource;
    }


    /**
     * Gets the rowRepeatDataSource value for this JS_Table.
     * 
     * @return rowRepeatDataSource
     */
    public DatasourceTypeEnum getRowRepeatDataSource() {
        return rowRepeatDataSource;
    }


    /**
     * Sets the rowRepeatDataSource value for this JS_Table.
     * 
     * @param rowRepeatDataSource
     */
    public void setRowRepeatDataSource(DatasourceTypeEnum rowRepeatDataSource) {
        this.rowRepeatDataSource = rowRepeatDataSource;
    }


    /**
     * Gets the rowRepeatCells value for this JS_Table.
     * 
     * @return rowRepeatCells
     */
    public String[] getRowRepeatCells() {
        return rowRepeatCells;
    }


    /**
     * Sets the rowRepeatCells value for this JS_Table.
     * 
     * @param rowRepeatCells
     */
    public void setRowRepeatCells(String[] rowRepeatCells) {
        this.rowRepeatCells = rowRepeatCells;
    }


    /**
     * Gets the columnRepeatCells value for this JS_Table.
     * 
     * @return columnRepeatCells
     */
    public String[] getColumnRepeatCells() {
        return columnRepeatCells;
    }


    /**
     * Sets the columnRepeatCells value for this JS_Table.
     * 
     * @param columnRepeatCells
     */
    public void setColumnRepeatCells(String[] columnRepeatCells) {
        this.columnRepeatCells = columnRepeatCells;
    }


    /**
     * Gets the maxColumnCount value for this JS_Table.
     * 
     * @return maxColumnCount
     */
    public int getMaxColumnCount() {
        return maxColumnCount;
    }


    /**
     * Sets the maxColumnCount value for this JS_Table.
     * 
     * @param maxColumnCount
     */
    public void setMaxColumnCount(int maxColumnCount) {
        this.maxColumnCount = maxColumnCount;
    }


    /**
     * Gets the copyColumnsWhole value for this JS_Table.
     * 
     * @return copyColumnsWhole
     */
    public boolean isCopyColumnsWhole() {
        return copyColumnsWhole;
    }


    /**
     * Sets the copyColumnsWhole value for this JS_Table.
     * 
     * @param copyColumnsWhole
     */
    public void setCopyColumnsWhole(boolean copyColumnsWhole) {
        this.copyColumnsWhole = copyColumnsWhole;
    }


    /**
     * Gets the copyRowsWhole value for this JS_Table.
     * 
     * @return copyRowsWhole
     */
    public boolean isCopyRowsWhole() {
        return copyRowsWhole;
    }


    /**
     * Sets the copyRowsWhole value for this JS_Table.
     * 
     * @param copyRowsWhole
     */
    public void setCopyRowsWhole(boolean copyRowsWhole) {
        this.copyRowsWhole = copyRowsWhole;
    }


    /**
     * Gets the usedFormatScope value for this JS_Table.
     * 
     * @return usedFormatScope
     */
    public TableFormatScope getUsedFormatScope() {
        return usedFormatScope;
    }


    /**
     * Sets the usedFormatScope value for this JS_Table.
     * 
     * @param usedFormatScope
     */
    public void setUsedFormatScope(TableFormatScope usedFormatScope) {
        this.usedFormatScope = usedFormatScope;
    }


    /**
     * Gets the tableFormat value for this JS_Table.
     * 
     * @return tableFormat
     */
    public JS_TableFormat getTableFormat() {
        return tableFormat;
    }


    /**
     * Sets the tableFormat value for this JS_Table.
     * 
     * @param tableFormat
     */
    public void setTableFormat(JS_TableFormat tableFormat) {
        this.tableFormat = tableFormat;
    }


    /**
     * Gets the staticDims value for this JS_Table.
     * 
     * @return staticDims
     */
    public StaticTableDim[] getStaticDims() {
        return staticDims;
    }


    /**
     * Sets the staticDims value for this JS_Table.
     * 
     * @param staticDims
     */
    public void setStaticDims(StaticTableDim[] staticDims) {
        this.staticDims = staticDims;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_Table)) {
            return false;
        }
        JS_Table other = (JS_Table) obj;
        if (obj == null) {
            return false;
        }
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.settingType==null && other.getSettingType()==null) || 
             (this.settingType!=null &&
              java.util.Arrays.equals(this.settingType, other.getSettingType()))) &&
            ((this.tableRepeatDataSource==null && other.getTableRepeatDataSource()==null) || 
             (this.tableRepeatDataSource!=null &&
              this.tableRepeatDataSource.equals(other.getTableRepeatDataSource()))) &&
            ((this.columnRepeatDataSource==null && other.getColumnRepeatDataSource()==null) || 
             (this.columnRepeatDataSource!=null &&
              this.columnRepeatDataSource.equals(other.getColumnRepeatDataSource()))) &&
            ((this.rowRepeatDataSource==null && other.getRowRepeatDataSource()==null) || 
             (this.rowRepeatDataSource!=null &&
              this.rowRepeatDataSource.equals(other.getRowRepeatDataSource()))) &&
            ((this.rowRepeatCells==null && other.getRowRepeatCells()==null) || 
             (this.rowRepeatCells!=null &&
              java.util.Arrays.equals(this.rowRepeatCells, other.getRowRepeatCells()))) &&
            ((this.columnRepeatCells==null && other.getColumnRepeatCells()==null) || 
             (this.columnRepeatCells!=null &&
              java.util.Arrays.equals(this.columnRepeatCells, other.getColumnRepeatCells()))) &&
            this.maxColumnCount == other.getMaxColumnCount() &&
            this.copyColumnsWhole == other.isCopyColumnsWhole() &&
            this.copyRowsWhole == other.isCopyRowsWhole() &&
            ((this.usedFormatScope==null && other.getUsedFormatScope()==null) || 
             (this.usedFormatScope!=null &&
              this.usedFormatScope.equals(other.getUsedFormatScope()))) &&
            ((this.tableFormat==null && other.getTableFormat()==null) || 
             (this.tableFormat!=null &&
              this.tableFormat.equals(other.getTableFormat()))) &&
            ((this.staticDims==null && other.getStaticDims()==null) || 
             (this.staticDims!=null &&
              java.util.Arrays.equals(this.staticDims, other.getStaticDims())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getSettingType() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getSettingType());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getSettingType(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getTableRepeatDataSource() != null) {
            _hashCode += getTableRepeatDataSource().hashCode();
        }
        if (getColumnRepeatDataSource() != null) {
            _hashCode += getColumnRepeatDataSource().hashCode();
        }
        if (getRowRepeatDataSource() != null) {
            _hashCode += getRowRepeatDataSource().hashCode();
        }
        if (getRowRepeatCells() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getRowRepeatCells());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getRowRepeatCells(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getColumnRepeatCells() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getColumnRepeatCells());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getColumnRepeatCells(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        _hashCode += getMaxColumnCount();
        _hashCode += (isCopyColumnsWhole() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += (isCopyRowsWhole() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        if (getUsedFormatScope() != null) {
            _hashCode += getUsedFormatScope().hashCode();
        }
        if (getTableFormat() != null) {
            _hashCode += getTableFormat().hashCode();
        }
        if (getStaticDims() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getStaticDims());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getStaticDims(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_Table.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_Table"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("settingType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SettingType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TableSettingTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tableRepeatDataSource");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TableRepeatDataSource"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DatasourceTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("columnRepeatDataSource");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ColumnRepeatDataSource"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DatasourceTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rowRepeatDataSource");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RowRepeatDataSource"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DatasourceTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rowRepeatCells");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RowRepeatCells"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "string"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("columnRepeatCells");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ColumnRepeatCells"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "string"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("maxColumnCount");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "MaxColumnCount"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("copyColumnsWhole");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CopyColumnsWhole"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("copyRowsWhole");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CopyRowsWhole"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("usedFormatScope");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "UsedFormatScope"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TableFormatScope"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tableFormat");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TableFormat"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_TableFormat"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("staticDims");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "StaticDims"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "StaticTableDim"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "StaticTableDim"));
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
