package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/1 16:44
 */
public class UnitSyncInfo extends PrintFriendliness {
    /**
     * {
     *  "status": "OK",
     *  "message": "",
     *  "code": null,
     *  "data": [{
     *   "unitId": 12,
     *   "unitDepiction": "<Font Style="FONT-FAMILY: Arial;FONT-SIZE: 10pt;COLOR: #000000;">Percentage</Font>",
     *   "unitShortDepiction": "<Font Style="FONT-FAMILY: Arial;FONT-SIZE: 10pt;COLOR: #000000;">%</Font>",
     *   "status": "Active"
     *  }]
     * }
     */
    /**
     *
     */
    private Integer unitId;
    /**
     *
     */
    private String unitDepiction;
    /**
     *
     */
    private String unitShortDepiction;
    /**
     *
     */
    private String status;

    private List<UnitLanguageSyncInfo> otherLanguageItems;

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitDepiction() {
        return unitDepiction;
    }

    public void setUnitDepiction(String unitDepiction) {
        this.unitDepiction = unitDepiction;
    }

    public String getUnitShortDepiction() {
        return unitShortDepiction;
    }

    public void setUnitShortDepiction(String unitShortDepiction) {
        this.unitShortDepiction = unitShortDepiction;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<UnitLanguageSyncInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<UnitLanguageSyncInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }
}
