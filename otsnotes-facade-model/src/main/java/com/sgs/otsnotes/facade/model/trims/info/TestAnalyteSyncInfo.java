package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/1 16:44
 */
public class TestAnalyteSyncInfo extends PrintFriendliness {
    /**
     * {
     *     "status": "OK",
     *     "message": "",
     *     "code": null,
     *     "data": [{
     *         "testAnalyteId": "1256",
     *         "testAnalyteAttribute": "Cd",
     *         "testAnalyteDesc": "Cadmium(Cd)",
     *         "testAnalyteCasNumber": "7440-43-9",
     *         "status": "Active",
     *         "otherLanguageItems": [{
     *             "multiTestAnalyteDesc": "镉(Cd)",
     *             "multiTestAnalyteAttribute": "镉",
     *             "languageId": 2
     *         }]
     *     }]
     * }
     */
    private Integer testAnalyteId;
    private Integer testAnalyteSeq;
    private String testAnalyteAttribute;
    private String testAnalyteDesc;
    private String testAnalyteCasNumber;
    private String status;
    private List<TestAnalyteLangSyncInfo> otherLanguageItems;

    public Integer getTestAnalyteId() {
        return testAnalyteId;
    }

    public void setTestAnalyteId(Integer testAnalyteId) {
        this.testAnalyteId = testAnalyteId;
    }

    public Integer getTestAnalyteSeq() {
        return testAnalyteSeq;
    }

    public void setTestAnalyteSeq(Integer testAnalyteSeq) {
        this.testAnalyteSeq = testAnalyteSeq;
    }

    public String getTestAnalyteAttribute() {
        return testAnalyteAttribute;
    }

    public void setTestAnalyteAttribute(String testAnalyteAttribute) {
        this.testAnalyteAttribute = testAnalyteAttribute;
    }

    public String getTestAnalyteDesc() {
        return testAnalyteDesc;
    }

    public void setTestAnalyteDesc(String testAnalyteDesc) {
        this.testAnalyteDesc = testAnalyteDesc;
    }

    public String getTestAnalyteCasNumber() {
        return testAnalyteCasNumber;
    }

    public void setTestAnalyteCasNumber(String testAnalyteCasNumber) {
        this.testAnalyteCasNumber = testAnalyteCasNumber;
    }


    public List<TestAnalyteLangSyncInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<TestAnalyteLangSyncInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
