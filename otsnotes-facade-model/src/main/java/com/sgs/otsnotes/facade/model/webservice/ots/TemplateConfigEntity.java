/**
 * TemplateConfigEntity.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class TemplateConfigEntity  implements java.io.Serializable {
    private String version;

    private Element[] elements;

    private JS_Optional[] optionalElements;

    private CellCriteria[] cellConfigurations;

    private TemplateTypeEnum templateType;

    private String templateFileName;

    private String configurationFileName;

    private int modifiedVersion;

    private int modifiedBy;

    private java.util.Calendar modifiedDate;

    private String modifiedSite;

    public TemplateConfigEntity() {
    }

    public TemplateConfigEntity(
           String version,
           Element[] elements,
           JS_Optional[] optionalElements,
           CellCriteria[] cellConfigurations,
           TemplateTypeEnum templateType,
           String templateFileName,
           String configurationFileName,
           int modifiedVersion,
           int modifiedBy,
           java.util.Calendar modifiedDate,
           String modifiedSite) {
           this.version = version;
           this.elements = elements;
           this.optionalElements = optionalElements;
           this.cellConfigurations = cellConfigurations;
           this.templateType = templateType;
           this.templateFileName = templateFileName;
           this.configurationFileName = configurationFileName;
           this.modifiedVersion = modifiedVersion;
           this.modifiedBy = modifiedBy;
           this.modifiedDate = modifiedDate;
           this.modifiedSite = modifiedSite;
    }


    /**
     * Gets the version value for this TemplateConfigEntity.
     * 
     * @return version
     */
    public String getVersion() {
        return version;
    }


    /**
     * Sets the version value for this TemplateConfigEntity.
     * 
     * @param version
     */
    public void setVersion(String version) {
        this.version = version;
    }


    /**
     * Gets the elements value for this TemplateConfigEntity.
     * 
     * @return elements
     */
    public Element[] getElements() {
        return elements;
    }


    /**
     * Sets the elements value for this TemplateConfigEntity.
     * 
     * @param elements
     */
    public void setElements(Element[] elements) {
        this.elements = elements;
    }


    /**
     * Gets the optionalElements value for this TemplateConfigEntity.
     * 
     * @return optionalElements
     */
    public JS_Optional[] getOptionalElements() {
        return optionalElements;
    }


    /**
     * Sets the optionalElements value for this TemplateConfigEntity.
     * 
     * @param optionalElements
     */
    public void setOptionalElements(JS_Optional[] optionalElements) {
        this.optionalElements = optionalElements;
    }


    /**
     * Gets the cellConfigurations value for this TemplateConfigEntity.
     * 
     * @return cellConfigurations
     */
    public CellCriteria[] getCellConfigurations() {
        return cellConfigurations;
    }


    /**
     * Sets the cellConfigurations value for this TemplateConfigEntity.
     * 
     * @param cellConfigurations
     */
    public void setCellConfigurations(CellCriteria[] cellConfigurations) {
        this.cellConfigurations = cellConfigurations;
    }


    /**
     * Gets the templateType value for this TemplateConfigEntity.
     * 
     * @return templateType
     */
    public TemplateTypeEnum getTemplateType() {
        return templateType;
    }


    /**
     * Sets the templateType value for this TemplateConfigEntity.
     * 
     * @param templateType
     */
    public void setTemplateType(TemplateTypeEnum templateType) {
        this.templateType = templateType;
    }


    /**
     * Gets the templateFileName value for this TemplateConfigEntity.
     * 
     * @return templateFileName
     */
    public String getTemplateFileName() {
        return templateFileName;
    }


    /**
     * Sets the templateFileName value for this TemplateConfigEntity.
     * 
     * @param templateFileName
     */
    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }


    /**
     * Gets the configurationFileName value for this TemplateConfigEntity.
     * 
     * @return configurationFileName
     */
    public String getConfigurationFileName() {
        return configurationFileName;
    }


    /**
     * Sets the configurationFileName value for this TemplateConfigEntity.
     * 
     * @param configurationFileName
     */
    public void setConfigurationFileName(String configurationFileName) {
        this.configurationFileName = configurationFileName;
    }


    /**
     * Gets the modifiedVersion value for this TemplateConfigEntity.
     * 
     * @return modifiedVersion
     */
    public int getModifiedVersion() {
        return modifiedVersion;
    }


    /**
     * Sets the modifiedVersion value for this TemplateConfigEntity.
     * 
     * @param modifiedVersion
     */
    public void setModifiedVersion(int modifiedVersion) {
        this.modifiedVersion = modifiedVersion;
    }


    /**
     * Gets the modifiedBy value for this TemplateConfigEntity.
     * 
     * @return modifiedBy
     */
    public int getModifiedBy() {
        return modifiedBy;
    }


    /**
     * Sets the modifiedBy value for this TemplateConfigEntity.
     * 
     * @param modifiedBy
     */
    public void setModifiedBy(int modifiedBy) {
        this.modifiedBy = modifiedBy;
    }


    /**
     * Gets the modifiedDate value for this TemplateConfigEntity.
     * 
     * @return modifiedDate
     */
    public java.util.Calendar getModifiedDate() {
        return modifiedDate;
    }


    /**
     * Sets the modifiedDate value for this TemplateConfigEntity.
     * 
     * @param modifiedDate
     */
    public void setModifiedDate(java.util.Calendar modifiedDate) {
        this.modifiedDate = modifiedDate;
    }


    /**
     * Gets the modifiedSite value for this TemplateConfigEntity.
     * 
     * @return modifiedSite
     */
    public String getModifiedSite() {
        return modifiedSite;
    }


    /**
     * Sets the modifiedSite value for this TemplateConfigEntity.
     * 
     * @param modifiedSite
     */
    public void setModifiedSite(String modifiedSite) {
        this.modifiedSite = modifiedSite;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof TemplateConfigEntity)) {
            return false;
        }
        TemplateConfigEntity other = (TemplateConfigEntity) obj;
        if (obj == null) {
            return false;
        }
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.version==null && other.getVersion()==null) || 
             (this.version!=null &&
              this.version.equals(other.getVersion()))) &&
            ((this.elements==null && other.getElements()==null) || 
             (this.elements!=null &&
              java.util.Arrays.equals(this.elements, other.getElements()))) &&
            ((this.optionalElements==null && other.getOptionalElements()==null) || 
             (this.optionalElements!=null &&
              java.util.Arrays.equals(this.optionalElements, other.getOptionalElements()))) &&
            ((this.cellConfigurations==null && other.getCellConfigurations()==null) || 
             (this.cellConfigurations!=null &&
              java.util.Arrays.equals(this.cellConfigurations, other.getCellConfigurations()))) &&
            ((this.templateType==null && other.getTemplateType()==null) || 
             (this.templateType!=null &&
              this.templateType.equals(other.getTemplateType()))) &&
            ((this.templateFileName==null && other.getTemplateFileName()==null) || 
             (this.templateFileName!=null &&
              this.templateFileName.equals(other.getTemplateFileName()))) &&
            ((this.configurationFileName==null && other.getConfigurationFileName()==null) || 
             (this.configurationFileName!=null &&
              this.configurationFileName.equals(other.getConfigurationFileName()))) &&
            this.modifiedVersion == other.getModifiedVersion() &&
            this.modifiedBy == other.getModifiedBy() &&
            ((this.modifiedDate==null && other.getModifiedDate()==null) || 
             (this.modifiedDate!=null &&
              this.modifiedDate.equals(other.getModifiedDate()))) &&
            ((this.modifiedSite==null && other.getModifiedSite()==null) || 
             (this.modifiedSite!=null &&
              this.modifiedSite.equals(other.getModifiedSite())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getVersion() != null) {
            _hashCode += getVersion().hashCode();
        }
        if (getElements() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getElements());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getElements(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getOptionalElements() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getOptionalElements());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getOptionalElements(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getCellConfigurations() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getCellConfigurations());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getCellConfigurations(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getTemplateType() != null) {
            _hashCode += getTemplateType().hashCode();
        }
        if (getTemplateFileName() != null) {
            _hashCode += getTemplateFileName().hashCode();
        }
        if (getConfigurationFileName() != null) {
            _hashCode += getConfigurationFileName().hashCode();
        }
        _hashCode += getModifiedVersion();
        _hashCode += getModifiedBy();
        if (getModifiedDate() != null) {
            _hashCode += getModifiedDate().hashCode();
        }
        if (getModifiedSite() != null) {
            _hashCode += getModifiedSite().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(TemplateConfigEntity.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateConfigEntity"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("version");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Version"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("elements");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Elements"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "Element"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Element"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("optionalElements");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "OptionalElements"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_Optional"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_Optional"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("cellConfigurations");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CellConfigurations"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "CellCriteria"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CellCriteria"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateFileName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateFileName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("configurationFileName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ConfigurationFileName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("modifiedVersion");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ModifiedVersion"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("modifiedBy");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ModifiedBy"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("modifiedDate");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ModifiedDate"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("modifiedSite");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ModifiedSite"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
