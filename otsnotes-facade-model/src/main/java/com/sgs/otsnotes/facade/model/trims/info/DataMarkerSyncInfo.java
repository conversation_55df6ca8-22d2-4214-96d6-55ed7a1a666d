package com.sgs.otsnotes.facade.model.trims.info;

/**
 *
 */
public final class DataMarkerSyncInfo extends BaseSyncInfo{
    /**
     *
     */
    private long id;

    /**
     * TL ID、PP Number
     */
    private int itemId;

    /**
     * 0：Inactive、1：Active
     */
    private int dataDeleted;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getItemId() {
        return itemId;
    }

    public void setItemId(int itemId) {
        this.itemId = itemId;
    }

    public int getDataDeleted() {
        return dataDeleted;
    }

    public void setDataDeleted(int dataDeleted) {
        this.dataDeleted = dataDeleted;
    }

}
