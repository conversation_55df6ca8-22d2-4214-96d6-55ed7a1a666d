/**
 * JS_Section.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_Section  extends Element implements java.io.Serializable {
    private String lastElementId;

    private TemplateSectionTypeEnum sectionType;

    private boolean isOptioinal;

    private String tooltip;

    private String[] usedBy;

    private int sequence;

    private String testItems;

    private String testReportBodyTemplateId;

    public JS_Section() {
    }

    public JS_Section(
           String id,
           String name,
           String lastElementId,
           TemplateSectionTypeEnum sectionType,
           boolean isOptioinal,
           String tooltip,
           String[] usedBy,
           int sequence,
           String testItems,
           String testReportBodyTemplateId) {
        super(
            id,
            name);
        this.lastElementId = lastElementId;
        this.sectionType = sectionType;
        this.isOptioinal = isOptioinal;
        this.tooltip = tooltip;
        this.usedBy = usedBy;
        this.sequence = sequence;
        this.testItems = testItems;
        this.testReportBodyTemplateId = testReportBodyTemplateId;
    }


    /**
     * Gets the lastElementId value for this JS_Section.
     * 
     * @return lastElementId
     */
    public String getLastElementId() {
        return lastElementId;
    }


    /**
     * Sets the lastElementId value for this JS_Section.
     * 
     * @param lastElementId
     */
    public void setLastElementId(String lastElementId) {
        this.lastElementId = lastElementId;
    }


    /**
     * Gets the sectionType value for this JS_Section.
     * 
     * @return sectionType
     */
    public TemplateSectionTypeEnum getSectionType() {
        return sectionType;
    }


    /**
     * Sets the sectionType value for this JS_Section.
     * 
     * @param sectionType
     */
    public void setSectionType(TemplateSectionTypeEnum sectionType) {
        this.sectionType = sectionType;
    }


    /**
     * Gets the isOptioinal value for this JS_Section.
     * 
     * @return isOptioinal
     */
    public boolean isIsOptioinal() {
        return isOptioinal;
    }


    /**
     * Sets the isOptioinal value for this JS_Section.
     * 
     * @param isOptioinal
     */
    public void setIsOptioinal(boolean isOptioinal) {
        this.isOptioinal = isOptioinal;
    }


    /**
     * Gets the tooltip value for this JS_Section.
     * 
     * @return tooltip
     */
    public String getTooltip() {
        return tooltip;
    }


    /**
     * Sets the tooltip value for this JS_Section.
     * 
     * @param tooltip
     */
    public void setTooltip(String tooltip) {
        this.tooltip = tooltip;
    }


    /**
     * Gets the usedBy value for this JS_Section.
     * 
     * @return usedBy
     */
    public String[] getUsedBy() {
        return usedBy;
    }


    /**
     * Sets the usedBy value for this JS_Section.
     * 
     * @param usedBy
     */
    public void setUsedBy(String[] usedBy) {
        this.usedBy = usedBy;
    }


    /**
     * Gets the sequence value for this JS_Section.
     * 
     * @return sequence
     */
    public int getSequence() {
        return sequence;
    }


    /**
     * Sets the sequence value for this JS_Section.
     * 
     * @param sequence
     */
    public void setSequence(int sequence) {
        this.sequence = sequence;
    }


    /**
     * Gets the testItems value for this JS_Section.
     * 
     * @return testItems
     */
    public String getTestItems() {
        return testItems;
    }


    /**
     * Sets the testItems value for this JS_Section.
     * 
     * @param testItems
     */
    public void setTestItems(String testItems) {
        this.testItems = testItems;
    }


    /**
     * Gets the testReportBodyTemplateId value for this JS_Section.
     * 
     * @return testReportBodyTemplateId
     */
    public String getTestReportBodyTemplateId() {
        return testReportBodyTemplateId;
    }


    /**
     * Sets the testReportBodyTemplateId value for this JS_Section.
     * 
     * @param testReportBodyTemplateId
     */
    public void setTestReportBodyTemplateId(String testReportBodyTemplateId) {
        this.testReportBodyTemplateId = testReportBodyTemplateId;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_Section)) {
            return false;
        }
        JS_Section other = (JS_Section) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.lastElementId==null && other.getLastElementId()==null) || 
             (this.lastElementId!=null &&
              this.lastElementId.equals(other.getLastElementId()))) &&
            ((this.sectionType==null && other.getSectionType()==null) || 
             (this.sectionType!=null &&
              this.sectionType.equals(other.getSectionType()))) &&
            this.isOptioinal == other.isIsOptioinal() &&
            ((this.tooltip==null && other.getTooltip()==null) || 
             (this.tooltip!=null &&
              this.tooltip.equals(other.getTooltip()))) &&
            ((this.usedBy==null && other.getUsedBy()==null) || 
             (this.usedBy!=null &&
              java.util.Arrays.equals(this.usedBy, other.getUsedBy()))) &&
            this.sequence == other.getSequence() &&
            ((this.testItems==null && other.getTestItems()==null) || 
             (this.testItems!=null &&
              this.testItems.equals(other.getTestItems()))) &&
            ((this.testReportBodyTemplateId==null && other.getTestReportBodyTemplateId()==null) || 
             (this.testReportBodyTemplateId!=null &&
              this.testReportBodyTemplateId.equals(other.getTestReportBodyTemplateId())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getLastElementId() != null) {
            _hashCode += getLastElementId().hashCode();
        }
        if (getSectionType() != null) {
            _hashCode += getSectionType().hashCode();
        }
        _hashCode += (isIsOptioinal() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        if (getTooltip() != null) {
            _hashCode += getTooltip().hashCode();
        }
        if (getUsedBy() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getUsedBy());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getUsedBy(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        _hashCode += getSequence();
        if (getTestItems() != null) {
            _hashCode += getTestItems().hashCode();
        }
        if (getTestReportBodyTemplateId() != null) {
            _hashCode += getTestReportBodyTemplateId().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_Section.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_Section"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("lastElementId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "LastElementId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sectionType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SectionType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateSectionTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isOptioinal");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsOptioinal"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tooltip");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Tooltip"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("usedBy");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "UsedBy"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "UsedByEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sequence");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Sequence"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("testItems");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TestItems"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("testReportBodyTemplateId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TestReportBodyTemplateId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
