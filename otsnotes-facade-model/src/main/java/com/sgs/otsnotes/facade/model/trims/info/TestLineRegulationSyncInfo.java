package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class TestLineRegulationSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer regVersionIdentifier;
    /**
     *
     */
    private Integer sectionId;
    /**
     *
     */
    private String sectionName;
    /**
     *
     */
    private String methodAlias;
    /**
     *
     */
    private String ppNotesAlias;
    /**
     *
     */
    private String evaluationAlias;

    public Integer getSectionId() {
        return sectionId;
    }

    public Integer getRegVersionIdentifier() {
        return regVersionIdentifier;
    }

    public void setRegVersionIdentifier(Integer regVersionIdentifier) {
        this.regVersionIdentifier = regVersionIdentifier;
    }

    public void setSectionId(Integer sectionId) {
        this.sectionId = sectionId;
    }

    public String getSectionName() {
        return sectionName;
    }

    public void setSectionName(String sectionName) {
        this.sectionName = sectionName;
    }

    public String getMethodAlias() {
        return methodAlias;
    }

    public void setMethodAlias(String methodAlias) {
        this.methodAlias = methodAlias;
    }

    public String getPpNotesAlias() {
        return ppNotesAlias;
    }

    public void setPpNotesAlias(String ppNotesAlias) {
        this.ppNotesAlias = ppNotesAlias;
    }

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }
}
