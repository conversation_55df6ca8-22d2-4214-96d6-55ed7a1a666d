package com.sgs.otsnotes.facade.model.enums;


import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum SubContractReportRequirement {
    CUSTOMER_REPORT_NULL("0", ""),
     CUSTOMER_REPORT_PDF("1", "PDF"),
     CUSTOMER_REPORT_WORD("2", "WORD"),
     CUSTOMER_REPORT_PDF_WORD("3", "PDF,WORD");

    private String code;
    private String name;
    private SubContractReportRequirement(String code, String name){
        this.code = code;
        this.name= name;
    }
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static SubContractReportRequirement enumOf(String code) {
        for(SubContractReportRequirement type: SubContractReportRequirement.values()) {
            // DIG-8555 Strings and Boxed types should be compared using "equals()".
            if(type.getCode().equals(code)) {
            //if(type.getCode() ==code) {
                return type;
            }
        }
        return null;
    }
    public static final Map<String, SubContractReportRequirement> maps = new HashMap<String, SubContractReportRequirement>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (SubContractReportRequirement enu : SubContractReportRequirement.values()) {
                put(enu.getCode(), enu);
            }
        }
    };
    /**
     *
     * @param status
     * @param subContractReportRequirements
     * @return
     */
    public static boolean check(String status, SubContractReportRequirement... subContractReportRequirements) {
        if (status == null || !maps.containsKey(status) || subContractReportRequirements == null || subContractReportRequirements.length <= 0){
            return false;
        }
        for (SubContractReportRequirement subContractReportRequirement: subContractReportRequirements){
            if (status.equalsIgnoreCase(subContractReportRequirement.getCode())){
                return true;
            }
        }
        return false;
    }
}
