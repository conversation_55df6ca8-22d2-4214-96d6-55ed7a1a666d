package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class RegulationSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private String regulationShortName;
    /**
     *
     */
    private Integer versionIdentifier;
    /**
     *
     */
    private Integer regulationId;
    /**
     *
     */
    private String regulationName;
    /**
     *
     */
    private Integer versionNo;
    /**
     *
     */
    private Integer status;
    /**
     *
     */
    private List<SectionSyncInfo> sectionItems;
    /**
     *
     *//*
    private List<CountrieSyncInfo> countries;
    *//**
     *
     *//*
    private List<StateSyncInfo> states;
    *//**
     *
     *//*
    private List<RegionSyncInfo> regions;*/
    /**
     *
     */
    private List<RegulationLangSyncInfo> otherLanguageItems;

    public String getRegulationShortName() {
        return regulationShortName;
    }

    public void setRegulationShortName(String regulationShortName) {
        this.regulationShortName = regulationShortName;
    }

    public Integer getVersionIdentifier() {
        return versionIdentifier;
    }

    public void setVersionIdentifier(Integer versionIdentifier) {
        this.versionIdentifier = versionIdentifier;
    }

    public Integer getRegulationId() {
        return regulationId;
    }

    public void setRegulationId(Integer regulationId) {
        this.regulationId = regulationId;
    }

    public String getRegulationName() {
        return regulationName;
    }

    public void setRegulationName(String regulationName) {
        this.regulationName = regulationName;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<SectionSyncInfo> getSectionItems() {
        return sectionItems;
    }

    public void setSectionItems(List<SectionSyncInfo> sectionItems) {
        this.sectionItems = sectionItems;
    }

    public List<RegulationLangSyncInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<RegulationLangSyncInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }
}
