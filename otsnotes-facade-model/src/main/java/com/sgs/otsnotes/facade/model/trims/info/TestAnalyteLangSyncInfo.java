package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/1 16:44
 */
public class TestAnalyteLangSyncInfo extends PrintFriendliness {
    /**
     * {
     *         "otherLanguageItems": [{
     *             "multiTestAnalyteDesc": "镉(Cd)",
     *             "multiTestAnalyteAttribute": "镉",
     *             "languageId": 2
     *         }]
     *     }]
     * }
     */
    private Integer languageId;
    private String multiTestAnalyteDesc;
    private String multiTestAnalyteAttribute;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getMultiTestAnalyteDesc() {
        return multiTestAnalyteDesc;
    }

    public void setMultiTestAnalyteDesc(String multiTestAnalyteDesc) {
        this.multiTestAnalyteDesc = multiTestAnalyteDesc;
    }

    public String getMultiTestAnalyteAttribute() {
        return multiTestAnalyteAttribute;
    }

    public void setMultiTestAnalyteAttribute(String multiTestAnalyteAttribute) {
        this.multiTestAnalyteAttribute = multiTestAnalyteAttribute;
    }
}
