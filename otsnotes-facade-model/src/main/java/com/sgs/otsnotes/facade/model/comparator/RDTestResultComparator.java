package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.extsystem.facade.model.reportdata.RdTestResultDTO;

import java.util.Comparator;

public class RDTestResultComparator implements Comparator<RdTestResultDTO> {
    /**
     * 是否为升序
     */
    private boolean isAsc;

    /**
     * @param isAsc
     */
    public RDTestResultComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * matrixId, testResultSeq
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(RdTestResultDTO o1, RdTestResultDTO o2) {
        //
        this.resetNull(o1);
        //
        this.resetNull(o2);

        int index = o1.getTestMatrixId().compareTo(o2.getTestMatrixId());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        index = Integer.compare(o1.getTestResultSeq(), o2.getTestResultSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        return index;
    }

    /**
     * @param td
     */
    private void resetNull(RdTestResultDTO td) {
        final Integer empty = 0;
        if (td.getTestMatrixId() == null) {
            td.setTestMatrixId("");
        }
        if (td.getTestResultSeq() == null) {
            td.setTestResultSeq(0);
        }


    }
}
