/**
 * GenerateConfirmation.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GenerateConfirmation  implements java.io.Serializable {
    private String confirmationId;

    private String confirmationTemplateFilePath;

    private String confirmationFilePath;

    private String testReportTemplateFilePath;

    private String testReportTemplateConfigFilePath;

    private ReportDatasource datasource;

    public GenerateConfirmation() {
    }

    public GenerateConfirmation(
           String confirmationId,
           String confirmationTemplateFilePath,
           String confirmationFilePath,
           String testReportTemplateFilePath,
           String testReportTemplateConfigFilePath,
           ReportDatasource datasource) {
           this.confirmationId = confirmationId;
           this.confirmationTemplateFilePath = confirmationTemplateFilePath;
           this.confirmationFilePath = confirmationFilePath;
           this.testReportTemplateFilePath = testReportTemplateFilePath;
           this.testReportTemplateConfigFilePath = testReportTemplateConfigFilePath;
           this.datasource = datasource;
    }


    /**
     * Gets the confirmationId value for this GenerateConfirmation.
     * 
     * @return confirmationId
     */
    public String getConfirmationId() {
        return confirmationId;
    }


    /**
     * Sets the confirmationId value for this GenerateConfirmation.
     * 
     * @param confirmationId
     */
    public void setConfirmationId(String confirmationId) {
        this.confirmationId = confirmationId;
    }


    /**
     * Gets the confirmationTemplateFilePath value for this GenerateConfirmation.
     * 
     * @return confirmationTemplateFilePath
     */
    public String getConfirmationTemplateFilePath() {
        return confirmationTemplateFilePath;
    }


    /**
     * Sets the confirmationTemplateFilePath value for this GenerateConfirmation.
     * 
     * @param confirmationTemplateFilePath
     */
    public void setConfirmationTemplateFilePath(String confirmationTemplateFilePath) {
        this.confirmationTemplateFilePath = confirmationTemplateFilePath;
    }


    /**
     * Gets the confirmationFilePath value for this GenerateConfirmation.
     * 
     * @return confirmationFilePath
     */
    public String getConfirmationFilePath() {
        return confirmationFilePath;
    }


    /**
     * Sets the confirmationFilePath value for this GenerateConfirmation.
     * 
     * @param confirmationFilePath
     */
    public void setConfirmationFilePath(String confirmationFilePath) {
        this.confirmationFilePath = confirmationFilePath;
    }


    /**
     * Gets the testReportTemplateFilePath value for this GenerateConfirmation.
     * 
     * @return testReportTemplateFilePath
     */
    public String getTestReportTemplateFilePath() {
        return testReportTemplateFilePath;
    }


    /**
     * Sets the testReportTemplateFilePath value for this GenerateConfirmation.
     * 
     * @param testReportTemplateFilePath
     */
    public void setTestReportTemplateFilePath(String testReportTemplateFilePath) {
        this.testReportTemplateFilePath = testReportTemplateFilePath;
    }


    /**
     * Gets the testReportTemplateConfigFilePath value for this GenerateConfirmation.
     * 
     * @return testReportTemplateConfigFilePath
     */
    public String getTestReportTemplateConfigFilePath() {
        return testReportTemplateConfigFilePath;
    }


    /**
     * Sets the testReportTemplateConfigFilePath value for this GenerateConfirmation.
     * 
     * @param testReportTemplateConfigFilePath
     */
    public void setTestReportTemplateConfigFilePath(String testReportTemplateConfigFilePath) {
        this.testReportTemplateConfigFilePath = testReportTemplateConfigFilePath;
    }


    /**
     * Gets the datasource value for this GenerateConfirmation.
     * 
     * @return datasource
     */
    public ReportDatasource getDatasource() {
        return datasource;
    }


    /**
     * Sets the datasource value for this GenerateConfirmation.
     * 
     * @param datasource
     */
    public void setDatasource(ReportDatasource datasource) {
        this.datasource = datasource;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GenerateConfirmation)) {
            return false;
        }
        GenerateConfirmation other = (GenerateConfirmation) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.confirmationId==null && other.getConfirmationId()==null) || 
             (this.confirmationId!=null &&
              this.confirmationId.equals(other.getConfirmationId()))) &&
            ((this.confirmationTemplateFilePath==null && other.getConfirmationTemplateFilePath()==null) || 
             (this.confirmationTemplateFilePath!=null &&
              this.confirmationTemplateFilePath.equals(other.getConfirmationTemplateFilePath()))) &&
            ((this.confirmationFilePath==null && other.getConfirmationFilePath()==null) || 
             (this.confirmationFilePath!=null &&
              this.confirmationFilePath.equals(other.getConfirmationFilePath()))) &&
            ((this.testReportTemplateFilePath==null && other.getTestReportTemplateFilePath()==null) || 
             (this.testReportTemplateFilePath!=null &&
              this.testReportTemplateFilePath.equals(other.getTestReportTemplateFilePath()))) &&
            ((this.testReportTemplateConfigFilePath==null && other.getTestReportTemplateConfigFilePath()==null) || 
             (this.testReportTemplateConfigFilePath!=null &&
              this.testReportTemplateConfigFilePath.equals(other.getTestReportTemplateConfigFilePath()))) &&
            ((this.datasource==null && other.getDatasource()==null) || 
             (this.datasource!=null &&
              this.datasource.equals(other.getDatasource())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getConfirmationId() != null) {
            _hashCode += getConfirmationId().hashCode();
        }
        if (getConfirmationTemplateFilePath() != null) {
            _hashCode += getConfirmationTemplateFilePath().hashCode();
        }
        if (getConfirmationFilePath() != null) {
            _hashCode += getConfirmationFilePath().hashCode();
        }
        if (getTestReportTemplateFilePath() != null) {
            _hashCode += getTestReportTemplateFilePath().hashCode();
        }
        if (getTestReportTemplateConfigFilePath() != null) {
            _hashCode += getTestReportTemplateConfigFilePath().hashCode();
        }
        if (getDatasource() != null) {
            _hashCode += getDatasource().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GenerateConfirmation.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GenerateConfirmation"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("confirmationId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "confirmationId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("confirmationTemplateFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "confirmationTemplateFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("confirmationFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "confirmationFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("testReportTemplateFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "testReportTemplateFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("testReportTemplateConfigFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "testReportTemplateConfigFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("datasource");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "datasource"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ReportDatasource"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
