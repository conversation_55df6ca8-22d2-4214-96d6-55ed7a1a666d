package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class PPTLLabSectionSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer labId;
    /**
     *
     */
    private Integer labSectionId;
    /**
     *
     */
    private Long id;

    /**
     *
     */
    private Long aid;
    /**
     *
     */
    private String status;

    public Integer getLabId() {
        return labId;
    }

    public void setLabId(Integer labId) {
        this.labId = labId;
    }

    public Integer getLabSectionId() {
        return labSectionId;
    }

    public void setLabSectionId(Integer labSectionId) {
        this.labSectionId = labSectionId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAid() {
        return aid;
    }

    public void setAid(Long aid) {
        this.aid = aid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
