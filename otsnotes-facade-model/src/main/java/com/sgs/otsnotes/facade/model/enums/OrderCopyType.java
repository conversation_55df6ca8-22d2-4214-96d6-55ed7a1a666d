package com.sgs.otsnotes.facade.model.enums;

import com.sgs.otsnotes.facade.model.common.CustomResult;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

/**
 * import com.sgs.framework.model.enums.OrderCopyType;
 */
@Deprecated
@Dict
public enum OrderCopyType {
    None(0,0,"None"),
    NewCopy((1 << 0),10,"New by Copy"),

    CopySection((1 << 1),20,"Customer、CoverPage、OriginalSample、TestLine、Service Requirement"),
    CCOTS((1 << 2),21,"Customer、CoverPage、OriginalSample、TestLine、Service Requirement"),
    CCOS((1 << 3),22,"Customer、CoverPage、OriginalSample、Service Requirement"),
    CoverPage((1 << 4),23,"CoverPage"),

    AmendReport((1 << 5),30,"AmendReport"),
    Supplement((1 << 6),31,"AmendReport Supplement"),
    Extract((1 << 7),32,"AmendReport Extract"),
    Replace((1 << 8),33,"AmendReport Replace"),
    TranslationReport((1 << 9),34,"Translation Report"),

    SplitReport((1 << 10),40,"Split Report"),
    Sample((1 << 11),41,1,"By Sample"),
    Conclusion((1 << 12),42,2,"By Conclusion"),
    TestLine((1 << 13),43,3,"By TestLine"),

    SubContract((1 << 14),50,"Sub Contract"),
    SubContractSync((1 << 15),51,"Sub Contract Sync"),
    ConditionSync((1 << 16), 52, "Sub Contract Sync Condition"),
    NewSubContract((1 << 17), 53, "New Sub Contract"),
    NewSubSync((1 << 18), 54, "New Sub Contract Sync"),
    NewSubCompleteSync((1 << 19), 55, "New Sub Contract Complete Sync"),

    CopyReport((1 << 20),60,"Copy Report"),
    ;

    private final int copyType;
    private int subCopyType = 0;
    private final int bitType;
    private final String message;

    OrderCopyType(int bitType, int copyType, String message) {
        this.bitType = bitType;
        this.copyType = copyType;
        this.message = message;
    }

    OrderCopyType(int bitType, int copyType, int subCopyType, String message) {
        this(bitType, copyType, message);
        this.subCopyType = subCopyType;
    }

    public int getCopyType() {
        return copyType;
    }

    public int getSubCopyType() {
        return subCopyType;
    }

    public int getBitType() {
        return bitType;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, OrderCopyType> subCopyMaps = new HashMap<Integer, OrderCopyType>();
    public static final Map<Integer, OrderCopyType> maps = new HashMap<Integer, OrderCopyType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (OrderCopyType copyType : OrderCopyType.values()) {
                put(copyType.getCopyType(), copyType);
                if (copyType.getSubCopyType() <= 0){
                    continue;
                }
                subCopyMaps.put(copyType.getSubCopyType(), copyType);
            }
        }
    };

    public static OrderCopyType findCopyType(Integer copyType) {
        if (copyType == null || !maps.containsKey(copyType.intValue())) {
            return null;
        }
        return maps.get(copyType.intValue());
    }

    public static OrderCopyType findSubCopyType(Integer subCopyType) {
        if (subCopyType == null || !subCopyMaps.containsKey(subCopyType.intValue())) {
            return null;
        }
        return subCopyMaps.get(subCopyType.intValue());
    }

    /**
     *
     * @param copyType
     * @param orderCopyType
     * @return
     */
    public static boolean check(Integer copyType, OrderCopyType orderCopyType) {
        if (copyType == null || !maps.containsKey(copyType.intValue()) || orderCopyType == null){
            return false;
        }
        return maps.get(copyType.intValue()) == orderCopyType;
    }

    /**
     *
     * @param future
     * @param <T>
     * @return
     */
    public <T> CustomResult execute(T future) {
        CustomResult result = new CustomResult();
        result.setSuccess(true);
        result.setMsg(this.getMessage());
        return result;
    }

    /**
     *
     * @param copyTypes
     * @return
     */
    public boolean check(OrderCopyType... copyTypes){
        if (copyTypes == null || copyTypes.length <= 0){
            return false;
        }
        for (OrderCopyType copyType: copyTypes){
            if ((this.getBitType() & copyType.getBitType()) > 0){
                return true;
            }
        }
        return false;
    }
}
