package com.sgs.otsnotes.facade.model.localize;

/**
 * 可本地化的Condition
 * <AUTHOR>
 * @date 2020/12/29 17:44
 */
public interface ConditionLocalizable {

    /**
     * 主键
     * @return ConditionBaseId
     */
    Long getConditionBaseId();

    /**
     * ConditionTypeName
     * @param conditionName 名称
     */
    void setTestConditionName(String testConditionName);

    /**
     * 获取ConditionTypeName名称
     * @return 返回名称
     */
    String getTestConditionName();

    /**
     * ConditionTypeName
     * @param conditionNameDesc 名称
     */
    void setTestConditionDesc(String testConditionName);

    /**
     * 获取ConditionTypeNameDesc名称
     * @return 返回名称
     */
    String getTestConditionDesc();
}
