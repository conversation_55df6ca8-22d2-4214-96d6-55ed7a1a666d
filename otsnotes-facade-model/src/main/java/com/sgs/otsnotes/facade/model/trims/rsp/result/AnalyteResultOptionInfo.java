package com.sgs.otsnotes.facade.model.trims.rsp.result;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public final class AnalyteResultOptionInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer optionId;

    /**
     *
     */
    private String optionValue;

    public Integer getOptionId() {
        return optionId;
    }

    public void setOptionId(Integer optionId) {
        this.optionId = optionId;
    }

    public String getOptionValue() {
        return optionValue;
    }

    public void setOptionValue(String optionValue) {
        this.optionValue = optionValue;
    }
}
