package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class CustomTestCategorySyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer customTestCategoryId;
    /**
     *
     */
    private String customTestCategoryName;
    /**
     *
     */
    private String customTestCategoryType;
    /**
     *
     */
    private Integer customAccountId;
    /**
     *
     */
    private String status;
    /**
     *
     */
    private List<Integer> testItems;
    /**
     *
     */
    private List<CustomTestCategoryLangSyncInfo> otherLanguageItems;

    private Integer productLineId;

    public Integer getCustomTestCategoryId() {
        return customTestCategoryId;
    }

    public void setCustomTestCategoryId(Integer customTestCategoryId) {
        this.customTestCategoryId = customTestCategoryId;
    }

    public String getCustomTestCategoryName() {
        return customTestCategoryName;
    }

    public void setCustomTestCategoryName(String customTestCategoryName) {
        this.customTestCategoryName = customTestCategoryName;
    }

    public String getCustomTestCategoryType() {
        return customTestCategoryType;
    }

    public void setCustomTestCategoryType(String customTestCategoryType) {
        this.customTestCategoryType = customTestCategoryType;
    }

    public Integer getCustomAccountId() {
        return customAccountId;
    }

    public void setCustomAccountId(Integer customAccountId) {
        this.customAccountId = customAccountId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<Integer> getTestItems() {
        return testItems;
    }

    public void setTestItems(List<Integer> testItems) {
        this.testItems = testItems;
    }

    public List<CustomTestCategoryLangSyncInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<CustomTestCategoryLangSyncInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }

    public Integer getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }
}
