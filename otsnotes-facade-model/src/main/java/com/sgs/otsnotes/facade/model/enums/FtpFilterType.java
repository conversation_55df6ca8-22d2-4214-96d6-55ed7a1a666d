package com.sgs.otsnotes.facade.model.enums;

import java.util.Arrays;
import java.util.List;

public enum FtpFilterType {
    XML(1, Arrays.asList(".XML")),
    RTF_DOC(2, Arrays.asList(".RTF",".DOC"));

    private final int code;
    private final List<String> format;

    FtpFilterType(int code, List<String> format) {
        this.code = code;

        this.format = format;
    }

    public int getCode() {
        return code;
    }

    public List<String> format() {
        return this.format;
    }

    public static FtpFilterType getCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FtpFilterType fileType : FtpFilterType.values()) {
            if (code.equals(fileType.getCode())) {
                return fileType;
            }
        }
        return null;
    }
}
