/**
 * FieldBindInfo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class FieldBindInfo  implements java.io.Serializable {
    private TemplateTypeEnum originalTemplateType;

    private int PGT;

    private int PG;

    private String instanceId;

    private String subInstanceId;

    private String parentCellCriteriaId;

    private boolean isBinded;

    public FieldBindInfo() {
    }

    public FieldBindInfo(
           TemplateTypeEnum originalTemplateType,
           int PGT,
           int PG,
           String instanceId,
           String subInstanceId,
           String parentCellCriteriaId,
           boolean isBinded) {
           this.originalTemplateType = originalTemplateType;
           this.PGT = PGT;
           this.PG = PG;
           this.instanceId = instanceId;
           this.subInstanceId = subInstanceId;
           this.parentCellCriteriaId = parentCellCriteriaId;
           this.isBinded = isBinded;
    }


    /**
     * Gets the originalTemplateType value for this FieldBindInfo.
     * 
     * @return originalTemplateType
     */
    public TemplateTypeEnum getOriginalTemplateType() {
        return originalTemplateType;
    }


    /**
     * Sets the originalTemplateType value for this FieldBindInfo.
     * 
     * @param originalTemplateType
     */
    public void setOriginalTemplateType(TemplateTypeEnum originalTemplateType) {
        this.originalTemplateType = originalTemplateType;
    }


    /**
     * Gets the PGT value for this FieldBindInfo.
     * 
     * @return PGT
     */
    public int getPGT() {
        return PGT;
    }


    /**
     * Sets the PGT value for this FieldBindInfo.
     * 
     * @param PGT
     */
    public void setPGT(int PGT) {
        this.PGT = PGT;
    }


    /**
     * Gets the PG value for this FieldBindInfo.
     * 
     * @return PG
     */
    public int getPG() {
        return PG;
    }


    /**
     * Sets the PG value for this FieldBindInfo.
     * 
     * @param PG
     */
    public void setPG(int PG) {
        this.PG = PG;
    }


    /**
     * Gets the instanceId value for this FieldBindInfo.
     * 
     * @return instanceId
     */
    public String getInstanceId() {
        return instanceId;
    }


    /**
     * Sets the instanceId value for this FieldBindInfo.
     * 
     * @param instanceId
     */
    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }


    /**
     * Gets the subInstanceId value for this FieldBindInfo.
     * 
     * @return subInstanceId
     */
    public String getSubInstanceId() {
        return subInstanceId;
    }


    /**
     * Sets the subInstanceId value for this FieldBindInfo.
     * 
     * @param subInstanceId
     */
    public void setSubInstanceId(String subInstanceId) {
        this.subInstanceId = subInstanceId;
    }


    /**
     * Gets the parentCellCriteriaId value for this FieldBindInfo.
     * 
     * @return parentCellCriteriaId
     */
    public String getParentCellCriteriaId() {
        return parentCellCriteriaId;
    }


    /**
     * Sets the parentCellCriteriaId value for this FieldBindInfo.
     * 
     * @param parentCellCriteriaId
     */
    public void setParentCellCriteriaId(String parentCellCriteriaId) {
        this.parentCellCriteriaId = parentCellCriteriaId;
    }


    /**
     * Gets the isBinded value for this FieldBindInfo.
     * 
     * @return isBinded
     */
    public boolean isIsBinded() {
        return isBinded;
    }


    /**
     * Sets the isBinded value for this FieldBindInfo.
     * 
     * @param isBinded
     */
    public void setIsBinded(boolean isBinded) {
        this.isBinded = isBinded;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof FieldBindInfo)) {
            return false;
        }
        FieldBindInfo other = (FieldBindInfo) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.originalTemplateType==null && other.getOriginalTemplateType()==null) || 
             (this.originalTemplateType!=null &&
              this.originalTemplateType.equals(other.getOriginalTemplateType()))) &&
            this.PGT == other.getPGT() &&
            this.PG == other.getPG() &&
            ((this.instanceId==null && other.getInstanceId()==null) || 
             (this.instanceId!=null &&
              this.instanceId.equals(other.getInstanceId()))) &&
            ((this.subInstanceId==null && other.getSubInstanceId()==null) || 
             (this.subInstanceId!=null &&
              this.subInstanceId.equals(other.getSubInstanceId()))) &&
            ((this.parentCellCriteriaId==null && other.getParentCellCriteriaId()==null) || 
             (this.parentCellCriteriaId!=null &&
              this.parentCellCriteriaId.equals(other.getParentCellCriteriaId()))) &&
            this.isBinded == other.isIsBinded();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getOriginalTemplateType() != null) {
            _hashCode += getOriginalTemplateType().hashCode();
        }
        _hashCode += getPGT();
        _hashCode += getPG();
        if (getInstanceId() != null) {
            _hashCode += getInstanceId().hashCode();
        }
        if (getSubInstanceId() != null) {
            _hashCode += getSubInstanceId().hashCode();
        }
        if (getParentCellCriteriaId() != null) {
            _hashCode += getParentCellCriteriaId().hashCode();
        }
        _hashCode += (isIsBinded() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(FieldBindInfo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FieldBindInfo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("originalTemplateType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "OriginalTemplateType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PGT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PGT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("instanceId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "InstanceId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("subInstanceId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubInstanceId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("parentCellCriteriaId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ParentCellCriteriaId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isBinded");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsBinded"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
