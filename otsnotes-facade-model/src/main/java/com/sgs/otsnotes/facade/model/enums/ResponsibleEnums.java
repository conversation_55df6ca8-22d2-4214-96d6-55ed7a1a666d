package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import org.apache.axis.utils.StringUtils;

/**
 * <AUTHOR>
 * 枚举code 是从接口抓取后 处理生成的，目前用到的只有几个值
 */

@Dict
public enum ResponsibleEnums {

    USA("USA"),
    EUR("EUR"),
    AS("AS"),
    GB("GB"),
    PPE("PPE"),
    FW("FW"),
    BOKEN("BOKEN"),
    EU1("EU1"),
    EU2("EU2"),
    US("US"),
    CRM("CRM"),
    GB1("GB1"),
    FW1("FW1"),
    FW2("FW2"),
    GBFW("GBFW"),
    GBZG("GBZG"),
    GBFW1("GBFW1"),
    OR("OR"),
    HK("HK"),
    JIS("JIS"),
    WKL("WKL"),
    TX("TX"),
    <PERSON>_H_M("H-H&M"),
    SUSTAINABILITY("SUSTAINABILITY"),
    SCM("SCM"),
    H_INDITEX("H-INDITEX"),
    G_C_A("G-C&A"),
    G_EU("G-EU"),
    G_JIS("G-JIS"),
    G_USA("G-USA"),
    P_AUS("P-AUS"),
    P_EUR1("P-EUR1"),
    P_EUR2("P-EUR2"),
    P_USA1("P-USA1"),
    P_USA2("P-USA2"),
    P_WKL("P-WKL"),
    SL_Subcontract("SL Subcontract"),
    GB_SHLab("GB SHLab"),
    GB_Subcontract("GB Subcontract"),
    GB_Convert("GB Convert"),
    FW_Subcontract("FW Subcontract"),
    CRM_WKL("CRM-WKL"),
    CRM_AS("CRM-AS"),
    CRM_EU("CRM-EU"),
    CRM_US("CRM-US"),
    FW_US("FW-US"),
    FW_EU("FW-EU"),
    JP("JP"),
    FW_AS("FW-AS"),
    FW_WKL("FW-WKL"),
    COSTCO("COSTCO"),
    Functional("Functional"),
    Internal_Subcontract("Internal Subcontract"),
    Correlation("Correlation"),
    US1("US1"),
    US2("US2"),
    US3("US3"),
    US4("US4"),
    EU3("EU3"),
    EU4("EU4"),
    BK("BK");

    @DictCodeField
    private String code;

    ResponsibleEnums(String code) {
        this.code = code;
    }

    public static boolean check(String code, ResponsibleEnums... enums) {
        if (StringUtils.isEmpty(code ) || enums == null || enums.length==0) {
            return false;
        }
        for (ResponsibleEnums anEnum : enums) {
            if (anEnum.code.equalsIgnoreCase(code)) {
                return true;
            }
        }
        return false;
    }

}
