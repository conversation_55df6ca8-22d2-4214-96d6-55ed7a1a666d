/**
 * JS_FormField.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_FormField  extends Element implements java.io.Serializable {
    private FormFieldTypeEnum formFieldType;

    private String radioGroupName;

    private String defaultValue;

    private TemplateFormFieldDataType dataType;

    private String format;

    private String tooltip;

    private boolean isRequired;

    private boolean isReadOnly;

    private int variableId;

    private String variableName;

    private String staticLinkValue;

    private String remark;

    private int sequenceNo;

    private String separator;

    private String requiredGroup;

    public JS_FormField() {
    }

    public JS_FormField(
           String id,
           String name,
           FormFieldTypeEnum formFieldType,
           String radioGroupName,
           String defaultValue,
           TemplateFormFieldDataType dataType,
           String format,
           String tooltip,
           boolean isRequired,
           boolean isReadOnly,
           int variableId,
           String variableName,
           String staticLinkValue,
           String remark,
           int sequenceNo,
           String separator,
           String requiredGroup) {
        super(
            id,
            name);
        this.formFieldType = formFieldType;
        this.radioGroupName = radioGroupName;
        this.defaultValue = defaultValue;
        this.dataType = dataType;
        this.format = format;
        this.tooltip = tooltip;
        this.isRequired = isRequired;
        this.isReadOnly = isReadOnly;
        this.variableId = variableId;
        this.variableName = variableName;
        this.staticLinkValue = staticLinkValue;
        this.remark = remark;
        this.sequenceNo = sequenceNo;
        this.separator = separator;
        this.requiredGroup = requiredGroup;
    }


    /**
     * Gets the formFieldType value for this JS_FormField.
     * 
     * @return formFieldType
     */
    public FormFieldTypeEnum getFormFieldType() {
        return formFieldType;
    }


    /**
     * Sets the formFieldType value for this JS_FormField.
     * 
     * @param formFieldType
     */
    public void setFormFieldType(FormFieldTypeEnum formFieldType) {
        this.formFieldType = formFieldType;
    }


    /**
     * Gets the radioGroupName value for this JS_FormField.
     * 
     * @return radioGroupName
     */
    public String getRadioGroupName() {
        return radioGroupName;
    }


    /**
     * Sets the radioGroupName value for this JS_FormField.
     * 
     * @param radioGroupName
     */
    public void setRadioGroupName(String radioGroupName) {
        this.radioGroupName = radioGroupName;
    }


    /**
     * Gets the defaultValue value for this JS_FormField.
     * 
     * @return defaultValue
     */
    public String getDefaultValue() {
        return defaultValue;
    }


    /**
     * Sets the defaultValue value for this JS_FormField.
     * 
     * @param defaultValue
     */
    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }


    /**
     * Gets the dataType value for this JS_FormField.
     * 
     * @return dataType
     */
    public TemplateFormFieldDataType getDataType() {
        return dataType;
    }


    /**
     * Sets the dataType value for this JS_FormField.
     * 
     * @param dataType
     */
    public void setDataType(TemplateFormFieldDataType dataType) {
        this.dataType = dataType;
    }


    /**
     * Gets the format value for this JS_FormField.
     * 
     * @return format
     */
    public String getFormat() {
        return format;
    }


    /**
     * Sets the format value for this JS_FormField.
     * 
     * @param format
     */
    public void setFormat(String format) {
        this.format = format;
    }


    /**
     * Gets the tooltip value for this JS_FormField.
     * 
     * @return tooltip
     */
    public String getTooltip() {
        return tooltip;
    }


    /**
     * Sets the tooltip value for this JS_FormField.
     * 
     * @param tooltip
     */
    public void setTooltip(String tooltip) {
        this.tooltip = tooltip;
    }


    /**
     * Gets the isRequired value for this JS_FormField.
     * 
     * @return isRequired
     */
    public boolean isIsRequired() {
        return isRequired;
    }


    /**
     * Sets the isRequired value for this JS_FormField.
     * 
     * @param isRequired
     */
    public void setIsRequired(boolean isRequired) {
        this.isRequired = isRequired;
    }


    /**
     * Gets the isReadOnly value for this JS_FormField.
     * 
     * @return isReadOnly
     */
    public boolean isIsReadOnly() {
        return isReadOnly;
    }


    /**
     * Sets the isReadOnly value for this JS_FormField.
     * 
     * @param isReadOnly
     */
    public void setIsReadOnly(boolean isReadOnly) {
        this.isReadOnly = isReadOnly;
    }


    /**
     * Gets the variableId value for this JS_FormField.
     * 
     * @return variableId
     */
    public int getVariableId() {
        return variableId;
    }


    /**
     * Sets the variableId value for this JS_FormField.
     * 
     * @param variableId
     */
    public void setVariableId(int variableId) {
        this.variableId = variableId;
    }


    /**
     * Gets the variableName value for this JS_FormField.
     * 
     * @return variableName
     */
    public String getVariableName() {
        return variableName;
    }


    /**
     * Sets the variableName value for this JS_FormField.
     * 
     * @param variableName
     */
    public void setVariableName(String variableName) {
        this.variableName = variableName;
    }


    /**
     * Gets the staticLinkValue value for this JS_FormField.
     * 
     * @return staticLinkValue
     */
    public String getStaticLinkValue() {
        return staticLinkValue;
    }


    /**
     * Sets the staticLinkValue value for this JS_FormField.
     * 
     * @param staticLinkValue
     */
    public void setStaticLinkValue(String staticLinkValue) {
        this.staticLinkValue = staticLinkValue;
    }


    /**
     * Gets the remark value for this JS_FormField.
     * 
     * @return remark
     */
    public String getRemark() {
        return remark;
    }


    /**
     * Sets the remark value for this JS_FormField.
     * 
     * @param remark
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }


    /**
     * Gets the sequenceNo value for this JS_FormField.
     * 
     * @return sequenceNo
     */
    public int getSequenceNo() {
        return sequenceNo;
    }


    /**
     * Sets the sequenceNo value for this JS_FormField.
     * 
     * @param sequenceNo
     */
    public void setSequenceNo(int sequenceNo) {
        this.sequenceNo = sequenceNo;
    }


    /**
     * Gets the separator value for this JS_FormField.
     * 
     * @return separator
     */
    public String getSeparator() {
        return separator;
    }


    /**
     * Sets the separator value for this JS_FormField.
     * 
     * @param separator
     */
    public void setSeparator(String separator) {
        this.separator = separator;
    }


    /**
     * Gets the requiredGroup value for this JS_FormField.
     * 
     * @return requiredGroup
     */
    public String getRequiredGroup() {
        return requiredGroup;
    }


    /**
     * Sets the requiredGroup value for this JS_FormField.
     * 
     * @param requiredGroup
     */
    public void setRequiredGroup(String requiredGroup) {
        this.requiredGroup = requiredGroup;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_FormField)) {
            return false;
        }
        JS_FormField other = (JS_FormField) obj;
        if (obj == null) {
            return false;
        }
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.formFieldType==null && other.getFormFieldType()==null) || 
             (this.formFieldType!=null &&
              this.formFieldType.equals(other.getFormFieldType()))) &&
            ((this.radioGroupName==null && other.getRadioGroupName()==null) || 
             (this.radioGroupName!=null &&
              this.radioGroupName.equals(other.getRadioGroupName()))) &&
            ((this.defaultValue==null && other.getDefaultValue()==null) || 
             (this.defaultValue!=null &&
              this.defaultValue.equals(other.getDefaultValue()))) &&
            ((this.dataType==null && other.getDataType()==null) || 
             (this.dataType!=null &&
              this.dataType.equals(other.getDataType()))) &&
            ((this.format==null && other.getFormat()==null) || 
             (this.format!=null &&
              this.format.equals(other.getFormat()))) &&
            ((this.tooltip==null && other.getTooltip()==null) || 
             (this.tooltip!=null &&
              this.tooltip.equals(other.getTooltip()))) &&
            this.isRequired == other.isIsRequired() &&
            this.isReadOnly == other.isIsReadOnly() &&
            this.variableId == other.getVariableId() &&
            ((this.variableName==null && other.getVariableName()==null) || 
             (this.variableName!=null &&
              this.variableName.equals(other.getVariableName()))) &&
            ((this.staticLinkValue==null && other.getStaticLinkValue()==null) || 
             (this.staticLinkValue!=null &&
              this.staticLinkValue.equals(other.getStaticLinkValue()))) &&
            ((this.remark==null && other.getRemark()==null) || 
             (this.remark!=null &&
              this.remark.equals(other.getRemark()))) &&
            this.sequenceNo == other.getSequenceNo() &&
            ((this.separator==null && other.getSeparator()==null) || 
             (this.separator!=null &&
              this.separator.equals(other.getSeparator()))) &&
            ((this.requiredGroup==null && other.getRequiredGroup()==null) || 
             (this.requiredGroup!=null &&
              this.requiredGroup.equals(other.getRequiredGroup())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getFormFieldType() != null) {
            _hashCode += getFormFieldType().hashCode();
        }
        if (getRadioGroupName() != null) {
            _hashCode += getRadioGroupName().hashCode();
        }
        if (getDefaultValue() != null) {
            _hashCode += getDefaultValue().hashCode();
        }
        if (getDataType() != null) {
            _hashCode += getDataType().hashCode();
        }
        if (getFormat() != null) {
            _hashCode += getFormat().hashCode();
        }
        if (getTooltip() != null) {
            _hashCode += getTooltip().hashCode();
        }
        _hashCode += (isIsRequired() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += (isIsReadOnly() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += getVariableId();
        if (getVariableName() != null) {
            _hashCode += getVariableName().hashCode();
        }
        if (getStaticLinkValue() != null) {
            _hashCode += getStaticLinkValue().hashCode();
        }
        if (getRemark() != null) {
            _hashCode += getRemark().hashCode();
        }
        _hashCode += getSequenceNo();
        if (getSeparator() != null) {
            _hashCode += getSeparator().hashCode();
        }
        if (getRequiredGroup() != null) {
            _hashCode += getRequiredGroup().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_FormField.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_FormField"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("formFieldType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FormFieldType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FormFieldTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("radioGroupName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RadioGroupName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("defaultValue");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DefaultValue"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("dataType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateFormFieldDataType"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("format");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Format"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tooltip");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Tooltip"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isRequired");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsRequired"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isReadOnly");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsReadOnly"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("variableId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "VariableId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("variableName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "VariableName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("staticLinkValue");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "StaticLinkValue"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("remark");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Remark"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sequenceNo");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SequenceNo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("separator");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Separator"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("requiredGroup");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RequiredGroup"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
