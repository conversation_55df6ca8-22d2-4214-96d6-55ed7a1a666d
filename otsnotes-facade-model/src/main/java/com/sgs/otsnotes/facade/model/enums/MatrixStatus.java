package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum MatrixStatus {
    Typing(701, "Typing"),
    Submit(702, "Submitted"),
    Completed(703, "Completed"),
    SubContracted(704, "Subcontracted"),
    Entered(705, "Entered"),
    Cancelled(706, "Cancelled"),
    NC(707, "Not Test"),// Not Test、NC
    DR(708, "Document Review");
    @DictCodeField
    private int status;
    @DictLabelField
    private String message;

    MatrixStatus(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public int getStatus() {
        return status;
    }

    public static final Map<Integer, MatrixStatus> maps = new HashMap<Integer, MatrixStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (MatrixStatus enu : MatrixStatus.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue()).getMessage();
    }

    public static MatrixStatus findStatus(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }

    /**
     * @param status
     * @param matrixStatuses
     * @return
     */
    public static boolean check(Integer status, MatrixStatus... matrixStatuses) {
        if (status == null || !maps.containsKey(status.intValue()) || matrixStatuses == null || matrixStatuses.length <= 0) {
            return false;
        }
        for (MatrixStatus maStatus : matrixStatuses) {
            if (status.intValue() == maStatus.getStatus()) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param matrixStatus
     * @return
     */
    public boolean check(MatrixStatus... matrixStatus) {
        if (matrixStatus == null || matrixStatus.length <= 0) {
            return false;
        }
        for (MatrixStatus status : matrixStatus) {
            if (this.getStatus() == status.getStatus()) {
                return true;
            }
        }
        return false;
    }
}
