package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/1 16:44
 */
public class LimitGroupSyncInfo extends PrintFriendliness {
    /**
     * {
     *  "status": "OK",
     *  "message": "",
     *  "code": null,
     *  "data": [{
     *   "testLimitGroupId": "12",
     *   "testLimitGroupName": "Proposition 65 list (cancer type toxicity)",
     *   "testLimitGroupText": "",
     *   "customerAccountId": "52233",
 * 		"testLimitGroupTypeId": "37",
 * 		"testLimitGroupTypeName": "Animal",
     *   "status": "Active",
     *   "standardVersionIdentifier": "33",
     *   "regulationVersionIdentifier": ""
     *
     *  }]
     * }
     */
    /**
     *
     */
    private Integer testLimitGroupId;

    private Integer testLimitGroupTypeId;

    private String testLimitGroupTypeName;
    /**
     *
     */
    private String testLimitGroupName;
    /**
     *
     */
    private String testLimitGroupText;

    private String testLimitGroupValue;
    /**
     *
     */
    private Integer customerAccountId;
    /**
     *
     */
    private String status;
    /**
     *
     */
    private Integer standardVersionIdentifier;
    /**
     *
     */
    private Integer regulationVersionIdentifier;

    public Integer getTestLimitGroupId() {
        return testLimitGroupId;
    }

    public void setTestLimitGroupId(Integer testLimitGroupId) {
        this.testLimitGroupId = testLimitGroupId;
    }

    public Integer getTestLimitGroupTypeId() {
        return testLimitGroupTypeId;
    }

    public void setTestLimitGroupTypeId(Integer testLimitGroupTypeId) {
        this.testLimitGroupTypeId = testLimitGroupTypeId;
    }

    public String getTestLimitGroupTypeName() {
        return testLimitGroupTypeName;
    }

    public void setTestLimitGroupTypeName(String testLimitGroupTypeName) {
        this.testLimitGroupTypeName = testLimitGroupTypeName;
    }

    public String getTestLimitGroupName() {
        return testLimitGroupName;
    }

    public void setTestLimitGroupName(String testLimitGroupName) {
        this.testLimitGroupName = testLimitGroupName;
    }

    public String getTestLimitGroupText() {
        return testLimitGroupText;
    }

    public void setTestLimitGroupText(String testLimitGroupText) {
        this.testLimitGroupText = testLimitGroupText;
    }

    public String getTestLimitGroupValue() {
        return testLimitGroupValue;
    }

    public void setTestLimitGroupValue(String testLimitGroupValue) {
        this.testLimitGroupValue = testLimitGroupValue;
    }

    public Integer getCustomerAccountId() {
        return customerAccountId;
    }

    public void setCustomerAccountId(Integer customerAccountId) {
        this.customerAccountId = customerAccountId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getStandardVersionIdentifier() {
        return standardVersionIdentifier;
    }

    public void setStandardVersionIdentifier(Integer standardVersionIdentifier) {
        this.standardVersionIdentifier = standardVersionIdentifier;
    }

    public Integer getRegulationVersionIdentifier() {
        return regulationVersionIdentifier;
    }

    public void setRegulationVersionIdentifier(Integer regulationVersionIdentifier) {
        this.regulationVersionIdentifier = regulationVersionIdentifier;
    }
}
