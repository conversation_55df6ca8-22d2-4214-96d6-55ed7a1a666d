/**
 * QueryBusinessTemplateListResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots.template;

public class QueryBusinessTemplateListResponse  implements java.io.Serializable {
    private com.sgs.otsnotes.facade.model.webservice.ots.template.TemplateListView[] queryBusinessTemplateListResult;

    public QueryBusinessTemplateListResponse() {
    }

    public QueryBusinessTemplateListResponse(
           com.sgs.otsnotes.facade.model.webservice.ots.template.TemplateListView[] queryBusinessTemplateListResult) {
           this.queryBusinessTemplateListResult = queryBusinessTemplateListResult;
    }


    /**
     * Gets the queryBusinessTemplateListResult value for this QueryBusinessTemplateListResponse.
     * 
     * @return queryBusinessTemplateListResult
     */
    public com.sgs.otsnotes.facade.model.webservice.ots.template.TemplateListView[] getQueryBusinessTemplateListResult() {
        return queryBusinessTemplateListResult;
    }


    /**
     * Sets the queryBusinessTemplateListResult value for this QueryBusinessTemplateListResponse.
     * 
     * @param queryBusinessTemplateListResult
     */
    public void setQueryBusinessTemplateListResult(com.sgs.otsnotes.facade.model.webservice.ots.template.TemplateListView[] queryBusinessTemplateListResult) {
        this.queryBusinessTemplateListResult = queryBusinessTemplateListResult;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof QueryBusinessTemplateListResponse)) {
            return false;
        }
        QueryBusinessTemplateListResponse other = (QueryBusinessTemplateListResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.queryBusinessTemplateListResult==null && other.getQueryBusinessTemplateListResult()==null) || 
             (this.queryBusinessTemplateListResult!=null &&
              java.util.Arrays.equals(this.queryBusinessTemplateListResult, other.getQueryBusinessTemplateListResult())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getQueryBusinessTemplateListResult() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getQueryBusinessTemplateListResult());
                 i++) {
                java.lang.Object obj = java.lang.reflect.Array.get(getQueryBusinessTemplateListResult(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(QueryBusinessTemplateListResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", ">QueryBusinessTemplateListResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("queryBusinessTemplateListResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "QueryBusinessTemplateListResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", "TemplateListView"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://tempuri.org/", "TemplateListView"));
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
