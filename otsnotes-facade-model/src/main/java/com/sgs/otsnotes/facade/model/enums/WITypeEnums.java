package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

/**
 * @ClassName WITypeEnums
 * @Description TODO
 * <AUTHOR>
 * @Date 4/20/2021
 */
@Dict
public enum  WITypeEnums {
    customer(0),
    standard(3),
    regulation(2);
    @DictCodeField
    private Integer type;
    WITypeEnums(Integer type){
        this.type = type;
    }

    public Integer getType() {
        return type;
    }
    public static WITypeEnums getType(Integer type){
        if(type==null){
            return null;
        }
        for (WITypeEnums value : WITypeEnums.values()) {
            if(type.compareTo(value.type)==0){
                return value;
            }
        }
        return null;
    }
}
