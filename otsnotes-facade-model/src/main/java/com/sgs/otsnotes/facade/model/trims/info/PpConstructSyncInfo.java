package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class PpConstructSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Long aid;
    /**
     *
     */
    private Integer sectionId;
    /**
     *
     */
    private Integer sequence;
    /**
     *
     */
    private String artifactType;
    /**
     *
     */
    private Integer artifactVersionIdentifier;
    /**
     *
     */
    private Integer tlExecutionClassificationCode;
    /**
     *
     */
    private String tlExecutionClassificationName;
    /**
     * --1=Method,2=Regulation,3=Standard,4=Protocol/Package
     */
    private Integer citationTypeId;
    /**
     *
     */
    private Integer citationVersionIdentifier;
    /**
     * --for Standard/Regulation
     */
    private Integer citationSectionId;
    /*
    * -- citationPpStandardSection
    * */
    private String citationPpStandardSection;
    /**
     * --for Method
     */
    private String citationSectionName;
    /**
     *
     */
    private String citationName;
    /**
     *
     */
    private String evaluationAlias;
    /**
     *
     */
    private String ppNotesAlias;
    /**
     *
     */
    private String flexibleData;
    /**
     *
     */
    private Integer isRefTlReportReferenceNote;

    /**
     *
     */
    private Integer isRefTlRequirementNote;
    /**
     *
     */
    private String reportReferenceNoteAlias;
    /**
     *
     */
    private String conditionInstructionsAlias;
    /**
     *
     */
    private String specificNotes;
    /**
     *
     */
    private List<Integer> conditionIds;
    /**
     *
     */
    private List<PpTestLineAnalyteRelSyncInfo> testAnalytes;

    private List<Integer> talVersionIdentifiers;

    private List<Integer> applicationFactorIds;

    /**
     * TL是否单独测试 CHAR(16777215)<br>
     *
     */
    private String testSeparately;
    /**
     * PP Construction跨版本唯一ID CHAR(16777215)<br>
     *
     */
    private String constructionId;

    public String getTestSeparately() {
        return testSeparately;
    }

    public void setTestSeparately(String testSeparately) {
        this.testSeparately = testSeparately;
    }

    public String getConstructionId() {
        return constructionId;
    }

    public void setConstructionId(String constructionId) {
        this.constructionId = constructionId;
    }

    public String getCitationPpStandardSection() {
        return citationPpStandardSection;
    }

    public void setCitationPpStandardSection(String citationPpStandardSection) {
        this.citationPpStandardSection = citationPpStandardSection;
    }

    public Long getAid() {
        return aid;
    }

    public void setAid(Long aid) {
        this.aid = aid;
    }

    public Integer getSectionId() {
        return sectionId;
    }

    public void setSectionId(Integer sectionId) {
        this.sectionId = sectionId;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getArtifactType() {
        return artifactType;
    }

    public void setArtifactType(String artifactType) {
        this.artifactType = artifactType;
    }

    public Integer getArtifactVersionIdentifier() {
        return artifactVersionIdentifier;
    }

    public void setArtifactVersionIdentifier(Integer artifactVersionIdentifier) {
        this.artifactVersionIdentifier = artifactVersionIdentifier;
    }

    public Integer getTlExecutionClassificationCode() {
        return tlExecutionClassificationCode;
    }

    public void setTlExecutionClassificationCode(Integer tlExecutionClassificationCode) {
        this.tlExecutionClassificationCode = tlExecutionClassificationCode;
    }

    public String getTlExecutionClassificationName() {
        return tlExecutionClassificationName;
    }

    public void setTlExecutionClassificationName(String tlExecutionClassificationName) {
        this.tlExecutionClassificationName = tlExecutionClassificationName;
    }

    public Integer getCitationTypeId() {
        return citationTypeId;
    }

    public void setCitationTypeId(Integer citationTypeId) {
        this.citationTypeId = citationTypeId;
    }

    public Integer getCitationVersionIdentifier() {
        return citationVersionIdentifier;
    }

    public void setCitationVersionIdentifier(Integer citationVersionIdentifier) {
        this.citationVersionIdentifier = citationVersionIdentifier;
    }

    public Integer getCitationSectionId() {
        return citationSectionId;
    }

    public void setCitationSectionId(Integer citationSectionId) {
        this.citationSectionId = citationSectionId;
    }

    public String getCitationSectionName() {
        return citationSectionName;
    }

    public void setCitationSectionName(String citationSectionName) {
        this.citationSectionName = citationSectionName;
    }

    public String getCitationName() {
        return citationName;
    }

    public void setCitationName(String citationName) {
        this.citationName = citationName;
    }

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }

    public String getPpNotesAlias() {
        return ppNotesAlias;
    }

    public void setPpNotesAlias(String ppNotesAlias) {
        this.ppNotesAlias = ppNotesAlias;
    }

    public String getFlexibleData() {
        return flexibleData;
    }

    public void setFlexibleData(String flexibleData) {
        this.flexibleData = flexibleData;
    }

    public Integer getIsRefTlReportReferenceNote() {
        return isRefTlReportReferenceNote;
    }

    public void setIsRefTlReportReferenceNote(Integer isRefTlReportReferenceNote) {
        this.isRefTlReportReferenceNote = isRefTlReportReferenceNote;
    }

    public Integer getIsRefTlRequirementNote() {
        return isRefTlRequirementNote;
    }

    public void setIsRefTlRequirementNote(Integer isRefTlRequirementNote) {
        this.isRefTlRequirementNote = isRefTlRequirementNote;
    }

    public String getReportReferenceNoteAlias() {
        return reportReferenceNoteAlias;
    }

    public void setReportReferenceNoteAlias(String reportReferenceNoteAlias) {
        this.reportReferenceNoteAlias = reportReferenceNoteAlias;
    }

    public String getConditionInstructionsAlias() {
        return conditionInstructionsAlias;
    }

    public void setConditionInstructionsAlias(String conditionInstructionsAlias) {
        this.conditionInstructionsAlias = conditionInstructionsAlias;
    }

    public String getSpecificNotes() {
        return specificNotes;
    }

    public void setSpecificNotes(String specificNotes) {
        this.specificNotes = specificNotes;
    }

    public List<PpTestLineAnalyteRelSyncInfo> getTestAnalytes() {
        return testAnalytes;
    }

    public void setTestAnalytes(List<PpTestLineAnalyteRelSyncInfo> testAnalytes) {
        this.testAnalytes = testAnalytes;
    }

    public List<Integer> getConditionIds() {
        return conditionIds;
    }

    public void setConditionIds(List<Integer> conditionIds) {
        this.conditionIds = conditionIds;
    }

    public List<Integer> getTalVersionIdentifiers() {
        return talVersionIdentifiers;
    }

    public void setTalVersionIdentifiers(List<Integer> talVersionIdentifiers) {
        this.talVersionIdentifiers = talVersionIdentifiers;
    }

    public List<Integer> getApplicationFactorIds() {
        return applicationFactorIds;
    }

    public void setApplicationFactorIds(List<Integer> applicationFactorIds) {
        this.applicationFactorIds = applicationFactorIds;
    }
}
