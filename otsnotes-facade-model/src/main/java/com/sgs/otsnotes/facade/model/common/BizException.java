package com.sgs.otsnotes.facade.model.common;

/**
 * Created by <PERSON> on 2019/05/09.
 */
public class BizException extends RuntimeException {

    private static final long serialVersionUID = -1581793943979612789L;

    private ResponseCode errorCode;

    public BizException(ResponseCode errorCode, String msg) {

        this(errorCode, msg, null);
    }

    public BizException(String msg) {
        this(ResponseCode.UNKNOWN, msg);
    }

    public BizException(ResponseCode errorCode) {
        this(errorCode, errorCode.getMessage(), null);
    }

    public BizException(String msg, Throwable cause) {
        this(ResponseCode.FAIL, msg, cause);
    }

    public BizException(ResponseCode errorCode, String msg, Throwable cause) {
        super(msg, cause);
        if (errorCode == null) {
            throw new IllegalArgumentException("errorCode is null");
        }
        this.errorCode = errorCode;
    }

    public ResponseCode getErrorCode() {
        return errorCode;
    }

    public static void throwBizException(ResponseCode errorCode, String message, Throwable cause) {
        throw new BizException(errorCode, message, cause);
    }

    public static void throwBizException(ResponseCode errorCode, String message) {
        throwBizException(errorCode, message, null);
    }
}
