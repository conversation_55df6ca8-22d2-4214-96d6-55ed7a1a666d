package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;
import com.sgs.otsnotes.facade.model.trims.info.EventSyncInfo;

/**
 *
 */
public final class EventTypeSyncForProdRsp extends PrintFriendliness {
    /**
     *
     */
    private boolean success;
    /**
     *
     */
    private EventSyncInfo data;
    /**
     *
     */
    private String msg;
    /**
     *
     */
    private String stackTrace;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public EventSyncInfo getData() {
        return data;
    }

    public void setData(EventSyncInfo data) {
        this.data = data;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(String stackTrace) {
        this.stackTrace = stackTrace;
    }
}
