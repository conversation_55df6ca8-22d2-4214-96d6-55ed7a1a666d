package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum TalApplicabilityType {
    None(0),
    ProductAttrId(1),
    ConditionId(2),
    TestItemVersionId(3),
    StandardVersionId(4),
    RegulationVersionId(5);

    private int type;

    TalApplicabilityType(int type) {
        this.type = type;
    }

    public static final Map<Integer, TalApplicabilityType> maps = new HashMap<Integer, TalApplicabilityType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TalApplicabilityType enu : TalApplicabilityType.values()) {
                put(enu.getType(), enu);
            }
        }
    };

    public int getType() {
        return type;
    }

    public static boolean check(Integer type, TalApplicabilityType talApplicabilityType){
        if (type == null || !maps.containsKey(type)) {
            return false;
        }
        return maps.get(type) == talApplicabilityType;
    }

    public static TalApplicabilityType findType(Integer type) {
        if (type == null || !maps.containsKey(type)) {
            return TalApplicabilityType.None;
        }
        return maps.get(type);
    }
}
