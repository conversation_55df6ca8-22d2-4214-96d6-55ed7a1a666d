package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum TrimsLabSectionStatus {
    None(0, "None"),
    Active(1, "Active"),
    PhaseOut(2, "PhaseOut"),
    Inactive(3, "Inactive");

    private int status;
    private String code;

    TrimsLabSectionStatus(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    static Map<Integer, TrimsLabSectionStatus> maps = new HashMap<>();
    static Map<String, TrimsLabSectionStatus> codeMaps = new HashMap<>();

    static {
        for (TrimsLabSectionStatus status : TrimsLabSectionStatus.values()) {
            maps.put(status.getStatus(), status);
            codeMaps.put(status.getCode().toLowerCase(), status);
        }
    };

    public static TrimsLabSectionStatus getStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    public static TrimsLabSectionStatus getCode(String code) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase())) {
            return null;
        }
        return codeMaps.get(code.toLowerCase());
    }

    public static boolean checkStatus(TrimsLabSectionStatus status, TrimsLabSectionStatus... labSectionStatus) {
        if (status == null || labSectionStatus == null || labSectionStatus.length <= 0){
            return false;
        }
        return check(status.getStatus(), labSectionStatus);
    }

    /**
     *
     * @param status
     * @param labSectionStatus
     * @return
     */
    public static boolean check(Integer status, TrimsLabSectionStatus... labSectionStatus) {
        if (status == null || !maps.containsKey(status.intValue()) || labSectionStatus == null || labSectionStatus.length <= 0){
            return false;
        }
        for (TrimsLabSectionStatus lsStatus: labSectionStatus){
            if (status.intValue() == lsStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param code
     * @param tlStatus
     * @return
     */
    public static boolean check(String code, TrimsTestLineStatus... tlStatus) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase()) || tlStatus == null || tlStatus.length <= 0){
            return false;
        }
        int status = codeMaps.get(code.toLowerCase()).getStatus();
        for (TrimsTestLineStatus tls: tlStatus){
            if (tls.getStatus() == status){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param testLineStatus
     * @return
     */
    public boolean check(TrimsTestLineStatus... testLineStatus){
        if (testLineStatus == null || testLineStatus.length <= 0){
            return false;
        }
        for (TrimsTestLineStatus tlStatus: testLineStatus){
            if (this.getStatus() == tlStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

}
