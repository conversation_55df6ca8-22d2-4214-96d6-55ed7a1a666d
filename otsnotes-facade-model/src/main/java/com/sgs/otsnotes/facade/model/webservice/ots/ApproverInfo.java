/**
 * ApproverInfo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ApproverInfo  implements java.io.Serializable {
    private int approverId;

    private DataItem[] informations;

    public ApproverInfo() {
    }

    public ApproverInfo(
           int approverId,
           DataItem[] informations) {
           this.approverId = approverId;
           this.informations = informations;
    }


    /**
     * Gets the approverId value for this ApproverInfo.
     * 
     * @return approverId
     */
    public int getApproverId() {
        return approverId;
    }


    /**
     * Sets the approverId value for this ApproverInfo.
     * 
     * @param approverId
     */
    public void setApproverId(int approverId) {
        this.approverId = approverId;
    }


    /**
     * Gets the informations value for this ApproverInfo.
     * 
     * @return informations
     */
    public DataItem[] getInformations() {
        return informations;
    }


    /**
     * Sets the informations value for this ApproverInfo.
     * 
     * @param informations
     */
    public void setInformations(DataItem[] informations) {
        this.informations = informations;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ApproverInfo)) {
            return false;
        }
        ApproverInfo other = (ApproverInfo) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.approverId == other.getApproverId() &&
            ((this.informations==null && other.getInformations()==null) || 
             (this.informations!=null &&
              java.util.Arrays.equals(this.informations, other.getInformations())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += getApproverId();
        if (getInformations() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getInformations());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getInformations(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ApproverInfo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ApproverInfo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("approverId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ApproverId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("informations");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Informations"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
