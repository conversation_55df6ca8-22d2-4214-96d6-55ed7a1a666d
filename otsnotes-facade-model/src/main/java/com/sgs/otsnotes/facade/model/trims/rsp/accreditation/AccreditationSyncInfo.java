package com.sgs.otsnotes.facade.model.trims.rsp.accreditation;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

/**
 * @ClassName AccreditationSyncInfo
 * @Description 同步labSection
 * <AUTHOR>
 * @Date 12/24/2020
 */

public class AccreditationSyncInfo extends PrintFriendliness {
    /**
     * 暂不使用
     */
    private Long caId;

    /**
     *
     */
    private Integer accId;

    /**
     *
     */
    private Integer isDeleted;

    /**
     * Delete=1时，只能取到Test Line ID对应Active版本的VersionIdentifier
     */
    private Integer tlVersionIdentifier;

    /**
     * Delete=1时，只能取到PP No.对应Active版本的VersionIdentifier
     */
    private Integer ppVersionIdentifier;

    /**
     *
     */
    private Integer laboratoryId;

    /**
     *
     */
    private List<Integer> testAnalyteIds;

    /**
     *
     */
    private List<Integer> productAttributeIds;

    /**
     *
     */
    private List<AccreditationItemsSyncInfo> accreditationItems;

    public Long getCaId() {
        return caId;
    }

    public void setCaId(Long caId) {
        this.caId = caId;
    }

    public Integer getAccId() {
        return accId;
    }

    public void setAccId(Integer accId) {
        this.accId = accId;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getTlVersionIdentifier() {
        return tlVersionIdentifier;
    }

    public void setTlVersionIdentifier(Integer tlVersionIdentifier) {
        this.tlVersionIdentifier = tlVersionIdentifier;
    }

    public Integer getPpVersionIdentifier() {
        return ppVersionIdentifier;
    }

    public void setPpVersionIdentifier(Integer ppVersionIdentifier) {
        this.ppVersionIdentifier = ppVersionIdentifier;
    }

    public Integer getLaboratoryId() {
        return laboratoryId;
    }

    public void setLaboratoryId(Integer laboratoryId) {
        this.laboratoryId = laboratoryId;
    }

    public List<Integer> getTestAnalyteIds() {
        return testAnalyteIds;
    }

    public void setTestAnalyteIds(List<Integer> testAnalyteIds) {
        this.testAnalyteIds = testAnalyteIds;
    }

    public List<Integer> getProductAttributeIds() {
        return productAttributeIds;
    }

    public void setProductAttributeIds(List<Integer> productAttributeIds) {
        this.productAttributeIds = productAttributeIds;
    }

    public List<AccreditationItemsSyncInfo> getAccreditationItems() {
        return accreditationItems;
    }

    public void setAccreditationItems(List<AccreditationItemsSyncInfo> accreditationItems) {
        this.accreditationItems = accreditationItems;
    }
}
