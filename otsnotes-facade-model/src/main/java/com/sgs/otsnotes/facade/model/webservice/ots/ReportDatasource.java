/**
 * ReportDatasource.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ReportDatasource  implements java.io.Serializable {
    private String version;

    private TemplateTypeEnum templateType;

    private int templateBusinessType;

    private String instanceId;

    private String subInstanceId;

    private String[] reportType;

    private TSGeneratingReportType generateType;

    private DataItem[] flatDataList;

    private PhysicalTestClauseItem[] physicalTestClause;

    private DataItem[] variableDataList;

    private RepeatData[] repeatDataList;

    private SubTemplateInfo[] subTemplates;

    private SubReportInfo[] subReports;

    private ImageInfo[] images;

    private FactDataItem[] factDataList;

    private String[] operationType;

    private DataItem[] testRequestedFilePathList;

    private byte[] complexDatasStream;

    private FlatTableInfo[] flatTables;

    public ReportDatasource() {
    }

    public ReportDatasource(
           String version,
           TemplateTypeEnum templateType,
           int templateBusinessType,
           String instanceId,
           String subInstanceId,
           String[] reportType,
           TSGeneratingReportType generateType,
           DataItem[] flatDataList,
           PhysicalTestClauseItem[] physicalTestClause,
           DataItem[] variableDataList,
           RepeatData[] repeatDataList,
           SubTemplateInfo[] subTemplates,
           SubReportInfo[] subReports,
           ImageInfo[] images,
           FactDataItem[] factDataList,
           String[] operationType,
           DataItem[] testRequestedFilePathList,
           byte[] complexDatasStream,
           FlatTableInfo[] flatTables) {
           this.version = version;
           this.templateType = templateType;
           this.templateBusinessType = templateBusinessType;
           this.instanceId = instanceId;
           this.subInstanceId = subInstanceId;
           this.reportType = reportType;
           this.generateType = generateType;
           this.flatDataList = flatDataList;
           this.physicalTestClause = physicalTestClause;
           this.variableDataList = variableDataList;
           this.repeatDataList = repeatDataList;
           this.subTemplates = subTemplates;
           this.subReports = subReports;
           this.images = images;
           this.factDataList = factDataList;
           this.operationType = operationType;
           this.testRequestedFilePathList = testRequestedFilePathList;
           this.complexDatasStream = complexDatasStream;
           this.flatTables = flatTables;
    }


    /**
     * Gets the version value for this ReportDatasource.
     * 
     * @return version
     */
    public String getVersion() {
        return version;
    }


    /**
     * Sets the version value for this ReportDatasource.
     * 
     * @param version
     */
    public void setVersion(String version) {
        this.version = version;
    }


    /**
     * Gets the templateType value for this ReportDatasource.
     * 
     * @return templateType
     */
    public TemplateTypeEnum getTemplateType() {
        return templateType;
    }


    /**
     * Sets the templateType value for this ReportDatasource.
     * 
     * @param templateType
     */
    public void setTemplateType(TemplateTypeEnum templateType) {
        this.templateType = templateType;
    }


    /**
     * Gets the templateBusinessType value for this ReportDatasource.
     * 
     * @return templateBusinessType
     */
    public int getTemplateBusinessType() {
        return templateBusinessType;
    }


    /**
     * Sets the templateBusinessType value for this ReportDatasource.
     * 
     * @param templateBusinessType
     */
    public void setTemplateBusinessType(int templateBusinessType) {
        this.templateBusinessType = templateBusinessType;
    }


    /**
     * Gets the instanceId value for this ReportDatasource.
     * 
     * @return instanceId
     */
    public String getInstanceId() {
        return instanceId;
    }


    /**
     * Sets the instanceId value for this ReportDatasource.
     * 
     * @param instanceId
     */
    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }


    /**
     * Gets the subInstanceId value for this ReportDatasource.
     * 
     * @return subInstanceId
     */
    public String getSubInstanceId() {
        return subInstanceId;
    }


    /**
     * Sets the subInstanceId value for this ReportDatasource.
     * 
     * @param subInstanceId
     */
    public void setSubInstanceId(String subInstanceId) {
        this.subInstanceId = subInstanceId;
    }


    /**
     * Gets the reportType value for this ReportDatasource.
     * 
     * @return reportType
     */
    public String[] getReportType() {
        return reportType;
    }


    /**
     * Sets the reportType value for this ReportDatasource.
     * 
     * @param reportType
     */
    public void setReportType(String[] reportType) {
        this.reportType = reportType;
    }


    /**
     * Gets the generateType value for this ReportDatasource.
     * 
     * @return generateType
     */
    public TSGeneratingReportType getGenerateType() {
        return generateType;
    }


    /**
     * Sets the generateType value for this ReportDatasource.
     * 
     * @param generateType
     */
    public void setGenerateType(TSGeneratingReportType generateType) {
        this.generateType = generateType;
    }


    /**
     * Gets the flatDataList value for this ReportDatasource.
     * 
     * @return flatDataList
     */
    public DataItem[] getFlatDataList() {
        return flatDataList;
    }


    /**
     * Sets the flatDataList value for this ReportDatasource.
     * 
     * @param flatDataList
     */
    public void setFlatDataList(DataItem[] flatDataList) {
        this.flatDataList = flatDataList;
    }


    /**
     * Gets the physicalTestClause value for this ReportDatasource.
     * 
     * @return physicalTestClause
     */
    public PhysicalTestClauseItem[] getPhysicalTestClause() {
        return physicalTestClause;
    }


    /**
     * Sets the physicalTestClause value for this ReportDatasource.
     * 
     * @param physicalTestClause
     */
    public void setPhysicalTestClause(PhysicalTestClauseItem[] physicalTestClause) {
        this.physicalTestClause = physicalTestClause;
    }


    /**
     * Gets the variableDataList value for this ReportDatasource.
     * 
     * @return variableDataList
     */
    public DataItem[] getVariableDataList() {
        return variableDataList;
    }


    /**
     * Sets the variableDataList value for this ReportDatasource.
     * 
     * @param variableDataList
     */
    public void setVariableDataList(DataItem[] variableDataList) {
        this.variableDataList = variableDataList;
    }


    /**
     * Gets the repeatDataList value for this ReportDatasource.
     * 
     * @return repeatDataList
     */
    public RepeatData[] getRepeatDataList() {
        return repeatDataList;
    }


    /**
     * Sets the repeatDataList value for this ReportDatasource.
     * 
     * @param repeatDataList
     */
    public void setRepeatDataList(RepeatData[] repeatDataList) {
        this.repeatDataList = repeatDataList;
    }


    /**
     * Gets the subTemplates value for this ReportDatasource.
     * 
     * @return subTemplates
     */
    public SubTemplateInfo[] getSubTemplates() {
        return subTemplates;
    }


    /**
     * Sets the subTemplates value for this ReportDatasource.
     * 
     * @param subTemplates
     */
    public void setSubTemplates(SubTemplateInfo[] subTemplates) {
        this.subTemplates = subTemplates;
    }


    /**
     * Gets the subReports value for this ReportDatasource.
     * 
     * @return subReports
     */
    public SubReportInfo[] getSubReports() {
        return subReports;
    }


    /**
     * Sets the subReports value for this ReportDatasource.
     * 
     * @param subReports
     */
    public void setSubReports(SubReportInfo[] subReports) {
        this.subReports = subReports;
    }


    /**
     * Gets the images value for this ReportDatasource.
     * 
     * @return images
     */
    public ImageInfo[] getImages() {
        return images;
    }


    /**
     * Sets the images value for this ReportDatasource.
     * 
     * @param images
     */
    public void setImages(ImageInfo[] images) {
        this.images = images;
    }


    /**
     * Gets the factDataList value for this ReportDatasource.
     * 
     * @return factDataList
     */
    public FactDataItem[] getFactDataList() {
        return factDataList;
    }


    /**
     * Sets the factDataList value for this ReportDatasource.
     * 
     * @param factDataList
     */
    public void setFactDataList(FactDataItem[] factDataList) {
        this.factDataList = factDataList;
    }


    /**
     * Gets the operationType value for this ReportDatasource.
     * 
     * @return operationType
     */
    public String[] getOperationType() {
        return operationType;
    }


    /**
     * Sets the operationType value for this ReportDatasource.
     * 
     * @param operationType
     */
    public void setOperationType(String[] operationType) {
        this.operationType = operationType;
    }


    /**
     * Gets the testRequestedFilePathList value for this ReportDatasource.
     * 
     * @return testRequestedFilePathList
     */
    public DataItem[] getTestRequestedFilePathList() {
        return testRequestedFilePathList;
    }


    /**
     * Sets the testRequestedFilePathList value for this ReportDatasource.
     * 
     * @param testRequestedFilePathList
     */
    public void setTestRequestedFilePathList(DataItem[] testRequestedFilePathList) {
        this.testRequestedFilePathList = testRequestedFilePathList;
    }


    /**
     * Gets the complexDatasStream value for this ReportDatasource.
     * 
     * @return complexDatasStream
     */
    public byte[] getComplexDatasStream() {
        return complexDatasStream;
    }


    /**
     * Sets the complexDatasStream value for this ReportDatasource.
     * 
     * @param complexDatasStream
     */
    public void setComplexDatasStream(byte[] complexDatasStream) {
        this.complexDatasStream = complexDatasStream;
    }


    /**
     * Gets the flatTables value for this ReportDatasource.
     * 
     * @return flatTables
     */
    public FlatTableInfo[] getFlatTables() {
        return flatTables;
    }


    /**
     * Sets the flatTables value for this ReportDatasource.
     * 
     * @param flatTables
     */
    public void setFlatTables(FlatTableInfo[] flatTables) {
        this.flatTables = flatTables;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ReportDatasource)) {
            return false;
        }
        ReportDatasource other = (ReportDatasource) obj;
        if (obj == null) {
            return false;
        }
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.version==null && other.getVersion()==null) || 
             (this.version!=null &&
              this.version.equals(other.getVersion()))) &&
            ((this.templateType==null && other.getTemplateType()==null) || 
             (this.templateType!=null &&
              this.templateType.equals(other.getTemplateType()))) &&
            this.templateBusinessType == other.getTemplateBusinessType() &&
            ((this.instanceId==null && other.getInstanceId()==null) || 
             (this.instanceId!=null &&
              this.instanceId.equals(other.getInstanceId()))) &&
            ((this.subInstanceId==null && other.getSubInstanceId()==null) || 
             (this.subInstanceId!=null &&
              this.subInstanceId.equals(other.getSubInstanceId()))) &&
            ((this.reportType==null && other.getReportType()==null) || 
             (this.reportType!=null &&
              java.util.Arrays.equals(this.reportType, other.getReportType()))) &&
            ((this.generateType==null && other.getGenerateType()==null) || 
             (this.generateType!=null &&
              this.generateType.equals(other.getGenerateType()))) &&
            ((this.flatDataList==null && other.getFlatDataList()==null) || 
             (this.flatDataList!=null &&
              java.util.Arrays.equals(this.flatDataList, other.getFlatDataList()))) &&
            ((this.physicalTestClause==null && other.getPhysicalTestClause()==null) || 
             (this.physicalTestClause!=null &&
              java.util.Arrays.equals(this.physicalTestClause, other.getPhysicalTestClause()))) &&
            ((this.variableDataList==null && other.getVariableDataList()==null) || 
             (this.variableDataList!=null &&
              java.util.Arrays.equals(this.variableDataList, other.getVariableDataList()))) &&
            ((this.repeatDataList==null && other.getRepeatDataList()==null) || 
             (this.repeatDataList!=null &&
              java.util.Arrays.equals(this.repeatDataList, other.getRepeatDataList()))) &&
            ((this.subTemplates==null && other.getSubTemplates()==null) || 
             (this.subTemplates!=null &&
              java.util.Arrays.equals(this.subTemplates, other.getSubTemplates()))) &&
            ((this.subReports==null && other.getSubReports()==null) || 
             (this.subReports!=null &&
              java.util.Arrays.equals(this.subReports, other.getSubReports()))) &&
            ((this.images==null && other.getImages()==null) || 
             (this.images!=null &&
              java.util.Arrays.equals(this.images, other.getImages()))) &&
            ((this.factDataList==null && other.getFactDataList()==null) || 
             (this.factDataList!=null &&
              java.util.Arrays.equals(this.factDataList, other.getFactDataList()))) &&
            ((this.operationType==null && other.getOperationType()==null) || 
             (this.operationType!=null &&
              java.util.Arrays.equals(this.operationType, other.getOperationType()))) &&
            ((this.testRequestedFilePathList==null && other.getTestRequestedFilePathList()==null) || 
             (this.testRequestedFilePathList!=null &&
              java.util.Arrays.equals(this.testRequestedFilePathList, other.getTestRequestedFilePathList()))) &&
            ((this.complexDatasStream==null && other.getComplexDatasStream()==null) || 
             (this.complexDatasStream!=null &&
              java.util.Arrays.equals(this.complexDatasStream, other.getComplexDatasStream()))) &&
            ((this.flatTables==null && other.getFlatTables()==null) || 
             (this.flatTables!=null &&
              java.util.Arrays.equals(this.flatTables, other.getFlatTables())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getVersion() != null) {
            _hashCode += getVersion().hashCode();
        }
        if (getTemplateType() != null) {
            _hashCode += getTemplateType().hashCode();
        }
        _hashCode += getTemplateBusinessType();
        if (getInstanceId() != null) {
            _hashCode += getInstanceId().hashCode();
        }
        if (getSubInstanceId() != null) {
            _hashCode += getSubInstanceId().hashCode();
        }
        if (getReportType() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getReportType());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getReportType(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getGenerateType() != null) {
            _hashCode += getGenerateType().hashCode();
        }
        if (getFlatDataList() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getFlatDataList());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getFlatDataList(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getPhysicalTestClause() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getPhysicalTestClause());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getPhysicalTestClause(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getVariableDataList() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getVariableDataList());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getVariableDataList(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getRepeatDataList() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getRepeatDataList());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getRepeatDataList(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getSubTemplates() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getSubTemplates());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getSubTemplates(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getSubReports() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getSubReports());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getSubReports(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getImages() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getImages());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getImages(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getFactDataList() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getFactDataList());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getFactDataList(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getOperationType() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getOperationType());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getOperationType(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getTestRequestedFilePathList() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getTestRequestedFilePathList());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getTestRequestedFilePathList(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getComplexDatasStream() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getComplexDatasStream());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getComplexDatasStream(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getFlatTables() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getFlatTables());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getFlatTables(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ReportDatasource.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ReportDatasource"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("version");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Version"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateBusinessType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateBusinessType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("instanceId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "InstanceId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("subInstanceId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubInstanceId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("reportType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ReportType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "UsedByEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("generateType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "GenerateType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TSGeneratingReportType"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("flatDataList");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FlatDataList"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("physicalTestClause");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PhysicalTestClause"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "PhysicalTestClauseItem"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PhysicalTestClauseItem"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("variableDataList");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "VariableDataList"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("repeatDataList");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RepeatDataList"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "RepeatData"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RepeatData"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("subTemplates");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubTemplates"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubTemplateInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubTemplateInfo"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("subReports");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubReports"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubReportInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SubReportInfo"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("images");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Images"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ImageInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ImageInfo"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("factDataList");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FactDataList"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FactDataItem"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FactDataItem"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("operationType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "OperationType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "GenerateReportOperationType"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("testRequestedFilePathList");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TestRequestedFilePathList"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("complexDatasStream");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ComplexDatasStream"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "base64Binary"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("flatTables");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FlatTables"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "FlatTableInfo"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "FlatTableInfo"));
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
