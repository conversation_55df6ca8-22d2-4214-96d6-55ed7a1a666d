package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;
import com.sgs.otsnotes.facade.model.trims.rsp.CustomerTestCategorySyncInfo;

import java.util.List;

public class TestLineSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer versionIdentifier;
    /**
     * 如果状态为Inactive则下面其它信息都不需要提供。
     */
    private Integer testLineId;
    /**
     *
     */
    private String status;
    /**
     * 0：代表没锁、1：代表被锁
     */
    private int isLocked;
    /**
     *
     */
    private Integer noOfReplication;
    /**
     *
     */
    private Integer versionNo;
    /**
     *
     */
    private Integer productLineId;
    /**
     *
     */
    private String productLineName;
    /**
     *
     */
    private String productLineAbbr;
    /**
     *
     */
    private Integer testVersionIdentifier;
    /**
     *
     */
    private Integer testId;
    /**
     *
     */
    private String testItemName;
    /**
     *
     */
    private String testLineEvaluation;
    /**
     *
     */
    private String methodName;
    /**
     *
     */
    private Integer methodVersionIdentifier;
    /**
     *
     */
    private Integer methodId;
    /**
     *
     */
    private String ppNotes;
    /**
     *
     */
    private String reportReferenceNote;
    /**
     *
     */
    private String conditionInstructions;

    /**
     *
     */
    private List<CustomerTestCategorySyncInfo> customerTestCategoryItems;
    /**
     *
     */
    private List<EquipmentTypeSyncInfo> equipmentTypeItems;
    /**
     *
     */
    private List<CustomerAppSyncInfo> customersApplicabilityItems;//workingInstructionId
    /**
     *
     */
    private List<TestConditionTypeSyncInfo> conditionItems;
    /**
     *
     */
    private List<TestStandardSyncInfo> testStandardItems;
    /**
     *
     */
    // 241029 trims端通知不再维护
    @Deprecated
    private List<TestLineRegulationSyncInfo> regulationItems;
    /**
     *
     */
    private List<CustomerAppSyncInfo> workInstructionItems;//workInstructionId
    /**
     *
     */
    private List<Integer> usageTypeIds;
    /**
     *
     */
    private List<Integer> productAttributeIds;
    /**
     *
     */
    private List<TestLineLangSyncInfo> otherLanguageItems;
    /**
     *
     */
    private List<TestLineAnalyteRelSyncInfo> testAnalyteItems;
    /**
     *
     */
    private List<TestLineAnalyteLimitRelSyncInfo> testAnalyteLimitItems;

    private List<Integer> applicationFactorIds;


    private List<Integer> customTestCategoryIds;

    private List<Integer> workInstructionIds;

    private List<Integer> equipmentTypeIds;




    public List<Integer> getEquipmentTypeIds() {
        return equipmentTypeIds;
    }

    public void setEquipmentTypeIds(List<Integer> equipmentTypeIds) {
        this.equipmentTypeIds = equipmentTypeIds;
    }

    public List<Integer> getCustomTestCategoryIds() {
        return customTestCategoryIds;
    }

    public void setCustomTestCategoryIds(List<Integer> customTestCategoryIds) {
        this.customTestCategoryIds = customTestCategoryIds;
    }

    public List<Integer> getWorkInstructionIds() {
        return workInstructionIds;
    }

    public void setWorkInstructionIds(List<Integer> workInstructionIds) {
        this.workInstructionIds = workInstructionIds;
    }

    public Integer getVersionIdentifier() {
        return versionIdentifier;
    }

    public void setVersionIdentifier(Integer versionIdentifier) {
        this.versionIdentifier = versionIdentifier;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getIsLocked() {
        return isLocked;
    }

    public void setIsLocked(int isLocked) {
        this.isLocked = isLocked;
    }

    public Integer getNoOfReplication() {
        return noOfReplication;
    }

    public void setNoOfReplication(Integer noOfReplication) {
        this.noOfReplication = noOfReplication;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }

    public String getProductLineName() {
        return productLineName;
    }

    public void setProductLineName(String productLineName) {
        this.productLineName = productLineName;
    }

    public String getProductLineAbbr() {
        return productLineAbbr;
    }

    public void setProductLineAbbr(String productLineAbbr) {
        this.productLineAbbr = productLineAbbr;
    }

    public Integer getTestVersionIdentifier() {
        return testVersionIdentifier;
    }

    public void setTestVersionIdentifier(Integer testVersionIdentifier) {
        this.testVersionIdentifier = testVersionIdentifier;
    }

    public Integer getTestId() {
        return testId;
    }

    public void setTestId(Integer testId) {
        this.testId = testId;
    }

    public String getTestItemName() {
        return testItemName;
    }

    public void setTestItemName(String testItemName) {
        this.testItemName = testItemName;
    }

    public String getTestLineEvaluation() {
        return testLineEvaluation;
    }

    public void setTestLineEvaluation(String testLineEvaluation) {
        this.testLineEvaluation = testLineEvaluation;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public Integer getMethodVersionIdentifier() {
        return methodVersionIdentifier;
    }

    public void setMethodVersionIdentifier(Integer methodVersionIdentifier) {
        this.methodVersionIdentifier = methodVersionIdentifier;
    }

    public Integer getMethodId() {
        return methodId;
    }

    public void setMethodId(Integer methodId) {
        this.methodId = methodId;
    }

    public String getPpNotes() {
        return ppNotes;
    }

    public void setPpNotes(String ppNotes) {
        this.ppNotes = ppNotes;
    }

    public String getReportReferenceNote() {
        return reportReferenceNote;
    }

    public void setReportReferenceNote(String reportReferenceNote) {
        this.reportReferenceNote = reportReferenceNote;
    }

    public String getConditionInstructions() {
        return conditionInstructions;
    }

    public void setConditionInstructions(String conditionInstructions) {
        this.conditionInstructions = conditionInstructions;
    }

    public List<CustomerTestCategorySyncInfo> getCustomerTestCategoryItems() {
        return customerTestCategoryItems;
    }

    public void setCustomerTestCategoryItems(List<CustomerTestCategorySyncInfo> customerTestCategoryItems) {
        this.customerTestCategoryItems = customerTestCategoryItems;
    }

    public List<CustomerAppSyncInfo> getCustomersApplicabilityItems() {
        return customersApplicabilityItems;
    }

    public void setCustomersApplicabilityItems(List<CustomerAppSyncInfo> customersApplicabilityItems) {
        this.customersApplicabilityItems = customersApplicabilityItems;
    }

    public List<EquipmentTypeSyncInfo> getEquipmentTypeItems() {
        return equipmentTypeItems;
    }

    public void setEquipmentTypeItems(List<EquipmentTypeSyncInfo> equipmentTypeItems) {
        this.equipmentTypeItems = equipmentTypeItems;
    }

    public List<TestConditionTypeSyncInfo> getConditionItems() {
        return conditionItems;
    }

    public void setConditionItems(List<TestConditionTypeSyncInfo> conditionItems) {
        this.conditionItems = conditionItems;
    }

    public List<TestStandardSyncInfo> getTestStandardItems() {
        return testStandardItems;
    }

    public void setTestStandardItems(List<TestStandardSyncInfo> testStandardItems) {
        this.testStandardItems = testStandardItems;
    }

    public List<TestLineRegulationSyncInfo> getRegulationItems() {
        return regulationItems;
    }

    public void setRegulationItems(List<TestLineRegulationSyncInfo> regulationItems) {
        this.regulationItems = regulationItems;
    }

    public List<CustomerAppSyncInfo> getWorkInstructionItems() {
        return workInstructionItems;
    }

    public void setWorkInstructionItems(List<CustomerAppSyncInfo> workInstructionItems) {
        this.workInstructionItems = workInstructionItems;
    }

    public List<Integer> getUsageTypeIds() {
        return usageTypeIds;
    }

    public void setUsageTypeIds(List<Integer> usageTypeIds) {
        this.usageTypeIds = usageTypeIds;
    }

    public List<Integer> getProductAttributeIds() {
        return productAttributeIds;
    }

    public void setProductAttributeIds(List<Integer> productAttributeIds) {
        this.productAttributeIds = productAttributeIds;
    }

    public List<TestLineLangSyncInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<TestLineLangSyncInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }

    public List<TestLineAnalyteRelSyncInfo> getTestAnalyteItems() {
        return testAnalyteItems;
    }

    public void setTestAnalyteItems(List<TestLineAnalyteRelSyncInfo> testAnalyteItems) {
        this.testAnalyteItems = testAnalyteItems;
    }

    public List<TestLineAnalyteLimitRelSyncInfo> getTestAnalyteLimitItems() {
        return testAnalyteLimitItems;
    }

    public void setTestAnalyteLimitItems(List<TestLineAnalyteLimitRelSyncInfo> testAnalyteLimitItems) {
        this.testAnalyteLimitItems = testAnalyteLimitItems;
    }

    public List<Integer> getApplicationFactorIds() {
        return applicationFactorIds;
    }

    public void setApplicationFactorIds(List<Integer> applicationFactorIds) {
        this.applicationFactorIds = applicationFactorIds;
    }
}
