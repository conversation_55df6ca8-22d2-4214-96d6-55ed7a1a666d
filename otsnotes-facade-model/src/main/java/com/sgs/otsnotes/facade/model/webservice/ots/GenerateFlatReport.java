/**
 * GenerateFlatReport.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GenerateFlatReport  implements java.io.Serializable {
    private String reportTypeName;

    private String instanceId;

    private String templateFilePath;

    private String instanceFilePath;

    private DataItem[] flatDataList;

    public GenerateFlatReport() {
    }

    public GenerateFlatReport(
           String reportTypeName,
           String instanceId,
           String templateFilePath,
           String instanceFilePath,
           DataItem[] flatDataList) {
           this.reportTypeName = reportTypeName;
           this.instanceId = instanceId;
           this.templateFilePath = templateFilePath;
           this.instanceFilePath = instanceFilePath;
           this.flatDataList = flatDataList;
    }


    /**
     * Gets the reportTypeName value for this GenerateFlatReport.
     * 
     * @return reportTypeName
     */
    public String getReportTypeName() {
        return reportTypeName;
    }


    /**
     * Sets the reportTypeName value for this GenerateFlatReport.
     * 
     * @param reportTypeName
     */
    public void setReportTypeName(String reportTypeName) {
        this.reportTypeName = reportTypeName;
    }


    /**
     * Gets the instanceId value for this GenerateFlatReport.
     * 
     * @return instanceId
     */
    public String getInstanceId() {
        return instanceId;
    }


    /**
     * Sets the instanceId value for this GenerateFlatReport.
     * 
     * @param instanceId
     */
    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }


    /**
     * Gets the templateFilePath value for this GenerateFlatReport.
     * 
     * @return templateFilePath
     */
    public String getTemplateFilePath() {
        return templateFilePath;
    }


    /**
     * Sets the templateFilePath value for this GenerateFlatReport.
     * 
     * @param templateFilePath
     */
    public void setTemplateFilePath(String templateFilePath) {
        this.templateFilePath = templateFilePath;
    }


    /**
     * Gets the instanceFilePath value for this GenerateFlatReport.
     * 
     * @return instanceFilePath
     */
    public String getInstanceFilePath() {
        return instanceFilePath;
    }


    /**
     * Sets the instanceFilePath value for this GenerateFlatReport.
     * 
     * @param instanceFilePath
     */
    public void setInstanceFilePath(String instanceFilePath) {
        this.instanceFilePath = instanceFilePath;
    }


    /**
     * Gets the flatDataList value for this GenerateFlatReport.
     * 
     * @return flatDataList
     */
    public DataItem[] getFlatDataList() {
        return flatDataList;
    }


    /**
     * Sets the flatDataList value for this GenerateFlatReport.
     * 
     * @param flatDataList
     */
    public void setFlatDataList(DataItem[] flatDataList) {
        this.flatDataList = flatDataList;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GenerateFlatReport)) {
            return false;
        }
        GenerateFlatReport other = (GenerateFlatReport) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.reportTypeName==null && other.getReportTypeName()==null) || 
             (this.reportTypeName!=null &&
              this.reportTypeName.equals(other.getReportTypeName()))) &&
            ((this.instanceId==null && other.getInstanceId()==null) || 
             (this.instanceId!=null &&
              this.instanceId.equals(other.getInstanceId()))) &&
            ((this.templateFilePath==null && other.getTemplateFilePath()==null) || 
             (this.templateFilePath!=null &&
              this.templateFilePath.equals(other.getTemplateFilePath()))) &&
            ((this.instanceFilePath==null && other.getInstanceFilePath()==null) || 
             (this.instanceFilePath!=null &&
              this.instanceFilePath.equals(other.getInstanceFilePath()))) &&
            ((this.flatDataList==null && other.getFlatDataList()==null) || 
             (this.flatDataList!=null &&
              java.util.Arrays.equals(this.flatDataList, other.getFlatDataList())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getReportTypeName() != null) {
            _hashCode += getReportTypeName().hashCode();
        }
        if (getInstanceId() != null) {
            _hashCode += getInstanceId().hashCode();
        }
        if (getTemplateFilePath() != null) {
            _hashCode += getTemplateFilePath().hashCode();
        }
        if (getInstanceFilePath() != null) {
            _hashCode += getInstanceFilePath().hashCode();
        }
        if (getFlatDataList() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getFlatDataList());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getFlatDataList(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GenerateFlatReport.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GenerateFlatReport"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("reportTypeName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "reportTypeName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("instanceId");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "instanceId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "templateFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("instanceFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "instanceFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("flatDataList");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "flatDataList"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItem"));
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
