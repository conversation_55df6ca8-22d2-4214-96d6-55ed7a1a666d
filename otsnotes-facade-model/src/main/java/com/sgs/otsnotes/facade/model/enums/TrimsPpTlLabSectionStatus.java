package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

public enum TrimsPpTlLabSectionStatus {
    Inactive(0, "Inactive"),
    Active(1, "Active");
    private int status;
    private String code;

    TrimsPpTlLabSectionStatus(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    static Map<Integer, TrimsPpTlLabSectionStatus> maps = new HashMap<>();
    static Map<String, TrimsPpTlLabSectionStatus> codeMaps = new HashMap<>();

    static {
        for (TrimsPpTlLabSectionStatus status : TrimsPpTlLabSectionStatus.values()) {
            maps.put(status.getStatus(), status);
            codeMaps.put(status.getCode().toLowerCase(), status);
        }
    };

    public static TrimsPpTlLabSectionStatus getStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    /**
     *
     * @param status
     * @param ppStatus
     * @return
     */
    public static boolean check(Integer status, TrimsPpTlLabSectionStatus... ppStatus) {
        if (status == null || !maps.containsKey(status.intValue()) || ppStatus == null || ppStatus.length <= 0){
            return false;
        }
        for (TrimsPpTlLabSectionStatus tlStatus: ppStatus){
            if (status.intValue() == tlStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    public static TrimsPpTlLabSectionStatus getCode(String code) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase())) {
            return null;
        }
        return codeMaps.get(code.toLowerCase());
    }

    /**
     *
     * @param code
     * @param ppStatuss
     * @return
     */
    public static boolean check(String code, TrimsPpTlLabSectionStatus... ppStatuss) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase()) || ppStatuss == null || ppStatuss.length <= 0){
            return false;
        }
        TrimsPpTlLabSectionStatus trimsPpStatus = codeMaps.get(code.toLowerCase());
        for (TrimsPpTlLabSectionStatus ppStatus: ppStatuss){
            if (ppStatus.getStatus() == trimsPpStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param ppStatus
     * @return
     */
    public boolean check(TrimsPpTlLabSectionStatus... ppStatus){
        if (ppStatus == null || ppStatus.length <= 0){
            return false;
        }
        for (TrimsPpTlLabSectionStatus tlStatus: ppStatus){
            if (this.getStatus() == tlStatus.getStatus()){
                return true;
            }
        }
        return false;
    }
}
