/**
 * QueryBusinessTemplateList2Response.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots.template;

public class QueryBusinessTemplateList2Response  implements java.io.Serializable {
    private com.sgs.otsnotes.facade.model.webservice.ots.template.TemplateListView[] queryBusinessTemplateList2Result;

    public QueryBusinessTemplateList2Response() {
    }

    public QueryBusinessTemplateList2Response(
           com.sgs.otsnotes.facade.model.webservice.ots.template.TemplateListView[] queryBusinessTemplateList2Result) {
           this.queryBusinessTemplateList2Result = queryBusinessTemplateList2Result;
    }


    /**
     * Gets the queryBusinessTemplateList2Result value for this QueryBusinessTemplateList2Response.
     * 
     * @return queryBusinessTemplateList2Result
     */
    public com.sgs.otsnotes.facade.model.webservice.ots.template.TemplateListView[] getQueryBusinessTemplateList2Result() {
        return queryBusinessTemplateList2Result;
    }


    /**
     * Sets the queryBusinessTemplateList2Result value for this QueryBusinessTemplateList2Response.
     * 
     * @param queryBusinessTemplateList2Result
     */
    public void setQueryBusinessTemplateList2Result(com.sgs.otsnotes.facade.model.webservice.ots.template.TemplateListView[] queryBusinessTemplateList2Result) {
        this.queryBusinessTemplateList2Result = queryBusinessTemplateList2Result;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof QueryBusinessTemplateList2Response)) {
            return false;
        }
        QueryBusinessTemplateList2Response other = (QueryBusinessTemplateList2Response) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.queryBusinessTemplateList2Result==null && other.getQueryBusinessTemplateList2Result()==null) || 
             (this.queryBusinessTemplateList2Result!=null &&
              java.util.Arrays.equals(this.queryBusinessTemplateList2Result, other.getQueryBusinessTemplateList2Result())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getQueryBusinessTemplateList2Result() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getQueryBusinessTemplateList2Result());
                 i++) {
                java.lang.Object obj = java.lang.reflect.Array.get(getQueryBusinessTemplateList2Result(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(QueryBusinessTemplateList2Response.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", ">QueryBusinessTemplateList2Response"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("queryBusinessTemplateList2Result");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "QueryBusinessTemplateList2Result"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", "TemplateListView"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://tempuri.org/", "TemplateListView"));
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
