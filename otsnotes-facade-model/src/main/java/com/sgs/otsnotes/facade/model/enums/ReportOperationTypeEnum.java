package com.sgs.otsnotes.facade.model.enums;

public enum ReportOperationTypeEnum {
    APPROVE("APPROVE", "REPORT_APPROVE"),
    DELIVERY_SOFT_COPY("REPORT_DELIVERY_SOFT_COPY", "DELIVERY_SOFT_COPY"),
    CONFIRM("CONFIRM", "REPORT_CONFIRM"),
    PENDING("PENDING", "REPORT_PENDING"),
    CA<PERSON><PERSON>("<PERSON><PERSON><PERSON>", "REPORT_CANCEL"),
    REJECT("REJECT", "REPORT_REJECT"),
    GENERATE("GENERATE", "REPORT_GENERATE"),
    REWORK("REWORK", "REPORT_REWORK"),
    RENEW_SAMPLE_INFO("RENEW_SAMPLE_INFO", "RENEW_SAMPLE_INFO"),
    RENEW_COVER_PAGE("RENEW_COVER_PAGE", "RENEW_COVER_PAGE"),
    ORDER_COPY("ORDER_COPY", "ORDER_COPY"),
    GENERATE_TEST_RESULT("GENERATE_TEST_RESULT", "GENERATE_TEST_RESULT"),
    RENEW_TEST_RESULT("RENEW_TEST_RESULT", "RENEW_TEST_RESULT"),
    RETEST_TEST_RESULT("RETEST_TEST_RESULT", "RETEST_TEST_RESULT"),
    SAVE_TEST_RESULT("SAVE_TEST_RESULT", "SAVE_TEST_RESULT"),
    REVIEW_TEST_RESULT("REVIEW_TEST_RESULT", "REVIEW_TEST_RESULT");

    private final String code;
    private final String type;

    ReportOperationTypeEnum(String code, String type) {
        this.code = code;
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public String getType() {
        return type;
    }
}
