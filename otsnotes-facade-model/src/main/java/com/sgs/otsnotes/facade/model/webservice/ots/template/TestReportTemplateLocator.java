/**
 * TestReportTemplateLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots.template;

public class TestReportTemplateLocator extends org.apache.axis.client.Service implements com.sgs.otsnotes.facade.model.webservice.ots.template.TestReportTemplate {

    public TestReportTemplateLocator() {
    }


    public TestReportTemplateLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public TestReportTemplateLocator(java.lang.String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for TestReportTemplateSoap
    private java.lang.String TestReportTemplateSoap_address = "http://**************/OTS/Services/TestReportTemplate.asmx";

    public java.lang.String getTestReportTemplateSoapAddress() {
        return TestReportTemplateSoap_address;
    }

    // The WSDD service name defaults to the port name.
    private java.lang.String TestReportTemplateSoapWSDDServiceName = "TestReportTemplateSoap";

    public java.lang.String getTestReportTemplateSoapWSDDServiceName() {
        return TestReportTemplateSoapWSDDServiceName;
    }

    public void setTestReportTemplateSoapWSDDServiceName(java.lang.String name) {
        TestReportTemplateSoapWSDDServiceName = name;
    }

    public com.sgs.otsnotes.facade.model.webservice.ots.template.TestReportTemplateSoap getTestReportTemplateSoap() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(TestReportTemplateSoap_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getTestReportTemplateSoap(endpoint);
    }

    public com.sgs.otsnotes.facade.model.webservice.ots.template.TestReportTemplateSoap getTestReportTemplateSoap(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
           com.sgs.otsnotes.facade.model.webservice.ots.template.TestReportTemplateSoapStub _stub = new com.sgs.otsnotes.facade.model.webservice.ots.template.TestReportTemplateSoapStub(portAddress, this);
            _stub.setPortName(getTestReportTemplateSoapWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setTestReportTemplateSoapEndpointAddress(java.lang.String address) {
        TestReportTemplateSoap_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (com.sgs.otsnotes.facade.model.webservice.ots.template.TestReportTemplateSoap.class.isAssignableFrom(serviceEndpointInterface)) {
               com.sgs.otsnotes.facade.model.webservice.ots.template.TestReportTemplateSoapStub _stub = new com.sgs.otsnotes.facade.model.webservice.ots.template.TestReportTemplateSoapStub(new java.net.URL(TestReportTemplateSoap_address), this);
                _stub.setPortName(getTestReportTemplateSoapWSDDServiceName());
                return _stub;
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        java.lang.String inputPortName = portName.getLocalPart();
        if ("TestReportTemplateSoap".equals(inputPortName)) {
            return getTestReportTemplateSoap();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://tempuri.org/", "TestReportTemplate");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://tempuri.org/", "TestReportTemplateSoap"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(java.lang.String portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        
if ("TestReportTemplateSoap".equals(portName)) {
            setTestReportTemplateSoapEndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
