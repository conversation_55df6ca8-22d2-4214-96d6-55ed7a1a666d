package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/1 16:44
 */
public class UnitLanguageSyncInfo extends PrintFriendliness {
    /**
     * "otherLanguageItems": [{
     *  	"unitShortDepiction": "<font style=\"FONT-FAMILY: Arial;FONT-SIZE: 10pt;COLOR: #000000;\">cfu/50cm<sup>2需要</sup></font>",
     *  	"unitDepiction": "<font color=\"#000000\" face=\"Arial\"><span style=\"font-size: 13.3333px;\">在</span></font>",
     *  	"languageId": "2",
     *  	"languageCode": "CHI"
     *  }],
     */
    /**
     *
     */
    private Integer languageId;
    /**
     *
     */
    private String languageCode;
    /**
     *
     */
    private String unitShortDepiction;
    /**
     *
     */
    private String unitDepiction;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getUnitShortDepiction() {
        return unitShortDepiction;
    }

    public void setUnitShortDepiction(String unitShortDepiction) {
        this.unitShortDepiction = unitShortDepiction;
    }

    public String getUnitDepiction() {
        return unitDepiction;
    }

    public void setUnitDepiction(String unitDepiction) {
        this.unitDepiction = unitDepiction;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }
}
