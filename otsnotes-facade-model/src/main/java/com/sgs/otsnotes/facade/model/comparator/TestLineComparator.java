package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.common.NumberUtils;
import com.sgs.otsnotes.facade.model.info.testline.TestLinePpInfo;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;

public class TestLineComparator implements Comparator<TestLinePpInfo> {
    /**
     * 是否为升序
     */
    private boolean isAsc;

    private boolean hasPpTem;

    private boolean isSpecial;

    private boolean isOB;
    /**
     *
     * @param isAsc
     */
    public TestLineComparator(boolean isAsc, boolean hasPpTem, boolean isSpecial, boolean isOB) {
        this.isAsc = isAsc;
        this.hasPpTem = hasPpTem;
        this.isSpecial = isSpecial;
        this.isOB = isOB;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(TestLinePpInfo o1, TestLinePpInfo o2) {
        if (o1.getPpOrderingSeq() == null){
            o1.setPpOrderingSeq(Integer.MAX_VALUE);
        }
        if (o2.getPpOrderingSeq() == null){
            o2.setPpOrderingSeq(Integer.MAX_VALUE);
        }
        if (o1.getPpNo() == null){
            o1.setPpNo(0);
        }
        if (o2.getPpNo() == null){
            o2.setPpNo(0);
        }
        if (o1.getIfNotPP() == null){
            o1.setIfNotPP(0);
        }
        if (o2.getIfNotPP() == null){
            o2.setIfNotPP(0);
        }
        if (o1.getLabSectionSeq() == null){
            //o1.setLabSectionSeq(0);
            //if(isOB && !isSpecial){
                o1.setLabSectionSeq(Integer.MAX_VALUE);
            //}

        }
        if (o2.getLabSectionSeq() == null){
            //o2.setLabSectionSeq(0);
            //if(isOB && !isSpecial){
                o2.setLabSectionSeq(Integer.MAX_VALUE);
            //}
        }
        if (o1.getSectionLevel() == null){
            o1.setSectionLevel("");
        }
        if (o2.getSectionLevel() == null){
            o2.setSectionLevel("");
        }
        if (o1.getTestLineSeq() == null){
            o1.setTestLineSeq(0);
        }
        if (o2.getTestLineSeq() == null){
            o2.setTestLineSeq(0);
        }
        if (o1.getEvaluationAlias() == null){
            o1.setEvaluationAlias("");
        }
        if (o2.getEvaluationAlias() == null){
            o2.setEvaluationAlias("");
        }
        if(o1.getSubContractLabCode() == null ){
            o1.setSubContractLabCode(StringUtils.EMPTY);
        }
        if(o2.getSubContractLabCode() == null ){
            o2.setSubContractLabCode(StringUtils.EMPTY);
        }
        if(o1.getSlimTestLineSeq() == null ){
            o1.setSlimTestLineSeq(Long.MIN_VALUE);
        }
        if(o2.getSlimTestLineSeq() == null ){
            o2.setSlimTestLineSeq(Long.MIN_VALUE);
        }
        if(o1.getFilename() == null){
            o1.setFilename("");
        }
        if(o2.getFilename() == null){
            o2.setFilename("");
        }
        int index = 0;


        if(isOB){//欧标
            if(isSpecial){
                index = o1.getIfNotPP().compareTo(o2.getIfNotPP());
                if (index < 0) {
                    return isAsc ? -1 : 1;
                }
                if (index > 0) {
                    return isAsc ? 1 : -1;
                }
                index = o1.getPpOrderingSeq().compareTo(o2.getPpOrderingSeq());
                if (index < 0) {
                    return isAsc ? -1 : 1;
                }
                if (index > 0) {
                    return isAsc ? 1 : -1;
                }
                index = o1.getPpNo().compareTo(o2.getPpNo());
                if (index < 0) {
                    return isAsc ? -1 : 1;
                }
                if (index > 0) {
                    return isAsc ? 1 : -1;
                }
                index = o1.getSectionLevel().compareTo(o2.getSectionLevel());
                if (index < 0) {
                    return isAsc ? -1 : 1;
                }
                if (index > 0) {
                    return isAsc ? 1 : -1;
                }
                index = Integer.compare(o1.getTestLineSeq(), o2.getTestLineSeq());
                if (index < 0) {
                    return isAsc ? -1 : 1;
                }
                if (index > 0) {
                    return isAsc ? 1 : -1;
                }
            }else {
                if(hasPpTem){
                    index = o1.getIfNotPP().compareTo(o2.getIfNotPP());
                    if (index < 0) {
                        return isAsc ? -1 : 1;
                    }
                    if (index > 0) {
                        return isAsc ? 1 : -1;
                    }
                    index = o1.getPpOrderingSeq().compareTo(o2.getPpOrderingSeq());
                    if (index < 0) {
                        return isAsc ? -1 : 1;
                    }
                    if (index > 0) {
                        return isAsc ? 1 : -1;
                    }
                    index = o1.getPpNo().compareTo(o2.getPpNo());
                    if (index < 0) {
                        return isAsc ? -1 : 1;
                    }
                    if (index > 0) {
                        return isAsc ? 1 : -1;
                    }
                }
                index = Integer.compare(o1.getLabSectionSeq(), o2.getLabSectionSeq());
                if (index < 0) {
                    return isAsc ? -1 : 1;
                }
                if (index > 0) {
                    return isAsc ? 1 : -1;
                }
            }
        }else { //国标
            index = o1.getIfNotPP().compareTo(o2.getIfNotPP());
            if (index < 0) {
                return isAsc ? -1 : 1;
            }
            if (index > 0) {
                return isAsc ? 1 : -1;
            }
            index = o1.getPpOrderingSeq().compareTo(o2.getPpOrderingSeq());
            if (index < 0) {
                return isAsc ? -1 : 1;
            }
            if (index > 0) {
                return isAsc ? 1 : -1;
            }
            index = o1.getPpNo().compareTo(o2.getPpNo());
            if (index < 0) {
                return isAsc ? -1 : 1;
            }
            if (index > 0) {
                return isAsc ? 1 : -1;
            }
            index = o1.getFilename().compareTo(o2.getFilename());
            if (index < 0) {
                return isAsc ? -1 : 1;
            }
            if (index > 0) {
                return isAsc ? 1 : -1;
            }
            //有样式的排前边
            index = o2.getHasStyle().compareTo(o1.getHasStyle());
            if (index < 0) {
                return isAsc ? -1 : 1;
            }
            if (index > 0) {
                return isAsc ? 1 : -1;
            }

            //PP表中有样式TL的排序？
            if(isSpecial){
                index = o1.getSectionLevel().compareTo(o2.getSectionLevel());
                if (index < 0) {
                    return isAsc ? -1 : 1;
                }
                if (index > 0) {
                    return isAsc ? 1 : -1;
                }
                index = Integer.compare(o1.getTestLineSeq(), o2.getTestLineSeq());
                if (index < 0) {
                    return isAsc ? -1 : 1;
                }
                if (index > 0) {
                    return isAsc ? 1 : -1;
                }
            }else {
                index = Integer.compare(o1.getLabSectionSeq(), o2.getLabSectionSeq());
                if (index < 0) {
                    return isAsc ? -1 : 1;
                }
                if (index > 0) {
                    return isAsc ? 1 : -1;
                }
            }
            //无样式
        }
        index = o1.getEvaluationAlias().compareTo(o2.getEvaluationAlias());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        //无样式
        index = o1.getTestLineId().compareTo(o2.getTestLineId());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }

        index = o1.getSlimTestLineSeq().compareTo(o2.getSlimTestLineSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
