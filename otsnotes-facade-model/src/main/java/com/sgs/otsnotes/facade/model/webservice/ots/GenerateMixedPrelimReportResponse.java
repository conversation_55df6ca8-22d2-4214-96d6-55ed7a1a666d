/**
 * GenerateMixedPrelimReportResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GenerateMixedPrelimReportResponse  implements java.io.Serializable {
    private boolean generateMixedPrelimReportResult;

    public GenerateMixedPrelimReportResponse() {
    }

    public GenerateMixedPrelimReportResponse(
           boolean generateMixedPrelimReportResult) {
           this.generateMixedPrelimReportResult = generateMixedPrelimReportResult;
    }


    /**
     * Gets the generateMixedPrelimReportResult value for this GenerateMixedPrelimReportResponse.
     * 
     * @return generateMixedPrelimReportResult
     */
    public boolean isGenerateMixedPrelimReportResult() {
        return generateMixedPrelimReportResult;
    }


    /**
     * Sets the generateMixedPrelimReportResult value for this GenerateMixedPrelimReportResponse.
     * 
     * @param generateMixedPrelimReportResult
     */
    public void setGenerateMixedPrelimReportResult(boolean generateMixedPrelimReportResult) {
        this.generateMixedPrelimReportResult = generateMixedPrelimReportResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GenerateMixedPrelimReportResponse)) {
            return false;
        }
        GenerateMixedPrelimReportResponse other = (GenerateMixedPrelimReportResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.generateMixedPrelimReportResult == other.isGenerateMixedPrelimReportResult();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += (isGenerateMixedPrelimReportResult() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GenerateMixedPrelimReportResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GenerateMixedPrelimReportResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("generateMixedPrelimReportResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "GenerateMixedPrelimReportResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
