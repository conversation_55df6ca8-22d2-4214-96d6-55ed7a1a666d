package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.math.BigDecimal;
import java.util.List;

public class PricingSyncInfo extends PrintFriendliness {

    /**
     *
     */
    private Long pricingItemDetailId;
    /**
     *
     */
    private String pricingType;
    /**
     *
     */
    private String status;
    /**
     *
     */
    private Integer versionIdentifier;
    /**
     *
     */
    private Integer productLineId;
    /**
     *
     */
    private String priceAttribute;
    /**
     *
     */
    private String priceLevel;
    /**
     *
     */
    private String priceLevelCode;
    /**
     *
     */
    private Integer locationId;
    /**
     *
     */
    private Integer countryId;
    /**
     *
     */
    private Integer regionId;
    /**
     *
     */
    private String serviceItemDescriptor;
    /**
     *
     */
    private BigDecimal itemPrice;
    /**
     *
     */
    private BigDecimal basicPrice;
    /**
     *
     */
    private BigDecimal maxPrice;
    /**
     *
     */
    private BigDecimal minPrice;
    /**
     *
     */
    private String currencyCode;
    /**
     *
     */
    private String remark;
    /**
     *
     */
    private Integer customerAccountId;
    /**
     *
     */
    private Integer onQuote;
    /**
     *
     */
    private Integer sequence;
    /**
     *
     */
    private String testAnalyteOperator;
    /**
     *
     */
    private List<Integer> testAnalyteIds;
    /**
     *
     */
    private String conditionOperator;
    /**
     *
     */
    private List<Integer> conditionIds;
    /**
     *
     */
    private PricingTatSyncInfo TAT;
    /**
     *
     */
    private List<PricingSampleSizesSyncInfo> sampleSizes;
    /**
     *
     */
    private List<PricingAciPriceItemsSyncInfo> aciPriceItems;
    /**
     *
     */
    private List<PricingTestLineAndProtocolsSyncInfo> testLineAndProtocols;
    /**
     *
     */
    private List<PricingOtherLanguageItemsSyncInfo> otherLanguageItems;

    public Long getPricingItemDetailId() {
        return pricingItemDetailId;
    }

    public void setPricingItemDetailId(Long pricingItemDetailId) {
        this.pricingItemDetailId = pricingItemDetailId;
    }

    public String getPricingType() {
        return pricingType;
    }

    public void setPricingType(String pricingType) {
        this.pricingType = pricingType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getVersionIdentifier() {
        return versionIdentifier;
    }

    public void setVersionIdentifier(Integer versionIdentifier) {
        this.versionIdentifier = versionIdentifier;
    }

    public Integer getProductLineId() {
        return productLineId;
    }

    public void setProductLineId(Integer productLineId) {
        this.productLineId = productLineId;
    }

    public String getPriceAttribute() {
        return priceAttribute;
    }

    public void setPriceAttribute(String priceAttribute) {
        this.priceAttribute = priceAttribute;
    }

    public String getPriceLevel() {
        return priceLevel;
    }

    public void setPriceLevel(String priceLevel) {
        this.priceLevel = priceLevel;
    }

    public String getPriceLevelCode() {
        return priceLevelCode;
    }

    public void setPriceLevelCode(String priceLevelCode) {
        this.priceLevelCode = priceLevelCode;
    }

    public Integer getLocationId() {
        return locationId;
    }

    public void setLocationId(Integer locationId) {
        this.locationId = locationId;
    }

    public Integer getCountryId() {
        return countryId;
    }

    public void setCountryId(Integer countryId) {
        this.countryId = countryId;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public String getServiceItemDescriptor() {
        return serviceItemDescriptor;
    }

    public void setServiceItemDescriptor(String serviceItemDescriptor) {
        this.serviceItemDescriptor = serviceItemDescriptor;
    }

    public BigDecimal getItemPrice() {
        return itemPrice;
    }

    public void setItemPrice(BigDecimal itemPrice) {
        this.itemPrice = itemPrice;
    }

    public BigDecimal getBasicPrice() {
        return basicPrice;
    }

    public void setBasicPrice(BigDecimal basicPrice) {
        this.basicPrice = basicPrice;
    }

    public BigDecimal getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(BigDecimal maxPrice) {
        this.maxPrice = maxPrice;
    }

    public BigDecimal getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(BigDecimal minPrice) {
        this.minPrice = minPrice;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getCustomerAccountId() {
        return customerAccountId;
    }

    public void setCustomerAccountId(Integer customerAccountId) {
        this.customerAccountId = customerAccountId;
    }

    public Integer getOnQuote() {
        return onQuote;
    }

    public void setOnQuote(Integer onQuote) {
        this.onQuote = onQuote;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getTestAnalyteOperator() {
        return testAnalyteOperator;
    }

    public void setTestAnalyteOperator(String testAnalyteOperator) {
        this.testAnalyteOperator = testAnalyteOperator;
    }

    public List<Integer> getTestAnalyteIds() {
        return testAnalyteIds;
    }

    public void setTestAnalyteIds(List<Integer> testAnalyteIds) {
        this.testAnalyteIds = testAnalyteIds;
    }

    public String getConditionOperator() {
        return conditionOperator;
    }

    public void setConditionOperator(String conditionOperator) {
        this.conditionOperator = conditionOperator;
    }

    public List<Integer> getConditionIds() {
        return conditionIds;
    }

    public void setConditionIds(List<Integer> conditionIds) {
        this.conditionIds = conditionIds;
    }

    public PricingTatSyncInfo getTAT() {
        return TAT;
    }

    public void setTAT(PricingTatSyncInfo TAT) {
        this.TAT = TAT;
    }

    public List<PricingSampleSizesSyncInfo> getSampleSizes() {
        return sampleSizes;
    }

    public void setSampleSizes(List<PricingSampleSizesSyncInfo> sampleSizes) {
        this.sampleSizes = sampleSizes;
    }

    public List<PricingAciPriceItemsSyncInfo> getAciPriceItems() {
        return aciPriceItems;
    }

    public void setAciPriceItems(List<PricingAciPriceItemsSyncInfo> aciPriceItems) {
        this.aciPriceItems = aciPriceItems;
    }

    public List<PricingTestLineAndProtocolsSyncInfo> getTestLineAndProtocols() {
        return testLineAndProtocols;
    }

    public void setTestLineAndProtocols(List<PricingTestLineAndProtocolsSyncInfo> testLineAndProtocols) {
        this.testLineAndProtocols = testLineAndProtocols;
    }

    public List<PricingOtherLanguageItemsSyncInfo> getOtherLanguageItems() {
        return otherLanguageItems;
    }

    public void setOtherLanguageItems(List<PricingOtherLanguageItemsSyncInfo> otherLanguageItems) {
        this.otherLanguageItems = otherLanguageItems;
    }

}
