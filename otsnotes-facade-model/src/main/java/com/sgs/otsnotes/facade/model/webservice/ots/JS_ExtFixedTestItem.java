/**
 * JS_ExtFixedTestItem.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_ExtFixedTestItem  extends Element implements java.io.Serializable {
    private String LTID;

    private String RBID;

    private int testItemType;

    private String testItemIds;

    public JS_ExtFixedTestItem() {
    }

    public JS_ExtFixedTestItem(
           String id,
           String name,
           String LTID,
           String RBID,
           int testItemType,
           String testItemIds) {
        super(
            id,
            name);
        this.LTID = LTID;
        this.RBID = RBID;
        this.testItemType = testItemType;
        this.testItemIds = testItemIds;
    }


    /**
     * Gets the LTID value for this JS_ExtFixedTestItem.
     * 
     * @return LTID
     */
    public String getLTID() {
        return LTID;
    }


    /**
     * Sets the LTID value for this JS_ExtFixedTestItem.
     * 
     * @param LTID
     */
    public void setLTID(String LTID) {
        this.LTID = LTID;
    }


    /**
     * Gets the RBID value for this JS_ExtFixedTestItem.
     * 
     * @return RBID
     */
    public String getRBID() {
        return RBID;
    }


    /**
     * Sets the RBID value for this JS_ExtFixedTestItem.
     * 
     * @param RBID
     */
    public void setRBID(String RBID) {
        this.RBID = RBID;
    }


    /**
     * Gets the testItemType value for this JS_ExtFixedTestItem.
     * 
     * @return testItemType
     */
    public int getTestItemType() {
        return testItemType;
    }


    /**
     * Sets the testItemType value for this JS_ExtFixedTestItem.
     * 
     * @param testItemType
     */
    public void setTestItemType(int testItemType) {
        this.testItemType = testItemType;
    }


    /**
     * Gets the testItemIds value for this JS_ExtFixedTestItem.
     * 
     * @return testItemIds
     */
    public String getTestItemIds() {
        return testItemIds;
    }


    /**
     * Sets the testItemIds value for this JS_ExtFixedTestItem.
     * 
     * @param testItemIds
     */
    public void setTestItemIds(String testItemIds) {
        this.testItemIds = testItemIds;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_ExtFixedTestItem)) {
            return false;
        }
        JS_ExtFixedTestItem other = (JS_ExtFixedTestItem) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.LTID==null && other.getLTID()==null) || 
             (this.LTID!=null &&
              this.LTID.equals(other.getLTID()))) &&
            ((this.RBID==null && other.getRBID()==null) || 
             (this.RBID!=null &&
              this.RBID.equals(other.getRBID()))) &&
            this.testItemType == other.getTestItemType() &&
            ((this.testItemIds==null && other.getTestItemIds()==null) || 
             (this.testItemIds!=null &&
              this.testItemIds.equals(other.getTestItemIds())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getLTID() != null) {
            _hashCode += getLTID().hashCode();
        }
        if (getRBID() != null) {
            _hashCode += getRBID().hashCode();
        }
        _hashCode += getTestItemType();
        if (getTestItemIds() != null) {
            _hashCode += getTestItemIds().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_ExtFixedTestItem.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_ExtFixedTestItem"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("LTID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "LTID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("RBID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RBID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("testItemType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TestItemType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("testItemIds");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TestItemIds"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
