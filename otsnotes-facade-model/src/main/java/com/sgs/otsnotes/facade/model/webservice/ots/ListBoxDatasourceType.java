/**
 * ListBoxDatasourceType.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ListBoxDatasourceType implements java.io.Serializable {
    private String _value_;
    private static java.util.HashMap _table_ = new java.util.HashMap();

    // Constructor
    protected ListBoxDatasourceType(String value) {
        _value_ = value;
        _table_.put(_value_,this);
    }

    public static final String _NA = "NA";
    public static final String _DataDictionary = "DataDictionary";
    public static final String _UserDataDictionary = "UserDataDictionary";
    public static final String _EquipmentNo = "EquipmentNo";
    public static final String _FreeInput = "FreeInput";
    public static final String _ServiceType = "ServiceType";
    public static final String _TestPackageFactor = "TestPackageFactor";
    public static final String _Country = "Country";
    public static final String _ProductCategory = "ProductCategory";
    public static final String _JingDongQtType = "JingDongQtType";
    public static final ListBoxDatasourceType NA = new ListBoxDatasourceType(_NA);
    public static final ListBoxDatasourceType DataDictionary = new ListBoxDatasourceType(_DataDictionary);
    public static final ListBoxDatasourceType UserDataDictionary = new ListBoxDatasourceType(_UserDataDictionary);
    public static final ListBoxDatasourceType EquipmentNo = new ListBoxDatasourceType(_EquipmentNo);
    public static final ListBoxDatasourceType FreeInput = new ListBoxDatasourceType(_FreeInput);
    public static final ListBoxDatasourceType ServiceType = new ListBoxDatasourceType(_ServiceType);
    public static final ListBoxDatasourceType TestPackageFactor = new ListBoxDatasourceType(_TestPackageFactor);
    public static final ListBoxDatasourceType Country = new ListBoxDatasourceType(_Country);
    public static final ListBoxDatasourceType ProductCategory = new ListBoxDatasourceType(_ProductCategory);
    public static final ListBoxDatasourceType JingDongQtType = new ListBoxDatasourceType(_JingDongQtType);
    public String getValue() { return _value_;}
    public static ListBoxDatasourceType fromValue(String value)
          throws IllegalArgumentException {
        ListBoxDatasourceType enumeration = (ListBoxDatasourceType)
            _table_.get(value);
        if (enumeration==null) {
            throw new IllegalArgumentException();
        }
        return enumeration;
    }
    public static ListBoxDatasourceType fromString(String value)
          throws IllegalArgumentException {
        return fromValue(value);
    }
    public boolean equals(Object obj) {return (obj == this);}
    public int hashCode() { return toString().hashCode();}
    public String toString() { return _value_;}
    public Object readResolve() throws java.io.ObjectStreamException { return fromValue(_value_);}
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumSerializer(
            _javaType, _xmlType);
    }
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumDeserializer(
            _javaType, _xmlType);
    }
    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ListBoxDatasourceType.class);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ListBoxDatasourceType"));
    }
    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

}
