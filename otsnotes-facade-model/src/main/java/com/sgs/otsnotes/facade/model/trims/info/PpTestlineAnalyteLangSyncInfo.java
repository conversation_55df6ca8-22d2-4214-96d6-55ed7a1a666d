package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class PpTestlineAnalyteLangSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer testAnalyteId;
    /**
     *
     */
    private String testAnalyteDescAlias;

    public Integer getTestAnalyteId() {
        return testAnalyteId;
    }

    public void setTestAnalyteId(Integer testAnalyteId) {
        this.testAnalyteId = testAnalyteId;
    }

    public String getTestAnalyteDescAlias() {
        return testAnalyteDescAlias;
    }

    public void setTestAnalyteDescAlias(String testAnalyteDescAlias) {
        this.testAnalyteDescAlias = testAnalyteDescAlias;
    }
}
