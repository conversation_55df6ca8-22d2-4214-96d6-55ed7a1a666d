package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

public enum CustomerValidatePassIfEnum {
    Exclude( "Exclude"),
    Include( "Include"),
    ;
    private String code;

    CustomerValidatePassIfEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    static Map<String, CustomerValidatePassIfEnum> typeMaps = new HashMap();
    static {
        for (CustomerValidatePassIfEnum passIfEnum : CustomerValidatePassIfEnum.values()) {
            typeMaps.put(passIfEnum.getCode(), passIfEnum);
        }
    }

    public static CustomerValidatePassIfEnum findType(String code) {
        if (code == null || !typeMaps.containsKey(code)) {
            return null;
        }
        return typeMaps.get(code);
    }

    /**
     *
     * @param type
     * @param analyteType
     * @return
     */
    public static boolean check(String type, CustomerValidatePassIfEnum analyteType) {
        if (type == null || !typeMaps.containsKey(type)){
            return false;
        }
        return typeMaps.get(type) == analyteType;
    }


}
