/**
 * TemplateDataItem.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class TemplateDataItem  implements java.io.Serializable {
    private String dataKey;

    private String dataValue;

    private DataItemFormat format;

    public TemplateDataItem() {
    }

    public TemplateDataItem(
           String dataKey,
           String dataValue,
           DataItemFormat format) {
           this.dataKey = dataKey;
           this.dataValue = dataValue;
           this.format = format;
    }


    /**
     * Gets the dataKey value for this TemplateDataItem.
     * 
     * @return dataKey
     */
    public String getDataKey() {
        return dataKey;
    }


    /**
     * Sets the dataKey value for this TemplateDataItem.
     * 
     * @param dataKey
     */
    public void setDataKey(String dataKey) {
        this.dataKey = dataKey;
    }


    /**
     * Gets the dataValue value for this TemplateDataItem.
     * 
     * @return dataValue
     */
    public String getDataValue() {
        return dataValue;
    }


    /**
     * Sets the dataValue value for this TemplateDataItem.
     * 
     * @param dataValue
     */
    public void setDataValue(String dataValue) {
        this.dataValue = dataValue;
    }


    /**
     * Gets the format value for this TemplateDataItem.
     * 
     * @return format
     */
    public DataItemFormat getFormat() {
        return format;
    }


    /**
     * Sets the format value for this TemplateDataItem.
     * 
     * @param format
     */
    public void setFormat(DataItemFormat format) {
        this.format = format;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof TemplateDataItem)) {
            return false;
        }
        TemplateDataItem other = (TemplateDataItem) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.dataKey==null && other.getDataKey()==null) || 
             (this.dataKey!=null &&
              this.dataKey.equals(other.getDataKey()))) &&
            ((this.dataValue==null && other.getDataValue()==null) || 
             (this.dataValue!=null &&
              this.dataValue.equals(other.getDataValue()))) &&
            ((this.format==null && other.getFormat()==null) || 
             (this.format!=null &&
              this.format.equals(other.getFormat())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getDataKey() != null) {
            _hashCode += getDataKey().hashCode();
        }
        if (getDataValue() != null) {
            _hashCode += getDataValue().hashCode();
        }
        if (getFormat() != null) {
            _hashCode += getFormat().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(TemplateDataItem.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateDataItem"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("dataKey");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataKey"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("dataValue");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataValue"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("format");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Format"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DataItemFormat"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
