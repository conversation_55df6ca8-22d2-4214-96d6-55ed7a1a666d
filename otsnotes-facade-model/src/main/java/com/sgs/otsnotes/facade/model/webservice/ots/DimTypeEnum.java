/**
 * DimTypeEnum.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class DimTypeEnum implements java.io.Serializable {
    private String _value_;
    private static java.util.HashMap _table_ = new java.util.HashMap();

    // Constructor
    protected DimTypeEnum(String value) {
        _value_ = value;
        _table_.put(_value_,this);
    }

    public static final String _TestItem = "TestItem";
    public static final String _TestMethod = "TestMethod";
    public static final String _Sample = "Sample";
    public static final String _WathTimes = "WathTimes";
    public static final String _TestPoint = "TestPoint";
    public static final String _ChemicalElement = "ChemicalElement";
    public static final String _SpecimenInstance = "SpecimenInstance";
    public static final String _ChemicLimit = "ChemicLimit";
    public static final String _RegionType = "RegionType";
    public static final String _TestStandard = "TestStandard";
    public static final String _ChemicUnit = "ChemicUnit";
    public static final DimTypeEnum TestItem = new DimTypeEnum(_TestItem);
    public static final DimTypeEnum TestMethod = new DimTypeEnum(_TestMethod);
    public static final DimTypeEnum Sample = new DimTypeEnum(_Sample);
    public static final DimTypeEnum WathTimes = new DimTypeEnum(_WathTimes);
    public static final DimTypeEnum TestPoint = new DimTypeEnum(_TestPoint);
    public static final DimTypeEnum ChemicalElement = new DimTypeEnum(_ChemicalElement);
    public static final DimTypeEnum SpecimenInstance = new DimTypeEnum(_SpecimenInstance);
    public static final DimTypeEnum ChemicLimit = new DimTypeEnum(_ChemicLimit);
    public static final DimTypeEnum RegionType = new DimTypeEnum(_RegionType);
    public static final DimTypeEnum TestStandard = new DimTypeEnum(_TestStandard);
    public static final DimTypeEnum ChemicUnit = new DimTypeEnum(_ChemicUnit);
    public String getValue() { return _value_;}
    public static DimTypeEnum fromValue(String value)
          throws IllegalArgumentException {
        DimTypeEnum enumeration = (DimTypeEnum)
            _table_.get(value);
        if (enumeration==null) {
            throw new IllegalArgumentException();
        }
        return enumeration;
    }
    public static DimTypeEnum fromString(String value)
          throws IllegalArgumentException {
        return fromValue(value);
    }
    public boolean equals(Object obj) {return (obj == this);}
    public int hashCode() { return toString().hashCode();}
    public String toString() { return _value_;}
    public Object readResolve() throws java.io.ObjectStreamException { return fromValue(_value_);}
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumSerializer(
            _javaType, _xmlType);
    }
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumDeserializer(
            _javaType, _xmlType);
    }
    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(DimTypeEnum.class);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DimTypeEnum"));
    }
    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

}
