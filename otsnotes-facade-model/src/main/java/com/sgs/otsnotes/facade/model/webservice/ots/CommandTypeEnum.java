/**
 * CommandTypeEnum.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class CommandTypeEnum implements java.io.Serializable {
    private String _value_;
    private static java.util.HashMap _table_ = new java.util.HashMap();

    // Constructor
    protected CommandTypeEnum(String value) {
        _value_ = value;
        _table_.put(_value_,this);
    }

    public static final String _Error = "Error";
    public static final String _GetTemplateHtml = "GetTemplateHtml";
    public static final String _DefineInputField = "DefineInputField";
    public static final String _RemoveInputField = "RemoveInputField";
    public static final String _DefineSection = "DefineSection";
    public static final String _RemoveSection = "RemoveSection";
    public static final String _DefineImage = "DefineImage";
    public static final String _RemoveImage = "RemoveImage";
    public static final String _DefineTable = "DefineTable";
    public static final String _RemoveTableRepeat = "RemoveTableRepeat";
    public static final String _DefineOptional = "DefineOptional";
    public static final String _RemoveOptional = "RemoveOptional";
    public static final String _DefineRequirementHeader = "DefineRequirementHeader";
    public static final String _RemoveRequirementHeader = "RemoveRequirementHeader";
    public static final String _SaveTemplateDefine = "SaveTemplateDefine";
    public static final String _DefineComponentRegion = "DefineComponentRegion";
    public static final String _RemoveComponentRegion = "RemoveComponentRegion";
    public static final String _DefineExclusiveRemark = "DefineExclusiveRemark";
    public static final String _RemoveExclusiveRemark = "RemoveExclusiveRemark";
    public static final String _DefineGeneralArea = "DefineGeneralArea";
    public static final String _RemoveGeneralArea = "RemoveGeneralArea";
    public static final String _DefineConditionRegion = "DefineConditionRegion";
    public static final String _RemoveConditionRegion = "RemoveConditionRegion";
    public static final String _DefineSubClausesTitleRemark = "DefineSubClausesTitleRemark";
    public static final String _RemoveSubClausesTitleRemark = "RemoveSubClausesTitleRemark";
    public static final String _DefineChemicTableDims = "DefineChemicTableDims";
    public static final String _RemoveChemicTableDims = "RemoveChemicTableDims";
    public static final String _DefineSequenceField = "DefineSequenceField";
    public static final String _RemoveSequenceField = "RemoveSequenceField";
    public static final String _DefineFormField = "DefineFormField";
    public static final String _RemoveFormField = "RemoveFormField";
    public static final String _DefineSpecificTableProperty = "DefineSpecificTableProperty";
    public static final String _RemoveSpecificTableProperty = "RemoveSpecificTableProperty";
    public static final String _DefineExcelWorksheet = "DefineExcelWorksheet";
    public static final String _DefineExcelRepeatTable = "DefineExcelRepeatTable";
    public static final String _RemoveExcelRepeatTable = "RemoveExcelRepeatTable";
    public static final String _DefineExcelRepeatTableType = "DefineExcelRepeatTableType";
    public static final String _RemoveExcelRepeatTableType = "RemoveExcelRepeatTableType";
    public static final String _DefineExcelFixedTestItem = "DefineExcelFixedTestItem";
    public static final String _RemoveExcelFixedTestItem = "RemoveExcelFixedTestItem";
    public static final String _GetTemplateConfiguration = "GetTemplateConfiguration";
    public static final String _SplitRun = "SplitRun";
    public static final String _CombineRuns = "CombineRuns";
    public static final String _NewGuid = "NewGuid";
    public static final String _GetExcelImageName = "GetExcelImageName";
    public static final String _InsertBookmark = "InsertBookmark";
    public static final String _InsertFormFieldName = "InsertFormFieldName";
    public static final String _GetWorkSheetHtml = "GetWorkSheetHtml";
    public static final String _GetWorksheetHtmlSize = "GetWorksheetHtmlSize";
    public static final String _GetWorkSheetConfiguration = "GetWorkSheetConfiguration";
    public static final String _GetWorkSheetConfigurationSize = "GetWorkSheetConfigurationSize";
    public static final String _DefineConclusion = "DefineConclusion";
    public static final String _RemoveConclusion = "RemoveConclusion";
    public static final String _IsExistTemplateConfigurationCache = "IsExistTemplateConfigurationCache";
    public static final String _RemoveTemplateConfigurationCache = "RemoveTemplateConfigurationCache";
    public static final String _RemoveAllSettings = "RemoveAllSettings";
    public static final String _GetWorksheetNamesForExcel = "GetWorksheetNamesForExcel";
    public static final String _DefineChapterTable = "DefineChapterTable";
    public static final String _RemoveChapterTable = "RemoveChapterTable";
    public static final String _DefineChapterTableRow = "DefineChapterTableRow";
    public static final String _RemoveChapterTableRow = "RemoveChapterTableRow";
    public static final String _IgnoreCommandType = "IgnoreCommandType";
    public static final CommandTypeEnum Error = new CommandTypeEnum(_Error);
    public static final CommandTypeEnum GetTemplateHtml = new CommandTypeEnum(_GetTemplateHtml);
    public static final CommandTypeEnum DefineInputField = new CommandTypeEnum(_DefineInputField);
    public static final CommandTypeEnum RemoveInputField = new CommandTypeEnum(_RemoveInputField);
    public static final CommandTypeEnum DefineSection = new CommandTypeEnum(_DefineSection);
    public static final CommandTypeEnum RemoveSection = new CommandTypeEnum(_RemoveSection);
    public static final CommandTypeEnum DefineImage = new CommandTypeEnum(_DefineImage);
    public static final CommandTypeEnum RemoveImage = new CommandTypeEnum(_RemoveImage);
    public static final CommandTypeEnum DefineTable = new CommandTypeEnum(_DefineTable);
    public static final CommandTypeEnum RemoveTableRepeat = new CommandTypeEnum(_RemoveTableRepeat);
    public static final CommandTypeEnum DefineOptional = new CommandTypeEnum(_DefineOptional);
    public static final CommandTypeEnum RemoveOptional = new CommandTypeEnum(_RemoveOptional);
    public static final CommandTypeEnum DefineRequirementHeader = new CommandTypeEnum(_DefineRequirementHeader);
    public static final CommandTypeEnum RemoveRequirementHeader = new CommandTypeEnum(_RemoveRequirementHeader);
    public static final CommandTypeEnum SaveTemplateDefine = new CommandTypeEnum(_SaveTemplateDefine);
    public static final CommandTypeEnum DefineComponentRegion = new CommandTypeEnum(_DefineComponentRegion);
    public static final CommandTypeEnum RemoveComponentRegion = new CommandTypeEnum(_RemoveComponentRegion);
    public static final CommandTypeEnum DefineExclusiveRemark = new CommandTypeEnum(_DefineExclusiveRemark);
    public static final CommandTypeEnum RemoveExclusiveRemark = new CommandTypeEnum(_RemoveExclusiveRemark);
    public static final CommandTypeEnum DefineGeneralArea = new CommandTypeEnum(_DefineGeneralArea);
    public static final CommandTypeEnum RemoveGeneralArea = new CommandTypeEnum(_RemoveGeneralArea);
    public static final CommandTypeEnum DefineConditionRegion = new CommandTypeEnum(_DefineConditionRegion);
    public static final CommandTypeEnum RemoveConditionRegion = new CommandTypeEnum(_RemoveConditionRegion);
    public static final CommandTypeEnum DefineSubClausesTitleRemark = new CommandTypeEnum(_DefineSubClausesTitleRemark);
    public static final CommandTypeEnum RemoveSubClausesTitleRemark = new CommandTypeEnum(_RemoveSubClausesTitleRemark);
    public static final CommandTypeEnum DefineChemicTableDims = new CommandTypeEnum(_DefineChemicTableDims);
    public static final CommandTypeEnum RemoveChemicTableDims = new CommandTypeEnum(_RemoveChemicTableDims);
    public static final CommandTypeEnum DefineSequenceField = new CommandTypeEnum(_DefineSequenceField);
    public static final CommandTypeEnum RemoveSequenceField = new CommandTypeEnum(_RemoveSequenceField);
    public static final CommandTypeEnum DefineFormField = new CommandTypeEnum(_DefineFormField);
    public static final CommandTypeEnum RemoveFormField = new CommandTypeEnum(_RemoveFormField);
    public static final CommandTypeEnum DefineSpecificTableProperty = new CommandTypeEnum(_DefineSpecificTableProperty);
    public static final CommandTypeEnum RemoveSpecificTableProperty = new CommandTypeEnum(_RemoveSpecificTableProperty);
    public static final CommandTypeEnum DefineExcelWorksheet = new CommandTypeEnum(_DefineExcelWorksheet);
    public static final CommandTypeEnum DefineExcelRepeatTable = new CommandTypeEnum(_DefineExcelRepeatTable);
    public static final CommandTypeEnum RemoveExcelRepeatTable = new CommandTypeEnum(_RemoveExcelRepeatTable);
    public static final CommandTypeEnum DefineExcelRepeatTableType = new CommandTypeEnum(_DefineExcelRepeatTableType);
    public static final CommandTypeEnum RemoveExcelRepeatTableType = new CommandTypeEnum(_RemoveExcelRepeatTableType);
    public static final CommandTypeEnum DefineExcelFixedTestItem = new CommandTypeEnum(_DefineExcelFixedTestItem);
    public static final CommandTypeEnum RemoveExcelFixedTestItem = new CommandTypeEnum(_RemoveExcelFixedTestItem);
    public static final CommandTypeEnum GetTemplateConfiguration = new CommandTypeEnum(_GetTemplateConfiguration);
    public static final CommandTypeEnum SplitRun = new CommandTypeEnum(_SplitRun);
    public static final CommandTypeEnum CombineRuns = new CommandTypeEnum(_CombineRuns);
    public static final CommandTypeEnum NewGuid = new CommandTypeEnum(_NewGuid);
    public static final CommandTypeEnum GetExcelImageName = new CommandTypeEnum(_GetExcelImageName);
    public static final CommandTypeEnum InsertBookmark = new CommandTypeEnum(_InsertBookmark);
    public static final CommandTypeEnum InsertFormFieldName = new CommandTypeEnum(_InsertFormFieldName);
    public static final CommandTypeEnum GetWorkSheetHtml = new CommandTypeEnum(_GetWorkSheetHtml);
    public static final CommandTypeEnum GetWorksheetHtmlSize = new CommandTypeEnum(_GetWorksheetHtmlSize);
    public static final CommandTypeEnum GetWorkSheetConfiguration = new CommandTypeEnum(_GetWorkSheetConfiguration);
    public static final CommandTypeEnum GetWorkSheetConfigurationSize = new CommandTypeEnum(_GetWorkSheetConfigurationSize);
    public static final CommandTypeEnum DefineConclusion = new CommandTypeEnum(_DefineConclusion);
    public static final CommandTypeEnum RemoveConclusion = new CommandTypeEnum(_RemoveConclusion);
    public static final CommandTypeEnum IsExistTemplateConfigurationCache = new CommandTypeEnum(_IsExistTemplateConfigurationCache);
    public static final CommandTypeEnum RemoveTemplateConfigurationCache = new CommandTypeEnum(_RemoveTemplateConfigurationCache);
    public static final CommandTypeEnum RemoveAllSettings = new CommandTypeEnum(_RemoveAllSettings);
    public static final CommandTypeEnum GetWorksheetNamesForExcel = new CommandTypeEnum(_GetWorksheetNamesForExcel);
    public static final CommandTypeEnum DefineChapterTable = new CommandTypeEnum(_DefineChapterTable);
    public static final CommandTypeEnum RemoveChapterTable = new CommandTypeEnum(_RemoveChapterTable);
    public static final CommandTypeEnum DefineChapterTableRow = new CommandTypeEnum(_DefineChapterTableRow);
    public static final CommandTypeEnum RemoveChapterTableRow = new CommandTypeEnum(_RemoveChapterTableRow);
    public static final CommandTypeEnum IgnoreCommandType = new CommandTypeEnum(_IgnoreCommandType);
    public String getValue() { return _value_;}
    public static CommandTypeEnum fromValue(String value)
          throws IllegalArgumentException {
        CommandTypeEnum enumeration = (CommandTypeEnum)
            _table_.get(value);
        if (enumeration==null) {
            throw new IllegalArgumentException();
        }
        return enumeration;
    }
    public static CommandTypeEnum fromString(String value)
          throws IllegalArgumentException {
        return fromValue(value);
    }
    public boolean equals(Object obj) {return (obj == this);}
    public int hashCode() { return toString().hashCode();}
    public String toString() { return _value_;}
    public Object readResolve() throws java.io.ObjectStreamException { return fromValue(_value_);}
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumSerializer(
            _javaType, _xmlType);
    }
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumDeserializer(
            _javaType, _xmlType);
    }
    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(CommandTypeEnum.class);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "CommandTypeEnum"));
    }
    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

}
