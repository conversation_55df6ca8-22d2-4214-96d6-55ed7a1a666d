package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum ConclusionCalcType {
    None(0, "None"),
    TestLine(1, "TestLine"),
    OriginalSample(2, "OriginalSample"),
    Section(4, "Section"),
    PP(8, "PP"),
    Report(16, "Section"),;
    @DictCodeField
    private int status;
    @DictLabelField
    private String code;

    ConclusionCalcType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return this.code;
    }

    static Map<Integer, ConclusionCalcType> maps = new HashMap<>();

    static {
        for (ConclusionCalcType calcType : ConclusionCalcType.values()) {
            maps.put(calcType.getStatus(), calcType);
        }
    }

    public static ConclusionCalcType findCode(Integer status) {
        if (status == null || !maps.containsKey(status)){
            return null;
        }
        return maps.get(status);
    }

    public static boolean check(Integer status) {
        if (status == null){
            return false;
        }
        return maps.containsKey(status);
    }

    /**
     *
     * @param status
     * @param calcType
     * @return
     */
    public static boolean check(Integer status, ConclusionCalcType calcType) {
        if (status == null || !maps.containsKey(status)){
            return false;
        }
        return maps.get(status) == calcType;
    }
}
