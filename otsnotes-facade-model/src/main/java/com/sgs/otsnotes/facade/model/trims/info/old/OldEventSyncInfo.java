package com.sgs.otsnotes.facade.model.trims.info.old;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;
import java.util.Map;

public final class OldEventSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private String type;

    /**
     *
     */
    private List<Long> id;

    /**
     *
     */
    private Map<Long, Long> eventIds;

    public List<Long> getId() {
        return id;
    }

    public void setId(List<Long> id) {
        this.id = id;
    }

    public Map<Long, Long> getEventIds() {
        return eventIds;
    }

    public void setEventIds(Map<Long, Long> eventIds) {
        this.eventIds = eventIds;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
