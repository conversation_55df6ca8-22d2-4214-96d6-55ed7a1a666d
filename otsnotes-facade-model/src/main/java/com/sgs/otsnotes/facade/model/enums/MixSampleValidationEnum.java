package com.sgs.otsnotes.facade.model.enums;
/**
 * 混测校验枚举
 * 1. 页面校验
 * 2. Approve校验
 */
public enum MixSampleValidationEnum {
    
    PAGE(1, "page"),
    APPROVE(2,  "approve");

    private Integer code;
    private String desc;

    MixSampleValidationEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
