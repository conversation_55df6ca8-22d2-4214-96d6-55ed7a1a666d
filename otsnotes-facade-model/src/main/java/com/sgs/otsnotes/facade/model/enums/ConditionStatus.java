package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum ConditionStatus {
    NoCondition(1001, "No Condition"),
    Confirmed(1002, "Confirmed"),
    UnConfirmed(1003, "UnConfirmed");
    @DictCodeField
    private Integer status;
    @DictLabelField
    private String message;

    ConditionStatus(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, ConditionStatus> maps = new HashMap<Integer, ConditionStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ConditionStatus enu : ConditionStatus.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static ConditionStatus findStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    /**
     *
     * @param status
     * @param conditionStatus
     * @return
     */
    public static boolean check(Integer status, ConditionStatus conditionStatus) {
        if (status == null || !maps.containsKey(status)){
            return false;
        }
        return maps.get(status) == conditionStatus;
    }
}
