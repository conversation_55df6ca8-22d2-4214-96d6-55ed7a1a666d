package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class TestLineAnalyteLangSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private Integer testAnalyteId;
    /**
     *
     */
    private String descriptionAlias;

    public Integer getTestAnalyteId() {
        return testAnalyteId;
    }

    public void setTestAnalyteId(Integer testAnalyteId) {
        this.testAnalyteId = testAnalyteId;
    }

    public String getDescriptionAlias() {
        return descriptionAlias;
    }

    public void setDescriptionAlias(String descriptionAlias) {
        this.descriptionAlias = descriptionAlias;
    }
}
