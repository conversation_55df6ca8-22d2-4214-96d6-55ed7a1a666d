package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/3 10:38
 */
public class TestLimitApplicabilityInfo extends PrintFriendliness {

    private Integer applicablityId;

    private List<Integer> productAttributeIds;

    private List<Integer> testVersionIdentifiers;

    private List<Integer> standardVersionidentifiers;

    private List<Integer> regulationVersionIdentifiers;

    private List<Integer> conditionIds;

    public Integer getApplicablityId() {
        return applicablityId;
    }

    public void setApplicablityId(Integer applicablityId) {
        this.applicablityId = applicablityId;
    }

    public List<Integer> getProductAttributeIds() {
        return productAttributeIds;
    }

    public void setProductAttributeIds(List<Integer> productAttributeIds) {
        this.productAttributeIds = productAttributeIds;
    }

    public List<Integer> getTestVersionIdentifiers() {
        return testVersionIdentifiers;
    }

    public void setTestVersionIdentifiers(List<Integer> testVersionIdentifiers) {
        this.testVersionIdentifiers = testVersionIdentifiers;
    }

    public List<Integer> getStandardVersionidentifiers() {
        return standardVersionidentifiers;
    }

    public void setStandardVersionidentifiers(List<Integer> standardVersionidentifiers) {
        this.standardVersionidentifiers = standardVersionidentifiers;
    }

    public List<Integer> getRegulationVersionIdentifiers() {
        return regulationVersionIdentifiers;
    }

    public void setRegulationVersionIdentifiers(List<Integer> regulationVersionIdentifiers) {
        this.regulationVersionIdentifiers = regulationVersionIdentifiers;
    }

    public List<Integer> getConditionIds() {
        return conditionIds;
    }

    public void setConditionIds(List<Integer> conditionIds) {
        this.conditionIds = conditionIds;
    }
}
