/**
 * JS_SpecificTable.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_SpecificTable  extends Element implements java.io.Serializable {
    private SpecificTableTypeEnum specificTableType;

    public JS_SpecificTable() {
    }

    public JS_SpecificTable(
           String id,
           String name,
           SpecificTableTypeEnum specificTableType) {
        super(
            id,
            name);
        this.specificTableType = specificTableType;
    }


    /**
     * Gets the specificTableType value for this JS_SpecificTable.
     * 
     * @return specificTableType
     */
    public SpecificTableTypeEnum getSpecificTableType() {
        return specificTableType;
    }


    /**
     * Sets the specificTableType value for this JS_SpecificTable.
     * 
     * @param specificTableType
     */
    public void setSpecificTableType(SpecificTableTypeEnum specificTableType) {
        this.specificTableType = specificTableType;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_SpecificTable)) {
            return false;
        }
        JS_SpecificTable other = (JS_SpecificTable) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.specificTableType==null && other.getSpecificTableType()==null) || 
             (this.specificTableType!=null &&
              this.specificTableType.equals(other.getSpecificTableType())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getSpecificTableType() != null) {
            _hashCode += getSpecificTableType().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_SpecificTable.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_SpecificTable"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("specificTableType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SpecificTableType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "SpecificTableTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
