/**
 * JS_ExtWorksheet.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_ExtWorksheet  extends Element implements java.io.Serializable {
    private Element[] elements;

    private CellCriteria[] cellConfigurations;

    private String LTID;

    private String RBID;

    public JS_ExtWorksheet() {
    }

    public JS_ExtWorksheet(
           String id,
           String name,
           Element[] elements,
           CellCriteria[] cellConfigurations,
           String LTID,
           String RBID) {
        super(
            id,
            name);
        this.elements = elements;
        this.cellConfigurations = cellConfigurations;
        this.LTID = LTID;
        this.RBID = RBID;
    }


    /**
     * Gets the elements value for this JS_ExtWorksheet.
     * 
     * @return elements
     */
    public Element[] getElements() {
        return elements;
    }


    /**
     * Sets the elements value for this JS_ExtWorksheet.
     * 
     * @param elements
     */
    public void setElements(Element[] elements) {
        this.elements = elements;
    }


    /**
     * Gets the cellConfigurations value for this JS_ExtWorksheet.
     * 
     * @return cellConfigurations
     */
    public CellCriteria[] getCellConfigurations() {
        return cellConfigurations;
    }


    /**
     * Sets the cellConfigurations value for this JS_ExtWorksheet.
     * 
     * @param cellConfigurations
     */
    public void setCellConfigurations(CellCriteria[] cellConfigurations) {
        this.cellConfigurations = cellConfigurations;
    }


    /**
     * Gets the LTID value for this JS_ExtWorksheet.
     * 
     * @return LTID
     */
    public String getLTID() {
        return LTID;
    }


    /**
     * Sets the LTID value for this JS_ExtWorksheet.
     * 
     * @param LTID
     */
    public void setLTID(String LTID) {
        this.LTID = LTID;
    }


    /**
     * Gets the RBID value for this JS_ExtWorksheet.
     * 
     * @return RBID
     */
    public String getRBID() {
        return RBID;
    }


    /**
     * Sets the RBID value for this JS_ExtWorksheet.
     * 
     * @param RBID
     */
    public void setRBID(String RBID) {
        this.RBID = RBID;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_ExtWorksheet)) {
            return false;
        }
        JS_ExtWorksheet other = (JS_ExtWorksheet) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.elements==null && other.getElements()==null) || 
             (this.elements!=null &&
              java.util.Arrays.equals(this.elements, other.getElements()))) &&
            ((this.cellConfigurations==null && other.getCellConfigurations()==null) || 
             (this.cellConfigurations!=null &&
              java.util.Arrays.equals(this.cellConfigurations, other.getCellConfigurations()))) &&
            ((this.LTID==null && other.getLTID()==null) || 
             (this.LTID!=null &&
              this.LTID.equals(other.getLTID()))) &&
            ((this.RBID==null && other.getRBID()==null) || 
             (this.RBID!=null &&
              this.RBID.equals(other.getRBID())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getElements() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getElements());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getElements(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getCellConfigurations() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getCellConfigurations());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getCellConfigurations(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getLTID() != null) {
            _hashCode += getLTID().hashCode();
        }
        if (getRBID() != null) {
            _hashCode += getRBID().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_ExtWorksheet.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_ExtWorksheet"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("elements");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Elements"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "Element"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Element"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("cellConfigurations");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CellConfigurations"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "CellCriteria"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        elemField.setItemQName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CellCriteria"));
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("LTID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "LTID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("RBID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "RBID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
