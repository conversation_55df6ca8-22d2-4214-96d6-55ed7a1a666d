/**
 * TemplateTypeEnum.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class TemplateTypeEnum implements java.io.Serializable {
    private String _value_;
    private static java.util.HashMap _table_ = new java.util.HashMap();

    // Constructor
    protected TemplateTypeEnum(String value) {
        _value_ = value;
        _table_.put(_value_,this);
    }

    public static final String _TestItemTemplate = "TestItemTemplate";
    public static final String _TestItemWithStandardTemplate = "TestItemWithStandardTemplate";
    public static final String _TestStandardTemplate = "TestStandardTemplate";
    public static final String _WorksheetTemplate = "WorksheetTemplate";
    public static final String _ConclusionTemplate = "ConclusionTemplate";
    public static final String _TestReportTemplate = "TestReportTemplate";
    public static final String _TestReportBodyTemplate = "TestReportBodyTemplate";
    public static final TemplateTypeEnum TestItemTemplate = new TemplateTypeEnum(_TestItemTemplate);
    public static final TemplateTypeEnum TestItemWithStandardTemplate = new TemplateTypeEnum(_TestItemWithStandardTemplate);
    public static final TemplateTypeEnum TestStandardTemplate = new TemplateTypeEnum(_TestStandardTemplate);
    public static final TemplateTypeEnum WorksheetTemplate = new TemplateTypeEnum(_WorksheetTemplate);
    public static final TemplateTypeEnum ConclusionTemplate = new TemplateTypeEnum(_ConclusionTemplate);
    public static final TemplateTypeEnum TestReportTemplate = new TemplateTypeEnum(_TestReportTemplate);
    public static final TemplateTypeEnum TestReportBodyTemplate = new TemplateTypeEnum(_TestReportBodyTemplate);
    public String getValue() { return _value_;}
    public static TemplateTypeEnum fromValue(String value)
          throws IllegalArgumentException {
        TemplateTypeEnum enumeration = (TemplateTypeEnum)
            _table_.get(value);
        if (enumeration==null) {
            throw new IllegalArgumentException();
        }
        return enumeration;
    }
    public static TemplateTypeEnum fromString(String value)
          throws IllegalArgumentException {
        return fromValue(value);
    }
    public boolean equals(Object obj) {return (obj == this);}
    public int hashCode() { return toString().hashCode();}
    public String toString() { return _value_;}
    public Object readResolve() throws java.io.ObjectStreamException { return fromValue(_value_);}
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumSerializer(
            _javaType, _xmlType);
    }
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumDeserializer(
            _javaType, _xmlType);
    }
    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(TemplateTypeEnum.class);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateTypeEnum"));
    }
    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

}
