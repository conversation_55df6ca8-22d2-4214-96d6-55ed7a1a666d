/**
 * TService.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public interface TService extends javax.xml.rpc.Service {
    public String getTServiceSoapAddress();

    public TServiceSoap getTServiceSoap() throws javax.xml.rpc.ServiceException;

    public TServiceSoap getTServiceSoap(java.net.URL portAddress) throws javax.xml.rpc.ServiceException;
}
