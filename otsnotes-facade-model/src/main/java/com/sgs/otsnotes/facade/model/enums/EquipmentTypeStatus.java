package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

public enum EquipmentTypeStatus {
    Inactive(0, "Inactive"),
    Active(1, "Active");

    private final int status;
    private final String code;

    EquipmentTypeStatus(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return this.code;
    }

    public static final Map<String, EquipmentTypeStatus> maps = new HashMap<String, EquipmentTypeStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (EquipmentTypeStatus enu : EquipmentTypeStatus.values()) {
                put(enu.getCode().toLowerCase(), enu);
            }
        }
    };

    public static EquipmentTypeStatus getCode(String code) {
        if (code == null || !maps.contains<PERSON><PERSON>(code.toLowerCase())) {
            return null;
        }
        return maps.get(code.toLowerCase());
    }
}
