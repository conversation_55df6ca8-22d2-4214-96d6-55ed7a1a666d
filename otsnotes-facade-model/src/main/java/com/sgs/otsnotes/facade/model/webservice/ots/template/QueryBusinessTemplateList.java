/**
 * QueryBusinessTemplateList.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots.template;

public class QueryBusinessTemplateList  implements java.io.Serializable {
    private int templateTypeID;

    private int buSubTypeID;

    private int templateID;

    private java.lang.String templateName;

    private java.lang.String customerName;

    private java.lang.String customerNo;

    private java.lang.String templatePackageName;

    private java.lang.String groupCode;

    private java.lang.String groupName;

    private java.lang.String PPTemplateID;

    public QueryBusinessTemplateList() {
    }

    public QueryBusinessTemplateList(
           int templateTypeID,
           int buSubTypeID,
           int templateID,
           java.lang.String templateName,
           java.lang.String customerName,
           java.lang.String customerNo,
           java.lang.String templatePackageName,
           java.lang.String groupCode,
           java.lang.String groupName,
           java.lang.String PPTemplateID) {
           this.templateTypeID = templateTypeID;
           this.buSubTypeID = buSubTypeID;
           this.templateID = templateID;
           this.templateName = templateName;
           this.customerName = customerName;
           this.customerNo = customerNo;
           this.templatePackageName = templatePackageName;
           this.groupCode = groupCode;
           this.groupName = groupName;
           this.PPTemplateID = PPTemplateID;
    }


    /**
     * Gets the templateTypeID value for this QueryBusinessTemplateList.
     * 
     * @return templateTypeID
     */
    public int getTemplateTypeID() {
        return templateTypeID;
    }


    /**
     * Sets the templateTypeID value for this QueryBusinessTemplateList.
     * 
     * @param templateTypeID
     */
    public void setTemplateTypeID(int templateTypeID) {
        this.templateTypeID = templateTypeID;
    }


    /**
     * Gets the buSubTypeID value for this QueryBusinessTemplateList.
     * 
     * @return buSubTypeID
     */
    public int getBuSubTypeID() {
        return buSubTypeID;
    }


    /**
     * Sets the buSubTypeID value for this QueryBusinessTemplateList.
     * 
     * @param buSubTypeID
     */
    public void setBuSubTypeID(int buSubTypeID) {
        this.buSubTypeID = buSubTypeID;
    }


    /**
     * Gets the templateID value for this QueryBusinessTemplateList.
     * 
     * @return templateID
     */
    public int getTemplateID() {
        return templateID;
    }


    /**
     * Sets the templateID value for this QueryBusinessTemplateList.
     * 
     * @param templateID
     */
    public void setTemplateID(int templateID) {
        this.templateID = templateID;
    }


    /**
     * Gets the templateName value for this QueryBusinessTemplateList.
     * 
     * @return templateName
     */
    public java.lang.String getTemplateName() {
        return templateName;
    }


    /**
     * Sets the templateName value for this QueryBusinessTemplateList.
     * 
     * @param templateName
     */
    public void setTemplateName(java.lang.String templateName) {
        this.templateName = templateName;
    }


    /**
     * Gets the customerName value for this QueryBusinessTemplateList.
     * 
     * @return customerName
     */
    public java.lang.String getCustomerName() {
        return customerName;
    }


    /**
     * Sets the customerName value for this QueryBusinessTemplateList.
     * 
     * @param customerName
     */
    public void setCustomerName(java.lang.String customerName) {
        this.customerName = customerName;
    }


    /**
     * Gets the customerNo value for this QueryBusinessTemplateList.
     * 
     * @return customerNo
     */
    public java.lang.String getCustomerNo() {
        return customerNo;
    }


    /**
     * Sets the customerNo value for this QueryBusinessTemplateList.
     * 
     * @param customerNo
     */
    public void setCustomerNo(java.lang.String customerNo) {
        this.customerNo = customerNo;
    }


    /**
     * Gets the templatePackageName value for this QueryBusinessTemplateList.
     * 
     * @return templatePackageName
     */
    public java.lang.String getTemplatePackageName() {
        return templatePackageName;
    }


    /**
     * Sets the templatePackageName value for this QueryBusinessTemplateList.
     * 
     * @param templatePackageName
     */
    public void setTemplatePackageName(java.lang.String templatePackageName) {
        this.templatePackageName = templatePackageName;
    }


    /**
     * Gets the groupCode value for this QueryBusinessTemplateList.
     * 
     * @return groupCode
     */
    public java.lang.String getGroupCode() {
        return groupCode;
    }


    /**
     * Sets the groupCode value for this QueryBusinessTemplateList.
     * 
     * @param groupCode
     */
    public void setGroupCode(java.lang.String groupCode) {
        this.groupCode = groupCode;
    }


    /**
     * Gets the groupName value for this QueryBusinessTemplateList.
     * 
     * @return groupName
     */
    public java.lang.String getGroupName() {
        return groupName;
    }


    /**
     * Sets the groupName value for this QueryBusinessTemplateList.
     * 
     * @param groupName
     */
    public void setGroupName(java.lang.String groupName) {
        this.groupName = groupName;
    }


    /**
     * Gets the PPTemplateID value for this QueryBusinessTemplateList.
     * 
     * @return PPTemplateID
     */
    public java.lang.String getPPTemplateID() {
        return PPTemplateID;
    }


    /**
     * Sets the PPTemplateID value for this QueryBusinessTemplateList.
     * 
     * @param PPTemplateID
     */
    public void setPPTemplateID(java.lang.String PPTemplateID) {
        this.PPTemplateID = PPTemplateID;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof QueryBusinessTemplateList)) {
            return false;
        }
        QueryBusinessTemplateList other = (QueryBusinessTemplateList) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.templateTypeID == other.getTemplateTypeID() &&
            this.buSubTypeID == other.getBuSubTypeID() &&
            this.templateID == other.getTemplateID() &&
            ((this.templateName==null && other.getTemplateName()==null) || 
             (this.templateName!=null &&
              this.templateName.equals(other.getTemplateName()))) &&
            ((this.customerName==null && other.getCustomerName()==null) || 
             (this.customerName!=null &&
              this.customerName.equals(other.getCustomerName()))) &&
            ((this.customerNo==null && other.getCustomerNo()==null) || 
             (this.customerNo!=null &&
              this.customerNo.equals(other.getCustomerNo()))) &&
            ((this.templatePackageName==null && other.getTemplatePackageName()==null) || 
             (this.templatePackageName!=null &&
              this.templatePackageName.equals(other.getTemplatePackageName()))) &&
            ((this.groupCode==null && other.getGroupCode()==null) || 
             (this.groupCode!=null &&
              this.groupCode.equals(other.getGroupCode()))) &&
            ((this.groupName==null && other.getGroupName()==null) || 
             (this.groupName!=null &&
              this.groupName.equals(other.getGroupName()))) &&
            ((this.PPTemplateID==null && other.getPPTemplateID()==null) || 
             (this.PPTemplateID!=null &&
              this.PPTemplateID.equals(other.getPPTemplateID())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += getTemplateTypeID();
        _hashCode += getBuSubTypeID();
        _hashCode += getTemplateID();
        if (getTemplateName() != null) {
            _hashCode += getTemplateName().hashCode();
        }
        if (getCustomerName() != null) {
            _hashCode += getCustomerName().hashCode();
        }
        if (getCustomerNo() != null) {
            _hashCode += getCustomerNo().hashCode();
        }
        if (getTemplatePackageName() != null) {
            _hashCode += getTemplatePackageName().hashCode();
        }
        if (getGroupCode() != null) {
            _hashCode += getGroupCode().hashCode();
        }
        if (getGroupName() != null) {
            _hashCode += getGroupName().hashCode();
        }
        if (getPPTemplateID() != null) {
            _hashCode += getPPTemplateID().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(QueryBusinessTemplateList.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", ">QueryBusinessTemplateList"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateTypeID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "templateTypeID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("buSubTypeID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "buSubTypeID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "templateID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templateName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "templateName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("customerName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "customerName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("customerNo");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "customerNo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("templatePackageName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "templatePackageName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("groupCode");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "groupCode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("groupName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "groupName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PPTemplateID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "PPTemplateID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
