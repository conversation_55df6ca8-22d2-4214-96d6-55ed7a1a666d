/**
 * JS_Image.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_Image  extends Element implements java.io.Serializable {
    private TemplateImageType imageType;

    private int certificateImageType;

    private String[] imagePrintOption;

    private String[] usedBy;

    private String imageSrcs;

    public JS_Image() {
    }

    public JS_Image(
           String id,
           String name,
           TemplateImageType imageType,
           int certificateImageType,
           String[] imagePrintOption,
           String[] usedBy,
           String imageSrcs) {
        super(
            id,
            name);
        this.imageType = imageType;
        this.certificateImageType = certificateImageType;
        this.imagePrintOption = imagePrintOption;
        this.usedBy = usedBy;
        this.imageSrcs = imageSrcs;
    }


    /**
     * Gets the imageType value for this JS_Image.
     * 
     * @return imageType
     */
    public TemplateImageType getImageType() {
        return imageType;
    }


    /**
     * Sets the imageType value for this JS_Image.
     * 
     * @param imageType
     */
    public void setImageType(TemplateImageType imageType) {
        this.imageType = imageType;
    }


    /**
     * Gets the certificateImageType value for this JS_Image.
     * 
     * @return certificateImageType
     */
    public int getCertificateImageType() {
        return certificateImageType;
    }


    /**
     * Sets the certificateImageType value for this JS_Image.
     * 
     * @param certificateImageType
     */
    public void setCertificateImageType(int certificateImageType) {
        this.certificateImageType = certificateImageType;
    }


    /**
     * Gets the imagePrintOption value for this JS_Image.
     * 
     * @return imagePrintOption
     */
    public String[] getImagePrintOption() {
        return imagePrintOption;
    }


    /**
     * Sets the imagePrintOption value for this JS_Image.
     * 
     * @param imagePrintOption
     */
    public void setImagePrintOption(String[] imagePrintOption) {
        this.imagePrintOption = imagePrintOption;
    }


    /**
     * Gets the usedBy value for this JS_Image.
     * 
     * @return usedBy
     */
    public String[] getUsedBy() {
        return usedBy;
    }


    /**
     * Sets the usedBy value for this JS_Image.
     * 
     * @param usedBy
     */
    public void setUsedBy(String[] usedBy) {
        this.usedBy = usedBy;
    }


    /**
     * Gets the imageSrcs value for this JS_Image.
     * 
     * @return imageSrcs
     */
    public String getImageSrcs() {
        return imageSrcs;
    }


    /**
     * Sets the imageSrcs value for this JS_Image.
     * 
     * @param imageSrcs
     */
    public void setImageSrcs(String imageSrcs) {
        this.imageSrcs = imageSrcs;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_Image)) {
            return false;
        }
        JS_Image other = (JS_Image) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.imageType==null && other.getImageType()==null) || 
             (this.imageType!=null &&
              this.imageType.equals(other.getImageType()))) &&
            this.certificateImageType == other.getCertificateImageType() &&
            ((this.imagePrintOption==null && other.getImagePrintOption()==null) || 
             (this.imagePrintOption!=null &&
              java.util.Arrays.equals(this.imagePrintOption, other.getImagePrintOption()))) &&
            ((this.usedBy==null && other.getUsedBy()==null) || 
             (this.usedBy!=null &&
              java.util.Arrays.equals(this.usedBy, other.getUsedBy()))) &&
            ((this.imageSrcs==null && other.getImageSrcs()==null) || 
             (this.imageSrcs!=null &&
              this.imageSrcs.equals(other.getImageSrcs())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getImageType() != null) {
            _hashCode += getImageType().hashCode();
        }
        _hashCode += getCertificateImageType();
        if (getImagePrintOption() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getImagePrintOption());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getImagePrintOption(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getUsedBy() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getUsedBy());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getUsedBy(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getImageSrcs() != null) {
            _hashCode += getImageSrcs().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_Image.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_Image"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("imageType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ImageType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateImageType"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("certificateImageType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CertificateImageType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("imagePrintOption");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ImagePrintOption"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ImagePrintOption"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("usedBy");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "UsedBy"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "UsedByEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("imageSrcs");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ImageSrcs"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
