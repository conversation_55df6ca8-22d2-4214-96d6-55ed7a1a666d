/**
 * JS_TestItemConclusion.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class JS_TestItemConclusion  extends Element implements java.io.Serializable {
    private boolean isConclusionTable;

    private int PGT;

    private int PG;

    private String testItemID;

    private String testMethodID;

    private int sampleID;

    private int sampleType;

    public JS_TestItemConclusion() {
    }

    public JS_TestItemConclusion(
           String id,
           String name,
           boolean isConclusionTable,
           int PGT,
           int PG,
           String testItemID,
           String testMethodID,
           int sampleID,
           int sampleType) {
        super(
            id,
            name);
        this.isConclusionTable = isConclusionTable;
        this.PGT = PGT;
        this.PG = PG;
        this.testItemID = testItemID;
        this.testMethodID = testMethodID;
        this.sampleID = sampleID;
        this.sampleType = sampleType;
    }


    /**
     * Gets the isConclusionTable value for this JS_TestItemConclusion.
     * 
     * @return isConclusionTable
     */
    public boolean isIsConclusionTable() {
        return isConclusionTable;
    }


    /**
     * Sets the isConclusionTable value for this JS_TestItemConclusion.
     * 
     * @param isConclusionTable
     */
    public void setIsConclusionTable(boolean isConclusionTable) {
        this.isConclusionTable = isConclusionTable;
    }


    /**
     * Gets the PGT value for this JS_TestItemConclusion.
     * 
     * @return PGT
     */
    public int getPGT() {
        return PGT;
    }


    /**
     * Sets the PGT value for this JS_TestItemConclusion.
     * 
     * @param PGT
     */
    public void setPGT(int PGT) {
        this.PGT = PGT;
    }


    /**
     * Gets the PG value for this JS_TestItemConclusion.
     * 
     * @return PG
     */
    public int getPG() {
        return PG;
    }


    /**
     * Sets the PG value for this JS_TestItemConclusion.
     * 
     * @param PG
     */
    public void setPG(int PG) {
        this.PG = PG;
    }


    /**
     * Gets the testItemID value for this JS_TestItemConclusion.
     * 
     * @return testItemID
     */
    public String getTestItemID() {
        return testItemID;
    }


    /**
     * Sets the testItemID value for this JS_TestItemConclusion.
     * 
     * @param testItemID
     */
    public void setTestItemID(String testItemID) {
        this.testItemID = testItemID;
    }


    /**
     * Gets the testMethodID value for this JS_TestItemConclusion.
     * 
     * @return testMethodID
     */
    public String getTestMethodID() {
        return testMethodID;
    }


    /**
     * Sets the testMethodID value for this JS_TestItemConclusion.
     * 
     * @param testMethodID
     */
    public void setTestMethodID(String testMethodID) {
        this.testMethodID = testMethodID;
    }


    /**
     * Gets the sampleID value for this JS_TestItemConclusion.
     * 
     * @return sampleID
     */
    public int getSampleID() {
        return sampleID;
    }


    /**
     * Sets the sampleID value for this JS_TestItemConclusion.
     * 
     * @param sampleID
     */
    public void setSampleID(int sampleID) {
        this.sampleID = sampleID;
    }


    /**
     * Gets the sampleType value for this JS_TestItemConclusion.
     * 
     * @return sampleType
     */
    public int getSampleType() {
        return sampleType;
    }


    /**
     * Sets the sampleType value for this JS_TestItemConclusion.
     * 
     * @param sampleType
     */
    public void setSampleType(int sampleType) {
        this.sampleType = sampleType;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof JS_TestItemConclusion)) {
            return false;
        }
        JS_TestItemConclusion other = (JS_TestItemConclusion) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            this.isConclusionTable == other.isIsConclusionTable() &&
            this.PGT == other.getPGT() &&
            this.PG == other.getPG() &&
            ((this.testItemID==null && other.getTestItemID()==null) || 
             (this.testItemID!=null &&
              this.testItemID.equals(other.getTestItemID()))) &&
            ((this.testMethodID==null && other.getTestMethodID()==null) || 
             (this.testMethodID!=null &&
              this.testMethodID.equals(other.getTestMethodID()))) &&
            this.sampleID == other.getSampleID() &&
            this.sampleType == other.getSampleType();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        _hashCode += (isIsConclusionTable() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        _hashCode += getPGT();
        _hashCode += getPG();
        if (getTestItemID() != null) {
            _hashCode += getTestItemID().hashCode();
        }
        if (getTestMethodID() != null) {
            _hashCode += getTestMethodID().hashCode();
        }
        _hashCode += getSampleID();
        _hashCode += getSampleType();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(JS_TestItemConclusion.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "JS_TestItemConclusion"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("isConclusionTable");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "IsConclusionTable"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PGT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PGT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "PG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("testItemID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TestItemID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("testMethodID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "TestMethodID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sampleID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SampleID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sampleType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SampleType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
