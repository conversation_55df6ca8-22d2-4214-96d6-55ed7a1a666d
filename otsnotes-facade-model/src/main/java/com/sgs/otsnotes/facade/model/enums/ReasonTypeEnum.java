package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum ReasonTypeEnum {
    None(0, "未知"),
    TestTesultError(1, "Wrong result/conclusion 测试结果/结论错误"),
    SampleError(2, "Wrong/missing in report text editing报告文字编辑错误/遗漏"),
    LackofTechnicalCapability(3, "Understanding deviations or omissions in Standard/manual 标准/Manual理解偏差或疏漏"),
    TestCustomerApplications(4, "Wrong Test item/standard测试项目或标准错误"),
    ManualUpdateUnderstandingError(5, "Wrong sampling 分样错误"),
    SubcontractingLaboratoryError(6, "Subcontract lab error分包实验室错误"),
    ReportingComplianceError(7, "System error 系统错误"),
    NonLaboratoryError(8, "Client request客户要求"),
    REPORTNORMATIVEERROR(9,"Report normative error报告规范性错误"),
    Other(10, "Others (Please specify) 其它（写明具体错误）");
    @DictCodeField
    private final int code;
    @DictLabelField
    private final String message;

    ReasonTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String message() {
        return this.message;
    }

    public static ReasonTypeEnum getCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReasonTypeEnum item : ReasonTypeEnum.values()) {
            if (code.equals(item.getCode())) {
                return item;
            }
        }
        return null;
    }
}
