package com.sgs.otsnotes.facade.model.enums;

import org.springframework.util.StringUtils;

/**
 * @ClassName TlExecutionClassificationCodeEnums
 * @Description TODO
 * <AUTHOR>
 * @Date 8/19/2020
 */
public enum TlExecutionClassificationCodeEnums {
    NeedToTest(1,"Need To Test"),
    InformationReviewOnly(2,"Document Review"),
    DisplayOnly(3,"Display Only"),
    NeedToTestAndShare(4,"Need To Test + Share");

    private Integer code;
    private String desc;

    TlExecutionClassificationCodeEnums(Integer code,String desc){
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getName(Integer code){
        if(code==null){
            return null;
        }
        for (TlExecutionClassificationCodeEnums enums : TlExecutionClassificationCodeEnums.values()) {
            if(enums.getCode().compareTo(code)==0){
                return enums.getDesc();
            }
        }
        return null;
    }
    public static String getName(String code){
        if(StringUtils.isEmpty(code)){
            return null;
        }
        Integer inputCode = 0;
        try{
            inputCode = Integer.parseInt(code);
        }catch (Exception e){
            return null;
        }
        for (TlExecutionClassificationCodeEnums enums : TlExecutionClassificationCodeEnums.values()) {
            if(inputCode.compareTo(enums.code)==0){
                return enums.getDesc();
            }
        }
        return null;
    }

    public static boolean check(Integer code,TlExecutionClassificationCodeEnums ... enums){
        if(code==null){
            return false;
        }
        for (TlExecutionClassificationCodeEnums codeEnums : enums) {
            if(codeEnums.getCode().compareTo(code)==0){
                return true;
            }
        }
        return false;
    }

}
