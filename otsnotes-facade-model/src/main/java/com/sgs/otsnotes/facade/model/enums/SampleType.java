package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

import java.util.Optional;

/**
 * Original/Component/Compsite/Mix
 * 101：Original、102：Component、103：Compsite、104：Mix、105：Share
 * {@link  com.sgs.framework.model.enums.SampleType}
 */
@Deprecated
public enum SampleType {
    OriginalSample(101, "O","O",100*1000000, 1000000,"原样","O", 101),
    <PERSON><PERSON>(102, "P","C",100000, 1000,"子样", "C", 102),
    SubSample(103, "P","C",0,"子子样", "SC", 102),
    MixSample(104, "P","C",999*1000000,"Mix 样", "G", 103),
    ShareSample(105, "P","C",100000, 1000,"共享样", "S", 104);

    private final int sampleType;
    private int seed = 1;
    private final String categoryPhy;
    private final String categoryChem;
    private final int sampleSeq;
    private final String message;
    private final String shortMessage;
    private final int starlimsType;

    SampleType(int sampleType, String categoryPhy, String categoryChem, int sampleSeq, String message,String shortMessage, int starlimsType) {
        this.sampleType = sampleType;
        this.categoryPhy = categoryPhy;
        this.categoryChem = categoryChem;
        this.sampleSeq = sampleSeq;
        this.message = message;
        this.shortMessage=shortMessage;
        this.starlimsType = starlimsType;
    }

    SampleType(int sampleType, String categoryPhy, String categoryChem, int sampleSeq, int seed, String message,String shortMessage, int starlimsType) {
        this(sampleType, categoryPhy,categoryChem,sampleSeq,message, shortMessage, starlimsType);
        this.seed = seed;
    }

    public int getSampleType() {
        return sampleType;
    }

    public String getCategoryPhy() {
        return categoryPhy;
    }

    public String getShortMessage() {
        return shortMessage;
    }

    public String getCategoryChem() {
        return categoryChem;
    }

    public String getMessage() {
        return message;
    }

    public int getSampleSeq() {
        return sampleSeq;
    }

    public int getSeed() {
        return seed;
    }

    public int getStarlimsType() {
        return starlimsType;
    }

    public static final Map<Integer, SampleType> maps = new HashMap<>();
    public static final Map<String, SampleType> smMaps = new HashMap<>();

    static {
        for (SampleType sampleType : SampleType.values()) {
            maps.put(sampleType.getSampleType(), sampleType);
            smMaps.put(sampleType.getShortMessage(), sampleType);
        }
    }

    public static boolean containsSampleType(Integer sampleType){
        return maps.containsKey(sampleType);
    }

    public static SampleType findType(Integer sampleType) {
        return Optional.ofNullable(maps.get(sampleType)).orElse(null);
    }

    public static String getMessage(Integer type) {
        if (type == null || !maps.containsKey(type)) {
            return null;
        }
        return maps.get(type).getMessage();
    }

    public static String getShortMessage(Integer type) {
        if (type == null || !maps.containsKey(type)) {
            return null;
        }
        return maps.get(type).getShortMessage();
    }

    public static boolean equals(Integer parentSampleType, Integer childSampleType) {
        if (parentSampleType == null || childSampleType == null){
            return false;
        }
        if (!maps.containsKey(parentSampleType) || !maps.containsKey(childSampleType)){
            return false;
        }
        SampleType parentType = maps.get(parentSampleType);
        SampleType childType = maps.get(childSampleType);
        if (childType == SampleType.Sample || childType == SampleType.ShareSample){
            return parentType == SampleType.OriginalSample;
        }
        if (childType == SampleType.SubSample){
            return parentType == SampleType.Sample || parentType == SampleType.ShareSample;
        }
        return false;
    }

    public static boolean equals(Integer type, SampleType sampleType) {
        if (type == null || !maps.containsKey(type)){
            return false;
        }
        return maps.get(type) == sampleType;
    }

    public static boolean check(Integer sampleType,SampleType... sampleTypes){
        if (sampleType == null || sampleType <= 0 || sampleTypes == null || sampleTypes.length <= 0){
            return false;
        }
        for (SampleType st: sampleTypes){
            if (sampleType == st.getSampleType()){
                return true;
            }
        }
        return false;
    }

    public static boolean check(SampleType sampleType,SampleType... sampleTypes){
        if (sampleType == null){
            return false;
        }
        return check(sampleType.getSampleType(), sampleTypes);
    }

    public static SampleType findTypeBySM(String shortMessage){
        if (shortMessage == null) {
            return null;
        }
        return smMaps.get(shortMessage.toUpperCase());
    }
}


