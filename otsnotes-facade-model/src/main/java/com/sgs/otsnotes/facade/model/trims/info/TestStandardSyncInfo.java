package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

public class TestStandardSyncInfo extends PrintFriendliness{
    /**
     *
     */
    private Integer sequence;
    /**
     *
     */
    private Integer tsVersionIdentifier;
    /**
     *
     */
    private Integer sectionId;
    /**
     *
     */
    private String sectionName;
    /**
     *
     */
    private String methodAlias;
    /**
     *
     */
    private String ppNotesAlias;
    /**
     *
     */
    private String evaluationAlias;

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Integer getTsVersionIdentifier() {
        return tsVersionIdentifier;
    }

    public void setTsVersionIdentifier(Integer tsVersionIdentifier) {
        this.tsVersionIdentifier = tsVersionIdentifier;
    }

    public Integer getSectionId() {
        return sectionId;
    }

    public void setSectionId(Integer sectionId) {
        this.sectionId = sectionId;
    }

    public String getSectionName() {
        return sectionName;
    }

    public void setSectionName(String sectionName) {
        this.sectionName = sectionName;
    }

    public String getMethodAlias() {
        return methodAlias;
    }

    public void setMethodAlias(String methodAlias) {
        this.methodAlias = methodAlias;
    }

    public String getPpNotesAlias() {
        return ppNotesAlias;
    }

    public void setPpNotesAlias(String ppNotesAlias) {
        this.ppNotesAlias = ppNotesAlias;
    }

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }
}
