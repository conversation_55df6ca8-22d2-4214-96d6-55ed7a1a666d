package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

/**
 * @ClassName BaiduTranslateErrorEnums
 * @Description 百度翻译接口错误code 对应枚举
 * <AUTHOR>
 * @Date 4/14/2021
 */
@Dict
public enum BaiduTranslateErrorEnums {

    _52000(52000, "成功", ""),
    _52001(52001, "请求超时", "重试"),
    _52002(52002, "系统错误", "重试"),
    _52003(52003, "未授权用户", "请检查您的appid是否正确，或者服务是否开通"),
    _54000(54000, "必填参数为空", "请检查是否少传参数"),
    _54001(54001, "签名错误", "请检查您的签名生成方法"),
    _54003(54003, "访问频率受限", "请降低您的调用频率，或进行身份认证后切换为高级版/尊享版"),
    _54004(54004, "账户余额不足", "请前往管理控制台为账户充值"),
    _54005(54005, "长query请求频繁", "请降低长query的发送频率，3s后再试"),
    _58000(58000, "客户端IP非法", "检查个人资料里填写的IP地址是否正确，可前往开发者信息-基本信息修改，可前往开发者信息-基本信息修改"),
    _58001(58001, "译文语言方向不支持", "检查译文语言是否在语言列表里"),
    _58002(58002, "服务当前已关闭", "请前往管理控制台开启服务"),
    _90107(90107, "认证未通过或未生效", "请前往我的认证查看认证进度");
    @DictCodeField
    private Integer errorCode;
    @DictLabelField
    private String desc;
    private String resolvent;

    BaiduTranslateErrorEnums(Integer errorCode, String desc, String resolvent) {
        this.errorCode = errorCode;
        this.desc = desc;
        this.resolvent = resolvent;
    }

    public static String getDesc(Integer errorCode) {
        if (errorCode == null) {
            return null;
        }
        BaiduTranslateErrorEnums[] values = BaiduTranslateErrorEnums.values();
        for (BaiduTranslateErrorEnums value : values) {
            // DIG-8555 Remove this "return" statement or make it conditional.
            // 这个筛选逻辑没有实现 所以加上
            if (errorCode.equals(value.getErrorCode())) {
                return value.desc;
            }
            //return value.desc;
        }
        return null;
    }

    public static BaiduTranslateErrorEnums getErrorEnums(Integer errorCode){
        if (errorCode == null) {
            return null;
        }
        BaiduTranslateErrorEnums[] values = BaiduTranslateErrorEnums.values();
        for (BaiduTranslateErrorEnums value : values) {
            // DIG-8555 Remove this "return" statement or make it conditional.
            // 这个筛选逻辑没有实现 所以加上
            if (errorCode.equals(value.getErrorCode())) {
                return value;
            }
            //return value;
        }
        return null;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getDesc() {
        return desc;
    }

    public String getResolvent() {
        return resolvent;
    }
}
