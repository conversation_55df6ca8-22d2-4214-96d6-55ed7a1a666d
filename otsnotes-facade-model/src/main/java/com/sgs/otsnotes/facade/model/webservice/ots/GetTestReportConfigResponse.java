/**
 * GetTestReportConfigResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class GetTestReportConfigResponse  implements java.io.Serializable {
    private TemplateConfigEntity getTestReportConfigResult;

    public GetTestReportConfigResponse() {
    }

    public GetTestReportConfigResponse(
           TemplateConfigEntity getTestReportConfigResult) {
           this.getTestReportConfigResult = getTestReportConfigResult;
    }


    /**
     * Gets the getTestReportConfigResult value for this GetTestReportConfigResponse.
     * 
     * @return getTestReportConfigResult
     */
    public TemplateConfigEntity getGetTestReportConfigResult() {
        return getTestReportConfigResult;
    }


    /**
     * Sets the getTestReportConfigResult value for this GetTestReportConfigResponse.
     * 
     * @param getTestReportConfigResult
     */
    public void setGetTestReportConfigResult(TemplateConfigEntity getTestReportConfigResult) {
        this.getTestReportConfigResult = getTestReportConfigResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GetTestReportConfigResponse)) {
            return false;
        }
        GetTestReportConfigResponse other = (GetTestReportConfigResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.getTestReportConfigResult==null && other.getGetTestReportConfigResult()==null) || 
             (this.getTestReportConfigResult!=null &&
              this.getTestReportConfigResult.equals(other.getGetTestReportConfigResult())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getGetTestReportConfigResult() != null) {
            _hashCode += getGetTestReportConfigResult().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GetTestReportConfigResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GetTestReportConfigResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("getTestReportConfigResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "GetTestReportConfigResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateConfigEntity"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
