package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import io.micrometer.core.lang.Nullable;
import org.springframework.util.StringUtils;

/**
 * sub_report 表 objectType字段对应枚举
 */
@Dict
public enum SubReportObjectTypeEnums {
    subcontract("subcontract",5,1),
    order("order",0,0),
    slimjob("slimjob",1,2,31),
    starlims("starlims",3,4, 30);

    @DictLabelField
    private String objectType;
    private Integer sourceCode;

    @DictCodeField
    private Integer subcontractTypeCode;

    private Integer refSystemId;

    SubReportObjectTypeEnums(String objectType,Integer sourceCode,Integer subcontractTypeCode) {
        this.objectType = objectType;
        this.sourceCode = sourceCode;
        this.subcontractTypeCode = subcontractTypeCode;
    }

    SubReportObjectTypeEnums(String objectType, Integer sourceCode, Integer subcontractTypeCode, Integer refSystemId) {
        this.objectType = objectType;
        this.sourceCode = sourceCode;
        this.subcontractTypeCode = subcontractTypeCode;
        this.refSystemId = refSystemId;
    }

    public Integer getRefSystemId() {
        return refSystemId;
    }

    public Integer getSubcontractTypeCode() {
        return subcontractTypeCode;
    }

    public String getObjectType() {
        return objectType;
    }

    public Integer getSourceCode() {
        return sourceCode;
    }

    public static boolean check(String objectType, SubReportObjectTypeEnums ... enums){
        if(StringUtils.isEmpty(objectType)){
            return  false;
        }
        for (SubReportObjectTypeEnums anEnum : enums) {
            if(anEnum.objectType.equalsIgnoreCase(objectType)){
                return true;
            }
        }
        return false;
    }

    public static SubReportObjectTypeEnums getObjectTypeEnum(String objectType){
        if(!SubReportObjectTypeEnums.check(objectType,SubReportObjectTypeEnums.values())){
            return null;
        }
        for (SubReportObjectTypeEnums value : SubReportObjectTypeEnums.values()) {
            if(value.objectType.equalsIgnoreCase(objectType)){
                return value;
            }
        }
        return null;
    }
}
