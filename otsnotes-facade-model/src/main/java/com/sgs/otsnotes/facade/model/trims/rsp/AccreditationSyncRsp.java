package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;
import com.sgs.otsnotes.facade.model.trims.rsp.accreditation.AccreditationSyncInfo;

import java.util.List;

/**
 * @ClassName AccreditationSyncRsp
 * @Description TODO
 * <AUTHOR>
 * @Date 12/24/2020
 */
public class AccreditationSyncRsp extends PrintFriendliness {

    private List<AccreditationSyncInfo> data;

    public List<AccreditationSyncInfo> getData() {
        return data;
    }

    public void setData(List<AccreditationSyncInfo> data) {
        this.data = data;
    }
}
