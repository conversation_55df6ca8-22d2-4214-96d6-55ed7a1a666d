/**
 * TServiceSoap.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public interface TServiceSoap extends java.rmi.Remote {
    public void clearTemporaryFolder() throws java.rmi.RemoteException;
    public String doCommand(String command, String args) throws java.rmi.RemoteException;
    public String[] getAllFilesInTemporaryFolder() throws java.rmi.RemoteException;
    public String[] getAllXmlFilesInDebugDataFolder() throws java.rmi.RemoteException;
    public String[] getSubDirAndFiles(String folderPath) throws java.rmi.RemoteException;
    public byte[] readTemporaryFile(String physicalPath) throws java.rmi.RemoteException;
    public byte[] downloadTemplateServicesFile(String filePath, javax.xml.rpc.holders.BooleanHolder isExists) throws java.rmi.RemoteException;
    public boolean fileExistInFileServer(String serverFilePath) throws java.rmi.RemoteException;
    public CommandResult uploadTemplate(int templateId, String templateFilePath, String templateConfigFilePath, int templateType) throws java.rmi.RemoteException;
    public CommandResult uploadApplicationFormTemplate(int templateId, String templateFilePath, String templateConfigFilePath) throws java.rmi.RemoteException;
    public String getTemplateHtml(int templateId, String templateFilePath, String templateConfigFilePath, String currentSheetName) throws java.rmi.RemoteException;
    public String getPreviewTemplateHtml(int templateId, String templateFilePath, String templateConfigFilePath, String currentSheetName) throws java.rmi.RemoteException;
    public String[] getTemplatePreviewImages(int templateId, String templateFilePath, String templateConfigFilePath) throws java.rmi.RemoteException;
    public boolean copyTemplate(TemplateInfo source, TemplateInfo target) throws java.rmi.RemoteException;
    public boolean removeTemplateConfigurationCache(int templateId) throws java.rmi.RemoteException;
    public TemplateConfigEntity getTemplateConfig(TemplateInstanceInfo info) throws java.rmi.RemoteException;
    public TemplateConfigEntity getWorksheetConfig(TemplateInstanceInfo worksheetInfo) throws java.rmi.RemoteException;
    public boolean generateWorksheet(TemplateInstanceInfo worksheetInfo, ReportDatasource datasource) throws java.rmi.RemoteException;
    public boolean updateWorksheetPhysicalTestClause(TemplateInstanceInfo worksheetInfo, ReportDatasource rd, javax.xml.rpc.holders.StringHolder errMsg) throws java.rmi.RemoteException;
    public String getWorksheetPhysicalTestClauseHtml(TemplateInstanceInfo[] worksheetInfos, javax.xml.rpc.holders.StringHolder errMsg) throws java.rmi.RemoteException;
    public boolean saveWorksheetDataIntoFile(TemplateInstanceInfo worksheetInfo, ReportDatasource datasource) throws java.rmi.RemoteException;
    public FactDataItem[] extractWorksheetChemicData(TemplateInstanceInfo worksheetInfo) throws java.rmi.RemoteException;
    public boolean combineFinalTestData(TemplateInstanceInfo worksheetInfo, String[] sourceServerFiles, String targetServerFile) throws java.rmi.RemoteException;
    public String[] getTestReportPreviewImages(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException;
    public TemplateConfigEntity getTestReportConfig(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException;
    public boolean generateTestReport(TemplateInstanceInfo reportInfo, ReportDatasource datasource, javax.xml.rpc.holders.ObjectHolder rlt) throws java.rmi.RemoteException;
    public byte[] mergeTestReportBinary(TemplateInstanceInfo reportInfo, ReportDatasource datasource, javax.xml.rpc.holders.StringHolder errMsg) throws java.rmi.RemoteException;
    public boolean generateMixedTemplateTestReport(TemplateInstanceInfo reportInfo, ReportDatasource datasource, String bodyTemplateFilePath, String bodyTemplateConfigFilePath) throws java.rmi.RemoteException;
    public boolean generateMixedPrelimReport(TemplateInstanceInfo prelimReportInfo, ReportDatasource prelimDatasource, String testReportFilePath, String testReportConfigFilePath) throws java.rmi.RemoteException;
    public boolean replaceUnknownCharAtReport(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException;
    public boolean saveTestReportDataIntoFile(TemplateInstanceInfo reportInfo, ReportDatasource datasource) throws java.rmi.RemoteException;
    public boolean approveTestReport(TemplateInstanceInfo reportInfo, ApproverInfo[] approverInfos, boolean isShowESignature) throws java.rmi.RemoteException;
    public String resetPDFPrivilege(String pdfFilePath, int privilege, boolean isRegSignature) throws java.rmi.RemoteException;
    public String resetPDFPrivilegeAndDSS(String pdfFilePath, int privilege) throws java.rmi.RemoteException;
    public boolean resetElectronicSignature(TemplateInstanceInfo reportInfo, boolean isOnlyLogo) throws java.rmi.RemoteException;
    public boolean fillTestReportApproverInformation(TemplateInstanceInfo reportInfo, ApproverInfo[] approverInfos) throws java.rmi.RemoteException;
    public String generateTestReportPDF(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException;
    public void generateTestReportDraftSoftCopyPDF(TemplateInstanceInfo reportInfo, String outputPdfFilePath, String waterMarkText, javax.xml.rpc.holders.BooleanHolder generateTestReportDraftSoftCopyPDFResult, com.sgs.otsnotes.facade.model.webservice.ots.holders.ArrayOfStringHolder errMsgs) throws java.rmi.RemoteException;
    public String preGenerateTestReportPDF(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException;
    public boolean isTestReportPDFExist(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException;
    public boolean isTestReportPDFConverting(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException;
    public void extractTestReportData(TemplateInstanceInfo reportInfo, com.sgs.otsnotes.facade.model.webservice.ots.holders.ArrayOfDataItemHolder extractTestReportDataResult, javax.xml.rpc.holders.StringHolder errMessage) throws java.rmi.RemoteException;
    public void deleteFilesAfterUploadTestReport(TemplateInstanceInfo reportInfo, javax.xml.rpc.holders.BooleanHolder deleteFilesAfterUploadTestReportResult, javax.xml.rpc.holders.StringHolder errMessage) throws java.rmi.RemoteException;
    public boolean deleteTBDElements(TemplateInstanceInfo reportInfo) throws java.rmi.RemoteException;
    public ReportDatasource getMockReportDatasource() throws java.rmi.RemoteException;
    public boolean deleteMockReportDatasourceFile() throws java.rmi.RemoteException;
    public boolean mergeReports(SubReportInfo[] reports, TemplateInstanceInfo targetReport) throws java.rmi.RemoteException;
    public void mergeDocumentBySectionBreak(String[] filePaths, String finalFullPath, int uiViewType, javax.xml.rpc.holders.StringHolder mergeDocumentBySectionBreakResult, javax.xml.rpc.holders.StringHolder errMsg) throws java.rmi.RemoteException;
    public byte[] mergeDocumentContent(String[] filePaths, String targetFilePath, javax.xml.rpc.holders.StringHolder errMsg) throws java.rmi.RemoteException;
    public void getMergedReportbySection(GetMergebySectionModel model, javax.xml.rpc.holders.StringHolder getMergedReportbySectionResult, javax.xml.rpc.holders.StringHolder errMsg, Boolean downSheet, Integer timeOut) throws java.rmi.RemoteException;
    public boolean generateConfirmation(String confirmationId, String confirmationTemplateFilePath, String confirmationFilePath, String testReportTemplateFilePath, String testReportTemplateConfigFilePath, ReportDatasource datasource) throws java.rmi.RemoteException;
    public boolean generateConfirmationByMockDatasource(String confirmationId, String confirmationTemplateFilePath, String confirmationFilePath, String testReportTemplateFilePath, String testReportTemplateConfigFilePath) throws java.rmi.RemoteException;
    public boolean convertHtmlToPDF(String html, String pdfFilePath) throws java.rmi.RemoteException;
    public boolean generateEquipmentReport(String equipmentReportId, String equipmentTemplateFilePath, String equipmentFilePath, DataItem[] flatDataList) throws java.rmi.RemoteException;
    public boolean generateFlatReport(String reportTypeName, String instanceId, String templateFilePath, String instanceFilePath, DataItem[] flatDataList) throws java.rmi.RemoteException;
    public boolean convertWordWithExternalConverter(String htmlString, String pdfFilePath, int waitMilliSeconds) throws java.rmi.RemoteException;
    public boolean generateApplicationForm(TemplateInstanceInfo reportInfo, ReportDatasource datasource) throws java.rmi.RemoteException;
    public boolean saveApplicationFormDataIntoFile(TemplateInstanceInfo reportInfo, ReportDatasource datasource) throws java.rmi.RemoteException;
    public boolean convertWordToPDF(String wordFilePath, String pdfFilePath) throws java.rmi.RemoteException;
    public boolean addWatermarkForPDF(String pdfFilePath, String outputPdfFilePath, String watermarkText) throws java.rmi.RemoteException;
    public boolean convertDSSToolFileToFileService(String documentName, String filePath) throws java.rmi.RemoteException;
    public boolean convertFileServiceFileToDSSTool(String documentName, String filePath) throws java.rmi.RemoteException;
    public boolean convertDSSToolFailFileToQueue(String documentName, String filePath) throws java.rmi.RemoteException;
    public boolean convertDSSToolFailFileToFileService(String documentName, String filePath) throws java.rmi.RemoteException;
    public void deleteDSSToolOutputFile(String documentName, String filePath) throws java.rmi.RemoteException;
    public String getProtocolReport(InTemplateInformation[] list, String fileName, Boolean downSheet, Integer timeOut, String callbackUri) throws java.rmi.RemoteException;
    public String processJasperReport(byte[] fileStream, String targetFilePath) throws java.rmi.RemoteException;
}
