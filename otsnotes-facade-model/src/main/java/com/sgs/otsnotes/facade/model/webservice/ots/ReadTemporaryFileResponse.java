/**
 * ReadTemporaryFileResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ReadTemporaryFileResponse  implements java.io.Serializable {
    private byte[] readTemporaryFileResult;

    public ReadTemporaryFileResponse() {
    }

    public ReadTemporaryFileResponse(
           byte[] readTemporaryFileResult) {
           this.readTemporaryFileResult = readTemporaryFileResult;
    }


    /**
     * Gets the readTemporaryFileResult value for this ReadTemporaryFileResponse.
     * 
     * @return readTemporaryFileResult
     */
    public byte[] getReadTemporaryFileResult() {
        return readTemporaryFileResult;
    }


    /**
     * Sets the readTemporaryFileResult value for this ReadTemporaryFileResponse.
     * 
     * @param readTemporaryFileResult
     */
    public void setReadTemporaryFileResult(byte[] readTemporaryFileResult) {
        this.readTemporaryFileResult = readTemporaryFileResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ReadTemporaryFileResponse)) {
            return false;
        }
        ReadTemporaryFileResponse other = (ReadTemporaryFileResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.readTemporaryFileResult==null && other.getReadTemporaryFileResult()==null) || 
             (this.readTemporaryFileResult!=null &&
              java.util.Arrays.equals(this.readTemporaryFileResult, other.getReadTemporaryFileResult())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getReadTemporaryFileResult() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getReadTemporaryFileResult());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getReadTemporaryFileResult(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ReadTemporaryFileResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">ReadTemporaryFileResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("readTemporaryFileResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ReadTemporaryFileResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "base64Binary"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
