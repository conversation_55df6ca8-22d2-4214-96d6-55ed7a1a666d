/**
 * AddWatermarkForPDF.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class AddWatermarkForPDF  implements java.io.Serializable {
    private String pdfFilePath;

    private String outputPdfFilePath;

    private String watermarkText;

    public AddWatermarkForPDF() {
    }

    public AddWatermarkForPDF(
           String pdfFilePath,
           String outputPdfFilePath,
           String watermarkText) {
           this.pdfFilePath = pdfFilePath;
           this.outputPdfFilePath = outputPdfFilePath;
           this.watermarkText = watermarkText;
    }


    /**
     * Gets the pdfFilePath value for this AddWatermarkForPDF.
     * 
     * @return pdfFilePath
     */
    public String getPdfFilePath() {
        return pdfFilePath;
    }


    /**
     * Sets the pdfFilePath value for this AddWatermarkForPDF.
     * 
     * @param pdfFilePath
     */
    public void setPdfFilePath(String pdfFilePath) {
        this.pdfFilePath = pdfFilePath;
    }


    /**
     * Gets the outputPdfFilePath value for this AddWatermarkForPDF.
     * 
     * @return outputPdfFilePath
     */
    public String getOutputPdfFilePath() {
        return outputPdfFilePath;
    }


    /**
     * Sets the outputPdfFilePath value for this AddWatermarkForPDF.
     * 
     * @param outputPdfFilePath
     */
    public void setOutputPdfFilePath(String outputPdfFilePath) {
        this.outputPdfFilePath = outputPdfFilePath;
    }


    /**
     * Gets the watermarkText value for this AddWatermarkForPDF.
     * 
     * @return watermarkText
     */
    public String getWatermarkText() {
        return watermarkText;
    }


    /**
     * Sets the watermarkText value for this AddWatermarkForPDF.
     * 
     * @param watermarkText
     */
    public void setWatermarkText(String watermarkText) {
        this.watermarkText = watermarkText;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof AddWatermarkForPDF)) {
            return false;
        }
        AddWatermarkForPDF other = (AddWatermarkForPDF) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.pdfFilePath==null && other.getPdfFilePath()==null) || 
             (this.pdfFilePath!=null &&
              this.pdfFilePath.equals(other.getPdfFilePath()))) &&
            ((this.outputPdfFilePath==null && other.getOutputPdfFilePath()==null) || 
             (this.outputPdfFilePath!=null &&
              this.outputPdfFilePath.equals(other.getOutputPdfFilePath()))) &&
            ((this.watermarkText==null && other.getWatermarkText()==null) || 
             (this.watermarkText!=null &&
              this.watermarkText.equals(other.getWatermarkText())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getPdfFilePath() != null) {
            _hashCode += getPdfFilePath().hashCode();
        }
        if (getOutputPdfFilePath() != null) {
            _hashCode += getOutputPdfFilePath().hashCode();
        }
        if (getWatermarkText() != null) {
            _hashCode += getWatermarkText().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(AddWatermarkForPDF.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">AddWatermarkForPDF"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("pdfFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "pdfFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("outputPdfFilePath");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "outputPdfFilePath"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("watermarkText");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "watermarkText"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
