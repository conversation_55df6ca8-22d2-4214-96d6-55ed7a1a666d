package com.sgs.otsnotes.facade.model.enums;

/**
 *
 */
public enum SyncTypeEnum {
    MasterData(1, "MasterData"),
    DataMarker(2, "DataMarker"),
    RelationShip(3, "RelationShip");

    private final int type;
    private final String text;

    SyncTypeEnum(int type, String text) {
        this.type = type;
        this.text = text;
    }

    public int getType() {
        return type;
    }

    public String getText() {
        return text;
    }

    /**
     *
     * @param syncType
     * @return
     */
    public static String getText(Integer syncType) {
        SyncTypeEnum enu = findType(syncType);
        return enu != null ? enu.getText() : null;
    }

    /**
     *
     * @param syncType
     * @param syncTypes
     * @return
     */
    public static boolean check(Integer syncType, SyncTypeEnum... syncTypes) {
        if (syncType == null || syncTypes == null || syncTypes.length <= 0){
            return false;
        }
        for (SyncTypeEnum enu: syncTypes) {
            if (enu.getType() == syncType.intValue()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param syncType
     * @return
     */
    public static SyncTypeEnum findType(Integer syncType){
        if (syncType == null){
            return null;
        }
        for (SyncTypeEnum enu: SyncTypeEnum.values()) {
            if (enu.getType() == syncType.intValue()){
                return enu;
            }
        }
        return null;
    }

    /**
     *
     * @param syncType
     * @return
     */
    public boolean check(Integer syncType){
        SyncTypeEnum enm = findType(syncType);
        return enm != null && this == enm;
    }

    /**
     *
     * @param syncType
     * @return
     */
    public boolean check(SyncTypeEnum syncType){
        if (syncType == null){
            return false;
        }
        return this.check(syncType.getType());
    }

}
