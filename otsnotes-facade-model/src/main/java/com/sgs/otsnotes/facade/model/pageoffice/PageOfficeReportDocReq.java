package com.sgs.otsnotes.facade.model.pageoffice;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sgs.framework.core.base.BaseRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class PageOfficeReportDocReq extends BaseRequest {
    /**
     *
     */
    @JsonIgnore
    private HttpServletRequest request;
    /**
     *
     */
    @JsonIgnore
    private HttpServletResponse response;
    /**
     *
     */
    private String reportId;
    /**
     *
     */
    private String sgsToken;

    public HttpServletRequest getRequest() {
        return request;
    }

    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    public HttpServletResponse getResponse() {
        return response;
    }

    public void setResponse(HttpServletResponse response) {
        this.response = response;
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public String getSgsToken() {
        return sgsToken;
    }

    public void setSgsToken(String sgsToken) {
        this.sgsToken = sgsToken;
    }
}
