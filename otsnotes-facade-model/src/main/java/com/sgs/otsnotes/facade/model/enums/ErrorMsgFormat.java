package com.sgs.otsnotes.facade.model.enums;

/**
 * <AUTHOR>
 * @date 2023/5/18 20:11
 */
public enum ErrorMsgFormat {
    LackMatrix(1, "%s和%s ", "在Order中没有找到Matrix的Assign关系："),
    LackTestLineCN(2, "%s", "Test Line缺少中文："),
    LackAnalyteCN(3, "%s (%s)", "Test Line缺少中文Analyte："),
    LackAnalyteResult(4, "%s（%s）", "Analyte下缺测试结果："),
    MixSampleFailMustsingle(5, "%s（%s）", "Mix样Fail必须Assign单测Sample:"),
    LackResult(6, "%s（%s）", "缺少测试结果:"),
    ;

    private final int type;
    private final String format;
    private final String message;

    ErrorMsgFormat(int type, String format, String message) {
        this.type = type;
        this.format = format;
        this.message = message;
    }

    public int getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }

    public String getFormat() {
        return format;
    }

    public static ErrorMsgFormat findType(Integer type) {
        if (type == null) {
            return null;
        }
        for (ErrorMsgFormat enu : ErrorMsgFormat.values()) {
            if (enu.getType() == type.intValue()) {
                return enu;
            }
        }
        return null;
    }

    public boolean check(ErrorMsgFormat emailType) {
        if (emailType == null) {
            return false;
        }
        return this.getType() == emailType.getType();
    }


}
