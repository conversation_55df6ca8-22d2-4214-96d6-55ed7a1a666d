package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/3 10:35
 */
public class TestLimitMutipleUnitsInfo extends PrintFriendliness {

    private Integer sequence;
    private Integer testingUnitId;
    private Integer reportingUnitId;
    private List<Integer> standardVersionidentifiers;

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Integer getTestingUnitId() {
        return testingUnitId;
    }

    public void setTestingUnitId(Integer testingUnitId) {
        this.testingUnitId = testingUnitId;
    }

    public Integer getReportingUnitId() {
        return reportingUnitId;
    }

    public void setReportingUnitId(Integer reportingUnitId) {
        this.reportingUnitId = reportingUnitId;
    }

    public List<Integer> getStandardVersionidentifiers() {
        return standardVersionidentifiers;
    }

    public void setStandardVersionidentifiers(List<Integer> standardVersionidentifiers) {
        this.standardVersionidentifiers = standardVersionidentifiers;
    }
}
