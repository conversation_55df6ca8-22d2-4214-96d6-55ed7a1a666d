package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.info.TestSortRuleInfo;

import java.util.Comparator;

public class TestSortRuleComparator implements Comparator<TestSortRuleInfo> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public TestSortRuleComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(TestSortRuleInfo o1, TestSortRuleInfo o2) {
        // to PpNo
        int index = o1.getPpNo().compareTo(o2.getPpNo());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        // to TestLineId
        index = Integer.compare(o1.getTestLineId(), o2.getTestLineId());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        // to LabSectionName
        index = o1.getLabSectionName().compareTo(o2.getLabSectionName());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        // to TestLineSeq
        index = Integer.compare(o1.getTestLineSeq(), o2.getTestLineSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        // to SectionLevel
        index = Integer.compare(o1.getSectionLevel(), o2.getSectionLevel());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
