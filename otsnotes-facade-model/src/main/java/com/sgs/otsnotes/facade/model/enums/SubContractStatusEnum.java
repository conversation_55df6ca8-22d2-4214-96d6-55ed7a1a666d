package com.sgs.otsnotes.facade.model.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

/**
 * 分包单状态
 * @Author: Jinx
 * @Date: 2019-06-13 13:36
 * @Description:
 **/
@Dict
public enum SubContractStatusEnum {

    Created(0, "New"),
    Testing(1, "Testing"),
    Complete(2, "Completed"),
    Cancelled(3, "Cancelled"),
    Pend(4,"Pending");

    @DictCodeField
    private int status = 1;
    @DictLabelField
    private String name;

    public int getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    SubContractStatusEnum(int status, String name){
        this.status = status;
        this.name = name;
    }

    public static final Map<Integer, SubContractStatusEnum> maps = new HashMap<Integer, SubContractStatusEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (SubContractStatusEnum enu : SubContractStatusEnum.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static boolean check(Integer status ,SubContractStatusEnum ... enums){
        if(status==null){
            return false;
        }
        for (SubContractStatusEnum anEnum : enums) {
            if(anEnum.getStatus() == status.intValue()){
                return true;
            }
        }
        return false;
    }

    public static String getName(Integer status){
        if(status==null){
            return null;
        }
        for (SubContractStatusEnum value : SubContractStatusEnum.values()) {
            if(value.status==status){
                return value.getName();
            }
        }
        return  null;
    }

    public static SubContractStatusEnum findStatus(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }

    public static List<Integer> activeStatusList(){
        return Arrays.asList(Created.status,Testing.status,Complete.status);
    }

}
