package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

/**
 * TODO 切换到FrameWork  TestLineType
 * 0：普通【默认值】、1：Pretreatment、2：SubContract(Order),3:是1和2计算出来， 4:cspp
 */
@Deprecated
public enum TestLineType {
    None(0, "普通【默认值】"),
    Pretreatment(1, "Pretreatment"),
    SubContractOrder(2, "SubContract(Order)"),
    CSPP(1<<2, "CSPP"),
    Claim(8, "Claim"),
    IngredientTL(1<<4,"SubTL"),
    CloneTestLine(1<<5,"CloneTestLine"),
    Job(1<<6,"Job"),
    OOB_TEST(1<<7,"OOB_TEST");

    private int type;
    private String message;

    TestLineType(int type, String message) {
        this.type = type;
        this.message = message;
    }

    public int getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }


    public static final Map<Integer, TestLineType> maps = new HashMap<Integer, TestLineType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TestLineType type : TestLineType.values()) {
                put(type.getType(), type);
            }
        }
    };

    public static TestLineType findType(Integer type) {
        if (type == null || !maps.containsKey(type)) {
            return null;
        }
        return maps.get(type);
    }

    /**
     *
     * @param type
     * @param testLineType
     * @return
     */
    public static boolean check(Integer type, TestLineType testLineType) {
        if (type == null || testLineType == null){
            return false;
        }
        return (type & testLineType.getType()) > 0;
    }

    /**
     *
     * @param pretreatment
     * @return
     */
    public static boolean isPretreatment(String pretreatment) {
        if (pretreatment == null){
            return false;
        }
        return pretreatment.toLowerCase().contains("pretreatment");
    }

    public static Integer calculateType(Integer type,TestLineType testLineType){
        if(type==null || testLineType==null ){
            return null;
        }
        return type ^testLineType.getType();

    }
}
