package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import java.util.Optional;

/**
 * <AUTHOR>
 */


public enum DataEntryStyleEnums {
    NoStyle(400),
    Style_one(401),
    Style_two(402),
    Style_three(403),
    Style_four(404),
    Style_five(405),
    Style_six(406),
    Style_seven(407);

    private final int style ;
    DataEntryStyleEnums(int style){
        this.style = style;
    }

    public int getStyle() {
        return style;
    }

    public static final Map<Integer, DataEntryStyleEnums> maps = new HashMap<Integer, DataEntryStyleEnums>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (DataEntryStyleEnums enu : DataEntryStyleEnums.values()) {
                put(enu.getStyle(), enu);
            }
        }
    };

    public static DataEntryStyleEnums findStyle(Integer style) {
        return Optional.ofNullable(maps.get(style)).orElse(NoStyle);
    }

    public static boolean check(Integer style, DataEntryStyleEnums... styles) {
        if (style == null || !maps.containsKey(style) || styles == null || styles.length <= 0){
            return false;
        }
        for (DataEntryStyleEnums tlStatus: styles){
            if (style.intValue() == tlStatus.getStyle()){
                return true;
            }
        }
        return false;
    }


}
