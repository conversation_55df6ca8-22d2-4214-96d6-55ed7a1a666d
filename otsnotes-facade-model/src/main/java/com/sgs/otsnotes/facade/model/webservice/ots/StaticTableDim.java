/**
 * StaticTableDim.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class StaticTableDim  implements java.io.Serializable {
    private String cellID;

    private String[] settingType;

    private DimTypeEnum dimType;

    private String value;

    private String unit;

    public StaticTableDim() {
    }

    public StaticTableDim(
           String cellID,
           String[] settingType,
           DimTypeEnum dimType,
           String value,
           String unit) {
           this.cellID = cellID;
           this.settingType = settingType;
           this.dimType = dimType;
           this.value = value;
           this.unit = unit;
    }


    /**
     * Gets the cellID value for this StaticTableDim.
     * 
     * @return cellID
     */
    public String getCellID() {
        return cellID;
    }


    /**
     * Sets the cellID value for this StaticTableDim.
     * 
     * @param cellID
     */
    public void setCellID(String cellID) {
        this.cellID = cellID;
    }


    /**
     * Gets the settingType value for this StaticTableDim.
     * 
     * @return settingType
     */
    public String[] getSettingType() {
        return settingType;
    }


    /**
     * Sets the settingType value for this StaticTableDim.
     * 
     * @param settingType
     */
    public void setSettingType(String[] settingType) {
        this.settingType = settingType;
    }


    /**
     * Gets the dimType value for this StaticTableDim.
     * 
     * @return dimType
     */
    public DimTypeEnum getDimType() {
        return dimType;
    }


    /**
     * Sets the dimType value for this StaticTableDim.
     * 
     * @param dimType
     */
    public void setDimType(DimTypeEnum dimType) {
        this.dimType = dimType;
    }


    /**
     * Gets the value value for this StaticTableDim.
     * 
     * @return value
     */
    public String getValue() {
        return value;
    }


    /**
     * Sets the value value for this StaticTableDim.
     * 
     * @param value
     */
    public void setValue(String value) {
        this.value = value;
    }


    /**
     * Gets the unit value for this StaticTableDim.
     * 
     * @return unit
     */
    public String getUnit() {
        return unit;
    }


    /**
     * Sets the unit value for this StaticTableDim.
     * 
     * @param unit
     */
    public void setUnit(String unit) {
        this.unit = unit;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof StaticTableDim)) {
            return false;
        }
        StaticTableDim other = (StaticTableDim) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.cellID==null && other.getCellID()==null) || 
             (this.cellID!=null &&
              this.cellID.equals(other.getCellID()))) &&
            ((this.settingType==null && other.getSettingType()==null) || 
             (this.settingType!=null &&
              java.util.Arrays.equals(this.settingType, other.getSettingType()))) &&
            ((this.dimType==null && other.getDimType()==null) || 
             (this.dimType!=null &&
              this.dimType.equals(other.getDimType()))) &&
            ((this.value==null && other.getValue()==null) || 
             (this.value!=null &&
              this.value.equals(other.getValue()))) &&
            ((this.unit==null && other.getUnit()==null) || 
             (this.unit!=null &&
              this.unit.equals(other.getUnit())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getCellID() != null) {
            _hashCode += getCellID().hashCode();
        }
        if (getSettingType() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getSettingType());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getSettingType(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getDimType() != null) {
            _hashCode += getDimType().hashCode();
        }
        if (getValue() != null) {
            _hashCode += getValue().hashCode();
        }
        if (getUnit() != null) {
            _hashCode += getUnit().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(StaticTableDim.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "StaticTableDim"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("cellID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "CellID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("settingType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "SettingType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TableSettingTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("dimType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "DimType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "DimTypeEnum"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("value");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Value"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("unit");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "Unit"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
