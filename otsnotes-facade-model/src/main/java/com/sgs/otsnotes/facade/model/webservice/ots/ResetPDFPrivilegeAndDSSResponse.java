/**
 * ResetPDFPrivilegeAndDSSResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ResetPDFPrivilegeAndDSSResponse  implements java.io.Serializable {
    private String resetPDFPrivilegeAndDSSResult;

    public ResetPDFPrivilegeAndDSSResponse() {
    }

    public ResetPDFPrivilegeAndDSSResponse(
           String resetPDFPrivilegeAndDSSResult) {
           this.resetPDFPrivilegeAndDSSResult = resetPDFPrivilegeAndDSSResult;
    }


    /**
     * Gets the resetPDFPrivilegeAndDSSResult value for this ResetPDFPrivilegeAndDSSResponse.
     * 
     * @return resetPDFPrivilegeAndDSSResult
     */
    public String getResetPDFPrivilegeAndDSSResult() {
        return resetPDFPrivilegeAndDSSResult;
    }


    /**
     * Sets the resetPDFPrivilegeAndDSSResult value for this ResetPDFPrivilegeAndDSSResponse.
     * 
     * @param resetPDFPrivilegeAndDSSResult
     */
    public void setResetPDFPrivilegeAndDSSResult(String resetPDFPrivilegeAndDSSResult) {
        this.resetPDFPrivilegeAndDSSResult = resetPDFPrivilegeAndDSSResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ResetPDFPrivilegeAndDSSResponse)) {
            return false;
        }
        ResetPDFPrivilegeAndDSSResponse other = (ResetPDFPrivilegeAndDSSResponse) obj;
        // DIG-8555 Change this condition so that it does not always evaluate to "false"
        //if (obj == null) return false;
        if (this == obj) {
            return true;
        }
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.resetPDFPrivilegeAndDSSResult==null && other.getResetPDFPrivilegeAndDSSResult()==null) || 
             (this.resetPDFPrivilegeAndDSSResult!=null &&
              this.resetPDFPrivilegeAndDSSResult.equals(other.getResetPDFPrivilegeAndDSSResult())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getResetPDFPrivilegeAndDSSResult() != null) {
            _hashCode += getResetPDFPrivilegeAndDSSResult().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ResetPDFPrivilegeAndDSSResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">ResetPDFPrivilegeAndDSSResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("resetPDFPrivilegeAndDSSResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "ResetPDFPrivilegeAndDSSResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
