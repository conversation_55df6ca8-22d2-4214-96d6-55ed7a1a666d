package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

/**
 * 执行类型(1：all-section、2：coverpage)
 */
@Dict
public enum CrossExecType {
    AllSection(1, "all-section"),
    CoverPage(2, "coverpage");
    @DictCodeField
    private Integer status;
    @DictLabelField
    private String value;

    CrossExecType(Integer status, String value) {
        this.status = status;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, CrossExecType> maps = new HashMap<Integer, CrossExecType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (CrossExecType enu : CrossExecType.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static boolean check(Integer status, CrossExecType execType) {
        if (status == null || status.intValue() <= 0 || !maps.containsKey(status.intValue())) {
            return false;
        }
        return maps.get(status) == execType;
    }

    public static String getValue(Integer status) {
        if (status == null || status == 0) {
            return null;
        }
        CrossExecType execType = maps.get(status);
        return execType == null ? null : execType.getValue();
    }
}