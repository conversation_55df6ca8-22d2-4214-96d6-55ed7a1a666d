package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum SequenceType {
    TestItemNo(0, "TestItemNo");

    private Integer status;
    private String message;

    SequenceType(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, SequenceType> maps = new HashMap<Integer, SequenceType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (SequenceType enu : SequenceType.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }
}
