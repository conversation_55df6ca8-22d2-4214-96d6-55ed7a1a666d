package com.sgs.otsnotes.facade.model.trims.info;

public class PricingOtherLanguageItemsSyncInfo {
    /**
     *
     */
    private Integer languageId;
    /**
     *
     */
    private String priceAttribute;
    /**
     *
     */
    private String remark;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getPriceAttribute() {
        return priceAttribute;
    }

    public void setPriceAttribute(String priceAttribute) {
        this.priceAttribute = priceAttribute;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
