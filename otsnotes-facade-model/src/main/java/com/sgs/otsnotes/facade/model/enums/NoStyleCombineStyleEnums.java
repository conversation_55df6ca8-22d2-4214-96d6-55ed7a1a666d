package com.sgs.otsnotes.facade.model.enums;

/**
 * 无样式配置页面 combine类型对应的模式
 * <AUTHOR>
 */
public enum NoStyleCombineStyleEnums {
    customer(1),
    standard(2);
    Integer noStyleCombineStyle;

    NoStyleCombineStyleEnums(Integer noStyleCombineStyle) {
        this.noStyleCombineStyle = noStyleCombineStyle;
    }

    public Integer getNoStyleCombineStyle() {
        return noStyleCombineStyle;
    }

    public static boolean check(Integer noStyleCombineStyle, NoStyleCombineStyleEnums... enums) {
        if (noStyleCombineStyle == null || enums == null || enums.length == 0) {
            return false;
        }
        for (NoStyleCombineStyleEnums anEnum : enums) {
            if (anEnum.noStyleCombineStyle.compareTo(noStyleCombineStyle) == 0) {
                return true;
            }
        }
        return false;
    }
}
