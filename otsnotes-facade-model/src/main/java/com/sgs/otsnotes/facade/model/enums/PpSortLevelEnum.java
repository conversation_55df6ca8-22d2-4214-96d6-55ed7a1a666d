package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;

/**
 * <AUTHOR>
 * @date 2020/7/20 17:50
 */
public enum PpSortLevelEnum {
    /**
     * 在protocol_setting中有配置的
     */
    PP_WITH_SETTING(10),

    /**
     * 有PPNO但未配置的
     */
    PP(20),


    /**
     * 没有PPNO的
     */
    PP_WITHOUT_NO(40),
    ;
    private Integer level;
    PpSortLevelEnum(Integer level){
        this.level = level;
    }

    public Integer getLevel() {
        return level;
    }
}
