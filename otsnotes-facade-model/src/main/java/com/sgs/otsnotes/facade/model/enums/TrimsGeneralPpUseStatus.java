package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

public enum TrimsGeneralPpUseStatus {
    Active(0, "None"),
    Deleted(1, "deleted");

    private int status;
    private String code;

    TrimsGeneralPpUseStatus(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    static Map<Integer, TrimsGeneralPpUseStatus> maps = new HashMap<>();
    static Map<String, TrimsGeneralPpUseStatus> codeMaps = new HashMap<>();

    static {
        for (TrimsGeneralPpUseStatus status : TrimsGeneralPpUseStatus.values()) {
            maps.put(status.getStatus(), status);
            codeMaps.put(status.getCode().toLowerCase(), status);
        }
    };

    public static TrimsGeneralPpUseStatus getStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    /**
     *
     * @param status
     * @param ppStatus
     * @return
     */
    public static boolean check(Integer status, TrimsGeneralPpUseStatus... ppStatus) {
        if (status == null || !maps.containsKey(status.intValue()) || ppStatus == null || ppStatus.length <= 0){
            return false;
        }
        for (TrimsGeneralPpUseStatus tlStatus: ppStatus){
            if (status.intValue() == tlStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    public static TrimsGeneralPpUseStatus getCode(String code) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase())) {
            return null;
        }
        return codeMaps.get(code.toLowerCase());
    }

    /**
     *
     * @param code
     * @param ppStatuss
     * @return
     */
    public static boolean check(String code, TrimsGeneralPpUseStatus... ppStatuss) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase()) || ppStatuss == null || ppStatuss.length <= 0){
            return false;
        }
        TrimsGeneralPpUseStatus trimsPpStatus = codeMaps.get(code.toLowerCase());
        for (TrimsGeneralPpUseStatus ppStatus: ppStatuss){
            if (ppStatus.getStatus() == trimsPpStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param ppStatus
     * @return
     */
    public boolean check(TrimsGeneralPpUseStatus... ppStatus){
        if (ppStatus == null || ppStatus.length <= 0){
            return false;
        }
        for (TrimsGeneralPpUseStatus tlStatus: ppStatus){
            if (this.getStatus() == tlStatus.getStatus()){
                return true;
            }
        }
        return false;
    }
}
