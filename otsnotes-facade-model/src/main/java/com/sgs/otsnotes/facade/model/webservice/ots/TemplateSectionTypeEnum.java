/**
 * TemplateSectionTypeEnum.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class TemplateSectionTypeEnum implements java.io.Serializable {
    private String _value_;
    private static java.util.HashMap _table_ = new java.util.HashMap();

    // Constructor
    protected TemplateSectionTypeEnum(String value) {
        _value_ = value;
        _table_.put(_value_,this);
    }

    public static final String _TestItemAssembly = "TestItemAssembly";
    public static final String _TestStandardAssembly = "TestStandardAssembly";
    public static final String _Conclusion = "Conclusion";
    public static final String _WorksheetFReport = "WorksheetFReport";
    public static final String _SLIMReport = "SLIMReport";
    public static final String _SubcontractReport = "SubcontractReport";
    public static final String _ThirdPartyReport = "ThirdPartyReport";
    public static final String _ReportPhotos = "ReportPhotos";
    public static final String _TestReportBodyTemplate = "TestReportBodyTemplate";
    public static final String _ChemicalWorksheetFReport = "ChemicalWorksheetFReport";
    public static final String _NonChemicalWorksheetFReport = "NonChemicalWorksheetFReport";
    public static final String _Cover = "Cover";
    public static final String _Attachments = "Attachments";
    public static final String _ApplicationForm = "ApplicationForm";
    public static final TemplateSectionTypeEnum TestItemAssembly = new TemplateSectionTypeEnum(_TestItemAssembly);
    public static final TemplateSectionTypeEnum TestStandardAssembly = new TemplateSectionTypeEnum(_TestStandardAssembly);
    public static final TemplateSectionTypeEnum Conclusion = new TemplateSectionTypeEnum(_Conclusion);
    public static final TemplateSectionTypeEnum WorksheetFReport = new TemplateSectionTypeEnum(_WorksheetFReport);
    public static final TemplateSectionTypeEnum SLIMReport = new TemplateSectionTypeEnum(_SLIMReport);
    public static final TemplateSectionTypeEnum SubcontractReport = new TemplateSectionTypeEnum(_SubcontractReport);
    public static final TemplateSectionTypeEnum ThirdPartyReport = new TemplateSectionTypeEnum(_ThirdPartyReport);
    public static final TemplateSectionTypeEnum ReportPhotos = new TemplateSectionTypeEnum(_ReportPhotos);
    public static final TemplateSectionTypeEnum TestReportBodyTemplate = new TemplateSectionTypeEnum(_TestReportBodyTemplate);
    public static final TemplateSectionTypeEnum ChemicalWorksheetFReport = new TemplateSectionTypeEnum(_ChemicalWorksheetFReport);
    public static final TemplateSectionTypeEnum NonChemicalWorksheetFReport = new TemplateSectionTypeEnum(_NonChemicalWorksheetFReport);
    public static final TemplateSectionTypeEnum Cover = new TemplateSectionTypeEnum(_Cover);
    public static final TemplateSectionTypeEnum Attachments = new TemplateSectionTypeEnum(_Attachments);
    public static final TemplateSectionTypeEnum ApplicationForm = new TemplateSectionTypeEnum(_ApplicationForm);
    public String getValue() { return _value_;}
    public static TemplateSectionTypeEnum fromValue(String value)
          throws IllegalArgumentException {
        TemplateSectionTypeEnum enumeration = (TemplateSectionTypeEnum)
            _table_.get(value);
        if (enumeration==null) {
            throw new IllegalArgumentException();
        }
        return enumeration;
    }
    public static TemplateSectionTypeEnum fromString(String value)
          throws IllegalArgumentException {
        return fromValue(value);
    }
    public boolean equals(Object obj) {return (obj == this);}
    public int hashCode() { return toString().hashCode();}
    public String toString() { return _value_;}
    public Object readResolve() throws java.io.ObjectStreamException { return fromValue(_value_);}
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumSerializer(
            _javaType, _xmlType);
    }
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumDeserializer(
            _javaType, _xmlType);
    }
    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(TemplateSectionTypeEnum.class);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "TemplateSectionTypeEnum"));
    }
    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

}
