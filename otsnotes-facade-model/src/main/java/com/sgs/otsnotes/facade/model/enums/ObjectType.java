package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;

@Dict
public enum ObjectType {
    Order("order", "order"),
    TestLine("testLine", "testLine"),
    Job("job", "job"),
    SubContract("subContract", "subContract"),
    StarLims("starLims", "starLims"),
    Report("report", "report");

    @DictCodeField
    private final String code;
    private final String message;

    ObjectType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<String, ObjectType> maps = new HashMap<String, ObjectType>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (ObjectType type : ObjectType.values()) {
                put(type.getCode().toLowerCase(), type);
            }
        }
    };

    public static ObjectType getCode(String code) {
        if (code == null || !maps.containsKey(code.toLowerCase())) {
            return null;
        }
        return maps.get(code.toLowerCase());
    }

    public static boolean check(String code, ObjectType objectType) {
        if (code == null || objectType == null || !maps.containsKey(code.toLowerCase())) {
            return false;
        }
        return maps.get(code.toLowerCase()) == objectType;
    }
}
