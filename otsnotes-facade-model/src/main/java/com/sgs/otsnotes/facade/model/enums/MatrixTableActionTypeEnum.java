package com.sgs.otsnotes.facade.model.enums;

/**
 * matrix页面中，各个按钮的动作
 * <AUTHOR>
 * @date 2020/11/11 17:21
 */
public enum MatrixTableActionTypeEnum {
    /**
     * UpdateStandard；此操作在去重时忽略matrixGroup
     */
    UPDATE_STANDARD("UpdateStandard"),
    /**
     * UpdateCondition；此操作在去重时不能忽略matrixGroup
     */
    UPDATE_CONDITION("UpdateCondition"),
    /**
     * ConfirmCondition 此操作在去重时不能忽略matrixGroup
     */
    CONFIRM_CONDITION("ConfirmCondition");

    private String name;

    MatrixTableActionTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
