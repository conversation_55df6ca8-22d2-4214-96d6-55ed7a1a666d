package com.sgs.otsnotes.facade.model.trims.info;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;

import java.util.List;

public class UsageTypeLangSyncInfo extends PrintFriendliness {
    /**
     *
     */
    private String usageTypeValue;
    /**
     *
     */
    private Integer languageId;
    /**
     *
     */
    private List<PositionSyncInfo> positions;

    public String getUsageTypeValue() {
        return usageTypeValue;
    }

    public void setUsageTypeValue(String usageTypeValue) {
        this.usageTypeValue = usageTypeValue;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public List<PositionSyncInfo> getPositions() {
        return positions;
    }

    public void setPositions(List<PositionSyncInfo> positions) {
        this.positions = positions;
    }
}
