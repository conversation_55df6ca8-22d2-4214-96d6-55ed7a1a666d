package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.Dict;
import org.apache.axis.utils.StringUtils;

/**
 * <AUTHOR>
 */

@Dict
public enum PageOfficeTypeEnums {
    open,select,editOnLine;

    public static boolean check(String type,PageOfficeTypeEnums ... enums){
        if(StringUtils.isEmpty(type) || enums==null || enums.length==0){
            return false;
        }
        for (PageOfficeTypeEnums anEnum : enums) {
            if(anEnum.name().equalsIgnoreCase(type)){
                return true;
            }
        }
        return false;
    }
}
