package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;
import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;

@Dict
public enum ArtifactType {
    TestLine(0, "TestLine"),
    PP(1, "Protocol");
    @DictCodeField
    private Integer type;
    @DictLabel<PERSON>ield
    private String code;

    ArtifactType(Integer type, String code) {
        this.type = type;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer getType() {
        return type;
    }

    static Map<Integer, ArtifactType> typeMaps = new HashMap();
    static Map<String, ArtifactType> codeMaps = new HashMap<>();
    static {
        for (ArtifactType artifactType : ArtifactType.values()) {
            typeMaps.put(artifactType.getType(), artifactType);
            codeMaps.put(artifactType.getCode(), artifactType);
        }
    }

    public static ArtifactType findType(Integer type) {
        if (type == null || !typeMaps.containsKey(type)) {
            return null;
        }
        return typeMaps.get(type);
    }

    public static ArtifactType findCode(String code) {
        if (codeMaps == null || !codeMaps.containsKey(code)) {
            return null;
        }
        return codeMaps.get(code);
    }

    public static boolean check(String code, ArtifactType artifactType) {
        if (codeMaps == null || !codeMaps.containsKey(code)) {
            return false;
        }
        return codeMaps.get(code) == artifactType;
    }

    /**
     *
     * @param type
     * @param artifactType
     * @return
     */
    public static boolean check(Integer type, ArtifactType artifactType) {
        if (type == null || !typeMaps.containsKey(type)){
            return false;
        }
        return typeMaps.get(type) == artifactType;
    }

    public static boolean check(Integer artifactType) {
        if (artifactType == null){
            return false;
        }
        return typeMaps.containsKey(artifactType);
    }
}
