package com.sgs.otsnotes.facade.model.enums;

import org.apache.commons.lang3.StringUtils;

public enum StarLimsReportType {
    Final(0, "Final"),
    Preliminary(1, "Preliminary");

    private int type;
    private String code;

    StarLimsReportType(int type, String code) {
        this.type = type;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getType() {
        return type;
    }

    public static int getType(String code) {
        StarLimsReportType enu = findReportType(code);
        return enu != null ? enu.getType() : 0;
    }

    public static StarLimsReportType findType(String code) {
        return findReportType(code);
    }

    /**
     *
     * @param code
     * @param reportTypes
     * @return
     */
    public static boolean check(String code, StarLimsReportType... reportTypes) {
        if (code == null || reportTypes == null || reportTypes.length <= 0){
            return false;
        }
        for (StarLimsReportType enu: reportTypes) {
            if (StringUtils.equalsIgnoreCase(enu.getCode(), code)){
                return true;
            }
        }
        return false;
    }

    public static boolean check(String reportType) {
        return findReportType(reportType) != null;
    }

    /**
     *
     * @param reportType
     * @return
     */
    public boolean check(StarLimsReportType reportType) {
        return reportType != null && this == reportType;
    }

    private static StarLimsReportType findReportType(String code){
        if (code == null){
            return null;
        }
        for (StarLimsReportType enu: values()) {
            if (StringUtils.equalsIgnoreCase(enu.getCode(), code)){
                return enu;
            }
        }
        return null;
    }
}
