package com.sgs.otsnotes.facade.model.annotation;

import com.sgs.otsnotes.facade.model.enums.TestLinePendingTypeEnums;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Desc 使用这个注解进行testlinePending的校验，一定要注意的一点是
 * 方法的入参一定要是对象！！！，因为aop没有做基本数据类型的解析
 * 默认就是按照对象的方式进行反射字段取值的
 */
@Target({ElementType.ANNOTATION_TYPE, ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface TestLinePending {

    /**
     *如果多层对象嵌套，需要设置为 a.b.c.d
      */
    String filedName();

    /** 0 是用testLine去校验
    // 1是用ppTlRel去校验,
    // 2 是当前属性是对象，需要从对象里面获取对应的TLid ,
    // 3是当前属性是list 对象 需要获取PPtlRel
     */
    TestLinePendingTypeEnums type();

    /** type 为2,3时 使用 */
    String lastFiledName() default "";

}
