package com.sgs.otsnotes.facade.model.enums;

import com.sgs.framework.core.dict.annotation.DictCodeField;
import com.sgs.framework.core.dict.annotation.DictLabelField;
import org.apache.axis.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;
import com.sgs.framework.core.dict.annotation.Dict;

/**
 * 1:Customer Report(PDF),2:Sub Report (Word)
 */
@Dict
public enum ReportRequirementEnum {
    Customer_Report_PDF("1", "Customer Report(PDF)"),
    Sub_Report_Word("2", "Sub Report (Word)"),
    Test_Only("4", "Testing Only");

    @DictCodeField
    private String code;
    @DictLabelField
    private String message;

    ReportRequirementEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<String, ReportRequirementEnum> maps = new HashMap<String, ReportRequirementEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ReportRequirementEnum reportRequirementEnum : ReportRequirementEnum.values()) {
                put(reportRequirementEnum.getCode(), reportRequirementEnum);
            }
        }
    };

    public static String getMessage(String code) {
        if (code == null) {
            return null;
        }
        for (ReportRequirementEnum item : ReportRequirementEnum.values()) {
            if (code.equals(item.getCode())) {
                return item.getMessage();
            }
        }
        return "";
    }

    /**
     *
     * @param code
     * @param reportRequirementEnums
     * @return
     */
    public static boolean check(String code, ReportRequirementEnum... reportRequirementEnums){
        if (code == null || !maps.containsKey(code) || reportRequirementEnums == null || reportRequirementEnums.length <= 0) {
            return false;
        }
        for (ReportRequirementEnum reportRequirementEnum : reportRequirementEnums){
            if (code.equalsIgnoreCase(reportRequirementEnum.getCode())){
                return true;
            }
        }
        return false;
    }
}
