package com.sgs.otsnotes.facade.model.trims.rsp.mdl;

import com.sgs.otsnotes.facade.model.common.PrintFriendliness;
import lombok.Data;

import java.util.List;

@Data
public class MdlSyncInfo extends PrintFriendliness {

    /**
     * TestLineVersionId+BU的唯一键，暂时对于应用没什么作用，加这个只是想关后续数据排查比较可能有用
     */
    private Integer caId;

    /**
     * trims端主键ID
     */
    private Integer mdlId;

    /**
     * 1代表数据已经删除，以下内容不会输出
     */
    private Integer isDeleted;

    /**
     *
     */
    private Integer tlVersionIdentifier;

    /**
     *
     */
    private Integer productLineId;

    /**
     *
     */
    private Integer laboratoryId;

    /**
     *
     */
    private String mdl;

    /**
     *
     */
    private Integer mdlUnitId;

    /**
     *
     */
    private String mdlUnitShortDepiction;

    /**
     *
     */
    private String rl;

    /**
     *
     */
    private Integer rlUnitId;

    /**
     *
     */
    private String rlUnitShortDepiction;

    /**
     *
     */
    private List<Integer> testAnalyteIds;

    /**
     *
     */
    private List<Integer> equipmentTypeIds;

    /**
     *
     */
    private List<Integer> productAttributeIds;


}
