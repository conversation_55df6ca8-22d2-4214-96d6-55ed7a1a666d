/**
 * ReportConclusionScopeEnum.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.facade.model.webservice.ots;

public class ReportConclusionScopeEnum implements java.io.Serializable {
    private String _value_;
    private static java.util.HashMap _table_ = new java.util.HashMap();

    // Constructor
    protected ReportConclusionScopeEnum(String value) {
        _value_ = value;
        _table_.put(_value_,this);
    }

    public static final String _NA = "NA";
    public static final String _Independence = "Independence";
    public static final String _TestItem = "TestItem";
    public static final String _TestStandard = "TestStandard";
    public static final String _TestReportOverall = "TestReportOverall";
    public static final String _TestItem_Sample = "TestItem_Sample";
    public static final String _Testitem_Method = "Testitem_Method";
    public static final String _PrelimReportOverall = "PrelimReportOverall";
    public static final String _PhysicalTestClause = "PhysicalTestClause";
    public static final ReportConclusionScopeEnum NA = new ReportConclusionScopeEnum(_NA);
    public static final ReportConclusionScopeEnum Independence = new ReportConclusionScopeEnum(_Independence);
    public static final ReportConclusionScopeEnum TestItem = new ReportConclusionScopeEnum(_TestItem);
    public static final ReportConclusionScopeEnum TestStandard = new ReportConclusionScopeEnum(_TestStandard);
    public static final ReportConclusionScopeEnum TestReportOverall = new ReportConclusionScopeEnum(_TestReportOverall);
    public static final ReportConclusionScopeEnum TestItem_Sample = new ReportConclusionScopeEnum(_TestItem_Sample);
    public static final ReportConclusionScopeEnum Testitem_Method = new ReportConclusionScopeEnum(_Testitem_Method);
    public static final ReportConclusionScopeEnum PrelimReportOverall = new ReportConclusionScopeEnum(_PrelimReportOverall);
    public static final ReportConclusionScopeEnum PhysicalTestClause = new ReportConclusionScopeEnum(_PhysicalTestClause);
    public String getValue() { return _value_;}
    public static ReportConclusionScopeEnum fromValue(String value)
          throws IllegalArgumentException {
        ReportConclusionScopeEnum enumeration = (ReportConclusionScopeEnum)
            _table_.get(value);
        if (enumeration==null) {
            throw new IllegalArgumentException();
        }
        return enumeration;
    }
    public static ReportConclusionScopeEnum fromString(String value)
          throws IllegalArgumentException {
        return fromValue(value);
    }
    public boolean equals(Object obj) {return (obj == this);}
    public int hashCode() { return toString().hashCode();}
    public String toString() { return _value_;}
    public Object readResolve() throws java.io.ObjectStreamException { return fromValue(_value_);}
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumSerializer(
            _javaType, _xmlType);
    }
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new org.apache.axis.encoding.ser.EnumDeserializer(
            _javaType, _xmlType);
    }
    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ReportConclusionScopeEnum.class);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", "ReportConclusionScopeEnum"));
    }
    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

}
